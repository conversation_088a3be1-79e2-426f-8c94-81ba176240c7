{
    "项目名称":"meissa",
    "版本":"2.0.0",
    "服务配置": {
        "公共配置": {
            "语音ID": "orion.ovs.client.*************",
            "系统语言": {
                "语言版本号": "02",
                "语言信息": [
                    {
                        "类型": "zh_CN",
                        "昵称": "简体中文"
                    },
                    {
                        "类型": "en_US",
                        "昵称": "English"
                    },
                    {
                        "类型": "zh_GD",
                        "昵称": "粤语"
                    },
                    {
                        "类型": "ja_JP",
                        "昵称": "日本語"
                    },
                    {
                        "类型": "th_TH",
                        "昵称": "ไทย"
                    },
                    {
                        "类型": "de_DE",
                        "昵称": "Deutsch"
                    },
                    {
                        "类型": "es_ES",
                        "昵称": "Español"
                    },
                    {
                        "类型": "ko_KR",
                        "昵称": "한국어"
                    },
                    {
                        "类型": "en_GB",
                        "昵称": "English (United Kingdom)"
                    },
                    {
                        "类型": "da_DK",
                        "昵称": "Dansk"
                    },
                    {
                        "类型": "sv_SE",
                        "昵称": "Svenska"
                    },
                    {
                        "类型": "fi_FI",
                        "昵称": "Suomalainen"
                    },
                    {
                        "类型": "nb_NO",
                        "昵称": "Norsk"
                    },
                    {
                        "类型": "fr_FR",
                        "昵称": "Français"
                    },
                    {
                        "类型": "nl_NL",
                        "昵称": "Nederlands"
                    },
                    {
                        "类型": "ru_RU",
                        "昵称": "Pусский"
                    },
                    {
                        "类型": "pl_PL",
                        "昵称": "Polskie"
                    },
                    {
                        "类型": "pt_PT",
                        "昵称": "Português"
                    },
                    {
                        "类型": "it_IT",
                        "昵称": "Italiano"
                    },
                    {
                        "类型": "ro_RO",
                        "昵称": "Română"
                    },
                    {
                        "类型": "ms_MY",
                        "昵称": "Orang Malaysia"
                    },
                    {
                        "类型": "vi_VN",
                        "昵称": "Tiếng Việt"
                    },
                    {
                        "类型": "id_ID",
                        "昵称": "Bahasa Indonesia"
                    },
                    {
                        "类型": "fil_PH",
                        "昵称": "Pilipino"
                    }
                ]
            }
        },
        "语音服务": {
            "是否启用": true,
            "service": "com.ainirobot.speechasrservice",
            "配置项": {
                "ASR语言": "普通话",
                "TTS语言": "普通话",
                "TTS配置": "在线",
                "ASR配置": "在线",
                "音频灌入来源": "内部",
                "唤醒算法": "单通道CNN_DNN",
                "是否获取声音信息": true,
                "客户端ID": "orion.ovs.client.*************",
                "客户端Secret": "07E12AF3E6952125C770B0B0C743342D",
                "客户端PID": "7006",
                "正式语音服务地址": {
                    "获取Token": "https://robot-passport.ainirobot.com:8866/account/bind",
                    "刷新Token": "https://robot-passport.ainirobot.com:8888/oauth2/token",
                    "ASR": "https://speech-bxm.ainirobot.com:443/h2/streaming-asr",
                    "QueryByText": "https://speech-bxm.ainirobot.com/text-asr/v1",
                    "TTS": "https://speech-bxm.ainirobot.com/tts/v1/text2audio",
                    "建立流式WS连接的请求地址": "wss://speech.orionstar.com/speech-bridge/ws/streaming",
                    "建立流式SSE连接的请求地址": "https://speech.orionstar.com/speech-bridge/events",
                    "心跳": "https://speech-bxm.ainirobot.com/echo"
                },
                "测试语音服务地址": {
                    "获取Token": "https://passporttest.ainirobot.com:8866/account/bind",
                    "刷新Token": "http://passporttest.ainirobot.com:8888/oauth2/token",
                    "ASR": "https://speech-test.ainirobot.com:443/h2/streaming-asr",
                    "QueryByText": "http://speech-test.ainirobot.com/text-asr/v1",
                    "TTS": "https://speech-test.ainirobot.com/tts/v1/text2audio",
                    "建立流式WS连接的请求地址": "wss://speech-test.orionstar.com/speech-bridge/ws/streaming",
                    "建立流式SSE连接的请求地址": "https://speech-test.orionstar.com/speech-bridge/events",
                    "心跳": "https://speech-test.ainirobot.com/echo"
                },
                "灰度语音服务地址": {
                    "获取Token": "https://robot-passport.ainirobot.com:8866/account/bind",
                    "刷新Token": "https://robot-passport.ainirobot.com:8888/oauth2/token",
                    "ASR": "https://speech-bxm-pre.ainirobot.com:443/h2/streaming-asr",
                    "QueryByText": "http://asr.ovspre.ainirobot.com:8003/text-asr/v1",
                    "TTS": "https://speech-bxm-pre.ainirobot.com/tts/v1/text2audio",
                    "建立流式WS连接的请求地址": "wss://speech.orionstar.com/speech-bridge/ws/streaming",
                    "建立流式SSE连接的请求地址": "https://speech.orionstar.com/speech-bridge/events",
                    "心跳": "https://speech-bxm-pre.ainirobot.com/echo"
                }
            }
        },
        "导航服务":{
            "是否启用":true,
            "service":"com.ainirobot.navigationservice",
            "配置项":{
                "底盘":"client_tk1",
                "底盘设备号":"2",
                "底盘IP":"************"
            },
            "自检项":{
                "重定位摄像头":false,
                "RGBD":false,
                "激光雷达":true,
                "红外":true,
                "里程计":true,
                "激光数据":true,
                "校准文件":false,
                "硬盘空间":false,
                "can连接":true
            }
        },
        "视觉服务":{
            "是否启用":true,
            "service":"com.ainirobot.headservice",
            "配置项":{
                "算法方案": "tx1",
                "人脸识别": true,
                "客流统计": false,
                "客流属性": false
            },
            "自检项":{
                "前置深度摄像头":false,
                "后置深度摄像头":false,
                "前置广角摄像头":true,
                "后置广角摄像头":false
            }
        },
        "头部控制服务":{
            "是否启用":true,
            "service":"com.ainirobot.headservice",
            "配置项":{

            }
        },
        "远程服务":{
            "是否启用":true,
            "service":"com.ainirobot.remotecontrolservice",
            "配置项":{
                "正式服务地址": {
                    "业务": "https://jiedai.ainirobot.com",
                    "维修": "https://jiedai.ainirobot.com",
                    "大数据": "https://api.orionbase.cn",
                    "时间": "http://speech-bxm.ainirobot.com/timestamp"
                },
                "测试服务地址": {
                    "业务": "https://test-jiedai.ainirobot.com",
                    "维修": "https://test-jiedai.ainirobot.com",
                    "大数据": "https://test-api.orionbase.cn",
                    "时间": "http://speech-test.ainirobot.com/timestamp"
                },
                "灰度服务地址": {
                    "业务": "https://jiedai.ainirobot.com",
                    "维修": "https://jiedai.ainirobot.com",
                    "大数据": "https://api.orionbase.cn",
                    "时间": "http://speech-bxm.ainirobot.com/timestamp"
                },
                "扫码绑定": true,
                "远程控制": true,
                "时钟同步": true,
                "无线网络": true
            },
            "自检项":{
                "远程服务连接":false
            }
        },
        "升级服务":{
            "是否启用":true,
            "service":"com.ainirobot.ota",
            "配置项":{
                "开机升级":true,
                "强制升级":true,
                "夜间升级":true,
                "静默升级": {
                    "是否启用":true,
                    "下载后直接升级":true,
                    "下载后手动升级": false
                },
                "PSB升级":true,
                "AC_CLIENT升级":true,
                "BMS升级":true,
                "MOTOR_HORIZON升级":true,
                "MOTOR_VERTICAL升级":true,
                "MOTOR_LEFT升级":true,
                "MOTOR_RIGHT升级":true,
                "TK1升级":true,
                "TX1升级":true,
                "server_domain":"https://jiedai.ainirobot.com",
                "develop_server_domain":"https://test-jiedai.ainirobot.com",
                "prerelease_server_domain":"https://jiedai.ainirobot.com"
	    }
        },
        "系统服务":{
            "是否启用":true,
            "service":"com.ainirobot.hardwareservice",
            "配置项":{
                "箱门": false,
                "270度水平云台":true
            },
            "自检项":{
                "云台水平电机":true,
                "云台俯仰电机":true,
                "BMS连接":true,
                "PSB":true,
                "红外模块连接":true,
                "右轮毂":true,
                "左轮毂":true,
                "电机零位检测":true,
                "821摄像头":false,
                "光线传感器":false
            }
        },
	    "外设管理":{
            "service":"com.ainirobot.hardwareservice",
            "外设配置":{
                "刷卡器服务": {
                    "是否启用": false
                },
                "扫码器服务": {
                    "是否启用": false,
                    "自检项": {
                        "扫码器": true
                    }
                },
                "客流服务": {
                    "是否启用": false,
                    "配置项":{
                        "客流统计": false
                    },
                    "自检项":{
                    }
                }
            }
        },
        "红外测温服务":{
            "service":"com.ainirobot.thermalservice",
            "是否启用": false,
            "配置项":{
                "产品ID": "1"
            }
        },
        "账号服务":{
            "是否启用":false,
            "配置项": {
                "本地注册": false
            }
        },
        "视频服务":{
            "是否启用": true,
            "service": "com.ainirobot.videocall",
            "配置项": {
            },
            "自检项": {
                "视频服务连接": false
            }
        },
        "核心服务":{
            "service":"com.ainirobot.coreservice",
            "配置项": {
                "Log等级": {
                    "等级": 1
                }
            }
        }
    }
}
