{"项目名称": "mini", "版本": "3.0.0", "服务配置": {"语音服务": {"是否启用": true, "service": "com.ainirobot.speechasrservice", "配置项": {"ASR语言": "普通话", "TTS语言": "普通话", "TTS配置": "在线", "ASR配置": "在线", "音频灌入来源": "内部", "是否获取声音信息": true, "客户端ID": "orion.ovs.client.*************", "客户端Secret": "6C3C71DC9059285CC1AAF53018FD42D1", "客户端PID": "116", "正式语音服务地址": {"获取Token": "https://robot-passport.ainirobot.com:8866/account/bind", "刷新Token": "https://robot-passport.ainirobot.com:8888/oauth2/token", "ASR": "https://speech-bxm.ainirobot.com:443/h2/streaming-asr", "QueryByText": "https://speech-bxm.ainirobot.com/text-asr/v1", "TTS": "https://speech-bxm.ainirobot.com/tts/v1/text2audio", "心跳": "https://speech-bxm.ainirobot.com/echo"}, "测试语音服务地址": {"获取Token": "https://passporttest.ainirobot.com:8866/account/bind", "刷新Token": "http://passporttest.ainirobot.com:8888/oauth2/token", "ASR": "https://speech-test.ainirobot.com:443/h2/streaming-asr", "QueryByText": "http://speech-test.ainirobot.com/text-asr/v1", "TTS": "https://speech-test.ainirobot.com/tts/v1/text2audio", "心跳": "https://speech-test.ainirobot.com/echo"}, "灰度语音服务地址": {"获取Token": "https://robot-passport.ainirobot.com:8866/account/bind", "刷新Token": "https://robot-passport.ainirobot.com:8888/oauth2/token", "ASR": "https://speech-bxm-pre.ainirobot.com:443/h2/streaming-asr", "QueryByText": "http://asr.ovspre.ainirobot.com:8003/text-asr/v1", "TTS": "https://speech-bxm-pre.ainirobot.com/tts/v1/text2audio", "心跳": "https://speech-bxm-pre.ainirobot.com/echo"}}}, "导航服务": {"是否启用": true, "service": "com.ainirobot.navigationservice", "配置项": {"底盘": "client_x86", "底盘设备号": "10"}, "自检项": {"重定位摄像头": false, "RGBD": false, "激光雷达": true, "红外": false, "里程计": true, "激光数据": false, "校准文件": false, "硬盘空间": false, "can连接": false}}, "视觉服务": {"是否启用": true, "service": "com.ainirobot.headservice", "配置项": {"算法方案": "vision_sdk", "人脸识别": true, "客流统计": false, "客流属性": false}, "自检项": {"前置广角摄像头": true, "后置广角摄像头": false, "前置深度摄像头": false, "后置深度摄像头": false}}, "头部控制服务": {"是否启用": true, "service": "com.ainirobot.headservice", "配置项": {}}, "远程服务": {"是否启用": true, "service": "com.ainirobot.remotecontrolservice", "配置项": {"正式服务地址": {"业务": "https://mini.ainirobot.com", "扩展业务": "https://mini-server.ainirobot.com", "维修": "https://mini.ainirobot.com", "大数据": "https://api.orionbase.cn", "时间": "http://speech-bxm.ainirobot.com/timestamp"}, "测试服务地址": {"业务": "https://test-mini.ainirobot.com", "扩展业务": "https://test-cm-mini.ainirobot.com", "维修": "https://test-mini.ainirobot.com", "大数据": "https://test-api.orionbase.cn", "时间": "http://speech-test.ainirobot.com/timestamp"}, "灰度服务地址": {"业务": "https://mini.ainirobot.com", "扩展业务": "https://mini-server.ainirobot.com", "维修": "https://mini.ainirobot.com", "大数据": "https://api.orionbase.cn", "时间": "http://speech-bxm.ainirobot.com/timestamp"}, "扫码绑定": true, "远程控制": true, "时钟同步": true, "无线网络": true}, "自检项": {"远程服务连接": false}}, "升级服务": {"是否启用": true, "service": "com.ainirobot.ota", "配置项": {"开机升级": false, "强制升级": true, "夜间升级": false, "静默升级": {"是否启用": false, "下载后直接升级": false, "下载后手动升级": false}, "静默下载": true, "PSB升级": true, "AC_CLIENT升级": false, "BMS升级": true, "MOTOR_HORIZON升级": false, "MOTOR_VERTICAL升级": true, "MOTOR_LEFT升级": false, "MOTOR_RIGHT升级": false, "TK1升级": false, "TX1升级": false, "server_domain": "https://api.orionbase.cn", "develop_server_domain": "https://test-api.orionbase.cn", "prerelease_server_domain": "https://api.orionbase.cn"}}, "系统服务": {"是否启用": true, "service": "com.ainirobot.hardwareservice", "配置项": {}, "自检项": {"云台水平电机": false, "云台俯仰电机": true, "BMS连接": false, "PSB": true, "红外模块连接": false, "右轮毂": false, "左轮毂": false, "电机零位检测": false, "821摄像头": false, "光线传感器": false}}, "外设管理": {"service": "com.ainirobot.hardwareservice", "外设配置": {"刷卡器服务": {"是否启用": false}}}}}