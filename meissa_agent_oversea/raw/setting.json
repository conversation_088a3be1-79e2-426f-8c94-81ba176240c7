{"项目名称": "meissa_2.0_oversea", "版本": "10302", "设置": {"boot_app_package_name": "com.ainirobot.moduleapp", "boss_avatar_on_mobile": 0, "build_new_map_on_mobile": 0, "charging_pile_configured": 0, "robot_auto_back_reception": 0, "robot_chat_reply": 1, "robot_dropdown_bar_password": 1, "robot_focus_follow": 1, "robot_regular_charge_time_list": "", "robot_obstacles_avoid": 0, "robot_prevent_collision": 1, "robot_samll_action": 0, "robot_setting_allow_auto_situ_service": 0, "robot_setting_auto_charge": 1, "robot_setting_ota_low_battery_auto_charge": 1, "robot_setting_cruise_angular_speed": 1.2, "robot_setting_cruise_linear_speed": 0.7, "robot_setting_disable_voice_control_volume": 0, "robot_setting_greet_angular_speed": 1.2, "robot_setting_greet_linear_speed": 0.3, "robot_setting_guide_angular_speed": 1.2, "robot_setting_guide_linear_speed": 0.7, "robot_setting_hide_manufacture_information": 0, "robot_setting_lead_angular_speed": 1.2, "robot_setting_lead_linear_speed": 0.7, "robot_setting_nav_angular_speed": 1.2, "robot_setting_nav_linear_speed": 0.7, "robot_setting_ota_update": 1, "robot_setting_shipping_mode": 0, "robot_setting_situ_service_running_time": 0, "robot_setting_situ_service_status": 0, "robot_setting_situ_service_time": 14400000, "robot_setting_sound_angel_center": 0, "robot_setting_sound_angel_range": 80, "robot_setting_speech_speed": 6, "robot_setting_speech_volume": 30, "robot_setting_speaker_role": "standard", "robot_setting_vad_end": 600, "robot_setting_waiting_time": 120, "robot_settings_charging_environment": 2, "robot_settings_obstacles_avoid_distance": 3.0, "robot_show_ad": 0, "robot_show_ad_and_rotate": 0, "robot_usable_when_charging": 0, "settings_global_switch_follow_style": 0, "sleep_list": "", "switch_allow_chat_when_interpret": 0, "switch_start_app_password": 0, "version_upgrade_on_mobile": 1, "robot_setting_demo_mode": 0, "robot_language": "en_US", "robot_time_zone_code": "Asia/Shanghai", "robot_mask_detection": 1, "robot_settings_navigation_nonsupport": 0, "robot_settings_default_body_speed": 50, "robot_settings_quick_setting_bar_support_method": "triple_swipe_top", "robot_settings_power_key_long_press_direct_shut_down": 1, "robot_settings_chassis_client_type": "client_waiter", "robot_settings_scanner_access_mode": "", "robot_settings_disable_charging_relocation": "1", "robot_settings_relocation_type": "type_waiter", "robot_setting_battery_info": 50, "robot_setting_screen_light_befor_standby": -1, "robot_setting_emergency_info": 0, "robot_setting_pose_estimate": 0, "robot_setting_wheel_over_current": 0, "robot_setting_bms_warning_status": 1, "robot_setting_navigation_line_tracking": 1, "robot_expires_status": 0, "robot_expires_time": 0, "robot_expires_left_time": 0, "robot_setting_system_environment": 0, "robot_setting_system_adb_always_open": 0, "client_id": "", "robot_map_tool_disable_remote_mapping": 1, "robot_map_tool_hide_cruise_setting": 0, "robot_map_tool_hide_set_charge_pile": 1, "robot_meal_setting_standby_time": 15, "robot_setting_map_outside_warning": 1, "robot_setting_being_pushed_warning": 1, "robot_map_tool_creat_map_mode": 1, "robot_setting_shutdown_switch": "0", "robot_setting_enable_target_custom": 1, "robot_setting_enable_single_marker": 1, "robot_setting_enable_dynamic_map_filter": 1, "robot_setting_navigation_start_mode_level": 2, "robot_setting_navigation_break_mode_level": 2, "robot_enable_asr_switch": 1, "robot_portal_opk": "system_5413441859acdb7380efcc6701e1530a", "robot_settings_charging_type": "charging_pile", "robot_setting_navi_lethal_radius_offset": 0, "robot_setting_navi_factory_robot_radius": 0, "robot_settings_charge_policy": 1, "robot_enable_recognize_continue_switch": 0, "robot_settings_battery_low_level": 10, "robot_setting_uvc_camera_switch": 0, "robot_setting_pro_tray_led_switch": 0, "robot_setting_radar_enable_in_standby": 0, "robot_setting_system_wifi_adb_always_open": 0, "robot_static_mode_switch": 0, "show_app_settings": "", "Mono_Camera_Switch": 1, "robot_setting_table_tracker_ip": "", "robot_setting_final_timeout": 10000, "robot_setting_anticollision_strip_switch": 1, "robot_setting_navi_temporary_rgbd_filter_down_vision_level": -1, "radar_close_delay_time": 5, "robot_settings_current_app": "", "robot_setting_cloud_server_code": "EU", "robot_setting_screen_density": 560, "robot_setting_elevator_control_enabled": 0, "robot_setting_asr_pop_up_type": 1, "robot_setting_auto_relocate_enabled": 0, "robot_setting_ota_type": 1, "robot_setting_voice_mode": -1, "robot_setting_voice_service_area": 1.2, "robot_setting_not_interrupt_switch": 1, "robot_setting_collect_data_switch": 0, "robot_setting_face_track_mode": -1, "robot_setting_ambient_light_switch": 1}}