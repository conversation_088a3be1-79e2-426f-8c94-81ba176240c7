{"项目名称": "slim_huazhu", "版本": "1.0.0", "服务配置": {"公共配置": {"语音ID": "orion.ovs.client.*************", "系统语言": {"语言版本号": "01", "语言信息": [{"类型": "zh_CN", "昵称": "简体中文"}, {"类型": "zh_TW", "昵称": "繁體中文"}, {"类型": "zh_GD", "昵称": "繁體中文(香港)"}]}, "地图兼容性配置": {"最高兼容版本": 2, "视觉类型": [0, 1], "标识码类型": [0, 1, 2, 3]}}, "语音服务": {"是否启用": true, "service": "com.ainirobot.speechasrservice", "配置项": {"ASR语言": "普通话", "TTS语言": "普通话", "TTS配置": "在线", "ASR配置": "在线", "音频灌入来源": "内部", "唤醒算法": "多通道WaveNet", "vad算法": "vad_2.0", "是否获取声音信息": true, "客户端ID": "orion.ovs.client.*************", "客户端Secret": "D382B36D8AD66A2BD327AB6989F529DC", "客户端PID": "100011", "正式语音服务地址": {"获取Token": "https://robot-passport.ainirobot.com:8866/account/bind", "刷新Token": "https://robot-passport.ainirobot.com:8888/oauth2/token", "ASR": "https://speech-bxm.ainirobot.com:443/h2/streaming-asr", "QueryByText": "https://speech-bxm.ainirobot.com/text-asr/v1", "TTS": "https://speech-bxm.ainirobot.com/tts/v1/text2audio", "获取发音人": "https://speech-bxm.ainirobot.com/tts/v1/voices", "获取唤醒应答音频": "https://speech-bxm.ainirobot.com/tts/v1/operate/audio", "建立流式WS连接的请求地址": "wss://speech.orionstar.com/speech-bridge/ws/streaming", "建立流式SSE连接的请求地址": "https://speech.orionstar.com/speech-bridge/events", "心跳": "https://speech-bxm.ainirobot.com/echo"}, "测试语音服务地址": {"获取Token": "https://passporttest.ainirobot.com:8866/account/bind", "刷新Token": "http://passporttest.ainirobot.com:8888/oauth2/token", "ASR": "https://speech-test.ainirobot.com:443/h2/streaming-asr", "QueryByText": "https://speech-test.ainirobot.com/text-asr/v1", "TTS": "https://speech-test.ainirobot.com/tts/v1/text2audio", "获取发音人": "https://speech-test.ainirobot.com/tts/v1/voices", "获取唤醒应答音频": "https://speech-test.ainirobot.com/tts/v1/operate/audio", "建立流式WS连接的请求地址": "wss://speech-test.orionstar.com/speech-bridge/ws/streaming", "建立流式SSE连接的请求地址": "https://speech-test.orionstar.com/speech-bridge/events", "心跳": "https://speech-test.ainirobot.com/echo"}, "灰度语音服务地址": {"获取Token": "https://robot-passport.ainirobot.com:8866/account/bind", "刷新Token": "https://robot-passport.ainirobot.com:8888/oauth2/token", "ASR": "https://speech-bxm-pre.ainirobot.com:443/h2/streaming-asr", "QueryByText": "https://asr.ovspre.ainirobot.com:8003/text-asr/v1", "TTS": "https://speech-bxm-pre.ainirobot.com/tts/v1/text2audio", "获取发音人": "https://speech-bxm.ainirobot.com/tts/v1/voices", "获取唤醒应答音频": "https://speech-bxm.ainirobot.com/tts/v1/operate/audio", "建立流式WS连接的请求地址": "wss://speech.orionstar.com/speech-bridge/ws/streaming", "建立流式SSE连接的请求地址": "https://speech.orionstar.com/speech-bridge/events", "心跳": "https://speech-bxm-pre.ainirobot.com/echo"}}}, "导航服务": {"是否启用": true, "service": "com.ainirobot.navigationservice", "配置项": {"底盘": "client_waiter", "底盘设备号": "50", "启用子设备号": true, "底盘IP": "127.0.0.1"}, "自检项": {"重定位摄像头": false, "RGBD": true, "激光雷达": true, "红外": false, "里程计": true, "激光数据": false, "校准文件": false, "硬盘空间": false, "can连接": false, "红外摄像头": true, "陀螺仪": true}}, "视觉服务": {"是否启用": true, "service": "com.ainirobot.headservice", "配置项": {"算法方案": "vision_sdk", "人脸识别": true, "客流统计": false, "客流属性": false}, "自检项": {"前置深度摄像头": false, "后置深度摄像头": false, "前置广角摄像头": false, "后置广角摄像头": false}}, "头部控制服务": {"是否启用": false, "service": "com.ainirobot.headservice", "配置项": {}}, "远程服务": {"是否启用": true, "service": "com.ainirobot.remotecontrolservice", "配置项": {"正式服务地址": {"业务": "https://jiedai.ainirobot.com", "维修": "https://jiedai.ainirobot.com", "大数据": "https://api.orionbase.cn", "时间": "http://speech-bxm.ainirobot.com/timestamp"}, "测试服务地址": {"业务": "https://test-jiedai.ainirobot.com", "维修": "https://test-jiedai.ainirobot.com", "大数据": "https://test-api.orionbase.cn", "时间": "http://speech-test.ainirobot.com/timestamp"}, "灰度服务地址": {"业务": "https://jiedai.ainirobot.com", "维修": "https://jiedai.ainirobot.com", "大数据": "https://api.orionbase.cn", "时间": "http://speech-bxm.ainirobot.com/timestamp"}, "moduleCode下载列表": ["module_skill_home", "module_guide_home", "module_welcome", "module_remote_greet", "module_reception", "module_guide", "module_qa", "module_ride", "module_cate_ads", "module_cafe_menu", "module_bxd_blessing", "module_bxd_deliver", "module_bxd_default_broadcast", "module_pushtask_message", "module_ads", "module_theme", "module_goto_place_talk", "module_collection_music", "module_ads_line_cfg", "module_silence_recom", "module_leading_ads", "module_leading_start_broadcast", "module_leading_arrive_broadcast", "module_collection_broadcast", "module_bxdp_goods_deliver"], "扫码绑定": true, "远程控制": true, "时钟同步": true, "无线网络": true}, "自检项": {"远程服务连接": false}}, "升级服务": {"是否启用": true, "service": "com.ainirobot.ota", "配置项": {"开机升级": true, "强制升级": true, "夜间升级": true, "静默升级": {"是否启用": true, "下载后直接升级": true, "下载后手动升级": false}, "PSB升级": true, "AC_CLIENT升级": false, "BMS升级": false, "MOTOR_HORIZON升级": false, "MOTOR_VERTICAL升级": false, "MOTOR_LEFT升级": false, "MOTOR_RIGHT升级": false, "TK1升级": false, "TX1升级": false, "server_domain": "https://jiedai.ainirobot.com", "develop_server_domain": "https://test-jiedai.ainirobot.com", "prerelease_server_domain": "https://jiedai.ainirobot.com"}}, "系统服务": {"是否启用": true, "service": "com.ainirobot.hardwareservice", "配置项": {"270度水平云台": false, "轮子恢复": true, "多功能开关": true, "多功能开关类型": 1}, "自检项": {"云台水平电机": false, "云台俯仰电机": false, "BMS连接": false, "PSB": false, "红外模块连接": false, "右轮毂": false, "左轮毂": false, "电机零位检测": false, "821摄像头": false, "光线传感器": false}}, "外设管理": {"service": "com.ainirobot.hardwareservice", "外设配置": {"刷卡器服务": {"是否启用": false}, "扫码器服务": {"是否启用": false, "自检项": {"扫码器": false}}, "客流服务": {"是否启用": false, "配置项": {"客流统计": false}, "自检项": {}}, "D430服务": {"是否启用": false, "配置项": {}, "自检项": {}}, "托盘摄像头服务": {"是否启用": true, "配置项": {}, "自检项": {}}}}, "红外测温服务": {"service": "com.ainirobot.thermalservice", "是否启用": false, "配置项": {"产品ID": "1"}}, "视频服务": {"是否启用": false, "service": "com.ainirobot.videocall", "配置项": {"获取视频流方式": 1, "远程视频旋转角度": 90, "本地预览旋转角度": 0}, "自检项": {"视频服务连接": false}}, "核心服务": {"service": "com.ainirobot.coreservice", "配置项": {"待机配置": {"静音": false, "降低屏幕亮度": true, "关闭视觉": true, "关闭语音": true, "关闭灯带": true, "关闭IR灯": true, "关闭雷达": true, "关闭轮子电机": true, "休眠": false, "关机": false, "关闭深度IR摄像头": true}, "定时关机配置": {"是否启用": true, "默认关机时间": "00:30"}, "是否有TopIR": true, "是否有回充IR": true, "是否使用ProZcbLed灯带接口": true, "是否使用锁骨灯": true, "是否有胸口灯": false, "是否有托盘指示灯": true, "是否有Mono": true}, "服务管理": {"账号服务": {"是否启用": false, "配置项": {"本地注册": false}}, "头部服务": {"是否启用": true, "配置项": {"支持270度水平云台": false, "支持水平云台": false, "支持俯仰云台": false}}, "电池服务": {"是否启用": true, "配置项": {"低电值": 5, "OTA低电值": 20}}, "导航服务": {"是否启用": true, "配置项": {"底盘半径": 0.245}}}}, "梯控服务": {"service": "com.ainirobot.elevator", "是否启用": true, "配置项": {"elevatorType": 1, "kaGaoHost": "wss://tikong.kagobot.com/socket", "kaGaoAuthUser": "15110267753", "kaGaoAuthPW": "267753"}}}}