#!/bin/sh
path=$(pwd)
echo $path
$path/gradlew buildSystemJar buildJar
if [ $? -ne 0 ]; then
   echo 'build failed,please check your code'
else
   echo 'build success'
   ls ./app/build/libs/
   echo ../SpeechService-orion/app/libs/ ../NavigationService-orion/app/libs/ ../NavigationService/app/libs/ ../HeadService/app/libs/ ../HardwareService/app/libs/ ../RemoteControlService/app/libs/ ../OtaService/app/libs/ ../PassGateService/app/libs/ ../UploadService-orion/app/libs/ ../Elevator/app/libs/ ../../RobotSystem/FirstConfig/app/libs/  ../../RobotSystem/Home/app/libs/ ../../RobotSystem/VideoCall/app/libs/ | xargs -n 1 cp -v ./app/build/libs/robotsystem.jar
   echo ../../RobotSystem/Inspection/app/libs/ ../../RobotSystem/MapTool/app/libs/ ../../RobotPlatform/platform/libs/ | xargs -n 1 cp -v ./app/build/libs/robotservice.jar
fi
