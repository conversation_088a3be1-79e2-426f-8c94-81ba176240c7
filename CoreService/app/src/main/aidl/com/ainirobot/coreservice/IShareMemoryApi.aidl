// IShareMemoryApi.aidl
package com.ainirobot.coreservice;

// Declare any non-default types here with import statements
import android.os.ParcelFileDescriptor;
import com.ainirobot.coreservice.IShareMemoryCallback;

interface IShareMemoryApi {

    ParcelFileDescriptor getParcelFileDescriptor(String type, String params);
    boolean setParcelFileDescriptor(String type, in ParcelFileDescriptor pfd, String params);
    oneway void releaseParcelFileDescriptor(String type);
    void broadcastPfd(in ParcelFileDescriptor pfd);
    void registerCallback(IShareMemoryCallback callback);
    void unregisterCallback(IShareMemoryCallback callback);
    void startShare();
    void stopShare();


}
