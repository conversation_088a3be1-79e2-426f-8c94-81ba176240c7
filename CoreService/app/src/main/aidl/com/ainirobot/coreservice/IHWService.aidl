/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice;
import  com.ainirobot.coreservice.IInspectCallBack;
import com.ainirobot.coreservice.bean.Command;
import com.ainirobot.coreservice.config.ServiceConfig;
import android.os.ParcelFileDescriptor;

interface IHWService {
    /* get HW status with integer response */
    int getHWStatus();

    List<Command> mount(String name, in ServiceConfig config);

    String getBoardName();

    String getVersion(String subsystem);

    void startUpgrade(String subsystem, String params);

    boolean isNeedUpgrade(String subsystem, String version);

    /* send sync command with result */
    String exeSyncCommand(String cmdType, String params, String language);

    /* send async command with boolean result, after command finish will response again. */
    oneway void exeAsyncCommand(String cmdType, String params, String language);

    boolean startStatusSocket(String type, int socketPort);

    boolean closeStatusSocket(String type, int socketPort);

    oneway void startInspect(IInspectCallBack callback);

    oneway void reset();

    oneway void switchAppControl(String packageName, String lastPackageName);

    oneway void setLanguage(String language);

    ParcelFileDescriptor getParcelFileDescriptor(String type, String params);

    boolean setParcelFileDescriptor(String type, in ParcelFileDescriptor pfd, String params);

    oneway void releaseParcelFileDescriptor(String type);

}
