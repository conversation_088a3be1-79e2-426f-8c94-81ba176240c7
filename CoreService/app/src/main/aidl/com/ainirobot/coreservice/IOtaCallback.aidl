/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

 package com.ainirobot.coreservice;

 import android.os.Bundle;

interface IOtaCallback {

    void onCmdResponse(int cmdId, String cmdType, String cmdResponse);

    boolean onOtaUpgradeStart(boolean needDownload);
    boolean onOtaRollbackStart();
    String onOtaGetDescription();
    boolean onOtaCancelDownload();
    void installPatch(in Bundle bundle);
    void onOtaInterrupted(String reason);
}
