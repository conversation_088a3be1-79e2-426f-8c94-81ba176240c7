/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice;

import com.ainirobot.coreservice.ISkillCallback;
import com.ainirobot.coreservice.listener.IToneListener;
import com.ainirobot.coreservice.listener.ITextListener;
import com.ainirobot.coreservice.listener.IMusicListener;
import com.ainirobot.coreservice.ISkillServerCheckListener;
import com.ainirobot.coreservice.config.SceneEntity;

interface ISkill{
    oneway void registerCallBack(ISkillCallback cb);
    oneway void unregisterCallBack(ISkillCallback cb);

    oneway void playText(String text, ITextListener listener);
    oneway void playTone(String type, IToneListener listener);
    oneway void stopTTS();
    oneway void setRecognizeMode(boolean isContinue);
    oneway void setASREnabled(boolean enable);
    oneway void setRecognizable(boolean enable);
    oneway void queryByText(String text);
    oneway void getActiveAsk(String properType, String robotProperJson);
    oneway void cancleAudioOperation();
    oneway void setWakeupHintClosed(boolean isWakeupHintClosed);
    oneway void setAngleCenterRange(float angle_center, float angle_range);
    oneway void setTTSParams(String ttsType, int value);
    oneway void setMultipleModeEnable(boolean enale);

    int setCustomizeWakeUpWord(String wakeUpWordChinese, String wakeUpWordSpell, String separator);
    int closeCustomizeWakeUpWord();
    int getPinYinScore(String pinyin, String separator);
    String queryPinYinFromChinese(String chineseWord);
    String queryPinYinMappingTable(String pinyin);
    String queryUserSetWakeUpWord();

    oneway void setLangRec(String autoLangJson);
    oneway void registerServerCheck(ISkillServerCheckListener Listener);
    oneway void unregisterServerCheck();

    oneway void playToneByLocalPath(String localPath, IToneListener listener);
    oneway void stopTone();
    void switchScene(in SceneEntity scene);
    boolean setAsrExtendProperty(String propertyJson);
    oneway void setASRParams(String asrType, String value);

     //nlp相关接口

     void onCreate(String app_id);

     void onForeground(String app_id);

     void onBackground(String app_id);

     void onDestroy(String app_id);

     void setVersion(String app_id,String app_version);

     void setPath(String app_id ,String path);

     void sendAgentMessage(String type,int code,String message);

     //已作废请使用:setSyncCustomNlpData
     void setSyncReportCustomNlpData(String app_id, String data);

     //已作废请使用:setAsyncCustomNlpData
     void setAsyncReportCustomNlpData(String app_id, String data);

     void resetNlpState();

     void setServerApp(in List<String> appList);

     void setDebug(boolean value);

     void setSyncCustomNlpData(in Map map);

     String setAsyncCustomNlpData(String opt, String data);

    int getTtsPlayStatus();

    oneway void downloadTtsAudio(String ttsEntitiesJson);

    String getSpokemanListByLanguage(String lang);

   oneway void playMusicByLocalPath(String localPath, boolean looping, boolean enableAudioFocus,IMusicListener listener);
   oneway void stopMusicPlay();
   // UI层关闭流式数据接收的状态同步.
   oneway void closeStreamDataReceived(String statusJson);

   boolean isRecognizeContinue();

   boolean isRecognizable();

   oneway void setRecognizeModeForce(boolean isContinue);

   oneway void setRecognizeModeNew(boolean isContinue, boolean isCloseStreamData);

   oneway void onAgentActionFinish(String action, int code, String message);

   oneway void onAgentActionState(String action, int state, String data);

   oneway void queryByTextWithThinking(String text, boolean isShowThinking);
}

