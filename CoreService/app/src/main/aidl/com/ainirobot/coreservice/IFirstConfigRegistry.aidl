/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice;

// Declare any non-default types here with import statements

import com.ainirobot.coreservice.IStatusListener;
import com.ainirobot.coreservice.listener.IActionListener;

interface IFirstConfigRegistry {

    int sendFirstConfigRequest(String type, String params);

    int sendFirstConfigCommand(String cmdAction, String cmdParam);

    int startAction(int reqId, int actionId, String params, IActionListener listener);

    int stopAction(int reqId, int actionId, boolean isResetHW);

    String registerStatusListener(String type, IStatusListener listener);

    boolean unregisterStatusListener(String id);

    void startInspection(int reqId, IActionListener listener);

//    void textToSpeech(String text);
//
//    void toneToSpeech(String type);
}
