/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice;

import com.ainirobot.coreservice.listener.IPersonListener;
import com.ainirobot.coreservice.listener.IActionListener;

interface IPersonApi {
    boolean setPersonListener(IPersonListener listener);
    void releasePersonListener();
    String getAllPersons();
    String getAllPersonsByDistance(double maxDistance);
    String getAllFaceList();
    String getAllFaceListByDistance(double maxDistance);
    String getCompleteFaceList();
    String getCompleteFaceListByDistance(double maxDistance);
    String getAllBodyList();
    String getAllBodyListByDistance(double maxDistance);
    String getFocusPerson();
    boolean registerById(int personId, String name, IActionListener listener);
    boolean registerByPic(String picturePath, String name, IActionListener listener);
    boolean recognizeById(int personId, IActionListener listener);
    boolean recognizeByPic(String picturePath, IActionListener listener);
    String getMultipleModeInfos(int index);
    String getRegisteredFaceList();
    boolean reRegisterWithUserIdById(int personId, String userId, IActionListener listener);
    boolean reRegisterWithUserIdByPic(String picturePath, String userId, IActionListener listener);
}
