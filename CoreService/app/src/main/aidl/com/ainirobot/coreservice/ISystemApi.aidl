// ISystemApi.aidl
package com.ainirobot.coreservice;

import com.ainirobot.coreservice.IModuleCallback;
import com.ainirobot.coreservice.IStatusListener;
import com.ainirobot.coreservice.listener.IActionListener;
import android.os.Bundle;
import com.ainirobot.coreservice.bean.Task;
import com.ainirobot.coreservice.bean.TaskEvent;

interface ISystemApi {
    void setCallback(String packageName, IModuleCallback cb);

    int startAction(int reqId, int actionId, String params, IActionListener listener);

    int stopAction(int reqId, int actionId, boolean isResetHW);

    boolean finishModuleWithResponse(int reqId, boolean ifParsedSucc, String response);

    String registerStatusListener(String type, IStatusListener listener);

    boolean unregisterStatusListener(String id);

    boolean startStatusSocket(String type, int socketPort);

    boolean closeStatusSocket(String type, int socketPort);

    void sendStatusReport(String type, String data);

    boolean startOtaUpgrade(boolean needDownload, boolean isRollback);

    void updateSystemStatus(String status, String data);

    String getOtaUpgradeDescription();

    boolean cancelOtaUpgradeDownload();

    boolean installPatch(in Bundle bundle);

    void startAppControl(String packageName);

    void stopAppControl();

    String getActiveApp();

    boolean updateCurrentStatus();

    void disableEmergency();

    void disableBattery();

    void disableRadar();

    void enableRadar();

    void resetSystemStatus();

    String getRobotInfo(int reqType, String params);

    String getSystemStatus();

    boolean isExternalServiceEnable(String name);

    void getRobotStatus(String type, IStatusListener listener);

    String getPassword(int length);

    String reportTask(in Task task);

    void reportTaskEvent(in TaskEvent taskEvent);

    List<Task> getCurrentTask();

    boolean setServerSupportLanguageList(String params);

    boolean hasTopIR();

    boolean hasChargeIR();

    boolean isUseProZcbLed();

    boolean isHasClavicleLight();

    boolean isHasChestLight();

    boolean isHasProTrayLED();

    boolean hasMono();

    boolean hasTopMono();

    void disablePushWarning();

    void enablePushWarning();

    boolean isUseAutoEffectLed();

    boolean isHasClavicleLed();

    boolean isHasChestLed();

    boolean isHasTrayLight();

    String getCreateMapType();

    boolean isSupportElevator();

    String isAlreadyInElevator();
}
