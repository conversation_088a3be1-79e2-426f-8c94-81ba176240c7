package com.ainirobot.coreservice;

import java.util.List;
import com.ainirobot.coreservice.listener.IAccountListener;
import com.ainirobot.coreservice.listener.IActionListener;

interface IAccountApi {
       String getMemberList();
       void syncServerDataToLocale(IActionListener listener);
       String getSystemToken();
       String getRecentMember();
       String getWholeMemberList();
       void registerAccountListener(IAccountListener listener);
       void unregisterAccountListener(IAccountListener listener);
       void recoveryRobot(String userId, String userToken, IActionListener listener);
       String getRegisteredFaceList();
       boolean reRegisterWithUserIdById(int personId, String userId, IActionListener listener);
       boolean reRegisterWithUserIdByPic(String picturePath, String userId, IActionListener listener);
}
