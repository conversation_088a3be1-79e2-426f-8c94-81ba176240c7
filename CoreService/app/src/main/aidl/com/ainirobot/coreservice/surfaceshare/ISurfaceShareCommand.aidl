// ISurfaceShareCommand.aidl
package com.ainirobot.coreservice.surfaceshare;

// Declare any non-default types here with import statements
import com.ainirobot.coreservice.surfaceshare.ISurfaceShareCallback;

interface ISurfaceShareCommand {
    boolean setStreamSurface(inout Surface surface);
    void unSetStreamSurface();
    boolean showSurface(inout Surface surface, boolean show);
    void registerCallback(ISurfaceShareCallback callback);
    void unRegisterCallback(ISurfaceShareCallback callback);
}
