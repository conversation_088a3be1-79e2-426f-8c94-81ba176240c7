/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice;

interface ISkillCallback {
    oneway void onSpeechParResult(String text);
    oneway void onStart();
    oneway void onStop();
    oneway void onVolumeChange(int volume);
    oneway void onQueryEnded(int queryEndStatus);
    String onGetMultipleModeInfos(int index);
    oneway void onQueryAsrResult(String asrResult);
    oneway void onError(String sid,int code,String message);
    oneway void onVadMuteTime(int vadMuteTime);
    oneway void onSpeechStreamData(String data);
}
