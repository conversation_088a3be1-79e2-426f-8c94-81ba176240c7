/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.listener;

interface IActionListener {
    void onResult(int status, String responseString);
    void onError(int errorCode, String errorString);
    void onStatusUpdate(int status, String data);
    void onResultWithExtraData(int status, String responseString, String extraData);
    void onErrorWithExtraData(int errorCode, String errorString, String extraData);
    void onStatusUpdateWithExtraData(int status, String data, String extraData);
}
