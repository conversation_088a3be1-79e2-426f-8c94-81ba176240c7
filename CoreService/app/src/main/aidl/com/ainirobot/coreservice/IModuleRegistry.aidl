/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice;

import com.ainirobot.coreservice.IModuleCallback;
import com.ainirobot.coreservice.IStatusListener;
import com.ainirobot.coreservice.listener.IActionListener;
import android.os.Bundle;
import com.ainirobot.coreservice.bean.Task;
import com.ainirobot.coreservice.bean.TaskEvent;

interface IModuleRegistry {

    void setCallback(IModuleCallback cb);

//    int startAction(int reqId, String cmdAction, String cmdParam);

    int startAction(int reqId, int actionId, String params, IActionListener listener);

    int stopAction(int reqId, int actionId, boolean isResetHW);

    int setLambColor(int reqId, int target, int color);

    int setLambAnimation(int reqId, int target, int start, int end,
                             int startTime, int endTime, int repeat, int onTime, int freeze);

    boolean isActive();

    boolean finishModuleParser(int reqId, boolean ifParsedSucc);

    boolean finishModuleWithResponse(int reqId, boolean ifParsedSucc, String response);

    int getHWStatus(int function);

    String registerStatusListener(String type, IStatusListener listener);

    boolean unregisterStatusListener(String id);

    boolean startStatusSocket(String type, int socketPort);

    boolean closeStatusSocket(String type, int socketPort);

    void sendStatusReport(String type, String data);

    void disableEmergency();

    void disableBattery();

    void resetSystemStatus();

    int setLight(int reqId, String params);

    boolean installPatch(in Bundle bundle);

    String getRobotInfo(int reqType, String params);

    boolean updateRobotStatus(int status);

    oneway void setLanguage(String language);

    boolean switchScreen(boolean onOff);

    void getRobotStatus(String type, IStatusListener listener);

    void enableEmergency();

    boolean updateSystemStatus(String status, String data);

    void enableBattery();

    void disableFunctionKey();

    void enableFunctionKey();

    String reportTask(in Task task);

    void reportTaskEvent(in TaskEvent taskEvent);

    List<Task> getCurrentTask();

    int setLedLight(int reqId, String properties);

    int stopActionCancelCmd(int reqId, int actionId, boolean isResetHW, boolean isCancelStopCommand);

    boolean hasTopIR();

    boolean hasChargeIR();

    boolean isUseProZcbLed();

    boolean isHasClavicleLight();

    boolean isHasChestLight();

    boolean isHasProTrayLED();

    boolean hasMono();

    boolean hasTopMono();

    void disablePushWarning();

    void enablePushWarning();

    boolean hasHeightLimitCamera();

    void disableOutsideMapAlarm();

    void enableOutsideMapAlarm();

    boolean isUseAutoEffectLed();

    boolean isHasClavicleLed();

    boolean isHasChestLed();

    boolean isHasTrayLight();

    String getCreateMapType();

    boolean isSupportElevator();

    String isAlreadyInElevator();

    void setDefaultHeadAngle(int hAngle, int vAngle);
}
