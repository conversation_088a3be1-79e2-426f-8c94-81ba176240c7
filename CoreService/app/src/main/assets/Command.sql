REPLACE INTO command (type, function, timeout, stopCommand, isAsync, resource) VALUES
("cmd_get_status"                                    , 1, 1000, "", 0, "")
("cmd_navi_start_update"                             , 1,   -1, "", 1, "")
("cmd_navi_get_version"                              , 1,15000, "", 1, "")
("cmd_navi_get_update_params"                        , 1, 5000, "", 1, "")
("cmd_navi_go_location"                              , 1,   -1, "cmd_navi_stop_navigation", 1, "[Navigation]")
("cmd_navi_turn_by_navigation"                       , 1,   -1, "cmd_navi_stop_navigation", 1, "[Navigation]")
("cmd_navi_stop_navigation"                          , 1, 1000, "", 1, "")
("cmd_navi_is_in_navigation"                         , 1, 1000, "", 1, "")
("cmd_navi_set_location"                             , 1, 1000, "", 1, "")
("cmd_navi_get_location"                             , 1, 1000, "", 1, "")
("cmd_navi_is_in_location"                           , 1, 1000, "", 1, "")
("cmd_navi_move_direction"                           , 1,   -1, "cmd_navi_stop_move", 1, "[Navigation]")
("cmd_navi_move_direction_angle"                     , 1,   -1, "cmd_navi_stop_move", 1, "[Navigation]")
("cmd_navi_move_distance_angle"                      , 1,   -1, "cmd_navi_stop_move", 1, "[Navigation]")
("cmd_navi_move_direction_angle_soft"                , 1,   -1, "cmd_navi_stop_move", 1, "[Navigation]")
("cmd_navi_rotate_in_place"                          , 1,   -1, "cmd_navi_stop_move", 1, "[Navigation]")
("cmd_navi_stop_move"                                , 1, 1000, "", 1, "")
("cmd_navi_set_max_acceleration"                     , 1, 1000, "", 1, "")
("cmd_navi_get_navigation_angle_speed"               , 1, 1000, "", 1, "")
("cmd_navi_get_navigation_line_speed"                , 1, 1000, "", 1, "")
("cmd_navi_report_navigation_velocity_change"        , 1, 1000, "", 1, "")
("cmd_navi_cruiselayout_start"                       , 1, 1000, "", 1, "")
("cmd_navi_cruiselayout_stop"                        , 1, 1000, "", 1, "")
("cmd_navi_cruise_start"                             , 1,   -1, "", 1, "")
("cmd_navi_cruise_stop"                              , 1, 1000, "", 1, "")
("cmd_navi_relocation"                               , 1, 1000, "", 1, "")
("cmd_navi_get_error_log"                            , 1, 300 * 1000, "", 1, "")
("cmd_navi_get_log_file"                             , 1, 300 * 1000, "", 1, "")
("cmd_navi_pack_log_file"                            , 1, 300 * 1000, "", 1, "")
("cmd_navi_set_pose_location"                        , 1, 1000, "", 1, "")
("cmd_navi_set_pose_estimate"                        , 1, 10 * 1000, "", 1, "")
("cmd_navi_set_config"                               , 1, 10 * 1000, "", 1, "")
("cmd_navi_get_config"                               , 1, 10 * 1000, "", 1, "")
("cmd_navi_start_creating_map"                       , 1, 30 * 1000, "", 1, "")
("cmd_navi_stop_creating_map"                        , 1, 60 * 60 * 1000, "", 1, "")
("cmd_navi_switch_map"                               , 1, 30 * 1000, "", 1, "")
("cmd_navi_get_place_name"                           , 1, 5000, "", 1, "")
("cmd_navi_get_place_list"                           , 1, 5000, "", 1, "")
("cmd_navi_get_place_list_with_name"                 , 1, 5000, "", 1, "")
("cmd_navi_get_placelist_with_namelist"              , 1, 5000, "", 1, "")
("cmd_navi_go_position"                              , 1,   -1, "cmd_navi_stop_navigation", 1, "[Navigation]")
("cmd_navi_is_estimate"                              , 1, 1000, "", 1, "")
("cmd_navi_is_has_vision"                            , 1, 1000, "", 1, "")
("cmd_navi_save_estimate"                            , 1,   -1, "", 1, "")
("cmd_navi_reset_estimate"                           , 1, 2 * 1000, "", 1, "")
("cmd_navi_locate_vision"                            , 1, 2800, "", 1, "")
("cmd_navi_go_default_theta"                         , 1, 1000, "", 1, "")
("cmd_navi_get_full_check_status"                    , 1, 30*1000, "", 1, "")
("cmd_navi_get_sensor_status"                        , 1, 40*1000, "", 1, "")
("cmd_navi_switch_auto_charge_mode"                  , 1,   -1, "", 1, "")
("cmd_navi_get_serial_number"                        , 1, 1000, "", 1, "")
("cmd_navi_get_patrol_list"                          , 1, 1000, "", 1, "")
("cmd_navi_set_patrol_list"                          , 1, 1000, "", 1, "")
("cmd_navi_load_current_map"                         , 1, 20 * 1000, "", 1, "")
("cmd_navi_check_cur_navi_map"                       , 1, 30*1000, "", 1, "")
("cmd_navi_resume_special_place_theta"               , 1, 10000, "", 1, "[Navigation]")
("cmd_navi_query_multi_floor_config"                 , 1, 5000, "", 1, "")
("cmd_navi_insert_multi_floor_config"                , 1, 5000, "", 1, "")
("cmd_navi_update_multi_floor_config"                , 1, 5000, "", 1, "")
("cmd_navi_remove_multi_floor_config"                , 1, 5000, "", 1, "")
("cmd_navi_get_multi_floor_config_and_pose"          , 1, 5000, "", 1, "")
("cmd_navi_get_multi_floor_config_and_common_pose"   , 1, 5000, "", 1, "")
("cmd_head_register"                                 , 2, 1000, "", 1, "")
("cmd_head_unregister"                               , 2, 1000, "", 1, "")
("cmd_head_move_head"                                , 2, 1000, "", 1, "")
("cmd_head_switch_camera"                            , 2, 1000, "", 1, "")
("cmd_head_set_track_target"                         , 2, 3000, "", 1, "")
("cmd_head_switch_track_target"                      , 2, 3000, "", 1, "")
("cmd_head_stop_track_target"                        , 2, 1000, "", 1, "")
("cmd_head_get_all_person_infos"                     , 2,   -1, "cmd_head_stop_send_person_infos", 1, "")
("cmd_head_get_person_info_by_name"                  , 2,   -1, "cmd_head_stop_send_person_infos", 1, "")
("cmd_head_stop_send_person_infos"                   , 2, 1000, "", 1, "")
("cmd_head_search_person_by_name"                    , 2, 1000, "", 1, "")
("cmd_head_get_picture_by_id"                        , 2,10000, "", 1, "")
("cmd_head_get_mask_info_by_id"                      , 2,10000, "", 1, "")
("cmd_report_navigation_status"                      , 2, 1000, "", 1, "")
("cmd_head_get_last_position"                        , 2, 1000, "", 1, "")
("cmd_head_is_header_connected"                      , 2, 1000, "", 1, "")
("cmd_switch_detect_algorithm"                       , 2, 1000, "", 1, "")
("cmd_head_video_record"                             , 2, 1000, "", 1, "")
("cmd_head_get_serial_number"                        , 2, 1000, "", 1, "")
("cmd_head_testcase"                                 , 2, 1000, "", 1, "")
("cmd_head_register_by_pic"                          , 2, 1000, "", 1, "")
("cmd_head_start_scp"                                , 2, 5000, "", 1, "")
("cmd_head_start_update"                             , 2,   -1, "", 1, "")
("cmd_head_get_version"                              , 2,15000, "", 1, "")
("cmd_head_ota_get_version"                         , 2, 15000, "", 1, "")
("cmd_head_get_update_params"                        , 2, 5000, "", 1, "")
("cmd_head_get_gesture_infos"                        , 2,   -1, "", 1, "")
("cmd_head_stop_gesture_infos"                       , 2, 1000, "", 1, "")
("cmd_head_get_movement"                             , 2, 1000, "", 1, "")
("cmd_head_get_all_face_pos"                         , 2,   -1, "cmd_head_stop_all_face_pos", 1, "")
("cmd_head_stop_all_face_pos"                        , 2, 1000, "", 1, "")
("cmd_head_get_status"                               , 2, 1000, "", 1, "")
("cmd_head_restart"                                  , 2, 1000, "", 1, "")
("cmd_head_get_camera_status"                        , 2, 1000, "", 1, "")
("cmd_head_get_depth_status"                         , 2,10000, "", 1, "")
("cmd_head_get_fov_status"                           , 2,10000, "", 1, "")
("cmd_head_start_person_statistic"                   , 2, 1000, "", 1, "")
("cmd_head_stop_person_statistic"                    , 2, 1000, "", 1, "")
("cmd_head_get_person_statistic"                     , 2, 5000, "", 1, "")
("cmd_head_start_vision"                             , 2, 2000, "", 1, "")
("cmd_head_stop_vision"                              , 2, 2000, "", 1, "")
("cmd_head_start_head_count"                         , 2,   -1, "cmd_head_stop_head_count", 1, "")
("cmd_head_stop_head_count"                          , 2, 1000, "", 1, "")
("cmd_head_get_head_count"                           , 2, 1000, "", 1, "")
("cmd_head_start_body_report"                        , 2, 2000, "", 1, "")
("cmd_head_stop_body_report"                         , 2, 2000, "", 1, "")
("cmd_head_record_face_start"                        , 2, 1000, "", 1, "")
("cmd_head_record_face_stop"                         , 2, 1000, "", 1, "")
("cmd_head_enable_avatar_face"                       , 2, 1000, "", 1, "")
("cmd_head_disable_avatar_face"                      , 2, 1000, "", 1, "")
("cmd_head_inspection"                               , 2, 60 * 1000, "", 1, "")
("cmd_head_picture_report_config"                    , 2, 1000, "", 1, "")
("cmd_head_turn_head"                                , 2, 1000, "", 1, "")
("cmd_head_recovery"                                 , 2, 60 * 1000, "", 1, "")
("cmd_head_unqualified_picture_config"               , 2, 1000, "", 1, "")
("cmd_head_picture_report_config"                    , 2, 1000, "", 1, "")
("cmd_head_get_fov_camera_info"                      , 2,10000, "", 1, "")
("cmd_remote_register"                               , 5, 5000, "", 1, "")
("cmd_remote_detect"                                 , 5, 60 * 1000, "", 1, "")
("cmd_remote_get_person_info"                        , 5, 60 * 1000, "", 1, "")
("cmd_remote_upload_map_to_server"                   , 5, 60 * 1000, "", 1, "")
("cmd_remote_get_domain"                             , 5, 5000, "", 1, "")
("cmd_remote_post_emergency_msg"                     , 5, 60 * 1000, "", 1, "")
("cmd_navi_remove_map"                               , 1, 30 * 1000, "", 1, "")
("cmd_navi_refresh_md5"                              , 1, 1000, "", 1, "")
("cmd_navi_remove_location"                          , 1, 1000, "", 1, "")
("cmd_navi_get_map_name"                             , 1, 1000, "", 1, "")
("cmd_navi_recovery"                                 , 1, 60 * 1000, "", 1, "")
("cmd_can_get_status"                                , 6,   -1, "", 1, "")
("cmd_can_lamp_open"                                 , 6,   -1, "", 1, "")
("cmd_can_lamp_jni2"                                 , 6,   -1, "", 1, "")
("cmd_can_lamp_anim"                                 , 6,   -1, "", 1, "")
("cmd_can_lamp_color"                                , 6,   -1, "", 1, "")
("cmd_can_lamp_close"                                , 6,   -1, "", 1, "")
("cmd_can_auto_charge_start"                         , 6,   -1, "", 1, "")
("cmd_can_auto_charge_end"                           , 6,   -1, "", 1, "")
("cmd_can_get_charge_status"                         , 6,   -1, "", 1, "")
("cmd_can_get_emergency_status"                      , 6,   -1, "", 1, "")
("cmd_can_ota_start"                                 , 6,   -1, "", 1, "")
("cmd_can_ota_get_state"                             , 6,   -1, "", 1, "")
("cmd_can_get_board_version"                         , 6,   -1, "", 1, "")
("cmd_can_get_motor_h_version"                       , 6,   -1, "", 1, "")
("cmd_can_get_motor_v_version"                       , 6,   -1, "", 1, "")
("cmd_can_get_psb_version"                           , 6,   -1, "", 1, "")
("cmd_can_get_psb_s_version"                         , 6,   -1, "", 1, "")
("cmd_can_get_auto_charge_version"                   , 6,   -1, "", 1, "")
("cmd_can_get_battery_version"                       , 6,   -1, "", 1, "")
("cmd_can_get_wheel_l_version"                       , 6,   -1, "", 1, "")
("cmd_can_get_wheel_r_version"                       , 6,   -1, "", 1, "")
("cmd_can_robot_reboot"                              , 6, 1000, "", 1, "")
("cmd_can_get_infrared_info"                         , 6, -1, "cmd_can_stop_send_infrared_info", 1, "")
("cmd_can_start_charge_without_estimate"             , 6, -1, "", 1, "")
("cmd_can_set_vertical_max_limit_angle"              , 6, -1, "", 1, "")
("cmd_can_set_lock_enable"                           , 6, -1, "", 1, "")
("cmd_can_get_door_status"                           , 6, 1000, "", 1, "")
("cmd_can_dormancy_start"                            , 6, -1, "", 1, "")
("cmd_can_cap_screen"                                , 6, 5000, "", 1, "")
("cmd_can_get_cpu_temperature"                       , 6, 5000, "", 1, "")
("cmd_navi_get_position"                             , 1, 1000, "", 1, "")
("cmd_can_shipping_mode"                             , 6,   -1, "", 1, "")
("cmd_navi_set_map_info"                             , 1, 120 * 1000, "", 1, "")
("cmd_remote_qrcode"                                 , 5, 60 * 1000, "", 1, "")
("cmd_remote_verify"                                 , 5, 60 * 1000, "", 1, "")
("cmd_remote_phone_verify"                           , 5, 60 * 1000, "", 1, "")
("cmd_remote_skill"                                  , 5, 5000, "", 1, "")
("cmd_remote_postmsg"                                , 5, 60 * 1000, "", 1, "")
("cmd_remote_guestinfo"                              , 5, 60 * 1000, "", 1, "")
("cmd_remote_register_tempid"                        , 5, 60 * 1000, "", 1, "")
("cmd_remote_wakeup_times"                           , 5, 5000, "", 1, "")
("cmd_remote_bind_status"                            , 5, 60 * 1000, "", 1, "")
("cmd_remote_get_map_id"                             , 5, 60 * 1000, "", 1, "")
("cmd_can_battery_time_remain"                       , 6,   -1, "", 1, "")
("cmd_can_charge_time_remain"                        , 6,   -1, "", 1, "")
("cmd_can_get_rotate_support"                        , 6, 2000, "", 1, "")
("cmd_remote_post_battery_time_remain"               , 5, 5000, "", 1, "")
("cmd_remote_post_charge_time_remain"                , 5, 5000, "", 1, "")
("cmd_remote_first_charging"                         , 5, 1000, "", 1, "")
("cmd_remote_charge_pile"                            , 5, 1000, "", 1, "")
("cmd_remote_post_prepared"                          , 5, 1000, "", 1, "")
("cmd_remote_post_set_place"                         , 5, 60 * 1000, "", 1, "")
("cmd_remote_post_cruise_status"                     , 5, 60 * 1000, "", 1, "")
("cmd_navi_set_fixed_estimate"                        , 1, 10 * 1000, "", 1, "")
("cmd_remote_upload_map_pkg"                         , 5, 60 * 1000, "", 1, "")
("cmd_remote_upload_place_list"                      , 5, 60 * 1000, "", 1, "")
("cmd_remote_delete_map"                             , 5, 60 * 1000, "", 1, "")
("cmd_remote_switch_map_info"                        , 5, 60 * 1000, "", 1, "")
("cmd_remote_get_map_list_info"                      , 5, 60 * 1000, "", 1, "")
("cmd_remote_download_map_pkg"                       , 5, 60 * 1000, "", 1, "")
("cmd_navi_parse_place_list"                         , 1, 1000, "", 1, "")
("cmd_remote_upload_oper_log"                        , 5, 60 * 1000, "", 1, "")
("cmd_navi_set_cruise_route"                          , 1, 1000, "", 1, "")
("cmd_navi_get_cruise_route"                          , 1, 1000, "", 1, "")
("cmd_navi_clear_cur_navi_map"                        , 1, 20000, "", 1, "")
("cmd_navi_check_obstacle"                            , 1, 1000, "", 1, "")
("cmd_navi_edit_place"                                , 1, 1000, "", 1, "")
("cmd_navi_get_placelist_by_mapname"                  , 1, 1000, "", 1, "")
("cmd_remote_first_config_login_state"                , 5, 5000, "", 1, "")
("cmd_remote_first_config_robot_info"                 , 5, 5000, "", 1, "")
("cmd_remote_modify_detect_name"                      , 5, 60 * 1000, "", 1, "")
("cmd_remote_ppt_mqtt_pipe"                           , 5, 1000, "", 1, "")
("cmd_remote_ppt_mqtt_action"                         , 5, 1000, "", 1, "")
("cmd_remote_check_bind_status"                       , 5, 60 * 1000, "", 1, "")
("cmd_remote_text_to_mp3"                             , 5, 60 * 1000, "", 1, "")
("cmd_navi_switch_manual_mode"                        , 1,   -1, "", 1, "")
("cmd_navi_time_out_report"                           , 1,   -1, "", 1, "")
("cmd_navi_time_out_msg_delete"                       , 1,   -1, "", 1, "")
("cmd_navi_get_shot_log"                              , 1,   1000 * 60, "", 1, "")
("cmd_navi_get_log_by_id"                             , 1,   5000, "", 1, "")
("cmd_navi_update_log_status_by_id"                   , 1,   5000, "", 1, "")
("cmd_navi_rename_map"                                , 1,   3000, "", 1, "")
("cmd_navi_clear_cruise_route"                        , 1, 1000, "", 1, "")
("cmd_navi_set_radar_status"                          , 1,   30 * 1000, "", 1, "")
("cmd_navi_query_radar_status"                        , 1,   5000, "", 1, "")
("cmd_guide_score"                                    , 5, 35*1000, "", 1, "")
("cmd_navi_check_pose_position"                       , 1, 1000, "", 1, "")
("cmd_remote_get_ask_way_list"                        , 5, 5000, "", 1, "")
("cmd_remote_rn_install_status"                       , 5, 5000, "", 1, "")
("cmd_remote_next_state"                              , 5, 1000, "", 1, "")
("cmd_remote_refresh_qrcode"                          , 5, 1000, "", 1, "")
("cmd_remote_skill_data_report"                       , 5, 50000, "", 1, "")
("cmd_remote_unbind_robot"                            , 5, 50000, "", 1, "")
("cmd_navi_move_distance_angle_with_obstacles"       , 1,   -1, "cmd_navi_stop_move", 1, "[Navigation]")
("cmd_navi_move_direction_angle_obstacles"           , 1,   -1, "cmd_navi_stop_move", 1, "[Navigation]")
("cmd_navi_set_min_obstacles_distance"                , 1,   -1, "", 1, "")
("cmd_navi_reset_min_obstacles_distance"              , 1,   -1, "", 1, "")
("cmd_navi_connect_status"                            , 1, 1000, "", 1, "")
("cmd_navi_add_mapping_pose"                          , 1, 5000, "", 1, "")
("cmd_navi_delete_mapping_pose"                       , 1, 5000, "", 1, "")
("cmd_navi_rename_mapping_pose"                       , 1, 5000, "", 1, "")
("cmd_navi_get_map_status"                            , 1, 5000, "", 1, "")
("cmd_navi_save_place_list"                           , 1, 5000, "", 1, "")
("cmd_navi_update_place_list"                         , 1, 5000, "", 1, "")
("cmd_navi_set_relocation"                            , 1, 10*1000, "", 1, "")
("cmd_navi_vision_charge_start"                       , 1, 180*1000, "", 1, "")
("cmd_navi_vision_charge_stop"                        , 1, 180*1000, "", 1, "")
("cmd_can_get_multi_func_switch_state"                , 6, 2000, "", 1, "")
("cmd_navi_query_multiple_map_status"                 , 1, 1000, "", 1, "")
("cmd_navi_get_navi_angle_speed"                         , 1, 5000, "", 1, "")
