REPLACE INTO actionInfo (actionName, actionId, isCommon, resource, permission) VALUES
("action_common"                                        , 100, 1, "", 0)
("action_get_status"                                    , 101, 1, "", 0)
("action_navi_start_update"                             , 102, 1, "", 0)
("action_navi_get_version"                              , 103, 1, "", 0)
("action_navi_go_location"                              , 104, 1, "[Navigation,Radar]", 0)
("action_navi_stop_navigation"                          , 105, 1, "", 0)
("action_navi_is_in_navigation"                         , 106, 1, "", 0)
("action_navi_set_location"                             , 107, 1, "", 0)
("action_navi_get_location"                             , 108, 1, "", 0)
("action_navi_is_in_location"                           , 109, 1, "", 0)
("action_navi_move_direction"                           , 110, 1, "[Navigation,Radar]", 0)
("action_navi_move_direction_angle"                     , 111, 1, "[Navigation,Radar]", 0)
("action_navi_move_distance_angle"                      , 112, 1, "[Navigation,Radar]", 0)
("action_navi_stop_move"                                , 113, 1, "", 0)
("action_navi_set_max_acceleration"                     , 114, 1, "", 0)
("action_navi_get_navigation_angle_speed"               , 115, 1, "", 0)
("action_navi_get_navigation_line_speed"                , 116, 1, "", 0)
("action_navi_report_navigation_velocity_change"        , 117, 1, "", 0)
("action_navi_cruiselayout_start"                       , 118, 1, "", 0)
("action_navi_cruiselayout_stop"                        , 119, 1, "", 0)
("action_navi_cruise_start"                             , 120, 1, "", 0)
("action_navi_cruise_stop"                              , 121, 1, "", 0)
("action_navi_relocation"                               , 122, 1, "", 0)
("action_navi_set_pose_location"                        , 123, 1, "", 0)
("action_navi_set_pose_estimate"                        , 124, 1, "", 0)
("action_navi_start_creating_map"                       , 125, 1, "[Navigation,Radar]", 0)
("action_navi_stop_creating_map"                        , 126, 0, "", 0)
("action_navi_switch_map"                               , 127, 1, "", 0)
("action_navi_get_place_name"                           , 128, 1, "", 0)
("action_navi_get_place_list"                           , 129, 1, "", 0)
("action_remote_ppt_mqtt_pipe"                          , 130, 1, "", 0)
("action_navi_go_position"                              , 131, 0, "[Navigation,Radar]", 0)
("action_remote_ppt_mqtt_action"                        , 132, 1, "", 0)
("action_navi_is_estimate"                              , 133, 1, "", 0)
("action_navi_save_estimate"                            , 134, 1, "", 0)
("action_navi_go_default_theta"                         , 135, 1, "", 0)
("action_head_register"                                 , 136, 1, "", 0)
("action_head_unregister"                               , 137, 1, "", 0)
("action_head_move_head"                                , 138, 1, "", 0)
("action_head_switch_camera"                            , 139, 1, "", 0)
("action_head_set_track_target"                         , 140, 1, "", 0)
("action_head_get_all_person_infos"                     , 141, 0, "", 0)
("action_head_get_person_info_by_name"                  , 142, 1, "", 0)
("action_head_stop_send_person_infos"                   , 143, 1, "", 0)
("action_head_search_person_by_name"                    , 144, 1, "", 0)
("action_head_get_picture_by_id"                        , 145, 1, "", 0)
("action_report_navigation_status"                      , 146, 1, "", 0)
("action_head_get_last_position"                        , 147, 1, "", 0)
("action_head_is_header_connected"                      , 148, 1, "", 0)
("action_switch_detect_algorithm"                       , 149, 1, "", 0)
("action_head_video_record"                             , 151, 1, "", 0)
("action_head_testcase"                                 , 152, 1, "", 0)
("action_head_register_by_pic"                          , 153, 1, "", 0)
("action_head_start_update"                             , 154, 1, "", 0)
("action_head_get_version"                              , 155, 1, "", 0)
("action_head_get_update_params"                        , 156, 1, "", 0)
("action_head_get_gesture_infos"                        , 157, 1, "", 0)
("action_head_stop_gesture_infos"                       , 158, 1, "", 0)
("action_head_get_movement"                             , 159, 1, "", 0)
("action_head_get_all_face_pos"                         , 160, 1, "", 0)
("action_head_stop_all_face_pos"                        , 161, 1, "", 0)
("action_head_get_status"                               , 162, 1, "", 0)
("action_head_restart"                                  , 163, 1, "", 0)
("action_head_get_camera_status"                        , 164, 1, "", 0)
("action_head_record_face_data"                         , 222, 1, "", 0)
("action_navi_remove_map"                               , 165, 1, "", 0)
("action_navi_remove_location"                          , 166, 1, "", 0)
("action_remote_register"                               , 167, 1, "", 0)
("action_remote_detect"                                 , 168, 1, "", 0)
("action_stop_track_target"                             , 169, 1, "", 0)
("action_navi_get_map_name"                             , 170, 1, "", 0)
("action_navi_get_position"                             , 171, 1, "", 0)
("action_can_auto_charge_start"                         , 172, 1, "", 0)
("action_can_auto_charge_end"                           , 173, 1, "", 0)
("action_can_auto_charge_status"                        , 174, 1, "", 0)
("action_switch_auto_charge_mode"                       , 175, 1, "", 0)
("action_navi_set_map_info"                             , 176, 1, "", 0)
("action_remote_qrcode"                                 , 177, 1, "", 0)
("action_remote_verify"                                 , 178, 1, "", 0)
("action_remote_skill"                                  , 179, 1, "", 0)
("action_remote_postmsg"                                , 180, 1, "", 0)
("action_remote_guestinfo"                              , 181, 1, "", 0)
("action_remote_register_tempid"                        , 182, 1, "", 0)
("action_remote_bind_status"                            , 183, 1, "", 0)
("action_can_emergency_status"                          , 184, 1, "", 0)
("action_can_lamp_animation"                            , 185, 1, "", 0)
("action_can_lamp_color"                                , 186, 1, "", 0)
("action_remote_post_remote_message"                    , 187, 1, "", 0)
("action_remote_wakeup_times"                           , 188, 1, "", 0)
("action_can_battery_time_remain"                       , 189, 1, "", 0)
("action_can_charge_time_remain"                        , 190, 1, "", 0)
("action_remote_post_battery_time_remain"               , 191, 1, "", 0)
("action_remote_post_charge_time_remain"                , 192, 1, "", 0)
("action_head_get_depth_status"                         , 193, 1, "", 0)
("action_head_get_fov_status"                           , 194, 1, "", 0)
("action_navi_get_update_params"                        , 195, 1, "", 0)
("action_can_get_motor_h_version"                       , 196, 1, "", 0)
("action_can_get_motor_v_version"                       , 197, 1, "", 0)
("action_can_get_psb_version"                           , 198, 1, "", 0)
("action_can_robot_reboot"                              , 199, 1, "", 1)
("action_navi_reset_estimate"                           , 200, 1, "", 0)
("action_remote_first_charging"                         , 201, 1, "", 0)
("action_remote_charge_pile"                            , 202, 1, "", 0)
("action_remote_post_prepared"                          , 203, 1, "", 0)
("action_head_get_serial_number"                        , 204, 1, "", 0)
("action_navi_get_serial_number"                        , 205, 1, "", 0)
("action_remote_post_set_place"                         , 206, 1, "", 0)
("action_navi_get_place_list_with_name"                 , 211, 1, "", 0)
("action_navi_set_config"                               , 207, 1, "", 0)
("action_navi_get_config"                               , 208, 1, "", 0)
("action_navi_set_patrol_list"                          , 209, 1, "", 0)
("action_navi_get_patrol_list"                          , 210, 1, "", 0)
("action_navi_get_placelist_with_namelist"              , 212, 1, "", 0)
("action_head_master_switch"                            , 213, 1, "", 0)
("action_can_get_auto_charge_version"                   , 214, 1, "", 0)
("action_can_get_battery_version"                       , 215, 1, "", 0)
("action_navi_resume_special_place_theta"               , 216, 1, "[Navigation,Radar]", 0)
("action_can_shipping_mode"                             , 217, 1, "", 0)
("action_remote_upload_map_to_server"                   , 218, 1, "", 0)
("action_navi_refresh_md5"                              , 219, 1, "", 0)
("action_remote_post_cruise_status"                     , 220, 1, "", 0)
("action_remote_get_map_id"                             , 221, 1, "", 0)
("action_can_get_rotate_support"                        , 223, 1, "", 0)
("action_remote_get_person_info"                        , 224, 1, "", 0)
("action_head_record_face_data"                         , 222, 1, "", 0)
("action_navi_load_current_map"                         , 225, 1, "", 0)
("action_remote_upload_map_pkg"                         , 226, 1, "", 0)
("action_remote_upload_place_list"                      , 227, 1, "", 0)
("action_remote_delete_map"                             , 228, 1, "", 0)
("action_remote_switch_map_info"                        , 229, 1, "", 0)
("action_remote_get_map_list_info"                      , 230, 1, "", 0)
("action_download_map_pkg"                              , 231, 0, "", 0)
("action_navi_parse_place_list"                         , 232, 1, "", 0)
("action_remote_upload_oper_log"                        , 233, 1, "", 0)
("action_navi_set_fixed_estimate"                       , 234, 1, "", 0)
("action_navi_is_has_vision"                            , 235, 1, "", 0)
("action_navi_set_cruise_route"                         , 236, 1, "", 0)
("action_navi_get_cruise_route"                         , 237, 1, "", 0)
("action_navi_clear_cur_navi_map"                       , 238, 1, "", 0)
("action_navi_edit_place"                               , 239, 1, "", 0)
("action_navi_get_placelist_by_mapname"                 , 240, 1, "", 0)
("action_remote_modify_detect_name"                     , 241, 1, "", 0)
("action_remote_first_config_login_state"               , 242, 1, "", 0)
("action_remote_first_config_robot_info"                , 243, 1, "", 0)
("action_can_get_infrared_info"                         , 244, 1, "", 0)
("action_can_start_charge_without_estimate"             , 245, 1, "", 0)
("action_can_get_wheel_l_version"                       , 246, 1, "", 0)
("action_can_get_wheel_r_version"                       , 247, 1, "", 0)
("action_get_error_log"                                 , 248, 1, "", 0)
("action_pack_log_file"                                 , 249, 1, "", 0)
("action_get_log_file"                                  , 250, 1, "", 0)
("action_head_switch_track_target"                      , 251, 1, "", 0)
("action_can_set_vertical_max_limit_angle"              , 252, 1, "", 0)
("action_text_to_mp3"                                   , 253, 1, "", 0)
("action_navi_check_cur_navi_map"                       , 254, 1, "", 0)
("action_navi_time_out_msg_delete"                      , 255, 1, "", 0)
("action_navi_time_out_report"                          , 256, 1, "", 0)
("action_navi_get_shot_log"                             , 257, 1, "", 0)
("action_navi_get_log_by_id"                            , 258, 1, "", 0)
("action_navi_update_log_status_by_id"                  , 259, 1, "", 0)
("action_guide_score_recording"                         , 260, 1, "", 0)
("action_navi_rename_map"                               , 261, 1, "", 0)
("action_navi_clear_cruise_route"                       , 262, 1, "", 0)
("action_navi_set_radar_status"                         , 263, 1, "", 0)
("action_navi_query_radar_status"                       , 264, 1, "", 0)
("action_navi_check_pose_position"                      , 265, 1, "", 0)
("action_head_turn_head"                                , 266, 1, "", 0)
("action_remote_get_ask_way_list"                       , 267, 1, "", 0)
("action_remote_rn_install_status"                      , 268, 1, "", 0)
("action_head_start_vision"                             , 304, 1, "", 0)
("action_head_stop_vision"                              , 305, 1, "", 0)
("action_head_start_head_count"                         , 306, 0, "", 0)
("action_head_stop_head_count"                          , 307, 1, "", 0)
("action_head_start_body_report"                        , 310, 1, "", 0)
("action_head_stop_body_report"                         , 311, 1, "", 0)
("action_remote_next_state"                             , 312, 1, "", 0)
("action_remote_refresh_qrcode"                         , 313, 1, "", 0)
("action_remote_skill_data_report"                      , 314, 1, "", 0)
("action_navi_turn_by_navigation"                       , 315, 1, "[Navigation,Radar]", 0)
("action_head_avatar_face_data"                         , 316, 1, "", 0)
("action_navi_check_obstacle"                           , 317, 1, "", 0)
("action_head_recovery"                                 , 318, 1, "", 0)
("action_navi_recovery"                                 , 319, 1, "", 0)
("action_remote_unbind_robot"                           , 320, 1, "", 0)
("action_can_set_lock_enable"                           , 321, 1, "", 0)
("action_can_get_door_status"                           , 322, 1, "", 0)
("action_can_dormancy_start"                            , 323, 1, "", 0)
("action_can_cap_screen"                                , 325, 1, "", 0)
("action_head_picture_report_config"                    , 326, 1, "", 0)
("action_head_unqualified_picture_config"               , 327, 1, "", 0)
("action_head_get_head_count"                           , 329, 1, "", 0)
("action_can_get_cpu_temperature"                       , 330, 1, "", 0)
("action_navi_save_place_list"                          , 331, 1, "", 0)
("action_navi_get_international_place_list"             , 332, 1, "", 0)
("action_navi_update_place_list"                        , 333, 1, "", 0)
("action_head_get_fov_camera_info"                      , 334, 1, "", 0)
("action_remote_tencent_upload"                         , 335, 1, "", 0)
("action_navi_add_mapping_pose"                         , 336, 1, "", 0)
("action_navi_delete_mapping_pose"                      , 337, 1, "", 0)
("action_navi_get_map_status"                           , 338, 1, "", 0)
("action_navi_set_force_estimate"                       , 339, 1, "", 0)
("action_navi_rename_mapping_pose"                      , 340, 1, "", 0)
("action_navi_get_sensor_status"                        , 341, 1, "", 0)
("action_navi_connect_status"                           , 342, 1, "", 0)
("action_navi_move_distance_angle_with_obstacles"       , 343, 1, "[Navigation,Radar]", 0)
("action_navi_set_min_obstacles_distance"               , 344, 1, "", 0)
("action_navi_move_direction_angle_obstacles"           , 345, 1, "[Navigation,Radar]", 0)
("action_navi_reset_min_obstacles_distance"             , 346, 1, "", 0)
("action_navi_vision_charge_start"                      , 347, 1, "[Navigation,Radar]", 0)
("action_navi_vision_charge_stop"                       , 348, 1, "[Navigation,Radar]", 0)
("action_navi_start_extend_map"                         , 349, 1, "", 0)
("action_navi_reload_map"                               , 350, 1, "", 0)
("action_navi_load_map"                                 , 351, 1, "", 0)
("action_navi_cancel_create_map"                        , 352, 1, "", 0)
("action_navi_set_relocation"                           , 353, 1, "", 0)
("action_remote_delete_place_list"                      , 354, 1, "", 0)
("action_remote_get_place_list"                         , 355, 1, "", 0)
("action_navi_query_multiple_map_status"                , 356, 1, "", 0)
("action_navi_get_map_info"                             , 365, 1, "", 0)
("action_navi_set_map_sync_state"                       , 366, 1, "", 0)
("action_navi_set_map_uuid"                             , 367, 1, "", 0)
("action_navi_set_map_finish_state"                     , 368, 1, "", 0)
("action_navi_set_map_update_time"                      , 369, 1, "", 0)
("action_navi_set_map_forbid_line"                      , 370, 1, "", 0)
("action_navi_load_current_map_without_pose_estimate"   , 375, 1, "", 0)
("action_head_get_mask_info_by_id"                      , 376, 1, "", 0)
("action_navi_save_road_data"                           , 377, 1, "", 0)
("action_navi_auto_draw_road"                           , 378, 1, "", 0)
("action_navi_query_multi_floor_config"                 , 379, 1, "", 0)
("action_navi_insert_multi_floor_config"                , 380, 1, "", 0)
("action_navi_update_multi_floor_config"                , 381, 1, "", 0)
("action_navi_remove_multi_floor_config"                , 382, 1, "", 0)
("action_navi_get_multi_floor_config_and_pose"          , 383, 1, "", 0)
("action_control_open_elevator_door"                    , 384, 1, "", 0)
("action_control_close_elevator_door"                   , 385, 1, "", 0)
("action_control_release_elevator"                      , 386, 1, "", 0)
("action_control_call_elevator"                         , 387, 1, "", 0)
("action_control_get_elevator_status"                   , 388, 1, "", 0)
("action_navi_get_multi_floor_config_and_common_pose"   , 389, 1, "", 0)
("action_navi_stop_expansion_map"                       , 390, 1, "", 0)
("action_navi_get_international_place_list_for_report"  , 391, 1, "", 0)
("action_navi_map_has_vision"                           , 392, 1, "", 0)
("action_navi_get_local_map_name_list"                  , 393, 1, "", 0)
("action_navi_get_local_map_info_list"                  , 394, 1, "", 0)
("action_navi_save_gate_data"                           , 395, 1, "", 0)
("action_navi_get_placelist_by_type"                    , 396, 1, "", 0)
("action_navi_go_position_by_type"                      , 397, 0, "[Navigation,Radar]", 0)
("action_remote_cancel_upload_file"                     , 398, 1, "", 0)
("action_navi_query_charge_area_config"                 , 399, 1, "", 0)
("action_navi_insert_charge_area_config"                , 400, 1, "", 0)
("action_navi_update_charge_area_config"                , 401, 1, "", 0)
("action_navi_remove_charge_area_config"                , 402, 1, "", 0)
("action_navi_has_obstacle_in_area"                     , 403, 1, "", 0)
("action_remote_get_floor_list"                         , 404, 1, "", 0)
("action_navi_rotate_in_place"                          , 405, 1, "[Navigation]", 0)
("action_lead"                                          ,  12, 0, "[Navigation,Radar,Head,HeadCamera]", 0)
("action_follow"                                        ,  13, 0, "[Navigation,Radar,Head,HeadCamera]", 0)
("action_navigation"                                    ,  14, 0, "[Navigation,Radar]", 0)
("action_register"                                      ,  15, 0, "", 0)
("action_unregister"                                    ,  16, 0, "", 0)
("action_focus_follow"                                  ,  17, 0, "[Navigation,Radar,Head,HeadCamera]", 0)
("action_wakeup"                                        ,  18, 0, "[Navigation,Radar,Head,HeadCamera]", 0)
("action_search_target"                                 ,  19, 0, "[Head]", 0)
("action_inspection"                                    ,  20, 0, "", 0)
("action_auto_navi_charge"                              ,  21, 0, "[Navigation,Radar]", 0)
("action_set_start_charge_pose"                         ,  22, 0, "[Navigation,Radar]", 0)
("action_cruise"                                        ,  23, 0, "[Navigation,Radar]", 0)
("action_reset_estimate"                                ,  24, 0, "", 0)
("action_locate_vision"                                 ,  25, 0, "", 0)
("action_set_current_map"                               ,  26, 0, "", 0)
("action_gongfu"                                        ,  27, 0, "[Navigation,Radar,Head]", 0)
("action_head_super"                                    ,  324, 0,"[Head]", 0)
("action_get_full_check_status"                         ,  28, 0, "", 0)
("action_special_cruise"                                ,  29, 0, "[Navigation,Radar]", 0)
("action_obstacle_follow"                               ,  30, 0, "[Navigation,Radar,Head,HeadCamera]", 0)
("action_pose_navigation"                               ,  31, 0, "[Navigation,Radar]", 0)
("action_navigation_back"                               ,  32, 0, "[Navigation,Radar]", 0)
("action_angle_reset"                                   ,  33, 0, "[Navigation,Radar,Head]", 0)
("action_go_position"                                   ,  34, 0, "[Navigation,Radar]", 0)
("action_body_follow"                                   ,  35, 0, "[Navigation,Radar,Head,HeadCamera]", 0)
("action_navigation_follow"                             ,  36, 0, "[Navigation,Radar]", 0)
("action_smart_focus_follow"                            ,  37, 0, "[Navigation,Radar,Head,HeadCamera]", 0)
("action_horizontal_turn_head"                          ,  40, 0, "[Head]", 0)
("action_recognize"                                     ,  41, 0, "", 0)
("action_robot_standby"                                 ,  42, 0, "", 0)
("action_face_track"                                    ,  43, 0, "[Navigation,Radar,Head]", 0)
("action_register_v0"                                   ,  44, 0, "", 0)
("action_recognize_v0"                                  ,  45, 0, "", 0)
("action_register_v1"                                   ,  46, 0, "", 0)
("action_recognize_v1"                                  ,  47, 0, "", 0)
("action_blue_fov_light"                                ,  48, 0, "", 0)
("action_red_fov_light"                                 ,  49, 0, "", 0)
("action_leave_charging_pile"                           ,  50, 0, "[Navigation]", 0)
("action_judge_charging_pile"                           ,  51, 0, "", 0)
("action_elevator_navigation"                           ,  52, 0, "[Navigation,Radar]", 0)
("action_control_electric_door"                         ,  53, 0, "", 0)
("action_head_reset_head"                               ,  54, 0, "", 0)
("action_radar_align"                                   ,  55, 0, "[Navigation,Radar]", 0)
