REPLACE INTO request (type, permission) VALUES
("req_ota_upgrade"                                    , "0")
("req_ota_upgrade_force"                              , "0")
("req_ota_remote"                                     , "0")
("req_ota_finish"                                     , "0")
("req_ota_downgrade"                                  , "0")
("relocate_switch_open"                               , "0")
("remote_bind_failed"                                 , "0")
("req_login_fail"                                     , "0")
("remote_bind_successful"                             , "0")
("req_remote_lock"                                    , "0")
("request_remote_push_map"                            , "0")
("req_function_key_press"                             , "0")
("req_function_key_release"                           , "0")
("remote_standby_start"                               , "0")
("remote_standby_stop"                                , "0")
("remote_stop_charging"                               , "0")
("remote_stop_charging_status"                        , "0")
("req_system_stop_charging"                           , "1")
("req_system_stop_charging_status"                    , "1")
("req_standby_start"                                  , "1")
("req_standby_stop"                                   , "1")
("req_bind_failed"                                    , "1")
("req_ota_result"                                     , "1")
("req_ota_inspect"                                    , "1")
("req_emergency"                                      , "1")
("req_emergency_press"                                , "1")
("req_emergency_release"                              , "1")
("req_battery_low"                                    , "1")
("req_battery_charging"                               , "1")
("req_battery_full"                                   , "1")
("req_normal"                                         , "1")
("req_ota_auth"                                       , "1")
("req_open_radar"                                     , "1")
("req_open_radar_succeed"                             , "1")
("req_set_light"                                      , "1")
("req_ota_download"                                   , "1")
("req_set_charge_pile"                                , "1")
("req_start_charge"                                   , "1")
("req_reposition"                                     , "1")
("relocate_switch_close"                              , "1")
("relocate_action"                                    , "1")
("req_speech_text"                                    , "1")
("req_stop_charging"                                  , "1")
("req_stop_charging_by_app"                           , "1")
("WeChat_set_charging_pile"                           , "1")
("WeChat_first_recharging"                            , "1")
("req_remote_relocate"                                , "1")
("req_hw_recovery"                                    , "1")
("req_hw_malfunction"                                 , "1")
("play_tts"                                           , "1")
("req_system_recovery"                                , "1")
("req_start_dormancy"                                 , "1")
("remote_push_map_need_switch"                        , "1")
("remote_push_map_no_switch"                          , "1")
("remote_go_position"                                 , "0")
("remote_go_pose"                                     , "0")
("remote_stop_navigation"                             , "0")
("system_remote_go_position"                          , "1")
("system_remote_go_pose"                              , "1")
("system_remote_stop_navigation"                      , "1")
("system_remote_lock_dialog"                          , "1")
("system_remote_lock_speech"                          , "1")
("system_remote_lock_lite_exit"                       , "1")
("system_remote_lock_full"                            , "1")
("system_remote_lock_full_exit"                       , "1")
("req_system_shutdown_timer"                          , "1")
("req_map_outside_release"                            , "1")
("req_map_outside"                                    , "1")
("req_system_time_warning_start"                      , "1")
("req_system_time_warning_stop"                       , "1")
("req_robot_being_pushed"                             , "1")
("req_robot_being_pushed_release"                     , "1")
("req_d430_calibration_start"                         , "1")
("req_multi_robot_error"                              , "1")
("req_enable_target_custom"                           , "1")
("req_chassis_sensor_normal"                          , "1")
("req_chassis_sensor_error"                           , "1")
("req_load_map"                                       , "1")
("req_stop_charging_confirm"                          , "1")
("req_leave_pile_go_point"                            , "1")
("system_go_charging"                                 , "0")
