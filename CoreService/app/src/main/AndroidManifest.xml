<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          xmlns:tools="http://schemas.android.com/tools"
          package="com.ainirobot.coreservice"
          android:sharedUserId="android.uid.system">

    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES"/>
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission android:name="android.permission.BROADCAST_STICKY"/>
    <uses-permission android:name="android.permission.SET_TIME_ZONE"/>
    <uses-permission android:name="com.ainirobot.permission.command"/>
    <uses-permission android:name="org.codeaurora.permission.POWER_OFF_ALARM" />

    <permission android:name="com.ainirobot.coreservice.robotSettingProvider"/>
    <permission android:name="com.ainirobot.coreservice.accountprovider"/>
    <permission android:name="com.ainirobot.coreservice.LanguageProvider"/>
    <permission android:name="com.ainirobot.coreservice.AuthProvider"/>

    <uses-feature android:name="android.hardware.camera"/>

    <application
        android:name=".ApplicationWrapper"
        android:allowBackup="true"
        android:icon="@mipmap/orion_default_icon"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/orion_default_icon"
        android:supportsRtl="true"
        tools:ignore="GoogleAppIndexingWarning">

        <activity android:name=".test.TestActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>

                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>

        <service
            android:name=".service.CoreService"
            android:exported="true"/>
        <service
            android:name=".daemon.DaemonService"
            android:enabled="true"
            android:exported="true"
            android:process=":Daemon">
        </service>
        <service
            android:name=".bi.BiReportService"
            android:enabled="true"
            android:exported="false"
            android:process=":BiReport">
        </service>
        <service
            android:name=".dump.DataDumpService"
            android:enabled="true"
            android:exported="false"
            android:process=":DataDump">
        </service>

        <service
            android:name=".daemon.PerformanceService"
            android:enabled="true"
            android:exported="false"
            android:process=":Performance">
        </service>

        <receiver android:name=".service.DeviceBoot">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE"/>
            </intent-filter>
        </receiver>

        <provider
            android:name=".data.robotsetting.RobotSettingProvider"
            android:authorities="com.ainirobot.coreservice.robotsettingprovider"
            android:exported="true"
            android:permission="com.ainirobot.coreservice.robotSettingProvider"/>
        <provider
            android:name=".data.account.databases.AccountProvider"
            android:authorities="com.ainirobot.coreservice.accountprovider"
            android:exported="false"
            android:permission="com.ainirobot.coreservice.accountprovider"/>
        <provider
            android:name=".data.core.DataProvider"
            android:authorities="com.ainirobot.coreservice.dataprovider"
            android:exported="true"/>
        <provider
            android:name="com.ainirobot.base.contentprovider.ErrorInfoContentProvider"
            android:authorities="com.ainirobot.base.contentprovider.ErrorInfo"
            android:exported="true"/>
        <provider
            android:name=".data.language.LanguageProvider"
            android:authorities="com.ainirobot.coreservice.LanguageProvider"
            android:exported="true"
            android:permission="com.ainirobot.coreservice.LanguageProvider"/>
        <provider
            android:name=".data.authorization.AuthProvider"
            android:authorities="com.ainirobot.coreservice.AuthProvider"
            android:exported="true"
            android:permission="com.ainirobot.coreservice.AuthProvider"/>

        <service
            android:name="com.ainirobot.base.cpumemory.aidl.CPUFreTempMonitorService"
            android:exported="true"
            android:process=":Performance">
            <intent-filter>
                <category android:name="android.intent.category.DEFAULT"/>
                <action android:name="android.service.aidl.CPUFreTempMonitorService"/>
            </intent-filter>
        </service>
    </application>

</manifest>
