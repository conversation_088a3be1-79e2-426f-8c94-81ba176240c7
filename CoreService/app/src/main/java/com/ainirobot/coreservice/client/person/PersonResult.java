package com.ainirobot.coreservice.client.person;

public class PersonResult {

    public static final int SUCCESS = 1;

    public static final int INITIALIZING = -1;
    public static final int DATA_SYNCING = -2;

    public static class Recognize {
        public static final int RECOGNIZE_ONLY_FACE = 100;
        public static final int RECOGNIZE_ALREADY_RUN = -100;
        public static final int RECOGNIZE_FAILED = -101;
    }

    public static class Register {
        public static final int REGISTER_ALREADY_RUN = -200;
        public static final int REGISTER_NAME_EMPTY = -201;
        public static final int REGISTER_NAME_DUPLICATE = -202;
        public static final int REGISTER_FAILED = -203;
    }

    public static class ReRegister {
        public static final int RE_REGISTER_ALREADY_RUN = -300;
        public static final int RE_REGISTER_USER_ID_EMPTY = -301;
        public static final int RE_REGISTER_USER_ID_NOT_EXIST = -302;
        public static final int RE_REGISTER_USER_ID_HAS_FACE_PAIR = -303;
        public static final int RE_REGISTER_FAILED = -304;
    }
}
