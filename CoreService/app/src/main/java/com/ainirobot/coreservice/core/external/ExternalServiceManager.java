/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.core.external;

import android.content.Intent;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.config.ServiceConfig;
import com.ainirobot.coreservice.core.module.Module;
import com.ainirobot.coreservice.service.CoreService;

import java.util.ArrayList;
import java.util.List;

public class ExternalServiceManager {

    private final static String TAG = ExternalServiceManager.class.getSimpleName();

    private final List<ExternalService> mExternalService = new ArrayList<>();
    private final OtaService mOtaService = new OtaService();
    private final CoreService mCore;

    private ExternalServiceWatchdog mWatch;

    public ExternalServiceManager(CoreService core) {
        mCore = core;
        mWatch = new ExternalServiceWatchdog();
    }

    public void addExternalService(ExternalService service) {
        Log.d(TAG, "Add external service [" + service + "]");
        if (mOtaService.equals(service)) {
            mOtaService.setConfig(service.getConfig());
            mExternalService.add(mOtaService);
            return;
        }

        if (mExternalService.contains(service)) {
            Log.d(TAG, "External service [" + service + "] is already exits");
            return;
        }

        synchronized (mExternalService) {
            mExternalService.add(service);
        }

        if (service.isEnable() && service.isKeepAlive()) {
            mWatch.add(service);
        }
    }

    public void removeExternalService(ExternalService service) {
        synchronized (mExternalService) {
            mExternalService.remove(service);
        }

        if (service.isEnable() && service.isKeepAlive()) {
            mWatch.remove(service);
        }
    }

    /**
     * 获取所有需要自检的Service
     *
     * @return
     */
    public List<ExternalService> getInspectService() {
        List<ExternalService> list = new ArrayList<>();
        synchronized (mExternalService) {
            for (ExternalService service : mExternalService) {
                if (service.isEnable() && service.isInspection()) {
                    list.add(service);
                }
            }
        }
        return list;
    }

    public List<ServiceConfig> getAllServiceConfig(String packageName) {
        List<ServiceConfig> list = new ArrayList<>();
        synchronized (mExternalService) {
            for (ExternalService service : mExternalService) {
                if (TextUtils.equals(packageName, service.getPackageName())
                        && service.getConfig() != null) {
                    list.add(service.getConfig());
                }
            }
        }
        Log.d(TAG, "Get service config : " + list + "  " + packageName);
        return list;
    }

    public ServiceConfig getServiceConfig(String serviceName) {
        synchronized (mExternalService) {
            for (ExternalService service : mExternalService) {
                if (TextUtils.equals(serviceName, service.getName())) {
                    return service.getConfig();
                }
            }
        }
        Log.d(TAG, "Get service config is empty : " + serviceName);
        return null;
    }

    public ExternalService getExternalService(String serviceName) {
        synchronized (mExternalService) {
            for (ExternalService service : mExternalService) {
                if (TextUtils.equals(serviceName, service.getName())) {
                    return service;
                }
            }
        }
        return null;
    }

    public List<ExternalService> getExternalServices() {
        return mExternalService;
    }

    public int getHWStatus(String serviceName) throws RemoteException {
        ExternalService service = getExternalService(serviceName);
        if (service != null) {
            return service.getStatus();
        }
        return Definition.HW_STATUS_NONE;
    }

    public OtaService getOtaService() {
        return mOtaService;
    }

    public void reset() {
        synchronized (mExternalService) {
            for (ExternalService service : mExternalService) {
                service.reset();
            }
        }
    }

    public void switchAppControl(String packageName, String lastPackageName) {
        Module module = mCore.getModuleManager().getModule(packageName);
        synchronized (mExternalService) {
            for (ExternalService service : mExternalService) {
                service.switchAppControl(packageName, lastPackageName);
            }
        }
        setLanguage(module);
    }

    public void setLanguage(Module module) {
        if (null == module) {
            setLanguage(mCore.getRobotInfoManager().getSystemLanguage());
        } else {
            setLanguage(module.getLanguage());
        }
    }

    public void setLanguage(String language) {
        if (TextUtils.isEmpty(language)) {
            language = mCore.getRobotInfoManager().getSystemLanguage();
        }
        synchronized (mExternalService) {
            for (ExternalService service : mExternalService) {
                service.setLanguage(language);
            }
            //通知App语言变化
            Intent intent = new Intent(Definition.ACTION_APP_LANGUAGE_CHANGE);
            intent.putExtra(Definition.LANGUAGE, language);
            ApplicationWrapper.getContext().sendBroadcast(intent);
        }
    }
}
