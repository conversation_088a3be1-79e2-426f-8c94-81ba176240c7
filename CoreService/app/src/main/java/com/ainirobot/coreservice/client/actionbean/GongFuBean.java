package com.ainirobot.coreservice.client.actionbean;

import android.text.TextUtils;

import com.google.gson.JsonObject;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * 连续动作剧本，动作由头部动作集合，底盘动作集合组成
 * <AUTHOR>
 * @see HeadAction 单个头部动作
 * @see FootAction 单个底盘动作
 */
public class GongFuBean extends BaseBean{

    private int mGongFuTyep;

    private List<HeadAction> mHeadActionList;
    private List<FootAction> mFootActionList;

    private String footActionJson;
    private String headActionJson;

    public String getFootActionJson() {
        return footActionJson;
    }

    public void setFootActionJson(String footActionJson) {
        this.footActionJson = footActionJson;
    }

    public String getHeadActionJson() {
        return headActionJson;
    }

    public void setHeadActionJson(String headActionJson) {
        this.headActionJson = headActionJson;
    }

    public List<HeadAction> getHeadActionList() {

        if(TextUtils.isEmpty(headActionJson)){
            return null;
        }

        mHeadActionList = new ArrayList<>();

        try {
            JSONArray array = new JSONArray(headActionJson);
            for(int i=0; i<array.length(); i++){
                JSONObject object = array.getJSONObject(i);
                HeadAction action = new HeadAction();
                action.setAngle_horizontal(object.optInt("angle_horizontal"));
                action.setAngle_vertical(object.optInt("angle_vertical"));
                action.setBeginTime(object.optLong("begin_time"));
                action.setSpeed_horizontal(object.optInt("speed_horizontal"));
                action.setSpeed_vertical(object.optInt("speed_vertical"));
                mHeadActionList.add(action);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return mHeadActionList;
    }

    public List<FootAction> getFootActionList() {

        if(TextUtils.isEmpty(footActionJson)){
            return null;
        }

        mFootActionList = new ArrayList<>();

        try {
            JSONArray array = new JSONArray(footActionJson);
            for(int i=0; i<array.length(); i++){
                JSONObject object = array.getJSONObject(i);
                FootAction action = new FootAction();
                action.setBeginTime(object.optLong("begin_time"));
                action.setAngle(Float.parseFloat(object.optString("angle")));
                action.setDistance(Float.parseFloat(object.optString("distance")));
                action.setSpeed(Float.parseFloat(object.optString("speed")));
                mFootActionList.add(action);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return mFootActionList;
    }

    /**
     * 剧本制作-添加头部动作
     * @param headActionList 头部动作集合
     */
    public void setHeadActionList(ArrayList<HeadAction> headActionList) {
        this.mHeadActionList = headActionList;
        if(headActionList == null || headActionList.size() < 1){
            headActionJson = "";
            return;
        }
        List<HeadAction> list = new ArrayList<>(headActionList);
        JSONArray array = new JSONArray();
        for(HeadAction action : list){
            array.put(action.toJsonObject());
        }
        headActionJson = array.toString();
    }

    /**
     * 剧本制作-添加底盘动作
     * @param footActionList 底盘动作集合
     */
    public void setFootActionList(ArrayList<FootAction> footActionList) {
        this.mFootActionList = footActionList;
        if(footActionList == null || footActionList.size() < 1){
            headActionJson = "";
            return;
        }
        List<FootAction> list = new ArrayList<>(footActionList);
        JSONArray array = new JSONArray();
        for(FootAction action : list){
            array.put(action.toJsonObject());
        }
        footActionJson = array.toString();
    }

    public int getmGongFuTyep() {
        return mGongFuTyep;
    }

    public void setmGongFuTyep(int mGongFuTyep) {
        this.mGongFuTyep = mGongFuTyep;
    }

    /**
     * 生成一个头部动作
     *
     * @param angle_horizontal 水平防线转动角度
     * @param angle_vertical 竖直方向转动角度
     * @param speed_horizontal 水平方向转动速度
     * @param speed_vertical 竖直方向转动速度
     * @param beginTime 该动作开始执行的事件
     * @return 一个头部动作
     */
    public HeadAction obtainHeadAction(int angle_horizontal, int angle_vertical,
                                       int speed_horizontal, int speed_vertical, long beginTime){
        return new HeadAction(angle_horizontal, angle_vertical, speed_horizontal, speed_vertical,
                beginTime);
    }

    /**
     * 生成一个底盘动作
     *
     * @param angle 底盘转动角度
     * @param speed 底盘转动速度
     * @param distance 底盘移动距离
     * @param beginTime 该动作执行时间
     * @return 新的底盘动作
     */
    public FootAction obtainFootAction(float angle, float speed, float distance, long beginTime){
        return new FootAction(angle, speed, distance, beginTime);
    }


    /**
     * 单个头部动作
     */
    public class HeadAction {
        private int headType;

        private int angle_horizontal;

        private int angle_vertical;

        private int speed_horizontal;

        private int speed_vertical;

        private Long beginTime;

        HeadAction(){}

        /**
         * 单个头部动作构造方法
         *
         * @param angle_horizontal 水平防线转动角度
         * @param angle_vertical 竖直方向转动角度
         * @param speed_horizontal 水平方向转动速度
         * @param speed_vertical 竖直方向转动速度
         * @param beginTime 该动作开始执行的事件
         */
        HeadAction(int angle_horizontal, int angle_vertical, int speed_horizontal, int speed_vertical, long beginTime){
            this.angle_vertical = angle_vertical;
            this.angle_horizontal = angle_horizontal;
            this.speed_horizontal = speed_horizontal;
            this.speed_vertical = speed_vertical;
            this.beginTime = beginTime;
        }

        public int getHeadType() {
            return headType;
        }

        public void setHeadType(int headType) {
            this.headType = headType;
        }

        public int getAngle_horizontal() {
            return angle_horizontal;
        }

        public void setAngle_horizontal(int angle_horizontal) {
            this.angle_horizontal = angle_horizontal;
        }

        public int getAngle_vertical() {
            return angle_vertical;
        }

        public void setAngle_vertical(int angle_vertical) {
            this.angle_vertical = angle_vertical;
        }

        public int getSpeed_horizontal() {
            return speed_horizontal;
        }

        public void setSpeed_horizontal(int speed_horizontal) {
            this.speed_horizontal = speed_horizontal;
        }

        public int getSpeed_vertical() {
            return speed_vertical;
        }

        public void setSpeed_vertical(int speed_vertical) {
            this.speed_vertical = speed_vertical;
        }

        public Long getBeginTime() {
            return beginTime;
        }

        public void setBeginTime(Long beginTime) {
            this.beginTime = beginTime;
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("head type: " + headType);
            sb.append(" , angle_horizontal: " + angle_horizontal);
            sb.append(" , angle_vertical: "+ angle_vertical);
            sb.append(" , speed_vertical: "+ speed_vertical);
            sb.append(" , speed_h: "+speed_horizontal);
            sb.append(" , begin time: "+ beginTime);
            return sb.toString();
        }

        public JSONObject toJsonObject(){
            JSONObject object = new JSONObject();

            try {
                object.put("angle_horizontal", angle_horizontal);
                object.put("angle_vertical", angle_vertical);
                object.put("speed_vertical", speed_vertical);
                object.put("speed_horizontal", speed_horizontal);
                object.put("begin_time", beginTime);
            } catch (JSONException e) {
                e.printStackTrace();
            }

            return object;
        }
    }

    /**
     * 单个底盘执行动作
     */
    public class FootAction {

        private Long beginTime;

        private float angle;

        private float speed;

        private float distance;

        FootAction(){}

        /**
         * 底盘动作构造方法
         *
         * @param angle 底盘转动角度
         * @param speed 底盘转动速度
         * @param distance 底盘移动距离
         * @param beginTime 该动作执行时间
         */
        FootAction(float angle, float speed, float distance, long beginTime){
            this.beginTime = beginTime;
            this.angle = angle;
            this.speed = speed;
            this.distance = distance;
        }

        public Long getBeginTime() {
            return beginTime;
        }

        public void setBeginTime(Long beginTime) {
            this.beginTime = beginTime;
        }

        public float getAngle() {
            return angle;
        }

        public void setAngle(float angle) {
            this.angle = angle;
        }

        public float getSpeed() {
            return speed;
        }

        public void setSpeed(float speed) {
            this.speed = speed;
        }

        public float getDistance() {
            return distance;
        }

        public void setDistance(float distance) {
            this.distance = distance;
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("angle: " + angle);
            sb.append(" , distance: " + distance);
            sb.append(" , speed: "+ speed);
            sb.append(" , begin time: "+ beginTime);
            return sb.toString();
        }

        public JSONObject toJsonObject(){
            JSONObject object = new JSONObject();
            try {
                object.put("angle", angle);
                object.put("distance", distance);
                object.put("speed", speed);
                object.put("begin_time", beginTime);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            return object;
        }
    }
}
