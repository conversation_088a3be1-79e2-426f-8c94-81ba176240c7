package com.ainirobot.coreservice.action.advance_charge.state;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.ChargeAdvancedAction;
import com.ainirobot.coreservice.action.StatusListener;
import com.ainirobot.coreservice.action.advance_charge.AdvanceChargeStateEnum;
import com.ainirobot.coreservice.action.advance_charge.ChargeCmdStateListener;
import com.ainirobot.coreservice.action.advance_charge.bean.StateData;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.AutoChargeBean;
import com.ainirobot.coreservice.client.actionbean.BasePoseBean;
import com.ainirobot.coreservice.client.actionbean.MultiRobotStatus;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public abstract class BaseAdvanceChargeState {

    protected String TAG;
    protected Gson mGson = new Gson();
    public static final long NAVIGATION_TIMEOUT = 2 * Definition.MINUTE;
    public static final long GO_NORMAL_NAVIGATION_TIMEOUT = 5 * Definition.MINUTE;
    protected boolean mIsStop;
    protected ChargeAdvancedAction mChargeAdvancedAction;
    private ChargeCmdStateListener chargeCmdStateListener;
    protected StateData mStateData;

    /**
     * 多机状态
     */
    protected volatile List<MultiRobotStatus> mRobotStatusList;
    private String mMultipleRobotStatusListener;

    public BaseAdvanceChargeState() {
        TAG = "ChargeAdvancedAction_" + this.getClass().getSimpleName();
    }

    public void start(ChargeAdvancedAction chargeAdvancedAction, StateData data, ChargeCmdStateListener chargeCmdStateListener) {
        mIsStop = false;
        this.mChargeAdvancedAction = chargeAdvancedAction;
        this.mStateData = data;
        this.chargeCmdStateListener = chargeCmdStateListener;
        onStart(chargeAdvancedAction, data);
    }

    protected String registerStatusListener(String type, StatusListener listener) {
        return mChargeAdvancedAction.registerStatusListener(type, listener);
    }

    protected void unregisterStatusListener(String type) {
        mChargeAdvancedAction.unregisterStatusListener(type);
    }

    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        if (mIsStop) {
            return false;
        }
        return onCmdResponse(cmdId, command, result, extraData);
    }

    public boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        if (mIsStop) {
            return false;
        }
        return onCmdStatusUpdate(cmdId, command, status, extraData);
    }

    protected void onStateUpdate(int state, String msg) {
        if (mIsStop) {
            return;
        }
        chargeCmdStateListener.onStateUpdate(state, msg);
    }

    protected void onStateRunSuccess() {
        if (mIsStop) {
            return;
        }
        chargeCmdStateListener.onStateRunSuccess(getNextState(), getData());
    }

    protected void onStateRunFailed(int errorCode, String errorMessage) {
        if (mIsStop) {
            return;
        }
        chargeCmdStateListener.onStateRunFailed(errorCode, errorMessage);
    }

    public void finishAction(int result, String message) {
        mChargeAdvancedAction.finishAction(result, message);
    }
    protected StateData getData() {
        return mStateData;
    }

    public void stop() {
        mIsStop = true;
        Log.d(TAG, "writeMultiRobotExtraData -1 -1 -1 ");
        writeMultiRobotExtraData(-1, -1, -1);
    }

    public void writeMultiRobotOccupiedPileIndex(int chargeOccupiedPileIndex) {
        this.writeMultiRobotExtraData(mStateData.getCurUseAreaId(), mStateData.getCurChargePriority(), chargeOccupiedPileIndex);
    }

    public void writeMultiRobotChargePriority(int areaId, int chargePriority) {
        this.writeMultiRobotExtraData(areaId, chargePriority, -1);
    }

    public void writeMultiRobotExtraData(int areaId, int chargePriority, int chargeOccupiedPileIndex) {

        ByteBuffer buffer = ByteBuffer.allocate(13);
        buffer.putInt(areaId);
        buffer.putInt(chargePriority);
        buffer.putInt(chargeOccupiedPileIndex);
        byte[] data = buffer.array();

        JSONArray array = new JSONArray();
        for (byte b : data) {
            array.put(b);
        }

        double currentTime = System.currentTimeMillis() / 1000.0;
        mChargeAdvancedAction.getApi().writeMultiRobotExtraData(mChargeAdvancedAction.getReqId(), array, currentTime);
    }

    protected void registerMultiRobotStatusListener() {
        mMultipleRobotStatusListener = registerStatusListener(Definition.STATUS_MULTIPLE_ROBOT_WORKING, new StatusListener() {
            @Override
            public void onStatusUpdate(String data) {
                if (mIsStop) {
                    return;
                }
                handleMultipleRobotData(data);
            }
        });
    }

    protected void unregisterMultiRobotStatusListener() {
        if (mMultipleRobotStatusListener != null) {
            unregisterStatusListener(mMultipleRobotStatusListener);
        }
    }

    private void handleMultipleRobotData(String data) {
        try {
            Type dataType = new TypeToken<List<MultiRobotStatus>>() {
            }.getType();
            mRobotStatusList = mGson.fromJson(data, dataType);
            boolean canContinue = checkMultiRobotStatus(mRobotStatusList);
            if (canContinue) {
                handleMultipleRobotStatus(mRobotStatusList);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 当底盘的多机状态报错时，导航任务不能继续
     * 多机报错状态只有当前机器才会上报，其他机器的errorStatus无意义。
     * 当时间戳相差超过5s 认为该条状态不可用【删除该状态】
     *
     * @param robotStatusList 多机状态信息
     * @return true无报错信息任务继续  false会结束当前任务。
     */
    private boolean checkMultiRobotStatus(List<MultiRobotStatus> robotStatusList) {
        for (int i = 0; i < robotStatusList.size(); i++) {
            MultiRobotStatus robotStatus = robotStatusList.get(i);
            if (robotStatus.isCurRobot()) {
                //删除超时的状态
                checkMultiRobotStatusTimeout(robotStatus.getTime(), robotStatusList);
                //判断error状态
                return checkMultiRobotErrorStatus(robotStatus);
            }
        }
        return true;
    }

    /**
     * 找到当前多机状态中是否有超时的状态，若有则删除
     *
     * @param curTime         当前机器的时间戳
     * @param robotStatusList 多机状态列表
     */
    private void checkMultiRobotStatusTimeout(long curTime, List<MultiRobotStatus> robotStatusList) {
        //遍历找到超时的状态数据，并删除
        Iterator<MultiRobotStatus> iterator = robotStatusList.iterator();
        while (iterator.hasNext()) {
            MultiRobotStatus robotStatus = (MultiRobotStatus) iterator.next();
            //数据延迟超过5s，认为该条数据不可用状态
            if (Math.abs(curTime - robotStatus.getTime()) > 5000) {
                Log.e(TAG, "!! checkMultiRobotStatusTimeout remove timeout status : " + robotStatus);
                iterator.remove();
            }
        }
        mRobotStatusList = robotStatusList;
    }

    /**
     * 处理当前机器人的error状态
     *
     * @param robotStatus 多机列表
     * @return true无报错信息任务继续  false会结束当前任务。
     */
    private boolean checkMultiRobotErrorStatus(MultiRobotStatus robotStatus) {
        switch (robotStatus.getErrorStatus()) {
            case Definition.MULTIPLE_STATUS_ERROR_MAP_NOT_MATCH:
//                onNavigationError(Definition.STATUS_NAVI_MULTI_MAP_NOT_MATCH,
//                        "Multiple map not match!", "");
                //这里不处理地图不匹配的情况，只处理由navi层直接上报的地图不匹配状态
                return true; //这里的地图不匹配不影响多机策略，底盘上报的地图不匹配会直接结束任务
            case Definition.MULTIPLE_STATUS_ERROR_LORA_DISCONNECT:
                finishAction(Definition.STATUS_NAVI_MULTI_LORA_DISCONNECT, "Lora disconnect!");
                Log.e(TAG, "checkMultiRobotErrorStatus: Lora disconnect!");
                return false;
            case Definition.MULTIPLE_STATUS_ERROR_VERSION_NOT_MATCH:
                finishAction(Definition.STATUS_NAVI_MULTI_VERSION_NOT_MATCH, "Multiple version not match");
                Log.e(TAG, "checkMultiRobotErrorStatus: Multiple version not match");
                return false;
            case Definition.MULTIPLE_STATUS_NORMAL:
            default:
                return true;
        }
    }

    /**
     * 判断某个点位当前是不是可用
     */
    protected boolean isPoseInUseCurrently(Pose pose, List<MultiRobotStatus> robotStatusList) {
        if (pose == null) {
            return false;
        }
        for (int i = 0; i < robotStatusList.size(); i++) {
            MultiRobotStatus robotStatus = robotStatusList.get(i);
            if (robotStatus.isCurRobot()) {
                Log.d(TAG, "isPoseInUseCurrently current robot");
                continue;
            }
            //status 为1表示无定位 2表示未在导航 3表示在导航中
            BasePoseBean poseBean = null;
            switch (robotStatus.getStatus()) {
                case 2:
                    //不在导航中，如果机器人当前点位在目标点，则目标点也属于不可达
                    poseBean = robotStatus.getPose();
                    break;
                case 3:
                    //处于导航模式，如果目标点被占用，属于不可达
                    poseBean = robotStatus.getGoal();
                    break;
                default:
                    break;
            }
            if (poseBean == null) {
                continue;
            }
            boolean inUse = calculateTargetPoseInUse(pose, poseBean);
            if (inUse) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否到达某点位范围内
     *
     * @param pose     机器人想要去的目标点
     * @param goalPose 另外一台机器当前导航的目标点
     * @return true 目标点不能到达 false  目标点可以去
     */
    protected boolean calculateTargetPoseInUse(Pose pose, BasePoseBean goalPose) {
        if (pose == null && goalPose == null) {
            return false;
        }
        double distance = Math.sqrt(Math.pow((pose.getX() - goalPose.getX()), 2)
                + Math.pow((pose.getY() - goalPose.getY()), 2));
        boolean inUse = isInPoseRange(distance);
        Log.d(TAG, "Target Pose " + pose.getName() + "distance: " + distance
                + ", inRange: " + inUse
                + ", pose:" + pose.toString()
                + ", goal:" + goalPose.toString());
        return inUse;
    }

    /**
     * 判断距离是否小于参数设置的，多机点位膨胀范围内
     *
     * @return true：在范围内
     */
    protected boolean isInPoseRange(double distance) {
        boolean inRange = distance < 0.5;
        Log.d(TAG, "isInPoseRange: " + inRange);
        return inRange;
    }

    public boolean isResultSuccess(String message) {
        if (!isJSON(message)) {
            return false;
        }
        return !TextUtils.isEmpty(message)
                && !Definition.PARAMS_TIMEOUT.equals(message)
                && !Definition.FAILED.equals(message);
    }

    private boolean isJSON(String message) {
        String jsonRegex = "^\\{.*\\}$|^\\[.*\\]$";
        return message.matches(jsonRegex);
    }

    protected String buildFailJson(int failStatus) {
        JSONObject json = new JSONObject();
        try {
            json.put(Definition.CHARGE_FAIL_STATUS, failStatus);
            String reason = getFailReasonMsg(failStatus);
            json.put(Definition.CHARGE_FAIL_REASON, reason);
            return json.toString();
        } catch (JSONException e) {
            e.printStackTrace();
            return "Unknown fail status";
        }
    }

    private String getFailReasonMsg(int failStatus) {
        switch (failStatus) {
            case Definition.CHARGE_FAIL_WHEN_NOT_ESTIMATE:
                return "Need estimate first before navigation !!!";

            case Definition.CHARGE_FAIL_WHEN_NAVIGATION:
                return "Navigate to statChargePoint error !!!";

            case Definition.CHARGE_FAIL_WHEN_PARSE_IN_LOCATION:
                return "Parse IsInLocation result error !!!";

            case Definition.CHARGE_FAIL_WHEN_NOT_MOVE_FOR_20S:
                return "Not MOVE any Distance for 20s !!!";

            case Definition.CHARGE_FAIL_WHEN_PSB_CHARGE:
                return "PSB autoCharge failure !!!";

            case Definition.CHARGE_FAIL_WHEN_LARGE_MAP_NAVI_TIMEOUT:
                return "Map is too large to navigate timeout !!!";

            case Definition.CHARGE_FAIL_WHEN_VISION_CHARGE_START:
                return "Vision charge start failed !!!";

            case Definition.CHARGE_FAIL_WHEN_VISION_CHARGE_STOP:
                return "Vision charge stop failed !!!";

            case Definition.CHARGE_FAIL_VISION_CHARGE_TIMEOUT:
                return "Vision charge timeout !!!";

            case Definition.STATUS_NAVI_MULTI_MAP_NOT_MATCH:
                return "navigation multi robot map not match!";

            case Definition.STATUS_NAVI_MULTI_LORA_DISCONNECT:
                return "navigation multi robot lora disconnect!";

            case Definition.STATUS_NAVI_MULTI_LORA_CONFIG_FAIL:
                return "navigation multi robot lora config fail!";

            case Definition.STATUS_NAVI_MULTI_VERSION_NOT_MATCH:
                return "navigation multi robot version not match!";

            default:
                return "Unkown fail reason ";
        }
    }

    protected void handleNavigationStatus(String status) {
        Log.d(TAG, "handleNavigationStatus status : " + status);
        switch (status) {
            case Definition.NAVIGATION_OCCLUDED:
                onStateUpdate(Definition.STATUS_GOAL_OCCLUDED, "The target is occluded");
                break;

            case Definition.NAVIGATION_OCCLUDED_END:
                onStateUpdate(Definition.STATUS_GOAL_OCCLUDED_END, "The occluded end");
                break;

            case Definition.NAVIGATION_AVOID:
                onStateUpdate(Definition.STATUS_NAVI_AVOID, "navigation avoid");
                break;

            case Definition.NAVIGATION_AVOID_END:
                onStateUpdate(Definition.STATUS_NAVI_AVOID_END, "navigation avoid end");
                break;

            case Definition.NAVIGATION_OUT_MAP:
                onStateUpdate(Definition.STATUS_NAVI_OUT_MAP, "The goal is out of map or more dangerous");
                break;

            case Definition.NAVIGATION_GLOBAL_PATH_FAILED:
                finishAction(Definition.STATUS_NAVI_GLOBAL_PATH_FAILED, "The global is path search failed");
                break;

            case Definition.NAVIGATION_OBSTACLES_AVOID:
                onStateUpdate(Definition.STATUS_NAVI_OBSTACLES_AVOID, "obstacles avoid");
                break;

            case Definition.NAVIGATION_STARTED:
                onStateUpdate(Definition.STATUS_START_NAVIGATION, "navigation started");
                break;
            case Definition.NAVIGATION_MULTI_ROBOT_WAITING:
                this.multiRobotWaiting();
                onStateUpdate(Definition.STATUS_NAVI_MULTI_ROBOT_WAITING, "Multiple robots are waiting to avoid obstacles");
                break;

            case Definition.NAVIGATION_MULTI_ROBOT_WAITING_END:
                this.multiRobotWaitingEnd();
                onStateUpdate(Definition.STATUS_NAVI_MULTI_ROBOT_WAITING_END, "Multiple robots passageways returned to normal");
                break;
            case Definition.NAVIGATION_MULTI_MAP_NOT_MATCH:
                finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.STATUS_NAVI_MULTI_MAP_NOT_MATCH));
                break;

            case Definition.NAVIGATION_MULTI_LORA_DISCONNECT:
                finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.STATUS_NAVI_MULTI_LORA_DISCONNECT));
                break;

            case Definition.NAVIGATION_MULTI_LORA_CONFIG_FAIL:
                finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.STATUS_NAVI_MULTI_LORA_CONFIG_FAIL));
                break;

            case Definition.NAVIGATION_MULTI_VERSION_NOT_MATCH:
                finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.STATUS_NAVI_MULTI_VERSION_NOT_MATCH));
                break;
            default:
                break;
        }
    }

    protected void multiRobotWaiting() {
    }

    protected void multiRobotWaitingEnd() {
    }
    protected void handleMultipleRobotStatus(List<MultiRobotStatus> robotStatusList) {
    }

    protected abstract void onStart(ChargeAdvancedAction chargeAdvancedAction, StateData stateData);

    protected abstract boolean onCmdResponse(int cmdId, String command, String result, String extraData);
    protected abstract boolean onCmdStatusUpdate(int cmdId, String command, String status, String extraData);
    protected abstract AdvanceChargeStateEnum getNextState();

}
