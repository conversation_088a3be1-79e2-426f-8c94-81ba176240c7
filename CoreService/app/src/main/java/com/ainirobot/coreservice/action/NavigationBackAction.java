/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.action;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.StopCommandList.IStopCommandChecker;
import com.ainirobot.coreservice.action.StopCommandList.StopCommand;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.actionbean.NavigationBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.robotlog.RobotLog;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;

public class NavigationBackAction extends AbstractAction<NavigationBean> {
    private static final String TAG = "NavigationBackAction";

    private enum NavigationState {
        IDLE, PREPARE, NAVIGATION_FIRST, NAVIGATION_SECOND, STOPPED
    }

    private int mReqId;
    private double mLinearSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED;
    private double mAngularSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED;
    private Pose mDestPose;
    private Gson mGson;
    private boolean mIsFlag = false;
    private String mPoseListener;
    private NavigationState mStatus;

    protected NavigationBackAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_NAVIGATION_BACK, resList);
        mGson = new Gson();
    }

    @Override
    protected boolean startAction(NavigationBean parameter, LocalApi api) {
        if (verifyParam(parameter)) {
            prepareNavigation(parameter);
            return true;
        }
        return false;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        RobotLog.d("Stop navigation action");
        mStatus = NavigationState.STOPPED;
        unregisterStatusListener(mPoseListener);
        mIsFlag = false;
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        Log.d(TAG, " cmdResponse: command:" + command + " result:" + result + " status:" + mStatus);
        switch (command) {
            case Definition.CMD_NAVI_IS_ESTIMATE:
                processPoseEstimate(result);
                return true;

            case Definition.CMD_NAVI_GO_POSITION:
                processNavigationResult(result, extraData);
                return true;
            default:
                return false;
        }
    }

    private void processNavigationResult(String result, String extraData) {
        Log.d(TAG, "processNavigationResult : " + result);

        if (Definition.NAVIGATION_OK.equals(result)) {
            if (mStatus != NavigationState.NAVIGATION_SECOND){
                onResult(Definition.RESULT_OK, result);
            }else {
                onResult(Definition.RESULT_STOP, result);
            }
            return;
        }
        if (Definition.NAVIGATION_CANCELED.equals(result)) {
            onResult(Definition.RESULT_STOP, result);
            return;
        }
        processError(Definition.ERROR_NAVIGATION_FAILED, "Navigation failed : " + result, extraData);
    }

    private void processError(int errorCode, String errorMessage, String extraData) {
        Log.d(TAG, "processError : " + errorCode + " " + errorMessage + " " + extraData);
        super.onError(errorCode, errorMessage, extraData);
    }

    @Override
    public boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_GO_POSITION:
                processNavigationStatus(status, extraData);
                return true;
            default:
                break;
        }
        return false;
    }

    private void prepareNavigation(NavigationBean params) {
        Log.d(TAG, "Start navigation action");
        mStatus = NavigationState.PREPARE;
        mIsFlag = false;
        mReqId = params.getReqId();
        mLinearSpeed = params.getLinearSpeed();
        mAngularSpeed = params.getAngularSpeed();
        checkPoseEstimate();
        registerPoseListener();
    }

    private void registerPoseListener() {
        mPoseListener = registerStatusListener(Definition.STATUS_POSE, new StatusListener() {
            @Override
            public void onStatusUpdate(String data) {
                onPoseUpdate(data);
            }
        });
    }

    private synchronized void onPoseUpdate(String data) {
        if (TextUtils.isEmpty(data) || mStatus != NavigationState.NAVIGATION_FIRST || mIsFlag == true) {
            return;
        }
        Pose pose = mGson.fromJson(data, Pose.class);

        double theta = mDestPose.getTheta() - 4 * Math.PI/5;
        if (theta < -Math.PI){
            theta = theta + 2 * Math.PI;
        }else if (theta > Math.PI){
            theta = theta - 2 * Math.PI;
        }

        double angleDiff = theta - pose.getTheta();
        if (angleDiff < -Math.PI){
            angleDiff = angleDiff + 2 * Math.PI;
        }else if (angleDiff > Math.PI){
            angleDiff = angleDiff - 2 * Math.PI;
        }else {

        }

        if (angleDiff < 0) {
            Log.d(TAG, " go second set goal:" + data + " theta:" + theta);
            mStatus = NavigationState.NAVIGATION_SECOND;
            mIsFlag = true;
            mApi.goPosition(mReqId, mDestPose.getX(), mDestPose.getY(), mDestPose.getTheta(), mLinearSpeed, mAngularSpeed);
        }
    }

    private void checkPoseEstimate() {
        Log.d(TAG, "Check pose estimate");
        mApi.isRobotEstimate(mReqId);
    }

    private void processNavigationStatus(String status, String extraData) {
        switch (status) {
            case Definition.NAVIGATION_OCCLUDED:
                onStatusUpdate(Definition.STATUS_GOAL_OCCLUDED, "The target is occluded", extraData);
                break;

            case Definition.NAVIGATION_OCCLUDED_END:
                onStatusUpdate(Definition.STATUS_GOAL_OCCLUDED_END, "The occluded end", extraData);
                break;

            case Definition.NAVIGATION_AVOID:
                onStatusUpdate(Definition.STATUS_NAVI_AVOID, "The local path search failed", extraData);
                break;

            case Definition.NAVIGATION_AVOID_END:
                onStatusUpdate(Definition.STATUS_NAVI_AVOID_END, "The local path search succeed", extraData);
                break;

            case Definition.NAVIGATION_OUT_MAP:
                onStatusUpdate(Definition.STATUS_NAVI_OUT_MAP, "The goal is out of map or more dangerous", extraData);
                break;

            case Definition.NAVIGATION_GLOBAL_PATH_FAILED:
                onStatusUpdate(Definition.STATUS_NAVI_GLOBAL_PATH_FAILED, "The global is path search failed", extraData);
                break;

            case Definition.NAVIGATION_OBSTACLES_AVOID:
                onStatusUpdate(Definition.STATUS_NAVI_OBSTACLES_AVOID, "obstacles avoid", extraData);
                break;

            case Definition.NAVIGATION_STARTED:
                onStatusUpdate(Definition.STATUS_START_NAVIGATION, "Navigation has been started", extraData);
                break;

            default:
                Log.i(TAG, "Command status: " + status +  " doesn't be handled");
                break;
        }
    }

    private void processPoseEstimate(String result) {
        if (!"true".equals(result)) {
            onError(Definition.ERROR_NOT_ESTIMATE, "Not pose estimate");
            return;
        }
        processGoTempPosition();
    }

    private void processGoTempPosition(){
        Log.d(TAG, " go firstPosition");
        mStatus = NavigationState.NAVIGATION_FIRST;
        mApi.goPosition(mReqId, mDestPose.getX(), mDestPose.getY(), mDestPose.getTheta() - Math.PI/2, mLinearSpeed, mAngularSpeed);
    }

    private Pose getTargetPose(String jsonString) {
        Log.d(TAG, "getTargetPose:" + jsonString);
        mDestPose = null;
        try {
            JSONObject json = new JSONObject(jsonString);

            float x = json.has("x") ? Float.valueOf(json.get("x").toString()) : Float.valueOf(json.get("px").toString());
            float y = json.has("y") ? Float.valueOf(json.get("y").toString()) : Float.valueOf(json.get("py").toString());
            float theta = Float.valueOf(json.get("theta").toString());
            mDestPose = new Pose();
            mDestPose.setX(x);
            mDestPose.setY(y);
            mDestPose.setTheta(theta);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return mDestPose;
    }

    @Override
    public boolean verifyParam(NavigationBean params) {
        if (params == null || TextUtils.isEmpty(params.getDestination())) {
            return false;
        }
        Pose pose = getTargetPose(params.getDestination());
        if (pose == null){
            return false;
        }
        return true;
    }

    @Override
    protected ArrayList<StopCommand> getStopCommands() {
        return new ArrayList<StopCommand>() {{
            add(new StopCommand(Definition.CMD_NAVI_STOP_NAVIGATION, null));
        }};
    }

    @Override
    protected IStopCommandChecker getStopCommandChecker() {
        return new IStopCommandChecker() {
            @Override
            public boolean executeSuccess(String command, String result) {
                RobotLog.d("Leading stop check : " + command);
                switch (command) {
                    case Definition.CMD_NAVI_STOP_NAVIGATION:
                        return true;
                }
                return false;
            }
        };
    }
}
