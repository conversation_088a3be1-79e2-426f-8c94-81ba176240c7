/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.test;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.RemoteException;
import android.util.Log;
import android.view.View;

import com.ainirobot.coreservice.R;
import com.ainirobot.coreservice.client.ApiListener;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.IntentUtil;
import com.ainirobot.coreservice.client.RobotApi;
import com.ainirobot.coreservice.client.actionbean.HeadTurnBean;
import com.ainirobot.coreservice.client.actionbean.LeadingParams;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.listener.Person;
import com.ainirobot.coreservice.client.listener.PersonInfoListener;
import com.ainirobot.coreservice.core.InternalDef;
import com.ainirobot.coreservice.core.system.OTAManager.OTAStartMode;
import com.ainirobot.coreservice.core.system.SystemManager;
import com.ainirobot.coreservice.service.CoreService;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;

public class TestActivity extends Activity {

    private RobotApi mApi;
    private static final String TAG = "TestActivity";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        Log.d(TAG, "TestActivity onCreate");
        startService();
        test(null);
    }

    public void startService() {
        Log.d(TAG, "TestActivity start service");

        Intent intent = new Intent();
        intent.setClass(TestActivity.this, CoreService.class);
        startService(intent);

        BufferedReader stdin = null;
        BufferedReader stderr = null;
        StringBuilder retBuilder = new StringBuilder("/n");

        java.lang.Process process = null;
        try {
            process = Runtime.getRuntime().exec("bmgr backup com.google.android.inputmethod.latin");
            stdin = new BufferedReader(new InputStreamReader(process.getInputStream()), 7777);
            stderr = new BufferedReader(new InputStreamReader(process.getErrorStream()), 7777);
            String line = null;
            while ((line = stdin.readLine()) != null || (line = stderr.readLine()) != null) {
                retBuilder.append(line).append("\n");
            }
            Log.d(TAG, retBuilder.toString());

        } catch (IOException e) {
            e.printStackTrace();
            Log.d(TAG, e.getMessage());
        } finally {
            try {
                if (process != null)
                    process.exitValue();
            } catch (IllegalThreadStateException e) {
                //e.printStackTrace();
                process.destroy();
            }
        }
    }

    private void startService(String packageName, String serviceName, String action) {
        Intent intent = IntentUtil.createExplicitIntent(packageName, serviceName, action);
        if (isAppExists(intent)) {
            startService(intent);
        }
    }

    private boolean isAppExists(Intent intent) {
        return !getPackageManager().queryIntentServices(intent, 0).isEmpty();
    }

    public void test(View view) {
        CoreService service = CoreService.mCore;
        mApi = RobotApi.getInstance();
        mApi.connectServer(this, new ApiListener() {
            @Override
            public void handleApiDisabled() {

            }

            @Override
            public void handleApiConnected() {
                Log.d(TAG, "RobotOS api connected");
            }

            @Override
            public void handleApiDisconnected() {
                Log.d(TAG, "RobotOS api disconnected");
            }
        });
    }

    public void deleteTx1(View view) {
        mApi.startUnRegister(Definition.MODULE_REQ_ID, "all", new ActionListener() {
            @Override
            public void onResult(int status, String responseString) throws RemoteException {
                Log.d(TAG, "RobotOS unregister  : " + status + "  " + responseString);
            }
        });
    }

    private Person mPerson;
    private PersonInfoListener listener;

    public void focusFollow(View view) {
        mPerson = null;
        //mApi.stopFocusFollow(0);
        mApi.stopGetAllPersonInfo(0, listener);
        listener = new PersonInfoListener() {
            @Override
            public void onData(int code, List<Person> data) {
                //super.onData(code, data);
                if (data.isEmpty() || mPerson != null) {
                    return;
                }
                mApi.stopGetAllPersonInfo(0, listener);
                mPerson = data.get(0);
                mApi.startFocusFollow(Definition.MODULE_REQ_ID, mPerson.getId(), 2000, 3.0f, new ActionListener() {

                    @Override
                    public void onResult(int status, String responseString) throws RemoteException {
                        Log.d(TAG, "RobotOS focus onResult : " + status + "  " + responseString);
                        switch (status) {
                            case Definition.STATUS_GUEST_LOST:
                                mApi.stopFocusFollow(0);
                                break;
                        }
                    }

                    @Override
                    public void onError(int errorCode, String errorString) throws RemoteException {
                        Log.d(TAG, "RobotOS focus onError : " + errorCode + "  " + errorString);
                    }

                    @Override
                    public void onStatusUpdate(int status, String data) throws RemoteException {
                        Log.d(TAG, "RobotOS focus onStatusUpdate : " + status + "  " + data);
                    }
                });
            }
        };
        mApi.startGetAllPersonInfo(Definition.MODULE_REQ_ID, listener);
    }

    public void stopFocusFollow(View view) {
        mApi.stopTrack(0, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(TAG, "RobotOS stopTrack : " + result + "  " + message);
            }
        });
        mApi.stopFocusFollow(Definition.MODULE_REQ_ID);
    }

    public void stopLead(View view) {
        mApi.stopTrack(0, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(TAG, "RobotOS stopTrack : " + result + "  " + message);
            }
        });
        mApi.stopLead(0, true);
    }

    @Override
    protected void onDestroy() {
        //mApi.disconnectServer(this);
        super.onDestroy();
    }

    public void lead(View view) {
        mPerson = null;
        mApi.stopLead(0, true);
        mApi.stopGetAllPersonInfo(0, listener);
        listener = new PersonInfoListener() {

            @Override
            public void onData(int code, List<Person> data) {
                if (data.isEmpty() || mPerson != null) {
                    return;
                }
                mApi.stopGetAllPersonInfo(0, listener);
                mPerson = data.get(0);
                LeadingParams params = new LeadingParams();
                params.setPersonId(mPerson.getId())
                        .setAvoidTimeout(20 * 1000)
                        .setDestinationName("会议室")
                        .setDetectDelay(5 * 1000)
                        .setLostTimer(2 * 1000)
                        .setWaitTimeout(20 * 1000);
                mApi.startLead(0, params, new ActionListener() {
                    @Override
                    public void onResult(int status, String responseString) throws RemoteException {
                        Log.i(TAG, "lead onresult, status:" + status + " result:" + responseString);
                        super.onResult(status, responseString);
                    }

                    @Override
                    public void onError(int errorCode, String errorString) throws RemoteException {
                        Log.i(TAG, "lead onerror, errorCode:" + errorCode + " result:" + errorString);
                        super.onError(errorCode, errorString);
                    }

                    @Override
                    public void onStatusUpdate(int status, String data) throws RemoteException {
                        Log.i(TAG, "lead onStatusUpdate, status:" + status + " result:" + data);
                        super.onStatusUpdate(status, data);
                    }
                });
            }
        };
        mApi.startGetAllPersonInfo(Definition.MODULE_REQ_ID, listener);


//        mApi.startLead(0,1, "李金鑫", "会议室", 2000l, new ActionListener() {
//            @Override
//            public void onResult(int status, String responseString) throws RemoteException {
//                Log.i(TAG, "lead onresult, status:" + status + " result:" + responseString);
//                super.onResult(status, responseString);
//            }
//
//            @Override
//            public void onError(int errorCode, String errorString) throws RemoteException {
//                Log.i(TAG, "lead onerror, errorCode:" + errorCode + " result:" + errorString);
//                super.onError(errorCode, errorString);
//            }
//
//            @Override
//            public void onStatusUpdate(int status, String data) throws RemoteException {
//                Log.i(TAG, "lead onStatusUpdate, status:" + status + " result:" + data);
//                super.onStatusUpdate(status, data);
//            }
//        });
    }

    public void register(View view) {
        mApi.startRegister(0, "李金鑫", 10000, 0, 0L, "", new ActionListener() {
            @Override
            public void onResult(int status, String responseString) throws RemoteException {
                Log.i(TAG, "register onresult, status:" + status + " result:" + responseString);
                super.onResult(status, responseString);
            }

            @Override
            public void onError(int errorCode, String errorString) throws RemoteException {
                Log.i(TAG, "register onresult, errorCode:" + errorCode + " result:" + errorString);
                super.onError(errorCode, errorString);
            }

            @Override
            public void onStatusUpdate(int status, String data) throws RemoteException {
                Log.i(TAG, "register onresult, status:" + status + " result:" + data);
                super.onStatusUpdate(status, data);
            }
        });
    }

    public void forward(View view) {
        mApi.goForward(Definition.MODULE_REQ_ID, 0.4f, 1, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(TAG, "RobotOS forward : " + result + "  " + message);
            }
        });
    }

    public void turnRight(View view) {
        mApi.turnRight(Definition.MODULE_REQ_ID, 30, 60, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(TAG, "RobotOS turnRight : " + result + "  " + message);
            }
        });
    }

    public void turnLeft(View view) {
        mApi.turnLeft(Definition.MODULE_REQ_ID, 30, 60, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(TAG, "RobotOS turnLeft : " + result + "  " + message);
            }
        });
    }

    public void turnHeadLeft90(View view) {
        HeadTurnBean bean = new HeadTurnBean();
        bean.setHorizontalMode(HeadTurnBean.HeadTurnMode.absolute);
        bean.setHorizontalAngle(-90);
        mApi.turnHead(0, bean, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(TAG, "turnHeadLeft90 onResult result: " + result + ", message: " + message);
            }

            @Override
            public void onStatusUpdate(int status, String data) {
                Log.d(TAG, "turnHeadLeft90 onStatusUpdate status: " + status + ", data: " + data);
            }
        });
    }

    public void turnHeadRight90(View view) {
        HeadTurnBean bean = new HeadTurnBean();
        bean.setHorizontalMode(HeadTurnBean.HeadTurnMode.absolute);
        bean.setHorizontalAngle(90);
        mApi.turnHead(0, bean, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(TAG, "turnHeadRight90 onResult result: " + result + ", message: " + message);
            }

            @Override
            public void onStatusUpdate(int status, String data) {
                Log.d(TAG, "turnHeadRight90 onStatusUpdate status: " + status + ", data: " + data);
            }
        });
    }

    public void stopTurnHead(View view) {
        mApi.stopTurnHead(0, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(TAG, "stopTurnHead onResult result: " + result + ", message: " + message);
            }

            @Override
            public void onStatusUpdate(int status, String data) {
                Log.d(TAG, "stopTurnHead onStatusUpdate status: " + status + ", data: " + data);
            }
        });
    }

    public void turnHeadUp45(View view) {
        HeadTurnBean bean = new HeadTurnBean();
        bean.setVerticalMode(HeadTurnBean.HeadTurnMode.absolute);
        bean.setVerticalAngle(45);
        mApi.turnHead(0, bean, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(TAG, "turnHeadUp45 onResult result: " + result + ", message: " + message);
            }

            @Override
            public void onStatusUpdate(int status, String data) {
                Log.d(TAG, "turnHeadUp45 onStatusUpdate status: " + status + ", data: " + data);
            }
        });
    }

    public void turnHeadDown90(View view) {
        HeadTurnBean bean = new HeadTurnBean();
        bean.setVerticalMode(HeadTurnBean.HeadTurnMode.absolute);
        bean.setVerticalAngle(90);
        mApi.turnHead(0, bean, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(TAG, "turnHeadDown0 onResult result: " + result + ", message: " + message);
            }

            @Override
            public void onStatusUpdate(int status, String data) {
                Log.d(TAG, "turnHeadDown0 onStatusUpdate status: " + status + ", data: " + data);
            }
        });
    }

    public void startFollow(View view) {
//        mApi.startFollow(0, "L-1-祝捷", 0, 2000, new ActionListener() {
//            @Override
//            public void onResult(int status, String responseString) throws RemoteException {
//                Log.d(TAG, "RobotOS follow : " + status + "  " + responseString);
//            }
//
//            @Override
//            public void onError(int errorCode, String errorString) throws RemoteException {
//                Log.d(TAG, "RobotOS follow : " + errorCode + "  " + errorString);
//            }
//
//            @Override
//            public void onStatusUpdate(int status, String data) throws RemoteException {
//                Log.d(TAG, "RobotOS follow : " + status + "  " + data);
//            }
//        });
    }

    public void stopFollow(View view) {
        //mApi.stopFollow(0);
        mApi.getHeadStatus(0, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                //super.onResult(result, message);
                Log.d(TAG, "RobotOS getHeadStatus : " + result + "  " + message);
            }
        });
    }

    public void stopInspection(View view) {
        Log.d(TAG, "ycLog : stopInspection");
        mApi.stopInspection(Definition.DEBUG_REQ_ID, true);
    }

    public void startInspection(View view) {
        Log.d(TAG, "ycLog : startInspection");
        mApi.startInspection(Definition.DEBUG_REQ_ID, 60 * 1000, true, new ActionListener() {
            @Override
            public void onResult(int status, String responseString) throws RemoteException {
                Log.d(TAG, "ycLog startInspection onResult : " + status + "||" + responseString);
            }

            @Override
            public void onError(int errorCode, String errorString) throws RemoteException {
                Log.d(TAG, "ycLog startInspection onError : " + errorCode + "||" + errorString);
            }
        });
    }

    public void testNavigation(final View view) {
        mApi.startNavigation(0, "会议室", 0.5, 20000, new ActionListener() {
            @Override
            public void onResult(int status, String responseString) throws RemoteException {
                Log.w(TAG, "Navigation 会议室 result : " + status);
                mApi.startNavigation(0, "前台", 0.5, 20000, new ActionListener() {
                    @Override
                    public void onResult(int status, String responseString) throws RemoteException {
                        Log.w(TAG, "Navigation 前台 result : " + status);
                        mApi.startNavigation(0, "研发区", 0.5, 20000, new ActionListener() {
                            @Override
                            public void onResult(int status, String responseString) throws RemoteException {
                                Log.w(TAG, "Navigation 研发区 result : " + status);
                                testNavigation(view);
                            }
                        });
                    }
                });
            }
        });
    }

    public void batteryCharging(View view) {
//        BatteryManager.testUpdateStatus(BatteryStatus.START_CHARGING, new Random(50).nextInt());
//        BatteryManager.testUpdateCharging(20);
    }

    public void batteryLow(View view) {
//        BatteryManager.testUpdateStatus(BatteryStatus.LOW, 20);
    }

    public void batteryFull(View view) {
//        BatteryManager.testUpdateStatus(BatteryStatus.FULL, 20);
    }

    public void batteryNormal(View view) {
//        BatteryManager.testUpdateStatus(BatteryStatus.NORMAL, 20);
    }

    public void startOta(View view) {
        SystemManager systemManager = CoreService.mCore.getSystemManager();
        systemManager.mOta.startService(OTAStartMode.NIGHTTIME, InternalDef.START_COMMAND_CHECK_NEW_VERSION);
    }

    public void disableLow(View view) {
        SystemManager systemManager = CoreService.mCore.getSystemManager();
        systemManager.setSystemStatusEnabled(Definition.SYSTEM_BATTERY_LOW, false);
    }

    public void disableEmergency(View view) {
        SystemManager systemManager = CoreService.mCore.getSystemManager();
        systemManager.setSystemStatusEnabled(Definition.SYSTEM_EMERGENCY, false);
    }

    public void disableAUtoOta(View view) {
        SystemManager systemManager = CoreService.mCore.getSystemManager();
        systemManager.setSystemStatusEnabled(Definition.SYSTEM_AUTO_OTA, false);
    }

    public void startSettingOta(View view) {
        SystemManager systemManager = CoreService.mCore.getSystemManager();
        systemManager.mOta.startService(OTAStartMode.SETTING, InternalDef.START_COMMAND_CHECK_NEW_VERSION);
    }

    public void enableEmergency(View view) {
        SystemManager systemManager = CoreService.mCore.getSystemManager();
        systemManager.setSystemStatusEnabled(Definition.SYSTEM_EMERGENCY, true);
    }

    public void enableLow(View view) {
        SystemManager systemManager = CoreService.mCore.getSystemManager();
        systemManager.setSystemStatusEnabled(Definition.SYSTEM_BATTERY_LOW, true);
    }

    public void enableAUtoOta(View view) {
        SystemManager systemManager = CoreService.mCore.getSystemManager();
        systemManager.setSystemStatusEnabled(Definition.SYSTEM_AUTO_OTA, true);
    }

    public void inChargingPile(View view) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.KEY_COORDINATE_DEVIATION, 0.3 + "");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        CoreService.mCore.getActionManager().exCmd(Definition.ACTION_JUDGE_IN_CHARGING_PILE, jsonObject.toString(), null);
    }


//    public void registerModule(View v) {
//        Log.d(TAG,"TestActivity Register Module");
//        Button button = (Button) findViewById(R.id.registerModule);
//        button.setVisibility(View.INVISIBLE);
//        button.setEnabled(false);
//
//        ModuleRegistryApi apiModule = new ModuleRegistryApi(this);
//        apiModule.connectApi();
//    }
}
