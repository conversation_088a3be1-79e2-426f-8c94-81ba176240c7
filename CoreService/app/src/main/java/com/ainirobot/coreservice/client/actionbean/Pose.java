/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.client.actionbean;

import com.ainirobot.coreservice.client.Definition;

import org.json.JSONException;
import org.json.JSONObject;

public class Pose {

    private float px, py, theta;
    private final long time;
    private String name;
    /**
     * FREE = 0;      // 正常区域，可以到
     * NOT_SAFE = 1;  // 危险区，不可以到
     * OBSTACLE = 2;  // 禁行区，不可以到
     * OUTSIDE = 3;   // 地图外，不可以到
     */
    private int status;
    private float distance;
    private boolean ignoreDistance = false;

    private boolean noDirectionalParking = false;

    private int safeDistance = Definition.POSE_SAFE_DISTANCE_DEFAULT;
    private int typeId; // 如果是特殊点位需要设置
    private int priority; // 特殊点位优先级
    private int poseId; // 特殊点修改优先级用来作为唯一标识

    public Pose() {
        this.time = System.currentTimeMillis();
    }

    public Pose(float x ,float y, float theta){
        this.px = x;
        this.py = y;
        this.theta = theta;
        this.time = System.currentTimeMillis();
    }

    public Pose(float x ,float y, float theta, String name){
        this.px = x;
        this.py = y;
        this.theta = theta;
        this.name = name;
        this.time = System.currentTimeMillis();
    }

    public Pose(float x ,float y, float theta, String name, boolean ignoreDistance, int safeDistance){
        this.px = x;
        this.py = y;
        this.theta = theta;
        this.name = name;
        this.ignoreDistance = ignoreDistance;
        this.safeDistance = safeDistance;
        this.time = System.currentTimeMillis();
    }

    public Pose(float x ,float y, float theta, String name, int typeId, int priority, int poseId){
        this.px = x;
        this.py = y;
        this.theta = theta;
        this.name = name;
        this.typeId = typeId;
        this.priority = priority;
        this.poseId = poseId;
        this.time = System.currentTimeMillis();
    }

    public Pose(float x ,float y, float theta, String name, int typeId, int priority, boolean ignoreDistance, int safeDistance){
        this.px = x;
        this.py = y;
        this.theta = theta;
        this.name = name;
        this.typeId = typeId;
        this.priority = priority;
        this.ignoreDistance = ignoreDistance;
        this.safeDistance = safeDistance;
        this.time = System.currentTimeMillis();
    }

    public float getX() {
        return px;
    }

    public float getY() {
        return py;
    }

    public float getTheta() {
        return theta;
    }

    public long getTime() {
        return time;
    }

    public void setX(float x) {
        this.px = x;
    }

    public void setY(float y) {
        this.py = y;
    }

    public void setTheta(float theta) {
        this.theta = theta;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public float getDistance() {
        return distance;
    }

    public void setDistance(float distance) {
        this.distance = distance;
    }

    public boolean getIgnoreDistance() {
        return ignoreDistance;
    }

    public void setIgnoreDistance(boolean ignoreDistance) {
        this.ignoreDistance = ignoreDistance;
    }

    public boolean getNoDirectionalParking() {
        return noDirectionalParking;
    }

    public void setNoDirectionalParking(boolean noDirectionalParking) {
        this.noDirectionalParking = noDirectionalParking;
    }

    public int getSafeDistance() {
        return safeDistance;
    }

    public void setSafeDistance(int safeDistance) {
        this.safeDistance = safeDistance;
    }

    public void setTypeId(int typeId) {
        this.typeId = typeId;
    }

    public int getTypeId() {
        return typeId;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public int getPriority() {
        return priority;
    }

    public void setPoseId(int poseId) {
        this.poseId = poseId;
    }

    public int getPoseId() {
        return poseId;
    }

    @Override
    public String toString() {
        return "x=" + px + "  y=" + py + " theta=" + theta + " name=" + name + " typeId=" + typeId + " priority=" + priority + " ignoreDistance=" + ignoreDistance + " safeDistance=" + safeDistance;
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof Pose)) {
            return false;
        }

        Pose pose = (Pose) obj;
        return this.px == pose.getX() && this.py == pose.getY() && this.theta == pose.getTheta();
    }

    @Override
    public int hashCode() {
        int code = 0;
        code += 31 * ((Float) this.px).hashCode() * 10;
        code += 31 * ((Float) this.py).hashCode() * 100;
        code += 31 * ((Float) this.theta).hashCode() * 1000;
        return code;
    }

    public double getDistance(Pose pose) {
        if (pose == null) {
            return Double.MAX_VALUE;
        }
        double destX = this.getX();
        double destY = this.getY();
        double x = pose.getX();
        double y = pose.getY();
        return Math.sqrt(Math.pow((x - destX), 2) + Math.pow((y - destY), 2));
    }

    public String toJson() {
        return toJsonObject().toString();
    }

    public JSONObject toJsonObject() {
        JSONObject object = new JSONObject();
        try {
            object.put("px", px);
            object.put("py", py);
            object.put("theta", theta);
            object.put("name", name);
            object.put("typeId", typeId);
            object.put("priority", priority);
            object.put("ignoreDistance", ignoreDistance);
            object.put("safeDistance", safeDistance);
            object.put("noDirectionalParking", noDirectionalParking);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return object;
    }
}
