package com.ainirobot.coreservice.bean;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

public class BackgroundTaskEvent implements Parcelable {
    private String currentTaskId;
    private String currentTaskType;
    private String currentTaskName;
    private List<TaskEvent> backgroundTaskList;

    public BackgroundTaskEvent() {
    }

    protected BackgroundTaskEvent(Parcel in) {
        currentTaskId = in.readString();
        currentTaskType = in.readString();
        currentTaskName = in.readString();
        backgroundTaskList = in.createTypedArrayList(TaskEvent.CREATOR);
    }

    public static final Creator<BackgroundTaskEvent> CREATOR = new Creator<BackgroundTaskEvent>() {
        @Override
        public BackgroundTaskEvent createFromParcel(Parcel in) {
            return new BackgroundTaskEvent(in);
        }

        @Override
        public BackgroundTaskEvent[] newArray(int size) {
            return new BackgroundTaskEvent[size];
        }
    };

    public String getCurrentTaskId() {
        return currentTaskId;
    }

    public void setCurrentTaskId(String currentTaskId) {
        this.currentTaskId = currentTaskId;
    }

    public String getCurrentTaskType() {
        return currentTaskType;
    }

    public void setCurrentTaskType(String currentTaskType) {
        this.currentTaskType = currentTaskType;
    }

    public String getCurrentTaskName() {
        return currentTaskName;
    }

    public void setCurrentTaskName(String currentTaskName) {
        this.currentTaskName = currentTaskName;
    }

    public List<TaskEvent> getBackgroundTaskList() {
        return backgroundTaskList;
    }

    public void setBackgroundTaskList(List<TaskEvent> backgroundTaskList) {
        this.backgroundTaskList = backgroundTaskList;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeString(currentTaskId);
        parcel.writeString(currentTaskType);
        parcel.writeString(currentTaskName);
        parcel.writeTypedList(backgroundTaskList);
    }
}
