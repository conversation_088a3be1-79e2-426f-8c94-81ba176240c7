package com.ainirobot.coreservice.action;

import android.os.IBinder;
import android.os.RemoteException;

import com.ainirobot.coreservice.listener.IActionListener;

public class ActionListenerProxy implements IActionListener {
    @Override
    public void onResult(int status, String responseString) throws RemoteException {

    }

    @Override
    public void onError(int errorCode, String errorString) throws RemoteException {

    }

    @Override
    public void onStatusUpdate(int status, String data) throws RemoteException {

    }

    @Override
    public void onResultWithExtraData(int status, String responseString, String extraData) throws RemoteException {

    }

    @Override
    public void onErrorWithExtraData(int errorCode, String errorString, String extraData) throws RemoteException {

    }

    @Override
    public void onStatusUpdateWithExtraData(int status, String data, String extraData) throws RemoteException {

    }

    @Override
    public IBinder asBinder() {
        return null;
    }
}
