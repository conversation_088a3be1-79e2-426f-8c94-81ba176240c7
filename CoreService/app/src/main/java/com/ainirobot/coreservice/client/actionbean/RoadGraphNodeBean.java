package com.ainirobot.coreservice.client.actionbean;

public class RoadGraphNodeBean {

    /**
     * 线段 和 端点 id 互相不能重复，从 0 递增，唯一且不可重复
     */
    private int id;
    /**
     * 地图点位坐标
     */
    private Vector2dBean position;

    public RoadGraphNodeBean() {

    }

    public RoadGraphNodeBean(int id, Vector2dBean position) {
        this.id = id;
        this.position = position;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Vector2dBean getPosition() {
        return position;
    }

    public void setPosition(Vector2dBean position) {
        this.position = position;
    }

    public RoadGraphNodeBean deepCopy() {
        RoadGraphNodeBean newNode = new RoadGraphNodeBean(this.getId(),
                new Vector2dBean(this.getPosition().x, this.getPosition().y));
        return newNode;
    }

    @Override
    public String toString() {
        return "RoadGraphNodeBean{" +
                "id=" + id +
                ", position=" + position +
                '}';
    }
}
