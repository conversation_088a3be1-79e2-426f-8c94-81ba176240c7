/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.client.surfaceshare;

import android.content.Context;
import android.os.Bundle;
import android.os.RemoteException;
import android.util.Log;
import android.view.Surface;

import com.ainirobot.coreservice.IRobotBinderPool;
import com.ainirobot.coreservice.ISurfaceShareApi;
import com.ainirobot.coreservice.client.BaseSubApi;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.messagedispatcher.MessageDispatcher;
import com.ainirobot.coreservice.client.messagedispatcher.SurfaceShareDispatcher;
import com.google.gson.Gson;

public class SurfaceShareApi extends BaseSubApi {

    private static final String TAG = SurfaceShareApi.class.getSimpleName();

    private static SurfaceShareApi instance;
    private String mPackageName;
    private ISurfaceShareApi mApi;
    private Gson mGson;
    private MessageDispatcher mMessageDispatcher;

    private SurfaceShareApi() {
        mGson = new Gson();
        mMessageDispatcher = new MessageDispatcher();
    }

    public static SurfaceShareApi getInstance() {
        if (instance == null) {
            instance = new SurfaceShareApi();
        }
        return instance;
    }

    public int requestImageFrame(Surface surface, SurfaceShareBean bean, SurfaceShareListener listener) {
        Log.d(TAG, "requestImageFrame");
        if (mApi == null || surface == null) {
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
        try {
            String jsonStr;
            if (bean == null) {
                bean = new SurfaceShareBean();
            }
            bean.setPackageName(mPackageName);
            jsonStr = mGson.toJson(bean);
            Bundle bundle = new Bundle();
            bundle.putParcelable("surface", surface);
            SurfaceShareDispatcher surfaceShareDispatcher = null;
            if (listener != null) {
                surfaceShareDispatcher = mMessageDispatcher.obtainSurfaceShareDispatcher(listener);
            }
            return mApi.requestImageFrame(bundle, jsonStr, surfaceShareDispatcher);
        } catch (RemoteException e) {
            return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
        }
    }

    public int abandonImageFrame(SurfaceShareBean bean) {
        Log.d(TAG, "abandonImageFrame");
        if (mApi == null) {
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
        try {
            String jsonStr;
            if (bean == null) {
                bean = new SurfaceShareBean();
            }
            bean.setPackageName(mPackageName);
            jsonStr = mGson.toJson(bean);
            return mApi.abandonImageFrame(jsonStr);
        } catch (RemoteException e) {
            return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
        }
    }

    public boolean isUsed() {
        Log.d(TAG, "isUsed");
        if (mApi == null) {
            return false;
        }
        try {
            return mApi.isUsed();
        } catch (RemoteException e) {
            return false;
        }
    }

    @Override
    public void onConnect(IRobotBinderPool robotBinderPool, Context context) {
        Log.d(TAG, "onConnect packageName: " + context.getPackageName());
        try {
            mApi = ISurfaceShareApi.Stub.asInterface(robotBinderPool.queryBinder(Definition.BIND_SURFACE_SHARE,
                    context.getPackageName()));
            mPackageName = context.getPackageName();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onDisconnect() {
        Log.d(TAG, "onDisconnect");
    }
}
