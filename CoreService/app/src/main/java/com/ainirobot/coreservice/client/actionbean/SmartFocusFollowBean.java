package com.ainirobot.coreservice.client.actionbean;

import android.support.annotation.IntDef;

import com.ainirobot.coreservice.test.TestModule;

/**
 * Created by orion on 2018/4/12.
 */

public class SmartFocusFollowBean extends BaseBean {

    @IntDef({InitMode.TRACK, InitMode.MULTI_STATION})
    public @interface InitMode {
        int TRACK = 0; //追踪
        int MULTI_STATION = 1; //首次启动多人静止
        int MULTI_STATION_ALWAYS = 2; //始终多人静止
    }

    //int reqId, String personName, long lostTimer, float maxDistance, int type
    private int personId;
    private long lostTimer;
    private float maxDistance;
    private boolean isAllowMoveBody = true;
    private @InitMode int initMode = InitMode.TRACK;

    public SmartFocusFollowBean() {
    }

    public @InitMode int getInitMode() {
        return TestModule.initMode;
    }

    public void setInitMode(@InitMode int initMode) {
        this.initMode = initMode;
    }

    public long getLostTimer() {
        return lostTimer;
    }

    public float getMaxDistance() {
        return maxDistance;
    }

    public void setLostTimer(long lostTimer) {
        this.lostTimer = lostTimer;
    }

    public void setMaxDistance(float maxDistance) {
        this.maxDistance = maxDistance;
    }

    public int getPersonId() {
        return personId;
    }

    public void setPersonId(int personId) {
        this.personId = personId;
    }

    public boolean isAllowMoveBody() {
        return this.isAllowMoveBody;
    }

    public void setAllowMoveBody(boolean isAllow) {
        this.isAllowMoveBody = isAllow;
    }
}
