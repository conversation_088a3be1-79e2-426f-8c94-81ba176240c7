package com.ainirobot.coreservice.core.system;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.bean.Task;
import com.ainirobot.coreservice.bean.TaskEvent;
import com.ainirobot.coreservice.core.status.StatusManager;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class TaskManager {

    private static final String TAG = "TaskManager";

    private volatile Task runningTask = new Task();
    private ConcurrentHashMap<String, Task> backRunningTasks = new ConcurrentHashMap<>();

    private StatusManager mStatusManager;

    private static TaskManager instance = new TaskManager();
    private TaskManager(){}

    public static TaskManager getInstance(){
        if(instance == null){
            instance = new TaskManager();
        }
        return instance;
    }

    public void init(StatusManager statusManager){
        this.mStatusManager = statusManager;
    }

    public synchronized String addTask(Task task){
        Log.i(TAG, "add task: "+task.toJson());
        String taskId = "";
        switch (task.getMode()){
            case Task.TASK_MODE_FOREGROUND:
                if(handleFrontTask(task)){
                    taskId = runningTask.getTaskId();
                }
                break;

            case Task.TASK_MODE_BACKGROUND:
                taskId = handleBackTask(task);
                break;

            default:
                break;
        }
        return taskId;
    }

    public synchronized void addTakEvent(TaskEvent taskEvent) {
        if (taskEvent == null) {
            return;
        }
        Log.d(TAG, "addTakEvent event: " + taskEvent.toJson());
        String taskId = taskEvent.getTaskId();

        if (runningTask.getTaskId().equals(taskId) || TextUtils.isEmpty(taskId)) {
            taskEvent.setTaskId(runningTask.getTaskId());
            taskEvent.setTaskType(runningTask.getType());
            runningTask.setTaskEvent(taskEvent);
        } else {
            Task task = getBackTaskByTaskID(taskId);
            if (task != null) {
                taskEvent.setTaskType(task.getType());
                backRunningTasks.get(task.getType()).setTaskEvent(taskEvent);
            } else {
                taskEvent.setTaskId(runningTask.getTaskId());
                taskEvent.setTaskType(runningTask.getType());
                runningTask.setTaskEvent(taskEvent);
            }
        }
        listener.onEventChange(taskEvent);
    }

    public List<Task> getCurrentTask(){
        List<Task> taskList = new ArrayList<>();
        taskList.add(runningTask);
        for (Task task : backRunningTasks.values()){
            taskList.add(task);
        }
        return taskList;
    }

    public void clearTask(){
        Log.i(TAG, "clear task");
        runningTask = new Task();
        backRunningTasks.clear();
    }

    private Task getBackTaskByTaskID(String taskId){
        for(Task task : backRunningTasks.values()){
            if(task.getTaskId().equals(taskId)){
                return task;
            }
        }
        return null;
    }

    private boolean handleFrontTask(Task newTask){
        if(newTask == null){
            return false;
        }
        String taskType = newTask.getType();
        String taskName = newTask.getName();
        int taskStatus = newTask.getStatus();
        if(TextUtils.isEmpty(taskType)){
            taskType = "undefined";
        }

        if(TextUtils.isEmpty(taskName)){
            taskName = "undefined";
        }

        if(!taskType.equals(runningTask.getType()) || taskType.equals("undefined")){
            newTask.setTaskId(generaTaskId(newTask));
            this.listener.onTaskChange(runningTask, newTask);
            try {
                runningTask = (Task) newTask.clone();
            } catch (CloneNotSupportedException e) {
                e.printStackTrace();
                return false;
            }
        } else {
            if(!runningTask.equals(newTask)){
                this.listener.onTaskChange(runningTask, newTask);
            }
            runningTask.setTaskId(newTask.getTaskId());
            runningTask.setName(taskName);
            runningTask.setStatus(taskStatus);
        }
        return true;
    }

    private String handleBackTask(Task bean){
        String taskType = bean.getType();
        if(TextUtils.isEmpty(taskType)){
            taskType = "undefined";
        }
        backRunningTasks.remove(taskType);
        if(bean.getStatus() != Task.TASK_STATUS_FINISH){
            bean.setTaskId(generaTaskId(bean));
            backRunningTasks.put(taskType, bean);
            return bean.getTaskId();
        } else {
            return "";
        }
    }


    private String generaTaskId(Task task){
        if (!TextUtils.isEmpty(task.getTaskId())){
            return task.getTaskId();
        }
        StringBuilder sb = new StringBuilder("");
        if(task.getMode() == Task.TASK_MODE_BACKGROUND) {
            sb.append("b-");
        } else {
            sb.append("f-");
        }
        String sn = RobotSettings.getSystemSn();
        sb.append(sn.substring(sn.length()-4, sn.length()));
        sb.append("-");
        sb.append(System.currentTimeMillis());
        return sb.toString();
    }

    private TaskInfoChangeListener listener = new TaskInfoChangeListener() {
        @Override
        public void onTaskChange(Task lastTask, Task newTask) {
            mStatusManager.handleStatus(Definition.STATUS_TASK_CHANGE, newTask.toJson());
        }

        @Override
        public void onEventChange(TaskEvent taskEvent) {
            mStatusManager.handleStatus(Definition.STATUS_TASK_EVENT_CHANGE, taskEvent.toJson());
        }
    };

    private interface TaskInfoChangeListener {
        void onTaskChange(Task lastTask, Task newTask);
        void onEventChange(TaskEvent taskEvent);
    }


}
