/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.core;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.bean.Command;
import com.ainirobot.coreservice.core.LocalApi.OnCmdResponse;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CopyOnWriteArrayList;

public class CommandManager {

    private static int sCmdCount;
    private ConcurrentLinkedQueue<CmdAction> mCmdActionQueue;
    private HashMap<String, Command> mCommands;

    //Commands to waiting for the response
    private ConcurrentHashMap<String, List<CmdAction>> mWaitingList;
    private ConcurrentHashMap<Integer, CmdAction> mWaitingCommands;
    private CmdTimeoutProcessor mTimeoutProcessor;

    public CommandManager(Context context) {
        mCmdActionQueue = new ConcurrentLinkedQueue<>();
        sCmdCount = 0;

        mCommands = new HashMap<>();
        mWaitingCommands = new ConcurrentHashMap<>();
        mWaitingList = new ConcurrentHashMap<>();
        mTimeoutProcessor = new CmdTimeoutProcessor();
    }

    public synchronized void addCommand(String serviceName, List<Command> commands) {
        for (Command command : commands) {
            if (command == null) {
                Log.d("CommandManager", serviceName);
                continue;
            }
            Log.d("CommandManager", "Register command : " + command.getType() + "  " + serviceName);
            command.setServiceName(serviceName);
            mCommands.put(command.getType(), command);
        }
    }

    public Iterator<Command> getCmdIterator() {
        return mCommands.values().iterator();
    }

    public List<Command> getCommandByFunction(int function) {
        return new ArrayList<>();
    }

    public synchronized CmdAction newCmdAction(int reqId, String type,
                                  String params, boolean isContinues, OnCmdResponse listener) {
        Command cmd = getCommand(type);
        CmdAction cmdAction = new CmdAction(sCmdCount, reqId, cmd, params, isContinues, listener);
        sCmdCount++;
        return cmdAction;
    }

    public void addCmdAction(CmdAction cmd) {
        mCmdActionQueue.offer(cmd);
    }

    public boolean removeCmdAction(CmdAction cmdAction) {
        return mCmdActionQueue.remove(cmdAction);
    }

    public void removeAllCmdAction() {
        mCmdActionQueue.clear();
    }

    public CmdAction getCmdAction(int id) {
        for (CmdAction cmd : mCmdActionQueue) {
            if (cmd.getId() == id) {
                return cmd;
            }
        }
        return null;
    }

    //get but not remove
    public CmdAction peekFirstAction() {
        return mCmdActionQueue.peek();
    }

    //get and remove
    public CmdAction pollFirstAction() {
        return mCmdActionQueue.poll();
    }

    private Command getCommand(String cmdType) {
        Command cmd = mCommands.get(cmdType);
        if (cmd == null) {
            cmd = new Command(cmdType);
        }
        return cmd;
    }

    public boolean isCmdSupported(String cmdType) {
        return getCommand(cmdType) != null;
    }

    public boolean isEmpty() {
        return mCmdActionQueue.isEmpty();
    }

    public void joinWaiting(CmdAction cmdAction) {
        if (cmdAction.getCallBack() == null) {
            return;
        }

        String type = cmdAction.getType();
        join(type, cmdAction, mWaitingList);

//        String stopCommand = cmdAction.getStopCommand();
//        join(stopCommand, cmdAction, mPendingStopList);

        mWaitingCommands.put(cmdAction.getId(), cmdAction);
        mTimeoutProcessor.submit(cmdAction);
    }

    public void removeFromWaiting(CmdAction action) {
        if (action.getCallBack() == null) {
            return;
        }

        String type = action.getType();
        if (!TextUtils.isEmpty(type) && mWaitingList.containsKey(type)) {
            mWaitingList.get(type).remove(action);
        }

//        String stopCommand = action.getStopCommand();
//        if (!TextUtils.isEmpty(stopCommand) && mPendingStopList.containsKey(stopCommand)) {
//            mPendingStopList.get(stopCommand).remove(action);
//        }

        mWaitingCommands.remove(action.getId());
        mTimeoutProcessor.cancel(action);
    }

    public List<CmdAction> getWaitingCmdList(String cmdType) {
        return get(cmdType, mWaitingList);
    }

    public CmdAction getWaitingCmdAction(int id) {
        return mWaitingCommands.get(id);
    }

//    public List<CmdAction> getPendingStopList(String stopCommand) {
//        return get(stopCommand, mPendingStopList);
//    }

    private List<CmdAction> get(String type, Map<String, List<CmdAction>> data) {
        if (!TextUtils.isEmpty(type) && data.containsKey(type)) {
            return data.get(type);
        }
        return new CopyOnWriteArrayList<>();
    }

    private void join(String type, CmdAction action, Map<String, List<CmdAction>> map) {
        if (TextUtils.isEmpty(type)) {
            return;
        }

        if (map.containsKey(type)) {
            map.get(type).add(action);
        } else {
            List<CmdAction> list = new CopyOnWriteArrayList<>();
            list.add(action);
            map.put(type, list);
        }
    }

    public final class CmdAction {
        private final int id;
        private final int requestId;
        private final Command cmd;
        private final String params;
        private final OnCmdResponse callBack;
        private final boolean isContinues;
        private TimeoutListener mListener;
        private String language;

        private CmdAction(int id, int reqId, Command cmd,
                          String params, boolean isContinues, OnCmdResponse callBack) {
            this.id = id;
            this.cmd = cmd;
            this.params = params;
            this.requestId = reqId;
            this.callBack = callBack;
            this.isContinues = isContinues;
        }

        public int getId() {
            return id;
        }

        public String getServiceName() {
            return cmd.getServiceName();
        }

        public String getParams() {
            return params;
        }

        public boolean isAsync() {
            return cmd.isAsync();
        }

        public String getType() {
            return cmd.getType();
        }

        public int getReqId() {
            return requestId;
        }

        public OnCmdResponse getCallBack() {
            return callBack;
        }

        public boolean isContinues() {
            return isContinues;
        }

        public long getTimeout() {
            return cmd.getTimeout();
        }

        public void setTimeoutListener(TimeoutListener listener) {
            mListener = listener;
        }

        public String getLanguage() {
            return language;
        }

        public void setLanguage(String language) {
            this.language = language;
        }

        public void onTimeout() {
            if (mListener != null) {
                mListener.onTimeout();
            }
        }
    }


    public interface TimeoutListener {
        void onTimeout();
    }
}
