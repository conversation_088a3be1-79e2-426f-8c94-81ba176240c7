package com.ainirobot.coreservice.action.download_map.state;

import android.text.TextUtils;

import com.ainirobot.coreservice.action.download_map.DownloadMapStateEnum;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.DownloadMapBean;

public class DownloadMapPkgState extends BaseDownloadMapPkgState {

    private boolean hasLocalMap;

    @Override
    protected void onStart(String paramsStr, DownloadMapBean downloadMapBean) {
        hasLocalMap = downloadMapBean.hasLocalMap();
        if(!hasLocalMap){ //下载地图
            sendCommand(0, Definition.CMD_REMOTE_DOWNLOAD_MAP_PKG, paramsStr);
        }else { //导入地图
            onStateRunSuccess();
        }
    }

    @Override
    public boolean onCmdResponse(int cmdId, String command, String result, String extraData) {
        if (TextUtils.equals(command, Definition.CMD_REMOTE_DOWNLOAD_MAP_PKG)) {
            if (TextUtils.equals(Definition.SUCCEED, result)) {
                onStateRunSuccess();
            } else {
                onStateRunFailed(0, "download is failed");
            }
            return true;
        }
        return false;
    }

    @Override
    protected DownloadMapStateEnum getNextState() {
        return !hasLocalMap ? DownloadMapStateEnum.UNZIP_MAP_FILE : DownloadMapStateEnum.COPY_IMPORT_MAP_FILE;
    }
}
