package com.ainirobot.coreservice.action.advnavi.bean;

/**
 * Data: 2022/8/9 19:41
 * Author: wanglijing
 * Description: ElevatorInfoBean
 */
public class ElevatorInfoBean {
    private String elevatorName;
    private int currentFloor;
    private int targetFloor;

    public ElevatorInfoBean(String elevatorName, int currentFloor,
                            int targetFloor) {
        this.elevatorName = elevatorName;
        this.currentFloor = currentFloor;
        this.targetFloor = targetFloor;
    }

    @Override
    public String toString() {
        return "Elevator{" +
                "elevatorName='" + elevatorName +
                ", currentFloor=" + currentFloor +
                ", targetFloor=" + targetFloor +
                '}';
    }
}
