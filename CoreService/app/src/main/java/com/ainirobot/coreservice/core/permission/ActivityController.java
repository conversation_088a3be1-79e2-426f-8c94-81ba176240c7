package com.ainirobot.coreservice.core.permission;

import android.app.IActivityController;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.os.Message;

public class ActivityController extends IActivityController.Stub {

    private static final String BUILD_TYPE_USER = "user";
    private Handler mHandler;

    public ActivityController(<PERSON><PERSON> handler) {
        mHandler = handler;
    }

    @Override
    public boolean activityStarting(Intent intent, String pkg) {
        if (mHandler != null) {
            Message message = new Message();
            message.what = PermissionManager.TYPE_ACTIVITY_STARTING;
            message.obj = new PermissionBean(intent, pkg);
            mHandler.sendMessage(message);
        }
        return true;
    }

    @Override
    public boolean activityResuming(String pkg) {
        if (mHandler != null) {
            Message message = new Message();
            message.what = PermissionManager.TYPE_ACTIVITY_RESUMING;
            message.obj = new PermissionBean(pkg, null);
            mHandler.sendMessage(message);
        }
        return true;
    }

    @Override
    public boolean appCrashed(String processName, int pid, String shortMsg, String longMsg,
                              long timeMillis, String stackTrace) {
        if (mHandler != null) {
            Message message = new Message();
            message.what = PermissionManager.TYPE_APP_CRASHED;
            message.obj = new PermissionBean(processName, pid, shortMsg, longMsg, timeMillis,
                    stackTrace);
            mHandler.sendMessage(message);
        }
        return !BUILD_TYPE_USER.equals(Build.TYPE);
    }

    @Override
    public int appEarlyNotResponding(String processName, int pid, String annotation) {
        if (mHandler != null) {
            Message message = new Message();
            message.what = PermissionManager.TYPE_APP_EARLY_NOT_RESPONDING;
            message.obj = new PermissionBean(processName, pid, annotation);
            mHandler.sendMessage(message);
        }
        return 0;
    }

    @Override
    public int appNotResponding(String processName, int pid, String processStats) {
        if (mHandler != null) {
            Message message = new Message();
            message.what = PermissionManager.TYPE_APP_NOT_RESPONDING;
            message.obj = new PermissionBean(processName, pid, processStats);
            mHandler.sendMessage(message);
        }
        return BUILD_TYPE_USER.equals(Build.TYPE) ? -1 : 0;
    }

    @Override
    public int systemNotResponding(String msg) {
        if (mHandler != null) {
            Message message = new Message();
            message.what = PermissionManager.TYPE_SYSTEM_NOT_RESPONDING;
            message.obj = new PermissionBean(msg);
            mHandler.sendMessage(message);
        }
        return -1;
    }
}
