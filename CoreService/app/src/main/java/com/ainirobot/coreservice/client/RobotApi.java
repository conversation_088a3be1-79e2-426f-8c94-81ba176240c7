package com.ainirobot.coreservice.client;

import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Bundle;
import android.os.HandlerThread;
import android.os.IBinder;
import android.os.Looper;
import android.os.RemoteException;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.base.OrionBase;
import com.ainirobot.coreservice.IModuleRegistry;
import com.ainirobot.coreservice.IRobotBinderPool;
import com.ainirobot.coreservice.bean.BackgroundTaskEvent;
import com.ainirobot.coreservice.bean.ChargeArea;
import com.ainirobot.coreservice.bean.LoadMapBean;
import com.ainirobot.coreservice.bean.Task;
import com.ainirobot.coreservice.client.Definition.TrackMode;
import com.ainirobot.coreservice.client.account.AccountApi;
import com.ainirobot.coreservice.client.actionbean.AngleResetBean;
import com.ainirobot.coreservice.client.actionbean.AutoChargeBean;
import com.ainirobot.coreservice.client.actionbean.BodyFollowBean;
import com.ainirobot.coreservice.client.actionbean.CheckObstacleBean;
import com.ainirobot.coreservice.client.actionbean.CommandBean;
import com.ainirobot.coreservice.client.actionbean.ControlElectricDoorBean;
import com.ainirobot.coreservice.client.actionbean.CruiseParams;
import com.ainirobot.coreservice.client.actionbean.CruiseRouteBean;
import com.ainirobot.coreservice.client.actionbean.FaceTrackBean;
import com.ainirobot.coreservice.client.actionbean.FocusFollowBean;
import com.ainirobot.coreservice.client.actionbean.GatePairPose;
import com.ainirobot.coreservice.client.actionbean.GoPositionBean;
import com.ainirobot.coreservice.client.actionbean.GoPositionByTypeBean;
import com.ainirobot.coreservice.client.actionbean.GongFuBean;
import com.ainirobot.coreservice.client.actionbean.HeadTurnBean;
import com.ainirobot.coreservice.client.actionbean.HeadTurnBean.HeadTurnMode;
import com.ainirobot.coreservice.client.actionbean.InspectActionBean;
import com.ainirobot.coreservice.client.actionbean.LeadingParams;
import com.ainirobot.coreservice.client.actionbean.LedLightBean;
import com.ainirobot.coreservice.client.actionbean.NaviCmdTimeOutBean;
import com.ainirobot.coreservice.client.actionbean.NavigationAdvancedBean;
import com.ainirobot.coreservice.client.actionbean.NavigationBean;
import com.ainirobot.coreservice.client.actionbean.NavigationFollowBean;
import com.ainirobot.coreservice.client.actionbean.ObstacleInAreaBean;
import com.ainirobot.coreservice.client.actionbean.PictureInfo;
import com.ainirobot.coreservice.client.actionbean.PlaceBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.actionbean.PushReportBean;
import com.ainirobot.coreservice.client.actionbean.RadarAlignBean;
import com.ainirobot.coreservice.client.actionbean.RegisterBean;
import com.ainirobot.coreservice.client.actionbean.ResetEstimateParams;
import com.ainirobot.coreservice.client.actionbean.RobotStandbyBean;
import com.ainirobot.coreservice.client.actionbean.SearchPersonBean;
import com.ainirobot.coreservice.client.actionbean.SmartFocusFollowBean;
import com.ainirobot.coreservice.client.actionbean.StartCreateMapBean;
import com.ainirobot.coreservice.client.actionbean.StopCreateMapBean;
import com.ainirobot.coreservice.client.actionbean.UnRegisterBean;
import com.ainirobot.coreservice.client.actionbean.WakeUpBean;
import com.ainirobot.coreservice.client.ashmem.ShareMemoryApi;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.listener.Person;
import com.ainirobot.coreservice.client.listener.PersonInfoListener;
import com.ainirobot.coreservice.client.log.RLog;
import com.ainirobot.coreservice.client.messagedispatcher.StatusDispatcher;
import com.ainirobot.coreservice.client.module.ModuleCallbackApi;
import com.ainirobot.coreservice.client.permission.PermissionApi;
import com.ainirobot.coreservice.client.person.PersonApi;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.client.surfaceshare.SurfaceShareApi;
import com.ainirobot.coreservice.core.InternalDef;
import com.ainirobot.coreservice.listener.IActionListener;
import com.ainirobot.coreservice.utils.FileUtils;
import com.ainirobot.coreservice.utils.SettingDataHelper;
import com.ainirobot.coreservice.utils.Utils;
import com.ainirobot.coreservice.utils.ZipUtils;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.reflect.TypeToken;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * RobotOS sdk核心类.<br>
 * 该类涵盖导航，头部运动，语音类等技能api
 * <p>RobotOS使用时，必须initAPi
 *
 * <AUTHOR>
 * @version 1.1.1
 * @since 2021-03-05
 */
public class RobotApi extends BaseApi {
    private static final String TAG = "RobotApi";

    public static final int ERROR_REMOTE_VISITOR = -101;

    private static final String DETECT_BODY = "body";
    private static final String DETECT_FACE = "face";
    private static final String DETECT = "detect";
    private static final String FILE_BACK = "file_back";

    private static RobotApi instance = new RobotApi();
    private IModuleRegistry mModuleRegistry = null;
    private ModuleCallbackApi mCallback;
    private static Gson mGson = new Gson();
    private final ArrayList<PersonInfoListener> mAllPersonInfoListener = new ArrayList<>();
    private ActionListener mPersonListener;
    private ApiListener apiListener = null;
    private String pkgName = null;
    /**
     * API回调处理线程
     */
    private HandlerThread mResponseThread;

    private RobotApi() {
        super();
        mSubApiList.add(PersonApi.getInstance());
        mSubApiList.add(RobotSettingApi.getInstance());
        mSubApiList.add(AccountApi.getInstance());
        mSubApiList.add(PermissionApi.getInstance());
        mSubApiList.add(SurfaceShareApi.getInstance());
        mSubApiList.add(ShareMemoryApi.getInstance());
    }

    @Override
    public boolean isApiConnectedService() {
        return super.isApiConnectedService();
    }

    public static RobotApi getInstance() {
        if (instance == null) {
            instance = new RobotApi();
        }
        return instance;
    }

    /**
     * RobotApi 开始授权状态
     * <p><br>
     *
     * @platform all
     */
    public void startAuthorization(Context ctx, @NonNull String packageName, String appId) {
        SettingDataHelper.getInstance().setContext(ctx);
        if (!packageName.equals(pkgName)) {
            SettingDataHelper.getInstance().setCurrentApp(packageName);
            pkgName = packageName;
        }
        String signMd5 = Utils.getPackageSign(ctx, packageName);
        SettingDataHelper.getInstance().setAuthInfo(packageName, signMd5, appId);
    }

    /**
     * RobotApi 查询授权状态
     * <p><br>
     *
     * @platform all
     */
    public boolean queryAuthorizationResult() {
        return SettingDataHelper.getInstance().getRobotInt("current_pkg_acl") == 1;
//        return mAuthData.isAuth(pkgName);
    }

    /**
     * RobotApi 初始化方法.
     * <p>调用RobotSDK第一步要进行的操作，只有收到{@link ApiListener}回调成功之后才可以完整使用<br>
     * 如果回调失败，需要重新尝试连接
     * 上述描述指前台任务，后台任务第一步是授权，查询授权成功后，调用该api是后台Client，查询失败或没设置
     * 授权为前台Client
     *
     * @param ctx      context上下文
     * @param listener 回调listener
     * @platform all
     */
    public void connectServer(Context ctx, ApiListener listener) {
        this.apiListener = listener;
        this.ctx = ctx;
        SettingDataHelper.getInstance().setContext(ctx);
        String zone = SettingDataHelper.getInstance().getCloudServerZone();
        Log.d(TAG, "RobotApi connectServer zone: " + zone);
        OrionBase.setDomainEnv(ProductInfo.isOverSea(), zone, RobotSettingApi.getInstance()
                                                                             .getRobotInt(Definition.ROBOT_SETTING_SYSTEM_ENV) == 1);
        OrionBase.start(ctx, null);
        RLog.initRLog(ctx);

        addApiEventListener(listener);
        Intent intent = IntentUtil.createExplicitIntent(Definition.CORE_SERVICE_NAME,
                IRobotBinderPool.class.getName());
        intent.putExtra(Definition.CLIENT_PACKAGE_NAME, ctx.getPackageName());
        ctx.bindService(intent, apiConnection, Service.BIND_AUTO_CREATE);
    }

    /**
     * RobotApi 去初始化.
     *
     * @platform all
     */
    @Override
    public void disconnectApi() {
        try {
            boolean isRemove = removeApiEventListener(apiListener);
            Log.d(TAG, "disconnectApi removeApiEventListener : " + isRemove);
            if (this.ctx != null) {
                Log.d(TAG, "unbindService apiConnection ");
                this.ctx.unbindService(apiConnection);
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    /**
     * 设置请求及系统事件的回调.
     * <p>{@link ModuleCallbackApi}为主要回调函数，RobotOS会通过该类回调主要事件
     *
     * @platform all
     * @see ModuleCallbackApi
     */
    public void setCallback(ModuleCallbackApi callback) {
        if (mModuleRegistry == null) {
            mCallback = callback;
            return;
        }

        try {
            mModuleRegistry.setCallback(callback);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }


    /**
     * API connection
     */
    protected ServiceConnection apiConnection = new ServiceConnection() {

        @Override
        public void onServiceConnected(ComponentName className, IBinder service) {
            mRobotBinderPool = IRobotBinderPool.Stub.asInterface(service);

            try {
                if (SettingDataHelper.getInstance().getRobotInt("current_pkg_acl") == 1) {
//                if (mAuthData.isAuth(ctx.getPackageName())) {
                    mModuleRegistry = IModuleRegistry.Stub.asInterface(
                            mRobotBinderPool.queryBinder(Definition.BIND_ROBOT_BACKGROUND, ctx.getPackageName()));
                } else {
                    mModuleRegistry = IModuleRegistry.Stub.asInterface(
                            mRobotBinderPool.queryBinder(Definition.BIND_ROBOT, ctx.getPackageName()));
                }
                mIsServiceConnected = true;
                notifyEventApiConnected();

                if (mCallback != null) {
                    setCallback(mCallback);
                }
            } catch (RemoteException | NullPointerException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName className) {
            mIsServiceConnected = false;
            notifyEventApiDisconnected();
            mModuleRegistry = null;
        }
    };


    /**
     * 检测注册模块是否可用.
     * <p> 不可用的情况可能包括：未调用{@link RobotApi#connectServer(Context, ApiListener)};当前client被挂起等
     *
     * @return ture RobotOS正常； false Robotos 异常
     * @platform all
     */
    public boolean isActive() {
        if (mModuleRegistry != null) {
            try {
                return mModuleRegistry.isActive();
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        return false;
    }

    /**
     * 禁止系统接管急停事件.
     * 系统不再接管急停事件，但机器人无法使用运动功能，其他能力可使用
     *
     * @platform all
     */
    public void disableEmergency() {
        try {
            if (mModuleRegistry == null) {
                Log.d(TAG, "Disable emergency failed, mModuleRegistry is null");
                return;
            }
            mModuleRegistry.disableEmergency();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 允许系统接管急停事件.
     * 系统接管急停事件后，当前调用client会被挂起，不能够使用任何机器人能力
     *
     * @platform all
     */
    public void enableEmergency() {
        try {
            if (mModuleRegistry == null) {
                Log.d(TAG, "enable emergency failed, mModuleRegistry is null");
                return;
            }
            mModuleRegistry.enableEmergency();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 禁止系统接管充电.
     * <p>充电时，client app可使用任何能力
     *
     * @platform all
     */
    public void disableBattery() {
        try {
            if (mModuleRegistry == null) {
                Log.d(TAG, "Disable battery failed, mModuleRegistry is null");
                return;
            }
            mModuleRegistry.disableBattery();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 允许系统接管充电状态.
     * <p>系统接管充电意味着当前clientapp，在充电状态下会被挂起
     *
     * @platform all
     */
    public void enableBattery() {
        try {
            if (mModuleRegistry == null) {
                Log.d(TAG, "Disable battery failed, mModuleRegistry is null");
                return;
            }
            mModuleRegistry.enableBattery();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 禁止系统接管推动报警事件.
     * 系统不再接管推动报警
     *
     * @platform all
     */
    public void disablePushWarning() {
        try {
            if (mModuleRegistry == null) {
                Log.d(TAG, "Disable push warning failed, mModuleRegistry is null");
                return;
            }
            mModuleRegistry.disablePushWarning();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 允许系统接管推动报警事件.
     * 系统接管推动报警事件后，当前调用client会被挂起，不能够使用任何机器人能力
     *
     * @platform all
     */
    public void enablePushWarning() {
        try {
            if (mModuleRegistry == null) {
                Log.d(TAG, "enable push warning failed, mModuleRegistry is null");
                return;
            }
            mModuleRegistry.enablePushWarning();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 重置机器人状态.
     * 恢复系统状态到设置的情况，系统状态包括：急停使能，充电使能
     *
     * @platform all
     */
    public void resetSystemStatus() {
        try {
            if (mModuleRegistry == null) {
                Log.d(TAG, "Reset system status failed, mModuleRegistry is null");
                return;
            }
            mModuleRegistry.resetSystemStatus();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 连续执行一系列底盘和头部动作.
     *
     * <p>可以指定系列动作{@link GongFuBean}，动作会按照预定设计执行<br>
     * 对应停止动作api：{@link #stopPlayAction(int)}
     *
     * @param reqId    请求id
     * @param params   动作剧本
     * @param listener 回调
     * @return api是否执行成功
     * @platform all
     */
    public int startPlayAction(int reqId, GongFuBean params, ActionListener listener) {
        if (params == null) {
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
        params.setReqId(reqId);
        String jsonStr = mGson.toJson(params);

        return startAction(reqId, Definition.ACTION_GONGFU, jsonStr, listener);
    }

    /**
     * 停止{@link #startPlayAction(int, GongFuBean, ActionListener)}启动的动作.
     *
     * @param reqId 请求id
     * @return api是否执行成功
     */
    public int stopPlayAction(int reqId) {
        return stopAction(reqId, Definition.ACTION_GONGFU, true);
    }

    /**
     * 执行头部动作.
     *
     * <p>已废弃，可以使用{@link RobotApi#startPlayAction(int, GongFuBean, ActionListener)} 替代
     *
     * @param reqId    请求id
     * @param params   头部动作剧本
     * @param listener 动作执行回调
     * @return api是否执行成功
     * @platform meissa
     * @deprecated
     */
    public int startHeadAction(int reqId, GongFuBean params, ActionListener listener) {
        //todo 播放ppt时有用到，是否可以去掉
        if (params == null) {
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
        params.setReqId(reqId);
        String jsonStr = mGson.toJson(params);

        return startAction(reqId, Definition.ACTION_HEAD_ACTIONS, jsonStr, listener);
    }

    /**
     * 停止执行头部动作.
     *
     * <p>和{@link RobotApi#startGetHeadCount(int, double, CommandListener)}成对使用，已废弃
     *
     * @param reqId
     * @return api是否执行成功
     * @platform meissa
     * @deprecated
     */
    public int stopHeadAction(int reqId) {
        return stopAction(reqId, Definition.ACTION_HEAD_ACTIONS, true);
    }

    /**
     * 开始引领.
     *
     * <p>这是一个组件功能，可以通过{@link LeadingParams}设置引领参数，执行引领动作<br>
     * 可以通过{@link #stopLead(int, boolean)}停止引领
     *
     * @param reqId    请求参数
     * @param params   引领参数
     * @param listener 回调函数
     * @return api是否成功执行
     * @platform all
     */
    public int startLead(int reqId, LeadingParams params, ActionListener listener) {
        if (params == null) {
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
        params.setReqId(reqId);
        String jsonStr = mGson.toJson(params);

        return startAction(reqId, Definition.ACTION_LEAD, jsonStr, listener);
    }

    /**
     * 停止引领.
     *
     * <p>启动引领通过{@link #startLead(int, LeadingParams, ActionListener)}开始，可调用此函数强行结束
     *
     * @param reqId     请求id
     * @param isResetHW 停止引领时是否需要重置机器人形态，比如头部归正，摄像头切换到前置等
     * @return api是否成功执行
     * @platform all
     */
    public int stopLead(int reqId, boolean isResetHW) {
        return stopAction(reqId, Definition.ACTION_LEAD, isResetHW);
    }

    /**
     * 开始跟随.
     * <p>组件功能，机器人可追随人的身体运动，可通过{@link #stopBodyFollowAction(int)}停止跟随<br>
     * 可以通过{@link #stopBodyFollowAction(int)}停止跟随
     *
     * @param reqId    请求id
     * @param personId 要跟随的人脸id，如果传值0可自行找人并跟随
     * @param listener 回调函数
     * @return api是否成功执行
     * @platform meissa, mini
     */
    public int startBodyFollowAction(int reqId, int personId, ActionListener listener) {
        return startBodyFollowAction(reqId, personId, 0, 0,
                SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED,
                SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED,
                SettingsUtil.ROBOT_SETTING_FOLLOW_DISTANCE_TO_PERSON,
                listener);
    }

    /**
     * 开始跟随.
     * <p>组件功能，可以支持自定义跟随参数，推荐使用{@link #startBodyFollowAction(int, int, ActionListener)}
     * 以达到最好的跟随效果<br>
     * 通过{@link #stopBodyFollowAction(int)}停止跟随
     *
     * @param reqId          请求id
     * @param personId       要跟随的人脸id，如果传值0可自行找人并跟随
     * @param lostTimer      跟随上人之后，人丢失放弃跟随任务的超时时间，单位ms
     * @param trackTimer     跟随上人之前，找人并且跟随上的超时时间，单位ms
     * @param linearSpeed    跟随线速度，经验值：0.7f
     * @param angularSpeed   跟随角速度，经验值：1.2f
     * @param distancePerson 跟随中机器人与人之间距离，经验值：1
     * @param listener       回调函数
     * @return api是否执行成功
     * @platform meissa, mini
     */
    public int startBodyFollowAction(int reqId, int personId, long lostTimer, long trackTimer,
                                     double linearSpeed, double angularSpeed,
                                     double distancePerson,
                                     ActionListener listener) {
        BodyFollowBean bean = new BodyFollowBean();
        bean.setReqId(reqId);
        bean.setPersonID(personId);
        bean.setLostTime(lostTimer);
        bean.setSetTrackTime(trackTimer);
        bean.setLinearSpeed(linearSpeed);
        bean.setAngularSpeed(angularSpeed);
        bean.setDistanceR2P(distancePerson);
        String jsonStr = mGson.toJson(bean);

        return startAction(reqId, Definition.ACTION_BODY_FOLLOW, jsonStr, listener);
    }

    /**
     * 停止跟随.
     *
     * @param reqId 请求id
     * @return api是否执行成功
     */
    public int stopBodyFollowAction(int reqId) {
        return stopAction(reqId, Definition.ACTION_BODY_FOLLOW, true);
    }

    /**
     * 开始原地焦点跟随.
     * <p>停止焦点跟随使用{@link #stopFocusFollow(int)}
     *
     * @param reqId       int
     * @param personId    focus follow person id
     * @param lostTimer   How long do you think it was lost
     * @param maxDistance How far away is no longer tracking
     * @return int request id
     * @platform meissa, mini，lara
     */
    @Deprecated
    public int startFocusFollow(int reqId, int personId, long lostTimer,
                                float maxDistance, ActionListener listener) {
        return startFocusFollow(reqId, personId, lostTimer, maxDistance, true, listener);
    }

    /**
     * 开始原地焦点跟随.
     * <p>停止焦点跟随使用{@link #stopFocusFollow(int)}
     *
     * @param reqId           请求id
     * @param personId        焦点跟随的人脸id，取值>=0
     * @param lostTimer       焦点跟随开始后，人脸丢失取消焦点跟随间隔时间
     * @param maxDistance     人脸距离多远之后不再焦点跟随
     * @param isAllowMoveBody 是否允许机器人身体移动
     * @param listener        回调函数
     * @return api执行结果
     * @platform meissa, mini，lara
     */
    @Deprecated
    public int startFocusFollow(int reqId, int personId, long lostTimer,
                                float maxDistance, boolean isAllowMoveBody, ActionListener listener) {
        FocusFollowBean bean = new FocusFollowBean();
        bean.setReqId(reqId);
        bean.setPersonId(personId);
        bean.setLostTimer(lostTimer);
        bean.setMaxDistance(maxDistance);
        bean.setAllowMoveBody(isAllowMoveBody);
        String jsonStr = mGson.toJson(bean);

        return startAction(reqId, Definition.ACTION_FOCUS_FOLLOW, jsonStr, listener);
    }

    public int startSmartFocusFollow(int reqId, long lostTimer, float maxDistance, ActionListener listener) {
        return startSmartFocusFollow(reqId, lostTimer, maxDistance, SmartFocusFollowBean.InitMode.TRACK, listener);
    }

    public int startSmartFocusFollow(int reqId, long lostTimer, float maxDistance,
                                     @SmartFocusFollowBean.InitMode int initMode, ActionListener listener) {
        SmartFocusFollowBean bean = new SmartFocusFollowBean();
        bean.setReqId(reqId);
        bean.setLostTimer(lostTimer);
        bean.setMaxDistance(maxDistance);
        bean.setAllowMoveBody(true);
        bean.setInitMode(initMode);
        String jsonStr = mGson.toJson(bean);

        return startAction(reqId, Definition.ACTION_SMART_FOCUS_FOLLOW, jsonStr, listener);
    }

    public int stopSmartFocusFollow(int reqId) {
        return stopAction(reqId, Definition.ACTION_SMART_FOCUS_FOLLOW, false);
    }

    /**
     * 停止焦点跟随
     *
     * @param reqId int
     * @return int
     * @platform meissa, mini，lara
     */
    @Deprecated
    public int stopFocusFollow(int reqId) {
        return stopAction(reqId, Definition.ACTION_FOCUS_FOLLOW, false);
    }

    /**
     * 停止焦点跟随
     *
     * @param reqId
     * @param isResetHW 是否需要重置机器人形态
     * @return api是否成功执行
     * @platform meissa, mini，lara
     */
    @Deprecated
    public int stopFocusFollow(int reqId, boolean isResetHW) {
        return stopAction(reqId, Definition.ACTION_FOCUS_FOLLOW, isResetHW);
    }

    /**
     * start face track
     *
     * @param reqId int
     * @param bean  face track param bean
     * @return int
     */
    public int startFaceTrack(int reqId, FaceTrackBean bean, ActionListener listener) {
        if (bean == null) {
            bean = new FaceTrackBean();
        }
        String jsonStr = mGson.toJson(bean);
        return startAction(reqId, Definition.ACTION_FACE_TRACK, jsonStr, listener);
    }

    /**
     * stop face track
     *
     * @param reqId int
     * @return int
     */
    public int stopFaceTrack(int reqId) {
        return stopAction(reqId, Definition.ACTION_FACE_TRACK, false);
    }

    /**
     * 开始导航.
     * <p>停止导航使用{@link #stopNavigation(int)}
     *
     * @param reqId               请求id
     * @param destination         导航目的地
     * @param coordinateDeviation 距离目标点多远可以认为是在目标点
     * @param time                机器人人被遮挡不动多长时间即自动退出巡逻
     * @param listener            回调函数
     * @return api执行结果
     * @paltform meissa, mini, saiph, max
     */
    public int startNavigation(int reqId, String destination, double coordinateDeviation,
                               long time, ActionListener listener) {
        return startNavigation(reqId, destination, coordinateDeviation, time, SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED, SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED, listener);
    }

    /**
     * 开始导航.
     * <p>停止导航使用{@link #stopNavigation(int)}
     *
     * @param reqId               请求id
     * @param destination         导航目的地
     * @param coordinateDeviation 距离目标点多远可以认为是在目标点
     * @param obsDistance         最大避障距离，距离目标的障碍物小于该值时，机器人停止
     * @param time                机器人人被遮挡不动多长时间即自动退出巡逻
     * @param listener            回调函数
     * @return api执行结果
     * @paltform meissa, mini, saiph, max
     */
    public int startNavigation(int reqId, String destination, double coordinateDeviation,
                               double obsDistance, long time, ActionListener listener) {
        return startNavigation(reqId, destination, coordinateDeviation, obsDistance, time, SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED, SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED, listener);
    }


    /**
     * 开始导航.
     * <p>停止导航使用{@link #stopNavigation(int)}
     *
     * @param reqId               请求id
     * @param destination         目的地名称
     * @param coordinateDeviation 距离目标点多远可以认为是在目标点
     * @param time                机器人人被遮挡不动多长时间即自动退出巡逻
     * @param linearSpeed         机器人运动线速度
     * @param angularSpeed        机器人运动角速度
     * @param listener            回调函数
     * @return api执行结果
     * @paltform meissa, mini, saiph, max
     */
    public int startNavigation(int reqId, String destination, double coordinateDeviation,
                               long time, double linearSpeed, double angularSpeed, ActionListener listener) {
        return startNavigation(reqId, destination, coordinateDeviation, time, linearSpeed, angularSpeed, false, listener);
    }

    /**
     * 开始导航.
     * <p>停止导航使用{@link #stopNavigation(int)}
     *
     * @param reqId               请求id
     * @param destination         目的地名称
     * @param coordinateDeviation 距离目标点多远可以认为是在目标点
     * @param obsDistance         最大避障距离，距离目标的障碍物小于该值时，机器人停止
     * @param time                机器人人被遮挡不动多长时间即自动退出巡逻
     * @param linearSpeed         机器人运动线速度
     * @param angularSpeed        机器人运动角速度
     * @param listener            回调函数
     * @return api执行结果
     * @paltform meissa, mini, saiph, max
     */
    public int startNavigation(int reqId, String destination, double coordinateDeviation, double obsDistance,
                               long time, double linearSpeed, double angularSpeed, ActionListener listener) {
        return startNavigation(reqId, destination, coordinateDeviation, obsDistance, time,
                linearSpeed, angularSpeed, false, 0.0D, 0, 0L, 0, 0, 0, 0, 0, 0, listener);
    }

    /**
     * 开始导航
     * <p>停止导航使用{@link #stopNavigation(int)}
     *
     * @param reqId               请求id
     * @param destination         目的地名称
     * @param coordinateDeviation 距离目标点多远可以认为是在目标点
     * @param time                机器人人被遮挡不动多长时间即自动退出巡逻
     * @param linearSpeed         机器人运动线速度
     * @param angularSpeed        机器人运动角速度
     * @param isAdjustAngle       是否适应导航结束时朝向的角度。如传false，则归正到点位设置时的角度
     * @param listener            回调函数
     * @return api执行结果
     * @paltform meissa, mini, saiph, max
     */
    public int startNavigation(int reqId, String destination, double coordinateDeviation,
                               long time, double linearSpeed, double angularSpeed,
                               boolean isAdjustAngle, ActionListener listener) {
        return startNavigation(reqId, destination, coordinateDeviation, time,
                linearSpeed, angularSpeed, isAdjustAngle, 0.0D, listener);
    }

    /**
     * 开始导航
     * <p>停止导航使用{@link #stopNavigation(int)}
     *
     * @param reqId               请求id
     * @param destination         目的地名称
     * @param coordinateDeviation 距离目标点多远可以认为是在目标点
     * @param time                机器人人被遮挡不动多长时间即自动退出巡逻
     * @param linearSpeed         机器人运动线速度
     * @param angularSpeed        机器人运动角速度
     * @param isAdjustAngle       是否适应导航结束时朝向的角度。如传false，则归正到点位设置时的角度
     * @param destinationRange    目标点无法到达时，距离目标点多少距离即认为导航成功
     * @param listener            回调函数
     * @return api是否执行
     * @paltform meissa, mini, saiph, max
     */
    public int startNavigation(int reqId, String destination, double coordinateDeviation,
                               long time, double linearSpeed, double angularSpeed,
                               boolean isAdjustAngle, double destinationRange, ActionListener listener) {
        return startNavigation(reqId, destination, coordinateDeviation, time,
                linearSpeed, angularSpeed, isAdjustAngle, destinationRange, 0, listener);
    }

    /**
     * 开始导航
     * <p>停止导航使用{@link #stopNavigation(int)}
     *
     * @param reqId                      请求id
     * @param destination                目的地名称
     * @param coordinateDeviation        距离目标点多远可以认为是在目标点
     * @param time                       机器人人被遮挡不动多长时间即自动退出巡逻
     * @param linearSpeed                机器人运动线速度
     * @param angularSpeed               机器人运动角速度
     * @param isAdjustAngle              是否适应导航结束时朝向的角度。如传false，则归正到点位设置时的角度
     * @param destinationRange           目标点无法到达时，距离目标点多少距离即认为导航成功
     * @param wheelOverCurrentRetryCount 导航过程中轮子堵转尝试次数
     * @param listener                   回调函数
     * @return api是否执行
     * @paltform meissa, mini, saiph, max
     */
    public int startNavigation(int reqId, String destination, double coordinateDeviation,
                               long time, double linearSpeed, double angularSpeed,
                               boolean isAdjustAngle, double destinationRange,
                               int wheelOverCurrentRetryCount, ActionListener listener) {
        return startNavigation(reqId, destination, coordinateDeviation, time,
                linearSpeed, angularSpeed, isAdjustAngle, destinationRange,
                wheelOverCurrentRetryCount, 0L, listener);
    }

    public int startNavigation(int reqId, String destination, double coordinateDeviation,
                               long time, double linearSpeed, double angularSpeed,
                               boolean isAdjustAngle, double destinationRange,
                               int wheelOverCurrentRetryCount, long multipleWaitTime,
                               ActionListener listener) {
        return startNavigation(reqId, destination, coordinateDeviation, time,
                linearSpeed, angularSpeed, isAdjustAngle, destinationRange,
                wheelOverCurrentRetryCount, multipleWaitTime, 0, listener);
    }

    /**
     * 开始导航
     * <p>停止导航使用{@link #stopNavigation(int)}
     *
     * @param reqId                      请求id
     * @param destination                目的地名称
     * @param coordinateDeviation        距离目标点多远可以认为是在目标点
     * @param time                       机器人人被遮挡不动多长时间即自动退出巡逻
     * @param linearSpeed                机器人运动线速度
     * @param angularSpeed               机器人运动角速度
     * @param isAdjustAngle              是否适应导航结束时朝向的角度。如传false，则归正到点位设置时的角度
     * @param destinationRange           目标点无法到达时，距离目标点多少距离即认为导航成功
     * @param wheelOverCurrentRetryCount 导航过程中轮子堵转尝试次数
     * @param multipleWaitTime           导航过程中如遇多机等待，多长时间超时
     * @param listener                   回调函数
     * @return api是否执行
     * @paltform meissa, mini, saiph, max
     */
    public int startNavigation(int reqId, String destination, double coordinateDeviation,
                               long time, double linearSpeed, double angularSpeed,
                               boolean isAdjustAngle, double destinationRange,
                               int wheelOverCurrentRetryCount, long multipleWaitTime,
                               int priority, ActionListener listener) {
        return startNavigation(reqId, destination, coordinateDeviation, time,
                linearSpeed, angularSpeed, isAdjustAngle, destinationRange,
                wheelOverCurrentRetryCount, multipleWaitTime, priority,
                0, 0, 0, 0, 0, listener);
    }

    /**
     * 开始导航
     * <p>停止导航使用{@link #stopNavigation(int)}
     *
     * @param reqId                      请求id
     * @param destination                目的地名称
     * @param coordinateDeviation        距离目标点多远可以认为是在目标点
     * @param time                       机器人人被遮挡不动多长时间即自动退出巡逻
     * @param linearSpeed                机器人运动线速度
     * @param angularSpeed               机器人运动角速度
     * @param isAdjustAngle              是否适应导航结束时朝向的角度。如传false，则归正到点位设置时的角度
     * @param destinationRange           目标点无法到达时，距离目标点多少距离即认为导航成功
     * @param wheelOverCurrentRetryCount 导航过程中轮子堵转尝试次数
     * @param multipleWaitTime           导航过程中如遇多机等待，多长时间超时
     * @param startModeLevel             单次导航过程中的启动模式，传0则走系统设置
     * @param brakeModeLevel             单次导航过程中的刹车模式，传0则走系统设置
     * @param listener                   回调函数
     * @return api是否执行
     * @paltform meissa, mini, saiph, max
     */
    public int startNavigation(int reqId, String destination, double coordinateDeviation,
                               long time, double linearSpeed, double angularSpeed,
                               boolean isAdjustAngle, double destinationRange,
                               int wheelOverCurrentRetryCount, long multipleWaitTime,
                               int priority, int startModeLevel, int brakeModeLevel, ActionListener listener) {
        return startNavigation(reqId, destination, coordinateDeviation, time,
                linearSpeed, angularSpeed, isAdjustAngle, destinationRange,
                wheelOverCurrentRetryCount, multipleWaitTime, priority,
                0, 0, 0, startModeLevel, brakeModeLevel, listener);
    }

    public int startNavigation(int reqId, String destination, double coordinateDeviation,
                               long time, double linearSpeed, double angularSpeed,
                               boolean isAdjustAngle, double destinationRange,
                               int wheelOverCurrentRetryCount, long multipleWaitTime,
                               int priority, double linearAcceleration, double angularAcceleration,
                               int runStopCmdParam, ActionListener listener) {
        return startNavigation(reqId, destination, coordinateDeviation, time, linearSpeed, angularSpeed, isAdjustAngle, destinationRange,
                wheelOverCurrentRetryCount, multipleWaitTime, priority, linearAcceleration, angularAcceleration, runStopCmdParam, 0, 0, listener);
    }

    /**
     * 开始导航
     * <p>停止导航使用{@link #stopNavigation(int)}
     *
     * @param reqId                      请求id
     * @param destination                目的地名称
     * @param coordinateDeviation        距离目标点多远可以认为是在目标点
     * @param time                       机器人人被遮挡不动多长时间即自动退出巡逻
     * @param linearSpeed                机器人运动线速度
     * @param angularSpeed               机器人运动角速度
     * @param isAdjustAngle              是否适应导航结束时朝向的角度。如传false，则归正到点位设置时的角度
     * @param destinationRange           目标点无法到达时，距离目标点多少距离即认为导航成功
     * @param wheelOverCurrentRetryCount 导航过程中轮子堵转尝试次数
     * @param multipleWaitTime           导航过程中如遇多机等待，多长时间超时
     * @param runStopCmdParam            导航过程中如遇多机等待，默认发送停止单次导航命令
     * @param listener                   回调函数
     * @return api是否执行
     * @paltform meissa, mini, saiph, max
     */
    public int startNavigation(int reqId, String destination, double coordinateDeviation,
                               long time, double linearSpeed, double angularSpeed,
                               boolean isAdjustAngle, double destinationRange,
                               int wheelOverCurrentRetryCount, long multipleWaitTime,
                               int priority, double linearAcceleration, double angularAcceleration,
                               int runStopCmdParam, int startModeLevel, int brakeModeLevel,
                               ActionListener listener) {
        return startNavigation(reqId, destination, coordinateDeviation, 0.0D, time, linearSpeed, angularSpeed, isAdjustAngle, destinationRange,
                wheelOverCurrentRetryCount, multipleWaitTime, priority, linearAcceleration, angularAcceleration, runStopCmdParam, startModeLevel, brakeModeLevel, listener);
    }

    /**
     * 开始导航
     * <p>停止导航使用{@link #stopNavigation(int)}
     *
     * @param reqId                      请求id
     * @param destination                目的地名称
     * @param coordinateDeviation        距离目标点多远可以认为是在目标点
     * @param obsDistance                最大避障距离，距离目标的障碍物小于该值时，机器人停止
     * @param time                       机器人人被遮挡不动多长时间即自动退出巡逻
     * @param linearSpeed                机器人运动线速度
     * @param angularSpeed               机器人运动角速度
     * @param isAdjustAngle              是否适应导航结束时朝向的角度。如传false，则归正到点位设置时的角度
     * @param destinationRange           目标点无法到达时，距离目标点多少距离即认为导航成功
     * @param wheelOverCurrentRetryCount 导航过程中轮子堵转尝试次数
     * @param multipleWaitTime           导航过程中如遇多机等待，多长时间超时
     * @param runStopCmdParam            导航过程中如遇多机等待，默认发送停止单次导航命令
     * @param listener                   回调函数
     * @return api是否执行
     * @paltform meissa, mini, saiph, max
     */
    public int startNavigation(int reqId, String destination,
                               double coordinateDeviation, double obsDistance,
                               long time, double linearSpeed, double angularSpeed,
                               boolean isAdjustAngle, double destinationRange,
                               int wheelOverCurrentRetryCount, long multipleWaitTime,
                               int priority, double linearAcceleration, double angularAcceleration,
                               int runStopCmdParam, int startModeLevel, int brakeModeLevel,
                               ActionListener listener) {
        return startNavigation(reqId, destination, coordinateDeviation, 0.0D, time, linearSpeed, angularSpeed, isAdjustAngle, destinationRange,
                wheelOverCurrentRetryCount, multipleWaitTime, priority, linearAcceleration, angularAcceleration, runStopCmdParam,
                startModeLevel, brakeModeLevel, 0, 0, listener);
    }

    public int startNavigation(int reqId, String destination,
                               double coordinateDeviation, double obsDistance,
                               long time, double linearSpeed, double angularSpeed,
                               boolean isAdjustAngle, double destinationRange,
                               int wheelOverCurrentRetryCount, long multipleWaitTime,
                               int priority, double linearAcceleration, double angularAcceleration,
                               int runStopCmdParam, int startModeLevel, int brakeModeLevel,
                               double posTolerance, double angleTolerance,
                               ActionListener listener) {
        return startNavigation(reqId, destination, coordinateDeviation, 0.0D, time, linearSpeed, angularSpeed, isAdjustAngle, destinationRange,
                wheelOverCurrentRetryCount, multipleWaitTime, priority, linearAcceleration, angularAcceleration, runStopCmdParam,
                startModeLevel, brakeModeLevel, 0, 0, -1, listener);
    }

    public int startNavigation(int reqId, String destination,
                               double coordinateDeviation, double obsDistance,
                               long time, double linearSpeed, double angularSpeed,
                               boolean isAdjustAngle, double destinationRange,
                               int wheelOverCurrentRetryCount, long multipleWaitTime,
                               int priority, double linearAcceleration, double angularAcceleration,
                               int runStopCmdParam, int startModeLevel, int brakeModeLevel,
                               double posTolerance, double angleTolerance, int roadMode,
                               ActionListener listener) {
        NavigationBean bean = new NavigationBean();
        bean.setReqId(reqId);
        bean.setDestination(destination);
        bean.setTime(time);
        bean.setCoordinateDeviation(coordinateDeviation);
        bean.setLinearSpeed(linearSpeed);
        bean.setAngularSpeed(angularSpeed);
        bean.setAdjustAngle(isAdjustAngle);
        bean.setDestinationRange(destinationRange);
        bean.setWheelOverCurrentRetryCount(wheelOverCurrentRetryCount);
        bean.setMultipleWaitTime(multipleWaitTime);
        bean.setPriority(priority);
        bean.setLinearAcceleration(linearAcceleration);
        bean.setAngularAcceleration(angularAcceleration);
        bean.setRunStopCmdParam(runStopCmdParam);
        bean.setStartModeLevel(startModeLevel);
        bean.setBrakeModeLevel(brakeModeLevel);
        bean.setObsDistance(obsDistance);
        bean.setPosTolerance(posTolerance);
        bean.setAngleTolerance(angleTolerance);
        bean.setRoadMode(roadMode);
        String jsonStr = mGson.toJson(bean);

        return startAction(reqId, Definition.ACTION_NAVIGATION, jsonStr, listener);
    }

    /**
     * 开始导航
     * <p>停止导航使用{@link #stopPoseNavigation(int)}
     *
     * @param reqId               请求id
     * @param pose                目的地坐标点
     * @param coordinateDeviation 开始导航前，距离目标点多远可以认为是在目标点
     * @param time                导航中，机器人人被遮挡不动多长时间即自动退出巡逻
     * @param listener            回调函数
     * @return api是否执行
     * @paltform meissa, mini, saiph, max
     */
    public int startNavigation(int reqId, Pose pose, double coordinateDeviation, long time, ActionListener listener) {
        return startNavigation(reqId, pose, coordinateDeviation, false, time,
                SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED,
                SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED, 0, listener);
    }

    /**
     * 开始导航
     * <p>停止导航使用{@link #stopPoseNavigation(int)}
     *
     * @param reqId               请求id
     * @param pose                目的地坐标点
     * @param coordinateDeviation 开始导航前，距离目标点多远可以认为是在目标点
     * @param obsDistance         最大避障距离，距离目标的障碍物小于该值时，机器人停止
     * @param time                导航中，机器人人被遮挡不动多长时间即自动退出巡逻
     * @param listener            回调函数
     * @return api是否执行
     * @paltform meissa, mini, saiph, max
     */
    public int startNavigation(int reqId, Pose pose, double coordinateDeviation, double obsDistance, long time, ActionListener listener) {
        return startNavigation(reqId, pose, coordinateDeviation, obsDistance, false, 0.0D, time,
                SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED,
                SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED, 0, 0, 0, 0, 0, 0, 0, 0, 0, listener);
    }

    /**
     * 开始导航
     * <p>停止导航使用{@link #stopPoseNavigation(int)}
     *
     * @param reqId               请求id
     * @param pose                目的地坐标点
     * @param coordinateDeviation 开始导航前，距离目标点多远可以认为是在目标点
     * @param obsDistance         最大避障距离，距离目标的障碍物小于该值时，机器人停止
     * @param destinationRange    目标点无法到达时，距离目标点多少距离即认为导航成功
     * @param time                导航中，机器人人被遮挡不动多长时间即自动退出巡逻
     * @param listener            回调函数
     * @return api是否执行
     * @paltform meissa, mini, saiph, max
     */
    public int startNavigation(int reqId, Pose pose, double coordinateDeviation, double obsDistance, double destinationRange, long time, ActionListener listener) {
        return startNavigation(reqId, pose, coordinateDeviation, obsDistance, false, destinationRange, time,
                SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED,
                SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED, 0, 0, 0, 0, 0, 0, 0, 0, 0, listener);
    }

    /**
     * 开始导航
     * <p>停止导航使用{@link #stopPoseNavigation(int)}
     *
     * @param reqId               请求id
     * @param pose                目的地坐标点
     * @param coordinateDeviation 开始导航前，距离目标点多远可以认为是在目标点
     * @param time                导航中，机器人人被遮挡不动多长时间即自动退出巡逻
     * @param linearSpeed         导航线速度
     * @param angularSpeed        导航角速度
     * @param listener            回调函数
     * @return api是否执行
     * @paltform meissa, mini, saiph, max
     */
    public int startNavigation(int reqId, Pose pose, double coordinateDeviation,
                               long time, double linearSpeed, double angularSpeed, ActionListener listener) {
        return startNavigation(reqId, pose, coordinateDeviation, false, time,
                linearSpeed, angularSpeed, listener);
    }

    public int startNavigation(int reqId, Pose pose, double coordinateDeviation, boolean isAdjustAngle,
                               long time, double linearSpeed, double angularSpeed, ActionListener listener) {

        return startNavigation(reqId, pose, coordinateDeviation, isAdjustAngle, time, linearSpeed,
                angularSpeed, 0, listener);
    }

    public int startNavigation(int reqId, Pose pose, double coordinateDeviation, boolean isAdjustAngle,
                               long time, double linearSpeed, double angularSpeed,
                               int wheelOverCurrentRetryCount, ActionListener listener) {
        return startNavigation(reqId, pose, coordinateDeviation, isAdjustAngle, time, linearSpeed,
                angularSpeed, wheelOverCurrentRetryCount, 0, listener);
    }

    public int startNavigation(int reqId, Pose pose, double coordinateDeviation, boolean isAdjustAngle,
                               long time, double linearSpeed, double angularSpeed,
                               int wheelOverCurrentRetryCount, int priority, ActionListener listener) {
        return startNavigation(reqId, pose, coordinateDeviation, isAdjustAngle, time, linearSpeed,
                angularSpeed, wheelOverCurrentRetryCount, priority, 0, 0, 0, 0, 0, listener);
    }

    public int startNavigation(int reqId, Pose pose, double coordinateDeviation, boolean isAdjustAngle,
                               long time, double linearSpeed, double angularSpeed,
                               int wheelOverCurrentRetryCount, int priority,
                               int startModeLevel, int brakeModeLevel, ActionListener listener) {
        return startNavigation(reqId, pose, coordinateDeviation, isAdjustAngle, time, linearSpeed,
                angularSpeed, wheelOverCurrentRetryCount, priority, 0, 0, 0, startModeLevel, brakeModeLevel, listener);
    }

    public int startNavigation(int reqId, Pose pose, double coordinateDeviation, boolean isAdjustAngle,
                               long time, double linearSpeed, double angularSpeed,
                               int wheelOverCurrentRetryCount, int priority,
                               double linearAcceleration, double angularAcceleration,
                               int runStopCmdParam, ActionListener listener) {

        return startNavigation(reqId, pose, coordinateDeviation, isAdjustAngle, time, linearSpeed, angularSpeed,
                wheelOverCurrentRetryCount, priority, linearAcceleration, angularAcceleration, runStopCmdParam, 0, 0, listener);
    }

    /**
     * start navigation action with pose
     * <p>停止导航使用{@link #stopNavigation(int)}
     *
     * @param reqId                      request id
     * @param pose                       pose object
     * @param coordinateDeviation        Coordinate
     * @param isAdjustAngle              is adjust angle, default value is false
     * @param time                       time out ms
     * @param linearSpeed                speed
     * @param angularSpeed               speed
     * @param wheelOverCurrentRetryCount after wheel over current, retry count
     * @param listener                   listener for result
     * @return
     * @paltform meissa, mini, saiph, max
     */
    public int startNavigation(int reqId, Pose pose, double coordinateDeviation, boolean isAdjustAngle,
                               long time, double linearSpeed, double angularSpeed,
                               int wheelOverCurrentRetryCount, int priority,
                               double linearAcceleration, double angularAcceleration,
                               int runStopCmdParam, int startModeLevel, int brakeModeLevel, ActionListener listener) {
        return startNavigation(reqId, pose, coordinateDeviation, 0.0D, isAdjustAngle, 0.0D, time, linearSpeed, angularSpeed,
                wheelOverCurrentRetryCount, priority, linearAcceleration, angularAcceleration, runStopCmdParam, startModeLevel, brakeModeLevel, 0, 0, listener);
    }

    /**
     * start navigation action with pose
     * <p>停止导航使用{@link #stopNavigation(int)}
     *
     * @param reqId                      request id
     * @param pose                       pose object
     * @param coordinateDeviation        Coordinate
     * @param obsDistance                obsDistance
     * @param isAdjustAngle              is adjust angle, default value is false
     * @param time                       time out ms
     * @param linearSpeed                speed
     * @param angularSpeed               speed
     * @param wheelOverCurrentRetryCount after wheel over current, retry count
     * @param listener                   listener for result
     * @return
     * @paltform meissa, mini, saiph, max
     */
    public int startNavigation(int reqId, Pose pose, double coordinateDeviation, double obsDistance,
                               boolean isAdjustAngle, long time, double linearSpeed, double angularSpeed,
                               int wheelOverCurrentRetryCount, int priority,
                               double linearAcceleration, double angularAcceleration,
                               int runStopCmdParam, int startModeLevel, int brakeModeLevel,
                               double posTolerance, double angleTolerance, ActionListener listener) {

        return startNavigation(reqId, pose, coordinateDeviation, obsDistance, isAdjustAngle, 0.0D, time, linearSpeed, angularSpeed,
                wheelOverCurrentRetryCount, priority, linearAcceleration, angularAcceleration, runStopCmdParam, startModeLevel, brakeModeLevel, posTolerance, angleTolerance, listener);
    }

    /**
     * start navigation action with pose
     * <p>停止导航使用{@link #stopNavigation(int)}
     *
     * @param reqId                      request id
     * @param pose                       pose object
     * @param coordinateDeviation        Coordinate
     * @param obsDistance                obsDistance
     * @param isAdjustAngle              is adjust angle, default value is false
     * @param destinationRange           When the target point cannot be reached, the distance from the target point is considered successful navigation
     * @param time                       time out ms
     * @param linearSpeed                speed
     * @param angularSpeed               speed
     * @param wheelOverCurrentRetryCount after wheel over current, retry count
     * @param listener                   listener for result
     * @return
     * @paltform meissa, mini, saiph, max
     */
    public int startNavigation(int reqId, Pose pose, double coordinateDeviation, double obsDistance,
                               boolean isAdjustAngle, double destinationRange, long time, double linearSpeed, double angularSpeed,
                               int wheelOverCurrentRetryCount, int priority,
                               double linearAcceleration, double angularAcceleration,
                               int runStopCmdParam, int startModeLevel, int brakeModeLevel,
                               double posTolerance, double angleTolerance, ActionListener listener) {
        return startNavigation(reqId, pose, coordinateDeviation, obsDistance, isAdjustAngle, destinationRange, time, linearSpeed, angularSpeed,
                wheelOverCurrentRetryCount, priority, linearAcceleration, angularAcceleration, runStopCmdParam, startModeLevel, brakeModeLevel, posTolerance, angleTolerance, -1, listener);
    }

    public int startNavigation(int reqId, Pose pose, double coordinateDeviation, double obsDistance,
                               boolean isAdjustAngle, double destinationRange, long time, double linearSpeed, double angularSpeed,
                               int wheelOverCurrentRetryCount, int priority,
                               double linearAcceleration, double angularAcceleration,
                               int runStopCmdParam, int startModeLevel, int brakeModeLevel,
                               double posTolerance, double angleTolerance, int roadMode, ActionListener listener) {

        GoPositionBean bean = new GoPositionBean();
        bean.setReqId(reqId);
        bean.setPose(pose);
        bean.setTime(time);
        bean.setCoordinateDeviation(coordinateDeviation);
        bean.setIsAdjustAngle(isAdjustAngle);
        bean.setLinearSpeed(linearSpeed);
        bean.setAngularSpeed(angularSpeed);
        bean.setWheelOverCurrentRetryCount(wheelOverCurrentRetryCount);
        bean.setPriority(priority);
        bean.setLinearAcceleration(linearAcceleration);
        bean.setAngularAcceleration(angularAcceleration);
        bean.setRunStopCmdParam(runStopCmdParam);
        bean.setStartModeLevel(startModeLevel);
        bean.setBrakeModeLevel(brakeModeLevel);
        bean.setObsDistance(obsDistance);
        bean.setDestinationRange(destinationRange);
        bean.setPosTolerance(posTolerance);
        bean.setAngleTolerance(angleTolerance);
        bean.setRoadMode(roadMode);
        return startAction(reqId, Definition.ACTION_GO_POSITION, mGson.toJson(bean), listener);
    }

    /**
     * 特殊点位开始导航
     *
     * @param typeId              特殊点位类型
     * @param priority            特殊点位优先级
     * @param coordinateDeviation 开始导航前， 距离目标点多远可以认为是在目标点
     * @param destinationRange    目的地为中心，多大范围内可认为已到达
     * @param time                导航中，机器人人被遮挡不动多长时间即自动退出巡逻
     * @param listener            回调函数
     */
    public int startNavigationByType(int reqId, int typeId, int priority, double coordinateDeviation, double destinationRange, long time, ActionListener listener) {
        return startNavigationByType(reqId, typeId, priority, coordinateDeviation, destinationRange, false, time,
                SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED,
                SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED, 0, listener);
    }

    public int startNavigationByType(int reqId, int typeId, int priority, double coordinateDeviation, double destinationRange, double obsDistance, long time, ActionListener listener) {
        return startNavigationByType(reqId, typeId, priority, coordinateDeviation, destinationRange, obsDistance, false, time,
                SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED,
                SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED, 0, 0, 0, 0, 0, 0, 0, listener);
    }

    public int startNavigationByType(int reqId, int typeId, int priority, double coordinateDeviation, double destinationRange,
                                     long time, double linearSpeed, double angularSpeed, ActionListener listener) {
        return startNavigationByType(reqId, typeId, priority, coordinateDeviation, destinationRange, false, time,
                linearSpeed, angularSpeed, listener);
    }

    public int startNavigationByType(int reqId, int typeId, int priority, double coordinateDeviation, double destinationRange, boolean isAdjustAngle,
                                     long time, double linearSpeed, double angularSpeed, ActionListener listener) {

        return startNavigationByType(reqId, typeId, priority, coordinateDeviation, destinationRange, isAdjustAngle, time, linearSpeed,
                angularSpeed, 0, listener);
    }

    public int startNavigationByType(int reqId, int typeId, int priority, double coordinateDeviation, double destinationRange, boolean isAdjustAngle,
                                     long time, double linearSpeed, double angularSpeed,
                                     int wheelOverCurrentRetryCount, ActionListener listener) {
        return startNavigationByType(reqId, typeId, priority, coordinateDeviation, destinationRange, isAdjustAngle, time, linearSpeed,
                angularSpeed, wheelOverCurrentRetryCount, 0, listener);
    }

    public int startNavigationByType(int reqId, int typeId, int priority, double coordinateDeviation, double destinationRange, boolean isAdjustAngle,
                                     long time, double linearSpeed, double angularSpeed,
                                     int wheelOverCurrentRetryCount, int taskPriority, ActionListener listener) {
        return startNavigationByType(reqId, typeId, priority, coordinateDeviation, destinationRange, isAdjustAngle, time, linearSpeed,
                angularSpeed, wheelOverCurrentRetryCount, taskPriority, 0, 0, 0, 0, 0, listener);
    }

    public int startNavigationByType(int reqId, int typeId, int priority, double coordinateDeviation, double destinationRange, boolean isAdjustAngle,
                                     long time, double linearSpeed, double angularSpeed,
                                     int wheelOverCurrentRetryCount, int taskPriority,
                                     double linearAcceleration, double angularAcceleration,
                                     int runStopCmdParam, int startModeLevel, int brakeModeLevel, ActionListener listener) {
        return startNavigationByType(reqId, typeId, priority, coordinateDeviation, destinationRange, 0.0D, isAdjustAngle, time, linearSpeed, angularSpeed,
                wheelOverCurrentRetryCount, taskPriority, linearAcceleration, angularAcceleration, runStopCmdParam, startModeLevel, brakeModeLevel, listener);
    }

    /**
     * 特殊点位开始导航
     *
     * @param typeId                     特殊点位类型
     * @param priority                   特殊点位优先级
     * @param coordinateDeviation        开始导航前， 距离目标点多远可以认为是在目标点
     * @param destinationRange           目的地为中心，多大范围内可认为已到达
     * @param obsDistance                最大避障距离，距离目标的障碍物小于该值时，机器人停止
     * @param time                       机器人人被遮挡不动多长时间即自动退出巡逻
     * @param linearSpeed                机器人运动线速度
     * @param angularSpeed               机器人运动角速度
     * @param isAdjustAngle              是否适应导航结束时朝向的角度。如传false，则归正到点位设置时的角度
     * @param wheelOverCurrentRetryCount 导航过程中轮子堵转尝试次数
     * @param runStopCmdParam            导航过程中如遇多机等待，默认发送停止单次导航命令
     * @param listener                   回调函数
     */
    public int startNavigationByType(int reqId, int typeId, int priority, double coordinateDeviation, double destinationRange, double obsDistance,
                                     boolean isAdjustAngle, long time, double linearSpeed, double angularSpeed,
                                     int wheelOverCurrentRetryCount, int taskPriority,
                                     double linearAcceleration, double angularAcceleration,
                                     int runStopCmdParam, int startModeLevel, int brakeModeLevel, ActionListener listener) {
        GoPositionByTypeBean bean = new GoPositionByTypeBean();
        bean.setReqId(reqId);
        bean.setTypeId(typeId);
        bean.setPriority(priority);
        bean.setTime(time);
        bean.setCoordinateDeviation(coordinateDeviation);
        bean.setDestinationRange(destinationRange);
        bean.setIsAdjustAngle(isAdjustAngle);
        bean.setLinearSpeed(linearSpeed);
        bean.setAngularSpeed(angularSpeed);
        bean.setWheelOverCurrentRetryCount(wheelOverCurrentRetryCount);
        bean.setTaskPriority(taskPriority);
        bean.setLinearAcceleration(linearAcceleration);
        bean.setAngularAcceleration(angularAcceleration);
        bean.setRunStopCmdParam(runStopCmdParam);
        bean.setStartModeLevel(startModeLevel);
        bean.setBrakeModeLevel(brakeModeLevel);
        bean.setObsDistance(obsDistance);
        return startAction(reqId, Definition.ACTION_NAVI_GO_POSITION_BY_TYPE, mGson.toJson(bean), listener);
    }

    /**
     * 开始导航
     * <p>停止导航使用{@link #stopPoseNavigation(int)}
     *
     * @param reqId
     * @param destination         导航目的地名称
     * @param coordinateDeviation 开始导航前，距离目标点多远可以认为是在目标点
     * @param time                导航中，机器人人被遮挡不动多长时间即自动退出巡逻
     * @param linearSpeed         导航线速度
     * @param angularSpeed        导航角速度
     * @param needRotate          导航到目标点是否需要转身
     * @param listener            回调函数
     * @return API是否执行
     * @paltform meissa, mini, saiph, max
     */
    @Deprecated
    public int startPoseNavigation(int reqId, String destination, double coordinateDeviation
            , long time, double linearSpeed, double angularSpeed
            , boolean needRotate, ActionListener listener) {
        NavigationBean bean = new NavigationBean();
        bean.setReqId(reqId);
        bean.setDestination(destination);
        bean.setTime(time);
        bean.setCoordinateDeviation(coordinateDeviation);
        bean.setLinearSpeed(linearSpeed);
        bean.setAngularSpeed(angularSpeed);
        bean.setmNeedRotate(needRotate);
        String jsonStr = mGson.toJson(bean);

        return startAction(reqId, Definition.ACTION_POSE_NAVIGATION, jsonStr, listener);
    }

    /**
     * 开始导航
     * <p>停止导航使用{@link #stopPoseNavigation(int)}
     *
     * @param reqId
     * @param pose         导航目的地pose
     * @param coordinateDeviation 开始导航前，距离目标点多远可以认为是在目标点
     * @param time                导航中，机器人人被遮挡不动多长时间即自动退出巡逻
     * @param roadMode 导航过程中是否按照地图巡线行驶
     * @param isNeedEstimate 是否需要定位
     * @param listener            回调函数
     * @return API是否执行
     */
    public int startPoseNavigation(int reqId, Pose pose, double coordinateDeviation, long time, double linearSpeed, int roadMode, boolean isNeedEstimate, boolean isAdjustAngle, ActionListener listener) {
        GoPositionBean bean = new GoPositionBean();
        bean.setReqId(reqId);
        bean.setPose(pose);
        bean.setTime(time);
        bean.setCoordinateDeviation(coordinateDeviation);
        bean.setLinearSpeed(linearSpeed);
        bean.setRoadMode(roadMode);
        bean.setNeedEstimate(isNeedEstimate);
        bean.setIsAdjustAngle(isAdjustAngle);
        return startAction(reqId, Definition.ACTION_GO_POSITION, mGson.toJson(bean), listener);
    }

    /**
     * 停止导航
     * <p> 停止使用pose点开始的导航
     *
     * @param reqId
     * @return
     * @paltform meissa, mini, saiph, max
     */
    public int stopPoseNavigation(int reqId) {
        return stopAction(reqId, Definition.ACTION_POSE_NAVIGATION, true);
    }

    /**
     * 停止导航
     * <p> 停止使用目的地名称开始的导航
     *
     * @param reqId
     * @return
     * @paltform meissa, mini, saiph, max
     */
    public int stopNavigation(int reqId) {
        stopAction(reqId, Definition.ACTION_GO_POSITION, true);
        return stopAction(reqId, Definition.ACTION_NAVIGATION, true, false);
    }

    /**
     * 停止导航
     * <p> 停止使用目的地名称开始的导航
     *
     * @param reqId
     * @return
     * @paltform meissa, mini, saiph, max
     */
    public int stopNavigation(int reqId, boolean isCancelStopCommand) {
        stopAction(reqId, Definition.ACTION_GO_POSITION, true);
        return stopAction(reqId, Definition.ACTION_NAVIGATION, true, isCancelStopCommand);
    }


    /**
     * 开始巡逻.
     * <p>该巡逻方式可以配置选择在某个点位是否停顿，如若dockingPoints为null，巡逻则是顺滑的
     * <br>默认线速度：0.7f, 默认角速度：1.2f
     *
     * @param reqId
     * @param route         巡逻路线
     * @param startPoint    开始巡逻点位index，index<route.size
     * @param dockingPoints 在哪些点位需要停顿
     * @param listener      回调函数
     * @return api执行
     */
    public int startCruise(int reqId, List<Pose> route, int startPoint,
                           List<Integer> dockingPoints, ActionListener listener) {
        return startCruise(reqId, route, startPoint, dockingPoints, SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED, SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED, listener);
    }

    /**
     * 开始巡逻.
     * <p>该巡逻方式可以配置选择在某个点位是否停顿，如若dockingPoints为null，巡逻则是顺滑的
     * <br>默认线速度：0.7f, 默认角速度：1.2f. 停止巡逻使用{@link #stopCruise(int)}
     *
     * @param reqId
     * @param route         巡逻路线
     * @param startPoint    开始巡逻点位index，index<route.size
     * @param dockingPoints 在哪些点位需要停顿
     * @param linearSpeed   巡逻线速度
     * @param angularSpeed  巡逻角速度
     * @param listener      回调函数
     * @return api执行
     */
    public int startCruise(int reqId, List<Pose> route, int startPoint,
                           List<Integer> dockingPoints,
                           double linearSpeed, double angularSpeed,
                           ActionListener listener) {

        return startCruise(reqId, route, startPoint, dockingPoints, linearSpeed, angularSpeed, 0L, listener);
    }

    /**
     * 开始巡逻.
     * <p>该巡逻方式可以配置选择在某个点位是否停顿，如若dockingPoints为null，巡逻则是顺滑的
     * <br>默认线速度：0.7f, 默认角速度：1.2f。停止巡逻使用{@link #stopCruise(int)}
     *
     * @param reqId
     * @param route         巡逻路线
     * @param startPoint    开始巡逻点位index，index<route.size
     * @param dockingPoints 在哪些点位需要停顿
     * @param linearSpeed   巡逻线速度
     * @param angularSpeed  巡逻角速度
     * @param multiTimeout  巡逻途中遭遇多级等待多长时间退出巡逻
     * @param listener      回调函数
     * @return api执行
     */
    public int startCruise(int reqId, List<Pose> route, int startPoint,
                           List<Integer> dockingPoints,
                           double linearSpeed, double angularSpeed,
                           long multiTimeout, ActionListener listener) {
        CruiseParams params = new CruiseParams();
        params.setReqId(reqId);
        params.setDockingPoints(dockingPoints);
        params.setRoute(route);
        params.setStartPoint(startPoint);
        params.setLinearSpeed(linearSpeed);
        params.setAngularSpeed(angularSpeed);
        params.setMultipleWaitTime(multiTimeout);
        return startAction(reqId, Definition.ACTION_CRUISE, mGson.toJson(params), listener);
    }


    /**
     * 停止巡逻
     *
     * @param reqId
     * @return
     */
    public int stopCruise(int reqId) {
        return stopAction(reqId, Definition.ACTION_CRUISE, true);
    }

    /**
     * 开始注册
     * <p> 停止注册使用{@link #stopRegister(int)}
     *
     * @param reqId       id
     * @param personName  name
     * @param timeout     How long does it think the registration has failed
     * @param tryCount    times collecting pictures in total ; if you don't need to set this ,set
     *                    default 1;
     * @param secondDelay long delay to second getPictureById when getting Picture failed first.
     * @param listener    listener
     * @return
     */
    public int startRegister(int reqId, @NonNull String personName, long timeout, int tryCount, long
            secondDelay, String welcomeContent, ActionListener listener) {
        Log.d(TAG, "startRegister reqID : " + reqId + ", personName : " + personName);
        RegisterBean bean = new RegisterBean();
        bean.setReqId(reqId);
        bean.setPersonName(personName);
        bean.setTime(timeout);
        bean.setCount(tryCount);
        bean.setSecondDelay(secondDelay);
        bean.setRemoteType(1);
        bean.setWelcomeContent(welcomeContent);
        String jsonStr = mGson.toJson(bean);

        return startAction(reqId, Definition.ACTION_REGISTER, jsonStr, listener);
    }

    /**
     * 检测识别当前人脸
     * <p>停止检测动作使用{@link #stopRegister(int)}
     *
     * @param reqId       id
     * @param timeout     How long does it think the registration has failed
     * @param tryCount    times collecting pictures in total ; if you don't need to set this ,set
     *                    default 1;
     * @param secondDelay long delay to second getPictureById when getting Picture failed first.
     * @param listener    listener
     * @return
     */
    public int startDetect(int reqId, long timeout, int tryCount, long
            secondDelay, ActionListener listener) {
        Log.d(TAG, "startDetect reqId : " + reqId + ", timeout : " + timeout
                + ", tryCount : " + tryCount + ", secondDelay : " + secondDelay + ", ActionListener : " + listener);
        RegisterBean bean = new RegisterBean();
        bean.setReqId(reqId);
        bean.setPersonName("");
        bean.setTime(timeout);
        bean.setCount(tryCount);
        bean.setSecondDelay(secondDelay);
        bean.setRemoteType(2);
        bean.setWelcomeContent("");
        String jsonStr = mGson.toJson(bean);

        return startAction(reqId, Definition.ACTION_REGISTER, jsonStr, listener);
    }

    /**
     * 停止注册
     *
     * @param reqId
     * @return
     */
    public int stopRegister(int reqId) {
        return stopAction(reqId, Definition.ACTION_REGISTER, true);
    }

    /**
     * 取消注册
     *
     * @param reqId
     * @param name     people name
     * @param listener listener
     * @return
     */
    public int startUnRegister(int reqId, String name, ActionListener listener) {
        UnRegisterBean bean = new UnRegisterBean();
        bean.setReqId(reqId);
        bean.setName(name);
        String jsonStr = mGson.toJson(bean);

        return startAction(reqId, Definition.ACTION_UNREGISTER, jsonStr, listener);
    }

    /**
     * 机器人唤醒.
     * <p>当呼唤机器人名字时，调用此api机器人可转身到声源方向
     * <br>可以通过{@link #stopWakeUp(int)}停止唤醒
     *
     * @param reqId
     * @param angle 唤醒角度，可以通过语音回调拿到这个角度
     * @return
     */
    public int wakeUp(int reqId, float angle, ActionListener listener) {
        return wakeUp(reqId, angle, true, listener);
    }

    /**
     * 机器人唤醒.
     * <p>当呼唤机器人名字时，调用此api机器人可转身到声源方向
     * <br>可以通过{@link #stopWakeUp(int)}停止唤醒
     *
     * @param reqId
     * @param angle    声源角度
     * @param moveHead 是否转动头部
     * @param listener
     * @return
     */
    public int wakeUp(int reqId, float angle, boolean moveHead, ActionListener listener) {
        WakeUpBean bean = new WakeUpBean();
        bean.setReqId(reqId);
        bean.setAngle(angle);
        bean.setNeedMoveHead(moveHead);
        String jsonStr = mGson.toJson(bean);

        return startAction(reqId, Definition.ACTION_WAKEUP, jsonStr, listener);
    }

    /**
     * 停止唤醒机器人
     *
     * @param reqId
     * @return
     */
    public int stopWakeUp(int reqId) {
        return stopAction(reqId, Definition.ACTION_WAKEUP, true);
    }

    /**
     * 开始找人.
     * 使用{@link #stopSearchPerson(int)}停止找人
     *
     * @param reqId
     * @param cameraType front camera is 1, back camera is 2
     * @param personName person name
     * @return
     * @deprecated
     */
    public int startSearchPerson(int reqId, int cameraType,
                                 String personName, long timeout, ActionListener listener) {
        SearchPersonBean bean = new SearchPersonBean();
        bean.setReqId(reqId);
        bean.setCameraType(cameraType);
        bean.setPersonName(personName);
        bean.setSearchTimeout(timeout);
        String jsonStr = mGson.toJson(bean);

        return startAction(reqId, Definition.ACTION_SEARCH_TARGET, jsonStr, listener);
    }

    /**
     * 停止找人
     *
     * @param reqId
     * @return
     * @deprecated
     */
    public int stopSearchPerson(int reqId) {
        return stopAction(reqId, Definition.ACTION_SEARCH_TARGET, true);
    }

    /**
     * 获取摄像头前方所有人员信息.
     * <p>新版api中使用{@link PersonApi}替代
     * <br>使用{@link #stopWakeUp(int)}停止获取人脸信息
     *
     * @param reqId
     * @param listener 人员信息回调
     * @return
     */
    @Deprecated
    public int startGetAllPersonInfo(final int reqId, PersonInfoListener listener) {
        return startGetAllPersonInfo(reqId, listener, false);
    }

    /**
     * 获取摄像头前方所有人员信息.
     * <p>新版api中使用{@link PersonApi}替代
     * <br>使用{@link #stopWakeUp(int)}停止获取人脸信息
     *
     * @param reqId
     * @param listener            人员信息回调函数
     * @param isNeedInvalidPerson 是否需要无效人员信息（无效人员指距离机器人较远，或者摄像头边缘无法看清的人员）
     * @return
     */
    @Deprecated
    public int startGetAllPersonInfo(final int reqId, PersonInfoListener listener,
                                     boolean isNeedInvalidPerson) {
        synchronized (mAllPersonInfoListener) {
            if (!mAllPersonInfoListener.contains(listener)) {
                mAllPersonInfoListener.add(listener);
            }
        }
        String params;
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_LOOPER, Definition.JSON_HEAD_CONTINUOUS);
            param.put(Definition.JSON_HEAD_IS_NEED_INVALID_PERSON, isNeedInvalidPerson ?
                    Definition.JSON_HEAD_NEED_INVALID_PERSON :
                    Definition.JSON_HEAD_NOT_NEED_INVALID_PERSON);
            params = param.toString();
        } catch (JSONException e) {
            e.printStackTrace();
            return ERROR_REMOTE_VISITOR;
        }
        CommandBean bean = new CommandBean();
        bean.setCmdType(Definition.CMD_HEAD_GET_ALL_PERSON_INFOS);
        bean.setReqId(reqId);
        bean.setParams(params);
        bean.setContinue(true);
        final String jsonParams = mGson.toJson(bean);

        final Type type = new TypeToken<List<Person>>() {
        }.getType();

        if (mPersonListener != null) {
            return startAction(reqId,
                    Definition.ACTION_HEAD_GET_ALL_PERSON_INFOS, jsonParams, mPersonListener);
        }

        mPersonListener = new ActionListener() {
            @Override
            public void onResult(int status, String responseString) throws RemoteException {
                if (status == Definition.ACTION_RESPONSE_ALREADY_RUN) {
                    return;
                }
                synchronized (mAllPersonInfoListener) {
                    for (PersonInfoListener p : mAllPersonInfoListener) {
                        p.onResult(status, responseString);
                    }
                }
                super.onResult(status, responseString);
            }

            @Override
            public void onError(int errorCode, String errorString) throws RemoteException {
                Log.e(TAG, "Get all person onError errorCode: " + errorCode
                        + ", errorString: " + errorString);
                if (errorCode == Definition.ERROR_LISTENER_INVALID
                        && mAllPersonInfoListener.size() > 0) {
                    Log.d(TAG, "Get all person restart");
                    startAction(reqId,
                            Definition.ACTION_HEAD_GET_ALL_PERSON_INFOS, jsonParams,
                            mPersonListener);
                }
            }

            @Override
            public void onStatusUpdate(int status, String data) throws RemoteException {
                //Log.d("RobotApi", "Get all person info : " + data);
                List<Person> result;
                try {
                    result = mGson.fromJson(data, type);
                } catch (Exception e) {
                    result = new ArrayList<>();
                }

                // add timestamp
                List<Person> personList = new ArrayList<>();
                if (result != null) {
                    for (Person person : result) {
                        person.setTimestamp(System.currentTimeMillis());
                        personList.add(person);
                    }
                }

                synchronized (mAllPersonInfoListener) {
                    for (PersonInfoListener p : mAllPersonInfoListener) {
                        p.onData(status, personList);
                    }
                }
                super.onStatusUpdate(status, data);
            }
        };
        return startAction(reqId, Definition.ACTION_HEAD_GET_ALL_PERSON_INFOS,
                jsonParams, mPersonListener);
    }

    /**
     * 停止获取人脸信息
     *
     * @param reqId
     * @param listener
     * @return
     */
    @Deprecated
    public int stopGetAllPersonInfo(int reqId, PersonInfoListener listener) {
        Log.d("RobotApi", "Stop all person info : " + mAllPersonInfoListener.size());
        synchronized (mAllPersonInfoListener) {
            if (mAllPersonInfoListener.contains(listener)) {
                mAllPersonInfoListener.remove(listener);
            }

            if (mAllPersonInfoListener.size() > 0) {
                return mAllPersonInfoListener.size();
            }
        }

        Log.d("RobotApi", "Stop all person info ###");
        return stopAction(reqId,
                Definition.ACTION_HEAD_GET_ALL_PERSON_INFOS, true);

    }

    private int stopAction(int reqId, int actionId, boolean isResetHW) {
        try {

            Map<String, Object> param = new HashMap<>(3);
            param.put("reqId", reqId);
            param.put("isResetHW", isResetHW);
            param.put("stopAction", true);
            logApi(actionId, getParamJsonObjectString(param));

            if (mModuleRegistry != null) {
                return mModuleRegistry.stopAction(reqId, actionId, isResetHW);
            }

            Log.d(TAG, "Stop action failed : " + actionId);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    private int stopAction(int reqId, int actionId, boolean isResetHW, boolean isCancelStopCommand) {
        try {

            Map<String, Object> param = new HashMap<>(3);
            param.put("reqId", reqId);
            param.put("isResetHW", isResetHW);
            param.put("stopAction", true);
            logApi(actionId, getParamJsonObjectString(param));

            if (mModuleRegistry != null) {
                return mModuleRegistry.stopActionCancelCmd(reqId, actionId, isResetHW, isCancelStopCommand);
            }

            Log.d(TAG, "Stop action failed : " + actionId);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    private void logApi(int actionId, String params) {
        try {
            OrionBase.logApi(OrionBase.API_TYPE_ACTION, String.valueOf(actionId), params);
        } catch (IllegalStateException e) {
            e.printStackTrace();
            Log.d(TAG, "OrionBase not initialize");
        }
    }

    /********************************  command   ***********************************/

    /**
     * 上报执行状态
     *
     * @param reqId
     * @param ifParsedSucc
     * @return
     * @deprecated
     */
    public boolean finishModuleParser(int reqId, boolean ifParsedSucc) {
        if (mModuleRegistry == null) {
            return false;
        }

        try {
            return mModuleRegistry.finishModuleParser(reqId, ifParsedSucc);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 上报执行状态
     *
     * @param reqId
     * @param ifParsedSucc
     * @param response
     * @return
     */
    public boolean finishModuleParser(int reqId, boolean ifParsedSucc, String response) {
        if (mModuleRegistry == null) {
            return false;
        }
        try {
            return mModuleRegistry.finishModuleWithResponse(reqId, ifParsedSucc, response);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return false;
    }

    //------------------------------ navigation apis ------------------------------------//

    /**
     * 后退指令
     *
     * @param reqId
     * @param speed    前进速度
     * @param listener 指令回调
     * @return api执行结果
     */
    public int goBackward(int reqId, float speed, CommandListener listener) {
        return goBackward(reqId, speed, Float.MAX_VALUE, listener);
    }

    /**
     * 后退指令.
     * <p>可以指定后退速度，后退距离
     *
     * @param reqId
     * @param speed    后退速度
     * @param distance 后退距离
     * @param listener 指令回调
     * @return api执行结果
     */
    public int goBackward(int reqId, float speed, float distance, CommandListener listener) {
        return motionLine(reqId, Definition.CMD_NAVI_MOVE_SUB_BACKWARD, speed, distance, listener);
    }

    /**
     * 前进指令.
     *
     * @param reqId
     * @param speed    前进速度
     * @param listener 指令回调
     * @return api执行结果
     */
    public int goForward(int reqId, float speed, CommandListener listener) {
        return goForward(reqId, speed, Float.MAX_VALUE, listener);
    }

    /**
     * 前进指令.
     * 可以指定前进速度和距离
     *
     * @param reqId
     * @param speed    前进速度
     * @param distance 前进距离
     * @param listener 指令回调
     * @return api执行结果
     */
    public int goForward(int reqId, float speed, float distance, CommandListener listener) {
        return goForward(reqId, speed, distance, false, listener);
    }

    /**
     * 前进指令.
     * 可以指定前进距离，速度，是否避障
     *
     * @param reqId
     * @param speed    前进速度
     * @param distance 前进距离
     * @param avoid    是否要避障
     * @param listener 指令回调
     * @return api执行结果
     */
    public int goForward(int reqId, float speed, float distance, boolean avoid,
                         CommandListener listener) {
        return motionLine(reqId, Definition.CMD_NAVI_MOVE_SUB_FORWARD, speed, distance, avoid,
                listener);
    }

    /**
     * 左转指令.
     *
     * @param reqId
     * @param speed    转动速度
     * @param listener 回调函数
     * @return api执行结果
     */
    public int turnLeft(int reqId, float speed, CommandListener listener) {
        return turnLeft(reqId, speed, Float.MAX_VALUE, listener);
    }

    /**
     * 左转指令.
     *
     * @param reqId
     * @param speed    转动速度
     * @param angle    转动角度
     * @param listener 回调函数
     * @return api执行结果
     */
    public int turnLeft(int reqId, float speed, float angle, CommandListener listener) {
        return motionAngle(reqId, Definition.CMD_NAVI_MOVE_SUB_TURN_LEFT, speed, angle, listener);
    }

    /**
     * 右转指令.
     *
     * @param reqId
     * @param speed    转动速度
     * @param listener 回调函数
     * @return api执行结果
     */
    public int turnRight(int reqId, float speed, CommandListener listener) {
        return turnRight(reqId, speed, Float.MAX_VALUE, listener);
    }

    /**
     * 右转指令.
     *
     * @param reqId
     * @param speed    转动速度
     * @param angle    转动角度
     * @param listener 回调函数
     * @return api执行结果
     */
    public int turnRight(int reqId, float speed, float angle, CommandListener listener) {
        return motionAngle(reqId, Definition.CMD_NAVI_MOVE_SUB_TURN_RIGHT, speed, angle, listener);
    }

    /**
     * 向后转.
     *
     * @param reqId
     * @param speed    转动速度
     * @param listener 回调函数
     * @return api执行结果
     */
    public int turnBack(int reqId, float speed, CommandListener listener) {
        return motionAngle(reqId, Definition.CMD_NAVI_MOVE_SUB_TURN_BACK, speed, 180f, listener);
    }

    /**
     * 转圈指令.
     *
     * @param reqId
     * @param speed    　转圈速度
     * @param listener 回调函数
     * @return api执行结果
     */
    public int rotate(int reqId, float speed, CommandListener listener) {
        return motionAngle(reqId, Definition.CMD_NAVI_MOVE_SUB_ROTATE, speed, Float.MAX_VALUE,
                listener);
    }

    /**
     * 按角度转动，基础方法
     *
     * @param reqId
     * @param direction
     * @param speed
     * @param angle
     * @param listener
     * @return
     */
    private int motionAngle(int reqId, String direction, float speed, float angle,
                            CommandListener listener) {
        float distance;
        float radianspeed = (float) Math.toRadians(speed);
        if (angle == Float.MAX_VALUE) {
            distance = Float.MAX_VALUE;
        } else {
            distance = (float) Math.toRadians(angle);
        }
        return motionLine(reqId, direction, radianspeed, distance, listener);
    }

    private int motionLine(int reqId, String direction, float speed, float distance,
                           final CommandListener listener) {
        return motionLine(reqId, direction, speed, distance, false, listener);
    }

    private int motionLine(int reqId, String direction, float speed, float distance,
                           boolean forwardAvoid,
                           final CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_DERICTION, direction);
            param.put(Definition.JSON_NAVI_DISTANCE, distance);
            param.put(Definition.JSON_NAVI_LINEAR_SPEED, speed);
            param.put(Definition.JSON_NAVI_FORWARD_AVOID, forwardAvoid);

            CommandBean bean = new CommandBean();
            bean.setContinue(false);
            bean.setNeedAssistEstimate(false);
            bean.setParams(param.toString());
            bean.setCmdType(Definition.CMD_NAVI_MOVE_DIRECTION);

            String params = mGson.toJson(bean);

            ActionListener al = parseCommand(listener);
            return startAction(reqId, Definition.ACTION_NAVI_MOVE_DIRECTION, params, al);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    /**
     * 按角度转动，基础方法
     * @param reqId
     * @param direction 转动方向，-1表示左转，1表示右转
     * @param speed    转动速度，单位：度/秒
     * @param angle   转动角度，单位：度
     * @param listener
     * @return
     */
    public int rotateInPlace(int reqId, int direction, float speed, float angle,
                           final CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_DERICTION, direction);
            param.put(Definition.JSON_NAVI_DISTANCE, angle);
            param.put(Definition.JSON_NAVI_LINEAR_SPEED, speed);

            CommandBean bean = new CommandBean();
            bean.setContinue(false);
            bean.setParams(param.toString());
            bean.setCmdType(Definition.CMD_NAVI_ROTATE_IN_PLACE);

            String params = mGson.toJson(bean);

            ActionListener al = parseCommand(listener);
            return startAction(reqId, Definition.ACTION_NAVI_ROTATE_IN_PLACE, params, al);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    /**
     * 控制机器人移动.
     * <p> 此移动方法，支持移动避障，且判断在前方一米范围内有障碍物，停留3s不向前走
     * <br> 使用{@link #stopMove(int, CommandListener)}停止运动
     *
     * @param reqId
     * @param lineSpeed    线速度
     * @param angularSpeed 角速度
     * @param listener     回调函数
     * @return api是否成功执行
     */
    public int motionArc(int reqId, float lineSpeed, float angularSpeed, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_LINEAR_SPEED, lineSpeed);
            param.put(Definition.JSON_NAVI_ANGULAR_SPEED, angularSpeed);

            CommandBean bean = new CommandBean();
            bean.setContinue(false);
            bean.setCmdType(Definition.CMD_NAVI_MOVE_DIRECTION_ANGLE);
            bean.setParams(param.toString());

            return startAction(reqId, Definition.ACTION_NAVI_MOVE_DIRECTION_ANGLE,
                    mGson.toJson(bean), parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    /**
     * 控制机器人移动
     * <p> 此移动方法，支持移动避障，前方有障碍物时，停止前进
     * <br> 使用{@link #stopMove(int, CommandListener)}停止运动
     *
     * @param reqId
     * @param lineSpeed    线速度
     * @param angularSpeed 角速度
     * @param listener     回调函数
     * @return
     */
    public int motionArcWithObstacles(int reqId, float lineSpeed, float angularSpeed, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_LINEAR_SPEED, lineSpeed);
            param.put(Definition.JSON_NAVI_ANGULAR_SPEED, angularSpeed);

            CommandBean bean = new CommandBean();
            bean.setContinue(false);
            bean.setCmdType(Definition.CMD_NAVI_MOVE_DIRECTION_ANGLE_OBSTACLES);
            bean.setParams(param.toString());

            return startAction(reqId, Definition.ACTION_NAVI_MOVE_DIRECTION_ANGLE_OBSTACLES,
                    mGson.toJson(bean), parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    /**
     * 停止运动
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int stopMove(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_STOP_MOVE, "", false);
        return startAction(reqId, Definition.ACTION_NAVI_STOP_MOVE, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 设置当前点位名称.
     *
     * @param reqId
     * @param placeName 点位名称
     * @param listener  回调函数
     * @return
     */
    public int setLocation(int reqId, String placeName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_LOCATION, placeName, false);
        return startAction(reqId, Definition.ACTION_NAVI_SET_LOCATION, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 设置某个点位的名称
     *
     * @param reqId
     * @param param    点位名称json, {"px":1.2, "py":1.2, "theta":1.2, "name":"接待点"}，坐标值float类型
     * @param listener 回调函数
     * @return
     */
    public int setPoseLocation(int reqId, String param, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_POSE_LOCATION, param, false);
        return startAction(reqId, Definition.ACTION_NAVI_SET_POSE_LOCATION, mGson.toJson(bean),
                parseCommand(listener));
    }

    //工厂使用方法，获取自检状态
    public int getFullCheckStatus(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_FULL_CHECK_STATUS, "", false);
        return startAction(reqId, Definition.ACTION_GET_FULL_CHECK_STATUS, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 重定位方法.
     * <p>该定位方法要求精度较低，定位成功率较高，适合于手动重定位方式
     *
     * @param reqId
     * @param param    点位json, {"px":1.2, "py":1.2, "theta":1.2}, 坐标值float类型
     * @param listener
     * @return
     */
    public int setPoseEstimate(int reqId, String param, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_POSE_ESTIMATE, param, false);
        return startAction(reqId, Definition.ACTION_NAVI_SET_POSE_ESTIMATE, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 重定位方法.
     * <p>该定位方法要求精度高，在确定位置推荐使用此种方式，比如充电桩重定位
     *
     * @param reqId
     * @param param    点位json, {"px":1.2, "py":1.2, "theta":1.2}, 坐标值float类型
     * @param listener
     * @return
     */
    public int setFixedEstimate(int reqId, String param, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_FIXED_ESTIMATE, param, false);
        return startAction(reqId, Definition.ACTION_NAVI_SET_FIXED_ESTIMATE, mGson.toJson(bean),
                listener);
    }

    public int setForceEstimate(int reqId, String param, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_FORCE_ESTIMATE, param, false);
        return startAction(reqId, Definition.ACTION_NAVI_SET_FORCE_ESTIMATE, mGson.toJson(bean),
                listener);
    }

    /**
     * 设置导航参数.
     *
     * @param reqId
     * @param param    导航参数
     * @param listener 回调函数
     * @return api是否成功执行
     */
    public int setNavigationConfig(int reqId, String param, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_CONFIG, param, false);
        return startAction(reqId, Definition.ACTION_NAVI_SET_CONFIG, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 获取导航参数
     *
     * @param reqId
     * @param listener 回调函数
     * @return api是否成功执行
     */
    public int getNavigationConfig(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_CONFIG, "", false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_CONFIG, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 开始建图，旧接口不传任何参数将只针对旧版本设备生效。
     *
     * @param reqId
     * @param startCreateMapBean 地图信息
     * @param listener           回调函数
     * @return api是否成功执行
     * @platform all
     */
    public int startCreatingMap(int reqId, StartCreateMapBean startCreateMapBean, CommandListener listener) {
        if (startCreateMapBean == null) {
            return Definition.CMD_SEND_ERROR_PARAMETERS_NULL;
        }
        String jsonStr = mGson.toJson(startCreateMapBean);
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_START_CREATING_MAP, jsonStr, false);
        return startAction(reqId, Definition.ACTION_NAVI_START_CREATING_MAP, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 停止建图
     *
     * @param reqId
     * @param mapName  地图名称
     * @param listener 回调函数
     * @return
     */
    public int stopCreatingMap(int reqId, String mapName, CommandListener listener) {
        StopCreateMapBean bean = new StopCreateMapBean();
        bean.setReqId(reqId);
        bean.setMapName(mapName);
        String jsonStr = mGson.toJson(bean);
        return startAction(reqId, Definition.ACTION_NAVI_STOP_CREATING_MAP, jsonStr,
                parseCommand(listener));
    }

    /**
     * 停止建图
     *
     * @param reqId
     * @param stopCreateMapBean 地图信息：名称，语言，类型，完成状态：1；高级地图：2
     * @param listener
     * @return
     */
    public int stopCreatingMap(int reqId, StopCreateMapBean stopCreateMapBean, CommandListener listener) {
        String jsonStr = mGson.toJson(stopCreateMapBean);
        return startAction(reqId, Definition.ACTION_NAVI_STOP_CREATING_MAP, jsonStr,
                parseCommand(listener));
    }

    /**
     * 切换地图
     *
     * @param reqId
     * @param mapName  地图名称
     * @param listener 回调函数
     * @return
     */
    public int switchMap(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SWITCH_MAP, mapName, false);
        return startAction(reqId, Definition.ACTION_NAVI_SWITCH_MAP, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 重新加载地图
     */
    public int loadCurrentMap(int reqId, CommandListener listener) {
        return loadCurrentMap(reqId, false, false, listener);
    }

    /**
     * 重新加载地图
     *
     * @param keepPose 重新load后，是否信任之前点
     */
    public int loadCurrentMap(int reqId, boolean keepPose, CommandListener listener) {
        return loadCurrentMap(reqId, true, keepPose, listener);
    }

    private int loadCurrentMap(int reqId, boolean useCustomKeepPose, boolean keepPose, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_LOAD_CURRENT_MAP,
                mGson.toJson(new LoadMapBean(useCustomKeepPose, keepPose)), false);
        return startAction(reqId, Definition.ACTION_NAVI_LOAD_CURRENT_MAP,
                mGson.toJson(bean), listener);
    }

    /**
     * 删除地图
     *
     * @param reqId
     * @param mapName  地图名称
     * @param listener 回调函数
     * @return
     */
    public int removeMap(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_REMOVE_MAP, mapName, false);
        return startAction(reqId, Definition.ACTION_NAVI_REMOVE_MAP, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 刷新保存地图md5值
     *
     * @param reqId
     * @param mapName  地图名称
     * @param listener 回调函数
     * @return
     */
    @Deprecated
    public int refreshMd5(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_REFRESH_MD5, mapName, false);
        return startAction(reqId, Definition.ACTION_NAVI_REFRESH_MD5, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 删除点位
     *
     * @param reqId
     * @param positionName 点位名称
     * @param listener     回调函数
     * @return
     */
    @Deprecated
    public int removeLocation(int reqId, String positionName, CommandListener listener) {
        return 0;
    }

    /**
     * 获取所有点位列表
     *
     * @param reqId
     * @param listener 回调函数
     * @return
     */
    public int getPlaceList(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_PLACE_LIST, "", false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_PLACE_LIST, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * @param reqId
     * @param listener
     * @return
     * @TODO 和getplacelist功能一样，需要删除，navigationService和maptool的都删除调
     */
    public int getPlaceListWithName(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_PLACE_LIST_WITH_NAME, "", false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_PLACE_LIST_WITH_NAME, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 获取某个地图的所有点位
     *
     * @param reqId
     * @param mapName  地图名称
     * @param listener 回调函数
     * @return
     */
    public int getPlaceListByMapName(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_PLACELIST_BY_MAPNAME, mapName, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_PLACELIST_BY_MAPNAME, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * @param reqId
     * @param param
     * @param listener
     * @return
     */
    public int getPlaceListWithNameList(int reqId, String param, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_PLACELIST_WITH_NAMELIST, param, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_PLACELIST_WITH_NAMELIST, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * @param reqId    请求码
     * @param typeId   获取特殊点位的类型值
     * @param listener 监听回调
     */
    public int getPlacesByType(int reqId, int typeId, CommandListener listener) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(Definition.JSON_NAVI_TYPE_ID, typeId);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_PLACELIST_BY_TYPE, jsonObject.toString(), false);
            return startAction(reqId, Definition.ACTION_NAVI_GET_PLACELIST_BY_TYPE, mGson.toJson(bean),
                    parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int getInternationalPlaceList(int reqId, String mapName, CommandListener listener) {
        return getInternationalPlaceList(reqId, mapName, false, listener);
    }

    /**
     * 获取某张地图下的所有点位
     * <p>包含各种语言的所有点位
     *
     * @param reqId
     * @param mapName  地图名称
     * @param printLog 是否打印日志
     * @param listener 回调函数
     * @return
     */
    public int getInternationalPlaceList(int reqId, String mapName, boolean printLog, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_MAP_NAME, mapName);
            param.put(Definition.JSON_PRINT_LOG, printLog);

            CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_INTERNATIONAL_PLACE_LIST, param.toString(), false);
            return startAction(reqId, Definition.ACTION_NAVI_GET_INTERNATIONAL_PLACE_LIST, mGson.toJson(bean),
                    parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获取某个定位点的点坐标
     *
     * @param reqId
     * @param positionName 点位名称
     * @param listener     回调函数
     * @return
     */
    public int getPlace(int reqId, String positionName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_PLACE_NAME, positionName, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_PLACE_NAME, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 导航到某个地方
     *
     * @param reqId
     * @param postionName   点位名称
     * @param isAdjustAngle 是否适应导航结束时朝向的角度，默认 false:即归正目标点设定角度
     * @param listener      回调函数
     * @return
     */
    @Deprecated
    public int goPosition(int reqId, String postionName, boolean isAdjustAngle, CommandListener listener) {
        return goPosition(reqId, postionName, null, isAdjustAngle, listener);
    }

    /**
     * 导航到某个地方
     *
     * @param reqId
     * @param position 点位名称
     * @param velocity 速度
     * @param listener 回调函数
     * @return
     */
    @Deprecated
    public int goPosition(int reqId, String position, String velocity, CommandListener listener) {

        return goPosition(reqId, position, velocity, false, listener);
    }

    /**
     * 导航到某个地方
     *
     * @param reqId
     * @param position      目的地名称
     * @param velocity      速度
     * @param isAdjustAngle 是否适应导航结束时朝向的角度，默认 false:即归正目标点设定角度
     * @param listener      回调函数
     * @return
     */
    @Deprecated
    public int goPosition(int reqId, String position, String velocity, boolean isAdjustAngle, CommandListener listener) {

        return goPosition(reqId, position, velocity, isAdjustAngle, 0.0D, listener);
    }

    /**
     * 导航到某个地方
     *
     * @param reqId
     * @param position         目的地名称
     * @param velocity         速度
     * @param isAdjustAngle    是否适应导航结束时朝向的角度，默认 false:即归正目标点设定角度
     * @param destinationRange 目的地为中心，多大范围内可认为已到达
     * @param listener         回调函数
     * @return
     */
    @Deprecated
    public int goPosition(int reqId, String position, String velocity, boolean isAdjustAngle,
                          double destinationRange, CommandListener listener) {
        return goPosition(reqId, position, velocity, isAdjustAngle, destinationRange, 0, listener);

    }

    /**
     * 导航到某个地方
     *
     * @param reqId
     * @param position         目的地名称
     * @param velocity         速度
     * @param isAdjustAngle    是否适应导航结束时朝向的角度，默认 false:即归正目标点设定角度
     * @param destinationRange 目的地为中心，多大范围内可认为已到达
     * @param listener         回调函数
     * @return
     */
    @Deprecated
    public int goPosition(int reqId, String position, String velocity, boolean isAdjustAngle,
                          double destinationRange, int priority, CommandListener listener) {
        String params = null;
        if (TextUtils.isEmpty(position)) {
            params = null;
        } else {
            try {
                JSONObject positionJson = new JSONObject(position);
                double linearSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED;
                double angularSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED;
                if (!TextUtils.isEmpty(velocity)) {
                    JSONObject velocityJson = new JSONObject(velocity);
                    linearSpeed = velocityJson.has(Definition.JSON_NAVI_LINEAR_SPEED) ? velocityJson.optDouble(Definition.JSON_NAVI_LINEAR_SPEED) : SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED;
                    angularSpeed = velocityJson.has(Definition.JSON_NAVI_ANGULAR_SPEED) ? velocityJson.optDouble(Definition.JSON_NAVI_ANGULAR_SPEED) : SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED;
                }
                positionJson.put(Definition.JSON_NAVI_LINEAR_SPEED, linearSpeed);
                positionJson.put(Definition.JSON_NAVI_ANGULAR_SPEED, angularSpeed);
                positionJson.put(Definition.JSON_NAVI_ISADJUST_ANGLE, isAdjustAngle);
                positionJson.put(Definition.JSON_NAVI_DESTINATION_RANGE, destinationRange);
                positionJson.put(Definition.JSON_NAVI_TASK_PRIORITY, priority);
                params = positionJson.toString();
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GO_POSITION, params, false);
        return startAction(reqId, Definition.ACTION_NAVI_GO_POSITION, mGson.toJson(bean),
                parseCommand(listener));
    }

//    /**
//     * 特殊点位根据类型和优先级导航到某个地方
//     *
//     * @param reqId         请求id
//     * @param typeId        特殊点位类型
//     * @param priority      特殊点位优先级
//     * @param isAdjustAngle 是否适应导航结束时朝向的角度，默认 false:即归正目标点设定角度
//     * @param listener      回调函数
//     */
//    public int goPositionByType(int reqId, int typeId, int priority, boolean isAdjustAngle, CommandListener listener) {
//        return goPositionByType(reqId, typeId, priority, null, isAdjustAngle, listener);
//    }
//
//    /**
//     * 特殊点位根据类型和优先级导航到某个地方
//     *
//     * @param reqId    请求id
//     * @param typeId   特殊点位类型
//     * @param priority 特殊点位优先级
//     * @param velocity 速度
//     * @param listener 回调函数
//     */
//    public int goPositionByType(int reqId, int typeId, int priority, String velocity, CommandListener listener) {
//        return goPositionByType(reqId, typeId, priority, velocity, false, listener);
//    }
//
//    /**
//     * 特殊点位根据类型和优先级导航到某个地方
//     *
//     * @param reqId         请求id
//     * @param typeId        特殊点位类型
//     * @param priority      特殊点位优先级
//     * @param velocity      速度
//     * @param isAdjustAngle 是否适应导航结束时朝向的角度，默认 false:即归正目标点设定角度
//     * @param listener      回调函数
//     */
//    public int goPositionByType(int reqId, int typeId, int priority, String velocity, boolean isAdjustAngle, CommandListener listener) {
//        return goPositionByType(reqId, typeId, priority, velocity, isAdjustAngle, 0.0D, listener);
//    }
//
//    /**
//     * 特殊点位根据类型和优先级导航到某个地方
//     *
//     * @param reqId            请求id
//     * @param typeId           特殊点位类型
//     * @param priority         特殊点位优先级
//     * @param velocity         速度
//     * @param isAdjustAngle    是否适应导航结束时朝向的角度，默认 false:即归正目标点设定角度
//     * @param destinationRange 目的地为中心，多大范围内可认为已到达
//     * @param listener         回调函数
//     */
//    public int goPositionByType(int reqId, int typeId, int priority, String velocity, boolean isAdjustAngle,
//                                double destinationRange, CommandListener listener) {
//        return goPositionByType(reqId, typeId, priority, velocity, isAdjustAngle, destinationRange, 0, listener);
//    }
//
//    /**
//     * 特殊点位根据类型和优先级导航到某个地方
//     *
//     * @param reqId            请求id
//     * @param typeId           特殊点位类型
//     * @param priority         特殊点位优先级
//     * @param velocity         速度
//     * @param isAdjustAngle    是否适应导航结束时朝向的角度，默认 false:即归正目标点设定角度
//     * @param destinationRange 目的地为中心，多大范围内可认为已到达
//     * @param taskPriority     任务优先级
//     * @param listener         回调函数
//     */
//    public int goPositionByType(int reqId, int typeId, int priority, String velocity, boolean isAdjustAngle,
//                                double destinationRange, int taskPriority, CommandListener listener) {
//        String params = null;
//        try {
//            JSONObject positionJson = new JSONObject();
//            positionJson.put(Definition.JSON_NAVI_TYPE_ID, typeId);
//            positionJson.put(Definition.JSON_NAVI_PRIORITY, priority);
//            double linearSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED;
//            double angularSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED;
//            if (!TextUtils.isEmpty(velocity)) {
//                JSONObject velocityJson = new JSONObject(velocity);
//                linearSpeed = velocityJson.has(Definition.JSON_NAVI_LINEAR_SPEED) ? velocityJson.optDouble(Definition.JSON_NAVI_LINEAR_SPEED) : SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED;
//                angularSpeed = velocityJson.has(Definition.JSON_NAVI_ANGULAR_SPEED) ? velocityJson.optDouble(Definition.JSON_NAVI_ANGULAR_SPEED) : SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED;
//            }
//            positionJson.put(Definition.JSON_NAVI_LINEAR_SPEED, linearSpeed);
//            positionJson.put(Definition.JSON_NAVI_ANGULAR_SPEED, angularSpeed);
//            positionJson.put(Definition.JSON_NAVI_ISADJUST_ANGLE, isAdjustAngle);
//            positionJson.put(Definition.JSON_NAVI_DESTINATION_RANGE, destinationRange);
//            positionJson.put(Definition.JSON_NAVI_TASK_PRIORITY, taskPriority);
//            params = positionJson.toString();
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//
//        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GO_POSITION_BY_TYPE, params, false);
//        return startAction(reqId, Definition.ACTION_NAVI_GO_POSITION_BY_TYPE, mGson.toJson(bean),
//                parseCommand(listener));
//    }


    /**
     * 旋转到目标点朝向
     *
     * @param reqId
     * @param position     目标点位置
     * @param linearSpeed  线速度，可为0，会有默认速度
     * @param angularSpeed 角速度，可为0，会有默认速度
     * @param turnLeft     控制左转(true)还是右转(false)到目标点朝向，如果转动小于30度，将自动选择最佳转动方向
     * @param listener     回调函数
     * @return
     */
    public int turnToTargetDirection(int reqId, Pose position, double linearSpeed, double angularSpeed
            , boolean turnLeft, CommandListener listener) {
        String params = null;
        if (position == null) {
            params = null;
        } else {
            try {
                JSONObject positionJson = new JSONObject();
                positionJson.put(Definition.JSON_NAVI_POSITION_X, position.getX());
                positionJson.put(Definition.JSON_NAVI_POSITION_Y, position.getY());
                positionJson.put(Definition.JSON_NAVI_POSITION_THETA, position.getTheta());
                positionJson.put(Definition.JSON_NAVI_LINEAR_SPEED, linearSpeed == 0 ?
                        SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED : linearSpeed);
                positionJson.put(Definition.JSON_NAVI_ANGULAR_SPEED, angularSpeed == 0 ?
                        SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED : angularSpeed);
                positionJson.put(Definition.JSON_NAVI_LEFT_OR_RIGHT, turnLeft);
                params = positionJson.toString();
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_TURN_BY_NAVIGATION, params, false);
        return startAction(reqId, Definition.ACTION_NAVI_TURN_BY_NAVIGATION, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 停止旋转到目标点朝向
     *
     * @param reqId
     * @return
     */
    public int stopTurnToTargetDirection(int reqId) {
        return stopAction(reqId, Definition.ACTION_NAVI_TURN_BY_NAVIGATION, true);
    }

    public int goPosition(int reqId, String postion, CommandListener listener) {
        return goPosition(reqId, postion, null, listener);
    }

    public int goPosition(int reqId, String position,
                          double linearSpeed, double angularSpeed, CommandListener listener) {
        return goPosition(reqId, position, linearSpeed, angularSpeed,
                0.0D, listener);
    }

    /**
     * 导航到某个点位
     *
     * @param reqId
     * @param position         点位名称
     * @param linearSpeed      线速度
     * @param angularSpeed     角速度
     * @param destinationRange 目的地为中心，多大范围内可认为已到达
     * @param listener         回调函数
     * @return
     */
    public int goPosition(int reqId, String position,
                          double linearSpeed, double angularSpeed,
                          double destinationRange, CommandListener listener) {
        String params = null;
        if (TextUtils.isEmpty(position)) {
            params = null;
        } else {
            try {
                JSONObject positionJson = new JSONObject(position);
                positionJson.put(Definition.JSON_NAVI_LINEAR_SPEED, linearSpeed);
                positionJson.put(Definition.JSON_NAVI_ANGULAR_SPEED, angularSpeed);
                positionJson.put(Definition.JSON_NAVI_DESTINATION_RANGE, destinationRange);
                params = positionJson.toString();
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GO_POSITION, params, false);
        return startAction(reqId, Definition.ACTION_NAVI_GO_POSITION, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 停止导航
     *
     * @param reqId
     * @return
     */
    public int stopGoPosition(int reqId) {
        stopAction(reqId, Definition.ACTION_NAVI_GO_POSITION, true);
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_STOP_NAVIGATION, null, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), null);
    }

    /**
     * 获取某个位置点的pose坐标
     *
     * @param reqId
     * @param placeName 点位名称
     * @param listener  回调函数
     * @return
     */
    public int getLocation(int reqId, String placeName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_LOCATION_HIGH_FREQUENCY, placeName, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_LOCATION, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 机器人是否在某个地点范围
     *
     * @param reqId
     * @param param    地点信息，json格式{"place_id":1, "target_place_name":"接待点", "coordinate_deviation":0.5}
     *                 ，coordinate_deviation指地点范围，传值double类型
     * @param listener
     * @return
     */
    public int isRobotInlocations(int reqId, String param, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_IS_IN_LOCATION, param, false);
        return startAction(reqId, Definition.ACTION_NAVI_IS_IN_LOCATION, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 机器人是否已经定位
     *
     * @param reqId
     * @param listener 回调函数
     * @return
     */
    public int isRobotEstimate(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_IS_ESTIMATE, "", false);
        return startAction(reqId, Definition.ACTION_NAVI_IS_ESTIMATE, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 保存当前点为定位点
     *
     * @param reqId
     * @param listener
     * @return
     * @deprecated
     */
    public int saveRobotEstimate(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SAVE_ESTIMATE, "", false);
        return startAction(reqId, Definition.ACTION_NAVI_SAVE_ESTIMATE, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 导航能力是否已ready
     *
     * @return true：导航能力已ok
     */
    public boolean isNavigationReady() {
        //TODO
        return true;
    }

    /**
     * 是否处于导航状态
     *
     * @param reqId
     * @param listener 回调函数
     * @return
     */
    public int isInNavigation(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_IS_IN_NAVIGATION, null, false);
        return startAction(reqId, Definition.ACTION_NAVI_IS_IN_NAVIGATION, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 切换到充电模式.
     * <p>该模式下导航会放弃对轮子的控制权
     *
     * @param reqId
     * @return
     */
    public int switchChargeMode(int reqId) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SWITCH_AUTO_CHARGE_MODE, null,
                false);
        return startAction(reqId, Definition.ACTION_SWITCH_AUTO_CHARGE_MODE, mGson.toJson(bean),
                null);
    }

    /**
     * 旋转指定角度
     *
     * @param reqId
     * @param text
     * @param params   旋转角度json
     * @param listener
     * @return
     * @deprecated
     */
    //------------------------------ head apis --------------------------------------//
    public int navMoveRotation(int reqId, String text, String params, CommandListener listener) {
        String direction = null;
        try {
            JSONObject json = new JSONObject(params);
            float angle = Float.valueOf(json.get("angle").toString());
            if (angle > 0) {
                direction = Definition.CMD_NAVI_MOVE_SUB_TURN_RIGHT;
            } else {
                direction = Definition.CMD_NAVI_MOVE_SUB_TURN_LEFT;
            }
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_DERICTION, direction);
            param.put(Definition.JSON_NAVI_DISTANCE, (float) Math.toRadians(Math.abs(angle)));
            param.put(Definition.JSON_NAVI_LINEAR_SPEED, 0.8f);
            param.put(Definition.JSON_NAVI_NO_NEED_ACCELERATION, true);

            CommandBean bean = new CommandBean();
            bean.setContinue(false);
            bean.setParams(param.toString());
            bean.setCmdType(Definition.CMD_NAVI_MOVE_DIRECTION);

            String jsonParams = mGson.toJson(bean);

            ActionListener al = parseCommand(listener);
            return startAction(reqId, Definition.ACTION_NAVI_MOVE_DIRECTION, jsonParams, al);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    /**
     * 切换摄像头。
     * 此api仅适应于机器人前后均有摄像头机器
     *
     * @param reqId
     * @param mode     摄像头模式，前置模式：{@link Definition#JSON_HEAD_FORWARD}，后置模式：{@link Definition#JSON_HEAD_BACKWARD}
     * @param listener
     * @return
     */
    public int switchCamera(int reqId, String mode, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_LOCATION, mode);

            CommandBean bean = new CommandBean(Definition.CMD_HEAD_SWITCH_CAMERA, param.toString
                                                                                               (), false);
            return startAction(reqId, Definition.ACTION_HEAD_SWITCH_CAMERA, mGson.toJson(bean),
                    parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * @return
     * @TODO 上层不再使用，待删除
     */
    private int startRecordFaceData() {
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_RECORD_FACE_START, "", false);
        return startAction(0, Definition.ACTION_HEAD_RECORD_FACE_DATA, mGson.toJson(bean), null);
    }

    /**
     * @return
     * @TODO 上层不再使用，待删除
     */
    private int stopRecordFaceData() {
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_RECORD_FACE_STOP, "", false);
        return startAction(0, Definition.ACTION_HEAD_RECORD_FACE_DATA, mGson.toJson(bean), null);
    }

    /**
     * @return
     * @TODO 上层不再使用，待删除
     */
    private int enableAvatarFaceData() {
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_ENABLE_AVATAR_FACE, "", false);
        return startAction(0, Definition.ACTION_HEAD_AVATAR_FACE_DATA, mGson.toJson(bean), null);
    }

    /**
     * @return
     * @TODO 上层不再使用，待删除
     */
    private int disableAvatarFaceData() {
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_DISABLE_AVATAR_FACE, "", false);
        return startAction(0, Definition.ACTION_HEAD_AVATAR_FACE_DATA, mGson.toJson(bean), null);
    }

    /**
     * 头部归正
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int resetHead(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_RESET_HEAD, "", false);
        return startAction(reqId, Definition.ACTION_HEAD_RESET_HEAD, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 头部归正到某个角度
     *
     * @param reqId
     * @param text
     * @param params
     * @param listener
     * @return
     * @deprecated
     */
    public int resetHeadAngle(int reqId, String text, String params, CommandListener listener) {
        int vertical = 0;
        int horizontal = 0;
        try {
            JSONObject json = new JSONObject(params);
            vertical = json.optInt("vertical");
            horizontal = json.optInt("horizontal");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return moveHead(reqId, Definition.JSON_HEAD_ABSOLUTE, Definition.JSON_HEAD_ABSOLUTE,
                horizontal, vertical, listener);
    }

    /**
     * 头部转动到某个角度
     *
     * @param reqId
     * @param hmode    水平方向移动方式，相对角度方式：{@link Definition#JSON_HEAD_RELATIVE}；绝对角度方式：{@link Definition#JSON_HEAD_ABSOLUTE}
     * @param vmode    竖直方向移动方式，相对角度方式：{@link Definition#JSON_HEAD_RELATIVE}；绝对角度方式：{@link Definition#JSON_HEAD_ABSOLUTE}
     * @param hangle   水平方向移动角度
     * @param vangle   竖直方向移动角度
     * @param listener 回调函数
     * @return
     */
    public int moveHead(int reqId, String hmode, String vmode, int hangle, int vangle,
                        CommandListener listener) {
        HeadTurnBean bean = new HeadTurnBean();
        bean.setVerticalMode(Definition.JSON_HEAD_RELATIVE.equals(vmode) ? HeadTurnMode.relative : HeadTurnMode.absolute);
        bean.setHorizontalMode(Definition.JSON_HEAD_RELATIVE.equals(hmode) ? HeadTurnMode.relative : HeadTurnMode.absolute);
        bean.setVerticalAngle(vangle);
        bean.setHorizontalAngle(hangle);

        return turnHead(reqId, bean, listener);
    }

    /**
     * 头部转动
     *
     * @param reqId
     * @param hmode     水平方向移动方式，相对角度方式：{@link Definition#JSON_HEAD_RELATIVE}；绝对角度方式：{@link Definition#JSON_HEAD_ABSOLUTE}
     * @param vmode     竖直方向移动方式，相对角度方式：{@link Definition#JSON_HEAD_RELATIVE}；绝对角度方式：{@link Definition#JSON_HEAD_ABSOLUTE}
     * @param hangle    水平方向移动角度
     * @param vangle    竖直方向移动角度
     * @param hMaxSpeed 水平方向移动速度
     * @param vMaxSpeed 竖直方向移动速度
     * @param listener  回调函数
     * @return
     */
    public int moveHead(int reqId, String hmode, String vmode,
                        int hangle, int vangle, int hMaxSpeed, int vMaxSpeed, CommandListener
                                listener) {
        HeadTurnBean bean = new HeadTurnBean();
        bean.setVerticalMode(Definition.JSON_HEAD_RELATIVE.equals(vmode) ? HeadTurnMode.relative : HeadTurnMode.absolute);
        bean.setHorizontalMode(Definition.JSON_HEAD_RELATIVE.equals(hmode) ? HeadTurnMode.relative : HeadTurnMode.absolute);
        bean.setVerticalAngle(vangle);
        bean.setHorizontalAngle(hangle);
        bean.setHorizontalMaxSpeed(hMaxSpeed);
        bean.setVerticalMaxSpeed(vMaxSpeed);

        return turnHead(reqId, bean, listener);
    }

    /**
     * 设置默认头部角度 (俯仰角)
     *
     * @param hAngle   水平角度
     * @param vAngle   俯仰角度
     * @return
     */
    public boolean setDefaultHeadAngle(int hAngle, int vAngle) {
        if (mModuleRegistry == null) {
            Log.d(TAG, "Set default head angle failed, hAngle : " + hAngle + ", vAngle :  " + vAngle);
            return false;
        }
        try {
            mModuleRegistry.setDefaultHeadAngle(hAngle, vAngle);
            return true;
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return false;
    }

    public boolean clearDefaultHeadAngle() {
        return setDefaultHeadAngle(-1, -1);
    }

    /**
     * 头部转动
     *
     * @param reqId
     * @param bean     移动参数bean类
     * @param listener 回调函数
     * @return
     */
    public int turnHead(int reqId, HeadTurnBean bean, CommandListener
            listener) {
        return startAction(reqId, Definition.ACTION_HEAD_TURN_HEAD, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 停止头部转动
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int stopTurnHead(int reqId, CommandListener
            listener) {
        return startAction(reqId, Definition.ACTION_HEAD_TURN_HEAD, mGson.toJson(new HeadTurnBean()),
                parseCommand(listener));
    }

    /**
     * 获取头部转台.
     * 回调函数中会返回头部角度信息
     *
     * @param reqId
     * @param listener 回调函数
     * @return
     */
    public int getHeadStatus(int reqId, CommandListener listener) {

        CommandBean bean = new CommandBean(Definition.CMD_HEAD_GET_STATUS, null, false);
        return startAction(reqId, Definition.ACTION_HEAD_GET_STATUS, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 设置焦点跟随对象
     *
     * @param reqId
     * @param name
     * @param id       人脸id
     * @param mode     焦点跟随方式，详见{@link TrackMode}
     * @param listener 回调函数
     * @return
     */
    public int setTrackTarget(int reqId, String name, int id,
                              TrackMode mode, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_NAME, name);
            param.put(Definition.JSON_HEAD_ID, id);
            param.put(Definition.JSON_HEAD_MODE, mode.getValue());
            param.put(Definition.JSON_HEAD_CONTROL, Definition.JSON_HEAD_START);

            CommandBean bean = new CommandBean(Definition.CMD_HEAD_SET_TRACK_TARGET, param
                    .toString(), false);
            return startAction(reqId, Definition.ACTION_HEAD_SET_TRACK_TARGET, mGson.toJson(bean)
                    , parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 按角度切换焦点跟随对象.
     * <p> 机器人会在该角度范围内查找合适人脸，并进行焦点跟随
     *
     * @param reqId
     * @param angle    角度
     * @param listener 回调函数
     * @return
     */
    public int switchTrackTarget(int reqId, int angle, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_SWITCH_ANGLE, angle);

            CommandBean bean = new CommandBean(Definition.CMD_HEAD_SWITCH_TRACK_TARGET, param
                    .toString(), false);
            return startAction(reqId, Definition.ACTION_HEAD_SWITCH_TRACK_TARGET, mGson.toJson(bean)
                    , parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 停止焦点跟随
     *
     * @param reqId
     * @param listener 回调函数
     * @return
     */
    public int stopTrack(int reqId, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_CONTROL, Definition.JSON_HEAD_STOP);
            param.put(Definition.JSON_HEAD_NAME, "");
            param.put(Definition.JSON_HEAD_ID, -1);
            param.put(Definition.JSON_HEAD_MODE, 3);
            CommandBean bean = new CommandBean(Definition.CMD_HEAD_STOP_TRACK_TARGET, param
                    .toString(), false);
            return startAction(reqId, Definition.ACTION_STOP_TRACK_TARGET, mGson.toJson(bean),
                    parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 监听当前人头统计的信息
     *
     * @param reqId
     * @param listener 回调函数
     * @return
     */
    public int startGetHeadCount(final int reqId, double intervalTime, CommandListener listener) {
        String params;
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_INTERVAL, intervalTime);
            params = param.toString();
        } catch (JSONException e) {
            e.printStackTrace();
            return ERROR_REMOTE_VISITOR;
        }
        CommandBean bean = new CommandBean();
        bean.setCmdType(Definition.CMD_HEAD_START_HEAD_COUNT);
        bean.setReqId(reqId);
        bean.setParams(params);
        bean.setContinue(false);
        return startAction(reqId, Definition.ACTION_HEAD_START_HEAD_COUNT, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 移除监听当前人头统计信息
     *
     * @param reqId
     * @return
     */
    public int stopGetHeadCount(int reqId, CommandListener listener) {
        Log.d("RobotApi", "Stop all head count ###");
        String params;
        JSONObject param = new JSONObject();
        params = param.toString();
        CommandBean bean = new CommandBean();
        bean.setCmdType(Definition.CMD_HEAD_STOP_HEAD_COUNT);
        bean.setReqId(reqId);
        bean.setParams(params);
        bean.setContinue(false);
        return startAction(reqId, Definition.ACTION_HEAD_STOP_HEAD_COUNT, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 获取人头统计信息
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int getHeadCount(int reqId, CommandListener listener) {
        Log.d("RobotApi", "get head count ###");
        String params;
        JSONObject param = new JSONObject();
        params = param.toString();
        CommandBean bean = new CommandBean();
        bean.setCmdType(Definition.CMD_HEAD_GET_HEAD_COUNT);
        bean.setReqId(reqId);
        bean.setParams(params);
        bean.setContinue(false);
        return startAction(reqId, Definition.ACTION_HEAD_GET_HEAD_COUNT, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int updatePictureReportConfig(int reqId, String config, CommandListener listener) {
        Log.d("RobotApi", "updatePictureReportConfig config: " + config);
        String params;
        JSONObject param = new JSONObject();
        try {
            param.put(Definition.JSON_CONFIG, config);
            params = param.toString();
            CommandBean bean = new CommandBean();
            bean.setCmdType(Definition.CMD_HEAD_PICTURE_REPORT_CONFIG);
            bean.setReqId(reqId);
            bean.setParams(params);
            bean.setContinue(false);
            return startAction(reqId, Definition.ACTION_HEAD_PICTURE_REPORT_CONFIG, mGson.toJson(bean),
                    parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    /**
     * 头部是否连接成功
     *
     * @param reqId
     * @param listener 回调函数
     * @return
     */
    public int isHeaderConnected(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_IS_HEADER_CONNECTED, null, false);
        return startAction(reqId, Definition.ACTION_HEAD_IS_HEADER_CONNECTED, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 获取某个人脸图片
     *
     * @param reqId
     * @param id       人脸id
     * @param count    拿几张人脸照片
     * @param listener 回调函数
     * @return
     */
    public int getPictureById(int reqId, int id, int count, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_ID, id);
            param.put(Definition.JSON_HEAD_COUNT, count);

            CommandBean bean = new CommandBean(Definition.CMD_HEAD_GET_PICTURE_BY_ID, param
                    .toString(), false);
            return startAction(reqId, Definition.ACTION_HEAD_GET_PICTURE_BY_ID, mGson.toJson
                                                                                             (bean), parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * Get gesture info
     *
     * @param reqId    request action id
     * @param mode     continuous/oneshot
     * @param listener listener
     * @return
     * @deprecated
     */
    public int getGestureInfos(int reqId, String mode, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_LOOPER, mode);

            CommandBean bean = new CommandBean(Definition.CMD_HEAD_GET_GESTURE_INFOS, param
                    .toString(), false);
            return startAction(reqId, Definition.ACTION_HEAD_GET_GESTURE_INFOS, mGson.toJson
                                                                                             (bean), parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * stop gesture info report
     *
     * @param reqId
     * @param listener
     * @return
     * @deprecated
     */
    public int stopGestureInfos(int reqId, CommandListener listener) {
        return stopAction(reqId, Definition.ACTION_HEAD_GET_GESTURE_INFOS, true);
    }

    /**
     * 获取语音token
     *
     * @param reqId
     * @param listener 回调函数
     * @return
     */
    public int getSpeechToken(int reqId, CommandListener listener) {
        //return startAction(reqId, Definition.ACTION_SPEECH_GET_TOKEN, null, listener);
        // FIXME(toBtoC 合并接口，空实现)
        new RuntimeException("合并接口，空实现").printStackTrace();
        try {
            listener.onError(-1, "不支持");
            listener.onError(-1, "不支持", "");
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return -1;
    }

    private int startAction(int reqId, int actionId, String params, ActionListener listener) {
        logApi(actionId, params);
        try {
            IActionListener actionListener = null;
            if (listener != null) {
                if (Definition.DEBUG_ALL_DISPATHER) Log.v(Definition.DISPATCHER_TAG,
                        "actionId:" + actionId + ", listener:" + listener.hashCode());
                if (mResponseThread != null) {
                    Looper looper = mResponseThread.getLooper();
                    actionListener = mMessageDispatcher.obtainActionDispatcher(looper, listener);
                    if (Definition.DEBUG_ALL_DISPATHER) Log.v(Definition.DISPATCHER_TAG,
                            "looper:" + looper.hashCode() + ", actionListener:" + actionListener.hashCode());
                } else {
                    actionListener = mMessageDispatcher.obtainActionDispatcher(listener);
                    if (Definition.DEBUG_ALL_DISPATHER) Log.v(Definition.DISPATCHER_TAG,
                            "actionListener:" + actionListener.hashCode());
                }
            }

            if (mModuleRegistry == null) {
                Log.d(TAG, "Start action failed : " + actionId + "  " + params);
                return Definition.CMD_SEND_ERROR_UNKNOWN;
            }

            return mModuleRegistry.startAction(reqId, actionId, params, actionListener);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 开始ppt控制通道
     *
     * @param reqId
     * @param listener 回调函数
     * @return
     */
    public int startPptControlPipe(int reqId, CommandListener listener) {
        Log.d("FC_", "RobotApi getPptMqttPipe");
        JsonObject obj = new JsonObject();
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_PPT_MQTT_PIPE, obj.toString(), false);
        return startAction(reqId, Definition.ACTION_REMOTE_PPT_MQTT_PIPE,
                mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * 发送ppt控制指令
     *
     * @param reqId
     * @param param
     * @param commandIndex
     * @return
     */
    public int sendPptControlAction(int reqId, String param, String commandIndex) {
        JsonObject obj = new JsonObject();
        obj.addProperty("action", param);
        obj.addProperty("command_index", commandIndex);
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_PPT_MQTT_ACTION, obj.toString(), false);
        return startAction(reqId, Definition.ACTION_REMOTE_PPT_MQTT_ACTION,
                mGson.toJson(bean), null);
    }

    /**
     * 人脸注册
     *
     * @param reqId
     * @param name     要注册的姓名
     * @param pictures 人脸照片集合
     * @param listener 回调函数
     * @return
     */
    public int remoteRegister(int reqId, String name,
                              List<String> pictures, String welcomeContent, CommandListener listener) {
        PictureInfo info = new PictureInfo();
        info.setName(name);
        info.setPictures(pictures);
        info.setWelcomeContent(welcomeContent);
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_REGISTER, mGson.toJson(info),
                false);
        return startAction(reqId, Definition.ACTION_REMOTE_REGISTER, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 人脸识别
     *
     * @param reqId
     * @param personId 人脸id
     * @param pictures 人脸图片集合
     * @param listener 回调函数
     * @return
     */
    public int remoteDetect(int reqId, String personId, List<String> pictures, CommandListener
            listener) {
        PictureInfo info = new PictureInfo();
        info.setId(personId);
        info.setPictures(pictures);
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_DETECT, mGson.toJson(info), false);
        return startAction(reqId, Definition.ACTION_REMOTE_DETECT, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 修改已注册姓名
     *
     * @param reqId
     * @param userId       人脸服务端id
     * @param modifiedName 要修改的姓名
     * @param listener     回调函数
     * @return
     */
    public int remoteModifyDetectName(int reqId, String userId, String modifiedName, String welcomeContent, CommandListener
            listener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("user_id", userId);
            jsonObject.put("full_name", modifiedName);
            jsonObject.put("welcome_content", welcomeContent);
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_MODIFY_DETECT_NAME, jsonObject.toString(), false);
            Log.d("RegisterRemote", "remoteModifyDetectName json : " + jsonObject.toString() + ", mGson toString : " + mGson.toJson(bean));
            return startAction(reqId, Definition.ACTION_REMOTE_MODIFY_DETECT_NAME, mGson.toJson(bean),
                    parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;

    }

    /**
     * 人脸信息获取
     *
     * @param reqId
     * @param personId 人脸id
     * @param pictures 人脸照片集合
     * @param listener 回调函数
     * @return
     */
    public int getPersonInfoFromNet(int reqId, String personId, List<String> pictures, CommandListener
            listener) {
        PictureInfo info = new PictureInfo();
        info.setId(personId);
        info.setPictures(pictures);
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_GET_PERSON_INFO, mGson.toJson(info), false);
        return startAction(reqId, Definition.ACTION_REMOTE_GET_PERSON_INFO, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 上传当前地图。（2018年最初期使用的接口，已废弃）
     *
     * @param reqId
     * @param mapName  地图名称
     * @param listener 回调函数
     * @return
     */
    @Deprecated
    public int uploadMapToServer(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_UPLOAD_MAP_TO_SERVER,
                mapName, false);
        return startAction(reqId, Definition.ACTION_REMOTE_UPLOAD_MAP_TO_SERVER,
                mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * 清空当前使用地图
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int clearCurNaviMap(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_CLEAR_CUR_NAVI_MAP, null, false);
        return startAction(reqId, Definition.ACTION_NAVI_CLEAR_CUR_NAVI_MAP, mGson.toJson(bean), listener);
    }

    /**
     * 检查机器人前方是否有障碍物
     *
     * @param reqId
     * @param startAngle 开始检测角度
     * @param endAngle   结束检测角度
     * @param distance   多远距离内
     * @param listener   回调函数
     * @return
     */
    public int checkIfHasObstacle(int reqId, double startAngle, double endAngle, double distance, CommandListener listener) {
        CheckObstacleBean beanParam = new CheckObstacleBean(startAngle, endAngle, distance);
        beanParam.setReqId(reqId);
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_CHECK_OBSTACLE, mGson.toJson(beanParam), false);
        return startAction(reqId, Definition.ACTION_NAVI_CHECK_OBSTACLE, mGson.toJson(bean), listener);
    }

    public int hasObstacleInArea(int reqId, double startAngle, double endAngle, double minDistance, double maxDistance, CommandListener listener) {
        ObstacleInAreaBean beanParam = new ObstacleInAreaBean(startAngle, endAngle, minDistance, maxDistance);
        beanParam.setReqId(reqId);
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_HAS_OBSTACLE_IN_AREA, mGson.toJson(beanParam), false);
        return startAction(reqId, Definition.ACTION_NAVI_HAS_OBSTACLE_IN_AREA, mGson.toJson(bean), listener);
    }

    /**
     * 编辑地点
     *
     * @param reqId
     * @param operParam 编辑json
     * @param listener  回调函数
     * @return
     */
    public int editPlace(int reqId, String operParam, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_EDIT_PLACE, operParam, false);
        return startAction(reqId, Definition.ACTION_NAVI_EDIT_PLACE, mGson.toJson(bean), listener);
    }

    /**
     * This api convert text to mp3 file, before use this api you should request sdcard permission,
     * use @link {ActivityCompat.requestPermissions}, and use use @link {Environment.
     * getExternalStorageDirectory()} for store mp3 file. if file does not exist, it will create it,
     * otherwise delete and create it. when you have finished using this mp3 file, and please
     * delete it. Be carefully that the file you given can access by system user, otherwise it will
     * create failed.
     *
     * @param reqId    request id from module
     * @param text     text which to transfer mp3
     * @param fullFile full file path, like /sdcard/audio
     * @param fileName file name, text.mp3
     * @param listener listener which can get execute result, message is json {"code":0, "message":
     *                 "fail detail"}, code 0 denote success, otherwise fail
     * @return 0 denote send this command to service, -1 denote error
     */

    public int textToMp3(int reqId, String text, String fullFile, String fileName, CommandListener listener) {

        String fullFilePathAndName = fullFile + File.separator + fileName;
        File file = new File(fullFilePathAndName);
        if (file.exists() && !file.canWrite()) {
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("text", text);
            jsonObject.put("fileName", fullFilePathAndName);
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_TEXT_TO_MP3, jsonObject.toString(), false);
            Log.d(TAG, "text to mp3 json is " + jsonObject.toString());
            return startAction(reqId, Definition.ACTION_REMOTE_TEXT_TO_MP3, mGson.toJson(bean), listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * Please put apk to sdcard path, and call this api to install apk. The install result will send
     * broadcast to you by "ainirobot.intent.action.INSTALL_RESULT". The received bundle contains
     * "result" is boolean denote install success or fail, "error_msg" is string denotes error message
     * and "task_id" is string that you set it.
     *
     * @param reqId
     * @param fullPathName apk full path and name
     * @param taskID       task id
     * @return false denote connect fail, true denote execute install but not install success.
     */
    public boolean installApk(int reqId, String fullPathName, String taskID) {
        try {
            Bundle bundle = new Bundle();
            bundle.putString("filePath", fullPathName);
            bundle.putString("task_id", taskID);

            if (mModuleRegistry == null) {
                return false;
            } else {
                return mModuleRegistry.installPatch(bundle);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return false;
    }

    //------------------------------ speech apis ------------------------------------//
    private ActionListener parseCommand(final CommandListener listener) {
        if (listener == null) {
            return null;
        }

        return listener;
//        ActionListener al = new ActionListener() {
//            @Override
//            public void onResult(int status, String responseString) throws RemoteException {
//                listener.onResult(status, responseString);
//            }
//
//            @Override
//            public void onStatusUpdate(int status, String data) throws RemoteException {
//                listener.onStatusUpdate(status, data);
//            }
//        };
//        return al;
    }

    /*************  canservice 相关   ******************************************/

    /**
     * 开始自动回充.
     *
     * <p>机器人需要在充电桩附近1m范围内，并且后端充电凸起对准充电桩
     *
     * @param reqId
     * @return
     */
    public int startCharge(int reqId) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_AUTO_CHARGE_START, null, false);
        return startAction(reqId, Definition.ACTION_AUTO_CHARGE_START, mGson.toJson(bean), null);
    }

    /**
     * 结束自动回充
     *
     * @param reqId
     * @return
     */
    public int stopCharge(int reqId) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_AUTO_CHARGE_END, null, false);
        return startAction(reqId, Definition.ACTION_AUTO_CHARGE_END, mGson.toJson(bean), null);
    }

    /**
     * 获取充电状态
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int getChargeStatus(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_CHARGE_STATUS, null, false);
        return startAction(reqId, Definition.ACTION_AUTO_CHARGE_STATUS, mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * set startChargePose
     *
     * @param reqId
     * @param listener
     * @param chargeMode 判断回充类型
     * @param timeout    timeout of this action
     * @return
     */
    @Deprecated
    public int setStartChargePoseAction(int reqId, long timeout, int chargeMode, String language, ActionListener listener) {

        return setStartChargePoseAction(reqId, timeout, chargeMode, "", 0, 0, language, listener);
    }

    /**
     * set startChargePose V10.1接口保留 注意：接口只增不删
     */
    public int setStartChargePoseAction(int reqId, long timeout, int chargeMode, String setStartChargePoseType, String language, ActionListener listener) {
        return setStartChargePoseAction(reqId, timeout, chargeMode, setStartChargePoseType, 0, 0, language, listener);
    }

    /**
     * set startChargePose V10.3新增
     *
     * @param reqId 请求id
     * @param timeout  timeout of this action
     * @param chargeMode 设置(充电桩/定位点)点位模式场景 目前有三种:(0:【建图完成设点】(直接替换当前使用点位)、1:【边建图边设点】、2:【边编辑地图边设点】)
     * @param setStartChargePoseType 充电类型(桩充/线充)
     * @param typeId 特殊点类型(当前接口主要支持设置2：充电桩:4：定位点两种类型)
     * @param priority 特殊点优先级
     * @param listener 回调监听
     * @return 返回结果
     */
    public int setStartChargePoseAction(int reqId, long timeout, int chargeMode, String setStartChargePoseType, int typeId, int priority, String language, ActionListener listener) {
        return setStartChargePoseAction(reqId, timeout, chargeMode, setStartChargePoseType, false, "", typeId, priority, language, listener);
    }

    /**
     * set startChargePose
     *
     * @param reqId 请求id
     * @param timeout  timeout of this action
     * @param chargeMode 设置(充电桩/定位点)点位模式场景 目前有三种:(0:【建图完成设点】(直接替换当前使用点位)、1:【边建图边设点】、2:【边编辑地图边设点】)
     * @param setStartChargePoseType 充电类型(桩充/线充)
     * @param placeName 特殊点名称(V10.3支持多特殊点位，特殊点名称可自主定义)
     * @param typeId 特殊点类型(当前接口主要支持设置2：充电桩:4：定位点两种类型)
     * @param priority 特殊点优先级
     * @param isReplacePose 建图时若是操作[替换]该点位，需要进行尝试删除已设置点位接口，由该字段控制
     * @param listener 回调监听
     * @return 返回结果
     */
    public int setStartChargePoseAction(int reqId, long timeout, int chargeMode, String setStartChargePoseType, boolean isReplacePose, String placeName, int typeId, int priority, String language, ActionListener listener) {
        AutoChargeBean bean = new AutoChargeBean();
        bean.setReqId(reqId);
        bean.setTimeout(timeout);
        //TODO：根据平台设置回充类型，这块代码逻辑是否需要由coreservice处理，而不应该交给上层调用者？
        bean.setChargeMode(chargeMode);
        bean.setMapLanguage(language);
        bean.setSetStartChargePoseType(setStartChargePoseType);
        bean.setPlaceName(placeName);
        bean.setTypeId(typeId);
        bean.setPriority(priority);
        bean.setReplacePose(isReplacePose);
        String toJson = mGson.toJson(bean);
        return startAction(reqId, Definition.ACTION_SET_START_CHARGE_POSE, toJson, listener);
    }

    /**
     * @param reqId
     * @param timeout
     * @param listener
     * @return
     * @see {{@link #setStartChargePoseAction(int, long, int, String, ActionListener)}}
     */
    @Deprecated
    public int setStartChargePoseAction(int reqId, long timeout, ActionListener listener) {
        AutoChargeBean bean = new AutoChargeBean();
        bean.setReqId(reqId);
        bean.setTimeout(timeout);
        String toJson = mGson.toJson(bean);
        return startAction(reqId, Definition.ACTION_SET_START_CHARGE_POSE, toJson, listener);
    }

    /**
     * navigate to startChargePoint and then start auto_charge.
     *
     * @param reqId
     * @param timeout  timeout to charge done.
     * @param listener
     * @return
     */
    public int startNaviToAutoChargeAction(int reqId, long timeout, ActionListener listener) {
        AutoChargeBean bean = new AutoChargeBean();
        bean.setReqId(reqId);
        bean.setTimeout(timeout);
        bean.setChargeType(Definition.CHARGE_VISION_POLICY);
        String toJson = mGson.toJson(bean);
        return startAction(reqId, Definition.ACTION_AUTO_NAVI_CHARGE, toJson, listener);

    }

    /**
     * navigate to startChargePoint and then start auto_charge.
     *
     * @param reqId
     * @param timeout   timeout to charge done.
     * @param distance  distance of avoid
     * @param avoidTime timeout of avoid
     * @param listener
     * @return
     */
    public int startNaviToAutoChargeAction(int reqId, long timeout, double distance, long avoidTime, ActionListener listener) {
        AutoChargeBean bean = new AutoChargeBean();
        bean.setReqId(reqId);
        bean.setTimeout(timeout);
        bean.setAvoidDistance(distance);
        bean.setAvoidTimeout(avoidTime);
        String toJson = mGson.toJson(bean);
        return startAction(reqId, Definition.ACTION_AUTO_NAVI_CHARGE, toJson, listener);
    }

    /**
     * 导航到回充点，开始自动回充
     *
     * @param reqId
     * @param timeout       timeout to charge done.
     * @param distance      distance of avoid
     * @param avoidTime     time of avoid
     * @param multiWaitTime 多机等待时长
     * @param listener      回调函数
     * @return
     */
    public int startNaviToAutoChargeAction(int reqId, long timeout, double distance, long avoidTime,
                                           long multiWaitTime, ActionListener listener) {
        AutoChargeBean bean = new AutoChargeBean();
        bean.setReqId(reqId);
        bean.setTimeout(timeout);
        bean.setAvoidDistance(distance);
        bean.setAvoidTimeout(avoidTime);
        bean.setMultipleWaitTime(multiWaitTime);
        String toJson = mGson.toJson(bean);
        return startAction(reqId, Definition.ACTION_AUTO_NAVI_CHARGE, toJson, listener);
    }

    /**
     * stop auto_charge
     *
     * @param reqId
     * @return
     */
    public int stopAutoChargeAction(int reqId, boolean isResetHW) {
        return stopAction(reqId, Definition.ACTION_AUTO_NAVI_CHARGE, isResetHW);
    }

    /**
     * 停止设置充电桩
     *
     * @param reqId
     * @param isResetHW 是否重置硬件状态
     * @return
     */
    public int stopSetChargePileAction(int reqId, boolean isResetHW) {
        return stopAction(reqId, Definition.ACTION_SET_START_CHARGE_POSE, isResetHW);
    }

    /**
     * called for stop charging on by 3th app .
     * If it is charging on and level more than 10 , go forward 0.1m and leave ChargePile so as to stop charging.
     *
     * @return int 0 note sent cmd success  ; else fail .
     */
    public int stopChargingByApp() {
        if (ctx != null) {
            ctx.sendBroadcast(new Intent(Definition.ACTION_STOP_CHARGING_BY_APP));
            return 0;
        }
        return ERROR_REMOTE_VISITOR;
    }

    /**
     * 获取急停状态
     *
     * @param reqId
     * @param listener 回调函数
     * @return
     */
    public int getEmergencyStatus(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_EMERGENCY_STATUS, null, false);
        return startAction(reqId, Definition.ACTION_EMERGENCY_STATUS, mGson.toJson(bean),
                parseCommand(listener));
    }

    @Deprecated
    public int setLambColor(int reqId, int target, int color) {
        if (mModuleRegistry == null) {
            Log.d(TAG, "Set lamb color failed");
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }

        try {
            return mModuleRegistry.setLambColor(reqId, target, color);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    @Deprecated
    public int setLambAnimation(int reqId, int target, int start, int end, int startTime, int
            endTime, int repeat, int onTime, int freeze) {
        if (mModuleRegistry == null) {
            Log.d(TAG, "Set lamb animation failed");
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }

        JSONObject params = new JSONObject();
        try {
            return mModuleRegistry.setLambAnimation(reqId, target, start, end,
                    startTime, endTime, repeat, onTime, freeze);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    /***********************module相关*****************************************/

    /**
     * @param appName
     * @param parsePattern
     * @param cb
     */
    @Deprecated
    public void registerModule(String appName, List<String> parsePattern, ModuleCallbackApi cb) {
        setCallback(cb);
    }

    public void unregisterModule(String appName) {
//        try {
//            mModuleRegistry.unregisterModule(appName);
//        } catch (RemoteException e) {
//            e.printStackTrace();
//        }
    }

    /**
     * 注册状态监听
     *
     * @param type     要监听的事件类型
     * @param listener 回调函数
     * @return
     */
    public String registerStatusListener(String type, StatusListener listener) {
        try {
            if (listener == null
                    || mModuleRegistry == null) {
                return null;
            }

            StatusDispatcher statusListener = null;
            if (mResponseThread != null) {
                Looper looper = mResponseThread.getLooper();
                statusListener = mMessageDispatcher.obtainStatusDispatcher(looper, listener);
            } else {
                statusListener = mMessageDispatcher.obtainStatusDispatcher(listener);
            }

            String id = mModuleRegistry.registerStatusListener(type, statusListener);
            statusListener.register(id);
            return id;
        } catch (RemoteException e) {
            e.printStackTrace();
            return null;
        }
    }

//    public boolean unregisterStatusListener(String id) {
//        try {
//            return mModuleRegistry.unregisterStatusListener(id);
//        } catch (RemoteException e) {
//            e.printStackTrace();
//            return false;
//        }
//    }

    /**
     * 取消注册状态监听
     *
     * @param listener 回调函数
     * @return
     */
    public boolean unregisterStatusListener(StatusListener listener) {
        try {
            if (listener == null || mModuleRegistry == null) {
                return false;
            }
            boolean result = mModuleRegistry.unregisterStatusListener(listener.getId());
            mMessageDispatcher.unregisterStatusDispatcher(listener.getId());
            return result;
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }

    public boolean startStatusSocket(String type, int socketPort) {
        if (mModuleRegistry == null) {
            Log.d(TAG, "Start status socket failed : " + type + "  " + socketPort);
            return false;
        }
        try {
            return mModuleRegistry.startStatusSocket(type, socketPort);
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }

    public boolean closeStatusSocket(String type, int socketPort) {
        if (mModuleRegistry == null) {
            Log.d(TAG, "Close status socket failed : " + type + "  " + socketPort);
            return false;
        }
        try {
            return mModuleRegistry.closeStatusSocket(type, socketPort);
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 发送机器人状态事件.
     *
     * <p>可以通过{@link #registerStatusListener(String, StatusListener)}函数监听状态事件
     *
     * @param type 状态类型
     * @param data 状态数据
     */
    public void sendStatusReport(String type, String data) {
        if (mModuleRegistry == null) {
            Log.d(TAG, "Send status report failed : " + type + "  " + data);
            return;
        }
        try {
            mModuleRegistry.sendStatusReport(type, data);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Deprecated
    public int startInspection(int reqId, long time, boolean isReInspection, ActionListener listener) {
        String result = getInspectResult();
        if (!isReInspection && result != null) {
            try {
                listener.onResult(Definition.ACTION_RESPONSE_SUCCESS, result);
                listener.onResult(Definition.ACTION_RESPONSE_SUCCESS, result, "");
            } catch (RemoteException e) {
                e.printStackTrace();
            }
            return 0;
        }

        return startInspection(reqId, time, listener);
    }

    /**
     * 开始自检
     *
     * @param reqId
     * @param time     自检超时时间
     * @param listener 回调函数
     * @return
     */
    public int startInspection(int reqId, long time, ActionListener listener) {
        InspectActionBean bean = new InspectActionBean();
        bean.setReqId(reqId);
        bean.setTimeOut(time);
        String jsonStr = mGson.toJson(bean);
        return startAction(reqId, Definition.ACTION_INSPECTION, jsonStr, listener);
    }

    /**
     * 停止自检
     *
     * @param reqId
     * @param isResetHW
     * @return
     */
    public int stopInspection(int reqId, boolean isResetHW) {
        return stopAction(reqId, Definition.ACTION_INSPECTION, isResetHW);
    }

    /**
     * 获取硬件状态
     *
     * @param function
     * @return
     * @throws RemoteException
     */
    public int getHWStatus(int function) throws RemoteException {
        return mModuleRegistry.getHWStatus(function);
    }

    /**
     * 获取头部版本号
     *
     * @param reqId
     * @param listener 回调函数
     * @return
     */
    public int getHeadVersion(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_GET_VERSION, null, false);
        return startAction(reqId, Definition.ACTION_HEAD_GET_VERSION, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 获取底盘版本号
     *
     * @param reqId
     * @param listener 回调函数
     * @return
     */
    public int getNavigationVersion(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_VERSION, null, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_VERSION,
                mGson.toJson(bean), parseCommand(listener));
    }

    public int getNavigationUpdateParams(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_UPDATE_PARAMS, null, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_UPDATE_PARAMS, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int getNavigationSerialNumber(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_SERIAL_NUMBER, null, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_SERIAL_NUMBER, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int getHeadUpdateParams(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_GET_UPDATE_PARAMS, null, false);
        return startAction(reqId, Definition.ACTION_HEAD_GET_UPDATE_PARAMS, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int getHeadSerialNumber(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_GET_SERIAL_NUMBER, null, false);
        return startAction(reqId, Definition.ACTION_HEAD_GET_SERIAL_NUMBER, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * Get left wheel version form canService
     */
    public int getCanWheelLeftVersion(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_WHEEL_L_VERSION, null, false);
        return startAction(reqId, Definition.ACTION_CAN_GET_WHEEL_L_VERSION, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * Get right wheel version form canService
     */
    public int getCanWheelRightVersion(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_WHEEL_R_VERSION, null, false);
        return startAction(reqId, Definition.ACTION_CAN_GET_WHEEL_R_VERSION, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int getCanMotorHorizontalVersion(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_MOTOR_H_VERSION, null, false);
        return startAction(reqId, Definition.ACTION_CAN_GET_MOTOR_H_VERSION, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int getCanMotorVerticalVersion(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_MOTOR_V_VERSION, null, false);
        return startAction(reqId, Definition.ACTION_CAN_GET_MOTOR_V_VERSION, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int getCanPsbVersion(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_PSB_VERSION, null, false);
        return startAction(reqId, Definition.ACTION_CAN_GET_PSB_VERSION, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int getCanAutoChargeVersion(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_AUTO_CHARGE_VERSION, null, false);
        return startAction(reqId, Definition.ACTION_CAN_GET_AUTO_CHARGE_VERSION, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int getCanBatteryVersion(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_BATTERY_VERSION, null, false);
        return startAction(reqId, Definition.ACTION_CAN_GET_BATTERY_VERSION, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 转到某个点的朝向
     *
     * <p>只是机器人身体转到该点朝向方位，机器人并不会动
     *
     * @param reqId
     * @param placeName 点位名称
     * @param listener  回调函数
     * @return
     */
    public int resumeSpecialPlaceTheta(int reqId, String placeName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_RESUME_SPECIAL_PLACE_THETA,
                placeName, false);
        bean.setNeedAssistEstimate(false);
        return startAction(reqId, Definition.ACTION_NAVI_RESUME_SPECIAL_PLACE_THETA,
                mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * 停止转到某个点的朝向
     *
     * @param reqId
     * @return
     */
    public int stopResumeSpecialPlaceThetaAction(int reqId) {
        stopAction(reqId, Definition.ACTION_NAVI_RESUME_SPECIAL_PLACE_THETA, true);
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_STOP_NAVIGATION, null, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), null);
    }

    /**
     * robot reboot
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int canRobotReboot(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_ROBOT_REBOOT, null, false);
        return startAction(reqId, Definition.ACTION_CAN_ROBOT_REBOOT, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int getHeadCameraStatus(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_GET_CAMERA_STATUS, null, false);
        return startAction(reqId, Definition.ACTION_HEAD_GET_CAMERA_STATUS,
                mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * 获取当前使用的地图名称
     *
     * @param reqId
     * @param listener 回调函数
     * @return
     */
    public int getMapName(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_MAP_NAME, null, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_MAP_NAME,
                mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * 获取当前pose点
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int getPosition(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_POSITION, null, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_POSITION,
                mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * @param reqId
     * @param param
     * @param listener
     * @return
     */
    public int setMapInfo(int reqId, String param, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_MAP_INFO, param, false);
        return startAction(reqId, Definition.ACTION_NAVI_SET_MAP_INFO, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int remoteRequestQrcode(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_QRCODE, null, false);
        return startAction(reqId, Definition.ACTION_REMOTE_QRCODE,
                mGson.toJson(bean), parseCommand(listener));
    }

    public int remoteCheckVerify(int reqId, String code, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_VERIFY, code, false);
        return startAction(reqId, Definition.ACTION_REMOTE_VERIFY,
                mGson.toJson(bean), parseCommand(listener));
    }

    public int remoteCheckPhoneVerify(int reqId, String code, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_PHONE_VERIFY, code, false);
        return startAction(reqId, Definition.ACTION_REMOTE_VERIFY,
                mGson.toJson(bean), parseCommand(listener));
    }

    public int remoteRequestSkill(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_SKILL, null, false);
        return startAction(reqId, Definition.ACTION_REMOTE_SKILL,
                mGson.toJson(bean), parseCommand(listener));
    }

    public int remotePostMsg(int reqId, String taskId, String status, CommandListener listener) {
        JsonObject obj = new JsonObject();
        obj.addProperty("taskId", taskId);
        obj.addProperty("status", status);
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_POSTMSG, obj.toString(), false);
        return startAction(reqId, Definition.ACTION_REMOTE_POSTMSG,
                mGson.toJson(bean), parseCommand(listener));
    }

    public int remotePostEmergencyMsg(int reqId, String status) {
        JsonObject obj = new JsonObject();
        obj.addProperty("emergency", status);
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_POST_EMERGENCY_MSG, obj.toString
                                                                                                (), false);
        return startAction(reqId, Definition.ACTION_REMOTE_POST_REMOTE_MESSAGE,
                mGson.toJson(bean), null);
    }

    public int remoteRequestGuestInfo(int reqId, String id, String type, String startTime, String
            endTime, CommandListener listener) {
        JsonObject obj = new JsonObject();
        obj.addProperty("visitor_id", id);
        obj.addProperty("type", type);
        obj.addProperty("btime", startTime);
        obj.addProperty("etime", endTime);
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_GUESTINFO, obj.toString(), false);
        return startAction(reqId, Definition.ACTION_REMOTE_GUESTINFO,
                mGson.toJson(bean), parseCommand(listener));
    }

    public int remoteRegister(int reqId, String name, String id,
                              List<String> pictures, CommandListener listener) {

        PictureInfo info = new PictureInfo();

        try {
            info.setId(id);
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }

        info.setName(name);
        info.setPictures(pictures);
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_REGISTER_TEMPID, mGson.toJson
                                                                                               (info), false);
        return startAction(reqId, Definition.ACTION_REMOTE_REGISTER_TEMPID,
                mGson.toJson(bean), parseCommand(listener));
    }

    //<CMD_REMOTE_BIND_STATUS> command will refresh profile, not available for user
    public int remoteBindStatus(int reqId, CommandListener listener) {
        JsonObject obj = new JsonObject();
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_CHECK_BIND_STATUS, obj.toString(),
                false);
        return startAction(reqId, Definition.ACTION_REMOTE_BIND_STATUS,
                mGson.toJson(bean), parseCommand(listener));
    }

    public int remoteGetMapId(int reqId, CommandListener listener) {
        JsonObject obj = new JsonObject();
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_GET_MAP_ID, obj.toString(),
                false);
        Log.d("FC_", "RobotApi remoteGetMapId ");
        return startAction(reqId, Definition.ACTION_REMOTE_GET_MAP_ID,
                mGson.toJson(bean), parseCommand(listener));
    }

    public int remoteWakeUpTimes(int reqId, CommandListener listener) {
        JsonObject obj = new JsonObject();
        obj.addProperty("wakeup", 1);
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_WAKEUP_TIMES, obj.toString(),
                false);
        return startAction(reqId, Definition.ACTION_REMOTE_WAKEUP_TIMES,
                mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * 获取电量剩余使用时间
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int getBatteryTimeRemaining(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_BATTERY_TIME_REMAIN, null, false);
        return startAction(reqId, Definition.ACTION_CAN_BATTERY_TIME_REMAIN,
                mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * 获取充电剩余时间
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int getChargeTimeRemaining(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_CHARGE_TIME_REMAIN, null, false);
        return startAction(reqId, Definition.ACTION_CAN_CHARGE_TIME_REMAIN,
                mGson.toJson(bean), parseCommand(listener));
    }

    public int sendBatteryTimeRemaining(int reqId, String msg) {
        JsonObject obj = new JsonObject();
        obj.addProperty("battery", Long.valueOf(msg).longValue());
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_POST_BATTERY_TIME_REMAIN, obj.toString(), false);
        return startAction(reqId, Definition.ACTION_REMOTE_POST_BATTERY_TIME_REMAIN,
                mGson.toJson(bean), null);
    }

    public int sendChargingTimeRemaining(int reqId, String msg) {
        JsonObject obj = new JsonObject();
        obj.addProperty("charging", Long.valueOf(msg).longValue());
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_POST_CHARGE_TIME_REMAIN, obj.toString(), false);
        return startAction(reqId, Definition.ACTION_REMOTE_POST_CHARGE_TIME_REMAIN,
                mGson.toJson(bean), null);
    }

    /**
     * 获取深度摄像头状态
     *
     * @param reqId
     * @param id
     * @param count
     * @param listener
     * @return
     */
    public int getDepthCameraStatus(int reqId, int id, int count, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_ID, id);
            param.put(Definition.JSON_HEAD_COUNT, count);

            CommandBean bean = new CommandBean();
            bean.setCmdType(Definition.CMD_HEAD_GET_DEPTH_STATUS);
            bean.setParams(param.toString());
            bean.setContinue(false);

            return startAction(reqId, Definition.ACTION_HEAD_GET_DEPTH_STATUS, mGson.toJson(bean), parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    public int postCruiseStatus(int reqId, String params) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_POST_CRUISE_STATUS, params, false);
        return startAction(reqId, Definition.ACTION_REMOTE_POST_CRUISE_STATUS,
                mGson.toJson(bean), null);
    }

    /**
     * 获取广角摄像头状态
     *
     * @param reqId
     * @param id
     * @param count
     * @param listener
     * @return
     */
    public int getFovCameraStatus(int reqId, int id, int count, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_ID, id);
            param.put(Definition.JSON_HEAD_COUNT, count);

            CommandBean bean = new CommandBean();
            bean.setCmdType(Definition.CMD_HEAD_GET_FOV_STATUS);
            bean.setParams(param.toString());
            bean.setContinue(false);

            return startAction(reqId, Definition.ACTION_HEAD_GET_FOV_STATUS, mGson.toJson(bean), parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    /**
     * 视觉重定位
     *
     * @param reqId
     * @param retryCount 尝试次数
     * @param listener   回调函数
     * @return
     */
    public int locateVision(int reqId, int retryCount, CommandListener listener) {
        ResetEstimateParams params = new ResetEstimateParams();
        params.setReqId(reqId);
        params.setRetryCount(retryCount);
        return startAction(reqId, Definition.ACTION_LOCATE_VISION,
                mGson.toJson(params), parseCommand(listener));
    }

    /**
     * 重定位
     *
     * @param reqId
     * @param retryCount
     * @param listener
     * @return
     */
    public int resetEstimate(int reqId, int retryCount, CommandListener listener) {
        ResetEstimateParams params = new ResetEstimateParams();
        params.setReqId(reqId);
        params.setRetryCount(retryCount);
        return startAction(reqId, Definition.ACTION_RESET_ESTIMATE,
                mGson.toJson(params), parseCommand(listener));
    }

    public int remotePostPrepared(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_POST_PREPARED, null, false);
        return startAction(reqId, Definition.ACTION_REMOTE_POST_PREPARED,
                mGson.toJson(bean), null);
    }

    public int postSetPlaceToServer(int reqId, String params) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_POST_SET_PLACE, params, false);
        return startAction(reqId, Definition.ACTION_REMOTE_POST_SET_PLACE,
                mGson.toJson(bean), null);
    }

    public int enableTX1(int reqId, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_MASTER_SWITCH_TYPE, 1);
            CommandBean bean = new CommandBean(Definition.CMD_HEAD_MASTER_SWITCH, param.toString(), false);
            return startAction(reqId, Definition.ACTION_HEAD_MASTER_SWITCH,
                    mGson.toJson(bean), parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    public int disableTX1(int reqId, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_MASTER_SWITCH_TYPE, 0);
            CommandBean bean = new CommandBean(Definition.CMD_HEAD_MASTER_SWITCH, param.toString(), false);
            return startAction(reqId, Definition.ACTION_HEAD_MASTER_SWITCH,
                    mGson.toJson(bean), parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }


    /**
     * remote charge pile pose when set charge from WeiXin program
     *
     * @param reqId
     * @param status   start/finish/running
     * @param msg      String err msg/JsonString position
     * @param result   boolean true/false
     * @param listener
     * @return
     */
    public int remoteChargePile(int reqId,
                                String status,
                                String msg,
                                boolean result,
                                CommandListener listener) {
        Log.d("FC_core", "remote setChargePile status : " + status + ", msg : " + msg + " , result : " + result);
        try {
            JsonObject value = new JsonObject();
            value.addProperty("status", status);
            value.addProperty("name", Definition.START_BACK_CHARGE_POSE);
            if (!TextUtils.isEmpty(msg)) {
                JsonElement msgJson = new JsonParser().parse(msg);
                if (msgJson != null && msgJson.isJsonObject()) {
                    value.add("position", msgJson);
                } else {
                    value.addProperty("msg", msg);
                }
            }

            JsonObject obj = new JsonObject();
            obj.addProperty("type", Definition.REQ_FIRST_SET_CHARGING_PILE);
            obj.add("value", value);
            obj.addProperty("result", result);
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_CHARGE_PILE, mGson.toJson(obj), false);
            Log.d("FC_core", "FCApi remoteChargePile　reqId = " + reqId);
            return startAction(reqId, Definition.ACTION_REMOTE_CHARGE_PILE,
                    mGson.toJson(bean), parseCommand(listener));
        } catch (Throwable e) {
            Log.e("FC_core", "FCApi remoteChargePile e : " + e.getLocalizedMessage());
            return 0;
        }

    }

    /**
     * report to result of auto_charge from WeiXin program
     *
     * @param reqId
     * @param status
     * @param result
     * @param listener
     * @return
     * @hide
     */
    public int remoteFirstCharge(int reqId,
                                 String status,
                                 boolean result,
                                 CommandListener listener) {
        Log.d("FC_core", "remote first charge status : " + status + " , result : " + result);
        try {
            JsonObject value = new JsonObject();
            value.addProperty("status", status);

            JsonObject obj = new JsonObject();
            obj.addProperty("type", Definition.REQ_FIRST_CHARGING);
            obj.add("value", value);
            obj.addProperty("result", result);

            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_FIRST_CHARGING, mGson.toJson(obj), false);
            Log.d("FC_core", "FCApi remoteFirstCharge　reqId = " + reqId);
            return startAction(reqId, Definition.ACTION_REMOTE_FIRST_CHARGING,
                    mGson.toJson(bean), parseCommand(listener));
        } catch (Throwable e) {
            Log.e("FC_core", "remoteFirstCharge e : " + e.getLocalizedMessage());
            return 0;
        }
    }

    public int setShippingMode(int reqId, CommandListener listener) {

        CommandBean bean = new CommandBean(Definition.CMD_CAN_SHIPPING_MODE, null, false);
        return startAction(reqId, Definition.ACTION_SHIPPING_MODE, mGson.toJson(bean), parseCommand(listener));

    }

    public int receptionRegister(int reqId, int detection, String imageSrc, String name, String taskId, int has_image,
                                 CommandListener listener) {

        JsonObject obj = new JsonObject();
        obj.addProperty("detection", detection);
        obj.addProperty("image", imageSrc);
        obj.addProperty("name", name);
        obj.addProperty("task_id", taskId);
        obj.addProperty("has_image", has_image);
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_REGISTER_TEMPID, mGson.toJson(obj), false);

        return startAction(reqId, Definition.ACTION_REMOTE_REGISTER_TEMPID, mGson.toJson(bean), parseCommand(listener));
    }

    @Deprecated
    public int getCanRotateSupport(int reqId, CommandListener listener) {
        listener.onStatusUpdate(Definition.STATUS_TURN_HEAD_SUPPORT_ROTATE, "");
        return 0;
    }

    public int cancelUploadMapFile(int reqId, String cancelType, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_CANCEL_UPLOAD_FILE
                , cancelType, false);
        return startAction(reqId, Definition.ACTION_REMOTE_CANCEL_UPLOAD_FILE, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int uploadMapPkg(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_UPLOAD_MAP_PKG
                , mapName, false);
        return startAction(reqId, Definition.ACTION_REMOTE_UPLOAD_MAP_PKG, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int uploadMapPlaceList(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_UPLOAD_PLACE_LIST
                , mapName, false);
        return startAction(reqId, Definition.ACTION_REMOTE_UPLOAD_PLACE_LIST, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int deleteCloudMapPlaceList(int reqId, String mapName, final CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_DELETE_PLACE_LIST, mapName, false);
        return startAction(reqId, Definition.ACTION_REMOTE_DELETE_PLACE_LIST, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int getCloudMapPlaceList(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_GET_PLACE_LIST, mapName, false);
        return startAction(reqId, Definition.ACTION_REMOTE_GET_PLACE_LIST, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int deleteCloudMap(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_DELETE_MAP
                , mapName, false);
        return startAction(reqId, Definition.ACTION_REMOTE_DELETE_MAP, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int uploadSwitchInfo(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_SWITCH_MAP_INFO
                , mapName, false);
        return startAction(reqId, Definition.ACTION_REMOTE_SWITCH_MAP_INFO, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int getCloudFloorListInfo(int reqId, String param, final CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_GET_FLOOR_LIST_INFO
                , param, false);
        return startAction(reqId, Definition.ACTION_REMOTE_GET_FLOOR_LIST, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int getCloudMapListInfo(int reqId, final CommandListener listener) {
        CommandListener proxyListener = new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                String msg = Utils.getFileContent(message);
                Utils.removeFileFromSDCard(message);
                listener.onResult(result, msg);
                listener.onResult(result, msg, "");
            }
        };

        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_GET_MAP_LIST_INFO
                , FILE_BACK, false);
        return startAction(reqId, Definition.ACTION_REMOTE_GET_MAP_LIST_INFO, mGson.toJson(bean),
                parseCommand(proxyListener));
    }

    public int getCloudMapListInfo(int reqId, String mapName, final CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_GET_MAP_LIST_INFO
                , mapName, false);
        return startAction(reqId, Definition.ACTION_REMOTE_GET_MAP_LIST_INFO, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int parseDownloadMapPlaceListToNaviProp(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_PARSE_PLACE_LIST, mapName, false);
        return startAction(reqId, Definition.ACTION_NAVI_PARSE_PLACE_LIST, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int saveRoadData(int reqId, String mapName, String roadJson, CommandListener listener) {
        JsonObject obj = new JsonObject();
        obj.addProperty("mapName", mapName);
        obj.addProperty("roadJson", roadJson);
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SAVE_ROAD_DATA, mGson.toJson(obj), false);
        return startAction(reqId, Definition.ACTION_NAVI_SAVE_ROAD_DATA, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int saveGateData(int reqId, String mapName, String gateJson, CommandListener listener) {
        JsonObject obj = new JsonObject();
        obj.addProperty("mapName", mapName);
        obj.addProperty("gateJson", gateJson);
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SAVE_GATE_DATA, mGson.toJson(obj), false);
        return startAction(reqId, Definition.ACTION_NAVI_SAVE_GATE_DATA, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int autoDrawRoad(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_AUTO_DRAW_ROAD, mapName, false);
        return startAction(reqId, Definition.ACTION_NAVI_AUTO_DRAW_ROAD, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 停止扩建地图
     */
    public int stopExpansionMap(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_STOP_EXPANSION_MAP, mapName, false);
        return startAction(reqId, Definition.ACTION_NAVI_STOP_EXPANSION_MAP, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int savePlaceListToPlaceFile(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SAVE_PLACE_LIST, mapName, false);
        return startAction(reqId, Definition.ACTION_NAVI_SAVE_PLACE_LIST, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int uploadOperationLog(int reqId, String module, String oper, String state,
                                  String msg, String mapName, CommandListener listener) {
        JsonObject obj = new JsonObject();
        obj.addProperty("module", module);
        obj.addProperty("operation", oper);
        obj.addProperty("state", state);
        obj.addProperty("msg", msg);
        obj.addProperty("name", mapName);
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_UPLOAD_OPER_LOG, mGson.toJson(obj)
                , false);
        return startAction(reqId, Definition.ACTION_REMOTE_UPLOAD_OPER_LOG, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 设置巡逻路线
     *
     * @param reqId
     * @param params
     * @param listener
     * @return
     * @deprecated
     */
    public int setNaviCruiseRoute(int reqId, String params, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_CRUISE_ROUTE, params, false);
        return startAction(reqId, Definition.ACTION_NAVI_SET_CRUISE_ROUTE, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 获取巡逻路线
     *
     * @param reqId
     * @param params
     * @param listener
     * @return
     * @deprecated
     */
    public int getNaviCruiseRoute(int reqId, String params, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_CRUISE_ROUTE, params, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_CRUISE_ROUTE, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 指定API回调处理线程
     * <p>
     * 未设置回调到调用线程, 如果调用线程没有Looper, 则回调到主线程
     *
     * @param thread
     */
    public void setResponseThread(HandlerThread thread) {
        this.mResponseThread = thread;
    }

    /*
     * Report navigation error to NavigationService for package.
     * @param reqId
     * @param timestamp
     * @param cacheId SN + "-" + timestamp
     * @param type
     * @param params
     * @param listener
     * @return
     */
    public int naviTimeOutCmdReport(int reqId, long timestamp, String cacheId, String type,
                                    String params, CommandListener listener) {
        Log.i(TAG, Definition.TAG_NAVI_LOG_REPORT + " type : " + type);
        NaviCmdTimeOutBean naviCmd = new NaviCmdTimeOutBean(timestamp, cacheId, reqId, type, params);
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_TIME_OUT_REPORT,
                new Gson().toJson(naviCmd), false);
        return startAction(reqId, Definition.ACTION_NAVI_TIME_OUT_REPORT,
                mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * 讲解员——评分——语音
     *
     * @param reqId
     * @param params
     * @param listener
     * @return
     */
    public int scoreRecordingTask(int reqId, String params, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_GUIDE_SCORE, params, false);
        return startAction(reqId, Definition.ACTION_GUIDE_SCORE_RECORDING,
                mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * Set lighting effects
     *
     * @param reqId    request ID
     * @param params,  including:
     *                 type - Lighting effect type
     *                 start - Start color
     *                 end - End color
     *                 startTime - Start time
     *                 endTime - End time
     *                 repeat - Loop times
     *                 onTime - Duration
     *                 freese - end lighting color
     * @param listener
     * @return the status of set action.
     */
    public int setLight(int reqId, String params, ActionListener listener) {
        if (mModuleRegistry == null) {
            Log.d(TAG, "set light, mModuleRegistry is null");
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
        try {
            return mModuleRegistry.setLight(reqId, params);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    public int setLedLight(int reqId, LedLightBean ledLightBean) {
        if (mModuleRegistry == null) {
            Log.d(TAG, "Set led light failed");
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }

        if (ledLightBean == null) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }

        try {
            return mModuleRegistry.setLedLight(reqId, mGson.toJson(ledLightBean));
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    @Deprecated
    public int robotReboot(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_ROBOT_REBOOT, null, false);
        return startAction(reqId, Definition.ACTION_CAN_ROBOT_REBOOT, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * Call this interface is used to obtain SN number
     *
     * @param listener
     * @return Returns the results
     */
    public int getRobotSn(CommandListener listener) {
        return RobotSettings.getSystemSn(listener);

    }

    /**
     * 获取机器人版本信息
     *
     * @return
     */
    public String getRobotVersion() {
        return RobotSettings.getVersion();
    }

    /**
     * 重命名地图名称
     *
     * @param reqId
     * @param oldName  原地图名字
     * @param newName  新地图名字
     * @param listener
     * @return 方法是否执行
     */
    public int renameMap(int reqId, String oldName, String newName, CommandListener listener) {
        JsonObject obj = new JsonObject();
        obj.addProperty(Definition.OLD_MAP_NAME, oldName);
        obj.addProperty(Definition.NEW_MAP_NAME, newName);
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_RENAME_MAP, mGson.toJson(obj), false);
        return startAction(reqId, Definition.ACTION_NAVI_RENAME_MAP, mGson.toJson(bean), listener);
    }

    /**
     * Clear the route of current map
     *
     * @param params Need map name with key "map_name"
     * @return
     */
    public int clearNaviCruiseRoute(int reqId, String params, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_CLEAR_CRUISE_ROUTE, params, false);
        return startAction(reqId, Definition.ACTION_NAVI_CLEAR_CRUISE_ROUTE, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int checkRobotPosePosition(int reqId, String param, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_CHECK_POSE_POSITION, param, false);
        return startAction(reqId, Definition.ACTION_NAVI_CHECK_POSE_POSITION, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int startNavigationBack(int reqId, String destination, ActionListener listener) {
        return startNavigationBack(reqId, destination, SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED, SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED, listener);
    }

    public int startNavigationBack(int reqId, String destinationJson, double linearSpeed, double angularSpeed, ActionListener listener) {
        NavigationBean bean = new NavigationBean();
        bean.setReqId(reqId);
        bean.setDestination(destinationJson);
        bean.setLinearSpeed(linearSpeed);
        bean.setAngularSpeed(angularSpeed);
        String jsonStr = mGson.toJson(bean);

        return startAction(reqId, Definition.ACTION_NAVIGATION_BACK, jsonStr, listener);
    }

    public int stopNavigationBack(int reqId) {
        return stopAction(reqId, Definition.ACTION_NAVIGATION_BACK, true);
    }

    /**
     * 停止所有的action指令
     *
     * @param reqId
     * @return
     */
    public int stopAllAction(int reqId) {
        return stopAction(reqId, Definition.ACTION_STOP_ALL_ACTIONS, true);
    }

    public int startAngleResetAction(int reqId, String destination, String poseJson, ActionListener listener) {

        AngleResetBean bean = new AngleResetBean();
        bean.setReqId(reqId);
        if (!TextUtils.isEmpty(destination)) {
            bean.setDestination(destination);
        }
        if (!TextUtils.isEmpty(poseJson)) {
            bean.setPoseJson(poseJson);
        }
        String jsonStr = mGson.toJson(bean);

        return startAction(reqId, Definition.ACTION_ANGLE_RESET, jsonStr, listener);
    }

    public int stopAngleResetAction(int reqId) {
        return stopAction(reqId, Definition.ACTION_ANGLE_RESET, true);
    }

    public int getAskWayList(int reqId, CommandListener listener) {
        final CommandBean commandBean = new CommandBean(Definition.CMD_REMOTE_GET_ASK_WAY_LIST, null, false);
        return startAction(reqId, Definition.ACTION_REMOTE_GET_ASK_WAY_LIST, mGson.toJson(commandBean), parseCommand(listener));
    }

    private static String getParamJsonObjectString(Map<String, Object> map) {
        try {
            JSONObject param = new JSONObject();

            for (Map.Entry<String, Object> entry : map.entrySet()) {
                param.put(entry.getKey(), entry.getValue());
            }
            return param.toString();
        } catch (Exception e) {
            return "";
        }
    }

    public int updateRnInstallStatus(int reqId, String installId, int status, String message, CommandListener listener) {
        JSONObject params = new JSONObject();
        try {
            params.put(Definition.JSON_RN_INSTALL_ID, installId);
            params.put(Definition.JSON_RN_INSTALL_STATUS, status);
            params.put(Definition.JSON_RN_INSTALL_MESSAGE, message);
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_RN_INSTALL_STATUS,
                    params.toString(), false);
            return startAction(reqId, Definition.ACTION_REMOTE_RN_INSTALL_STATUS,
                    mGson.toJson(bean), parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int startPictureReport(int reqId, double intervalTime, CommandListener listener) {
//        String params;
//        try {
//            JSONObject param = new JSONObject();
//            param.put(Definition.JSON_HEAD_INTERVAL, intervalTime);
//            params = param.toString();
//        } catch (JSONException e) {
//            e.printStackTrace();
//            return ERROR_REMOTE_VISITOR;
//        }
//        CommandBean bean = new CommandBean();
//        bean.setCmdType(Definition.CMD_HEAD_START_PICTURE_REPORT);
//        bean.setReqId(reqId);
//        bean.setParams(params);
//        bean.setContinue(false);
//        return startAction(reqId, Definition.ACTION_HEAD_START_PICTURE_REPORT, mGson.toJson(bean),
//                parseCommand(listener));
        // FIXME(toBtoC 合并接口，空实现)
        new RuntimeException("合并接口，空实现").printStackTrace();
        try {
            listener.onError(-1, "不支持");
            listener.onError(-1, "不支持", "");
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return -1;
    }

    public int stopPictureReport(int reqId, CommandListener listener) {
//        CommandBean bean = new CommandBean(Definition.CMD_HEAD_STOP_PICTURE_REPORT,
//                null, false);
//        return startAction(reqId, Definition.ACTION_HEAD_STOP_PICTURE_REPORT, mGson.toJson(bean),
//                parseCommand(listener));
        // FIXME(toBtoC 合并接口，空实现)
        new RuntimeException("合并接口，空实现").printStackTrace();
        try {
            listener.onError(-1, "不支持");
            listener.onError(-1, "不支持", "");
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return -1;
    }

    public int startBodyReport(int reqId, double intervalTime, CommandListener listener) {
//        String params;
//        try {
//            JSONObject param = new JSONObject();
//            param.put(Definition.JSON_HEAD_INTERVAL, intervalTime);
//            params = param.toString();
//        } catch (JSONException e) {
//            e.printStackTrace();
//            return ERROR_REMOTE_VISITOR;
//        }
//        CommandBean bean = new CommandBean();
//        bean.setCmdType(Definition.CMD_HEAD_START_BODY_REPORT);
//        bean.setReqId(reqId);
//        bean.setParams(params);
//        bean.setContinue(false);
//        return startAction(reqId, Definition.ACTION_HEAD_START_BODY_REPORT, mGson.toJson(bean),
//                parseCommand(listener));
        // FIXME(toBtoC 合并接口，空实现)
        new RuntimeException("合并接口，空实现").printStackTrace();
        try {
            listener.onError(-1, "不支持");
            listener.onError(-1, "不支持", "");
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return -1;
    }

    public int stopBodyReport(int reqId, CommandListener listener) {
//        CommandBean bean = new CommandBean(Definition.CMD_HEAD_STOP_BODY_REPORT,
//                null, false);
//        return startAction(reqId, Definition.ACTION_HEAD_STOP_BODY_REPORT, mGson.toJson(bean),
//                parseCommand(listener));
        // FIXME(toBtoC 合并接口，空实现)
        new RuntimeException("合并接口，空实现").printStackTrace();
        try {
            listener.onError(-1, "不支持");
            listener.onError(-1, "不支持", "");
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return -1;
    }

    public int startVision(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_START_VISION,
                null, false);
        return startAction(reqId, Definition.ACTION_HEAD_START_VISION, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int stopVision(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_STOP_VISION,
                null, false);
        return startAction(reqId, Definition.ACTION_HEAD_STOP_VISION, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 临时方案
     *
     * @param reqId
     * @param listener
     * @return
     */
    @Deprecated
    public int startBackupVision(int reqId, CommandListener listener) {
        Log.d(TAG, "startBackupVision");
        CommandBean bean = new CommandBean(Definition.CMD_BACKUP_START_VISION,
                null, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 临时方案
     *
     * @param reqId
     * @param listener
     * @return
     */
    @Deprecated
    public int stopBackupVision(int reqId, CommandListener listener) {
        Log.d(TAG, "stopBackupVision");
        CommandBean bean = new CommandBean(Definition.CMD_BACKUP_STOP_VISION,
                null, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 更新休眠状态
     *
     * @param standbyStart
     * @param jsonData
     * @return
     */
    public boolean updateStandbyStatus(boolean standbyStart, String jsonData) {
        try {
            Map<String, Object> robotHashMap = mGson.fromJson(jsonData, new TypeToken<Map<String, Object>>() {
            }.getType());
            if (null == robotHashMap) {
                robotHashMap = new HashMap<>();
            }
            String standbyStatus;
            if (standbyStart) {
                standbyStatus = Definition.START;
            } else {
                standbyStatus = Definition.STOP;
            }
            robotHashMap.put(Definition.JSON_STANDBY_STATUS, standbyStatus);
            return mModuleRegistry.updateSystemStatus(Definition.SYSTEM_STANDBY, mGson.toJson(robotHashMap));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 机器人开始休眠
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int robotStandby(int reqId, CommandListener listener) {
        RobotStandbyBean bean = new RobotStandbyBean();
        return robotStandby(reqId, bean, listener);
    }

    /**
     * 机器人开始休眠
     *
     * @param reqId
     * @param bean     休眠参数
     * @param listener
     * @return
     */
    public int robotStandby(int reqId, RobotStandbyBean bean, CommandListener listener) {
        bean.setReqId(reqId);
        String jsonStr = mGson.toJson(bean);
        return startAction(reqId, Definition.ACTION_ROBOT_STANDBY, jsonStr, listener);
    }

    /**
     * 机器人结束休眠
     *
     * @param reqId
     * @return
     */
    public int robotStandbyEnd(int reqId) {
        return stopAction(reqId, Definition.ACTION_ROBOT_STANDBY, false);
    }

    public int stopHorizontalTurnHead(int reqId) {
        try {
            return mModuleRegistry.stopAction(reqId, Definition.ACTION_HORIZONTAL_TURN_HEAD, true);
        } catch (RemoteException e) {
            e.printStackTrace();
            return ERROR_REMOTE_VISITOR;
        }
    }

    public int skillDataReport(int reqId, String param, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_SKILL_DATA_REPORT, param, false);
        return startAction(reqId, Definition.ACTION_REMOTE_SKILL_DATA_REPORT, mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * get charge state
     *
     * @return String result is string "true" or "false"
     */
    public boolean getChargeStatus() {
        boolean isCharging = false;
        try {
            String result = mModuleRegistry.getRobotInfo(Definition.SYNC_ACTION_IS_CHARGING, null);
            isCharging = Boolean.parseBoolean(result);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return isCharging;
    }

    /**
     * get battery level
     *
     * @return result is string "0"...."100"
     */
    public int getBatteryLevel() {
        String batteryLevel = "";
        try {
            batteryLevel = mModuleRegistry.getRobotInfo(Definition.SYNC_ACTION_GET_BATTERY_LEVEL, null);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        if (!TextUtils.isEmpty(batteryLevel)) {
            return Integer.parseInt(batteryLevel);
        } else {
            return 0;
        }
    }

    /**
     * 开关箱门
     *
     * @param reqId
     * @param type   外置设备类型
     * @param bord   侧门编号
     * @param enable 开关状态
     * @return
     */
    public int setLockEnable(int reqId, int type, int bord, boolean enable) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_DOOR_TYPE, type);
            param.put(Definition.JSON_CAN_BOARD, bord);
            param.put(Definition.JSON_CAN_LOCK_ENABLE, enable);
            CommandBean bean = new CommandBean(Definition.CMD_CAN_SET_LOCK_ENABLE, param.toString(), false);
            return startAction(reqId, Definition.ACTION_CAN_SET_LOCK_ENABLE,
                    mGson.toJson(bean), null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    /**
     * 获取舱门状态
     *
     * @param reqId
     * @param type     外置设备类型
     * @param listener
     * @return
     */
    public int getDoorStatus(int reqId, int type, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_DOOR_TYPE, type);
            CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_DOOR_STATUS, param.toString(), false);
            return startAction(reqId, Definition.ACTION_CAN_GET_DOOR_STATUS,
                    mGson.toJson(bean), parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    /**
     * 从服务端获取语言列表
     *
     * @param reqId
     * @param listener 回调函数
     * @return
     */
    public int getLanguageList(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_GET_LANGUAGE_LIST, null, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * get inspect result
     *
     * @return inspection result
     */
    public String getInspectResult() {

        String inspectionResult = null;
        try {
            inspectionResult = mModuleRegistry.getRobotInfo(Definition.SYNC_ACTION_GET_INSPECTION_RESULT, null);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return inspectionResult;
    }

    /**
     * 获取机器人软件版本
     *
     * @return 软件版本
     */
    public String getVersion() {
        return RobotSettings.getVersion();
    }

    /**
     * 定位状态
     *
     * @return true 已定位
     */
    public boolean isRobotEstimate() {
        boolean estimate = false;
        try {
            String result = mModuleRegistry.getRobotInfo(Definition.SYNC_ACTION_IS_ROBOT_ESTIMATE, null);
            estimate = Boolean.parseBoolean(result);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return estimate;
    }

    /**
     * 充电桩点位状态
     *
     * @return true 已设置
     */
    public boolean isChargePileExits() {
        boolean exits = false;
        try {
            String result = mModuleRegistry.getRobotInfo(Definition.SYNC_ACTION_IS_CHARGE_PILE_EXIT, null);
            exits = Boolean.parseBoolean(result);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return exits;
    }

    /**
     * 云台是否支持 -180 旋转
     *
     * @return true 支持
     */
    public boolean isSupportHeadReverse() {
        boolean support = false;
        try {
            String result = mModuleRegistry.getRobotInfo(Definition.SYNC_ACTION_IS_SUPPORT_HEAD_REVERSE, null);
            support = Boolean.parseBoolean(result);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return support;
    }

    /**
     * 机器是否在指定点位
     *
     * @param name  点位名称
     * @param range 范围半径
     * @return
     */
    public boolean isRobotInlocations(@NonNull String name, double range) {
        boolean inLocation = false;
        try {
            JSONObject params = new JSONObject();
            params.put("name", name);
            params.put("range", range);
            String result = mModuleRegistry.getRobotInfo(Definition.SYNC_ACTION_IS_IN_LOCATION, params.toString());
            inLocation = Boolean.parseBoolean(result);
        } catch (JSONException | RemoteException e) {
            e.printStackTrace();
        }
        return inLocation;
    }

    /**
     * 机器是否在接待点
     */
    public boolean isInReceptionLocation() {
        boolean inLocation = false;
        try {
            String result = mModuleRegistry.getRobotInfo(Definition.SYNC_ACTION_IS_IN_RECEPTION_LOCATION, null);
            inLocation = Boolean.parseBoolean(result);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return inLocation;
    }

    /**
     * 指定名称地点和机器当前位置间的距离
     */
    public double getPlaceDistance(String placeName) {
        double distance = 0;
        try {
            String result = mModuleRegistry.getRobotInfo(Definition.SYNC_ACTION_GET_PLACE_DISTANCE, placeName);
            distance = Double.parseDouble(result);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return distance;
    }

    public double getPlaceOrPoseDistance(String placeName, String destPlaceName) {
        return getPlaceOrPoseDistance(placeName, null, destPlaceName, null);
    }

    public double getPlaceOrPoseDistance(String placeName, Pose destPose) {
        return getPlaceOrPoseDistance(placeName, null, null, destPose);
    }

    public double getPlaceOrPoseDistance(Pose sourcePose, String destPlaceName) {
        return getPlaceOrPoseDistance(null, sourcePose, destPlaceName, null);
    }

    public double getPlaceOrPoseDistance(Pose sourcePose, Pose destPose) {
        return getPlaceOrPoseDistance(null, sourcePose, null, destPose);
    }

    /**
     * 指定名称地点和指定位置间的距离
     */
    public double getPlaceOrPoseDistance(String placeName, Pose sourcePose, String destPlaceName, Pose destPose) {
        double distance = 0;
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAV_SOUR_PLACENAME, placeName);
            param.put(Definition.JSON_NAV_DEST_PLACENAME, destPlaceName);
            if (sourcePose != null) {
                param.put(Definition.JSON_NAV_SOUR_POSE, mGson.toJson(sourcePose));
            }
            if (destPose != null) {
                param.put(Definition.JSON_NAV_DEST_POSE, mGson.toJson(destPose));
            }
            String result = mModuleRegistry.getRobotInfo(Definition.SYNC_ACTION_GET_PLACE_POSE_DISTANCE, param.toString());
            distance = Double.parseDouble(result);
        } catch (JSONException | RemoteException e) {
            e.printStackTrace();
        }
        return distance;
    }

    /**
     * 当前地图下所有 Pose 点
     */
    public List<Pose> getPlaceList() {
        List<Pose> list = null;
        try {
            String result = mModuleRegistry.getRobotInfo(Definition.SYNC_ACTION_GET_ALL_LOCATION, null);
            list = mGson.fromJson(result, new TypeToken<List<Pose>>() {
            }.getType());
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return list;
    }

    /**
     * 指定名称 Pose 点
     */
    public Pose getSpecialPose(String placeName) {
        Pose pose = null;
        try {
            String result = mModuleRegistry.getRobotInfo(Definition.SYNC_ACTION_GET_SPECIAL_LOCATION, placeName);
            pose = mGson.fromJson(result, Pose.class);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return pose;
    }

    /**
     * 当前 Pose 点
     */
    public Pose getCurrentPose() {
        Pose pose = null;
        try {
            String result = mModuleRegistry.getRobotInfo(Definition.SYNC_ACTION_GET_CURRENT_LOCATION, null);
            pose = mGson.fromJson(result, Pose.class);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return pose;
    }

    /**
     * 巡逻路线
     */
    public CruiseRouteBean getNaviCruiseRoute() {
        CruiseRouteBean route = null;
        try {
            String result = mModuleRegistry.getRobotInfo(Definition.SYNC_ACTION_GET_CRUISE_ROUTE, null);
            route = mGson.fromJson(result, CruiseRouteBean.class);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return route;
    }

    /**
     * 当前地图名称
     */
    public String getMapName() {
        try {
            return mModuleRegistry.getRobotInfo(Definition.SYNC_ACTION_GET_MAP_NAME, null);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 查询Robot状态
     */
    public void getRobotStatus(String type, StatusListener listener) {
        try {

            if (listener == null
                    || mModuleRegistry == null) {
                return;
            }

            StatusDispatcher statusListener = null;
            if (mResponseThread != null) {
                Looper looper = mResponseThread.getLooper();
                statusListener = mMessageDispatcher.obtainStatusDispatcher(looper, listener);
            } else {
                statusListener = mMessageDispatcher.obtainStatusDispatcher(listener);
            }

            mModuleRegistry.getRobotStatus(type, statusListener);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 截屏接口
     *
     * @param reqId      if you don't know the exact reqId , put into 0;
     * @param screenType 0 is default ; 1 is extra screen for example the big screen.
     * @param listener
     * @return the local path of the screen caped
     */
    public int capScreen(int reqId, int screenType, CommandListener listener) {
        JSONObject params = new JSONObject();
        try {
            params.put(Definition.SCREEN_DISPLAY_TYPE, screenType);
            CommandBean bean = new CommandBean(Definition.CMD_CAN_CAP_SCREEN, params.toString(), false);
            return startAction(reqId, Definition.ACTION_CAN_CAP_SCREEN,
                    mGson.toJson(bean), parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获取cpu温度
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int getCpuTemperature(int reqId, CommandListener listener) {
        JSONObject params = new JSONObject();
        CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_CPU_TEMPERATURE, params.toString(), false);
        return startAction(reqId, Definition.ACTION_CAN_GET_CPU_TEMPERATURE,
                mGson.toJson(bean), parseCommand(listener));
    }

    public String getPipeInfo() {
        String pipeInfo = null;
        try {
            pipeInfo = mModuleRegistry.getRobotInfo(Definition.SYNC_ACTION_GET_CHANNEL_INFO, null);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return pipeInfo;
    }

    public String getMqttInfo() {
        String pipeInfo = null;
        try {
            pipeInfo = mModuleRegistry.getRobotInfo(Definition.SYNC_ACTION_GET_MQTT_INFO, null);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return pipeInfo;
    }

    /**
     * 获取传感器状态
     *
     * @param reqId
     * @param listener 回调函数
     * @return
     */
    public int getSensorStatus(int reqId, CommandListener listener) {
        JSONObject params = new JSONObject();
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_SENSOR_STATUS, params.toString(), false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_SENSOR_STATUS,
                mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * 雷达开关
     *
     * @param reqId
     * @param openRadar true-启动 false-关闭
     * @param listener
     * @return
     */
    public int updateRadarStatus(int reqId, boolean openRadar, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_OPEN_RADAR, openRadar);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_RADAR_STATUS, param.toString(), false);
            return startAction(reqId, Definition.ACTION_NAVI_SET_RADAR_STATUS, mGson.toJson(bean), listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获取当前雷达状态
     *
     * @param reqId
     * @param listener
     * @return radarStatus 新曾雷达具体状态，0-已启动 1-已关闭 2-正在启动 3-正在关闭
     */
    public int queryRadarStatus(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_QUERY_RADAR_STATUS, "", false);
            return startAction(reqId, Definition.ACTION_NAVI_QUERY_RADAR_STATUS, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获取底盘连接状态
     *
     * @param reqId
     * @param listener 回调函数
     * @return
     */
    public int getNaviConnectStatus(int reqId, CommandListener listener) {
        JSONObject params = new JSONObject();
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_CONNECT_STATUS, params.toString(), false);
        return startAction(reqId, Definition.ACTION_NAVI_CONNECT_STATUS,
                mGson.toJson(bean), parseCommand(listener));
    }

    public int remoteRequestQrcodeMini(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_QRCODE_MINI, null, false);
        return startAction(reqId, Definition.ACTION_COMMON,
                mGson.toJson(bean), parseCommand(listener));
    }

    public int remoteBindStatusMini(int reqId, String sid, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_CHECK_BIND_STATUS_MINI, sid,
                false);
        return startAction(reqId, Definition.ACTION_COMMON,
                mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * 设置最小的安全避障距离
     *
     * @param reqId
     * @param distance 避障距离
     * @param listener 回调函数
     * @return
     */
    public int setObstaclesSafeDistance(int reqId, double distance, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_MIN_OBSTACLES_DISTANCE, distance);

            CommandBean bean = new CommandBean();
            bean.setContinue(false);
            bean.setCmdType(Definition.CMD_NAVI_SET_MIN_OBSTACLES_DISTANCE);
            bean.setParams(param.toString());

            return startAction(reqId, Definition.ACTION_NAVI_SET_MIN_OBSTACLES_DISTANCE,
                    mGson.toJson(bean), parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    public int executeCardCmd(int reqId, String params, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CARD_INDICATE_CMD_CHANNEL, params, false);
        return this.startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), this.parseCommand(listener));
    }

    /**
     * 重置安全避障距离
     *
     * @param reqId
     * @param listener 回调函数
     * @return
     */
    public int resetObstaclesSafeDistance(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_RESET_MIN_OBSTACLES_DISTANCE, "", false);
            return startAction(reqId, Definition.ACTION_NAVI_RESET_MIN_OBSTACLES_DISTANCE, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 更新机器人状态
     *
     * @param status 取值：{@link Definition#ROBOT_STATUS_IDLE}, {@link Definition#ROBOT_STATUS_BUSY}
     * @return
     */
    public boolean updateRobotStatus(int status) {
        try {
            return mModuleRegistry.updateRobotStatus(status);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 开关屏幕背光
     *
     * @param onOff
     * @return
     */
    public boolean switchScreen(boolean onOff) {
        try {
            return mModuleRegistry.switchScreen(onOff);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 设置语言
     *
     * @param language 语言
     */
    public void setLanguage(String language) {
        try {
            mModuleRegistry.setLanguage(language);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取fov摄像头信息
     *
     * @param reqId
     * @param listener 回调函数
     * @return
     */
    public int getFovCameraInfo(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_GET_FOV_CAMERA_INFO, null, false);
        return startAction(reqId, Definition.ACTION_HEAD_GET_FOV_CAMERA_INFO, mGson.toJson(bean), listener);
    }

    /**
     * 批量更新地点列表
     *
     * @param reqId
     * @param placeList 地点列表
     * @param listener  回调函数
     * @return
     */
    public int updatePlaceList(int reqId, List<PlaceBean> placeList, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_UPDATE_PLACE_LIST,
                ZipUtils.zipMapData(Definition.CMD_NAVI_UPDATE_PLACE_LIST,
                        mGson, mGson.toJson(placeList)), false);
        return startAction(reqId, Definition.ACTION_NAVI_UPDATE_PLACE_LIST, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 边建图边设点-保存位置点
     *
     * @param reqId
     * @param pose     位置点
     * @param listener 回调函数
     * @return
     */
    public int saveMappingPose(int reqId, Pose pose, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_ADD_MAPPING_POSE, pose.toJson(), false);
        return startAction(reqId, Definition.ACTION_NAVI_ADD_MAPPING_POSE, mGson.toJson(bean), listener);
    }

    /**
     * 边建图边设点-删除位置点
     *
     * @param reqId
     * @param name     要删除的地点名称
     * @param listener 回调函数
     * @return
     */
    public int deleteMappingPose(int reqId, String name, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_DELETE_MAPPING_POSE, name, false);
        return startAction(reqId, Definition.ACTION_NAVI_DELETE_MAPPING_POSE, mGson.toJson(bean), listener);
    }

    /**
     * 边建图边设点-重命名点位
     *
     * @param reqId
     * @param param    重命名参数
     * @param listener 回调函数
     * @return
     */
    public int renameMappingPose(int reqId, String param, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_RENAME_MAPPING_POSE, param, false);
        return startAction(reqId, Definition.ACTION_NAVI_RENAME_MAPPING_POSE, mGson.toJson(bean), listener);
    }

    /**
     * 边建图边设点-重命名点位
     *
     * @param reqId
     * @param newPoseName 新的点位名称
     * @param oldPoseName 老的点位名称
     * @param listener    回调函数
     * @return
     */
    public int renameMappingPose(int reqId, String newPoseName, String oldPoseName, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_RENAME_POSE_OLD_POSENAME, oldPoseName);
            param.put(Definition.JSON_NAVI_RENAME_POSE_NEW_POSENAME, newPoseName);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_RENAME_MAPPING_POSE, param.toString(), false);
            return startAction(reqId, Definition.ACTION_NAVI_RENAME_MAPPING_POSE, mGson.toJson(bean), listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 远程分身-机器人处理收到的呼叫请求
     *
     * @param reqId
     * @param param    接通还是挂断
     * @param listener 回调函数
     * @return
     */
    public int answerAppResult(int reqId, String param, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_VIDEO_APP_ANSWER, param, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
    }

    /**
     * 远程分身-获取用户列表
     *
     * @param reqId
     * @param param
     * @param listener
     * @return
     */
    public int getUserListWithVideo(int reqId, String param, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_VIDEO_USER_LIST, param, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
    }

    /**
     * 远程分身-呼叫手机端
     *
     * @param reqId
     * @param param    呼叫参数
     * @param listener
     * @return
     */
    public int inviteCallApp(int reqId, String param, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_VIDEO_INVITE_CALL, param, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
    }

    /**
     * 扩建地图
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int startExtendMap(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_START_EXTEND_MAP, "", false);
        return startAction(reqId, Definition.ACTION_NAVI_START_EXTEND_MAP, mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * 取消新建地图
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int cancelCreateMap(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_CANCEL_CREATE_MAP, "", false);
        return startAction(reqId, Definition.ACTION_NAVI_CANCEL_CREATE_MAP, mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * 获取视觉使用分辨率
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int getVisionResolution(int reqId, CommandListener listener) {
        JSONObject params = new JSONObject();
        CommandBean bean = new CommandBean(Definition.CMD_VISION_GET_RESOLUTION, params.toString(), false);
        return startAction(reqId, Definition.ACTION_COMMON,
                mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * 离开充电桩
     *
     * @param speed    默认为0.7
     * @param distance 默认为0.2m
     * @return
     */
    public int leaveChargingPile(int reqId, float speed, float distance, CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.JSON_NAVI_DISTANCE, distance + "");
            jsonObject.put("speed", speed + "");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return startAction(reqId, Definition.ACTION_LEAVE_CHARGING_PILE, jsonObject.toString(), listener);
    }

    public int getVisionMode(int reqId, CommandListener listener) {
        JSONObject params = new JSONObject();
        CommandBean bean = new CommandBean(Definition.CMD_VISION_GET_VISION_MODE, params.toString(), false);
        return startAction(reqId, Definition.ACTION_COMMON,
                mGson.toJson(bean), parseCommand(listener));
    }

    public int judgeInChargingPile(int reqId, float coordinateDeviation, CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.KEY_COORDINATE_DEVIATION, String.valueOf(coordinateDeviation));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return startAction(reqId, Definition.ACTION_JUDGE_IN_CHARGING_PILE, jsonObject.toString(), listener);
    }

    public int setChassisRelocation(int reqId, int mode, Pose pose, CommandListener listener) {

        try {
            JSONObject param = new JSONObject();
            if (pose != null) {
                param.put(Definition.JSON_NAVI_RELOCATION_POSE, pose.toJsonObject());
            }
            param.put(Definition.JSON_NAVI_RELOCATION_TYPE, mode);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_RELOCATION, param
                    .toString(), false);
            return startAction(reqId, Definition.ACTION_NAVI_SET_RELOCATION, mGson.toJson
                                                                                          (bean), parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int getDefWelcomeTTS(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_GET_DEFAULT_WELCOME_TTS, null, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), parseCommand(listener));
    }

    public int taskModeReport(int reqId, String taskId, String taskMode, int taskResult, CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.JSON_TASK_MODE_ID, taskId);
            jsonObject.put(Definition.JSON_TASK_MODE_NAME, taskMode);
            jsonObject.put(Definition.JSON_TASK_MODE_RESULT, taskResult);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_TASK_MODE_REPORT, jsonObject.toString(), false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), parseCommand(listener));
    }

    public int taskExecReport(int reqId, String taskId, String taskType, String execResult, String execData, CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.JSON_TASK_ID, taskId);
            jsonObject.put(Definition.JSON_TASK_TYPE, taskType);
            jsonObject.put(Definition.JSON_TASK_EXEC_RESULT, execResult);
            jsonObject.put(Definition.JSON_TASK_EXEC_DATA, execData);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_TASK_EXEC_REPORT, jsonObject.toString(), false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), parseCommand(listener));
    }

    public int taskCommandReport(int reqId, String cmdId, String cmdType, String execResult,
                                 String execData, CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.JSON_CMD_ID, cmdId);
            jsonObject.put(Definition.JSON_CMD_TYPE, cmdType);
            jsonObject.put(Definition.JSON_TASK_EXEC_RESULT, execResult);
            jsonObject.put(Definition.JSON_TASK_EXEC_DATA, execData);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_COMMAND_EXEC_REPORT,
                jsonObject.toString(), false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), parseCommand(listener));
    }

    public int getSpecialDishLabels(int reqId, CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_GET_SPECIAL_DISH_LABELS,
                jsonObject.toString(), false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), parseCommand(listener));
    }

    public int getAppToken(int reqId, String app_type, String app_name, String app_version,
                           CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.JSON_APP_TYPE, app_type);
            jsonObject.put(Definition.JSON_APP_NAME, app_name);
            jsonObject.put(Definition.JSON_APP_VERSION, app_version);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_GET_APP_TOKEN,
                jsonObject.toString(), false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * 机器人是否有视觉能力
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int isRobotHasVision(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_IS_HAS_VISIION, "", false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
    }

    public int resetPoseEstimate(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_RESET_ESTIMATE, "", false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
    }

    /**
     * 获取多机功能的开关状态
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int getMultiFunctionSwitchState(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_MULTI_FUNC_SWITCH_STATE, "", false);
        return startAction(reqId, Definition.ACTION_COMMON,
                mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * 拍照.
     * <p>使用fov摄像头拍照
     *
     * @param reqId
     * @param params
     * @param listener
     * @return
     */
    public int takePicture(int reqId, String params, CommandListener listener) {
        return getFovCameraStatus(reqId, -1, 0, listener);
    }

    /**
     * 上传巡逻拍照的图片
     *
     * @param reqId
     * @param params
     * @param listener
     * @return
     */
    public int uploadCruisePicture(int reqId, String params, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_UPLOAD_CRUISE_PICTURE, params, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * 查询多机状态下，机器人本地地图是否和多机服务器地图匹配
     */
    public int queryMultipleMapMatchStatus(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_QUERY_MULTIPLE_MAP_STATUS, null, false);
            return startAction(reqId, Definition.ACTION_NAVI_QUERY_MULTIPLE_MAP_STATUS, mGson.toJson(bean),
                    parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    /**
     * 获取地图信息
     *
     * @param reqId
     * @param mapName  地图名称
     * @param listener
     * @return
     */
    public int getMapInfo(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_MAP_INFO, mapName, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_MAP_INFO, mGson.toJson(bean), listener);
    }

    /**
     * 设置地图上传云端状态
     *
     * @param reqId
     * @param mapName      地图名称
     * @param isMapState   是否是Map状态。否则为Place状态
     * @param needReUpload 是否需要重置状态
     * @param listener
     * @return
     */
    public int setMapSyncState(int reqId, String mapName, boolean isMapState, boolean needReUpload, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_MAP_NAME, mapName);
            param.put(Definition.JSON_MAP_IS_MAP_STATE, isMapState);
            param.put(Definition.JSON_MAP_NEED_RE_UPLOAD, needReUpload);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_MAP_SYNC_STATE, param.toString(), false);
            return startAction(reqId, Definition.ACTION_NAVI_SET_MAP_SYNC_STATE, mGson.toJson(bean), listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 设置地图完成状态
     *
     * @param reqId
     * @param mapName     地图名称
     * @param finishState 0-没有地图 1-开始建图成功 2-充电桩设置成功 4-接待点设置成功 7-完成
     * @param isReset     是否需要重置，重置的话，直接覆盖原值，否则需要与原值进行“或（|）”操作
     * @param listener
     * @return
     */
    public int setMapFinishState(int reqId, String mapName, int finishState, boolean isReset, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_MAP_NAME, mapName);
            param.put(Definition.JSON_MAP_FINISH_STATE, finishState);
            param.put(Definition.JSON_MAP_FINISH_STATE_IS_RESET, isReset);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_MAP_FINISH_STATE, param.toString(), false);
            return startAction(reqId, Definition.ACTION_NAVI_SET_MAP_FINISH_STATE, mGson.toJson(bean), listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 设置地图禁行线标记
     *
     * @param reqId
     * @param mapName    地图名称
     * @param forbidFlag 0-没有禁行线
     * @param listener
     * @return
     */
    public int setMapForbidLineFlag(int reqId, String mapName, int forbidFlag, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_MAP_NAME, mapName);
            param.put(Definition.JSON_MAP_FORBID_LINE, forbidFlag);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_MAP_FORBID_LINE, param.toString(), false);
            return startAction(reqId, Definition.ACTION_NAVI_SET_MAP_FORBID_LINE, mGson.toJson(bean), listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 设置地图更新时间
     *
     * @param reqId
     * @param mapName    地图名称
     * @param updateTime 更新时间(s)
     * @param listener
     * @return
     */
    public int setMapUpdateTime(int reqId, String mapName, long updateTime, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_MAP_NAME, mapName);
            param.put(Definition.JSON_MAP_UPDATE_TIME, updateTime);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_MAP_UPDATE_TIME, param.toString(), false);
            return startAction(reqId, Definition.ACTION_NAVI_SET_MAP_UPDATE_TIME, mGson.toJson(bean), listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 禁用功能键
     */
    public void disableFunctionKey() {
        try {
            if (mModuleRegistry == null) {
                Log.d(TAG, "Disable function key failed, mModuleRegistry is null");
                return;
            }
            mModuleRegistry.disableFunctionKey();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 使能功能键
     */
    public void enableFunctionKey() {
        try {
            if (mModuleRegistry == null) {
                Log.d(TAG, "enable function key failed, mModuleRegistry is null");
                return;
            }
            mModuleRegistry.enableFunctionKey();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }


    /**
     * 上报当前任务
     *
     * @param task 任务
     * @return
     */
    public String reportTask(TaskProxy task) {
        if (task == null) {
            return null;
        }
        try {
            return mModuleRegistry.reportTask(task.generateTask());
        } catch (RemoteException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 上报某个任务事件
     *
     * @param taskEvent 任务事件
     */
    public void reportTaskEvent(TaskEventProxy taskEvent) {
        if (taskEvent == null) {
            return;
        }
        try {
            mModuleRegistry.reportTaskEvent(taskEvent.generateTaskEvent());
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 上报后台任务事件，会通过当前任务 ID 和任务 type 进行匹配，匹配成功则上报
     *
     * @param taskEvent 后台任务事件，必须有当前任务 ID 和当前任务 type，以及后台任务
     */
    public void reportBackgroundTaskEvent(BackgroundTaskEvent taskEvent) {
        if (!isTaskEventValid(taskEvent)) {
            Log.d(TAG, "reportBackgroundTaskEvent: taskEvent is invalid");
            return;
        }
        sendStatusReport(Definition.STATUS_BACKGROUND_TASK_CHANGE, mGson.toJson(taskEvent));
    }

    private boolean isTaskEventValid(BackgroundTaskEvent taskEvent) {
        return taskEvent != null &&
                !TextUtils.isEmpty(taskEvent.getCurrentTaskId()) &&
                !TextUtils.isEmpty(taskEvent.getCurrentTaskType()) &&
                taskEvent.getBackgroundTaskList() != null &&
                !taskEvent.getBackgroundTaskList().isEmpty();
    }

    /**
     * 获取当前的任务列表
     *
     * @return
     */
    public List<Task> getCurrentTasks() {
        try {
            return mModuleRegistry.getCurrentTask();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return new ArrayList<>();
    }

    public int moduleCodeConfigUpload(int reqId, String moduleCode, String description, String configName, String configJson, CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.JSON_MODULE_CODE, moduleCode);
            jsonObject.put(Definition.JSON_DESCRIPTION, description);
            jsonObject.put(Definition.JSON_CONFIG_NAME, configName);
            jsonObject.put(Definition.JSON_CONFIG_JSON, configJson);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_MODULE_CODE_CONFIG_UPLOAD, jsonObject.toString(), false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), parseCommand(listener));

    }

    public int getCanPsbMotorVersion(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_PSB_S_VERSION, null, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int setPowerLpm(int reqId, String params) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_CAN_SET_POWER_LPM, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int pushTaskReport(int reqId, List<PushReportBean> pushReportBeans, CommandListener listener) {
        if (pushReportBeans == null || pushReportBeans.size() <= 0) {
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
        try {
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_PUSH_TASK_REPORT,
                    mGson.toJson(pushReportBeans), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 解析底盘地图数据
     * 第一个版本是解析road.json为road_graph.data数据
     * dataType如果是 roadData表明解析的是road.json
     */
    public int parseChassisMapData(int reqId, String mapName, String dataType, CommandListener listener) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(Definition.JSON_MAP_NAME, mapName);
            jsonObject.put(Definition.JSON_DATA_TYPE, dataType);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_PARSE_MAP_DATA, jsonObject.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 配置多机参数
     *
     * @param reqId
     * @param params   参数信息参照LoraConfigBean
     * @param listener
     * @return
     */
    public int setMultiRobotSettingConfig(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_MULTI_ROBOT_CONFIG, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获取本地和服务端都支持的系统语言
     *
     * @return
     */
    public String getLocalAndServerSupportLanguageList() {
        String languageList = "";
        try {
            if (mModuleRegistry == null) {
                Log.d(TAG, "Get language failed, mModuleRegistry is null");
                return languageList;
            }
            languageList = mModuleRegistry.getRobotInfo(
                    Definition.SYNC_ACTION_GET_LOCAL_AND_SERVER_LANGUAGE, null);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return languageList;
    }

    public int floorPositionList(
            int reqId,
            String floorId,
            String posName,
            String padZcbPlateBackTableTaskStatus,
            CommandListener listener
    ) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.JSON_REMOTE_FLOOR_ID, floorId);
            jsonObject.put(Definition.JSON_REMOTE_POS_NAME, posName);
            jsonObject.put(Definition.JSON_REMOTE_PAD_ZCB_PLATE_BACK_TABLE_TASK_STATUS, padZcbPlateBackTableTaskStatus);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        try {
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_FLOOR_POSITION_LIST,
                    jsonObject.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int taskGroupQueue(int reqId, String allowTaskType, String taskGroupTypeList,
                              CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.JSON_REMOTE_ALLOW_TASK_TYPE, allowTaskType);
            jsonObject.put(Definition.JSON_REMOTE_TASK_GROUP_TYPE, taskGroupTypeList);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        try {
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_TASK_GROUP_QUEUE,
                    jsonObject.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int taskCreate(int reqId, String parentTaskOutId, String taskType, String taskList,
                          CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.JSON_REMOTE_PARENT_TASK_OUT_ID, parentTaskOutId);
            jsonObject.put(Definition.JSON_REMOTE_TASK_TYPE, taskType);
            jsonObject.put(Definition.JSON_REMOTE_TASK_LIST, taskList);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        try {
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_TASK_CREATE,
                    jsonObject.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int taskTake(int reqId, String taskId, CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.JSON_REMOTE_TASK_ID, taskId);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        try {
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_TASK_TAKE,
                    jsonObject.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int taskManual(int reqId, String taskId, CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.JSON_REMOTE_TASK_ID, taskId);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        try {
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_TASK_MANUAL,
                    jsonObject.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int taskCancel(int reqId, String taskId, String cancelResult, CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.JSON_REMOTE_TASK_ID, taskId);
            jsonObject.put(Definition.JSON_REMOTE_CANCEL_RESULT, cancelResult);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        try {
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_TASK_CANCEL,
                    jsonObject.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * @param reqId
     * @param taskId   多个taskId用,隔开
     * @param listener
     * @return
     */
    public int taskSucc(int reqId, String taskId, CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.JSON_REMOTE_TASK_ID, taskId);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        try {
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_TASK_SUCC,
                    jsonObject.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int taskPosQrcode(int reqId, String qrcodeType, String posName, CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.JSON_REMOTE_QRCODE_TYPE, qrcodeType);
            jsonObject.put(Definition.JSON_REMOTE_POS_NAME, posName);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        try {
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_TASK_POS_QRCODE,
                    jsonObject.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int cabinetPutGoodsStatus(int reqId, String taskId, String stationType, String stationId, String stationOrderId, CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.JSON_REMOTE_TASK_ID, taskId);
            jsonObject.put(Definition.JSON_REMOTE_STATION_TYPE, stationType);
            jsonObject.put(Definition.JSON_REMOTE_STATION_ID, stationId);
            jsonObject.put(Definition.JSON_REMOTE_STATION_ORDER_ID, stationOrderId);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        try {
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_CABINET_PUT_GOOD_STATUS,
                    jsonObject.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获取多机参数
     */
    public int getMultiRobotSettingConfig(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_MULTI_ROBOT_CONFIG, null, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 发送多机消息，默认下发消息为非测试消息
     */
    public int sendMultiRobotMessage(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_SEND_MULTI_ROBOT_MESSAGE, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 切换轮子控制模式.该模式下导航会放弃对轮子的控制权
     */
    public int switchWheelControlMode(int reqId, Definition.WheelControlMode controlMode) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.JSON_NAVI_WHEEL_MODE, controlMode.getValue());
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_WHEEL_CONTROL_MODE, jsonObject.toString(),
                    false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                    null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int getNaviPathInfo(int reqId, Pose startPose, Pose endPose, CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        JSONObject startPoseJson = new JSONObject();
        JSONObject endPoseJson = new JSONObject();
        try {
            startPoseJson.put("x", startPose.getX());
            startPoseJson.put("y", startPose.getY());
            startPoseJson.put("theta", startPose.getTheta());

            endPoseJson.put("x", endPose.getX());
            endPoseJson.put("y", endPose.getY());
            endPoseJson.put("theta", endPose.getTheta());

            jsonObject.put("start_pose", startPoseJson);
            jsonObject.put("end_pose", endPoseJson);

            CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_NAVI_PATH_INFO, jsonObject.toString(),
                    false);

            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int getNaviPathInfoToGoals(int reqId, List<String> goalsName, CommandListener listener) {

        if (goalsName == null || goalsName.size() == 0) {
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
        JSONArray array = new JSONArray();
        for (String goalName : goalsName) {
            JSONObject object = new JSONObject();
            try {
                object.put("name", goalName);
            } catch (JSONException e) {
                e.printStackTrace();
                return Definition.CMD_SEND_ERROR_UNKNOWN;
            }
            array.put(object);
        }
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_NAVI_PATH_INFO_TO_GOALS, array.toString(),
                false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
    }

    /**
     * 获取A点到B的巡线路径
     * <p> 此方法必须打开巡线，路径颗粒度最小为5cm，该方法有方向
     *
     * @param reqId
     * @param startPose 起始点位
     * @param endPose   结束点位
     * @param listener  回调函数
     * @return api是否成功执行
     */
    public int getNaviPathDetail(int reqId, Pose startPose, Pose endPose, CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        JSONObject startPoseJson = new JSONObject();
        JSONObject endPoseJson = new JSONObject();
        try {
            startPoseJson.put("x", startPose.getX());
            startPoseJson.put("y", startPose.getY());
            startPoseJson.put("theta", startPose.getTheta());

            endPoseJson.put("x", endPose.getX());
            endPoseJson.put("y", endPose.getY());
            endPoseJson.put("theta", endPose.getTheta());

            jsonObject.put("start_pose", startPoseJson);
            jsonObject.put("end_pose", endPoseJson);

            CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_NAVI_PATH_DETAIL, jsonObject.toString(),
                    false);

            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获取机器人从当前位置移动到目标点，经过闸机线的起点终点坐标
     * <p> 此方法必须打开巡线，路径颗粒度最小为5cm，该方法有方向，返回点位小于2个表示不通过闸机
     *
     * @param reqId
     * @param destination 目标点名称
     * @param listener    回调函数
     * @return api是否成功执行
     */
    public int getGatePassingRoute(int reqId, String destination, CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.JSON_NAVI_DESTINATION_NAME, destination);

            CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_NAVI_GATE_PASSING_ROUTE, jsonObject.toString(),
                    false);

            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获取机器人从当前位置移动到目标点，经过闸机线的起点终点坐标
     * <p> 此方法必须打开巡线，路径颗粒度最小为5cm，该方法有方向，返回点位小于2个表示不通过闸机
     *
     * @param reqId
     * @param targetPose 目标点Pose
     * @param listener   回调函数
     * @return api是否成功执行
     */
    public int getGatePassingRoute(int reqId, Pose targetPose, CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("x", targetPose.getX());
            jsonObject.put("y", targetPose.getY());
            jsonObject.put("theta", targetPose.getTheta());

            CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_NAVI_GATE_PASSING_ROUTE, jsonObject.toString(),
                    false);

            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 闸机导航
     */
    public int startGateNavgation(int reqId,Pose firstPose, Pose secondPose, String destination,
                                  double linearSpeed, double angularSpeed,
                                  ActionListener listener) {
        NavigationAdvancedBean bean = new NavigationAdvancedBean();
        bean.setReqId(reqId);
        bean.setDestination(destination);
        bean.setGateFirstPose(firstPose);
        bean.setGateSecondPose(secondPose);
        bean.setLinearSpeed(linearSpeed);
        bean.setAngularSpeed(angularSpeed);
        bean.setNaviStrategy(Definition.AdvNaviStrategy.GATE);
        String jsonStr = mGson.toJson(bean);
        return startAction(reqId, Definition.ACTION_ADVANCED_NAVIGATION, jsonStr, listener);
    }

    /**
     * 多闸机导航
     */
    public int startMultiGateNavigation(int reqId, List<GatePairPose> gatePairPoses, String destination, CommandListener listener) {
        NavigationAdvancedBean bean = new NavigationAdvancedBean();
        bean.setReqId(reqId);
        bean.setDestination(destination);
        bean.setGatePairPoses(gatePairPoses);
        bean.setNaviType(NavigationAdvancedBean.TYPE_POSE); // 过多闸机都用点位导航
        bean.setNaviStrategy(Definition.AdvNaviStrategy.GATE);
        String jsonStr = mGson.toJson(bean);
        return startAction(reqId, Definition.ACTION_ADVANCED_NAVIGATION, jsonStr, listener);
    }

    /**
     * 获取人脸是否戴口罩
     *
     * @param reqId
     * @param id       人脸id
     * @param listener 回调函数
     * @return
     */
    public int getMaskInfoById(int reqId, int id, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_ID, id);
            param.put(Definition.JSON_HEAD_COUNT, 1);

            CommandBean bean = new CommandBean(Definition.CMD_HEAD_GET_MASK_INFO_BY_ID, param
                    .toString(), false);
            return startAction(reqId, Definition.ACTION_HEAD_GET_MASK_INFO_BY_ID, mGson.toJson
                                                                                               (bean), parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int getAdditionalDevices(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_ADDITIONAL_DEVICES, "", false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 设置映射点
     */
    public int setMappingPlace(int reqId, String mapName, int placeType, String placeCnName,
                               String placeId, String mappingPlaceId, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_MAP_NAME, mapName);
            param.put(Definition.JSON_PLACE_TYPE, placeType);
            param.put(Definition.JSON_PLACE_CN_NAME, placeCnName);
            param.put(Definition.JSON_NAVI_PLACE_ID, placeId);
            param.put(Definition.JSON_MAPPING_PLACE_ID, mappingPlaceId);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_MAPPING_PLACE, param
                    .toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson
                                                                             (bean), parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获取映射信息
     */
    public int getMappingInfo(int reqId, String mapName, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_MAP_NAME, mapName);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_MAPPING_INFO, param
                    .toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson
                                                                             (bean), parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 是否开启底盘雷达数据上报的开关
     *
     * @param reqId
     * @param enabled
     * @return
     */
    public int setNavigationLineData(int reqId, boolean enabled) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_ENALBE_LINE_DATA, enabled);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_ENABLE_REPORT_LINE_DATA, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 是否开启RGBD深度图像上报的开关
     *
     * @param reqId
     * @param enabled
     * @return
     */
    @Deprecated
    public int setNavigationDepthImage(int reqId, boolean enabled) {
        return setNavigationDepthImage(reqId, enabled, Definition.DEPTH_DEVICE.DEPTH_1);
    }

    /**
     * 是否开启深度图像上报的开关
     *
     * @param reqId
     * @param device
     * @param enabled
     * @return
     */
    public int setNavigationDepthImage(int reqId, boolean enabled, Definition.DEPTH_DEVICE device) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_ENALBE_DEPTH_IMAGE, enabled);
            param.put(Definition.JSON_NAVI_DEPTH_DEVICE, device.getId());
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_ENABLE_DEPTH_IMAGE, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 是否开启Top IR图像上报的开关
     *
     * @param reqId
     * @param enabled
     * @return
     */
    @Deprecated
    public int setNavigationIRImage(int reqId, boolean enabled) {
        return setNavigationIRImage(reqId, enabled, Definition.IR_DEVICE.TOP_IR);
    }

    /**
     * 是否开启 IR图像上报的开关
     *
     * @param reqId
     * @param device
     * @param enabled
     * @return
     */
    public int setNavigationIRImage(int reqId, boolean enabled, Definition.IR_DEVICE device) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_ENALBE_IR_IMAGE, enabled);
            param.put(Definition.JSON_NAVI_IR_DEVICE, device.getId());
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_ENABLE_IR_IMAGE, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 控制消毒机器人模块的供电控制接口
     *
     * @enabled true will charged, false will discharged.
     */
    public int setXDPowerEnable(int reqId, boolean enabled, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_XD_POWER, enabled);
            CommandBean bean = new CommandBean(Definition.CMD_CAN_SET_XD_POWER, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 消毒豹项目控制消毒模块的风扇
     *
     * @param enable true will open ; false will close
     */
    public int setXDFanEnable(int reqId, boolean enable, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_XD_FAN, enable);
            CommandBean bean = new CommandBean(Definition.CMD_CAN_SET_XD_FAN_ENABLE, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    /**
     * 消毒豹项目控制消毒模块的雾化强度
     *
     * @param rank rank will be JSON_CAN_XD_RANK_CLOSE or JSON_CAN_XD_RANK_OPEN_SMALL or JSON_CAN_XD_RANK_OPEN_LARGE
     */
    public int setXDRank(int reqId, int rank, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_XD_RANK, rank);
            CommandBean bean = new CommandBean(Definition.CMD_CAN_SET_XD_RANK, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 系统接管触发回充, 功能和RobotSettings 去充电一样.
     */
    public int goCharging(int reqId) {
        //走系统接管充电
        this.ctx.sendBroadcast(new Intent(InternalDef.ACTION_START_CHARGE));
        return Definition.RESULT_OK;
    }

    public int startDataSetRecord(int reqId, String sensor) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_SENSOR_LIST, sensor);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_START_DATA_SET_RECORD, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int stopDataSetRecord(int reqId, boolean isLocalData) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_SAVE_LOCAL_SENSOR_DATA, isLocalData);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_STOP_DATA_SET_RECORD, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int uploadNaviDataSet(int reqId, String sensor) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_SENSOR_LIST, sensor);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_UPLOAD_NAVI_DATA_SET, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 判断电梯功能是否启用
     */
    public boolean isElevatorControlEnable() {
        int enableValue = RobotSettingApi.getInstance()
                                         .getRobotInt(Definition.ROBOT_SETTING_ELEVATOR_CONTROL_ENABLED);
        Log.d(TAG, "isElevatorControlEnabled : " + enableValue);
        return enableValue == 1;
    }

    /**
     * 获得充电区域配置信息
     *
     * @return 全部配置信息
     */
    public int queryChargeAreaConfig(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_QUERY_CHARGE_AREA_CONFIG, null, false);
            return startAction(reqId, Definition.ACTION_NAVI_QUERY_CHARGE_AREA_CONFIG, mGson.toJson(bean),
                    parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;

    }

    /**
     * 新增充电区域配置信息
     *
     * @param param List<ChargeArea> 形式，ChargeArea对象可参考NavigationService结构
     */
    public int insertChargeAreaConfig(int reqId, String param, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_INSERT_CHARGE_AREA_CONFIG, param, false);
            return startAction(reqId, Definition.ACTION_NAVI_INSERT_CHARGE_AREA_CONFIG, mGson.toJson(bean),
                    parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 更新充电区域配置
     *
     */
    public int updateChargeAreaConfig(int reqId, String chargeArea, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_VALUE, chargeArea);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_UPDATE_CHARGE_AREA_CONFIG, param.toString(), false);
            return startAction(reqId, Definition.ACTION_NAVI_UPDATE_CHARGE_AREA_CONFIG, mGson.toJson(bean), parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    /**
     * 删除充电区域配置
     *
     */
    public int removeChargeAreaConfig(int reqId, String chargeArea, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_REMOVE_CHARGE_AREA_CONFIG, chargeArea, false);
            return startAction(reqId, Definition.ACTION_NAVI_REMOVE_CHARGE_AREA_CONFIG, mGson.toJson(bean), parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获得多层配置信息
     *
     * @return 全部配置信息
     */
    public int queryMultiFloorConfig(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_QUERY_MULTI_FLOOR_CONFIG, null, false);
            return startAction(reqId, Definition.ACTION_NAVI_QUERY_MULTI_FLOOR_CONFIG, mGson.toJson(bean),
                    parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;

    }


    /**
     * 获得多层配置和对应地图里的所有点位
     */
    public int getMultiFloorConfigAndPose(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_MULTI_FLOOR_CONFIG_AND_POSE, null, false);
            return startAction(reqId, Definition.ACTION_NAVI_GET_MULTI_FLOOR_CONFIG_AND_POSE, mGson.toJson(bean), parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获得多层配置和对应地图里的点位
     * （不包含特殊点位）
     */
    public int getMultiFloorConfigAndCommonPose(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_MULTI_FLOOR_CONFIG_AND_COMMON_POSE, null, false);
            return startAction(reqId, Definition.ACTION_NAVI_GET_MULTI_FLOOR_CONFIG_AND_COMMON_POSE, mGson.toJson(bean), parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 新增多楼层配置信息
     *
     * @param param List<MultiFloorInfo> 形式，MultiFloorInfo对象可参考NavigationService结构
     *              ** 必须有floorId、mapId
     */
    public int insertMultiFloorConfig(int reqId, String param, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_INSERT_MULTI_FLOOR_CONFIG, param, false);
            return startAction(reqId, Definition.ACTION_NAVI_INSERT_MULTI_FLOOR_CONFIG, mGson.toJson(bean),
                    parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    /**
     * 更新多层配置中，某楼层地图状态类型
     *
     * @param floorId    floorId
     * @param floorState 要修改的状态类型
     */
    public int updateMultiFloorConfigFloorState(int reqId, int floorId, String mapName, int floorState, CommandListener listener) {
        try {
            JSONObject valueObj = new JSONObject();
            valueObj.put("floorId", floorId);
            valueObj.put("mapName", mapName);
            valueObj.put("floorState", floorState);

            JSONObject param = new JSONObject();
            param.put(Definition.JSON_TYPE, Definition.JSON_NAVI_MULTI_FLOOR_UPDATE_FLOOR_STATE);
            param.put(Definition.JSON_VALUE, valueObj.toString());
            return updateMultiFloorConfig(reqId, param.toString(), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    /**
     * 更新多层配置中，楼层映射id，该id用于lora梯控
     *
     * @param floorId    floorId
     * @param mapName
     * @param floorIndex 楼层映射id
     */
    public int updateMultiFloorConfigFloorIndex(int reqId, int floorId, String mapName, int floorIndex, CommandListener listener) {
        try {
            JSONObject valueObj = new JSONObject();
            valueObj.put("floorId", floorId);
            valueObj.put("mapName", mapName);
            valueObj.put("floorIndex", floorIndex);

            JSONObject param = new JSONObject();
            param.put(Definition.JSON_TYPE, Definition.JSON_NAVI_MULTI_FLOOR_UPDATE_FLOOR_INDEX);
            param.put(Definition.JSON_VALUE, valueObj.toString());
            return updateMultiFloorConfig(reqId, param.toString(), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    /**
     * 更新多层配置中，楼层别名
     *
     * @param floorId    floorId
     * @param mapName
     * @param floorAlias 楼层别名
     */
    public int updateMultiFloorConfigFloorAlias(int reqId, int floorId, String mapName, String floorAlias, CommandListener listener) {
        try {
            JSONObject valueObj = new JSONObject();
            JSONObject param = new JSONObject();
            valueObj.put("floorId", floorId);
            valueObj.put("mapName", mapName);
            valueObj.put("floorAlias", floorAlias);

            param.put(Definition.JSON_TYPE, Definition.JSON_NAVI_MULTI_FLOOR_UPDATE_FLOOR_ALIAS);
            param.put(Definition.JSON_VALUE, valueObj.toString());
            return updateMultiFloorConfig(reqId, param.toString(), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    /**
     * 更新多层配置中，该层可用的电梯
     *
     * @param floorId            floorId
     * @param mapName
     * @param availableElevators 可用的电梯
     */
    public int updateMultiFloorConfigElevators(int reqId, int floorId, String mapName, List<String> availableElevators, CommandListener listener) {
        try {
            JSONObject valueObj = new JSONObject();
            JSONObject param = new JSONObject();
            valueObj.put("floorId", floorId);
            valueObj.put("mapName", mapName);
            valueObj.put("availableElevators", availableElevators);

            param.put(Definition.JSON_TYPE, Definition.JSON_NAVI_MULTI_FLOOR_UPDATE_ELEVATORS);
            param.put(Definition.JSON_VALUE, valueObj.toString());
            return updateMultiFloorConfig(reqId, param.toString(), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    /**
     * 更新多层配置中，某楼层地图状态类型
     *
     * @param param 包含更新的type，及数据value
     *              数据中必须包含floorId、mapId用于查询，其他要更新的字段名称参考NavigationService的MultiFloorInfo
     */
    public int updateMultiFloorConfig(int reqId, String param, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_UPDATE_MULTI_FLOOR_CONFIG, param, false);
            return startAction(reqId, Definition.ACTION_NAVI_UPDATE_MULTI_FLOOR_CONFIG, mGson.toJson(bean), parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    /**
     * 根据id删除某层配置
     *
     * @param floorId floorId
     * @param mapName 地图名
     */
    public int removeMultiFloorConfigById(int reqId, int floorId, String mapName, CommandListener listener) {
        try {
            JSONObject valueObj = new JSONObject();
            valueObj.put("floorId", floorId);
            valueObj.put("mapName", mapName);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_REMOVE_MULTI_FLOOR_CONFIG, valueObj.toString(), false);
            return startAction(reqId, Definition.ACTION_NAVI_REMOVE_MULTI_FLOOR_CONFIG, mGson.toJson(bean), parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 打开电梯门
     */
    public int openElevatorDoor(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_CONTROL_OPEN_ELEVATOR_DOOR,
                    null, false);
            return startAction(reqId, Definition.ACTION_CONTROL_OPEN_ELEVATOR_DOOR,
                    mGson.toJson(bean), parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 关闭电梯门
     */
    public int closeElevatorDoor(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_CONTROL_CLOSE_ELEVATOR_DOOR,
                    null, false);
            return startAction(reqId, Definition.ACTION_CONTROL_CLOSE_ELEVATOR_DOOR,
                    mGson.toJson(bean), parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 释放电梯
     */
    public int releaseElevator(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_CONTROL_RELEASE_ELEVATOR,
                    null, false);
            return startAction(reqId, Definition.ACTION_CONTROL_RELEASE_ELEVATOR,
                    mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 呼叫电梯到指定楼层
     */
    public int callElevatorToTargetFloor(int reqId, int currentFloor, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CONTROL_TARGET_FLOOR, currentFloor);
            CommandBean bean = new CommandBean(Definition.CMD_CONTROL_CALL_ELEVATOR, param.toString(), false);
            return startAction(reqId, Definition.ACTION_CONTROL_CALL_ELEVATOR,
                    mGson.toJson(bean), parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获取电梯状态
     */
    public int getElevatorStatus(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_CONTROL_GET_ELEVATOR_STATUS,
                    null, false);
            return startAction(reqId, Definition.ACTION_CONTROL_GET_ELEVATOR_STATUS,
                    mGson.toJson(bean), parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 乘梯导航
     */
    public int startElevatorNavigation(int reqId, String destination, int floor,
                                       ActionListener listener) {
        return startElevatorNavigation(reqId, destination, floor, false, listener);
    }

    public int startElevatorNavigation(int reqId, String destination, int floor,
                                       boolean isAdjustAngle, ActionListener listener) {
        return startElevatorNavigation(reqId, destination, floor, isAdjustAngle,
                SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED,
                SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED, listener);
    }

    /**
     * 乘梯导航
     * @param destination 目的地
     * @param floor 目的地楼层
     * @param isAdjustAngle 是否适应导航结束时朝向的角度，默认 false:即归正目标点设定角度
     * @param listener 监听回调
     * @return
     */
    public int startElevatorNavigation(int reqId, String destination, int floor,
                                       boolean isAdjustAngle, double linearSpeed,
                                       double angularSpeed, ActionListener listener) {
        NavigationAdvancedBean bean = new NavigationAdvancedBean();
        bean.setReqId(reqId);
        bean.setDestination(destination);
        bean.setTargetFloor(floor);
        bean.setAdjustAngle(isAdjustAngle);
        bean.setLinearSpeed(linearSpeed);
        bean.setAngularSpeed(angularSpeed);
        bean.setNaviStrategy(Definition.AdvNaviStrategy.ELEVATOR);
        String jsonStr = mGson.toJson(bean);

        return startAction(reqId, Definition.ACTION_ADVANCED_NAVIGATION, jsonStr, listener);
    }

    public int startElevatorNavigation(int reqId, String destination, int floor,
                                       boolean isAdjustAngle, double linearSpeed, double angularSpeed,
                                       boolean isReversePoseTheta, ActionListener listener) {
        NavigationAdvancedBean bean = new NavigationAdvancedBean();
        bean.setReqId(reqId);
        bean.setDestination(destination);
        bean.setTargetFloor(floor);
        bean.setAdjustAngle(isAdjustAngle);
        bean.setLinearSpeed(linearSpeed);
        bean.setAngularSpeed(angularSpeed);
        bean.setIsReversePoseTheta(isReversePoseTheta);
        bean.setNaviStrategy(Definition.AdvNaviStrategy.ELEVATOR);
        String jsonStr = mGson.toJson(bean);

        return startAction(reqId, Definition.ACTION_ADVANCED_NAVIGATION, jsonStr, listener);
    }

    /**
     * 多机导航策略
     *
     * @param destination      目的地
     * @param advNaviStrategy  导航策略,NULL: 不使用多机导航
     * @param standbyDesList   备用目的地列表
     * @param navigationParams 导航参数
     * @param listener         listener
     */
    public int startMultiRobotNavigation(int reqId,
                                         String destination,
                                         Definition.AdvNaviStrategy advNaviStrategy,
                                         List<String> standbyDesList,
                                         String navigationParams,
                                         ActionListener listener) {

        NavigationAdvancedBean bean = new NavigationAdvancedBean();
        bean.setReqId(reqId);
        bean.setDestination(destination);
        bean.setNaviStrategy(advNaviStrategy);
        bean.setStandbyDesList(standbyDesList);
        bean.setNavigationParams(navigationParams);
        String jsonStr = mGson.toJson(bean);

        return startAction(reqId, Definition.ACTION_ADVANCED_NAVIGATION, jsonStr, listener);
    }

    /**
     * 乘梯导航
     */
    public int startElevatorNavigation(int reqId, String destination, int floor, double coordinateDeviation,
                                       long time, double linearSpeed, double angularSpeed,
                                       boolean isAdjustAngle, double destinationRange,
                                       int wheelOverCurrentRetryCount, long multipleWaitTime,
                                       int priority, double linearAcceleration, double angularAcceleration,
                                       ActionListener listener) {
        NavigationAdvancedBean bean = new NavigationAdvancedBean();
        bean.setReqId(reqId);
        bean.setDestination(destination);
        bean.setTargetFloor(floor);
        bean.setTime(time);
        bean.setCoordinateDeviation(coordinateDeviation);
        bean.setLinearSpeed(linearSpeed);
        bean.setAngularSpeed(angularSpeed);
        bean.setAdjustAngle(isAdjustAngle);
        bean.setDestinationRange(destinationRange);
        bean.setWheelOverCurrentRetryCount(wheelOverCurrentRetryCount);
        bean.setMultipleWaitTime(multipleWaitTime);
        bean.setPriority(priority);
        bean.setLinearAcceleration(linearAcceleration);
        bean.setAngularAcceleration(angularAcceleration);
        String jsonStr = mGson.toJson(bean);
        bean.setNaviStrategy(Definition.AdvNaviStrategy.ELEVATOR);

        return startAction(reqId, Definition.ACTION_ADVANCED_NAVIGATION, jsonStr, listener);
    }

    public int stopAdvanceNavigation(int reqId) {
        return stopAction(reqId, Definition.ACTION_ADVANCED_NAVIGATION, true);
    }

    /**
     * 是否存在TopIR
     *
     * @return
     */
    public boolean hasTopIR() {
        try {
            return mModuleRegistry != null && mModuleRegistry.hasTopIR();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 控制UVC托盘摄像头模块的识别, 启动单次识别
     */
    public int uvcSingleClassify(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_UVC_CAMERA_SINGLE_CLASSIFY, "", false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 线激光托盘摄像头，使用制具自检
     */
    public int trayLaserInspection(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_TRAY_LASER_INSPECTION, "", false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 控制UVC托盘摄像头模块的识别, 启动持续识别
     */
    public int uvcContinueClassify(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_UVC_CAMERA_CONTINUE_CLASSIFY, "", false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 控制UVC托盘摄像头模块的识别, 停止持续识别
     */
    public int uvcStopContinueClassify(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_UVC_CAMERA_CONTINUE_CLASSIFY_STOP, "", false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * UVC托盘摄像头的设备的连接状态查询
     */
    public int queryUvcCameraConnectedStatus(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_QUERY_UVC_CAMERA_CONNECTED_STATUS, "", false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 是否存在回充IR
     *
     * @return
     */
    public boolean hasChargeIR() {
        try {
            return mModuleRegistry != null && mModuleRegistry.hasChargeIR();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 是否使用自动效果灯带接口
     *
     * @return
     */
    public boolean isUseAutoEffectLed() {
        try {
            return mModuleRegistry != null && mModuleRegistry.isUseAutoEffectLed();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 自动效果底盘灯带
     */
    public int setBottomLedEffect(int reqId, int effect, CommandListener listener) {
        if (!isUseAutoEffectLed()) {
            if (listener != null){
                listener.onResult(Definition.CMD_SEND_ERROR_RES_NOT_HOLD, Definition.FAILED, "");
            }
            return Definition.CMD_SEND_ERROR_RES_NOT_HOLD;
        }
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_LED_EFFECT, effect);
            CommandBean bean = new CommandBean(Definition.CMD_CAN_BOTTOM_LED_EFFECT, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 是否有锁骨灯
     * @return
     */
    public boolean isHasClavicleLed() {
        try {
            return mModuleRegistry != null && mModuleRegistry.isHasClavicleLed();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 是否有胸口灯
     * @return
     */
    public boolean isHasChestLed() {
        try {
            return mModuleRegistry != null && mModuleRegistry.isHasChestLed();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 控制IobLed灯，即Saiph Pro的锁骨灯
     *"Iob"应该是拼写错误，应该是Lobby，表示胸口部位的灯带。
     * 对招财Pro，豹小递送是锁骨灯；小秘2是胸口灯
     * Slim不支持该接口，靠业务区分
     */
    public int setClavicleLedEffect(int reqId, int effect, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_LED_EFFECT, effect);
            CommandBean bean = new CommandBean(Definition.CMD_CAN_CLAVICLE_LED_EFFECT, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 是否有托盘指示灯
     *
     * @return
     */
    public boolean isHasTrayLight() {
        try {
            return mModuleRegistry != null && mModuleRegistry.isHasTrayLight();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * Pro托盘三层指示灯的控制
     *
     * @param effect must be defined in Definition.
     */
    public int setTrayLightEffect(int reqId, int effect, CommandListener listener) {
        if (!isHasTrayLight()) {
            if (listener != null) {
                listener.onResult(Definition.CMD_SEND_ERROR_RES_NOT_HOLD, Definition.FAILED, "");
            }
            return Definition.CMD_SEND_ERROR_RES_NOT_HOLD;
        }
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_TRAY_LIGHT_EFFECT, effect);
            CommandBean bean = new CommandBean(Definition.CMD_CAN_TRAY_LIGHT_EFFECT, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * Slim托盘灯控制（设备接口不一样），靠业务调用区分
     */
    public int setTrayLedEffect(int reqId, int effect, CommandListener listener) {
        if (!isHasTrayLight()) {
            //不想增加新判断了，靠相同接口，判断的还是是否存在托盘Led
            if (listener != null){
                listener.onResult(Definition.CMD_SEND_ERROR_RES_NOT_HOLD, Definition.FAILED, "");
            }
            return Definition.CMD_SEND_ERROR_RES_NOT_HOLD;
        }
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_TRAY_AUTO_LED_EFFECT, effect);
            CommandBean bean = new CommandBean(Definition.CMD_CAN_TRAY_LED_EFFECT, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 是否使用ProZcbLed灯带接口
     *
     * @return
     */
    @Deprecated
    public boolean isUseProZcbLed() {
        try {
            return mModuleRegistry != null && mModuleRegistry.isUseProZcbLed();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 控制ZcbLed灯，即Saiph Pro的底盘灯带
     * Zcb:招财豹
     */
    @Deprecated
    public int setProZcbLedEffect(int reqId, int proZcbEffect, CommandListener listener) {
        if (!isUseProZcbLed()) {
            if (listener != null) {
                listener.onResult(Definition.CMD_SEND_ERROR_RES_NOT_HOLD, Definition.FAILED, "");
            }
            return Definition.CMD_SEND_ERROR_RES_NOT_HOLD;
        }
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_PRO_ZCB_EFFECT, proZcbEffect);
            CommandBean bean = new CommandBean(Definition.CMD_ZCB_LED_EFFECT, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 是否有锁骨灯
     *
     * @return
     */
    @Deprecated
    public boolean isHasClavicleLight() {
        try {
            return mModuleRegistry != null && mModuleRegistry.isHasClavicleLight();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 是否有胸口灯
     *
     * @return
     */
    @Deprecated
    public boolean isHasChestLight() {
        try {
            return mModuleRegistry != null && mModuleRegistry.isHasChestLight();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 控制IobLed灯，即Saiph Pro的锁骨灯
     * "Iob"应该是拼写错误，应该是Lobby，表示胸口部位的灯带。
     */
    @Deprecated
    public int setProIobLedEffect(int reqId, int proIobLedEffect, CommandListener listener) {

        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_PRO_IOB_EFFECT, proIobLedEffect);
            CommandBean bean = new CommandBean(Definition.CMD_IOB_LED_EFFECT, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 是否有托盘指示灯
     *
     * @return
     */
    @Deprecated
    public boolean isHasProTrayLED() {
        try {
            return mModuleRegistry != null && mModuleRegistry.isHasProTrayLED();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * Pro托盘三层指示灯的控制
     *
     * @param trayLedEffect must be defined in Definition.
     *                      like TRAYLEDS_TRAY01ON , TRAYLEDS_TRAY01OFF , TRAYLEDS_TRAY02ON , TRAYLEDS_TRAY02OFF ,
     *                      TRAYLEDS_TRAY03ON , TRAYLEDS_TRAY03OFF , TRAYLEDS_ALLON , TRAYLEDS_ALLOFF
     */
    @Deprecated
    public int setProTrayLedEffect(int reqId, int trayLedEffect, CommandListener listener) {
        if (!isHasProTrayLED()) {
            if (listener != null) {
                listener.onResult(Definition.CMD_SEND_ERROR_RES_NOT_HOLD, Definition.FAILED, "");
            }
            return Definition.CMD_SEND_ERROR_RES_NOT_HOLD;
        }
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_TRAY_LED_EFFECT, trayLedEffect);
            CommandBean bean = new CommandBean(Definition.CMD_TRAY_LED_EFFECT, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 地图类型是否包含视觉
     *
     * @param reqId
     * @param mapName  地图名称
     * @param listener
     * @return
     */
    public int isMapHasVision(int reqId, String mapName, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_MAP_NAME, mapName);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_MAP_HAS_VISION, param.toString(), false);
            return startAction(reqId, Definition.ACTION_NAVI_MAP_HAS_VISION, mGson.toJson(bean), listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    /**
     * 上传额外文件
     */
    public int uploadExtraFilePkg(int reqId, String mapName, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_UPLOAD_EXTRA_FILE_PKG, mapName, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 下载额外文件
     */
    public int downloadExtraFilePkg(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_DOWNLOAD_EXTRA_FILE_PKG, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 保存额外信息
     */
    public int setExtraFileData(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_EXTRA_FILE_DATA, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 压缩地图文件
     */
    public int zipMapFile(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_ZIP_MAP_FILE, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 解压地图文件
     */
    public int unzipMapFile(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_UNZIP_MAP_FILE, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获得导航角速度
     */
    public int getNaviAngSpeed(int reqId, String speed, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_NAVI_ANGLE_SPEED, speed, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 下载地图包的所有流程
     */
    public int downLoadWholeMapPkg(int reqId, String params, CommandListener listener) {
        try {
            return startAction(reqId, Definition.ACTION_DOWNLOAD_WHOLE_MAP_PKG, params, listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 终止下载地图包的所有流程
     */
    public int stopDownLoadWholeMapPkg(int reqId) {
        try {
            return stopAction(reqId, Definition.ACTION_DOWNLOAD_WHOLE_MAP_PKG, true);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获取本地地图信息列表，从数据库获取
     */
    public int getLocalMapInfoList(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_LOCAL_MAP_INFO_LIST, "", false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_LOCAL_MAP_INFO_LIST, mGson.toJson(bean), listener);
    }

    /**
     * 当前机型是否有Mono
     */
    public boolean hasMono() {
        try {
            return mModuleRegistry != null && mModuleRegistry.hasMono();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 当前机型是否有TopMono
     *
     * @return
     */
    public boolean hasTopMono() {
        try {
            return mModuleRegistry != null && mModuleRegistry.hasTopMono();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public boolean isSupportElevator() {
        try {
            return mModuleRegistry != null && mModuleRegistry.isSupportElevator();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * Carry是否支持线激光摄像头（一层）
     * @return
     */
    public boolean hasHeightLimitCamera() {
        try {
            return mModuleRegistry != null && mModuleRegistry.hasHeightLimitCamera();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 主动问题上报
     *
     * @param occurrenceTime
     * @param issueType
     * @param packageName
     * @param pageName
     * @param description
     */
    public int proactiveProblemReport(long occurrenceTime, String issueType, String packageName, String pageName, String description) {

        Intent intent = new Intent(Definition.INTENT_PROBLEM_REPORT);

        intent.putExtra(Definition.KEY_PROBLEM_OCCURRENCE_TIME, occurrenceTime);
        intent.putExtra(Definition.KEY_PROBLEM_ISSUE_TYPE, issueType);
        intent.putExtra(Definition.KEY_PROBLEM_PACKAGE_NAME, packageName);
        intent.putExtra(Definition.KEY_PROBLEM_PAGE_NAME, pageName);
        intent.putExtra(Definition.KEY_PROBLEM_DESCRIPTION, description);

        this.ctx.sendBroadcast(intent);
        return Definition.RESULT_OK;
    }

    /**
     * Pro含有额外的电动门外设时，控制电动门接口
     *
     * @param doorCmd must be defined in Definition , only support one of below commands :
     *                CAN_DOOR_DOOR1_DOOR2_OPEN; CAN_DOOR_DOOR1_DOOR2_CLOSE ;
     *                CAN_DOOR_DOOR3_DOOR4_OPEN; CAN_DOOR_DOOR3_DOOR4_CLOSE;
     *                CAN_DOOR_ALL_OPEN; CAN_DOOR_ALL_CLOSE;
     *                发送指令前，先通过mApi.registerStatusListener(STATUS_CAN_ELECTRIC_DOOR_CTRL,listener)
     *                实时获取控制指令的结果状态值
     */
    public int setElectricDoorCtrl(int reqId, int doorCmd, CommandListener listener) {
        if (!FileUtils.hasElectricDoor()) {
            if (listener != null) {
                listener.onResult(Definition.CMD_SEND_ERROR_RES_NOT_HOLD, Definition.FAILED, "");
            }
            return Definition.CMD_SEND_ERROR_RES_NOT_HOLD;
        }
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_ELECTRIC_DOOR_CTRL, doorCmd);
            CommandBean bean = new CommandBean(Definition.CMD_CAN_ELECTRIC_DOOR_CTRL, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * Pro电动门外设时，查询电动门状态接口,各个门对应的查询结果状态值，defined in Definition:
     * CAN_DOOR_STATUS_OPEN = 51;
     * CAN_DOOR_STATUS_CLOSE = 68;
     * CAN_DOOR_STATUS_TIMEOUT = 85;
     * CAN_DOOR_STATUS_FG2LOW = 238;
     * CAN_DOOR_STATUS_RUNNING = 102;
     *
     * @param listener
     */
    public int getElectricDoorStatus(int reqId, CommandListener listener) {
        if (!FileUtils.hasElectricDoor()) {
            if (listener != null) {
                listener.onResult(Definition.CMD_SEND_ERROR_RES_NOT_HOLD, Definition.FAILED, "");
            }
            return Definition.CMD_SEND_ERROR_RES_NOT_HOLD;
        }
        try {
            CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_ELECTRIC_DOOR_STATUS, "", false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 主动问题上报
     *
     * @param occurrenceTime
     * @param issueType
     * @param packageName
     * @param pageName
     * @param description
     */
    public int setQueryFeedback(int reqId, String chatMaxSid, String feedbackResult, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CHAT_MAX_SID, chatMaxSid);
            param.put(Definition.JSON_FEEDBACK_RESULT, feedbackResult);
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_SET_QUERY_FEEDBACK, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获取点图分离标志位
     */
    public int getMapPosSeparate(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_GET_MAP_POS_SEPARATE, "", false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
    }

    /**
     * 通过本地内存卡目录下地图文件夹的地图名，得到对应地图信息(List<MapInfo>)
     */
    public int getMapInfoBySdMapNames(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_MAP_INFO_BY_SD_MAP_NAMES, null, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获取当前使用地图名
     */
    public int getCurrentMapName(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_CURRENT_MAP_NAME, null, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    /**
     * 获取机器人移动距离
     */
    public int getMotionDistance(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_MOTION_DISTANCE,
                    null, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                    parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int startControlElectricDoor(int reqId, int doorCmd, ActionListener listener) {
        try {
            ControlElectricDoorBean bean = new ControlElectricDoorBean(doorCmd);
            return startAction(reqId, Definition.ACTION_CONTROL_ELECTRIC_DOOR, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 上传多楼层信息给接待后台
     *
     * @param reqId
     * @param multiFloorInfo 多楼层信息json串
     * @param listener
     * @return
     */
    public int uploadMultiFloorInfo(int reqId, String multiFloorInfo, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_UPDATE_MULTI_FLOOR_INFO
                , multiFloorInfo, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 机器人任务信息同步接口
     *
     * @param reqId
     * @param paramJson
     * @param listener
     */
    public int deliveryRobotTask(int reqId, String paramJson, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_DELIVERY_ROBOTTASK
                , paramJson, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 机器人上下线通知接口
     *
     * @param reqId
     * @param paramJson
     * @param listener
     */
    public int deliveryRobotStatusNotify(int reqId, String paramJson, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_DELIVERY_ROBOTSTATUSNOTIFY
                , paramJson, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 机器人异常上报
     *
     * @param reqId
     * @param paramJson
     * @param listener
     */
    public int deliveryMonitorDetail(int reqId, String paramJson, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_DELIVERY_MONITORDETAIL
                , paramJson, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 机器人点评上报
     *
     * @param reqId
     * @param paramJson
     * @param listener
     */
    public int deliveryRobotComment(int reqId, String paramJson, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_DELIVERY_ROBOTCOMMENT
                , paramJson, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 打电话接口，（POC方案，后期要改为递送流程事件上报的方式）
     */
    public int deliveryRemoteCall(int reqId, String eventData, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_CALL
                , eventData, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 递送任务事件上报
     */
    public int deliveryReport(int reqId, String eventData, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_DELIVER_REPORT
                , eventData, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int pauseNavigation(int reqId, boolean isPause, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_PAUSE_NAVIGATION, isPause);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_PAUSE_NAVIGATION, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int startRadarAlign(int reqId, String poseName, boolean isNeedRetry, ActionListener listener) {
        try {
            RadarAlignBean bean = new RadarAlignBean();
            bean.setDestination(poseName);
            bean.setIsNeedRetry(isNeedRetry);
            return startAction(reqId, Definition.ACTION_RADAR_ALIGN, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int startRadarAlign(int reqId, String poseName, CommandListener listener) {
        try {
            RadarAlignBean bean = new RadarAlignBean();
            bean.setDestination(poseName);
            return startAction(reqId, Definition.ACTION_RADAR_ALIGN, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int startRadarAlign(int reqId, String poseName, long startAlignTimeout,
                               long retryDelayTime, long navigationTimeout, CommandListener listener) {
        try {
            RadarAlignBean bean = new RadarAlignBean();
            bean.setDestination(poseName);
            bean.setStartAlignTimeout(startAlignTimeout);
            bean.setRetryDelayTime(retryDelayTime);
            bean.setNavigationTimeout(navigationTimeout);
            return startAction(reqId, Definition.ACTION_RADAR_ALIGN, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int stopRadarAlign(int reqId) {
        return stopAction(reqId, Definition.ACTION_RADAR_ALIGN, true);
    }

    /**
     * 开始人体跟随.
     *
     * @param reqId      请求id
     * @param followId   跟随人id
     * @param lostFindTimeout  跟随上人之后，人丢失放弃跟随任务的超时时间，单位s
     * @param listener   回调函数
     * @return api是否执行成功
     * @platform carry
     */
    public int startNavigationFollowAction(int reqId, String followId, int lostFindTimeout, ActionListener listener) {
        NavigationFollowBean bean = new NavigationFollowBean();
        bean.setLostFindTimeout(lostFindTimeout);
        bean.setFollowId(followId);
        return startAction(reqId, Definition.ACTION_NAVIGATION_FOLLOW, mGson.toJson(bean), listener);
    }

    public int stopNavigationFollowAction(int reqId) {
        return stopAction(reqId, Definition.ACTION_NAVIGATION_FOLLOW, true);
    }

    /**
     * 通过照片文件识别二维码
     * @param file 二维码图片文件
     * @param listener
     */
    public int detectQrCode(int reqId, String file, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_DETECT_QRCODE_BY_PIC
                , file, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 禁止出图报警，系统不再接管出图报警
     *
     * @platform all
     */
    public void disableOutsideMapAlarm() {
        try {
            if (mModuleRegistry == null) {
                Log.d(TAG, "Disable outside map alarm failed, mModuleRegistry is null");
                return;
            }
            mModuleRegistry.disableOutsideMapAlarm();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 启用出图报警，系统接管出图报警
     *
     * @platform all
     */
    public void enableOutsideMapAlarm() {
        try {
            if (mModuleRegistry == null) {
                Log.d(TAG, "Enable outside map alarm failed, mModuleRegistry is null");
                return;
            }
            mModuleRegistry.enableOutsideMapAlarm();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取支持的创建地图类型
     */
    public String getCreateMapType() {
        try {
            if (mModuleRegistry == null) {
                Log.d(TAG, "Get create map type failed, mModuleRegistry is null");
                return "";
            }
            return mModuleRegistry.getCreateMapType();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 报给天工的——任务事件上报
     */
    public int taskEventReport(int reqId, String eventData, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_TASK_EVENT_REPORT
                , eventData, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 是否已经在电梯里
     * @return json对象
     * 字段一：Definition.JSON_TASK_EXEC_RESULT
     * Definition.ERROR_NOT_FOUND_ELEVATOR_PATH(没有找到电梯配置)
     * Definition.ERROR_NOT_ESTIMATE(没有定位)
     * Definition.RESULT_ROBOT_IN_ELEVATOR(机器人在电梯中)
     * Definition.RESULT_ROBOT_IN_ELEVATOR(机器人不在电梯中)
     * 字段二：Definition.JSON_TASK_EXEC_DATA
     * 错误详细信息，在电梯中，返回电梯名
     */
    public String isAlreadyInElevator() {
        try {
            return mModuleRegistry != null ? mModuleRegistry.isAlreadyInElevator() : null;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     *批量更新闸机关系信息
     * @param reqId
     * @param params
     * @param listener
     * @return
     */
    public int batchInsertOrUpdateGate(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_BATCH_INSERT_OR_UPDATE_GATE, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     *根据闸机主键批量删除指定关系信息
     * @param reqId
     * @param params
     * @param listener
     * @return
     */
    public int deleteByGateIds(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_DELETE_BY_GATE_IDS, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    /**
     *根据闸机线主键批量删除指定关系信息
     * @param reqId
     * @param params
     * @param listener
     * @return
     */
    public int deleteByLineIds(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_DELETE_BY_LINE_IDS, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    /**
     *删除传入闸机线主键以外的数据并返回删除的闸机关系集合
     * @param reqId
     * @param params
     * @param listener
     * @return
     */
    public int deleteExceptLineIds(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_DELETE_EXCEPT_LINE_IDS, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    /**
     *根据闸机ID获取闸机关系信息列表
     * @param reqId
     * @param params
     * @param listener
     * @return
     */
    public int findByGateIds(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_FIND_BY_GATE_IDS, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }
    /**
     *根据闸机线ID获取闸机关系信息列表
     * @param reqId
     * @param params
     * @param listener
     * @return
     */
    public int findByLineIds(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_FIND_BY_LINE_IDS, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获取所有闸机关系信息
     * @param reqId
     * @param listener
     * @return
     */
    public int getAllGateRelationData(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_FIND_GATE_RELATION, null, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获取机器人从当前位置移动到目标点，经过闸机线的起点终点坐标集合
     * <p> 此方法必须打开巡线，路径颗粒度最小为5cm，该方法有方向，返回点位小于2个表示不通过闸机
     *
     * @param reqId
     * @param destination  目标点名称
     * @param listener     回调函数
     * @return api是否成功执行
     */
    public int getMultiNaviGatePassingRoute(int reqId, String destination, CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.JSON_NAVI_DESTINATION_NAME, destination);

            CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_NAVI_MULT_GATE_PASSING_ROUTE, jsonObject.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    /**
     * 获取闸机服务中的所有本地闸机设备
     *
     */
    public int getAllGateDevices(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_GATE_GET_ALL_GATE_DEVICES, "", false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
    }

    /**
     * 更新闸机服务中闸机设备中，某个设备的绑定状态
     * @param params 闸机设备参数json串, 包含两个字段：gateIdentifier和isBindGateLine
     */
    public int updateBindGateLineState(int reqId, String params, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_GATE_UPDATE_BIND_LINE_STATE, params, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
    }

    /**
     * 批量更新闸机信息
     * @param params 闸机设备信息参数json
     */
    public int updateGateDevicesInfo(int reqId, String params, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_GATE_UPDATE_GATE_DEVICES_INFO, params, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
    }

    public String getLineSpeed() {
        String line = "";
        try {
            line = mModuleRegistry.getRobotInfo(Definition.SYNC_ACTION_GET_LINE_SPEED, null);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return line;
    }

    /**
     * 设置导航最大速度
     *
     * @param reqId
     * @param lineSpeed 线速度
     * @param listener  回调函数
     * @return
     */
    public int setLineSpeed(int reqId, String lineSpeed, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_LINE_SPEED, lineSpeed, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 地图里有无指定点位
     *
     */
    public int hasPlaceInMap(int reqId, String mapName, String placeName, CommandListener listener) {
        JSONObject param = new JSONObject();
        try {
            param.put(Definition.JSON_MAP_NAME, mapName);
            param.put(Definition.JSON_PLACE_NAME, placeName);

            CommandBean bean = new CommandBean(Definition.CMD_NAVI_HAS_PLACE_IN_MAPNAME, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }
}
