package com.ainirobot.coreservice.action.advance_charge.state;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.ChargeAdvancedAction;
import com.ainirobot.coreservice.action.NavigationResetHeadUtils;
import com.ainirobot.coreservice.action.StatusListener;
import com.ainirobot.coreservice.action.advance_charge.AdvanceChargeStateEnum;
import com.ainirobot.coreservice.action.advance_charge.bean.ChargeResult;
import com.ainirobot.coreservice.action.advance_charge.bean.StateData;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.AutoChargeBean;
import com.ainirobot.coreservice.client.actionbean.BatteryBean;
import com.ainirobot.coreservice.client.actionbean.MultiRobotStatus;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.config.ConfigManager;
import com.ainirobot.coreservice.core.LocalApi;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

public class GoNormalChargeState extends BaseAdvanceChargeState {
    private volatile Status mStatus;
    private static final String SUCCEED = "succeed";
    private static final int CHARGE_TYPE_PILE = 0;
    private static final int CHARGE_TYPE_WIRE = 1;
    private static final int CHARGE_TYPE_WIRE_CUSTOM = 2;
    private static final long DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT = 300 * 1000;
    private Timer naviToChargeTimer;
    private long timeout;
    private Pose mFlagPose;
    private double mAvoidDistance;
    private long mAvoidTimeout;
    private String mPoseListener;
    private int mInt = 0;
    private volatile boolean isNavigating = false;
    private double coordinateDeviation = 0.1f;
    private IChargePolicy mCurrentPolicy;
    private IChargePolicy[] mPolicy = new IChargePolicy[Definition.CHARGE_MAX_POLICY];
    /**
     * 多机模式等待超时时长，默认300s
     **/
    private long mWaitTimeout;
    /**
     * 是否是在多机避障状态，如果是则停止处理机器N秒不动退出导航的逻辑
     **/
    private volatile boolean isMultipleWaitStatus = false;
    private Timer mWaitTimer;
    private int mConfigChargingType;
    private String mCustomLocation;
    private NavigationResetHeadUtils mNavigationResetHeadUtils;

    private enum Status {
        IDLE,
        GO_LOCATION,
        GO_CHARGE,
        CHARGING

    }

    private void updateStatus(Status status) {
        Log.i(TAG, "updateStatus: " + status);
        mStatus = status;
    }

    @Override
    protected void onStart(ChargeAdvancedAction chargeAdvancedAction, StateData stateData) {
        updateStatus(Status.IDLE);
        mPolicy[Definition.CHARGE_VISION_POLICY] = new VisionPolicy();
        mPolicy[Definition.CHARGE_WIRE_POLICY] = new WireChargingPolicy();
        mNavigationResetHeadUtils = new NavigationResetHeadUtils(chargeAdvancedAction.getApi(),
                chargeAdvancedAction.getActionManager().getCoreService().getHeadMoveManager());
        updateParams(stateData.getAutoChargeBean());
        chargeAdvancedAction.getApi().isRobotEstimate(chargeAdvancedAction.getReqId());
    }

    private void updateParams(AutoChargeBean autoChargeBean) {
        timeout = autoChargeBean.getTimeout();
        mAvoidDistance = autoChargeBean.getAvoidDistance();
        mAvoidTimeout = autoChargeBean.getAvoidTimeout();
        coordinateDeviation = autoChargeBean.getCoordinateDeviation();
        long waitTimeout = autoChargeBean.getMultipleWaitTime();
        mWaitTimeout = (waitTimeout <= 0) ? DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT : waitTimeout;
        isMultipleWaitStatus = false;
        if (timeout < GO_NORMAL_NAVIGATION_TIMEOUT) {
            timeout = GO_NORMAL_NAVIGATION_TIMEOUT;
        }
        Log.d(TAG, "verifyParam timeout:" + timeout + ", mAvoidTimeout:" + mAvoidTimeout
                + ", mAvoidDistance:" + mAvoidDistance);
    }

    @Override
    protected boolean onCmdResponse(int cmdId, String command, String result, String extraData) {
        Log.d(TAG, "cmdResponse cmdType=" + command + " result=" + result);
        switch (command) {
            case Definition.CMD_NAVI_IS_ESTIMATE:
                Log.d(TAG, "CMD_NAVI_IS_ESTIMATE  result : " + result);
                if ("true".equals(result)) {
                    checkChargePlace();
                } else {
                    finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.CHARGE_FAIL_WHEN_NOT_ESTIMATE));
                }
                return true;
            case Definition.CMD_NAVI_GET_LOCATION:
                processGetLocation(result, extraData);
                return true;
            case Definition.CMD_NAVI_IS_IN_LOCATION:
                Log.d(TAG, "handle CMD_NAVI_IS_IN_LOCATION result : " + result);
                if (mStatus == Status.GO_CHARGE) {
                    return false;
                }
                handleIsInLocation(result, extraData);
                return true;
            case Definition.CMD_NAVI_RESUME_SPECIAL_PLACE_THETA:
                Log.d(TAG, "handle CMD_NAVI_RESUME_SPECIAL_PLACE_THETA resutl : " + result);
                if (SUCCEED.equals(result) || Definition.SPECIAL_THETA_ALREADY_SUCCESS_NO_NEED_TO_RESUME.equals(result)) {
                    updateStatus(Status.GO_CHARGE);
                    isNavigating = false;
                    cancelNaviToChargeTimer();
                    startChargePolicy();
                } else {
                    isNavigating = true;
                    Log.d(TAG, "handle CMD_NAVI_RESUME_SPECIAL_PLACE_THETA FAIL");
                    if (mConfigChargingType != CHARGE_TYPE_WIRE_CUSTOM) {
                        mChargeAdvancedAction.getApi().goPositionByType(mChargeAdvancedAction.getReqId(), getChargingPoseTypeId(mConfigChargingType), Definition.SPECIAL_PLACE_HIGH_PRIORITY);
                    } else {
                        mChargeAdvancedAction.getApi().goPlace(mChargeAdvancedAction.getReqId(), mCustomLocation);
                    }
                }
                return true;
            case Definition.CMD_NAVI_GO_LOCATION:
            case Definition.CMD_NAVI_GO_POSITION_BY_TYPE:
                if (mStatus == Status.GO_CHARGE) {
                    return false;
                }
                Log.d(TAG, "cmdType: " + command + ", result = " + result + ", mState : " + mStatus);
                cancelNaviToChargeTimer();
                if (Definition.NAVIGATION_OK.equals(result)) {
                    updateStatus(Status.GO_CHARGE);
                    isNavigating = false;
                    onCmdStatusUpdate(mChargeAdvancedAction.getReqId(), Definition.CMD_NAVI_IS_IN_LOCATION, Definition.CMD_NAVI_IS_IN_LOCATION, extraData);//通知上层已经在回充点位置
                    startChargePolicy();
                } else {
                    finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.CHARGE_FAIL_WHEN_NAVIGATION));
                }
                return true;
            default:
                if (!mNavigationResetHeadUtils.onCmdResponse(cmdId, command, result, extraData)){
                    onChargePolicyCmdResponse(cmdId, command, result);
                }
                return false;
        }
    }

    @Override
    protected boolean onCmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        Log.d(TAG, "onCmdStatusUpdate cmdType=" + command + " status=" + status);
        switch (command) {
            case Definition.CMD_NAVI_GO_LOCATION:
            case Definition.CMD_NAVI_GO_POSITION_BY_TYPE:
                handleNavigationStatus(status);
                return true;
            case Definition.CMD_NAVI_IS_IN_LOCATION:
                onStateUpdate(Definition.STATUS_ALREADY_AT_START_CHARGE_POINT, "already at start charge point and begin to psb charge");
                return true;
            default:
                if (!mNavigationResetHeadUtils.cmdStatusUpdate(cmdId, command, status, extraData)){
                    onChargePolicyCmdStatusUpdate(cmdId, command, status);
                }
                return true;
        }
    }

    protected void multiRobotWaiting() {
        updateMultipleRobotWaitingStatus(true);
        startWaitingTimer();
    }

    protected void multiRobotWaitingEnd() {
        updateMultipleRobotWaitingStatus(false);
        cancelWaitTimer();
        startNaviToChargeTimer();
    }

    @Override
    public void stop() {
        super.stop();
        Log.d(TAG, "ChargeAdvanceAction stopAction ------------  ");
        updateMultipleRobotWaitingStatus(false);
        cancelWaitTimer();
        unregisterStatusListener(mPoseListener);
        cancelNaviToChargeTimer();
        stopChargePolicy();
        updateStatus(Status.IDLE);
        isNavigating = false;
        mConfigChargingType = 0;
        mInt = 0;
        mNavigationResetHeadUtils.stop();
    }

    private void checkChargePlace() {
        boolean hasChargeIR = ConfigManager.isHasChargeIR();
        if (hasChargeIR && isChargingTypePile()) {//有TopIR且选择桩充
            mConfigChargingType = CHARGE_TYPE_PILE;
        } else {
            mCustomLocation = mChargeAdvancedAction.getActionManager().getRobotSettingManager().
                    getRobotSetting(Definition.ROBOT_SETTING_LOW_BATTERY_NAVI_LOCATION);
            Log.e(TAG, "mCustomLocation: " + mCustomLocation);
            mConfigChargingType = TextUtils.isEmpty(mCustomLocation) ? CHARGE_TYPE_WIRE : CHARGE_TYPE_WIRE_CUSTOM;
        }
        if (mConfigChargingType != CHARGE_TYPE_WIRE_CUSTOM) {
            mChargeAdvancedAction.getApi().getLocation(mChargeAdvancedAction.getReqId(), getChargingFinalPoseTypeId(mConfigChargingType));
        } else {
            // CHARGE_TYPE_WIRE_CUSTOM 类型 业务层设置的其他点位作为回充点
            mChargeAdvancedAction.getApi().getLocation(mChargeAdvancedAction.getReqId(), mCustomLocation);
        }
    }

    private boolean isChargingTypePile() {
        String chargingType = mChargeAdvancedAction.getActionManager().getRobotSettingManager().
                getRobotSetting(Definition.ROBOT_SETTINGS_CHARGING_TYPE);
        return Definition.CHARGING_TYPE_PILE.equals(chargingType);
    }

    private void updatePolicy() {
        if (mConfigChargingType == CHARGE_TYPE_PILE) {
            mCurrentPolicy = mPolicy[Definition.CHARGE_VISION_POLICY];
        } else {
            mCurrentPolicy = mPolicy[Definition.CHARGE_WIRE_POLICY];
        }
    }

    private String getChargingFinalPoseName(int chargingType) {
        switch (chargingType) {
            case CHARGE_TYPE_PILE:
                return Definition.START_CHARGE_PILE_POSE;
            case CHARGE_TYPE_WIRE:
                return Definition.LOCATE_POSITION_POSE;
            case CHARGE_TYPE_WIRE_CUSTOM:
                return mCustomLocation;
        }
        return Definition.LOCATE_POSITION_POSE;
    }

    private int getChargingFinalPoseTypeId(int chargingType) {
        switch (chargingType) {
            case CHARGE_TYPE_PILE:
                return Definition.CHARGING_POLE_TYPE;
            case CHARGE_TYPE_WIRE:
                return Definition.POSITIONING_POINT_TYPE;
        }
        return Definition.POSITIONING_POINT_TYPE;
    }

    private int getChargingPoseTypeId(int chargingType) {
        switch (chargingType) {
            case CHARGE_TYPE_PILE:
                return Definition.CHARGING_POINT_TYPE;
            case CHARGE_TYPE_WIRE:
                return Definition.POSITIONING_POINT_TYPE;
        }
        return Definition.POSITIONING_POINT_TYPE;
    }

    private void processGetLocation(String result, String extraData) {
        try {
            JSONObject json = new JSONObject(result);
            boolean isExist = json.getBoolean(Definition.JSON_NAVI_SITE_EXIST);
            String posName = json.getString(Definition.JSON_REMOTE_POS_NAME);
            if (isExist) {
                updatePolicy();
                startCheckLocation();
            } else {
                if (mConfigChargingType == CHARGE_TYPE_WIRE_CUSTOM) {
                    mConfigChargingType = CHARGE_TYPE_WIRE;
                    mChargeAdvancedAction.getApi().getLocation(mChargeAdvancedAction.getReqId(), getChargingFinalPoseTypeId(mConfigChargingType));
                } else {
                    finishAction(isChargePile() ?
                            Definition.CHARGE_FAIL_NOT_EXIST_CHARGING_PILE_POSE :
                            Definition.CHARGE_FAIL_NOT_EXIST_CHARGING_WIRE_POSE, getChargingFinalPoseName(mConfigChargingType));
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
            finishAction(isChargePile() ?
                    Definition.CHARGE_FAIL_NOT_EXIST_CHARGING_PILE_POSE :
                    Definition.CHARGE_FAIL_NOT_EXIST_CHARGING_WIRE_POSE, getChargingFinalPoseName(mConfigChargingType));
        }
    }

    private void startCheckLocation() {
        // parse parameter to gain chargePoint , then　goPosition (px py angle) 导航到该点,　then startAutoCharge
        mFlagPose = null;
        mInt = 0;
        updateStatus(Status.GO_LOCATION);
        Log.d(TAG, "start charge isRobotInLocation");
        registerPoseListener();
        isNavigating = true;
        if (mConfigChargingType == CHARGE_TYPE_WIRE_CUSTOM) {
            isRobotInLocation(mCustomLocation, coordinateDeviation);
        } else {
            isRobotInLocation(getChargingFinalPoseTypeId(mConfigChargingType), coordinateDeviation);
        }
        mChargeAdvancedAction.getApi().resetHead(mChargeAdvancedAction.getReqId());
        startNaviToChargeTimer();
    }


    /**
     * 更新多机避障状态，如果是多机避障状态，则机器人超时等待策略需忽略
     * @param isWaiting 是否正在多机避障等待状态
     */
    private void updateMultipleRobotWaitingStatus(boolean isWaiting){
        isMultipleWaitStatus = isWaiting;
    }

    private void startWaitingTimer() {
        cancelWaitTimer();
        mWaitTimer = new Timer();
        mWaitTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (mStatus != Status.GO_LOCATION) {
                    return;
                }
                finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.ERROR_MULTI_ROBOT_WAITING_TIMEOUT));
            }
        }, mWaitTimeout);
    }

    private void cancelWaitTimer() {
        if (mWaitTimer != null) {
            mWaitTimer.cancel();
            mWaitTimer = null;
        }
    }

    private boolean isAborted(Pose pose) {
        double distance = pose.getDistance(mFlagPose);
        if (!isMultipleWaitStatus && Double.compare(distance, mAvoidDistance) < 0) {
            long movingTime = pose.getTime() - mFlagPose.getTime();
            if (movingTime > mAvoidTimeout) {
                return true;
            }
        } else {
            mFlagPose = pose;
        }
        return false;
    }

    private void registerPoseListener() {
        mPoseListener = registerStatusListener(Definition.STATUS_POSE, new StatusListener() {
            @Override
            public void onStatusUpdate(String data) {
                if (mStatus == Status.GO_CHARGE) {
                    return;
                }
                Pose pose = mGson.fromJson(data, Pose.class);
                boolean aborted = isAborted(pose);
                mInt++;
                if (isNavigating && aborted) {
                    finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.CHARGE_FAIL_WHEN_NOT_MOVE_FOR_20S));
                }
            }
        });
    }

    private void startNaviToChargeTimer() {
        Log.d(TAG, "startNaviToChargeTimer mstate:" + mStatus + " multiWaitStatus:" + isMultipleWaitStatus);
        if (mStatus != Status.GO_LOCATION){
            return;
        }
        cancelNaviToChargeTimer();
        naviToChargeTimer = new Timer();
        naviToChargeTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (mStatus == Status.GO_CHARGE || isMultipleWaitStatus) {
                    return;
                }
                finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.CHARGE_FAIL_WHEN_LARGE_MAP_NAVI_TIMEOUT));
            }
        }, timeout);

    }

    private void cancelNaviToChargeTimer() {
        if (naviToChargeTimer != null) {
            naviToChargeTimer.cancel();
            naviToChargeTimer = null;
        }
    }

    private boolean isChargePile() {
        return mConfigChargingType == CHARGE_TYPE_PILE;
    }

    private void isRobotInLocation(String placeName, double coordinateDeviation) {
        JSONObject obj = new JSONObject();
        try {
            obj.put(Definition.JSON_NAVI_TARGET_PLACE_NAME, placeName);
            obj.put(Definition.JSON_NAVI_COORDINATE_DEVIATION, coordinateDeviation);
            mChargeAdvancedAction.getApi().isRobotInlocations(mChargeAdvancedAction.getReqId(), obj.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void isRobotInLocation(int typeId, double coordinateDeviation) {
        mChargeAdvancedAction.getApi().isRobotInLocations(mChargeAdvancedAction.getReqId(), typeId, Definition.SPECIAL_PLACE_HIGH_PRIORITY, coordinateDeviation);
    }

    private void resumeSpecialPlaceTheta(String placeName) {
        JSONObject obj = new JSONObject();
        try {
            obj.put(Definition.JSON_NAVI_TARGET_PLACE_NAME, placeName);
            mChargeAdvancedAction.getApi().resumeSpecialPlaceTheta(mChargeAdvancedAction.getReqId(), obj.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void resumeSpecialPlaceTheta(int typeId) {
        JSONObject obj = new JSONObject();
        try {
            obj.put(Definition.JSON_NAVI_TYPE_ID, typeId);
            obj.put(Definition.JSON_NAVI_PRIORITY, Definition.SPECIAL_PLACE_HIGH_PRIORITY);
            mChargeAdvancedAction.getApi().isRobotInlocations(mChargeAdvancedAction.getReqId(), obj.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void handleIsInLocation(String result, String extraData) {
        try {
            JSONObject json = new JSONObject(result);
            boolean isInLocation = json.optBoolean(Definition.JSON_NAVI_IS_IN_LOCATION, false);
            if (isInLocation) {
                if (mConfigChargingType != CHARGE_TYPE_PILE) {
                    Log.d(TAG, "Already in location");
                    onCmdStatusUpdate(mChargeAdvancedAction.getReqId(), Definition.CMD_NAVI_IS_IN_LOCATION, Definition.CMD_NAVI_IS_IN_LOCATION, extraData);
                    startChargePolicy();
                } else {
                    Log.d(TAG, "resume place theta");
                    onCmdStatusUpdate(mChargeAdvancedAction.getReqId(), Definition.CMD_NAVI_IS_IN_LOCATION, Definition.CMD_NAVI_IS_IN_LOCATION, extraData);//通知上层已经在回充点位置
                    if (mConfigChargingType != CHARGE_TYPE_WIRE_CUSTOM) {
                        resumeSpecialPlaceTheta(getChargingPoseTypeId(mConfigChargingType));
                    } else {
                        resumeSpecialPlaceTheta(mCustomLocation);
                    }
                }
            } else {
                Log.d(TAG, "start go place , checkResetHeadState ============  ");
                mNavigationResetHeadUtils.checkResetHeadState(new NavigationResetHeadUtils.ResetHeadListener() {
                    @Override
                    public void onResetHeadSuccess() {
                        Log.d(TAG, "start go place , go ============  ");
                        if (mConfigChargingType != CHARGE_TYPE_WIRE_CUSTOM) {
                            if (mConfigChargingType == CHARGE_TYPE_PILE) {
                                if (ConfigManager.isFrontCharge()) {
                                    mChargeAdvancedAction.getApi().goPositionByType(mChargeAdvancedAction.getReqId(), Definition.CHARGING_POINT_TYPE, Definition.SPECIAL_PLACE_HIGH_PRIORITY, true);
                                } else {
                                    mChargeAdvancedAction.getApi().goPositionByType(mChargeAdvancedAction.getReqId(), Definition.CHARGING_POINT_TYPE, Definition.SPECIAL_PLACE_HIGH_PRIORITY);
                                }
                            } else {
                                mChargeAdvancedAction.getApi().goPositionByType(mChargeAdvancedAction.getReqId(), Definition.POSITIONING_POINT_TYPE, Definition.SPECIAL_PLACE_HIGH_PRIORITY);
                            }
                        } else {
                            mChargeAdvancedAction.getApi().goPlace(mChargeAdvancedAction.getReqId(), mCustomLocation);
                        }
                        mNavigationResetHeadUtils.startHeadStatusListener(mChargeAdvancedAction.getReqId());
                    }

                    @Override
                    public void onResetHeadFailed(int errorCode, String errorString) {
                        finishAction(errorCode, errorString);
                    }
                }, mChargeAdvancedAction.getReqId());

            }
        } catch (Throwable e) {
            e.printStackTrace();
            Log.d(TAG, "handleIsInLocation error : " + e.getLocalizedMessage());
            finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.CHARGE_FAIL_WHEN_PARSE_IN_LOCATION));
        }
    }

    private void startChargePolicy() {
        if (null == mCurrentPolicy) {
            Log.e(TAG, "startChargePolicy policy is null ");
            return;
        }
        mCurrentPolicy.start();
    }

    private void stopChargePolicy() {
        if (null == mCurrentPolicy) {
            return;
        }
        mCurrentPolicy.stop();
        mCurrentPolicy = null;
    }

    private void onChargePolicyCmdStatusUpdate(int cmdId, String cmdType, String params) {
        if (null == mCurrentPolicy) {
            return;
        }
        mCurrentPolicy.onCmdStatusUpdate(cmdId, cmdType, params);
    }

    private void onChargePolicyCmdResponse(int cmdId, String cmdType, String params) {
        if (null == mCurrentPolicy) {
            return;
        }
        mCurrentPolicy.onCmdResponse(cmdId, cmdType, params, mChargeAdvancedAction.getApi());
    }


    @Override
    protected AdvanceChargeStateEnum getNextState() {
        return null;
    }


    //------------------------------ 回充策略 ------------------------------
    private interface IChargePolicy {
        boolean start();

        boolean stop();

        boolean onCmdResponse(int cmdId, String cmdType, String params, LocalApi api);

        void onCmdStatusUpdate(int cmdId, String command, String status);
    }

    /**
     * 线充方式回充
     */
    private class WireChargingPolicy implements IChargePolicy {

        @Override
        public boolean start() {
            Log.d(TAG, "WireChargingPolicy start: mState=" + mStatus);
            registerStatusListener(Definition.STATUS_BATTERY, new StatusListener() {
                @Override
                public void onStatusUpdate(String data) {
                    Log.d(TAG, "STATUS_BATTERY onStatusUpdate: data=" + data);
                    Gson gson = new Gson();
                    BatteryBean batteryBean = gson.fromJson(data, BatteryBean.class);
                    if (batteryBean != null && batteryBean.isCharging()) {
                        finishAction(Definition.RESULT_OK, "wire charging success");
                    }
                }
            });
            return false;
        }

        @Override
        public boolean stop() {
            Log.d(TAG, "WireChargingPolicy stop: mState=" + mStatus);
            unregisterStatusListener(Definition.STATUS_BATTERY);
            return false;
        }

        @Override
        public boolean onCmdResponse(int cmdId, String cmdType, String params, LocalApi api) {
            return false;
        }

        @Override
        public void onCmdStatusUpdate(int cmdId, String command, String status) {

        }
    }

    /**
     * 底盘通过视觉拍照定位，然后底盘直接控制轮子 由起始回充点 移动到 充电桩.
     */
    private class VisionPolicy implements IChargePolicy {

        @Override
        public boolean start() {
            boolean isFrontCharge = ConfigManager.isFrontCharge();
            Log.d(TAG, "send cmd_navi_vision_charge_start cmd  isFrontCharge=" + isFrontCharge);
            mChargeAdvancedAction.getApi().startVisionCharge(mChargeAdvancedAction.getReqId(), isFrontCharge);
            return true;
        }

        @Override
        public boolean stop() {
            Log.d(TAG, "stop mState :" + mStatus);
            mChargeAdvancedAction.getApi().stopVisionCharge(mChargeAdvancedAction.getReqId());
            return true;
        }

        @Override
        public boolean onCmdResponse(int cmdId, String cmdType, String params, LocalApi api) {
            Log.d(TAG, "VisionPolicy onCmdResponse cmdType=" + cmdType + " params=" + params);
            switch (cmdType) {
                case Definition.CMD_NAVI_VISION_CHARGE_START:
                    if ("timeout".equals(params)) {
                        finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.CHARGE_FAIL_VISION_CHARGE_TIMEOUT));
                        return false;
                    }

                    Gson gson = new Gson();
                    ChargeResult chargeResult = gson.fromJson(params, ChargeResult.class);
                    int code = chargeResult.getCode();
                    if (code == 25) {
                        chargeResult.setCode(Definition.RESULT_OK);
                        finishAction(Definition.RESULT_OK, gson.toJson(chargeResult));
                    } else if (code == 26) {
                        chargeResult.setCode(Definition.RESULT_FAILED);
                        finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.CHARGE_FAIL_WHEN_VISION_CHARGE_START));
                    }
                    break;

                case Definition.CMD_NAVI_VISION_CHARGE_STOP:
                    gson = new Gson();
                    chargeResult = gson.fromJson(params, ChargeResult.class);
                    code = chargeResult.getCode();
                    if (code == 0) {
                        chargeResult.setCode(Definition.RESULT_OK);
                        finishAction(Definition.RESULT_OK, gson.toJson(chargeResult));
                    } else {
                        chargeResult.setCode(Definition.RESULT_FAILED);
                        finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.CHARGE_FAIL_WHEN_VISION_CHARGE_STOP));
                    }
                    break;

                default:
                    break;

            }
            return true;
        }

        @Override
        public void onCmdStatusUpdate(int cmdId, String cmdType, String status) {
            switch (cmdType) {
                case Definition.CMD_NAVI_VISION_CHARGE_START:
                    Log.d(TAG, "VisionPolicy onCmdStatusUpdate : cmdType=" + cmdType
                            + " status=" + status);
                    Gson gson = new Gson();
                    ChargeResult chargeResult = gson.fromJson(status, ChargeResult.class);
                    int code = chargeResult.getCode();

                    if (code == 0) {
                        onStateUpdate(Definition.STATUS_VISION_CHARGE_START_GOTO_CHARGING_PILE,
                                "Start go to charging pile");
                    } else {
                        onStateUpdate(code, status);
                    }
                    break;
            }
        }

    }
}
