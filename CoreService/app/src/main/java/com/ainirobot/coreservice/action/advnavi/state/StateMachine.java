package com.ainirobot.coreservice.action.advnavi.state;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.support.annotation.NonNull;
import android.util.Log;

import com.ainirobot.coreservice.action.NavigationAdvancedAction;
import com.ainirobot.coreservice.action.StopCommandList;
import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.client.Definition;

import java.util.List;

public class StateMachine {
    private static final String TAG = "NaviAdvancedAction-" + StateMachine.class.getSimpleName();
    private static final Object sSyncLock = new Object();
    private static Looper sLooper;
    private NavigationAdvancedAction mAction;

    private StateHandler mStateHandler = null;

    private volatile BaseState mCurrentState;
    private volatile boolean isRunning = false;

    private final IStateListener mListener = new IStateListener() {
        @Override
        public void onSuccess() {
            startNext();
        }

        @Override
        public void onStop() {
            stopAction();
        }
    };

    static {
        HandlerThread handlerThread = new HandlerThread("NaviElevatorStateMachine");
        handlerThread.start();
        sLooper = handlerThread.getLooper();
    }

    public StateMachine(NavigationAdvancedAction action) {
        if (mStateHandler == null) {
            mStateHandler = new StateHandler(sLooper);
        }
        Log.d(TAG, "StateMachine init");
        mAction = action;

        updateCurrentState(StateEnum.IDLE.getState());
    }


    public void stopAction() {
        synchronized (sSyncLock) {
            Log.e(TAG, "stopAction");
            isRunning = false;
            mStateHandler.removeCallbacksAndMessages(null);
            if (null != mCurrentState) {
                mCurrentState.exit();
            }

            updateCurrentState(StateEnum.IDLE.getState());
        }
    }

    /**
     * 需要坐电梯流程：默认 -> 准备(初始化) -> 去电梯口 -> 在电梯外等待 -> 进电梯 -> 切图 -> 在电梯内等待 -> 出梯 -> 去目的地
     * 在电梯内流程：默认 -> 准备(初始化) -> 切图 -> 在电梯内等待 -> 出梯 -> 去目的地
     * 不需要坐电梯流程：默认 -> 准备(初始化) -> 去目的地
     *
     * @param stateData
     */
    public boolean start(StateData stateData) {
        synchronized (sSyncLock) {
            if (mCurrentState == null || mCurrentState == StateEnum.IDLE.getState()) {
                //startAction方法需要return true后，才会changeStatus为RUN
                //故不能直接切换到准备状态，延迟一段时间
                isRunning = true;
                transitionTo(new TransitionInfo(StateEnum.PREPARE.getState(), stateData), 50);
                return true;
            } else {
                Log.e(TAG, "cannot startInspect, current state: " + mCurrentState.getStateName());
                return false;
            }
        }
    }

    private void startNext() {
        TransitionInfo transitionInfo = null;
        if (mCurrentState != null && mCurrentState.getNextState() != null) {
            BaseState nextState = mCurrentState.getNextState();
            Log.d(TAG, "startNext: current=" + mCurrentState.getStateName() +
                    ", next=" + nextState.getStateName());
            transitionInfo = new TransitionInfo(mCurrentState.getNextState(), mCurrentState.getData());
        } else {
            StateData stateData = new StateData(null);
            stateData.setResultCode(Definition.STATUS_ILLEGAL_STATE);
            stateData.setMsg("[startNext]cur state is null");
            transitionInfo = new TransitionInfo(StateEnum.IDLE.getState(), stateData);
        }
        transitionTo(transitionInfo);
    }

    private void transitionTo(TransitionInfo transitionInfo) {
        transitionTo(transitionInfo, 0);
    }

    private void transitionTo(TransitionInfo transitionInfo, long delay) {
        Message msg = Message.obtain();
        msg.what = StateHandler.MSG_TRANSITION_TO;
        msg.obj = transitionInfo;

        if (mStateHandler.hasMessages(StateHandler.MSG_TRANSITION_TO)) {
            Log.d(TAG, "remove msg:" + StateHandler.MSG_TRANSITION_TO);
            mStateHandler.removeMessages(StateHandler.MSG_TRANSITION_TO);
        }

        if (delay > 0) {
            Log.i(TAG, "wait[ " + delay + "ms ]" + ", tranceTo:" + transitionInfo.getState());
            mStateHandler.sendMessageDelayed(msg, delay);
        } else {
            Log.e(TAG, "tranceTo:" + transitionInfo.getState());
            mStateHandler.sendMessage(msg);
        }
    }

    private void handleTransition(TransitionInfo transitionInfo) {
        synchronized (sSyncLock) {
            if (!isRunning) {
                Log.e(TAG, "not transition ! not running !");
                return;
            }
            BaseState state = transitionInfo.getState();
            if (state == null) {
                throw new IllegalStateException("State cannot be null");
            }

            Log.d(TAG, "handleTransition from[ " + mCurrentState + " ]" + " to [ " + state + " ]");
            if (mCurrentState != null) {
                mCurrentState.exit();
            }

            state.setAction(mAction);
            updateCurrentState(state);
//            mCurrentState.setAction(mAction);
            mCurrentState.preAction(transitionInfo.getData(), mListener);
            mCurrentState.doAction();
        }
    }

    private void updateCurrentState(@NonNull BaseState state) {
        mCurrentState = state;
        Log.i(TAG, "updateCurrentState " + mCurrentState.getStateName());
    }

    private void handleRequest(MessageInfo obj) {
        if (mCurrentState != null) {
            Log.d(TAG, ((BaseState) mCurrentState).getStateName() + " handle msg:" + "");
//            mState.handleMessage(obj);
        }
    }

    private class StateHandler extends Handler {
        private static final int MSG_TRANSITION_TO = 100;
        private static final int MSG_NEW_REQUEST = 200;

        StateHandler(final Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(final Message msg) {
            if (!isRunning) {
                Log.e(TAG, "handleMessage: not running");
                return;
            }
            switch (msg.what) {
                case MSG_TRANSITION_TO:
                    handleTransition((TransitionInfo) msg.obj);
                    break;
                case MSG_NEW_REQUEST:
                    handleRequest((MessageInfo) msg.obj);
                    break;
                default:
                    super.handleMessage(msg);
                    break;
            }
        }
    }

    private static class TransitionInfo {
        private final BaseState mState;
        private final StateData mData;

        TransitionInfo(final BaseState state, final StateData data) {
            mState = state;
            mData = data;
        }

        BaseState getState() {
            return mState;
        }

        StateData getData() {
            return mData;
        }

        @Override
        @NonNull
        public String toString() {
            return "TransitionInfo["
                    + ", mState="
                    + mState
                    + ", mData="
                    + mData
                    + ']';
        }
    }

    private static class MessageInfo {
        private final String mType;
        private final String mValue;

        MessageInfo(final String type,
                    final String value) {
            mType = type;
            mValue = value;
        }

        String getType() {
            return mType;
        }

        String getValue() {
            return mValue;
        }

        @Override
        @NonNull
        public String toString() {
            return "MessageInfo["
                    + ", mType="
                    + mType
                    + ", mValue="
                    + mValue
                    + ']';
        }
    }

    public BaseState getCurrentState() {
        return mCurrentState;
    }

    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        if (mCurrentState == StateEnum.IDLE.getState()) {
            Log.d(TAG, "current is idle state");
            return false;
        }
        return mCurrentState.cmdResponse(cmdId, command, result, extraData);
    }

    public boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        if (mCurrentState == StateEnum.IDLE.getState()) {
            Log.d(TAG, "current is idle state");
            return false;
        }
        return mCurrentState.cmdStatusUpdate(cmdId, command, status, extraData);
    }

    public List<StopCommandList.StopCommand> getStopCommands() {
        if (mCurrentState == StateEnum.IDLE.getState()) {
            Log.d(TAG, "current is idle state");
            return null;
        }
        return mCurrentState.getStopCommands();
    }
}