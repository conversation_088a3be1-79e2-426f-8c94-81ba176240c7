package com.ainirobot.coreservice.core.apiproxy.impl.person;

import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.ActionManager;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.account.MemberBean;
import com.ainirobot.coreservice.client.actionbean.RecognizeBean;
import com.ainirobot.coreservice.client.actionbean.RemoteRegisterBean;
import com.ainirobot.coreservice.client.person.FaceBean;
import com.ainirobot.coreservice.core.apiproxy.result.BaseApiResult;
import com.ainirobot.coreservice.core.apiproxy.result.impl.LocalSyncFaceResult;
import com.ainirobot.coreservice.data.account.MemberDataHelper;
import com.ainirobot.coreservice.service.CoreService;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

public class PersonApiProxy_v1 extends PersonBaseApiProxy {

    private static final String TAG = PersonApiProxy_v1.class.getSimpleName();

    public PersonApiProxy_v1(CoreService core, Handler handler) {
        super(core, handler);
    }

    @Override
    public void localFaceDataSync() {
        List<FaceBean> onlyLocalFaceList = MemberDataHelper.getInstance(mCore)
                .getOnlyLocalFaceList();
        Log.d(TAG, "localFaceDataSync onlyLocalFaceList: " + mGson.toJson(onlyLocalFaceList));
        if (mHandler != null) {
            Message msg = new Message();
            msg.what = MSG_API_RESULT;
            msg.obj = new LocalSyncFaceResult(BaseApiResult.RESULT_SUCCESS,
                    new LocalSyncFaceResult.LocalDataSyncData("remote", null));
            mHandler.sendMessage(msg);
        }
    }

    @Override
    public void registerById(final int personId, final String name,
                             final boolean isReEntry) {
        String faceId = generalFaceId();
        Log.i(TAG, "registerById personId: " + personId
                + ", name: " + name + ", faceId: " + faceId + ", isReEntry: " + isReEntry);
        register(personId, "", name, faceId, isReEntry);
    }

    @Override
    public void registerByPic(final String picturePath, final String name,
                              final boolean isReEntry) {
        String faceId = generalFaceId();
        Log.i(TAG, "registerByPic picturePath: " + picturePath
                + ", name: " + name + ", faceId: " + faceId + ", isReEntry: " + isReEntry);
        register(-1, picturePath, name, faceId, isReEntry);
    }

    private void register(final int personId, final String picturePath, final String name,
                          final String faceId, final boolean isReEntry) {
        RemoteRegisterBean bean = new RemoteRegisterBean();
        bean.setPersonId(personId);
        bean.setPicturePath(picturePath);
        bean.setPersonName(name);
        bean.setFaceId(faceId);
        ActionManager actionManager = mCore.getActionManager();
        actionManager.exStopCmd(Definition.ACTION_REGISTER_v1, false);
        actionManager.exCmd(Definition.ACTION_REGISTER_v1, mGson.toJson(bean),
                new ApiListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.d(TAG, "register result: " + result
                                + ", message: " + message);
                        int apiResult;
                        Object data = null;
                        if (result == Definition.RESULT_SUCCEED) {
                            FaceBean faceBean = mGson.fromJson(message, FaceBean.class);
                            String newPictureUri = saveLocalRegisterPic(faceBean
                                    .getPictureUri(), faceBean.getFaceId());
                            if (TextUtils.isEmpty(newPictureUri)) {
                                apiResult = BaseApiResult.RESULT_FAILED;
                                data = "copy picture failed";
                            } else {
                                faceBean.setPictureUri(newPictureUri);
                                MemberDataHelper.getInstance(mCore).insertNewFace(faceBean);
                                apiResult = BaseApiResult.RESULT_SUCCESS;
                                data = mGson.toJson(MemberDataHelper.getInstance(mCore)
                                        .getMemberByFaceId(faceBean.getFaceId()));
                            }
                        } else {
                            apiResult = BaseApiResult.RESULT_FAILED;
                        }
                        if (mHandler != null) {
                            Message msg = new Message();
                            msg.what = MSG_API_RESULT;
                            if (!isReEntry) {
                                msg.obj = new BaseApiResult(BaseApiResult
                                        .Type.REGISTER, apiResult, data);
                            } else {
                                msg.obj = new BaseApiResult(BaseApiResult
                                        .Type.RE_REGISTER, apiResult, data);
                            }
                            mHandler.sendMessage(msg);
                        }
                    }

                    @Override
                    public void onError(int errorCode, String errorString) {
                        Log.d(TAG, "registerByPic errorCode: " + errorCode
                                + ", errorString: " + errorString);
                        int apiResult = BaseApiResult.RESULT_FAILED;
                        if (mHandler != null) {
                            Message msg = new Message();
                            msg.what = MSG_API_RESULT;
                            if (!isReEntry) {
                                msg.obj = new BaseApiResult(BaseApiResult
                                        .Type.REGISTER, apiResult, null);
                            } else {
                                msg.obj = new BaseApiResult(BaseApiResult
                                        .Type.RE_REGISTER, apiResult, null);
                            }
                            mHandler.sendMessage(msg);
                        }
                    }
                });
    }

    @Override
    public void recognizeById(final int personId) {
        Log.i(TAG, "recognizeById personId: " + personId);
        recognize(personId, "");
    }

    @Override
    public void recognizeByPic(final String picturePath) {
        Log.i(TAG, "recognizeByPic picturePath: " + picturePath);
        recognize(-1, picturePath);
    }

    private void recognize(final int personId, final String picturePath) {
        RecognizeBean bean = new RecognizeBean();
        bean.setPersonId(personId);
        bean.setPicturePath(picturePath);
        ActionManager actionManager = mCore.getActionManager();
        actionManager.exStopCmd(Definition.ACTION_RECOGNIZE_v1, false);
        actionManager.exCmd(Definition.ACTION_RECOGNIZE_v1, mGson.toJson(bean),
                new ApiListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.d(TAG, "recognize result: " + result
                                + ", message: " + message);
                        int apiResult;
                        Object data;
                        if (result == Definition.RESULT_SUCCEED) {
                            List<FaceBean> faceBeanList;
                            final Type faceType = new TypeToken<List<FaceBean>>() {
                            }.getType();
                            try {
                                faceBeanList = mGson.fromJson(message, faceType);
                            } catch (Exception e) {
                                faceBeanList = new ArrayList<>();
                            }
                            MemberBean memberBean = MemberDataHelper.getInstance(mCore)
                                    .getBestMember(faceBeanList);
                            if (memberBean == null) {
                                apiResult = BaseApiResult.RESULT_FAILED;
                                data = "not contains in local";
                            } else if (memberBean.getUserBean() == null) {
                                apiResult = BaseApiResult.RESULT_RECOGNIZE_ONLY_FACE;
                                data = memberBean;
                            } else {
                                if (memberBean.getUserBean() != null) {
                                    MemberDataHelper.getInstance(mCore)
                                            .updateRecentUser(memberBean.getUserBean().getUserId());
                                }
                                apiResult = BaseApiResult.RESULT_SUCCESS;
                                data = memberBean;
                            }
                        } else {
                            apiResult = BaseApiResult.RESULT_FAILED;
                            data = message;
                        }
                        if (mHandler != null) {
                            Message msg = new Message();
                            msg.what = MSG_API_RESULT;
                            msg.obj = new BaseApiResult(BaseApiResult
                                    .Type.RECOGNIZE, apiResult, data);
                            mHandler.sendMessage(msg);
                        }
                    }

                    @Override
                    public void onError(int errorCode, String errorString) {
                        Log.d(TAG, "recognizeByPic errorCode: " + errorCode
                                + ", errorString: " + errorString);
                        int apiResult = BaseApiResult.RESULT_FAILED;
                        if (mHandler != null) {
                            Message msg = new Message();
                            msg.what = MSG_API_RESULT;
                            msg.obj = new BaseApiResult(BaseApiResult
                                    .Type.RECOGNIZE, apiResult, null);
                            mHandler.sendMessage(msg);
                        }
                    }
                });
    }

    @Override
    public void deleteFaceList(List<FaceBean> deleteFaceList) {
    }
}
