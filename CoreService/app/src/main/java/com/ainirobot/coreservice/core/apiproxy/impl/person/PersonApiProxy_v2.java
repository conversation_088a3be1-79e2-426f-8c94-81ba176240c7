package com.ainirobot.coreservice.core.apiproxy.impl.person;

import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.ActionManager;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.account.MemberBean;
import com.ainirobot.coreservice.client.actionbean.CommandBean;
import com.ainirobot.coreservice.client.person.FaceBean;
import com.ainirobot.coreservice.core.apiproxy.result.BaseApiResult;
import com.ainirobot.coreservice.core.apiproxy.result.impl.LocalSyncFaceResult;
import com.ainirobot.coreservice.data.account.MemberDataHelper;
import com.ainirobot.coreservice.service.CoreService;
import com.ainirobot.coreservice.utils.VisionUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class PersonApiProxy_v2 extends PersonBaseApiProxy {

    private static final String TAG = PersonApiProxy_v2.class.getSimpleName();

    public PersonApiProxy_v2(CoreService core, Handler handler) {
        super(core, handler);
    }

    @Override
    public void localFaceDataSync() {
        String version = MemberDataHelper.getInstance(mCore).getRegisterAlgorithmVersion();
        List<FaceBean> faceBeanList = MemberDataHelper.getInstance(mCore).getFaceList();
        Log.i(TAG, "localFaceDataSync version: " + version
                + ", size: " + faceBeanList.size());
        JSONObject param = new JSONObject();
        JSONArray array = new JSONArray();
        try {
            if (TextUtils.isEmpty(version)) {
                version = "0.0";
            }
            param.put("version", version);
            for (int i = 0; i < faceBeanList.size(); i++) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", faceBeanList.get(i).getFaceId());
                String feature = faceBeanList.get(i).getFeature();
                if (!TextUtils.isEmpty(feature)) {
                    JSONArray featureArray = new JSONArray(feature);
                    jsonObject.put("feature", featureArray);
                }
                jsonObject.put("path", faceBeanList.get(i).getPictureUri());
                array.put(jsonObject);
            }
            param.put("data", array);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        CommandBean bean = new CommandBean(Definition.CMD_VISION_FACE_DATA_SYNC,
                param.toString(), false);
        ActionManager actionManager = mCore.getActionManager();
        actionManager.exCmd(Definition.ACTION_COMMON,
                mGson.toJson(bean), new ApiListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.d(TAG, "localFaceDataSync result: " + result
                                + ", message: " + message);
                        String status = null;
                        String version = null;
                        JSONArray array = null;
                        try {
                            JSONObject json = new JSONObject(message);
                            status = json.optString("status");
                            version = json.optString("version");
                            array = json.optJSONArray("data");
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        Log.d(TAG, "localFaceDataSync status: " + status
                                + ", version: " + version + ", array: " + array);
                        int apiResult;
                        LocalSyncFaceResult.LocalDataSyncData data = null;
                        if ((!"ok".equalsIgnoreCase(status)
                                && !"upgraded".equalsIgnoreCase(status))
                                || TextUtils.isEmpty(version)) {
                            apiResult = BaseApiResult.RESULT_FAILED;
                        } else if ("upgraded".equalsIgnoreCase(status)) {
                            List<FaceBean> faceList = new ArrayList<>();
                            for (int i = 0; i < array.length(); i++) {
                                try {
                                    JSONObject json = array.getJSONObject(i);
                                    String faceId = json.optString("name");
                                    String feature = json.optString("feature");
                                    FaceBean faceBean = new FaceBean(faceId, feature);
                                    faceList.add(faceBean);
                                } catch (JSONException e) {
                                    e.printStackTrace();
                                }
                            }
                            apiResult = BaseApiResult.RESULT_LOCAL_SYNC_UPGRADED;
                            data = new LocalSyncFaceResult.LocalDataSyncData(version, faceList);
                        } else {
                            apiResult = BaseApiResult.RESULT_SUCCESS;
                            data = new LocalSyncFaceResult.LocalDataSyncData(version, null);
                        }
                        if (mHandler != null) {
                            Message msg = new Message();
                            msg.what = MSG_API_RESULT;
                            msg.obj = new LocalSyncFaceResult(apiResult, data);
                            mHandler.sendMessage(msg);
                        }
                    }
                });
    }

    @Override
    public void registerById(final int personId, final String name, final boolean isReEntry) {
        String faceId = generalFaceId();
        Log.i(TAG, "registerById personId: " + personId
                + ", name: " + name + ", faceId: " + faceId + ", isReEntry: " + isReEntry);
        JSONObject param = new JSONObject();
        final FaceBean bean = new FaceBean();
        bean.setName(name);
        bean.setFaceId(faceId);
        try {
            param.put("id", personId);
            param.put("name", bean.getFaceId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        CommandBean commandBean = new CommandBean(Definition.CMD_VISION_REGISTER_BY_ID,
                param.toString(), false);
        ActionManager actionManager = mCore.getActionManager();
        actionManager.exCmd(Definition.ACTION_COMMON,
                mGson.toJson(commandBean), new ApiListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.d(TAG, "registerById result: " + result
                                + ", message: " + message);
                        String picturePath = null;
                        String feature = null;
                        String status = null;
                        try {
                            JSONObject json = new JSONObject(message);
                            status = json.optString("status");
                            picturePath = json.optString("path");
                            feature = json.optString("feature");
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        Log.d(TAG, "registerById picturePath: " + picturePath
                                + ", feature: " + feature + ", status: " + status);
                        int apiResult;
                        Object data = null;
                        if (!"ok".equalsIgnoreCase(status) || TextUtils.isEmpty(picturePath)
                                || TextUtils.isEmpty(feature)) {
                            apiResult = BaseApiResult.RESULT_FAILED;
                        } else {
                            FaceBean faceBean = new FaceBean(bean.getFaceId(), bean.getName(), feature,
                                    bean.getPictureUri(), true);
                            String newPictureUri = saveLocalRegisterPic(faceBean
                                    .getPictureUri(), faceBean.getFaceId());
                            if (TextUtils.isEmpty(newPictureUri)) {
                                apiResult = BaseApiResult.RESULT_FAILED;
                                data = "copy picture failed";
                            } else {
                                faceBean.setPictureUri(newPictureUri);
                                MemberDataHelper.getInstance(mCore).insertNewFace(faceBean);
                                apiResult = BaseApiResult.RESULT_SUCCESS;
                                data = mGson.toJson(MemberDataHelper.getInstance(mCore)
                                        .getMemberByFaceId(faceBean.getFaceId()));
                            }
                        }
                        if (mHandler != null) {
                            Message msg = new Message();
                            msg.what = MSG_API_RESULT;
                            if (!isReEntry) {
                                msg.obj = new BaseApiResult(BaseApiResult
                                        .Type.REGISTER, apiResult, data);
                            } else {
                                msg.obj = new BaseApiResult(BaseApiResult
                                        .Type.RE_REGISTER, apiResult, data);
                            }
                            mHandler.sendMessage(msg);
                        }
                    }
                });
    }

    @Override
    public void registerByPic(final String picturePath, final String name,
                              final boolean isReEntry) {
        String faceId = generalFaceId();
        Log.i(TAG, "registerByPic picturePath: " + picturePath
                + ", name: " + name + ", faceId: " + faceId + ", isReEntry: " + isReEntry);
        JSONObject param = new JSONObject();
        JSONArray array = new JSONArray();
        final FaceBean bean = new FaceBean();
        bean.setName(name);
        bean.setFaceId(faceId);
        bean.setPictureUri(picturePath);
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("path", picturePath);
            jsonObject.put("name", bean.getFaceId());
            array.put(jsonObject);
            param.put("data", array);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        CommandBean commandBean = new CommandBean(Definition.CMD_VISION_REGISTER_BY_PIC,
                param.toString(), false);
        ActionManager actionManager = mCore.getActionManager();
        actionManager.exCmd(Definition.ACTION_COMMON,
                mGson.toJson(commandBean), new ApiListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.d(TAG, "registerByPic result: " + result
                                + ", message: " + message);
                        String feature = null;
                        String status = null;
                        try {
                            JSONObject json = new JSONObject(message);
                            status = json.optString("status");
                            JSONArray array = json.optJSONArray("data");
                            if (array != null && array.length() > 0) {
                                JSONObject jsonObject = array.optJSONObject(0);
                                String faceId = jsonObject.optString("name");
                                if (bean.getFaceId().equals(faceId)) {
                                    feature = jsonObject.optString("feature");
                                }
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        Log.d(TAG, "registerByPic feature: " + feature
                                + ", status: " + status);
                        int apiResult;
                        Object data = null;
                        if (!"ok".equalsIgnoreCase(status) || TextUtils.isEmpty(feature)) {
                            apiResult = BaseApiResult.RESULT_FAILED;
                        } else {
                            FaceBean faceBean = new FaceBean(bean.getFaceId(), bean.getName(), feature,
                                    bean.getPictureUri(), true);
                            String newPictureUri = saveLocalRegisterPic(faceBean
                                    .getPictureUri(), faceBean.getFaceId());
                            if (TextUtils.isEmpty(newPictureUri)) {
                                apiResult = BaseApiResult.RESULT_FAILED;
                                data = "copy picture failed";
                            } else {
                                faceBean.setPictureUri(newPictureUri);
                                MemberDataHelper.getInstance(mCore).insertNewFace(faceBean);
                                apiResult = BaseApiResult.RESULT_SUCCESS;
                                data = mGson.toJson(MemberDataHelper.getInstance(mCore)
                                        .getMemberByFaceId(faceBean.getFaceId()));
                            }
                        }
                        if (mHandler != null) {
                            Message msg = new Message();
                            msg.what = MSG_API_RESULT;
                            if (!isReEntry) {
                                msg.obj = new BaseApiResult(BaseApiResult
                                        .Type.REGISTER, apiResult, data);
                            } else {
                                msg.obj = new BaseApiResult(BaseApiResult
                                        .Type.RE_REGISTER, apiResult, data);
                            }
                            mHandler.sendMessage(msg);
                        }
                    }
                });
    }

    @Override
    public void recognizeById(final int personId) {
        Log.i(TAG, "recognizeById personId: " + personId);
        JSONObject param = new JSONObject();
        try {
            param.put("id", personId);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        CommandBean commandBean = new CommandBean(Definition.CMD_VISION_RECOGNIZE_BY_ID,
                param.toString(), false);
        ActionManager actionManager = mCore.getActionManager();
        actionManager.exCmd(Definition.ACTION_COMMON,
                mGson.toJson(commandBean), new ApiListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.d(TAG, "recognizeById result: " + result
                                + ", message: " + message);
                        String status = null;
                        String faceId = null;
                        try {
                            JSONObject json = new JSONObject(message);
                            status = json.optString("status");
                            faceId = json.optString("name");
                            String path = json.optString("path");
                            if (!TextUtils.isEmpty(path)) {
                                VisionUtils.deletePicture(path);
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        Log.d(TAG, "recognizeById faceId: " + faceId + ", status: " + status);
                        int apiResult;
                        Object data = null;
                        if (!"ok".equalsIgnoreCase(status) || TextUtils.isEmpty(faceId)) {
                            apiResult = BaseApiResult.RESULT_FAILED;
                        } else {
                            MemberBean memberBean = MemberDataHelper.getInstance(mCore)
                                    .getMemberByFaceId(faceId);
                            FaceBean faceBean = MemberDataHelper.getInstance(mCore)
                                    .getFaceByFaceId(faceId);
                            if (faceBean == null) {
                                apiResult = BaseApiResult.RESULT_FAILED;
                                data = "not contains in local";
                            } else if (memberBean == null) {
                                apiResult = BaseApiResult.RESULT_RECOGNIZE_ONLY_FACE;
                                MemberBean member = new MemberBean();
                                member.setFaceBean(faceBean);
                                data = member;
                            } else {
                                if (memberBean.getUserBean() != null) {
                                    MemberDataHelper.getInstance(mCore)
                                            .updateRecentUser(memberBean.getUserBean().getUserId());
                                }
                                apiResult = BaseApiResult.RESULT_SUCCESS;
                                data = memberBean;
                            }
                        }
                        if (mHandler != null) {
                            Message msg = new Message();
                            msg.what = MSG_API_RESULT;
                            msg.obj = new BaseApiResult(BaseApiResult
                                    .Type.RECOGNIZE, apiResult, data);
                            mHandler.sendMessage(msg);
                        }
                    }
                });
    }

    @Override
    public void recognizeByPic(final String picturePath) {
        Log.i(TAG, "recognizeByPic picturePath: " + picturePath);
        JSONObject param = new JSONObject();
        JSONArray array = new JSONArray();
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("path", picturePath);
            array.put(jsonObject);
            param.put("data", array);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        CommandBean commandBean = new CommandBean(Definition.CMD_VISION_RECOGNIZE_BY_PIC,
                param.toString(), false);
        ActionManager actionManager = mCore.getActionManager();
        actionManager.exCmd(Definition.ACTION_COMMON,
                mGson.toJson(commandBean), new ApiListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.d(TAG, "recognizeByPic result: " + result
                                + ", message: " + message);
                        String status = null;
                        String faceId = null;
                        try {
                            JSONObject json = new JSONObject(message);
                            status = json.optString("status");
                            JSONArray array = json.optJSONArray("data");
                            if (array != null && array.length() > 0) {
                                JSONObject jsonObject = array.optJSONObject(0);
                                faceId = jsonObject.optString("name");
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        Log.d(TAG, "recognizeByPic faceId: " + faceId + ", status: " + status);
                        int apiResult;
                        Object data = null;
                        if (!"ok".equalsIgnoreCase(status) || TextUtils.isEmpty(faceId)) {
                            apiResult = BaseApiResult.RESULT_FAILED;
                        } else {
                            MemberBean memberBean = MemberDataHelper.getInstance(mCore)
                                    .getMemberByFaceId(faceId);
                            if (memberBean == null) {
                                apiResult = BaseApiResult.RESULT_FAILED;
                                data = "not contains in local";
                            } else {
                                if (memberBean.getUserBean() != null) {
                                    MemberDataHelper.getInstance(mCore)
                                            .updateRecentUser(memberBean.getUserBean().getUserId());
                                }
                                apiResult = BaseApiResult.RESULT_SUCCESS;
                                data = memberBean;
                            }
                        }
                        if (mHandler != null) {
                            Message msg = new Message();
                            msg.what = MSG_API_RESULT;
                            msg.obj = new BaseApiResult(BaseApiResult
                                    .Type.RECOGNIZE, apiResult, data);
                            mHandler.sendMessage(msg);
                        }
                    }
                });
    }

    @Override
    public void deleteFaceList(List<FaceBean> deleteFaceList) {
    }
}
