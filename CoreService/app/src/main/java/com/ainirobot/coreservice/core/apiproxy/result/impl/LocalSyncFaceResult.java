package com.ainirobot.coreservice.core.apiproxy.result.impl;

import com.ainirobot.coreservice.client.person.FaceBean;
import com.ainirobot.coreservice.core.apiproxy.result.BaseApiResult;

import java.util.List;

public class LocalSyncFaceResult extends BaseApiResult {

    public LocalSyncFaceResult(int result, LocalDataSyncData data) {
        super(Type.LOCAL_SYNC_FACE, result, data);
    }

    public static class LocalDataSyncData {
        private String version;
        private List<FaceBean> faceList;

        public LocalDataSyncData(String version, List<FaceBean> faceList) {
            this.version = version;
            this.faceList = faceList;
        }

        public String getVersion() {
            return version;
        }

        public List<FaceBean> getFaceList() {
            return faceList;
        }
    }
}
