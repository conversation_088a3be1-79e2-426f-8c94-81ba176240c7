package com.ainirobot.coreservice.action.advnavi.state;

import android.util.Log;

import com.ainirobot.coreservice.action.StopCommandList;
import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.action.advnavi.elevatorcmd.ElevatorControlUtil;
import com.ainirobot.coreservice.action.advnavi.elevatorcmd.ElevatorControlUtilListener;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.utils.DelayTask;

import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.TimeUnit;

/**
 * Data: 2022/8/9 11:04
 * Author: wanglijing
 * Description: WaitOutsideState
 */
public class WaitOutsideState extends BaseState {

    private Status status = Status.IDLE;
    //电梯外等待超时
    private final long WAIT_TIMEOUT_MINUTE = TimeUnit.MINUTES.toMillis(5);
    //延迟执行时间
    private final long WAIT_START_TIME_MINUTE = TimeUnit.SECONDS.toMillis(30);
    private final long NOT_WAIT_TIME = 0L;
    private ElevatorControlUtil controlUtil;
    private volatile Timer mCallTimer;
    private volatile Timer mRegisterTimer;
    private long startTime;
    private int mRegisterRetryCount = 0;
    /**
     * 重试五次，加上首次，共6次
     */
    private final int MAX_REGISTER_RETRY_COUNT = 6;

    private enum Status {
        IDLE, REGISTER_ELEVATOR,  //注册电梯
        CALL_ELEVATOR,  //正在呼叫电梯，还未收到电梯回复消息
        WAIT_ELEVATOR,  //呼梯成功，在等待到达
        CALL_FAIL,      //呼梯失败
        ARRIVED         //电梯到达
    }

    WaitOutsideState(String name) {
        super(name);
    }

    @Override
    protected void preAction(StateData data, IStateListener listener) {
        super.preAction(data, listener);
        controlUtil = new ElevatorControlUtil(this, mStateData.getCurrentFloorIndex(), true, elevatorControlUtilListener);
        updateCurrentStatus(Status.IDLE);
    }

    @Override
    protected void doAction() {
        super.doAction();

        /**
         * 注意：这里的注册电梯接口调用，是处理进电梯失败放弃此次任务后，再次呼梯的情况。
         * 第一次到达电梯口等待时不是必须在这里重复注册的，但是为了保证机器人在电梯口的状态一致性，并且减少条件判断的复杂度，
         * 这里统一在这里再注册电梯。
         * 如果梯控协议不希望重复注册电梯，可以在梯控层做判断，如果已经注册过电梯，不再重复注册。
         *
         * 从梯控流程灵活性和健壮性考虑，这里统一在这里再注册电梯。
         */
//        registerElevatorCmd();//先注册电梯，注册成功后再呼梯
        //直接注册改为延迟1秒后注册，避免GoGate中释放后这里立即注册，指令间隔太短导致梯控协议层处理不过来。比如自研梯控这种情况忽略了unlock指令。
        updateCurrentStatus(Status.REGISTER_ELEVATOR);
        startRegisterWaitTimer();//首次注册电梯后，开始超时等待
    }

    @Override
    protected void exit() {
        cancelCallWaitTimer();
        cancelRegisterWaitTimer();
        cancelCallElevator();
        if (null != controlUtil) {
            //这里不释放电梯，后面要控制电梯门
            controlUtil.stopControlElevator();
        }
        super.exit();
    }

    @Override
    protected List<StopCommandList.StopCommand> getStopCommands() {
        return new ArrayList<StopCommandList.StopCommand>() {{
            add(new StopCommandList.StopCommand(Definition.CMD_CONTROL_RELEASE_ELEVATOR, null));
        }};
    }

    @Override
    protected BaseState getNextState() {
        if (actionState == Definition.ElevatorAction.ACTION_ELEVATOR_ARRIVED) {
            return StateEnum.ENTER_ELEVATOR.getState();
        }
        return super.getNextState();
    }

    @Override
    protected boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        if (null != controlUtil && controlUtil.cmdResponse(command, result, extraData)) {
            return true;
        }
        switch (command) {
            case Definition.CMD_CONTROL_REGISTER_ELEVATOR:
                handleRegisterElevatorResult(result, extraData);
                return true;
        }
        return super.cmdResponse(cmdId, command, result, extraData);
    }

    @Override
    protected boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        if (null != controlUtil && controlUtil.cmdResponse(command, status, extraData)) {
            return true;
        }
        return super.cmdStatusUpdate(cmdId, command, status, extraData);
    }

    private void updateCurrentStatus(Status status) {
        Log.d(TAG, "updateCurrentStatus " + this.status + " --> " + status);
        this.status = status;
    }

    /**
     * 根据不同事件，获得延迟执行的事件
     * 如进梯失败返回电梯口事件，需延迟一段时间再进行呼梯
     *
     * @return 延迟的毫秒
     */
    private long getDelayStartTime() {
        if (mStateData.getActionCode() == Definition.ElevatorAction.ACTION_BACK_GATE) {
            Log.d(TAG, "need delay start");
            return WAIT_START_TIME_MINUTE;
        }
        return NOT_WAIT_TIME;
    }

    /**
     * 延迟一段时间再执行呼梯
     */
    private void delayStartAction() {
        long delayTime = getDelayStartTime();
        DelayTask.cancel(TAG);
        DelayTask.submit(TAG, new Runnable() {
            @Override
            public void run() {
                if (!isAlive()) {
                    return;
                }
                callElevator();//注册成功后，首次呼梯
                startCallWaitTimer();//首次呼梯后，开始超时等待
            }
        }, delayTime);
    }

    /**
     * 调用注册电梯接口
     * 这里的call一定是在电梯外，所以只需要提前注册电梯
     */
    private void registerElevatorCmd() {
        Log.d(TAG, "registerElevatorCmd: retryCount=" + mRegisterRetryCount);
        super.registerElevator();//2秒重试一次，最多重试3次
    }

    /**
     * 处理注册电梯结果
     */
    private void handleRegisterElevatorResult(String result, String extraData) {
        Log.d(TAG, "startRegisterWaitTimer mRegisterRetryCount=" + mRegisterRetryCount + ", status=" + status);
        if (Definition.SUCCEED.equals(result)) {
            //注册成功后，呼梯
            cancelRegisterWaitTimer();
            delayStartAction();
        }
    }

    /**
     * 调用呼梯接口
     */
    private void callElevator() {
        Log.d(TAG, "callElevator");
        updateCurrentStatus(Status.CALL_ELEVATOR);
        if (null != controlUtil) {
            controlUtil.startCallElevator();
        }
    }

    /**
     * 电梯到达，更新事件：elevator_arrived
     */
    private void elevatorArrived() {
        Log.d(TAG, "elevatorArrived");
        updateCurrentStatus(Status.ARRIVED);
        updateAction(Definition.ElevatorAction.ACTION_ELEVATOR_ARRIVED);
        onStatusUpdate(Definition.STATUS_ELEVATOR_ARRIVED_CURRENT_FLOOR, "arrived current floor");
        onSuccess();
    }

    // ---------    超时    ----------- //

    /**
     * 注册电梯超时处理
     * 2秒一次，最多重试3次
     */
    private synchronized void startRegisterWaitTimer() {
        cancelRegisterWaitTimer();
        startTime = System.currentTimeMillis();
        mRegisterTimer = new Timer();
        mRegisterRetryCount = 0;
        mRegisterTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (!isAlive()) {
                    cancelRegisterWaitTimer();
                    return;
                }

                mRegisterRetryCount++;
                Log.d(TAG, "startRegisterWaitTimer mRegisterRetryCount=" + mRegisterRetryCount + ", status=" + status);
                if (status == Status.REGISTER_ELEVATOR) {
                    if (mRegisterRetryCount > MAX_REGISTER_RETRY_COUNT) {
                        Log.i(TAG, "register wait time out! ");
                        cancelRegisterWaitTimer();
                        waitRegisterElevatorTimeOut();
                    } else {
                        registerElevatorCmd();
                    }
                }
            }
        }, 1 * 1000, 2 * 1000);
    }

    private synchronized void cancelRegisterWaitTimer() {
        if (mRegisterTimer != null) {
            mRegisterTimer.cancel();
            mRegisterTimer = null;
        }
    }

    /**
     * 注册失败
     * 结束递送任务，让给占用电梯的机器人
     */
    private void waitRegisterElevatorTimeOut() {
        Log.d(TAG, "waitRegisterElevatorTimeOut: status=" + status);
        onResult(Definition.ERROR_CALL_ELEVATOR_FAILED, Definition.PARAMS_TIMEOUT);
    }

    /**
     * 呼梯超时处理
     * <p>
     * 呼叫电梯超出处理：
     * 1.每1分钟重新调用一次呼梯接口(去掉此逻辑，通信问题由Elevator层保障，这里不重发呼梯指令)
     * 2.每5分钟判断一次当前状态，如果是呼梯成功（指令应答），但电梯一直未到达，则重置等待时间，无限循环
     * 3.如果呼梯失败（指令未应答），直接返回失败
     */
    private synchronized void startCallWaitTimer() {
        cancelCallWaitTimer();
        startTime = System.currentTimeMillis();
        mCallTimer = new Timer();
        mCallTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (!isAlive()) {
                    cancelCallWaitTimer();
                    return;
                }

                //呼叫电梯超时处理
                if (status == Status.CALL_ELEVATOR) {
                    if (System.currentTimeMillis() - startTime > WAIT_TIMEOUT_MINUTE) {
                        Log.i(TAG, "wait time out! ");
                        cancelCallWaitTimer();
                        waitCallElevatorTimeOut();
                    } else {
                        callElevator();//startCallWaitTimer, 1分钟一次
                    }
                }
            }
        }, 60 * 1000, 60 * 1000);
    }

    private synchronized void cancelCallWaitTimer() {
        if (mCallTimer != null) {
            mCallTimer.cancel();
            mCallTimer = null;
        }
    }

    /**
     * 超时处理
     */
    private void waitCallElevatorTimeOut() {
        if (status == Status.WAIT_ELEVATOR) {
            //通知用户：呼梯成功但电梯一直未到达；
            onStatusUpdate(Definition.STATUS_UPDATE_RETRY_ERROR, Definition.STATUS_CALL_SUC_ELEVATOR_NOT_ARRIVED);
            startCallWaitTimer(); //超时后，重新设置超时时间继续等待，直到电梯到达
        } else {
            onResult(Definition.ERROR_CALL_ELEVATOR_FAILED, Definition.PARAMS_TIMEOUT);
        }
    }

    /**
     * 控制电梯开门时状态监听
     */
    private final ElevatorControlUtilListener elevatorControlUtilListener = new ElevatorControlUtilListener() {
        @Override
        public void onElevatorStatusUpdate(int id, String param, String extraData) {
            if (id == Definition.STATUS_ELEVATOR_FLOOR_INFO && status != Status.WAIT_ELEVATOR) {
                updateCurrentStatus(Status.WAIT_ELEVATOR);
            }
            onStatusUpdate(id, param, extraData);
        }

        @Override
        public void onElevatorResult(int id, String param) {
            onElevatorCmdResult(id, param);
        }

        @Override
        public void onSuccess() {
            elevatorArrived();
        }
    };

    private void onElevatorCmdResult(int id, String param) {
        Log.d(TAG, "onElevatorCmdResult status:" + id + " , data:" + param);
        switch (id) {
            case Definition.ERROR_ELEVATOR_CONTROL:
            case Definition.ERROR_CALL_ELEVATOR_FAILED:
            case Definition.ERROR_CLOSE_ELEVATOR_DOOR_FAILED:
            case Definition.ERROR_OPEN_ELEVATOR_DOOR_FAILED:
                onResult(Definition.ERROR_CALL_ELEVATOR_FAILED, param);
                break;
            default:
                onResult(id, param);
                break;
        }
    }


}
