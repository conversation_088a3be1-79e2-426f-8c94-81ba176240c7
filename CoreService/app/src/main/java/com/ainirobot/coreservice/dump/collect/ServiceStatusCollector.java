package com.ainirobot.coreservice.dump.collect;

import com.ainirobot.coreservice.dump.manager.DataDumpManager;

/**
 * Service status data.
 * Created by Orion on 2020/5/25.
 */
public class ServiceStatusCollector extends AbstractCollector {

    public ServiceStatusCollector(DataDumpManager mManager) {
        super(mManager);
    }

    @Override
    protected boolean startCollect(String param) {
        return true;
    }

    @Override
    protected boolean stopCollect() {
        return true;
    }
}
