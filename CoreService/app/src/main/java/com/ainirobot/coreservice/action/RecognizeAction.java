/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.coreservice.action;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.PictureInfo;
import com.ainirobot.coreservice.client.actionbean.RecognizeBean;
import com.ainirobot.coreservice.client.listener.Person;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.utils.VisionUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class RecognizeAction extends AbstractAction<RecognizeBean> {

    private static final String TAG = RecognizeAction.class.getSimpleName();
    private static final int PICTURE_NUMBER = 1;

    private enum State {
        IDLE, GET_PICTURE, REMOTE_DETECT
    }

    private int mReqId = 0;
    private int mPersonId;
    private String mPicPath;
    private State mState;

    RecognizeAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_RECOGNIZE_v0, resList);
    }

    @Override
    protected boolean startAction(RecognizeBean parameter, LocalApi api) {
        mPersonId = parameter.getPersonId();
        String picPath = parameter.getPicturePath();
        Log.d(TAG, "startAction personId: " + mPersonId + ", picPath: " + picPath);
        if (TextUtils.isEmpty(picPath)) {
            updateState(State.GET_PICTURE);
            mApi.getPictureById(mReqId, mPersonId, PICTURE_NUMBER);
        } else {
            updateState(State.REMOTE_DETECT);
            PictureInfo info = new PictureInfo();
            List<String> picList = new ArrayList<>();
            picList.add(picPath);
            info.setPictures(picList);
            mApi.getRemotePersonInfo(mReqId, info);
        }
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_HEAD_GET_PICTURE_BY_ID:
                handleGetPictureById(result);
                break;

            case Definition.CMD_REMOTE_GET_PERSON_INFO:
                handleRemoteDetect(result);
                break;
        }
        return true;
    }

    private void handleGetPictureById(String result) {
        Log.d(TAG, "handleGetPictureById result: " + result + ", state: " + mState);
        if (mState != State.GET_PICTURE) {
            return;
        }
        String status = null;
        JSONArray pictures = null;
//        int personId = -1;
        try {
            JSONObject json = new JSONObject(result == null ? "" :
                    result);
            status = json.optString("status");
            pictures = json.optJSONArray("pictures");
//            personId = json.optInt("id", -1);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        //TODO HeadService未返回真实的personId
        Log.d(TAG, "handleGetPictureById status: " + status +", pictures: " + pictures);
        if (pictures == null || pictures.length() <= 0
                || TextUtils.isEmpty(pictures.optString(0))) {
            onError(Definition.RESULT_FAILURE, "get picture failed");
        } else {
            updateState(State.REMOTE_DETECT);
            mPicPath = pictures.optString(0);
            PictureInfo info = new PictureInfo();
            List<String> picList = new ArrayList<>();
            picList.add(mPicPath);
            info.setPictures(picList);
            info.setId(String.valueOf(mPersonId));
            info.setStatus(status);
            mApi.getRemotePersonInfo(mReqId, info);
        }
    }

    private void handleRemoteDetect(String result) {
        Log.d(TAG, "remoteDetect result: " + result);
        if (mState != State.REMOTE_DETECT) {
            return;
        }
        if (VisionUtils.isRemoteDetectSuccess(result)) {
            Person.Remote remote = VisionUtils.updateRemoteData(result);
            if (remote != null) {
                onResult(Definition.RESULT_SUCCEED, mGson.toJson(remote));
            } else {
                onError(Definition.RESULT_FAILURE, "remote detect failed");
            }
        } else {
            onError(Definition.RESULT_FAILURE, "remote detect failed");
        }
    }

    private void updateState(State newState) {
        Log.d(TAG, "updateState: newState: " + newState + ", state: " + mState);
        mState = newState;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        VisionUtils.deletePicture(mPicPath);
        mState = State.IDLE;
        return true;
    }

    @Override
    protected boolean verifyParam(RecognizeBean param) {
        return param != null;
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return null;
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return null;
    }
}
