package com.ainirobot.coreservice.bi.report;

import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.Person;
import com.ainirobot.coreservice.client.upload.bi.CallChainDef;
import com.ainirobot.coreservice.client.upload.bi.CallChainReport;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Created by Orion on 2020/4/29.
 */
public class NavigationFollowActionReporter extends CallChainReport {

    private NavigationFollowActionReporter() {
        super(CallChainDef.NAME_NAVIGATION_BODY_FOLLOW);
    }

    public static class Builder {

        public static NavigationFollowActionReporter build() {
            return new NavigationFollowActionReporter();
        }

        public static NavigationFollowActionReporter buildRobotOsReporter() {
            return (NavigationFollowActionReporter) Builder.build()
                    .addSourceRobotOs()
                    .addPackageNameCoreService();
        }

        public static NavigationFollowActionReporter buildVisionSdkReporter() {
            return (NavigationFollowActionReporter) Builder.build()
                    .addSourceVisionSdk()
                    .addPackageNameCoreService();
        }

    }

    public static void startActionReport(String name, String params) {
        Builder.buildRobotOsReporter()
                .addNodeMethodTypeLifecycle()
                .addNodeMethodName(name)
                .addNodeParams(params)
                .report();
    }

    public static void stopActionReport(String name) {
        Builder.buildRobotOsReporter()
                .addNodeMethodTypeLifecycle()
                .addNodeMethodName(name)
                .report();
    }

    public static void onFollowStopReport(String name) {
        Builder.buildRobotOsReporter()
                .addNodeMethodTypeFunction()
                .addNodeMethodName(name)
                .report();
    }

    public static void setTrackTargetReport(String name, String personName, int personId,
                                            String trackMode) {
        JSONObject params = new JSONObject();
        try {
            params.put("personName", personName);
            params.put("personId", personId);
            params.put("trackMode", trackMode);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodTypeApiCall()
                .addNodeMethodName(name)
                .addNodeParams(params.toString())
                .report();
    }

    public static void handleTrackResultReport(String name, String params) {
        Builder.buildVisionSdkReporter()
                .addNodeMethodTypeApiCallback()
                .addNodeMethodName(name)
                .addNodeResult(params)
                .report();
    }

    public static void setObstaclesSafeDistanceReport(String name, double mDistanceR2P) {
        JSONObject params = new JSONObject();
        try {
            params.put("mDistanceR2P", mDistanceR2P);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodTypeApiCall()
                .addNodeMethodName(name)
                .addNodeParams(params.toString())
                .report();
    }

    public static void setObstaclesSafeDistanceResponseReport(String name, String result) {
        Builder.buildRobotOsReporter()
                .addNodeMethodTypeApiCallback()
                .addNodeMethodName(name)
                .addNodeResult(result)
                .report();
    }

    public static void getAllPersonInfosReport(String name) {
        Builder.buildRobotOsReporter()
                .addNodeMethodTypeApiCall()
                .addNodeMethodName(name)
                .report();
    }

    public static void handleBodyInfosReport(String name, String params) {
        Builder.buildVisionSdkReporter()
                .addNodeMethodTypeApiCallback()
                .addNodeMethodName(name)
                .addNodeResult(params)
                .report();
    }

    public static void registerPoseListenerReport(String name) {
        Builder.buildRobotOsReporter()
                .addNodeMethodTypeApiCall()
                .addNodeMethodName(name)
                .report();
    }

    public static void registerEstimateListenerReport(String name) {
        Builder.buildRobotOsReporter()
                .addNodeMethodTypeApiCall()
                .addNodeMethodName(name)
                .report();
    }

    public static void handleAvoidStateReport(String name, String result) {
        Builder.buildVisionSdkReporter()
                .addNodeMethodTypeApiCallback()
                .addNodeMethodName(name)
                .addNodeResult(result)
                .report();
    }

    public static void onFollowStartReport(String name) {
        Builder.buildRobotOsReporter()
                .addNodeMethodTypeFunction()
                .addNodeMethodName(name)
                .report();
    }

    public static void checkIsNeedFollowReportEntrance(String name, String personState) {
        JSONObject params = new JSONObject();
        try {
            params.put("personState", personState);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodTypeFunction()
                .addNodeMethodName(name)
                .addNodeEntrance(params.toString())
                .report();
    }

    public static void checkIsNeedFollowReportMiddle(String name, String person) {
        JSONObject params = new JSONObject();
        try {
            params.put("person", person);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodTypeFunction()
                .addNodeMethodName(name)
                .addNodeMiddle(params.toString())
                .report();
    }

    public static void checkIsNeedFollowReportExport(String name, String msg) {
        JSONObject params = new JSONObject();
        try {
            params.put("msg", msg);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodTypeFunction()
                .addNodeMethodName(name)
                .addNodeExport(params.toString())
                .report();
    }

    public static void keepMotionReportExport(String name, int personId, int angle, double distance) {
        JSONObject params = new JSONObject();
        try {
            params.put("personNull", false);
            params.put("personId", personId);
            params.put("angle", angle);
            params.put("distance", distance);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodTypeFunction()
                .addNodeMethodName(name)
                .addNodeExport(params.toString())
                .report();
    }

    public static void mFollowTaskReport(String name, String exception) {
        JSONObject params = new JSONObject();
        try {
            params.put("exception", exception);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodTypeFunction()
                .addNodeMethodName(name)
                .addNodeExport(params.toString())
                .report();
    }

    public static void isRobotEstimateReport(String name) {
        Builder.buildRobotOsReporter()
                .addNodeMethodTypeApiCall()
                .addNodeMethodName(name)
                .report();
    }

    public static void processPoseEstimateReport(String name, String params) {
        Builder.buildVisionSdkReporter()
                .addNodeMethodTypeApiCallback()
                .addNodeMethodName(name)
                .addNodeResult(params)
                .report();
    }

    public static void motionArcReport(String name, float distance, float angle, float headSpeed,
                                       float latency, boolean bodyFollow) {
        JSONObject params = new JSONObject();
        try {
            params.put("distance", distance);
            params.put("angle", angle);
            params.put("headSpeed", headSpeed);
            params.put("latency", latency);
            params.put("bodyFollow", bodyFollow);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodTypeApiCall()
                .addNodeMethodName(name)
                .addNodeParams(params.toString())
                .report();
    }

    public static void goPositionReport(String name, double x, double y, double theta,
                                        double linearSpeed, double angularSpeed,
                                        boolean isAdjustAngle, boolean keepMove) {
        JSONObject params = new JSONObject();
        try {
            params.put("x", x);
            params.put("y", y);
            params.put("theta", theta);
            params.put("linearSpeed", linearSpeed);
            params.put("angularSpeed", angularSpeed);
            params.put("isAdjustAngle", isAdjustAngle);
            params.put("keepMove", keepMove);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodTypeApiCall()
                .addNodeMethodName(name)
                .addNodeParams(params.toString())
                .report();
    }

    public static void calculateDestinationPoseReport(String name, Person person, Pose pose, Pose desPose) {
        JSONObject params = new JSONObject();
        try {
            params.put("person", person.toString());
            params.put("pose", pose.toString());
            params.put("desPose", desPose.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodTypeFunction()
                .addNodeMethodName(name)
                .addNodeParams(params.toString())
                .report();
    }

    public static void handleNavigationOutOfMapReport(String name) {
        Builder.buildRobotOsReporter()
                .addNodeMethodTypeFunction()
                .addNodeMethodName(name)
                .report();
    }

    public static void goNextCorrectPoseReport(String name) {
        Builder.buildRobotOsReporter()
                .addNodeMethodTypeFunction()
                .addNodeMethodName(name)
                .report();
    }

    public static void clearBlurPoseReport(String name) {
        Builder.buildRobotOsReporter()
                .addNodeMethodTypeFunction()
                .addNodeMethodName(name)
                .report();
    }

    public static void statusReport(String name, int status, String data) {
        JSONObject params = new JSONObject();
        try {
            params.put("status", status);
            params.put("data", data);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodName(name)
                .addStatus(params.toString())
                .report();
    }

    public static void resultReport(String name, int code, String msg) {
        JSONObject params = new JSONObject();
        try {
            params.put("code", code);
            params.put("msg", msg);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodName(name)
                .addResult(params.toString())
                .report();
    }

}
