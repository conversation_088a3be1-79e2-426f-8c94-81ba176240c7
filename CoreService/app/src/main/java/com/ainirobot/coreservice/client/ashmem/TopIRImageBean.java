package com.ainirobot.coreservice.client.ashmem;

import android.support.annotation.NonNull;
import android.util.Log;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;

/**
 * Top IR图的实体
 */
public class TopIRImageBean extends BaseShareMemBean {

    private long timeStamp ; // 时间戳
    private int type_size; // 单个像素对应的字节数
    private int width;
    private int height;
    private int stride;  //
    private int imageDataLength; //深度图数据区长度
    private byte[] imageData; //深度图数据区，每两个字节表示Rgbd摄像头前物体的距离

    public TopIRImageBean() {
        this.type = ShareDataType.TOPIR_IMG.getType();
    }

    public TopIRImageBean(long timeStamp, int type_size, int width, int height, int stride, int imageDataLength, byte[] imageData) {
        this.type = ShareDataType.TOPIR_IMG.getType();
        this.timeStamp = timeStamp;
        this.type_size = type_size;
        this.width = width;
        this.height = height;
        this.stride = stride;
        this.imageDataLength = imageDataLength;
        this.imageData = imageData;
    }

    public long getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(long timeStamp) {
        this.timeStamp = timeStamp;
    }

    public int getType_size() {
        return type_size;
    }

    public void setType_size(int type_size) {
        this.type_size = type_size;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public int getStride() {
        return stride;
    }

    public void setStride(int stride) {
        this.stride = stride;
    }

    public int getImageDataLength() {
        return imageDataLength;
    }

    public void setImageDataLength(int imageDataLength) {
        this.imageDataLength = imageDataLength;
    }

    public byte[] getImageData() {
        return imageData;
    }

    public void setImageData(byte[] imageData) {
        this.imageData = imageData;
    }

    /**
     * 把bean文件数据写入字节数组
     *
     * @return
     */
    public byte[] toBytes() {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        DataOutputStream dos = new DataOutputStream(bos);
        try {
            dos.writeInt(type);
            dos.writeLong(timeStamp);
            dos.writeInt(type_size);
            dos.writeInt(width);
            dos.writeInt(height);
            dos.writeInt(stride);
            dos.writeInt(imageDataLength);
            dos.write(imageData);
            Log.d("TopIRImageBean", "toBytes imageData length=" + imageData.length);
            return bos.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
            Log.e("TopIRImageBean", "beanToStream:e: " + e.getMessage());
            return null;
        } finally {
            close(dos);
            close(bos);
        }
    }


    @Override
    public String toString() {
        return "TopIRImageBean{" +
                "type=" + type +
                "timeStamp=" + timeStamp +
                "type_size=" + type_size +
                ", width=" + width +
                ", width=" + height +
                ", stride=" + stride +
                ", imageDataLength=" + imageDataLength +
                ", imageData=" + Arrays.toString(imageData) +
                '}';
    }

    public void close(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
