package com.ainirobot.coreservice.client.actionbean;

import com.ainirobot.coreservice.client.SettingsUtil;

/**
 * Created by orion on 2018/4/12.
 */

public class AngleResetBean extends BaseBean {

    private String destination;
    private String poseJson;
    private double mLinearSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED;
    private double mAngularSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED;

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public String getPoseJson() {
        return poseJson;
    }

    public void setPoseJson(String poseJson) {
        this.poseJson = poseJson;
    }

    public double getLinearSpeed(){
        return mLinearSpeed;
    }

    public void setLinearSpeed(double linearSpeed){
        this.mLinearSpeed = linearSpeed;
    }

    public double getAngularSpeed(){
        return mAngularSpeed;
    }

    public void setAngularSpeed(double angularSpeed){
        this.mAngularSpeed = angularSpeed;
    }

}
