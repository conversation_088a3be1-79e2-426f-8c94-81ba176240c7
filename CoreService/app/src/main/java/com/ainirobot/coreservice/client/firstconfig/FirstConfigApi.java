/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.client.firstconfig;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.IFirstConfigRegistry;
import com.ainirobot.coreservice.IStatusListener;
import com.ainirobot.coreservice.client.BaseApi;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.IntentUtil;
import com.ainirobot.coreservice.client.actionbean.AutoChargeBean;
import com.ainirobot.coreservice.client.actionbean.CommandBean;
import com.ainirobot.coreservice.client.actionbean.InspectActionBean;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.listener.IActionListener;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import org.json.JSONException;
import org.json.JSONObject;

import static com.ainirobot.coreservice.client.RobotApi.ERROR_REMOTE_VISITOR;

public class FirstConfigApi extends BaseApi {

    private IFirstConfigRegistry firstConfigRegistry;
    private volatile boolean mIsServiceConnected = false;
    private static Gson mGson = new Gson();

    private FirstConfigApi() {
        super();
    }

    private static class InstanceHolder {
        private static final FirstConfigApi instance = new FirstConfigApi();
    }

    public static FirstConfigApi getInstance() {
        return InstanceHolder.instance;
    }

    /**
     * Connect API
     */
    public void connectApi(Context context) {
        Intent intent = IntentUtil.createExplicitIntent(Definition.CORE_SERVICE_NAME,
                IFirstConfigRegistry.class.getName());
        context.bindService(intent, apiConnection, 0);
    }

    /**
     * Disconnect API
     */
    public void disconnectApi(Context context) {
        try {
            context.unbindService(apiConnection);
        } catch (IllegalArgumentException e) {
            // Nothing to do
        }
    }

    /**
     * API connection
     */
    protected ServiceConnection apiConnection = new ServiceConnection() {
        public void onServiceConnected(ComponentName className, IBinder service) {
            Log.d("FC_core", "FCApi onServiceConnected");
            firstConfigRegistry = IFirstConfigRegistry.Stub.asInterface(service);
            mIsServiceConnected = true;
            notifyEventApiConnected();
        }

        public void onServiceDisconnected(ComponentName className) {
            Log.d("FC_core", "FCApi onServiceDisconnected");
            mIsServiceConnected = false;
            notifyEventApiDisconnected();
            firstConfigRegistry = null;
        }
    };

    public int sendFirstConfigRequest(String type, String params) {
        try {
            Log.d("FC_core", "sendFirstConfigRequest type = " + type + ", param = " + params);
            return firstConfigRegistry.sendFirstConfigRequest(type, params);
        } catch (RemoteException e) {
            e.printStackTrace();
            return -1;
        }
    }

    public int sendFirstConfigCommand(String cmdAction, String cmdParam) {
        try {
            return firstConfigRegistry.sendFirstConfigCommand(cmdAction, cmdParam);
        } catch (RemoteException e) {
            e.printStackTrace();
            return -1;
        }
    }


    public String registerStatusListener(String type, IStatusListener listener) {
        try {
            return firstConfigRegistry.registerStatusListener(type, listener);
        } catch (RemoteException e) {
            e.printStackTrace();
            return null;
        }
    }

    public boolean unregisterStatusListener(String id) {
        try {
            return firstConfigRegistry.unregisterStatusListener(id);
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }

    public boolean isServiceConnected() {
        return mIsServiceConnected;
    }

    /**
     * timeout 3 minutes default .
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int startInspection(int reqId, ActionListener listener) {
        //TODO  need to modify 3 minute later .
        return startInspection(reqId, 30 * 1000, listener);
    }

    public int startInspection(int reqId, long time, ActionListener listener) {
        Log.d("FC_core", "========== startInspection timeout = " + time + " ==================");
        InspectActionBean bean = new InspectActionBean();
        bean.setReqId(reqId);
        bean.setTimeOut(time);
        String jsonStr = mGson.toJson(bean);

        try {
            IActionListener actionListener = null;
            if (listener != null) {
                actionListener = mMessageDispatcher.obtainActionDispatcher(listener);
            }
            return firstConfigRegistry.startAction(reqId, Definition.ACTION_INSPECTION, jsonStr, actionListener);
        } catch (RemoteException e) {
            e.printStackTrace();
            return ERROR_REMOTE_VISITOR;
        }
    }


    /**
     * remote request QRcode
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int remoteRequestQrcode(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_QRCODE, null, false);
        Log.d("FC_core", "FCApi remoteRequestQrcode reqId = " + reqId);
        return startAction(reqId, Definition.ACTION_REMOTE_QRCODE,
                mGson.toJson(bean), parseCommand(listener));
    }

    public int remoteBindStatus(int reqId, CommandListener listener) {
        JsonObject obj = new JsonObject();
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_BIND_STATUS, obj.toString(), false);
        Log.d("FC_core", "FCApi remoteBindStatus　reqId = " + reqId);
        return startAction(reqId, Definition.ACTION_REMOTE_BIND_STATUS,
                mGson.toJson(bean), parseCommand(listener));
    }

    public int getPosition(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_POSITION, null, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_POSITION,
                mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * @param reqId
     * @param direction 前后左右方向
     *                  CMD_NAVI_MOVE_SUB_FORWARD = "forward";
     *                  CMD_NAVI_MOVE_SUB_BACKWARD = "backward";
     *                  CMD_NAVI_MOVE_SUB_TURN_LEFT = "turn_left";
     *                  CMD_NAVI_MOVE_SUB_TURN_RIGHT = "turn_right";
     *                  CMD_NAVI_MOVE_SUB_TURN_BACK = "turn_back";
     *                  CMD_NAVI_MOVE_SUB_ROTATE = "rotate";
     *                  CMD_NAVI_MOVE_SUB_STOP = "stop";
     * @param speed     how many meters to go per second
     * @param distance  the distance to move
     * @param listener
     * @return
     */
    public int motionLine(int reqId, String direction, float speed, float distance, final
    CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_DERICTION, direction);
            param.put(Definition.JSON_NAVI_DISTANCE, distance);
            param.put(Definition.JSON_NAVI_LINEAR_SPEED, speed);

            CommandBean bean = new CommandBean();
            bean.setContinue(false);
            bean.setParams(param.toString());
            bean.setCmdType(Definition.CMD_NAVI_MOVE_DIRECTION);

            String params = mGson.toJson(bean);
            ActionListener al = parseCommand(listener);

            return startAction(reqId, Definition.ACTION_NAVI_MOVE_DIRECTION, params, al);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    /**
     * switch to auto charge mode , to control rover .
     *
     * @param reqId
     * @return
     */
    public int switchChargeMode(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SWITCH_AUTO_CHARGE_MODE, null, false);
        return startAction(reqId, Definition.ACTION_SWITCH_AUTO_CHARGE_MODE, mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * start auto charge
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int startAutoCharge(int reqId, CommandListener listener) {
        Log.d("FC_core", "startAutoCharge reqId = " + reqId);
//        switchChargeMode(reqId);
        CommandBean bean = new CommandBean(Definition.CMD_CAN_AUTO_CHARGE_START, null, false);
        return startAction(reqId, Definition.ACTION_AUTO_CHARGE_START, mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * stop auto charge
     *
     * @param reqId
     * @return
     */
    public int stopAutoCharge(int reqId) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_AUTO_CHARGE_END, null, false);
        return startAction(reqId, Definition.ACTION_AUTO_CHARGE_END, mGson.toJson(bean), null);
    }


    /**
     * After starting autoCharge , get status of autoCharge.
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int getChargeStatus(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_CHARGE_STATUS, null, false);
        return startAction(reqId, Definition.ACTION_AUTO_CHARGE_STATUS, mGson.toJson(bean), parseCommand(listener));
    }


    /**
     * set startChargePose
     *
     * @param reqId
     * @param listener
     * @param timeout  timeout of this action
     * @return
     */
    public int setStartChargePoseAction(int reqId, long timeout, ActionListener listener) {
        AutoChargeBean bean = new AutoChargeBean();
        bean.setReqId(reqId);
        bean.setTimeout(timeout);
        String toJson = mGson.toJson(bean);
        return startAction(reqId, Definition.ACTION_SET_START_CHARGE_POSE, toJson, listener);
    }

    /**
     * navigate to startChargePoint and then start auto_charge.
     *
     * @param reqId
     * @param timeout  timeout to charge done.
     * @param listener
     * @return
     */
    public int startNaviToAutoCharge(int reqId, long timeout, ActionListener listener) {
        AutoChargeBean bean = new AutoChargeBean();
        bean.setReqId(reqId);
        bean.setTimeout(timeout);
        String toJson = mGson.toJson(bean);
        return startAction(reqId, Definition.ACTION_AUTO_NAVI_CHARGE, toJson, listener);

    }

    /**
     * @param reqId
     * @param status   start/finish/running
     * @param msg      String err msg/JsonString position
     * @param result   boolean true/false
     * @param listener
     * @return
     */
    public int remoteChargePile(int reqId,
                                String status,
                                String msg,
                                boolean result,
                                CommandListener listener) {
        try {
            Log.d("FC_core", "remoteChargePile msg : " + msg);
            JsonObject value = new JsonObject();
            value.addProperty("status", status);
            value.addProperty("name", Definition.START_BACK_CHARGE_POSE);
            if (!TextUtils.isEmpty(msg)) {
                JsonElement msgJson = new JsonParser().parse(msg);
                if (msgJson != null && msgJson.isJsonObject()) {
                    value.add("position", msgJson);
                } else {
                    value.addProperty("msg", msg);
                }
            }

            JsonObject obj = new JsonObject();
            obj.addProperty("type", Definition.REQ_FIRST_SET_CHARGING_PILE);
            obj.add("value", value);
            obj.addProperty("result", result);
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_CHARGE_PILE, mGson.toJson(obj), false);
            Log.d("FC_core", "FCApi remoteChargePile　reqId = " + reqId);
            return startAction(reqId, Definition.ACTION_REMOTE_CHARGE_PILE,
                    mGson.toJson(bean), parseCommand(listener));
        } catch (Throwable e) {
            Log.e("FC_core", "FCApi remoteChargePile e : " + e.getLocalizedMessage());
            return 0;
        }

    }

    /**
     * report to result of auto_charge
     *
     * @param reqId
     * @param status
     * @param result
     * @param listener
     * @return
     */
    public int remoteFirstCharge(int reqId,
                                 String status,
                                 boolean result,
                                 CommandListener listener) {
        try {
            JsonObject value = new JsonObject();
            value.addProperty("status", status);

            JsonObject obj = new JsonObject();
            obj.addProperty("type", Definition.REQ_FIRST_CHARGING);
            obj.add("value", value);
            obj.addProperty("result", result);

            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_FIRST_CHARGING, mGson.toJson(obj), false);
            Log.d("FC_core", "FCApi remoteFirstCharge　reqId = " + reqId);
            return startAction(reqId, Definition.ACTION_REMOTE_FIRST_CHARGING,
                    mGson.toJson(bean), parseCommand(listener));
        } catch (Throwable e) {
            Log.e("FC_core", "remoteFirstCharge e : " + e.getLocalizedMessage());
            return 0;
        }
    }

    /**
     * get Pose by place name
     *
     * @param reqId
     * @param param    the place name
     * @param listener
     * @return
     */
    public int getPlace(int reqId, String param, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_PLACE_NAME, param, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_PLACE_NAME, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * request is Robot estimate
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int isRobotEstimate(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_IS_ESTIMATE, "", false);
        return startAction(reqId, Definition.ACTION_NAVI_IS_ESTIMATE, mGson.toJson(bean),
                parseCommand(listener));
    }


    private int startAction(int reqId, int actionId, String params, ActionListener listener) {
        try {
            IActionListener actionListener = null;
            if (listener != null) {
                actionListener = mMessageDispatcher.obtainActionDispatcher(listener);
            }
            return firstConfigRegistry.startAction(reqId, actionId, params, actionListener);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    private ActionListener parseCommand(final CommandListener listener) {
        if (listener == null) {
            return null;
        }
        ActionListener al = new ActionListener() {
            @Override
            public void onResult(int status, String responseString) throws RemoteException {
                listener.onResult(status, responseString);
                listener.onResult(status, responseString, "");
            }
        };
        return al;
    }

    public int postSetPlaceToServer(int reqId, String params) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_POST_SET_PLACE, params, false);
        return startAction(reqId, Definition.ACTION_REMOTE_POST_SET_PLACE,
                mGson.toJson(bean), null);
    }

//    public void textToSpeech(String text){
//        try {
//            Log.d("FC_core","FCApi textToSpeech text = "+text);
//            firstConfigRegistry.textToSpeech(text);
//        } catch (RemoteException e) {
//            e.printStackTrace();
//        }
//    }
//
//    public void toneToSpeech(String type){
//        try {
//            firstConfigRegistry.toneToSpeech(type);
//        } catch (RemoteException e) {
//            e.printStackTrace();
//        }
//    }


}
