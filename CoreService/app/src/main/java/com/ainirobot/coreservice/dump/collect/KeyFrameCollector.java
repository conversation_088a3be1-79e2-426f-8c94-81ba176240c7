package com.ainirobot.coreservice.dump.collect;

import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.dump.manager.DataDumpManager;

/**
 * Key frame in body follow mode witch image is used for init the body.(.jpg)
 * Created by Orion on 2020/5/25.
 */
public class KeyFrameCollector extends AbstractCollector {
    private static final String TAG = KeyFrameCollector.class.getSimpleName();

    public KeyFrameCollector(DataDumpManager mManager) {
        super(mManager);
    }

    @Override
    protected int getPermission() {
        return RobotSettingApi.getInstance().getRobotInt(Definition.DATA_DUMP_RIGHTS_VISION_RECORD);
    }

    @Override
    protected boolean startCollect(String param) {
        Log.d(TAG, "startCollect : param=" + param);
        getPicture();
        //TODO 怎么和视频关联？只有跟随触发的视频才有关键帧
        return true;
    }

    @Override
    protected boolean stopCollect() {
        Log.d(TAG, "stopCollect");
        return true;
    }

    private void exitCollect() {
        Log.d(TAG, "exitCollect");
        super.stop();
    }

    private void getPicture() {
        Log.d(TAG, "getPicture");
        mSystemApi.takePicture(mReq, null, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(TAG, "getPicture onResult : " + result + ", " + message);
                exitCollect();
            }
        });
    }

}
