package com.ainirobot.coreservice.client.actionbean;

/**
 * Created by orion on 2018/4/12.
 */

public class BodyFollowBean extends BaseBean {

    private int personID;
    private int type;
    private long lostTime;
    private long setTrackTime;
    private double linearSpeed;
    private double angularSpeed;
    private double distanceR2P;

    public double getDistanceR2P() {
        return distanceR2P;
    }

    public void setDistanceR2P(double distanceR2P) {
        this.distanceR2P = distanceR2P;
    }

    public int getPersonID() {
        return personID;
    }

    public void setPersonID(int personID) {
        this.personID = personID;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public long getLostTime() {
        return lostTime;
    }

    public void setLostTime(long lostTime) {
        this.lostTime = lostTime;
    }

    public double getLinearSpeed() {
        return linearSpeed;
    }

    public void setLinearSpeed(double linearSpeed) {
        this.linearSpeed = linearSpeed;
    }

    public double getAngularSpeed() {
        return angularSpeed;
    }

    public void setAngularSpeed(double argularSpeed) {
        this.angularSpeed = argularSpeed;
    }

    public long getSetTrackTime() {
        return setTrackTime;
    }

    public void setSetTrackTime(long setTrackTime) {
        this.setTrackTime = setTrackTime;
    }

    @Override
    public String toString() {
        return "BodyFollowBean{" +
                "personID=" + personID +
                ", type=" + type +
                ", lostTime=" + lostTime +
                ", setTrackTime=" + setTrackTime +
                ", linearSpeed=" + linearSpeed +
                ", angularSpeed=" + angularSpeed +
                ", distanceR2P=" + distanceR2P +
                '}';
    }
}
