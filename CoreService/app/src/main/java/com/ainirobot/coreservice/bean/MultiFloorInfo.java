package com.ainirobot.coreservice.bean;

import com.ainirobot.coreservice.client.actionbean.Pose;

import java.util.List;

/**
 * Data: 2022/6/9 11:39 上午
 * Author: wanglijing
 * <p>
 * 多楼层配置信息
 */
public class MultiFloorInfo implements Cloneable{


    /**
     * id 主键
     */
    private int floorId;
    /**
     * 楼层index映射
     * lora发送给梯控时映射的id
     */
    private int floorIndex;
    /**
     * 楼层名称
     * 对应真实的楼层名称，如二楼、大厅
     */
    private String floorAlias;
    /**
     * 楼层类型
     */
    private int floorState;
    /**
     * 地图名称
     */
    private String mapName;

    /**
     * 地图中使用的电梯
     * list不会为空
     */
    private List<String> availableElevators;

    //是否启用电梯等待点
    private int isUseElevatorWaitPoint;
    //电梯等待点
    private List<String> elevatorWaitPoints;
    //电梯等待区
    private String elevatorWaitAreaJson;

    /**
     * 每层地图里的点
     */
    private List<Pose> poseList;
    /**
     * 该楼层的类型
     */
    public static class MultiFloorType {
        public static final int DEFAULT_FLOOR = 0;  //默认楼层
        public static final int MAIN_FLOOR = 1; //主楼层
    }

    public MultiFloorInfo() {
    }

    public int getFloorId() {
        return floorId;
    }

    public void setFloorId(int floorId) {
        this.floorId = floorId;
    }

    public int getFloorIndex() {
        return floorIndex;
    }

    public void setFloorIndex(int floorIndex) {
        this.floorIndex = floorIndex;
    }

    public String getFloorAlias() {
        return floorAlias;
    }

    public void setFloorAlias(String floorAlias) {
        this.floorAlias = floorAlias;
    }

    public String getMapName() {
        return mapName;
    }

    public void setMapName(String mapName) {
        this.mapName = mapName;
    }

    public int getFloorState() {
        return floorState;
    }

    public void setFloorState(int floorState) {
        this.floorState = floorState;
    }

    public List<String> getAvailableElevators() {
        return availableElevators;
    }

    public void setAvailableElevators(List<String> availableElevators) {
        this.availableElevators = availableElevators;
    }

    public int getIsUseElevatorWaitPoint() {
        return this.isUseElevatorWaitPoint;
    }

    public void setIsUseElevatorWaitPoint(int isUse) {
        this.isUseElevatorWaitPoint = isUse;
    }

    public List<String> getElevatorWaitPoints() {
        return this.elevatorWaitPoints;
    }

    public void setElevatorWaitPoints(List<String> elevatorWaitPoints) {
        this.elevatorWaitPoints = elevatorWaitPoints;
    }
    public String getElevatorWaitAreaJson() {
        return this.elevatorWaitAreaJson;
    }

    public void setElevatorWaitAreaJson(String elevatorWaitAreaJson) {
        this.elevatorWaitAreaJson = elevatorWaitAreaJson;
    }
    public List<Pose> getPoseList() {
        return poseList;
    }

    public void setPoseList(List<Pose> poseList) {
        this.poseList = poseList;
    }

    @Override
    public String toString() {
        return "MultiFloorInfo{" +
                "floorId=" + floorId +
                ", floorIndex=" + floorIndex +
                ", floorAlias='" + floorAlias + '\'' +
                ", floorState=" + floorState +
                ", mapName='" + mapName + '\'' +
                ", availableElevators=" + availableElevators +
                ", poseList=" + poseList +
                '}';
    }

    @Override
    public Object clone() {
        MultiFloorInfo bean = null;
        try {
            bean = (MultiFloorInfo) super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return bean;
    }
}
