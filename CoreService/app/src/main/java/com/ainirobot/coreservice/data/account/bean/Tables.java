package com.ainirobot.coreservice.data.account.bean;

import android.net.Uri;

public final class Tables {

    private static final String AUTHORITY = "com.ainirobot.coreservice.accountprovider";
    public static final Uri AUTHORITY_URI = Uri.parse("content://" + AUTHORITY);
    public static final String USER = "user";
    public static final String USER_PRE_RELEASE = "user_pre_release";
    public static final String USER_DEVELOP = "user_develop";
    public static final String FACE = "face";
    public static final String FACE_PRE_RELEASE = "face_pre_release";
    public static final String FACE_DEVELOP = "face_develop";
    public static final String TOKEN = "token";
    public static final String META = "meta";

    public interface UserColumns {
        String USER_ID = "user_id";
        String TOKEN_ID = "token_id";
        String MOBILE = "mobile";
        String ROLE_ID = "role_id";
        String NICK_NAME = "nick_name";
        String AVATAR_URL = "avatar_url";
        String CREATE_TIME = "create_time";
        String UPDATE_TIME = "update_time";
        String IS_RECENT = "is_recent";
    }

    public interface FaceColumns {
        String FACE_ID = "face_id";
        String USER_ID = "user_id";
        String NAME = "face_name";
        String PICTURE_URI = "picture_uri";
        String FEATURE = "feature";
        String REGISTER_TIME = "register_time";
        String IS_ONLY_LOCAL = "is_only_local";
        String IS_DELETE = "is_delete";
    }

    public interface MetaColumns {
        String MODULE = "module";
        String VALUE = "value";
    }

    public interface TokenColumns {
        String TOKEN_ID = "token_id";
        String VALUE = "value";
        String EXPIRES_IN = "expires_in";
        String CREATE_TIME = "create_time";
    }
}
