package com.ainirobot.coreservice.action.advnavi.gatecmd;


import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.utils.DelayTask;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Date: 2024/2/22
 * Author: dengting
 * Description: GateCommand
 */
public abstract class GateCommand {
    protected String TAG = GateCommand.class.getSimpleName();
    protected GateCommandListener listener;
    protected GateCommandInterface commandInterface;
    /**
     * 开始时的命令类型
     */
    protected CmdType intentType;
    /**
     * 正在发送的命令类型
     * <p>
     * 多个命令嵌套时，当前正在执行的命令类型
     */
    private CmdType cmdType;
    protected volatile boolean isAlive = false;
    /**
     * 重试次数
     */
    protected int retryCount = 0;
    /**
     * 失败次数(也是MAX_RETRY_COUNT)
     */
    protected int failCount = 0;
    /**
     * 重试最大次数，默认5次
     */
    protected int MAX_RETRY_COUNT = 5;


    public enum CmdType {
        OPEN_DOOR,
        CLOSE_DOOR,
        CALL_GATE,
        RELEASE_GATE,
    }

    GateCommand(String name,
                GateCommand.CmdType intentType,
                GateCommandInterface commandInterface,
                GateCommandListener listener) {

        updateFailCount(0);
        updateRetryCount(0);
        this.listener = listener;
        this.intentType = intentType;
        this.commandInterface = commandInterface;
        if (TextUtils.isEmpty(name)) {
            TAG = TAG + "-" + getClass().getSimpleName();
        } else {
            TAG = name + "-" + getClass().getSimpleName();
        }
    }

    public void start() {
        Log.d(TAG, "start");
        isAlive = true;
    }

    public void stop() {
        Log.d(TAG, "stop");
        isAlive = false;
        DelayTask.cancel(TAG);
        updateFailCount(0);
    }

    public void onSuccess() {
        updateFailCount(0);
        updateRetryCount(0);
    }

    public void onFail() {
        failCount++;
        updateFailCount(failCount);
        updateRetryCount(0);
    }

    protected abstract void onRetry();

    public void cmdResponse(String command, String result, String extraData) {
        if (!isAlive) {
            Log.d(TAG, "cmdResponse " + cmdType + " not alive");
            return;
        }
        //上报给当前正在运行的命令
        if (getCommandType(command) == cmdType) {
            Log.d(TAG, cmdType + " response [ " + result + " ] extraData:" + extraData);
            defaultCmdResponse(result);
        }
    }

    private CmdType getCommandType(String command) {
        switch (command) {
            case Definition.CMD_CONTROL_CALL_GATE:
                return CmdType.CALL_GATE;
            case Definition.CMD_CONTROL_OPEN_GATE_DOOR:
            case Definition.CMD_CONTROL_OPEN_REV_GATE_DOOR:
                return CmdType.OPEN_DOOR;
            case Definition.CMD_CONTROL_CLOSE_GATE_DOOR:
                return CmdType.CLOSE_DOOR;
            case Definition.CMD_CONTROL_RELEASE_GATE:
                return CmdType.RELEASE_GATE;
        }
        return null;
    }

    private void defaultCmdResponse(String result) {
        Log.d(TAG, "defaultCmdResponse: " + cmdType + result);
        switch (result) {
            case Definition.SUCCEED:
                onSuccess();
                break;
            case Definition.FAILED:
                onFail();
                break;
            case Definition.PARAMS_TIMEOUT:
                onTimeout();
                break;
        }
    }

    public void initCmdType(CmdType cmdType) {
        Log.d(TAG, "initCmdType: " + cmdType);
        this.cmdType = cmdType;

    }

    public void callGateDoor() {
        if (!isEnable()) {
            return;
        }
        commandInterface.cmdCallGate();
    }
    public void openGateDoor() {
        if (!isEnable()) {
            return;
        }
        commandInterface.cmdOpenGateDoor();
    }

    public void closeGateDoor() {
        if (!isEnable()) {
            return;
        }
        commandInterface.cmdCloseGateDoor();
    }


    public void releaseGateControl() {
        if (!isEnable()) {
            return;
        }
        commandInterface.cmdReleaseGate();
    }


    public void onResult(int status, String data) {
        Log.d(TAG, "onResult status:" + status + " ,data:" + data + " cmdType:" + cmdType);
        if (null != listener) {
            try {
                JSONObject object = new JSONObject();
                object.put(Definition.JSON_CMD_TYPE, cmdType.toString());
                object.put(Definition.JSON_VALUE, data);
                listener.onFinish(status, object.toString());
                this.stop();
                return;
            } catch (JSONException e) {
                e.printStackTrace();
            }
            listener.onFinish(status, data);
            this.stop();
        }
    }


    public void onCmdStatusUpdate(int status, String data, String extraData) {
        if (null != listener) {
            listener.onStatusUpdate(status, data, extraData);
        }
    }

    public boolean isEnable() {
        if (!isAlive) {
            Log.e(TAG, "not alive");
            return false;
        }
        if (null == commandInterface) {
            Log.e(TAG, "commandInterface is null");
            onResult(Definition.ERROR_GATE_CONTROL, "command interface is null");
            return false;
        }
        return true;
    }

    /**
     * 超时时默认重试
     */
    public void onTimeout() {
        Log.d(TAG, "onTimeout retry");
        retryCall();
    }

    public void retryCall() {
        if (retryCount > MAX_RETRY_COUNT) {
            retryTooMuchTime();
            return;
        }
        retryCount++;
        updateRetryCount(retryCount);
        onRetry();
    }

    public void updateRetryCount(int count) {
        retryCount = count;
    }

    public void updateFailCount(int count) {
        failCount = count;
        if (failCount > MAX_RETRY_COUNT) {
            Log.e(TAG, " fail too much time");
            onResult(Definition.ERROR_GATE_CONTROL, "fail too much time");
        }
    }

    public CmdType getCmdType() {
        return cmdType;
    }

    private void retryTooMuchTime() {
        Log.e(TAG, cmdType + " retry too much time");
        switch (cmdType) {
            case OPEN_DOOR:
                onResult(Definition.ERROR_OPEN_GATE_DOOR_FAILED, "open door retry too much time");
                break;
            case CLOSE_DOOR:
                onResult(Definition.ERROR_CLOSE_GATE_DOOR_FAILED, "close door retry too much time");
                break;
            case CALL_GATE:
                onResult(Definition.ERROR_CALL_GATE_FAILED, "call floor retry too much time");
                break;
            case RELEASE_GATE:
                onResult(Definition.ERROR_GATE_CONTROL, "release gate retry too much time");
                break;
        }
    }
}
