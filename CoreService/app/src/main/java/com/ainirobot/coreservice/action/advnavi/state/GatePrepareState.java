package com.ainirobot.coreservice.action.advnavi.state;

import android.util.Log;

import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.GatePairPose;

/**
 * Date: 2024/2/22
 * Author: dengting
 * GatePrepareState
 * <p>
 * 准备阶段
 * 后续是否需要将是否过闸机和闸机先后顺序在此设置若是两个pose没有name，则在这把name设上，否则直接取name
 */
public class GatePrepareState extends BaseState {

    GatePrepareState(String name) {
        super(name);
    }

    @Override
    protected void preAction(StateData data, IStateListener listener) {
        super.preAction(data, listener);
    }

    @Override
    protected void doAction() {
        super.doAction();
        //根据参数区分导航类型
        handleNaviTypeByParam();
    }

    /**
     * 根据参数区分导航类型
     */
    private void handleNaviTypeByParam() {
        needPassGate();
    }

    @Override
    protected BaseState getNextState() {
        switch (actionState) {
            //需要过闸机，下一状态：去闸机第一个口
            case ACTION_NEED_GATE:
                return StateEnum.GO_GATE_FIRST.getState();

            //不需要开闸机，下一状态：导航去目的地
            case ACTION_NO_NEED_GATE:
                return StateEnum.GO_DESTINATION.getState();
            default:
                return super.getNextState();
        }
    }


    @Override
    protected boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        if (!isAlive()) {
            Log.d(TAG, "current is not alive");
            return false;
        }
        return super.cmdResponse(cmdId, command, result, extraData);
    }

    /**
     * 需要开闸机
     * 更该改开闸机事件
     */
    private void needPassGate() {
        // 区分多闸机并且兼容单闸机
        if (mStateData.isMultiGateMode()) {
            boolean moveToNextGateResult = mStateData.moveToNextGate();
            Log.d(TAG, "moveToNextGateResult: " + moveToNextGateResult + ", currentGateIndex: " + mStateData.getCurrentGateIndex());
        }
        updateAction(Definition.ElevatorAction.ACTION_NEED_GATE);
        onSuccess();
    }

}
