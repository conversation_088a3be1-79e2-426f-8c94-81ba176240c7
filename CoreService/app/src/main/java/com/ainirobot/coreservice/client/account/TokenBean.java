package com.ainirobot.coreservice.client.account;

public class TokenBean {

    public static final String LOCAL_SYSTEM_TOKEN = "localSystemToken";

    private String tokenId;
    private String value;
    private long expiresIn;
    private long createTime;

    public TokenBean(String tokenId, String value, long expiresIn, long createTime) {
        this.tokenId = tokenId;
        this.value = value;
        this.expiresIn = expiresIn;
        this.createTime = createTime;
    }

    public TokenBean(String tokenId, String value) {
        this.tokenId = tokenId;
        this.value = value;
    }

    public TokenBean() {}

    public String getTokenId() {
        return tokenId;
    }

    public void setTokenId(String tokenId) {
        this.tokenId = tokenId;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public long getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(long expiresIn) {
        this.expiresIn = expiresIn;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }
}
