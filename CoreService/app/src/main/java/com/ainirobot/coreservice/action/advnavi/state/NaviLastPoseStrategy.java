package com.ainirobot.coreservice.action.advnavi.state;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.advnavi.bean.RoadLineBean;
import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.action.advnavi.bean.Vector2d;
import com.ainirobot.coreservice.action.advnavi.util.RoadLineUtils;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.MultiRobotStatus;
import com.ainirobot.coreservice.client.actionbean.Pose;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * Data: 2022/9/26 17:47
 * Author: wanglijing
 * 多机导航，去最后一个点
 */
public class NaviLastPoseStrategy extends BaseMultiRobotNaviState {

    private volatile MultipleStatus multipleStatus;
    private RoadLineUtils roadLineUtils;
    private List<RoadLineBean> standbyRangeRoads = new ArrayList<>();

    private enum MultipleStatus {
        PREPARE,
        START,
        NAVIGATION,
    }

    NaviLastPoseStrategy(String name) {
        super(name);
    }

    @Override
    protected void preAction(StateData data, IStateListener listener) {
        super.preAction(data, listener);
        addDestinationToStandbyList();
        roadLineUtils = new RoadLineUtils();
    }

    @Override
    protected void doAction() {
        super.doAction();
        updateMultiStatus(MultipleStatus.PREPARE);
    }

    @Override
    protected void handleMultipleRobotStatus(List<MultiRobotStatus> robotStatusList) {
        if (multipleStatus == MultipleStatus.PREPARE) {
            //这里是为了更新一下多机状态
            updateMultiStatus(MultipleStatus.START);
            toLastPose();   //导航去最后一个点
//            if (isInStandbyRoad()) {
//                findNearbyPose();   //判断点位状态
//            } else {
//                toLastPose();   //导航去最后一个点
//            }
        }
    }

    /**
     * 判断距那个点近
     */
    private void findNearbyPose() {
        Pose nearPose = findNearPose();
        if (nearPose != null) {
            Log.i(TAG, "near pose available " + nearPose);
            reportNearPose(nearPose);
        } else {
            Log.i(TAG, "near pose not available " + nearPose);
            toLastPose();
        }
    }

    /**
     * 上报到达 参数包含当前距那个点近
     */
    private void reportNearPose(Pose nearPose) {
        JSONObject jsonObject = new JSONObject();
        try {
            boolean isMainDes = TextUtils.equals(nearPose.getName(), mDestination);
            String destinationName = nearPose.getName();
            jsonObject.put("mainDestination", isMainDes);
            jsonObject.put("destination", destinationName);

            onResult(Definition.RESULT_NAVIGATION_ARRIVED, jsonObject.toString(), "");
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 判断当前是否在补位区
     *
     * @return true：在补位区巡线上
     */
    private synchronized boolean isInStandbyRoad() {
        //加载当前地图所有巡线
        roadLineUtils.loadRoadLineInfo(getRobotInfo(Definition.SYNC_ACTION_GET_MAP_NAME));

        if (roadLineUtils.getAllRoadLines().size() <= 0) {
            Log.e(TAG, "isInStandbyRoad current map no road");
            return false;
        }

        //补位区包含哪些巡线
        standbyRangeRoads = getStandbyRangeRoads();
        Log.i(TAG, "isInStandbyRoad standbyRangeRoads: " + standbyRangeRoads.size() + "  " + standbyRangeRoads);

        //当前点在哪个巡线上
        List<RoadLineBean> currentPoseRoads = roadLineUtils.
                findRoadContentPose(getCurrentPose(), standbyRangeRoads);
        Log.i(TAG, "isInStandbyRoad currentPoseRoads: " + currentPoseRoads);
        //若为空，没在巡线上
        return currentPoseRoads != null && currentPoseRoads.size() > 0;

    }

    /**
     * 找到补位区这几个点在哪些巡线上
     */
    private List<RoadLineBean> getStandbyRangeRoads() {
        List<RoadLineBean> standbyRoads = new ArrayList<>();
        //遍历每个点找巡线
        for (Pose pose : mValidStandbyList) {
            List<RoadLineBean> contentRoads = roadLineUtils.findRoadContentPose(pose);
            standbyRoads.addAll(contentRoads);
        }
        //去重
        return new ArrayList<>(new HashSet<>(standbyRoads));
    }

    /**
     * 找到最近的补位点
     */
    private Pose findNearPose() {
        Pose currentPose = getCurrentPose();
        if (currentPose == null) {
            return null;
        }

        List<Pose> sortList = new ArrayList<>();

        int size = mValidStandbyList.size();
        for (int i = 0; i < size - 1; i++) {
            for (int j = 0; j < size - i - 1; j++) {
                if (calculatePoseDistance(mValidStandbyList.get(j), currentPose)
                        > calculatePoseDistance(mValidStandbyList.get(j + 1), currentPose)) {

                    Pose tempPose = mValidStandbyList.get(j);
                    sortList.add(j, mValidStandbyList.get(j + 1));
                    sortList.add(j + 1, tempPose);
                }
            }
        }
        sortList = new ArrayList<>(new HashSet<>(sortList));
        Log.i(TAG, "findNearPose sortList: " + sortList);

        //找到最近两个点看能不能去
        if (sortList.size() >= 2) {
            sortList.subList(0, 2);
        }
        Log.i(TAG, "findNearPose sub list: " + sortList);

        //从前往后找，找到最近的点
        for (Pose pose : sortList) {
            if (poseAvailable(pose)) {
                return pose;
            }
        }
        return null;
    }

    /**
     * 巡线方向判断该点是否可用
     */
    private boolean poseAvailable(Pose nearPose) {
        List<RoadLineBean> roadLineBeans = roadLineUtils.findRoadContentPose(nearPose, standbyRangeRoads);
        if (roadLineBeans == null || roadLineBeans.size() <= 0) {
            Log.e(TAG, "poseAvailable road is null !!!");
            return false;
        }

        //判断最终目的地是否可达
        if (TextUtils.equals(nearPose.getName(), mDestination)) {
            Log.i(TAG, "poseAvailable near mDestination");
            DestinationState state = judgeDestinationIsAvailable(mRobotStatusList);
            //不可达也找不到最近的点了，回最后一个点去
            if (state != DestinationState.AVAILABLE
                    && state != DestinationState.NERA_DESTINATION) {
                return false;
            }
        }

        //TODO 根据巡线方向属性，及当前位置信息判断能否到达该点
//        RoadLineBean roadBean = roadLineBeans.get(0);
//        isReachedByRoadRule(roadBean, nearPose);
        return true;
    }

    /**
     * 根据巡线方向属性，及当前位置信息判断能否到达该点
     *
     * @param roadBean   巡线信息
     * @param targetPose 目标点
     */
    private boolean isReachedByRoadRule(RoadLineBean roadBean, Pose targetPose) {
        //未勾选单向行驶
        if (roadBean.getRetrograde_cost() != -1) {
            return true;
        }

        //获得巡线终点信息
        Pose currentPose = getCurrentPose();
        Log.d(TAG, "isReachedByRoadRule currentPose: " + currentPose);
        Vector2d startPosition = roadBean.getStartPose().getPosition();
        Pose startPose = new Pose(
                Float.parseFloat(String.valueOf(startPosition.getX())),
                Float.parseFloat(String.valueOf(startPosition.getX())),
                0);


        Vector2d endPosition = roadBean.getEndPose().getPosition();
        Pose endPose = new Pose(
                Float.parseFloat(String.valueOf(endPosition.getX())),
                Float.parseFloat(String.valueOf(endPosition.getX())),
                0);

        // |                |         |
        // *S       R1      *T   R2   *E
        // |                |         |
        //向前行驶
        if (roadBean.getRule() == 1) {
            boolean in = isInRect(currentPose, startPose, targetPose, roadBean.getLineWidth());
            Log.i(TAG, "isReachedByRoadRule to front : " + in);
            return in;
        }

        //向后
        if (roadBean.getRule() == 2) {
            boolean in = isInRect(currentPose, endPose, targetPose, roadBean.getLineWidth());
            Log.i(TAG, "isReachedByRoadRule to back : " + in);
            return in;

        }

        return false;
    }


    /**
     * 判断currentPose 是否在startPose和endPose组成的矩形区域内
     *
     * @param currentPose 当前位置
     * @param startPose   矩形开始边界
     * @param endPose     矩形结束边界
     * @param lineWidth   矩形宽度
     */
    private boolean isInRect(Pose currentPose, Pose startPose, Pose endPose, double lineWidth) {
        if (roadLineUtils != null) {
            RoadLineBean lineBean = new RoadLineBean();
            lineBean.setLineWidth(lineWidth);
            lineBean.buildStartPose(startPose);
            lineBean.buildEndPose(endPose);
            return roadLineUtils.isPoseInRoad(currentPose, lineBean);
        }
        return false;
    }

    private void toLastPose() {
        Pose lastPose = getLastPoseInQueue();
        if (lastPose == null) {
            //des为null指的是没有可用的目标点位，表明当前所有地点都有导航任务
            onResult(Definition.ERROR_NO_AVAILABLE_DESTINATION);
        } else {
            updateMultiStatus(MultipleStatus.NAVIGATION);
            //去优先级最低的点
            Log.i(TAG, "to last pose:" + lastPose);
            startGoStandbyDestination(lastPose.getName());
        }
    }

    private Pose getLastPoseInQueue() {
        //找到队尾
        if (mValidStandbyList != null && mValidStandbyList.size() > 0) {
            return mValidStandbyList.get(mValidStandbyList.size() - 1);
        }
        return null;
    }

    private void startGoStandbyDestination(String poseName) {
        startNavigation(poseName, isOneAvailableStandbyPoseCurrently());
    }

    private void updateMultiStatus(MultipleStatus status){
        Log.i(TAG, "updateMultiStatus: " + status);
        this.multipleStatus = status;
    }
}
