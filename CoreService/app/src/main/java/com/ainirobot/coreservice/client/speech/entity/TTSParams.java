package com.ainirobot.coreservice.client.speech.entity;

import android.support.annotation.Keep;
import android.support.annotation.StringDef;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

@StringDef({TTSParams.SPEECHVOLUME, TTSParams.SPEAKERROLE, TTSParams.SPEECHPIT,
        TTSParams.SPEECHRATE, TTSParams.SPEECHSPEED, TTSParams.SPEECHLANGUAGE})
public @interface TTSParams {
    String SPEECHVOLUME = "SpeechVolume";
    String SPEAKERROLE = "SpeakerRole";
    String SPEECHPIT = "SpeechPit";
    String SPEECHRATE = "SpeechRate";
    String SPEECHSPEED = "SpeechSpeed";
    String SPEECHLANGUAGE = "SpeechLanguage";

    @Retention(RetentionPolicy.CLASS)
    @Documented
    @Keep
    @interface SpeechVolume {
        int VOLUME_0 = 0;
        int VOLUME_1 = 1;
        int VOLUME_2 = 2;
        int VOLUME_3 = 3;
        int VOLUME_4 = 4;
        int VOLUME_5 = 5;
        int VOLUME_6 = 6;
        int VOLUME_7 = 7;
        int VOLUME_8 = 8;
        int VOLUME_9 = 9;
        int VOLUME_10 = 10;
        int VOLUME_11 = 11;
        int VOLUME_12 = 12;
        int VOLUME_13 = 13;
        int VOLUME_14 = 14;
        int VOLUME_15 = 15;
        int VOLUME_16 = 16;
        int VOLUME_17 = 17;
        int VOLUME_18 = 18;
        int VOLUME_19 = 19;
        int VOLUME_20 = 20;
        int VOLUME_21 = 21;
        int VOLUME_22 = 22;
        int VOLUME_23 = 23;
        int VOLUME_24 = 24;
        int VOLUME_25 = 25;
        int VOLUME_26 = 26;
        int VOLUME_27 = 27;
        int VOLUME_28 = 28;
        int VOLUME_29 = 29;
        int VOLUME_30 = 30;
    }

    @Retention(RetentionPolicy.CLASS)
    @Documented
    @Keep
    @Deprecated
    @interface SpeakerRole {
        //中文发音人
        // 小雅 女声
        int SPEAKER_XIAOYA = 0;
        // 采采 女声
        int SPEAKER_CAICAI = 1;
        // 莫林 男声
        int SPEAKER_MOLIN = 10;
        // 岳云鹏 男声
//        int SPEAKER_YUEYUNPENG = 11;
        // 童声
        int SPEAKER_CHILD = 20;

        //英文发音人
        // 默认的小雅的人设对应的英文发言人
        int SPEAKER_XIAOYA_ENG = 30;
        // native 女声
        int SPEAKER_NATIVE_ENG = 31;
        // 男声
        int SPEAKER_MAN_ENG = 40;
        // 童声
        int SPEAKER_CHILD_ENG = 50;

    }

    @Retention(RetentionPolicy.CLASS)
    @Documented
    @Keep
    @interface SpeechPit {
        int PIT_0 = 0;
        int PIT_1 = 1;
        int PIT_2 = 2;
        int PIT_3 = 3;
        int PIT_4 = 4;
        int PIT_5 = 5;
        int PIT_6 = 6;
        int PIT_7 = 7;
        int PIT_8 = 8;
        int PIT_9 = 9;
    }

    @Retention(RetentionPolicy.CLASS)
    @Documented
    @Keep
    @interface SpeechRate {
        int RATE_8K = 0;
        int RATE_16K = 1;
        int RATE_18K = 2;
        int RATE_20K = 3;
        int RATE_24K = 4;
        int RATE_32K = 5;
    }

    @Retention(RetentionPolicy.CLASS)
    @Documented
    @Keep
    @interface SpeechSpeed {
        int SPEED_0 = 0;
        int SPEED_1 = 1;
        int SPEED_2 = 2;
        int SPEED_3 = 3;
        int SPEED_4 = 4;
        int SPEED_5 = 5;
        int SPEED_6 = 6;
        int SPEED_7 = 7;
        int SPEED_8 = 8;
        int SPEED_9 = 9;
    }
}
