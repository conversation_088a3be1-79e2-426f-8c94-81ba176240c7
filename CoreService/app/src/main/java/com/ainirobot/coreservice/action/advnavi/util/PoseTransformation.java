package com.ainirobot.coreservice.action.advnavi.util;


import com.ainirobot.coreservice.client.actionbean.Pose;

/**
 * 通过相对位姿计算最终位姿
 */
public class PoseTransformation {

    /**
     * 计算相对位姿
     *
     * @param pose1 参考点位姿
     * @param pose2 相对点位姿
     * @return 相对位姿，相对点位姿在参考点位姿坐标系下的表示
     */
    public static Pose relativePose(Pose pose1, Pose pose2) {
        // 计算相对位置
        double dx = pose2.getX() - pose1.getX();
        double dy = pose2.getY() - pose1.getY();
        double relativeX = dx * Math.cos(pose1.getTheta()) + dy * Math.sin(pose1.getTheta());
        double relativeY = -dx * Math.sin(pose1.getTheta()) + dy * Math.cos(pose1.getTheta());

        // 计算相对方向
        double relativeTheta = pose2.getTheta() - pose1.getTheta();

        Pose relative = new Pose((float) relativeX, (float) relativeY, (float) relativeTheta);
        return relative;
    }

    /**
     * 计算最终位姿
     *
     * @param original 参考点位姿
     * @param relative 相对位姿，相对点位姿在参考点位姿坐标系下的表示
     * @return 相对点位姿
     */
    public static Pose finalPose(Pose original, Pose relative) {

        // 计算最终位置
        double finalPoseX = original.getX() + relative.getX() * Math.cos(original.getTheta()) - relative.getY() * Math.sin(original.getTheta());
        double finalPoseY = original.getY() + relative.getX() * Math.sin(original.getTheta()) + relative.getY() * Math.cos(original.getTheta());

        // 计算最终方向
        double finalPoseTheta = original.getTheta() + relative.getTheta();

        Pose finalPose = new Pose((float) finalPoseX, (float) finalPoseY, (float) finalPoseTheta);
        return finalPose;
    }

    public static void test() {
        // 示例数据
        Pose pose1 = new Pose(1.0f, 2.0f, (float) Math.toRadians(30.0));
        Pose pose2 = new Pose(3.0f, 4.0f, (float) Math.toRadians(60.0));

        Pose relativePose = relativePose(pose1, pose2);
        System.out.println("PoseTransformation：Relative Pose:" + relativePose + " angle=" + Math.toDegrees(relativePose.getTheta()));

        Pose finalPose = finalPose(pose1, relativePose);
        System.out.println("PoseTransformation：Final Pose:" + finalPose + " angle=" + Math.toDegrees(finalPose.getTheta()));

        Pose pose3 = new Pose(1.2f, 2.2f, (float) Math.toRadians(32.0));
        Pose finalPose2 = finalPose(pose3, relativePose);
        System.out.println("PoseTransformation：Final Pose2:" + finalPose2 + " angle=" + Math.toDegrees(finalPose2.getTheta()));


        Pose relativePose2 = relativePose(pose3, finalPose2);
        System.out.println("PoseTransformation：Relative Pose2:" + relativePose2 + " angle=" + Math.toDegrees(relativePose2.getTheta()));
        Pose finalPose3 = finalPose(pose1, relativePose2);
        System.out.println("PoseTransformation：Final Pose3:" + finalPose3 + " angle=" + Math.toDegrees(finalPose3.getTheta()));
    }

}
