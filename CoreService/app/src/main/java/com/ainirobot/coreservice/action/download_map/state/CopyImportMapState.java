package com.ainirobot.coreservice.action.download_map.state;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.download_map.DownloadMapStateEnum;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.DownloadMapBean;

public class CopyImportMapState extends BaseDownloadMapPkgState {
    private DownloadMapBean downloadMapBean;

    @Override
    protected void onStart(String paramsStr, DownloadMapBean downloadMapBean) {
        Log.d(TAG, "CopyImportMapState onStart: ");
        this.downloadMapBean = downloadMapBean;
        sendCommand(0, Definition.CMD_NAVI_COPY_IMPORT_MAP_FILE, downloadMapBean.getMapName());
    }

    @Override
    public boolean onCmdResponse(int cmdId, String command, String result, String extraData) {
        if (TextUtils.equals(command, Definition.CMD_NAVI_COPY_IMPORT_MAP_FILE)) {
            if (TextUtils.equals(Definition.SUCCEED, result)) {
                onStateRunSuccess();
            } else {
                onStateRunFailed(0, "copy import map failed");
            }
            return true;
        }
        return false;
    }

    @Override
    protected DownloadMapStateEnum getNextState() {
        if (downloadMapBean.isOldMap() && !downloadMapBean.isOverwriteLocalPlace()) {
            return DownloadMapStateEnum.PARSE_ROAD_DATA;
        }
        return DownloadMapStateEnum.PARSE_PLACE_LIST;
    }
}
