package com.ainirobot.coreservice.bean;

public class ElevatorState {
    String state;
    int floor;
    long updateTime;

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public int getFloor() {
        return floor;
    }

    public void setFloor(int floor) {
        this.floor = floor;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ElevatorState{" +
                "state='" + state + '\'' +
                ", floor=" + floor +
                ", updateTime=" + updateTime +
                '}';
    }
}
