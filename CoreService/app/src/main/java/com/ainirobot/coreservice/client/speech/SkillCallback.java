/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.client.speech;

import android.os.RemoteException;

import com.ainirobot.coreservice.ISkillCallback;

public abstract class SkillCallback extends ISkillCallback.Stub {

    @Override
    public abstract void onSpeechParResult(String text) throws RemoteException;

    @Override
    public abstract void onStart() throws RemoteException;

    @Override
    public abstract void onStop() throws RemoteException;

    @Override
    public abstract void onVolumeChange(int volume) throws RemoteException;

    @Override
    public abstract void onQueryEnded(int queryEndStatus) throws RemoteException;

    @Override
    public String onGetMultipleModeInfos(int index) throws RemoteException {
        return null;
    }

    @Override
    public void onQueryAsrResult(String asrResult) throws RemoteException {
    }

    @Override
    public void onError(String sid, int code, String message) throws RemoteException {
    }

    @Override
    public void onVadMuteTime(int vadMuteTime) throws RemoteException {
    }

    @Override
    public void onSpeechStreamData(String data) throws RemoteException {

    }
}
