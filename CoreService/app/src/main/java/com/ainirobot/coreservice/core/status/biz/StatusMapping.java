package com.ainirobot.coreservice.core.status.biz;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotOS;

import java.util.HashMap;

public class StatusMapping {

    private HashMap<String, String> mStatusMapping;
    private HashMap<String, String> mStatusParamsMapping;

    private static class StatusMappingInner {
        static StatusMapping INSTANCE = new StatusMapping();
    }

    public static StatusMapping getInstance() {
        return StatusMappingInner.INSTANCE;
    }

    private StatusMapping() {
        mStatusMapping = new HashMap<>();
        mStatusParamsMapping = new HashMap<>();
        initConfigMappings();
    }

    private void initConfigMappings() {
        add(Definition.CMD_NAVI_GET_FULL_CHECK_STATUS, RobotOS.NAVIGATION_SERVICE);
        add(Definition.CMD_NAVI_GET_SENSOR_STATUS, RobotOS.NAVIGATION_SERVICE);
        add(Definition.CMD_NAVI_QUERY_RADAR_STATUS, RobotOS.NAVIGATION_SERVICE);
        add(Definition.CMD_NAVI_CONNECT_STATUS, RobotOS.NAVIGATION_SERVICE);

        add(Definition.CMD_REPORT_NAVIGATION_STATUS, RobotOS.VISION_SERVICE);
        add(Definition.CMD_HEAD_GET_STATUS, RobotOS.VISION_SERVICE);
        add(Definition.CMD_HEAD_GET_CAMERA_STATUS, RobotOS.VISION_SERVICE);
        add(Definition.CMD_HEAD_GET_DEPTH_STATUS, RobotOS.VISION_SERVICE, StatusParamsMapping.getCameraParams());
        add(Definition.CMD_HEAD_GET_FOV_STATUS, RobotOS.VISION_SERVICE, StatusParamsMapping.getCameraParams());
        add(Definition.CMD_HEAD_IS_HEADER_CONNECTED, RobotOS.VISION_SERVICE);

        add(Definition.CMD_CAN_GET_STATUS, RobotOS.SYSTEM_SERVICE);
        add(Definition.CMD_CAN_GET_CHARGE_STATUS, RobotOS.SYSTEM_SERVICE);
        add(Definition.CMD_CAN_GET_EMERGENCY_STATUS, RobotOS.OTA_SERVICE);

        add(Definition.CMD_REMOTE_POST_CRUISE_STATUS, RobotOS.REMOTE_SERVICE);
        add(Definition.CMD_REMOTE_CHECK_BIND_STATUS, RobotOS.REMOTE_SERVICE);
        add(Definition.STATUS_ELEVATOR, RobotOS.ELEVATOR_SERVICE);
        add(Definition.STATUS_GATE, RobotOS.PASS_GATE_SERVICE);
    }

    private void add(String statusType, String serviceName) {
        if (TextUtils.isEmpty(statusType) || TextUtils.isEmpty(serviceName)) {
            Log.w(StatusProvider.TAG, String.format("statusType : %s, className : %s", statusType, serviceName));
            return;
        }
        if (TextUtils.isEmpty(statusType) || mStatusMapping.containsKey(statusType)) {
            Log.w(StatusProvider.TAG, String.format("mActionMappings already contain %s", statusType));
            return;
        }

        mStatusMapping.put(statusType, serviceName);
    }

    private void add(String statusType, String serviceName, String params) {
        if (!TextUtils.isEmpty(params)) {
            addParams(statusType, params);
        }
        add(statusType, serviceName);
    }

    private void addParams(String statusType, String params) {
        if (TextUtils.isEmpty(statusType) || TextUtils.isEmpty(params)) {
            Log.w(StatusProvider.TAG, String.format("statusType : %s, params : %s", statusType, params));
            return;
        }
        if (TextUtils.isEmpty(statusType) || mStatusParamsMapping.containsKey(statusType)) {
            Log.w(StatusProvider.TAG, String.format("mActionMappings already contain %s", statusType));
            return;
        }

        mStatusParamsMapping.put(statusType, params);
    }

    public HashMap getStatusMapping() {
        return mStatusMapping;
    }

    public String getParams(String statusType){
        if(mStatusParamsMapping.size() > 0){
            return mStatusParamsMapping.get(statusType);
        }
        return null;
    }

}
