/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.client.speech;

import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.base.OrionBase;
import com.ainirobot.coreservice.ISkill;
import com.ainirobot.coreservice.client.ApiListener;
import com.ainirobot.coreservice.client.BaseApi;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.IntentUtil;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.coreservice.client.listener.ToneListener;
import com.ainirobot.coreservice.client.messagedispatcher.TextDispatcher;
import com.ainirobot.coreservice.client.messagedispatcher.ToneDispatcher;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.client.speech.entity.ASRParams;
import com.ainirobot.coreservice.client.speech.entity.LangJson;
import com.ainirobot.coreservice.client.speech.entity.TTSEntity;
import com.ainirobot.coreservice.client.speech.entity.TTSParams;
import com.ainirobot.coreservice.config.SceneEntity;
import com.ainirobot.coreservice.listener.IMusicListener;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class SkillApi extends BaseApi {

    private static final String TAG = SkillApi.class.getSimpleName();

    private ISkill mSkill;
    private Gson mGson = new GsonBuilder()
            .disableHtmlEscaping()
            .create();

    public SkillApi() {

    }

    /**
     * Connect API
     */
    public void connectApi(Context context, ApiListener listener) {
        this.ctx = context;
        String zone = RobotSettingApi.getInstance()
                                     .getRobotString(Definition.ROBOT_SETTINGS_CLOUD_SERVER_ZONE);
        OrionBase.setDomainEnv(ProductInfo.isOverSea(), zone, RobotSettingApi.getInstance()
                                                                             .getRobotInt(Definition.ROBOT_SETTING_SYSTEM_ENV) == 1);
        OrionBase.start(ctx, null);
        addApiEventListener(listener);

        String systemOSType = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_SETTINGS_SYSTEM_OS_TYPE);
        String globalSystemOSType = RobotSettings.getGlobalSettings(context,
                Definition.ROBOT_SETTINGS_SYSTEM_OS_TYPE, "");
        Log.d(TAG, "connectApi systemOSType=" + systemOSType + " globalSystemOSType=" + globalSystemOSType);

        Intent intent;
        if (TextUtils.equals(systemOSType, Definition.OSType.AGENTOS.getValue()) ||
                TextUtils.equals(globalSystemOSType, Definition.OSType.AGENTOS.getValue())) {
            intent = IntentUtil.createExplicitIntent(Definition.AGENT_PACKAGE_NAME,
                    Definition.AGENT_SERVICE_NAME,
                    context.getPackageName());
        } else {
            intent = IntentUtil.createExplicitIntent(Definition.SPEECH_PACKAGE_NAME,
                    Definition.SPEECH_SERVICE_NAME,
                    context.getPackageName());
        }
        context.bindService(intent, apiConnection, Service.BIND_AUTO_CREATE);
    }

    /**
     * Connect API
     */
    public void connectApi(Context context) {
        this.ctx = context;
        String zone = RobotSettingApi.getInstance()
                                     .getRobotString(Definition.ROBOT_SETTINGS_CLOUD_SERVER_ZONE);
        Log.d(TAG, "SkillApi connnectApi zone: " + zone);
        OrionBase.setDomainEnv(ProductInfo.isOverSea(), zone, RobotSettingApi.getInstance()
                                                                             .getRobotInt(Definition.ROBOT_SETTING_SYSTEM_ENV) == 1);
        OrionBase.start(ctx, null);

        String systemOSType = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_SETTINGS_SYSTEM_OS_TYPE);
        String globalSystemOSType = RobotSettings.getGlobalSettings(context,
                Definition.ROBOT_SETTINGS_SYSTEM_OS_TYPE, "");
        Log.d(TAG, "connectApi systemOSType=" + systemOSType + " globalSystemOSType=" + globalSystemOSType);

        Intent intent;
        if (TextUtils.equals(systemOSType, Definition.OSType.AGENTOS.getValue()) ||
                TextUtils.equals(globalSystemOSType, Definition.OSType.AGENTOS.getValue())) {
            intent = IntentUtil.createExplicitIntent(Definition.AGENT_PACKAGE_NAME,
                    Definition.AGENT_SERVICE_NAME,
                    context.getPackageName());
        } else {
            intent = IntentUtil.createExplicitIntent(Definition.SPEECH_PACKAGE_NAME,
                    Definition.SPEECH_SERVICE_NAME,
                    context.getPackageName());
        }

        context.bindService(intent, apiConnection, Service.BIND_AUTO_CREATE);
    }

    /**
     * Disconnect API
     */
    @Override
    public void disconnectApi() {
        try {
            removeAllApiEventListeners();
            if (null != ctx) {
                ctx.unbindService(apiConnection);
            }
        } catch (IllegalArgumentException e) {
            // Nothing to do
        }
    }

    /**
     * API connection
     */
    protected ServiceConnection apiConnection = new ServiceConnection() {
        public void onServiceConnected(ComponentName className, IBinder service) {
            mSkill = ISkill.Stub.asInterface(service);
            mIsServiceConnected = true;
            notifyEventApiConnected();
        }

        public void onServiceDisconnected(ComponentName className) {
            mIsServiceConnected = false;
            notifyEventApiDisconnected();
        }
    };


    public void registerCallBack(SkillCallback cb) {
        try {
            mSkill.registerCallBack(cb);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public boolean unregisterCallBack(SkillCallback cb) {
        try {
            mSkill.unregisterCallBack(cb);
            return true;
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * TTS合成
     *
     * @param text     合成文本
     * @param listener 合成回调
     */
    @Deprecated
    public void playText(String text, TextListener listener) {
        try {
            TextDispatcher textDispatcher = null;
            if (listener != null) {
                textDispatcher = mMessageDispatcher.obtainTextDispatcher(listener);
            }
            if (isInvalidTtsText(text)) {
                if (null != textDispatcher) {
                    textDispatcher.onError();
                }
                Log.e(TAG, "playText: " + text + " is invalid tts text.");
                return;
            }
            mSkill.playText(text, textDispatcher);
            OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "playText", "");
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * TTS合成
     *
     * @param ttsEntity {@link TTSEntity}
     * @param listener  合成回调
     */
    public void playText(TTSEntity ttsEntity, TextListener listener) {
        try {
            TextDispatcher textDispatcher = null;
            if (listener != null) {
                textDispatcher = mMessageDispatcher.obtainTextDispatcher(listener);
            }
            if (null != ttsEntity) {
                if (!isValidUUID(ttsEntity.getTextSid())) {
                    String sid = UUID.randomUUID().toString();
                    ttsEntity.setTextSid(sid);
                }
                if (isInvalidTtsText(ttsEntity.getText())) {
                    if (null != textDispatcher) {
                        textDispatcher.onError();
                    }
                    Log.e(TAG, "playText: " + ttsEntity.getText() + " is invalid tts text.");
                    return;
                }
                mSkill.playText(mGson.toJson(ttsEntity), textDispatcher);
            }
            Map<String, Object> param = new HashMap<>(1);
            param.put("ttsEntity", true);
            OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "playText", getParamJsonObjectString(param));
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    //校验播放内容是非法的
    private boolean isInvalidTtsText(String text) {
        //非法Json格式，后续可以判断文本长度等
        return textIsJson(text);
    }

    private boolean textIsJson(String json) {
        try {
            JsonElement jsonElement = new JsonParser().parse(json);
            return jsonElement.isJsonObject() || jsonElement.isJsonArray();
        } catch (Exception e) {
            return false;
        }
    }


    /**
     * 预下载mp3文件
     *
     * @param ttsEntitiesJson {@link TTSEntity}
     */
    public void downloadTtsAudio(String ttsEntitiesJson) {
        try {
            mSkill.downloadTtsAudio(ttsEntitiesJson);
            Map<String, Object> param = new HashMap<>(1);
            param.put("downloadTtsAudio", ttsEntitiesJson);
            OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "downloadTtsAudio", getParamJsonObjectString(param));
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * @return 获取当前语言下的发音人列表
     */
    public String getSpokemanListByLanguage(String lang) {
        try {
            Map<String, Object> param = new HashMap<>(1);
            param.put("lang", lang);
            OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "getSpokemanListByLanguage",
                    getParamJsonObjectString(param));
            return mSkill.getSpokemanListByLanguage(lang);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 语音唤醒应答开关
     *
     * @param isWakeupHintClosed true 关闭应答 false 开启应答
     */
    public void setWakeupHintClosed(boolean isWakeupHintClosed) {
        try {
            mSkill.setWakeupHintClosed(isWakeupHintClosed);
            OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "setWakeupHintClosed", "");
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }


    /**
     * 设置TTS速度
     *
     * @param value {@link TTSParams.SpeechSpeed}
     */
    public void setTTSSpeechSpeed(int value) {
        try {
            mSkill.setTTSParams(TTSParams.SPEECHSPEED, value);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 设置TTS相关参数
     *
     * @param ttsType tts参数类型{@link TTSParams}
     * @param value   {@link TTSParams.SpeechVolume, TTSParams.SpeakerRole, TTSParams.SpeechPit, TTSParams.SpeechRate, TTSParams.SpeechSpeed}
     */
    public void setTTSParams(@TTSParams String ttsType, int value) {
        try {
            mSkill.setTTSParams(ttsType, value);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 短音频播报
     *
     * @param type
     * @param listener 音频播报回调
     * @deprecated Use {@link #playToneByLocalPath(String, ToneListener)} instead.
     */
    @Deprecated
    public void playTone(String type, ToneListener listener) {
        try {
            ToneDispatcher toneDispatcher = null;
            if (listener != null) {
                toneDispatcher = mMessageDispatcher.obtainToneDispatcher(listener);
            }
            mSkill.playTone(type, toneDispatcher);
            OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "playTone", "");
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 短音频播报
     *
     * @param localPath 本地音频路径
     * @param listener  音频播报回调
     */
    public void playToneByLocalPath(String localPath, ToneListener listener) {
        try {
            ToneDispatcher toneDispatcher = null;
            if (listener != null) {
                toneDispatcher = mMessageDispatcher.obtainToneDispatcher(listener);
            }
            mSkill.playToneByLocalPath(localPath, toneDispatcher);
            OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "playToneWithLocalPath", "");
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 本地音乐播报
     *
     * @param localPath        本地音频路径
     * @param looping          是否循环播放
     * @param enableAudioFocus 是否启用音频焦点
     * @param listener         音频播报回调
     */
    public void playMusicByLocalPath(String localPath, boolean looping, boolean enableAudioFocus,
                                     IMusicListener listener) {
        try {
            mSkill.playMusicByLocalPath(localPath, looping, enableAudioFocus, listener);
            Map<String, Object> param = new HashMap<>(1);
            param.put("localPath", localPath);
            param.put("looping", looping);
            param.put("enableAudioFocus", enableAudioFocus);
            OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "playMusicByLocalPath",
                    getParamJsonObjectString(param));
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 停止播放的音乐
     */
    public void stopMusicPlay() {
        try {
            mSkill.stopMusicPlay();
            OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "stopMusicPlay", "");
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 强制停止播报TTS。
     * 如果当前有TTS正在播报，并且设置了相应的TextListener，会触发{@link TextListener#onStop()}
     */
    public void stopTTS() {
        try {
            mSkill.stopTTS();
            OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "stopTTS", "");
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void switchScene(SceneEntity scene) {
        try {
            mSkill.switchScene(scene);
            OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "switchScene", scene.toString());
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 强制停止播报Tone。
     * 如果当前有Tone正在播报，并且设置了相应的ToneListener，会触发{@link ToneListener#onStop()}
     */
    public void stopTone() {
        try {
            mSkill.stopTone();
            OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "stopTone", "");
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 取消弱网等流程的定时器，短音频播报，停止当前TTS播报
     * 如果设置开启弱网，other播报，在activity销毁时应该调用该方法
     */
    public void cancleAudioOperation() {
        try {
            mSkill.cancleAudioOperation();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }


    /**
     * 设置语音识别（asr）模式
     *
     * @param isContinue true 持续识别 false 单次识别
     */
    public void setRecognizeMode(boolean isContinue) {
        try {
            mSkill.setRecognizeMode(isContinue);

            Map<String, Object> param = new HashMap<>(1);
            param.put("isContinue", isContinue);
            OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "setRecognizeMode", getParamJsonObjectString(param));
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 强制设置语音识别（asr）模式，忽略settings开关
     *
     * @param isContinue true 持续识别 false 单次识别
     */
    public void setRecognizeModeForce(boolean isContinue) {
        try {
            mSkill.setRecognizeModeForce(isContinue);

            Map<String, Object> param = new HashMap<>(1);
            param.put("isContinue", isContinue);
            OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "setRecognizeModeForce", getParamJsonObjectString(param));
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 强制设置语音识别（asr）模式，忽略settings开关
     *
     * @param isContinue        true 持续识别 false 单次识别
     * @param isCloseStreamData true 切到单拾音关闭流数据 false 切到单拾音不关闭流数据
     */
    public void setRecognizeModeNew(boolean isContinue, boolean isCloseStreamData) {
        try {
            mSkill.setRecognizeModeNew(isContinue, isCloseStreamData);

            Map<String, Object> param = new HashMap<>(1);
            param.put("isContinue", isContinue);
            param.put("isCloseStreamData", isCloseStreamData);
            OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "setRecognizeModeNew", getParamJsonObjectString(param));
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 该API将要被废弃，如果调用者需要创建Audio，直接创建即可，支持多Audio同时工作
     * 如果调用者需要不接收唤醒词和识别结果等结果请调用
     * {@link #setRecognizable(boolean enable)}
     * <p>
     * 设置语音识别(asr)，语音合成（TTS）服务是否开启
     *
     * @param enable true 开启 false 关闭
     */
    @Deprecated
    public void setASREnabled(boolean enable) {
        try {
            mSkill.setASREnabled(enable);

            Map<String, Object> param = new HashMap<>(1);
            param.put("enable", enable);
            OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "setASREnabled", getParamJsonObjectString(param));
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 设置语音识别(asr)是否开启
     *
     * @param enable true 开启 false 关闭
     */
    public void setRecognizable(boolean enable) {
        try {
            mSkill.setRecognizable(enable);
            Map<String, Object> param = new HashMap<>(1);
            param.put("enable", enable);
            OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "setRecognizable", getParamJsonObjectString(param));
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 通过文本获取NLP最终结果
     *
     * @param text query的文本
     */
    public void queryByText(String text) {
        OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "queryByText", "");
        try {
            mSkill.queryByText(text);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void queryByTextWithThinking(String text, boolean isShowThinking) {
        OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "queryByTextWithThinking", "");
        try {
            mSkill.queryByTextWithThinking(text, isShowThinking);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }


    /**
     * 通过传递属性信息获取主动问答
     *
     * @param properType      属性类型 "active_ask"
     * @param robotProperJson 属性json "{face_info:{}}"
     */
    public void getActiveAsk(String properType, String robotProperJson) {
        try {
            mSkill.getActiveAsk(properType, robotProperJson);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 往asr链路首包传递属性信息
     *
     * @param propertyJson 属性信息json
     * @return 设置成功
     */
    public boolean setAsrExtendProperty(String propertyJson) {
        try {
            return mSkill.setAsrExtendProperty(propertyJson);
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 设置识别区域
     *
     * @param angle_center 中心角度 [0,360)
     * @param angle_range  区间角度 [0,120]
     */
    public void setAngleCenterRange(float angle_center, float angle_range) {
        try {
            mSkill.setAngleCenterRange(angle_center, angle_range);

            Map<String, Object> param = new HashMap<>(2);
            param.put("angle_center", angle_center);
            param.put("angle_range", angle_range);
            OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "setAngleCenterRange", getParamJsonObjectString(param));
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 设置多模态是否开启
     *
     * @param enale true 开启 false 关闭
     */
    public void setMultipleModeEnable(boolean enale) {
        Map<String, Object> param = new HashMap<>(1);
        param.put("enale", enale);
        OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "setMultipleModeEnable", getParamJsonObjectString(param));
        try {
            mSkill.setMultipleModeEnable(enale);
        } catch (RemoteException e) {
            Log.e(TAG, Log.getStackTraceString(e));
        }
    }

    /**
     * 设置唤醒词
     *
     * @param wakeUpWordChinese 唤醒词 对应的汉字
     * @param wakeUpWordPinYin  唤醒词 对应的拼音
     * @param separator         唤醒词之间的分隔符
     * @return int 0:成功  非0:失败
     */
    public int setCustomizeWakeUpWord(String wakeUpWordChinese, String wakeUpWordPinYin, String separator) {
        OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "setCustomizeWakeUpWord", "");
        try {
            return mSkill.setCustomizeWakeUpWord(wakeUpWordChinese, wakeUpWordPinYin, separator);
        } catch (RemoteException e) {
            Log.e(TAG, Log.getStackTraceString(e));
            return -1;
        }
    }

    /**
     * 关闭唤醒词
     *
     * @return int 0:成功  非0:失败
     */
    public int closeCustomizeWakeUpWord() {
        OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "closeCustomizeWakeUpWord", "");
        try {
            return mSkill.closeCustomizeWakeUpWord();
        } catch (RemoteException e) {
            Log.e(TAG, Log.getStackTraceString(e));
            return -1;
        }
    }

    /**
     * 获取拼音的唤醒率得分
     *
     * @return 拼音 2.5及以上得分 合格  -1:获取异常
     */
    public int getPinYinScore(String pinyin, String separator) {
        OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "getPinYinScore", "");
        try {
            return mSkill.getPinYinScore(pinyin, separator);
        } catch (RemoteException e) {
            Log.e(TAG, Log.getStackTraceString(e));
            return -1;
        }
    }

    /**
     * 通过汉字 查询 Spell
     *
     * @param chineseWord 汉字
     * @return 对应的拼音
     */
    public String queryPinYinFromChinese(String chineseWord) {
        OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "queryPinYinFromChinese", "");
        try {
            return mSkill.queryPinYinFromChinese(chineseWord);
        } catch (RemoteException e) {
            Log.e(TAG, Log.getStackTraceString(e));
            return null;
        }
    }

    /**
     * 查询拼音对应的映射
     *
     * @return 拼音
     */
    public String queryPinYinMappingTable(String pinyin) {
        OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "queryPinYinMappingTable", "");
        try {
            return mSkill.queryPinYinMappingTable(pinyin);
        } catch (RemoteException e) {
            Log.e(TAG, Log.getStackTraceString(e));
            return null;
        }
    }

    /**
     * 查询用户设置的唤醒词
     *
     * @return 唤醒词
     */
    public String queryUserSetWakeUpWord() {

        OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "queryUserSetWakeUpWord", "");

        try {
            return mSkill.queryUserSetWakeUpWord();
        } catch (RemoteException e) {
            Log.e(TAG, Log.getStackTraceString(e));
            return null;
        }
    }

    /**
     * 设置语种识别
     *
     * @param langJson 语种识别参数实体类,langs如果为空，取默认值
     *                 自动识别
     *                 <p>
     *                 {
     *                 "isAuto": true,
     *                 "langs": [1,2]
     *                 }
     *                 <p>
     *                 非自动识别
     *                 <p>
     *                 {
     *                 "isAuto": false,
     *                 "langs": [1]
     *                 }
     */
    public void setLangRec(LangJson langJson) {
        try {
            if (null != langJson)
                mSkill.setLangRec(new Gson().toJson(langJson));
        } catch (RemoteException e) {
            Log.e(TAG, Log.getStackTraceString(e));
        }
    }

    /**
     * 设置asr参数
     *
     * @param asrType 参数类型
     * @param value   参数值
     */
    public void setASRParams(@ASRParams String asrType, String value) {
        try {
            mSkill.setASRParams(asrType, value);
        } catch (RemoteException e) {
            Log.e(TAG, Log.getStackTraceString(e));
        }
    }

    private static String getParamJsonObjectString(Map<String, Object> map) {
        try {
            JSONObject param = new JSONObject();
            if (map != null) {
                for (Map.Entry<String, Object> entry : map.entrySet()) {
                    param.put(entry.getKey(), entry.getValue());
                }
            }
            return param.toString();
        } catch (Exception e) {
            return "";
        }
    }

    public void registerServerCheck(SkillServerCheckListener listener) {
        try {
            mSkill.registerServerCheck(listener);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void unregisterServerCheck() {
        try {
            mSkill.unregisterServerCheck();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void onCreate(String app_id) {
        try {
            mSkill.onCreate(app_id);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void onForeground(String app_id) {
        try {
            mSkill.onForeground(app_id);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void onBackground(String app_id) {
        try {
            mSkill.onBackground(app_id);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void onDestroy(String app_id) {
        try {
            mSkill.onDestroy(app_id);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void setVersion(String app_id, String app_version) {
        try {
            mSkill.setVersion(app_id, app_version);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void setPath(String app_id, String path) {
        try {
            mSkill.setPath(app_id, path);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void sendAgentMessage(String type,int code,String message){
        try {
            mSkill.sendAgentMessage(type, code, message);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void setSyncReportCustomNlpData(String app_id, String data) {
        try {
            mSkill.setSyncReportCustomNlpData(app_id, data);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void setAsyncReportCustomNlpData(String app_id, String data) {
        try {
            mSkill.setAsyncReportCustomNlpData(app_id, data);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void resetNlpState() {
        try {
            mSkill.resetNlpState();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void setServerApp(List<String> appList) {
        try {
            mSkill.setServerApp(appList);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void setDebug(boolean value) {
        try {
            mSkill.setDebug(value);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void setSyncCustomNlpData(Map<String, Object> map) {
        try {
            mSkill.setSyncCustomNlpData(map);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public String setAsyncCustomNlpData(String opt, String data) {
        try {
            return mSkill.setAsyncCustomNlpData(opt, data);
        } catch (RemoteException e) {
            e.printStackTrace();
            return "error";
        }
    }

    public int getTtsPlayStatus() {
        OrionBase.logApi(OrionBase.API_TYPE_AUDIO, "getTtsPlayStatus", "");
        try {
            return mSkill.getTtsPlayStatus();
        } catch (RemoteException e) {
            Log.e(TAG, Log.getStackTraceString(e));
            return -1;
        }
    }

    /**
     * UI层关闭流式数据接收的状态同步.
     *
     * @param statusJson
     */
    public void closeStreamDataReceived(String statusJson) {
        try {
            mSkill.closeStreamDataReceived(statusJson);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public boolean isRecognizeContinue() {
        try {
            return mSkill.isRecognizeContinue();
        } catch (RemoteException e) {
            e.printStackTrace();
            return true;
        }
    }

    public boolean isRecognizable() {
        try {
            return mSkill.isRecognizable();
        } catch (RemoteException e) {
            e.printStackTrace();
            return true;
        }
    }

    public boolean isValidUUID(String uuidStr) {
        if (uuidStr == null || uuidStr.length() == 0) {
            return false;
        }
        try {
            UUID.fromString(uuidStr);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public boolean onAgentActionFinish(String action, int code, String message) {
        try {
            mSkill.onAgentActionFinish(action, code, message);
            return true;
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }

    public boolean onAgentActionState(String action, int state, String message) {
        try {
            mSkill.onAgentActionState(action, state, message);
            return true;
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }
}
