/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.core.system;

import static com.ainirobot.coreservice.core.InternalDef.START_COMMAND_STOP_DOWNGRADE_SERVICE;

import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.core.InternalDef;

import java.util.Calendar;

public class OTAManager {
    private final String TAG = "OTAManager";

    private OTAStartMode mStartMode = OTAStartMode.BOOT;

    private Context mContext;

    public enum OTAStatus {
        NONE,
        WAITING_AUTH,
        AUTH,
        UPGRADING,
        SHOW_RESULT,
        INSPECTION
    }

    public enum OTAStartMode {
        /**
         * Nighttime OTA
         */
        NIGHTTIME(Definition.OTA_START_NIGHTTIME),

        /**
         * Boot auto OTA
         */
        BOOT(Definition.OTA_START_BOOT),
        SETTING(Definition.OTA_START_SETTING),
        REMOTE(Definition.OTA_START_REMOTE);

        String value;

        OTAStartMode(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public OTAManager(Context context) {
        mContext = context;
        registerReceiver();
    }

    private void registerReceiver() {
        IntentFilter intentFilter = new IntentFilter(InternalDef.ACTION_TIMING_TASK);
        TimerReceiver receiver = new TimerReceiver();
        mContext.registerReceiver(receiver, intentFilter);
    }

    private class TimerReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            Calendar calendar = Calendar.getInstance();
            Log.d(TAG, "Alarm: " + calendar.get(Calendar.HOUR_OF_DAY));
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            if (1 <= hour && 6 >= hour) {
                startService(OTAManager.OTAStartMode.NIGHTTIME, InternalDef.START_COMMAND_CHECK_NEW_VERSION);
            }
        }
    }

    public void startService(OTAStartMode mode, int command) {
        Log.d(TAG, "Start OTA service : " + mode.name());
        this.mStartMode = mode;

        Intent intent = createOTAIntent();
        intent.putExtra(InternalDef.KEY_START_COMMAND, command);
        intent.putExtra(InternalDef.KEY_START_MODE, mStartMode.getValue());
        mContext.startService(intent);
    }

    public void startDowngradeService(OTAStartMode mode, int command) {
        Log.d(TAG, "Start OTA DOWNGRADE service : " + mode.name());
        this.mStartMode = mode;
        Intent intent = createOtaDowngradeIntent();
        intent.putExtra(InternalDef.KEY_START_COMMAND, command);
        intent.putExtra(InternalDef.KEY_START_MODE, mStartMode.getValue());
        mContext.startService(intent);
    }

    public void resetStatMode() {
        this.mStartMode = OTAStartMode.SETTING;
    }

    public OTAStartMode getStartMode() {
        return mStartMode;
    }

    public void stopService() {
        Log.d(TAG, "Stop OTA service");
        Intent intent = createOTAIntent();
        intent.putExtra(InternalDef.KEY_START_COMMAND,
                InternalDef.START_COMMAND_STOP_SERVICE);
        mContext.startService(intent);
    }

    public void stopOtaDownload() {
        Log.d(TAG, "Stop OTA download");
        Intent intent = createOTAIntent();
        intent.putExtra(InternalDef.KEY_START_COMMAND,
                InternalDef.START_COMMAND_DOWNLOAD_STOP);
        mContext.startService(intent);
    }

    private Intent createOTAIntent() {
        ComponentName component = new ComponentName(
                InternalDef.OTA_PACKAGE_NAME,
                InternalDef.OTA_SERVICE_NAME);
        Intent intent = new Intent();
        intent.setComponent(component);
        return intent;
    }

    public void stopOtaDowngradeService() {
        Log.d(TAG, "stopOtaDowngradeService");
        Intent intent = createOtaDowngradeIntent();
        intent.putExtra(InternalDef.KEY_START_COMMAND, START_COMMAND_STOP_DOWNGRADE_SERVICE);
        mContext.startService(intent);
    }

    private Intent createOtaDowngradeIntent() {
        ComponentName component = new ComponentName(InternalDef.OTA_DOWNGRADE_PACKAGE_NAME,
                InternalDef.OTA_DOWNGRADE_SERVICE_NAME);
        Intent intent = new Intent();
        intent.setComponent(component);
        return intent;
    }
}
