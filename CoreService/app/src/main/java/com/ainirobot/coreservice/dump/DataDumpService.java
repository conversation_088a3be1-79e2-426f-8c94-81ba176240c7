package com.ainirobot.coreservice.dump;

import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.IBinder;
import android.support.annotation.Nullable;
import android.util.Log;

import com.ainirobot.coreservice.client.ApiListener;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.input.DumpApi;
import com.ainirobot.coreservice.dump.callback.DumpHwCallBack;
import com.ainirobot.coreservice.dump.callback.DumpModuleCallBack;
import com.ainirobot.coreservice.dump.manager.DataDumpManager;
import com.ainirobot.coreservice.utils.DelayTask;

/**
 * Created by Orion on 2020/5/25.
 */
public class DataDumpService extends Service {
    private static final String TAG = "DataDumpService";

    private Context mContext;
    private SystemApi mSystemApi;
    private DumpModuleCallBack mDumpModuleCallBack;
    private DumpHwCallBack mDumpHwCallBack;
    private DumpApi mDumpApi;
    private DataDumpManager mDataDumpManager;

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "Start data dump service : " + this.getPackageName());
        this.mContext = getApplicationContext();

        mDataDumpManager = new DataDumpManager(this);

        //调用其它外设api
        mSystemApi = SystemApi.getInstance();
        //接收其它外设request和hwStatus
        mDumpModuleCallBack = new DumpModuleCallBack(this);
        initSystemApi();

        //上报状态和应答
        mDumpApi = new DumpApi(mContext);
        //接收上层模块发送的command，即当前dump进程对外提供的api操作
        mDumpHwCallBack = new DumpHwCallBack(this);
        initDumpApi();
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        throw new UnsupportedOperationException("Not yet implemented");
    }

    /**
     * 初始化SystemApi
     */
    private void initSystemApi() {
        Log.d(TAG, "Connect to core service : initSystemApi");
        mSystemApi.connect(this, new ApiListener() {
            @Override
            public void handleApiDisabled() {

            }

            @Override
            public void handleApiConnected() {
                Log.d(TAG, "Handle api connected : initSystemApi");
                DelayTask.cancel(TAG);
                mSystemApi.setCallback(mDumpModuleCallBack);
            }

            @Override
            public void handleApiDisconnected() {
                Log.d(TAG, "Handle api disconnected : initSystemApi");
                reconnectSystemApi();
            }
        });

        reconnectSystemApi();
    }

    /**
     * 重连CoreService
     */
    private void reconnectSystemApi() {
        Log.d(TAG, "Reconnect to core service : SystemApi");
        DelayTask.submit(this, new Runnable() {
            @Override
            public void run() {
                if (mSystemApi.isApiConnectedService()) {
                    Log.d(TAG, "The SystemApi is connected");
                    return;
                }
                initSystemApi();
            }
        }, 3000);
    }

    /**
     * 初始化DumpApi
     */
    private void initDumpApi() {
        Log.d(TAG, "Connect to core service : initDumpApi");
        mDumpApi.addApiEventListener(new ApiListener() {
            @Override
            public void handleApiDisabled() {

            }

            @Override
            public void handleApiConnected() {
                Log.d(TAG, "Handle api connected : initDumpApi");
                mDumpApi.registerHWService(RobotOS.DUMP_SERVICE, mDumpHwCallBack);
            }

            @Override
            public void handleApiDisconnected() {
                Log.d(TAG, "Handle api disconnected : initDumpApi");
                reconnectDumpApi();
            }
        });
        mDumpApi.connectApi();
    }

    private void reconnectDumpApi() {
        Log.d(TAG, "Connect to core service : DumpApi");
        DelayTask.submit(this, new Runnable() {
            @Override
            public void run() {
                if (mDumpApi.isApiConnectedService()) {
                    Log.d(TAG, "The DumpApi is connected");
                    return;
                }
                mDumpApi.connectApi();
            }
        }, 3000);
    }

    public DataDumpManager getDataDumpManager() {
        return mDataDumpManager;
    }

    public DumpModuleCallBack getDumpModuleCallBack() {
        return mDumpModuleCallBack;
    }

    public DumpHwCallBack getDumpHwCallBack() {
        return mDumpHwCallBack;
    }

    public DumpApi getDumpApi() {
        return mDumpApi;
    }

}
