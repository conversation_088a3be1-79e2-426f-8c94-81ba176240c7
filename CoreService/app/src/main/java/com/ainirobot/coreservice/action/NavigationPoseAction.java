/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.action;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.StopCommandList.IStopCommandChecker;
import com.ainirobot.coreservice.action.StopCommandList.StopCommand;
import com.ainirobot.coreservice.bi.report.NavigationFollowActionReporter;
import com.ainirobot.coreservice.bi.report.SdkNavigateFinishReport;
import com.ainirobot.coreservice.bi.report.SdkNavigateStartReport;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.actionbean.NavigationBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.robotlog.RobotLog;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Timer;
import java.util.TimerTask;

public class NavigationPoseAction extends AbstractAction<NavigationBean> {
    private static final String TAG = "NavigationPoseAction";

    //if 20 seconds moving distance less than 0.1 meters is that navigation failed
    private static final double MIN_DISTANCE = 0.1; //unit meter
    private static final long DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT = 300 * 1000;

    private enum NavigationState {
        IDLE, PREPARE, NAVIGATION, STOPPED
    }

    private int mReqId;
    private String mDestination;
    private double mLinearSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED;
    private double mAngularSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED;
    private Pose mDestPose;
    private double mCoordinateDeviation;
    private long mTimeout;
    private String mPoseListener;
    private Gson mGson;
    private Pose mFlagPose;
    private NavigationState mStatus;
    private SdkNavigateStartReport navigateStartReport;
    private SdkNavigateFinishReport navigateFinishReport;
    private boolean mNeedRotate;
    /**多机模式等待超时时长，默认300s **/
    private long mWaitTimeout;
    /**是否是在多机避障状态，如果是则停止处理机器N秒不动退出导航的逻辑 **/
    private volatile boolean isMultipleWaitStatus = false;
    private Timer mWaitTimer;

    protected NavigationPoseAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_POSE_NAVIGATION, resList);
        mGson = new Gson();

        navigateStartReport = new SdkNavigateStartReport();
        navigateFinishReport = new SdkNavigateFinishReport();
    }

    @Override
    protected boolean startAction(NavigationBean parameter, LocalApi api) {
        if (verifyParam(parameter)) {
            prepareNavigation(parameter);
            return true;
        }
        return false;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        RobotLog.d("Stop navigation action");
        updateMultipleRobotWaitingStatus(false);
        cancelWaitTimer();
        mStatus = NavigationState.STOPPED;
        unregisterStatusListener(mPoseListener);
        mFlagPose = null;
        //mApi.stopNavigation(mReqId);
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_IS_IN_LOCATION:
                verifyLocation(result);
                return true;

            case Definition.CMD_NAVI_GET_LOCATION:
                processGetLocation(result);
                return true;

            case Definition.CMD_NAVI_IS_ESTIMATE:
                processPoseEstimate(result);
                return true;

            case Definition.CMD_NAVI_GO_POSITION:
                processNavigationResult(result , extraData);
                return true;
        }
        return false;
    }

    private void processNavigationResult(String result, String extraData) {
        Log.d(TAG, "processNavigationResult : " + result);

        navigateFinishReport.fillData().report();

        if (Definition.NAVIGATION_OK.equals(result)) {
            if (mStatus != NavigationState.STOPPED){
                onResult(Definition.RESULT_OK, result);
            }else {
                onResult(Definition.RESULT_STOP, result);
            }
            return;
        }
        if (Definition.NAVIGATION_CANCELED.equals(result)) {
            onResult(Definition.RESULT_STOP, result);
            return;
        }
        processError(Definition.ERROR_NAVIGATION_FAILED, "Navigation failed : " + result, extraData);
    }

    private void processError(int errorCode, String errorMessage, String extraData) {
        Log.d(TAG, "processError : " + errorCode + " " + errorMessage + " " + extraData);
        super.onError(errorCode, errorMessage, extraData);
    }


    @Override
    public boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_GO_POSITION:
                processNavigationStatus(status, extraData);
                return true;
        }
        return false;
    }

    private void prepareNavigation(NavigationBean params) {
        Log.d(TAG, "Start navigation action");
        mStatus = NavigationState.PREPARE;
        mReqId = params.getReqId();
        mDestination = params.getDestination();
        mLinearSpeed = params.getLinearSpeed();
        mAngularSpeed = params.getAngularSpeed();
        mCoordinateDeviation = params.getCoordinateDeviation();
        mNeedRotate = params.ismNeedRotate();
        mFlagPose = null;
        long timeout = params.getTime();
        mTimeout = ((timeout == 0) ? Long.MAX_VALUE : timeout);
        long waitTimeout = params.getMultipleWaitTime();
        mWaitTimeout = (waitTimeout <= 0) ? DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT: waitTimeout;
        isMultipleWaitStatus = false;
        //isRobotInLocation(mDestination);
        checkPoseEstimate();
        registerPoseListener();
    }

    private void isRobotInLocation(String placeName, double coordinateDeviation) {
        JSONObject obj = new JSONObject();
        try {
            obj.put(Definition.JSON_NAVI_TARGET_PLACE_NAME, placeName);
            obj.put(Definition.JSON_NAVI_COORDINATE_DEVIATION, coordinateDeviation);
            mApi.isRobotInlocations(mReqId, obj.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void checkPoseEstimate() {
        Log.d(TAG, "Check pose estimate");
        mApi.isRobotEstimate(mReqId);
    }

    private void processNavigationStatus(String status, String extraData) {
        switch (status) {
            case Definition.NAVIGATION_OCCLUDED:
                onStatusUpdate(Definition.STATUS_GOAL_OCCLUDED, "The target is occluded", extraData);
                break;

            case Definition.NAVIGATION_OCCLUDED_END:
                onStatusUpdate(Definition.STATUS_GOAL_OCCLUDED_END, "The occluded end", extraData);
                break;

            case Definition.NAVIGATION_AVOID:
                onStatusUpdate(Definition.STATUS_NAVI_AVOID, "The local path search failed", extraData);
                break;

            case Definition.NAVIGATION_AVOID_END:
                onStatusUpdate(Definition.STATUS_NAVI_AVOID_END, "The local path search succeed", extraData);
                break;

            case Definition.NAVIGATION_OUT_MAP:
                onStatusUpdate(Definition.STATUS_NAVI_OUT_MAP, "The goal is out of map or more dangerous", extraData);
                break;

            case Definition.NAVIGATION_GLOBAL_PATH_FAILED:
                onStatusUpdate(Definition.STATUS_NAVI_GLOBAL_PATH_FAILED, "The global is path search failed", extraData);
                break;

            case Definition.NAVIGATION_OBSTACLES_AVOID:
                onStatusUpdate(Definition.STATUS_NAVI_OBSTACLES_AVOID, "obstacles avoid", extraData);
                break;

            case Definition.NAVIGATION_STARTED:
                onStatusUpdate(Definition.STATUS_START_NAVIGATION, "Navigation has been started", extraData);
                break;
            case Definition.NAVIGATION_MULTI_ROBOT_WAITING:
                updateMultipleRobotWaitingStatus(true);
                startWaitingTimer();
                onStatusUpdate(Definition.STATUS_NAVI_MULTI_ROBOT_WAITING, "Multiple robots are waiting to avoid obstacles");
                break;

            case Definition.NAVIGATION_MULTI_ROBOT_WAITING_END:
                updateMultipleRobotWaitingStatus(false);
                cancelWaitTimer();
                onStatusUpdate(Definition.STATUS_NAVI_MULTI_ROBOT_WAITING_END, "Multiple robots passageways returned to normal");
                break;
            default:
                Log.i(TAG, "Command status: " + status +  " doesn't be handled");
                break;
        }
    }

    /**
     * 更新多机避障状态，如果是多机避障状态，则机器人超时等待策略需忽略
     * @param isWaiting 是否正在多机避障等待状态
     */
    private void updateMultipleRobotWaitingStatus(boolean isWaiting){
        isMultipleWaitStatus = isWaiting;
    }

    private void startWaitingTimer() {
        cancelWaitTimer();
        mWaitTimer = new Timer();
        mWaitTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (mStatus != NavigationState.NAVIGATION) {
                    return;
                }
                onError(Definition.ERROR_MULTI_ROBOT_WAITING_TIMEOUT, "multiple robot wait timeout");
            }
        }, mWaitTimeout);
    }

    private void cancelWaitTimer() {
        if (mWaitTimer != null) {
            mWaitTimer.cancel();
            mWaitTimer = null;
        }
    }

    private void processPoseEstimate(String result) {
        if (!"true".equals(result)) {
            onError(Definition.ERROR_NOT_ESTIMATE, "Not pose estimate");
            return;
        }
        isRobotInLocation(mDestination, mCoordinateDeviation);
    }

    private void verifyLocation(String result) {
        try {
            JSONObject json = new JSONObject(result);
            boolean isInLocation = json.optBoolean(Definition.JSON_NAVI_IS_IN_LOCATION, false);
            if (isInLocation) {
                //stop();
                onResult(Definition.ERROR_IN_DESTINATION, "Already at the " + mDestination);
            } else {
                mApi.getLocation(mReqId, mDestination);
            }
        } catch (JSONException e) {
            e.printStackTrace();
            onError(Definition.ERROR_IN_DESTINATION, "Judge at the " + mDestination + " failed");
        }
    }

    private void processGetLocation(String result) {
        try {
            JSONObject json = new JSONObject(result);
            boolean isExist = json.getBoolean(Definition.JSON_NAVI_SITE_EXIST);
            if (isExist) {
                mDestPose = mGson.fromJson(result, Pose.class);
                Log.i(TAG, "processGetLocation: mDestPose " + mDestPose.toString());
                mStatus = NavigationState.NAVIGATION;
                //onStatusUpdate(Definition.STATUS_START_NAVIGATION, "Start navigation");
                //mApi.goPlace(mReqId, mDestination, mLinearSpeed, mAngularSpeed);
                float theta = mDestPose.getTheta();

                Log.d(TAG, "handle location, mNeedRotate= " + mNeedRotate + ", theta= " + theta);
                if (mNeedRotate) {
                    if (0 == theta) {
                        theta = 3.1415926f;
                    } else if (0 < theta) {
                        theta = theta - 3.1415926f;
                    } else if (0 > theta) {
                        theta = theta + 3.1415926f;
                    }
                }

                Log.d(TAG, "handle location, theta= " + theta);
                mApi.goPosition(mReqId, mDestPose.getX(), mDestPose.getY(), theta, mLinearSpeed, mAngularSpeed);
                navigateStartReport.fillData(mDestination)
                        .report();
            } else {
                //stop();
                onError(Definition.ERROR_DESTINATION_NOT_EXIST, "Destination not exist");
            }
        } catch (JSONException e) {
            e.printStackTrace();
            //stop();
            onError(Definition.ERROR_DESTINATION_NOT_EXIST, "Get " + mDestination + " failed");
        }
    }

    private void registerPoseListener() {
        mPoseListener = registerStatusListener(Definition.STATUS_POSE, new StatusListener() {
            @Override
            public void onStatusUpdate(String data) {
                onPoseUpdate(data);
            }
        });
    }

    private synchronized void onPoseUpdate(String data) {
        if (TextUtils.isEmpty(data) || mStatus != NavigationState.NAVIGATION) {
            return;
        }
        Pose pose = mGson.fromJson(data, Pose.class);
        if (isAborted(pose)) {
            Log.d(TAG, "Pose timeout : " + mFlagPose.toString() + "   "
                    + pose.toString() + "  " + mStatus);
            onError(Definition.ERROR_DESTINATION_CAN_NOT_ARRAIVE, "Timeout");
            //stop();
        }
    }

    private boolean isAborted(Pose pose) {
        double distance = pose.getDistance(mFlagPose);
        if (!isMultipleWaitStatus && Double.compare(distance, MIN_DISTANCE) < 0) {
            long movingTime = pose.getTime() - mFlagPose.getTime();
            if (movingTime > mTimeout) {
                return true;
            }
        } else {
            mFlagPose = pose;
        }
        return false;
    }

    public boolean verifyParam(NavigationBean params) {
        if (TextUtils.isEmpty(params.getDestination())) {
            return false;
        }
        return true;
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return new ArrayList<StopCommand>() {{
            add(new StopCommand(Definition.CMD_NAVI_STOP_NAVIGATION, null));
        }};
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return new IStopCommandChecker() {
            @Override
            public boolean executeSuccess(String command, String result) {
                RobotLog.d("Leading stop check : " + command);
                switch (command) {
                    case Definition.CMD_NAVI_STOP_NAVIGATION:
                        return true;
                }
                return false;
            }
        };
    }
}
