/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.action.advnavi.bean;

import android.util.Log;

import com.ainirobot.coreservice.bean.MultiFloorInfo;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.GatePairPose;
import com.ainirobot.coreservice.client.actionbean.MultiRobotConfigBean;
import com.ainirobot.coreservice.client.actionbean.NavigationAdvancedBean;
import com.ainirobot.coreservice.client.actionbean.Pose;

import java.util.ArrayList;
import java.util.List;

public class StateData {
    private Definition.ElevatorAction actionCode = Definition.ElevatorAction.ACTION_DEFAULT;
    /**
     * 结束原因
     */
    private int resultCode = 0;
    /**
     * 消息
     */
    private String msg;
    /**
     * 额外参数
     */
    private String extraData;
    /**
     * 当前机器人所在楼层的 楼层映射
     * 即出发楼层
     */
    private int currentFloorIndex;
    /**
     * 乘坐的电梯中心点
     */
    private Pose elevatorCenterPose;
    /**
     * 进电梯时，机器实际停靠在电梯内的位置和电梯中心位置的差值pose，进电梯和重定位时用到
     */
    protected Pose deltaPoseEnter;
    /**
     * 第一个闸机口
     */
    private Pose gateFirstPose;
    /**
     * 第二个闸机口
     */
    private Pose gateSecondPose;

    /**
     * 当前楼层的多层配置信息
     */
    private MultiFloorInfo currentFloorMultiFloorInfo;

    /**
     * 目的地楼层的多层配置信息
     */
    private MultiFloorInfo targetFloorMultiFloorInfo;

    /**
     * 导航参数
     */
    private final NavigationAdvancedBean navigationBean;

    /**
     * 多机配置信息
     */
    private MultiRobotConfigBean multiRobotConfigBean;

    /**
     * 有效备用点(没有目的地点位)
     */
    private final List<Pose> validStandbyList = new ArrayList<>();

    /**
     * 电梯等待点
     */
    private final List<Pose> elevatorWaitPoints = new ArrayList<>();

    /**
     * 本次导航是否前往电梯等待点
     */
    private boolean isGoElevatorWaitPoint = false;

    public StateData(NavigationAdvancedBean navigationBean) {
        this.navigationBean = navigationBean;
    }

    public Definition.ElevatorAction getActionCode() {
        return actionCode;
    }

    public void setActionCode(Definition.ElevatorAction actionCode) {
        this.actionCode = actionCode;
    }

    public int getResultCode() {
        return resultCode;
    }

    public void setResultCode(int resultCode) {
        this.resultCode = resultCode;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getExtraData() {
        return extraData;
    }

    public void setExtraData(String extraData) {
        this.extraData = extraData;
    }

    /**
     * 出发楼层和目的地楼层是否相同
     *
     * @return true:相同
     */
    public boolean isSameFloorIndex() {
        return getCurrentFloorIndex() == getTargetFloorIndex();
    }


    public String getTargetFloorMapName() {
        if (null == targetFloorMultiFloorInfo) {
            return null;
        }
        return targetFloorMultiFloorInfo.getMapName();
    }

    public int getCurrentFloorIndex() {
        return currentFloorIndex;
    }

    public void setCurrentFloorIndex(int currentFloorIndex) {
        this.currentFloorIndex = currentFloorIndex;
    }

    public int getTargetFloorIndex() {
        if (null == navigationBean){
            return 0;
        }
        return navigationBean.getTargetFloor();
    }

    public String getDestination() {
        if (null == navigationBean){
            return "";
        }
        return navigationBean.getDestination();
    }

    public List<GatePairPose> getGatePairPoses() {
        if (null == navigationBean){
            return new ArrayList<>();
        }
        return navigationBean.getGatePairPoses();
    }

    private int currentGateIndex = -1; // 多闸机导航闸机通过记录

    /**
     * 是否可以过多个闸机，根据List<GatePosePair>判断
     */
    public boolean isMultiGateMode() {
        List<GatePairPose> gates = getGatePairPoses();
        return gates != null && !gates.isEmpty();
    }

    /**
     * 移动到下一个闸机
     * @return 是否还有闸机需要处理
     */
    public boolean moveToNextGate() {
        currentGateIndex++;
        return hasMoreGatesToPass();
    }

    /**
     * 是否还有更多的闸机需要处理
     */
    public boolean hasMoreGatesToPass() {
        List<GatePairPose> gates = getGatePairPoses();
        return gates !=null && !gates.isEmpty() && currentGateIndex < gates.size();
    }

    /**
     * 获取当前被使用闸机的第一个点位
     */
    public Pose getCurrentGateFirstPose() {
        List<GatePairPose> gates = getGatePairPoses();
        if (gates != null && currentGateIndex >= 0 && currentGateIndex < gates.size()) {
            return gates.get(currentGateIndex).getFirstPose();
        }
        return null;
    }

    /**
     * 获取当前被使用闸机的第二个点位
     */
    public Pose getCurrentGateSecondPose() {
        List<GatePairPose> gates = getGatePairPoses();
        if (gates != null && currentGateIndex >= 0 && currentGateIndex < gates.size()) {
            return gates.get(currentGateIndex).getSecondPose();
        }
        return null;
    }

    /**
     * 是否是最后一个闸机
     */
    public boolean isLastGate() {
        List<GatePairPose> gates = getGatePairPoses();
        return gates != null && currentGateIndex == gates.size() - 1;
    }

    /**
     * 获取当前闸机索引
     */
    public int getCurrentGateIndex() {
        return currentGateIndex;
    }

    /**
     * 获取当前闸机的身份标识
     */
    public String getCurrentGateId() {
        List<GatePairPose> gates = getGatePairPoses();
        return gates.get(currentGateIndex).getGateIdentifier();
    }


    public NavigationAdvancedBean getNavigationBean() {
        return navigationBean;
    }

    public Pose getElevatorCenterPose() {
        return elevatorCenterPose;
    }

    public void setElevatorCenterPose(Pose elevatorCenterPose) {
        this.elevatorCenterPose = elevatorCenterPose;
    }

    public Pose getGateFirstPose() {
        return navigationBean.getGateFirstPose();
    }

    public void setGateFirstPose(Pose gateFirstPose) {
        this.gateFirstPose = gateFirstPose;
    }

    public Pose getGateSecondPose() {
        return navigationBean.getGateSecondPose();
    }

    public void setGateSecondPose(Pose gateSecondPose) {
        this.gateSecondPose = gateSecondPose;
    }

    public String getElevatorName() {
        if (null != elevatorCenterPose) {
            String poseName = elevatorCenterPose.getName();
//            String elevatorNameOld = poseName.substring(0, poseName.length() - Definition.ELEVATOR_CENTER_POSE.length());
//            return elevatorNameOld;

            //通过”-“判断电梯名字，字符串截取为两段，取第一段
            String[] split = poseName.split("-");
            String elevatorName = split[0];
            Log.d("StateData", "getElevatorName: " + elevatorName + "， elevatorCenterPose: " + elevatorCenterPose);
            return elevatorName;
        }
        return null;
    }

    public String getElevatorCenterPoseName(){
        if (null != elevatorCenterPose) {
            return elevatorCenterPose.getName();
        }
        return null;
    }

    public MultiFloorInfo getCurrentFloorMultiFloorInfo() {
        return currentFloorMultiFloorInfo;
    }

    public void setCurrentFloorMultiFloorInfo(MultiFloorInfo currentFloorMultiFloorInfo) {
        this.currentFloorMultiFloorInfo = currentFloorMultiFloorInfo;
    }

    public MultiFloorInfo getTargetFloorMultiFloorInfo() {
        return targetFloorMultiFloorInfo;
    }

    public void setTargetFloorMultiFloorInfo(MultiFloorInfo targetFloorMultiFloorInfo) {
        this.targetFloorMultiFloorInfo = targetFloorMultiFloorInfo;
    }

    public MultiRobotConfigBean getMultiRobotConfigBean() {
        return multiRobotConfigBean;
    }

    public void setMultiRobotConfigBean(MultiRobotConfigBean multiRobotConfigBean) {
        this.multiRobotConfigBean = multiRobotConfigBean;
    }

    public List<Pose> getValidStandbyList() {
        return validStandbyList;
    }

    public void setValidStandbyList(List<Pose> validStandbyList) {
        this.validStandbyList.clear();
        if (validStandbyList != null && validStandbyList.size() > 0) {
            this.validStandbyList.addAll(validStandbyList);
        }
    }

    public List<Pose> getElevatorWaitPoints() {
        return elevatorWaitPoints;
    }

    public void setElevatorWaitPoints(List<Pose> poseList) {
        this.elevatorWaitPoints.clear();
        if (poseList != null && poseList.size() > 0) {
            this.elevatorWaitPoints.addAll(poseList);
        }
    }

    public boolean getIsGoElevatorWaitPoint() {
        return isGoElevatorWaitPoint;
    }

    public void setIsGoElevatorWaitPoint(boolean isGoElevatorWaitPoint) {
        this.isGoElevatorWaitPoint = isGoElevatorWaitPoint;
    }

    /**
     * 导航策略是否为Elevator
     */
    public boolean isElevatorStrategy() {
        return getNavigationBean().getNaviStrategy() == Definition.AdvNaviStrategy.ELEVATOR;
    }
    /**
     * 导航策略是否为Gate
     */
    public boolean isGateStrategy() {
        return getNavigationBean().getNaviStrategy() == Definition.AdvNaviStrategy.GATE;
    }

    public Pose getDeltaPoseEnter() {
        return deltaPoseEnter;
    }

    public void setDeltaPoseEnter(Pose deltaPoseEnter) {
        this.deltaPoseEnter = deltaPoseEnter;
    }

    @Override
    public String toString() {
        return "StateData{" +
                "destination=" + getDestination() +
                ", actionCode=" + actionCode +
                ", resultCode=" + resultCode +
                ", msg='" + msg + '\'' +
                ", extraData='" + extraData + '\'' +
                ", currentFloorIndex=" + currentFloorIndex +
                ", elevatorCenterPose=" + elevatorCenterPose +
                ", currentFloorMultiFloorInfo=" + currentFloorMultiFloorInfo +
                ", targetFloorMultiFloorInfo=" + targetFloorMultiFloorInfo +
                ", navigationBean=" + navigationBean +
                ", multiRobotConfigBean=" + multiRobotConfigBean +
                ", validStandbyList=" + validStandbyList +
                ", deltaPoseEnter=" + deltaPoseEnter +
                '}';
    }
}
