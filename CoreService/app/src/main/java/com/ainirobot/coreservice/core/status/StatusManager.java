/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.core.status;

import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.IStatusListener;
import com.ainirobot.coreservice.core.status.biz.StatusProvider;
import com.ainirobot.coreservice.service.CoreService;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.robotlog.RobotLog;

public class StatusManager {

    private static final int MSG_STATUS_REGISTER_HW = 0x0000;
    private static final int MSG_STATUS_UNREGISTER_HW = 0x0001;
    private static final int MSG_STATUS_SEND_STATUS = 0x0010;
    private static final int MSG_STATUS_QUERY_STATUS = 0x0011;
    private static final int MSG_STATUS_REGISTER_LISTENER = 0x0100;
    private static final int MSG_STATUS_UNREGISTER_LISTENER = 0x0101;

    private static final String TAG = "StatusManager";

    private final String SEPARATOR = "@";

    private final String BUNDLE_TYPE = "type";
    private final String BUNDLE_DATA = "data";
    private final String BUNDLE_HW_FUNCTION = "hwFunction";

    private final IStatus mStatus;
    private Handler mStatusHandler;
    private HandlerThread mStatusThread;

    public StatusManager(CoreService coreService) {
        mStatus = new StatusProvider(coreService);

        mStatusThread = new HandlerThread("StatusThread");
        mStatusThread.start();
        mStatusHandler = new StatusHandler(mStatusThread.getLooper());
    }

    public void registerHWCallback(String serviceName) {
        Message message = Message.obtain();
        Bundle bundle = new Bundle();
        bundle.putString(BUNDLE_HW_FUNCTION, serviceName);
        message.setData(bundle);
        message.what = MSG_STATUS_REGISTER_HW;
        mStatusHandler.sendMessage(message);
    }

    public void unregisterHWCallback(String serviceName) {
        Message message = Message.obtain();
        Bundle bundle = new Bundle();
        bundle.putString(BUNDLE_HW_FUNCTION, serviceName);
        message.setData(bundle);
        message.what = MSG_STATUS_UNREGISTER_HW;
        mStatusHandler.sendMessage(message);
    }

    public void handleStatus(String type, String data) {
        handleStatus(RobotOS.UNKNOWN_SERVICE, type, data);
    }

    public void handleStatus(String serviceName, String type, String data) {
        Message message = Message.obtain();
        Bundle bundle = new Bundle();
        bundle.putString(BUNDLE_TYPE, type);
        bundle.putString(BUNDLE_DATA, data);
        bundle.putString(BUNDLE_HW_FUNCTION, serviceName);
        message.setData(bundle);
        message.what = MSG_STATUS_SEND_STATUS;
        mStatusHandler.sendMessage(message);
    }

    public synchronized String registerStatusListener(String type, StatusSubscriber listener) {
        Log.d(TAG, "Status   listener : type=" + type + ", listener:"
                + (listener == null ? "" : listener.hashCode()));
        Message message = Message.obtain();
        Bundle bundle = new Bundle();
        bundle.putString(BUNDLE_TYPE, type);
        message.setData(bundle);
        message.obj = listener;
        message.what = MSG_STATUS_REGISTER_LISTENER;
        mStatusHandler.sendMessage(message);
        return generateId(type, listener);
    }

    public synchronized boolean unregisterStatusListener(String id) {
        Log.d(TAG, "Status unregister listener : id=" + id);
        if (TextUtils.isEmpty(id) || !id.contains(SEPARATOR)) {
            return false;
        }

        String[] content = id.split(SEPARATOR);
        String type = content[0];
        int hashcode = Integer.parseInt(content[1]);
        return mStatus.unregisterStatusListener(type, hashcode);
    }

    public void getRobotStatus(String type, IStatusListener listener) {
        RemoteSubscriber remoteSubscriber = new RemoteSubscriber(listener);
        Message message = Message.obtain();
        Bundle bundle = new Bundle();
        bundle.putString(BUNDLE_TYPE, type);
        message.setData(bundle);
        message.obj = remoteSubscriber;
        message.what = MSG_STATUS_QUERY_STATUS;
        mStatusHandler.sendMessage(message);
    }

    private String generateId(String type, StatusSubscriber listener) {
        return type + SEPARATOR + listener.hashCode();
    }

    private class StatusHandler extends Handler {

        public StatusHandler(Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(Message msg) {
            if (msg == null) {
                return;
            }
            Bundle bundle = msg.getData();
            String type = bundle.getString(BUNDLE_TYPE);
            String data = bundle.getString(BUNDLE_DATA);
            String serviceName;

            switch (msg.what) {
                case MSG_STATUS_REGISTER_HW:
                    serviceName = bundle.getString(BUNDLE_HW_FUNCTION);
                    mStatus.registerHWCallback(serviceName);
                    break;
                case MSG_STATUS_UNREGISTER_HW:
                    serviceName = bundle.getString(BUNDLE_HW_FUNCTION);
                    mStatus.unregisterHWCallback(serviceName);
                    break;
                case MSG_STATUS_QUERY_STATUS:
                    mStatus.getRobotStatus(type, (StatusSubscriber) msg.obj);
                    break;
                case MSG_STATUS_SEND_STATUS:
                    serviceName = bundle.getString(BUNDLE_HW_FUNCTION);
                    if (data != null) {
                        mStatus.sendStatusReport(serviceName, type, data);
                    }
                    break;
                case MSG_STATUS_REGISTER_LISTENER:
                    mStatus.registerStatusListener(type, (StatusSubscriber) msg.obj);
                    break;
                default:
                    break;
            }
        }
    }
}