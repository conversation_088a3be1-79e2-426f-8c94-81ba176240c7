package com.ainirobot.coreservice.action;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.NavigationFollowBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.core.LocalApi;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.Timer;
import java.util.TimerTask;

/**
 * 导航跟随
 * <p>
 * 提供start、stop、pause、resume接口，解析NavigationBodyBean参数。
 */
public class NavigationFollowAction extends AbstractAction<NavigationFollowBean> {
    private static final String TAG = NavigationFollowAction.class.getSimpleName();

    private static final double MIN_DISTANCE = 0.1; //unit meter
    private static final int DEFAULT_LOST_TIMEOUT = 10;
    private String mFollowId;

    private enum FollowState {
        IDLE, PREPARE, FOLLOWING, LOST
    }

    private int mReqId;
    private String mPoseListener;
    private Gson mGson;
    private Pose mFlagPose;
    private FollowState mStatus;
    private Timer mLostTimer;
    private int mLostFindTimeout;


    protected NavigationFollowAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_NAVIGATION_FOLLOW, resList);
    }

    @Override
    protected boolean startAction(NavigationFollowBean parameter, LocalApi api) {
        Log.d(TAG, "startAction:: parameter= " + parameter.toString());
        parseParams(parameter);
//        checkPoseEstimate();
        startHumanFollow();
        return true;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        unregisterStatusListener(mPoseListener);
        cancelLostTimer();
        mStatus = FollowState.IDLE;
        return true;
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return new ArrayList<StopCommandList.StopCommand>() {{
            add(new StopCommandList.StopCommand(Definition.CMD_NAVI_STOP_HUMAN_FOLLOW, null));
        }};
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return (command, result) -> Definition.CMD_NAVI_STOP_HUMAN_FOLLOW.equals(command);
    }

    @Override
    protected boolean verifyParam(NavigationFollowBean param) {
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        Log.d(TAG, "cmdResponse: command=" + command + " result=" + result + " extraData=" + extraData);
        switch (command) {
            case Definition.CMD_NAVI_START_HUMAN_FOLLOW:
                //目前跟随没有时间限制，只要是response就认为是失败，根据具体情况处理
                onError(Definition.STATUS_HUMAN_FOLLOWING_TRACKING_START_FAILED, "human following failed:" + result, extraData);
                return true;
            case Definition.CMD_NAVI_IS_ESTIMATE:
                processPoseEstimate(result, extraData);
                return true;
            default:
                return false;
        }
    }

    @Override
    public boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        Log.d(TAG, "cmdStatusUpdate: command=" + command + " status=" + status + " extraData=" + extraData);
        if (!Definition.CMD_NAVI_START_HUMAN_FOLLOW.equals(command)) {
            Log.e(TAG, "command err:" + command);
            return false;
        }
        switch (status) {
            case Definition.NAVIGATION_HUMAN_START_SUCCESS:
                mStatus = FollowState.PREPARE;
//                registerPoseListener();
//                startLostTimer();
                return true;
            case Definition.NAVIGATION_HUMAN_START_FAILED:
                onError(Definition.STATUS_HUMAN_FOLLOWING_TRACKING_START_FAILED, "start human following failed", extraData);
                return true;
            case Definition.NAVIGATION_HUMAN_FOLLOWING:
                mStatus = FollowState.FOLLOWING;
                onStatusUpdate(Definition.STATUS_HUMAN_FOLLOWING_INIT_SUCCESS, "");
                return true;
            case Definition.NAVIGATION_HUMAN_LOST:
                mStatus = FollowState.LOST;
                onStatusUpdate(Definition.STATUS_HUMAN_FOLLOWING_TRACKING_LOST, "");
                return true;
            case Definition.NAVIGATION_HUMAN_ID_CHANGE:
                onStatusUpdate(Definition.STATUS_HUMAN_FOLLOWING_ID_CHANGE, extraData);
                return true;
            case Definition.NAVIGATION_HUMAN_FOLLOWING_LONG_TIME_NO_TAG:
                onStatusUpdate(Definition.STATUS_HUMAN_FOLLOWING_LONG_TIME_NO_TAG, "");
                return true;
        }
        return false;
    }

    private void parseParams(NavigationFollowBean parameter) {
        mReqId = parameter.getReqId();
        mLostFindTimeout = parameter.getLostFindTimeout();
        mLostFindTimeout = (mLostFindTimeout <= 0) ? DEFAULT_LOST_TIMEOUT : mLostFindTimeout;
        mFollowId = parameter.getFollowId();
    }

    private void checkPoseEstimate() {
        mApi.isRobotEstimate(mReqId);
    }

    private void startHumanFollow() {
        mApi.startHumanFollow(mReqId, mFollowId, mLostFindTimeout);
    }

    private void registerPoseListener() {
        mPoseListener = registerStatusListener(Definition.STATUS_POSE, this::onPoseUpdate);
    }

    private synchronized void onPoseUpdate(String data) {
        if (TextUtils.isEmpty(data) || mStatus == FollowState.IDLE) {
            return;
        }
//        Pose pose = mGson.fromJson(data, Pose.class);
    }

    private void processPoseEstimate(String result, String extraData) {
        if (!"true".equals(result)) {
            onError(Definition.ERROR_NOT_ESTIMATE, "Not pose estimate", extraData);
            return;
        }
        startHumanFollow();
    }

    private void cancelLostTimer() {
        if (mLostTimer != null) {
            mLostTimer.cancel();
            mLostTimer = null;
        }
    }

}
