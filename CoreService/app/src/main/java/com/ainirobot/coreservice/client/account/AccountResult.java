package com.ainirobot.coreservice.client.account;

public class AccountResult {

    public static final int SUCCESS = 1;

    public static class Recovery {
        public static final int RECOVERY_ALREADY_RUN = -100;
        public static final int RECOVERY_USER_ID_EMPTY = -101;
        public static final int RECOVERY_USER_ID_INVALID = -102;
        public static final int RECOVERY_USER_TOKEN_INVALID = -103;
        public static final int RECOVERY_FAILED = -104;
    }
}
