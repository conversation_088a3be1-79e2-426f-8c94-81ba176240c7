///*
// *  Copyright (C) 2017 OrionStar Technology Project
// *
// *  Licensed under the Apache License, Version 2.0 (the "License");
// *  you may not use this file except in compliance with the License.
// *  You may obtain a copy of the License at
// *
// *       http://www.apache.org/licenses/LICENSE-2.0
// *
// *  Unless required by applicable law or agreed to in writing, software
// *  distributed under the License is distributed on an "AS IS" BASIS,
// *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// *  See the License for the specific language governing permissions and
// *  limitations under the License.
// */
//
//package com.ainirobot.coreservice.log;
//
//import android.util.Log;
//
//import java.io.PrintWriter;
//import java.io.StringWriter;
//import java.io.Writer;
//
//public final class RobotLog {
//    private static final String TAG = "RobotLog";
//
//
//    public static int v(String tag, String msg) {
//        return Log.v(tag, msg);
//    }
//
//    public static int d(String tag, String msg) {
//        return Log.e(tag, msg);
//    }
//
//    public static int e(String tag, String msg) {
//        return Log.e(tag, msg);
//    }
//
//    public static int v(String msg) {
//        return Log.v(TAG, msg);
//    }
//
//    public static int d(String msg) {
//        return Log.e(TAG, msg);
//    }
//
//    public static int e(String msg) {
//        return Log.e(TAG, msg);
//    }
//}