/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.client.messagedispatcher;

import android.os.RemoteException;

import com.ainirobot.coreservice.client.StatusListener;

class StatusMessage implements IMsgHandle, IRecyclable {
    private static RecyclablePool<StatusMessage> sPool = new RecyclablePool<>();

    private String type;
    private String data;
    private StatusListener listener;

    private StatusMessage(String type, String data, StatusListener listener) {
        this.type = type;
        this.data = data;
        this.listener = listener;
    }

    static StatusMessage obtain(String type, String data, StatusListener listener) {
        StatusMessage statusMessage = sPool.obtain();

        if (statusMessage == null) {
            statusMessage = new StatusMessage(type, data, listener);
        } else {
            statusMessage.type = type;
            statusMessage.data = data;
            statusMessage.listener = listener;
        }
        return statusMessage;
    }

    @Override
    public void recycle() {
        type = null;
        data = null;
        listener = null;
    }

    @Override
    public void handleMessage() {
        try {
            listener.onStatusUpdate(type, data);
        } catch (RemoteException e) {

        }
        sPool.recycle(this);
    }
}
