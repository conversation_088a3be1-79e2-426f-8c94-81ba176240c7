package com.ainirobot.coreservice.client.actionbean;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * robot os inspection result
 *
 * @version V1.0.0
 * @date 2019/10/10 15:26
 */
public class InspectionResult {

    /**
     * result : 1
     * step : [{"service":"","step":""}]
     * fail : [{"name":"link","code":-1,"service":"硬件服务","errorMsg":"连接异常"},{"name":"motorHorizontal","code":-1,"service":"硬件服务","errorMsg":""}]
     * success : [{"name":"link","service":"硬件服务"},{"name":"motorHorizontal","service":"硬件服务"}]
     */

    private int result;
    private boolean otaResult = true;
    private List<StepBean> step = new ArrayList<>();
    private List<FailBean> fail = new ArrayList<>();
    private List<PassBean> pass = new ArrayList<>();

    public InspectionResult() {
    }

    public InspectionResult(int result, List<StepBean> step, List<FailBean> fail, List<PassBean> pass) {
        this.result = result;
        this.step = step;
        this.fail = fail;
        this.pass = pass;
    }

    public InspectionResult(int result, boolean otaResult, List<StepBean> step, List<FailBean> fail, List<PassBean> pass) {
        this.result = result;
        this.otaResult = otaResult;
        this.step = step;
        this.fail = fail;
        this.pass = pass;
    }

    public boolean isOtaResult() {
        return otaResult;
    }

    public void setOtaResult(boolean otaResult) {
        this.otaResult = otaResult;
    }

    public int getResult() {
        return result;
    }

    public void setResult(int result) {
        this.result = result;
    }

    public List<StepBean> getStep() {
        return step;
    }

    public void setStep(List<StepBean> step) {
        this.step = step;
    }

    public List<FailBean> getFail() {
        return fail;
    }

    public void setFail(List<FailBean> fail) {
        this.fail = fail;
    }

    public List<PassBean> getPass() {
        return pass;
    }

    public void setPass(List<PassBean> pass) {
        this.pass = pass;
    }

    public static class StepBean {
        /**
         * service :
         * step :
         */

        private String service;
        private int step;

        public StepBean() {
        }

        public StepBean(String service, int step) {
            this.service = service;
            this.step = step;
        }

        public String getService() {
            return service;
        }

        public void setService(String service) {
            this.service = service;
        }

        public int getStep() {
            return step;
        }

        public void setStep(int step) {
            this.step = step;
        }

        @Override
        public String toString() {
            return "StepBean{" +
                    "service='" + service + '\'' +
                    ", step='" + step + '\'' +
                    '}';
        }
    }

    public static class FailBean {
        /**
         * name : link
         * code : -1
         * service : 硬件服务
         * errorMsg : 连接异常
         */

        private String name;
        private int code;
        private String service;
        private String errorMsg;
        private boolean isIgnore;

        /**
         * 错误消息多语言支持
         * <p>
         * 存储对应语言的失败信息
         */
        private Map<String, String> langErrorMsg = new HashMap<>();

        public FailBean() {
        }

        public FailBean(String name, int code, String service, String errorMsg) {
            this.name = name;
            this.code = code;
            this.service = service;
            this.errorMsg = errorMsg;
        }

        public FailBean(String name, int code, String service, String errorMsg, boolean isIgnore) {
            this.name = name;
            this.code = code;
            this.service = service;
            this.errorMsg = errorMsg;
            this.isIgnore = isIgnore;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getService() {
            return service;
        }

        public void setService(String service) {
            this.service = service;
        }

        public String getErrorMsg() {
            return errorMsg;
        }

        public void setErrorMsg(String errorMsg) {
            this.errorMsg = errorMsg;
        }

        public boolean isIgnore() {
            return isIgnore;
        }

        public void setIgnore(boolean ignore) {
            isIgnore = ignore;
        }

        public void setErrorMsg(Map<String, String> errorMsg) {
            if (errorMsg == null) {
                errorMsg = new HashMap<>();
            }
            this.langErrorMsg = errorMsg;
        }

        public String getErrorMsg(String language) {
            return this.langErrorMsg.get(language);
        }

        /**
         * 添加多语言错误信息
         *
         * @param lang     语言码
         * @param errorMsg 错误信息
         */
        public void addErrorMsg(String lang, String errorMsg) {
            langErrorMsg.put(lang, errorMsg);
        }

        @Override
        public String toString() {
            return "FailBean{" +
                    "name='" + name + '\'' +
                    ", code=" + code +
                    ", service='" + service + '\'' +
                    ", errorMsg='" + errorMsg + '\'' +
                    ", isIgnore=" + isIgnore +
                    '}';
        }
    }

    public static class PassBean {
        /**
         * name : link
         * service : 硬件服务
         */

        private String name;
        private String service;

        public PassBean() {
        }

        public PassBean(String name, String service) {
            this.name = name;
            this.service = service;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getService() {
            return service;
        }

        public void setService(String service) {
            this.service = service;
        }

        @Override
        public String toString() {
            return "PassBean{" +
                    "name='" + name + '\'' +
                    ", service='" + service + '\'' +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "InspectionResult{" +
                "result=" + result +
                ", otaResult=" + otaResult +
                ", step=" + step +
                ", fail=" + fail +
                ", pass=" + pass +
                '}';
    }
}
