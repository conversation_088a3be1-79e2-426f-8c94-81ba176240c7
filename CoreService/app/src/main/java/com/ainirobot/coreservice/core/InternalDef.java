/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.core;

public class InternalDef {

    //----------------- request and command -----------------//
    public static final String REQ_THREAD_NAME = "request_thread_name";
    public static final String CMD_THREAD_NAME = "command_thread_name";

    public static final String REQ_TYPE = "req_type";
    public static final String CMD_LIST = "cmd_list";
    public static final String CMD_TYPE = "cmd_type";
    public static final String CMD_PARAM = "cmd_param";
    public static final String CMD_IF_WAITING_RESULT = "cmd_if_waitting_result";

    //----------------- intent and message -----------------//
    public static final int MSG_CORE_ADD_REQUEST_ACTION = 100;
    public static final int MSG_CORE_ADD_COMMAND_ACTION = 101;
    public static final int MSG_CORE_HW_REGISTER = 102;
    public static final int MSG_CORE_HW_UNREGISTER = 103;
    public static final int MSG_CORE_HW_CMD_RESPONSE = 104;
    public static final int MSG_CORE_HW_CMD_STATUS = 111;
    public static final int MSG_CORE_HW_STATUS_REPORT = 105;
    public static final int MSG_CORE_NEW_TEXT_SPEECH = 106;
    public static final int MSG_CORE_NEW_TONE_SPEECH = 107;
    public static final int MSG_CORE_SET_ANGLE = 108;
    public static final int MSG_CORE_SPEECH_SET_RECOGNIZE_MODE = 109;
    public static final int MSG_CORE_SPEECH_AVOID_SKILLS = 110;

    public static final int MSG_REQUEST_DEFAULT = 200;
    public static final int MSG_REQUEST_WITH_TYPE = 201;
    public static final int MSG_REQUEST_WITH_TEXT = 202;
    public static final int MSG_REQUEST_FINISH_PARSER = 203;
    public static final int MSG_SWITCH_CONTROL_MODE = 204;

    public static final int MSG_REQUEST_HW_REPORT = 220;

    public static final String BUNDLE_REQ = "bundle_req";
    public static final String BUNDLE_TEXT = "bundle_text";
    public static final String BUNDLE_INT = "bundle_int";
    public static final String BUNDLE_BOOLEAN = "bundle_boolean";
    public static final String BUNDLE_TYPE = "bundle_type";
    public static final String BUNDLE_RESPONSE = "bundle_response";
    public static final String BUNDLE_EXTRA_DATA = "bundle_extra_data";
    public static final String BUNDLE_CONTROL_MODE = "bundle_control_mode";
    public static final String BUNDLE_REQUEST_FROM = "bundle_request_from";
    public static final String BUNDLE_REQUEST_ID = "bundle_request_id";

    //----------------- module -----------------//
    public static final int MODULE_STATUS_IDLE = 0;
    public static final int MODULE_STATUS_ACTIVE = 1;
    public static final int MODULE_STATUS_WAITTING = 2;

    public static final int MODULE_PRIORITY_LOW = 0;
    public static final int MODULE_PRIORITY_MIDDLE = 1;
    public static final int MODULE_PRIORITY_HIGH = 2;
    public static final int MODULE_PRIORITY_COUNT = 3;

    //----------------- input function ----------//
    public static final int INPUT_FUNCTION_NONE = 0;
    public static final int INPUT_FUNCTION_SPEECH = 1;
    public static final int INPUT_FUNCTION_DIRECT_CONNECTION = 2;
    public static final int INPUT_FUNCTION_REMOTE = 3;
    public static final int INPUT_FUNCTION_OTA = 4;
    public static final int INPUT_FUNCTION_DIRECT_REMOTE = 5;
    public static final int INPUT_FUNCTION_SYSTEM = 6;
    public static final int INPUT_FUNCTION_EXTERNAL = 7;

    //---------------- Control mode ----------//
    public static final int MODE_ALL = -1;
    public static final int MODE_SPEECH = 0;
    public static final int MODE_REMOTE = 1;

    //---------------- State mode ----------//
    public static final int STATE_MODE_NORMAL = 0;
    public static final int STATE_MODE_OTA = 1;

    //------------- STM32 ----------------------------//
    public static final int STM32_NUMBER = 6;


    //-------------- Res Id ---------------//
    public static final int RES_INVALID_ID = -1;
    public static final int RES_HEAD_ID = 0;
    public static final int RES_HEAD_CAMERA_ID = 1;
    public static final int RES_CHASSIS_ID = 2;
    public static final int RES_RADAR_ID = 3;
    public static final int RES_CHASSIS_LIGHT_ID = 3;
    public static final int RES_MAX_ID = 4;
    public static final int RES_ELEVATOR_ID = 5;

    //-------------- Res name -------------//
    public static final String[] RES_NAMES = {"Head", "HeadCamera", "Navigation", "Radar"};

    //-------------- Request -------------//
    public static final String REQ_OTA_REMOTE = "req_ota_remote";
    public static final String REQ_OTA_FORCE = "req_ota_upgrade_force";
    public static final String REQ_OTA = "req_ota_upgrade";
    public static final String REQ_OTA_FINISH = "req_ota_finish";
    public static final String REQ_OTA_DOWNGRADE = "req_ota_downgrade";
    public static final String RELOCATE_SWITCH_OPEN = "relocate_switch_open";
    public static final String WECHAT_SET_CHARGING_PILE = "set_charging_pile";
    public static final String WECHAT_FIRST_RECHARGING = "first_recharging";

    //-------------- Setting --------------//
    public static final String SETTING_SET_CHARGE_PILE = "setting_set_charge_pile";
    public static final String SETTING_START_CHARGE = "setting_start_charge";
    public static final String SETTING_START_CHARGE_TIMING = "setting_start_charge_timing";
    public static final String SETTING_AUTO_CHARGE = "setting_auto_charge";
    public static final String SETTING_AUTO_OTA = "setting_auto_ota";
    public static final String SETTING_START_REPOSITION = "setting_start_reposition";
    public static final String SETTING_E70_RECOVERY_START_REPOSITION = "setting_e70_recovery_start_reposition";
    public static final String SETTING_STOP_CHARGE = "setting_stop_charge";
    public static final String SETTING_STOP_CHARGE_BY_APP = "setting_stop_charge_by_app";
    public static final String SETTING_START_DORMANCY = "settings_start_dormancy";
    public static final String SETTING_RECOVERY = "setting_recovery";
    public static final String SETTING_SHUTDOWN_TIMER = "setting_shutdown_timer";
    public static final String SETTING_CHARGING_USABLE = "setting_charging_usable";

    //-------------- OTA ---------------//
    public static final String OTA_UPGRADE = "ota_upgrade";
    public static final String OTA_PACKAGE_NAME = "com.ainirobot.ota";
    public static final String OTA_SERVICE_NAME = "com.ainirobot.ota.service.UpdateService";
    public static final String KEY_START_COMMAND = "start_command";
    public static final String KEY_START_MODE = "start_mode";
    public static final String KEY_CMD_TYPE = "cmdType";
    public static final String KEY_PARAMS = "params";
    public static final String OTA_DOWNGRADE_PACKAGE_NAME = "com.ainirobot.home";
    public static final String OTA_DOWNGRADE_SERVICE_NAME = "com.ainirobot.home.ota.service.DowngradeService";

    public static final String EMERGENCY_PRESS = "1";
    public static final String EMERGENCY_RELEASE = "0";

    public static final int FUNCTION_KEY_PRESS = 1;
    public static final int FUNCTION_KEY_RELEASE = 2;

    public static final int ABNORMAL = 1;
    public static final int NORMAL = 0;

    public static final int START_COMMAND_CHECK_NEW_VERSION = 101;
    public static final int START_COMMAND_CHECK_OTA_RESULT = 103;
    public static final int START_COMMAND_STOP_SERVICE = 106;
    public static final int START_COMMAND_STOP_DOWNGRADE_SERVICE = 105;
    public static final int START_COMMAND_CHECK_OTA_DOWNGRADE_RESULT = 103;
    public static final int START_COMMAND_DOWNLOAD_STOP = 118;

    public static final String ALARM_WHAT = "what";
    public static final String ALARM_TYPE = "type";

    public static final String ACTION_TIMING_TASK = "com.ainirobot.coreservice.timer";
    public static final int OTA_CHECKING = 201;

    public static final String CHARGING = "charging";
    public static final String POSE_ESTIMATE = "poseEstimate";
    public static final String SET_CHARGE_PILE = "setChargePile";
    public static final String HAVE_CUR_NAVI_MAP = "haveCurNaviMap";
    public static final String MAP_DRIFT = "mapDrift";
    public static final String MAP_OUTSIDE_ENTER = "mapOutsideEnter";
    public static final String MAP_OUTSIDE_LEAVE = "mapOutsideLeave";
    public static final String BEING_PUSHED = "beingPushed";
    public static final String BEING_PUSHED_RELEASE = "beingPushedRelease";
    public static final String MULTI_ERROR_TYPE = "multi_error_type";

    public static final String SET_REMOTE_REQ_ID = "setRemoteReqId";

    public static final String UPDATE_RADAR_STATUS = "radar_status";
    public static final String UPDATE_OPEN_RADAR_REASON = "open_radar_reason";

    //------------- Setting -----------//
    public static final String ACTION_START_CHARGE = "com.ainirobot.moduleapp.action.ui.low_power_auto_charge_start";
    public static final String ACTION_SET_CHARGE_PILE = "com.ainirobot.moduleapp.action.ui.set_charge_point_start";
    public static final String ACTION_ROBOT_REPOSITION = "com.orion.messia.reposition_manual";
    public static final String ACTION_CONFIGURE_STOP_CHARGE = "com.ainirobot.moduleapp.action.ui.stop_charging";
    public static final String ACTION_START_DORMANCY = "action_start_dormancy";
    public static final String ACTION_START_SHUTDOWN_TIMER = "action_start_shutdown_timer";

    public static final String OTA_BROADCAST_ACTION_TO_ABOUT_ROBOT = "com.ainirobot.moduleapp.ota.user_touch";
    public static final String OTA_BROADCAST_ACTION_TO_REMOTE_CONTROL = "com.ainirobot.moduleapp.ota.kill.otaservice";
    public static final String OTA_VERSION_UPGRADE_SETTING = "version_upgrade_on_mobile";

    public static final String EXTRA_SET_CHARGING_ENVIRONMENT = "robot.intent.extra.SET_CHARGING_ENVIRONMENT";
    public static final String ACTION_RECOVERY = "com.ainirobot.home.restore_factor_settings";

    public static final String ACTION_CALLER_SYSTEM = "system";
}
