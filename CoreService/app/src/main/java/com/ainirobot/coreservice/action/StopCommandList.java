/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.action;

import android.text.TextUtils;
import android.util.SparseArray;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.concurrent.ConcurrentLinkedQueue;

public class StopCommandList implements ICmdResponse {
    private static final String TAG = StopCommandList.class.getCanonicalName();
    private static ConcurrentLinkedQueue<StopCommandList> mCache = new ConcurrentLinkedQueue<>();

    public static class StopCommand {
        int cmdId;
        String command;
        String param;

        public StopCommand(String command, String param) {
            if (!TextUtils.isEmpty(command)) {
                this.command = command;
                this.param = param;
            } else {
                throw new IllegalArgumentException("parameter error: command = "
                        + command + "; para = " + param);
            }
        }
    }

    interface IStopCommandChecker {
        /**
         * check stop command executed success or not.
         *
         * @param command stop cmd
         * @param result  executed result
         * @return true is successful.
         */
        boolean executeSuccess(String command, String result);
    }

    private SparseArray<StopCommand> mCommandList = new SparseArray<>();
    private IStopCommandChecker mChecker;
    private ArrayList<String> mFailedList = new ArrayList<>();

    static StopCommandList Obtain() {
        StopCommandList item = mCache.poll();
        if (item == null) {
            item = new StopCommandList();
        }
        return item;
    }

    void recycle() {
        mCommandList.clear();
        mFailedList.clear();
        mChecker = null;
        mCache.offer(this);
    }

    void addCommands(ArrayList<StopCommand> commands) {
        for (StopCommand item : commands) {
            mCommandList.put(item.cmdId, item);
        }
    }

    void setChecker(IStopCommandChecker response) {
        mChecker = response;
    }

    boolean complete() {
        return mCommandList.size() == 0;
    }

    Iterator<String> getFailedIterator() {
        return (mFailedList.size() > 0) ? mFailedList.iterator() : null;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        if (mChecker != null && mCommandList.get(cmdId) != null) {
            if (!mChecker.executeSuccess(command, result)) {
                mFailedList.add(command);
            }
            mCommandList.remove(cmdId);
            return true;
        }
        return false;
    }
}
