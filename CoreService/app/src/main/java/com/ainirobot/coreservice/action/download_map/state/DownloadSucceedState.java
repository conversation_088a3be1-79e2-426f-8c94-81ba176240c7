package com.ainirobot.coreservice.action.download_map.state;

import com.ainirobot.coreservice.action.download_map.DownloadMapStateEnum;
import com.ainirobot.coreservice.client.actionbean.DownloadMapBean;

public class DownloadSucceedState extends BaseDownloadMapPkgState {

    @Override
    protected void onStart(String paramsStr, DownloadMapBean downloadMapBean) {

    }

    @Override
    public boolean onCmdResponse(int cmdId, String command, String result, String extraData) {
        return false;
    }

    @Override
    protected DownloadMapStateEnum getNextState() {
        return null;
    }
}
