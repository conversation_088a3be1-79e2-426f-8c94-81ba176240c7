package com.ainirobot.coreservice.bean;

public class LoadMapBean {
    private boolean useCustomKeepPose;
    private boolean keepPose;

    public LoadMapBean(boolean useCustomKeepPose, boolean keepPose) {
        this.useCustomKeepPose = useCustomKeepPose;
        this.keepPose = keepPose;
    }

    public boolean isUseCustomKeepPose() {
        return useCustomKeepPose;
    }

    public boolean isKeepPose() {
        return keepPose;
    }
}
