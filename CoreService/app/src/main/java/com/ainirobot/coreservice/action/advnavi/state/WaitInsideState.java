package com.ainirobot.coreservice.action.advnavi.state;

import android.util.Log;

import com.ainirobot.coreservice.action.StopCommandList;
import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.action.advnavi.elevatorcmd.ElevatorControlUtil;
import com.ainirobot.coreservice.action.advnavi.elevatorcmd.ElevatorControlUtilListener;
import com.ainirobot.coreservice.client.Definition;

import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.TimeUnit;

/**
 * Data: 2022/8/9 11:07
 * Author: wanglijing
 * Description: WaitInsideState
 * 在电梯内等待状态
 */
public class WaitInsideState extends BaseState {
    //电梯内等待超时
    private final long WAIT_TIMEOUT_MINUTE = TimeUnit.MINUTES.toMillis(5);
    private ElevatorControlUtil controlUtil;

    private volatile Timer mTimer;
    private long startTime;

    private Status status;

    private enum Status {
        IDLE,
        REGISTER_ELEVATOR,  //注册电梯
        REPORT_ENTER_ELEVATOR_COMPLETE,  //机器人进入电梯成功，状态上报
        CALL_ELEVATOR,  //正在呼叫电梯，还未收到电梯回复消息
        WAIT_ELEVATOR,  //呼梯成功，在等待到达
        CALL_FAIL,      //呼梯失败
        ARRIVED         //电梯到达
    }

    WaitInsideState(String name) {
        super(name);
    }


    @Override
    protected void preAction(StateData data, IStateListener listener) {
        super.preAction(data, listener);
        controlUtil = new ElevatorControlUtil(this,
                mStateData.getTargetFloorIndex(),
                true,
                elevatorControlUtilListener);
        updateCurrentStatus(Status.IDLE);
    }

    @Override
    protected void doAction() {
        super.doAction();

        /**
         * 注意：这里的注册电梯接口调用，是处理在电梯内部出现异常情况，重新注册电梯。
         * 比如，电梯内部出现通信异常，导致电梯控制层无法控制电梯，这时候需要重新注册电梯。
         * 如果梯控协议不希望重复注册电梯，可以在梯控层做判断，如果已经注册过电梯，不再重复注册。
         *
         * 从梯控流程灵活性和健壮性考虑，这里统一在这里再注册电梯。
         */
        registerElevatorCmd();//首次注册电梯
        startWaitTimer();//首次注册电梯后，开始超时等待
    }

    @Override
    protected void exit() {
        cancelWaitTimer();
        cancelCallElevator();
        if (null != controlUtil) {
            //这里不释放电梯，后面要控制电梯门
            controlUtil.stopControlElevator();
        }
        super.exit();
    }

    @Override
    protected List<StopCommandList.StopCommand> getStopCommands() {
        return new ArrayList<StopCommandList.StopCommand>() {{
            add(new StopCommandList.StopCommand(Definition.CMD_CONTROL_RELEASE_ELEVATOR, null));
        }};
    }

    @Override
    protected BaseState getNextState() {
        if (actionState == Definition.ElevatorAction.ACTION_ELEVATOR_ARRIVED) {
            return StateEnum.EXIT_ELEVATOR.getState();
        }
        return super.getNextState();
    }

    @Override
    protected boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        if (null != controlUtil
                && controlUtil.cmdResponse(command, result, extraData)) {
            return true;
        }
        switch (command) {
            case Definition.CMD_CONTROL_REGISTER_ELEVATOR:
                handleRegisterElevatorResult(result, extraData);
                return true;
            case Definition.CMD_CONTROL_ROBOT_STATE_UPDATE:
                handleRobotStatusUpdateResult(result, extraData);
                return true;
        }
        return super.cmdResponse(cmdId, command, result, extraData);
    }

    @Override
    protected boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        if (null != controlUtil
                && controlUtil.cmdResponse(command, status, extraData)) {
            return true;
        }
        return super.cmdStatusUpdate(cmdId, command, status, extraData);
    }

    /**
     * 调用注册电梯接口
     * 这里的call一定是在电梯内，注册成功后，上报机器人进入电梯，然后开始呼梯
     */
    private void registerElevatorCmd() {
        Log.d(TAG, "registerElevatorCmd");
        updateCurrentStatus(WaitInsideState.Status.REGISTER_ELEVATOR);
        super.registerElevator();//1分钟重试一次，最多重试5次
    }

    /**
     * 处理注册电梯结果
     */
    private void handleRegisterElevatorResult(String result, String extraData) {
        Log.d(TAG, "handleRegisterElevatorResult " + result + " extraData:" + extraData);
        if (Definition.SUCCEED.equals(result)) {
            reportRobotEnterElevatorComplete();//注册成功后，上报机器人进入电梯，然后开始呼梯
            startWaitTimer();//首次上报机器人进入电梯后，开始超时等待
        }
    }

    /**
     * 调用上报机器人状态接口，进入电梯成功
     */
    public void reportRobotEnterElevatorComplete() {
        Log.d(TAG, "reportRobotEnterElevatorComplete");
        updateCurrentStatus(WaitInsideState.Status.REPORT_ENTER_ELEVATOR_COMPLETE);
        super.updateRobotElevatorStatus(Definition.RobotElevatorState.ENTER_ELEVATOR_COMPLETE);//内部呼叫电梯之前，上报机器人进入电梯
    }

    /**
     * 处理上报机器人进入电梯成功结果
     */
    private void handleRobotStatusUpdateResult(String result, String extraData) {
        Log.d(TAG, "handleRobotStatusUpdateResult " + result + " extraData:" + extraData + " status:" + status);
        if (Definition.SUCCEED.equals(result)) {
            callElevator();//上报机器人进入电梯后，开始呼梯
            startWaitTimer();//首次呼梯后，开始超时等待
        }
    }

    /**
     * 调用呼梯接口
     */
    private void callElevator() {
        Log.d(TAG, "callElevator");
        updateCurrentStatus(WaitInsideState.Status.CALL_ELEVATOR);
        if (null != controlUtil) {
            controlUtil.startCallElevator();
        }
    }

    /**
     * 电梯到达，更新事件：elevator_arrived
     */
    private void elevatorArrived() {
        updateCurrentStatus(Status.ARRIVED);
        updateAction(Definition.ElevatorAction.ACTION_ELEVATOR_ARRIVED);
        onStatusUpdate(Definition.STATUS_ELEVATOR_ARRIVED_TARGET_FLOOR, "arrived target floor");
        onSuccess();
    }

    // ---------    超时    ----------- //

    /**
     * 呼梯超时处理
     * <p>
     * 注册电梯超时处理：
     * 每隔一分钟重新注册一次，直到注册成功
     * <p>
     * 呼叫电梯超出处理：
     * 1.每1分钟重新调用一次呼梯接口(去掉此逻辑，通信问题由Elevator层保障，这里不重发呼梯指令)
     * 2.每5分钟判断一次当前状态，如果是呼梯成功（指令应答），但电梯一直未到达，则重置等待时间，无限循环
     * 3.如果呼梯失败（指令未应答），直接返回失败
     */
    private synchronized void startWaitTimer() {
        cancelWaitTimer();
        startTime = System.currentTimeMillis();
        mTimer = new Timer();
        mTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (!isAlive()) {
                    cancelWaitTimer();
                    return;
                }

                if (status == WaitInsideState.Status.REGISTER_ELEVATOR) {
                    registerElevatorCmd();//注册电梯重试
                    return;
                }

                if (status == WaitInsideState.Status.REGISTER_ELEVATOR) {
                    reportRobotEnterElevatorComplete();//上报机器人进入电梯重试
                    return;
                }

                //呼叫电梯超时处理
                if (status == WaitInsideState.Status.CALL_ELEVATOR) {
                    if (System.currentTimeMillis() - startTime > WAIT_TIMEOUT_MINUTE) {
                        Log.i(TAG, "wait time out! ");
                        cancelWaitTimer();
                        waitCallElevatorTimeOut();
                    } else {
                        callElevator();//startWaitTimer, 1分钟一次
                    }
                }
            }
        }, 60 * 1000, 60 * 1000);
    }

    private synchronized void cancelWaitTimer() {
        if (mTimer != null) {
            mTimer.cancel();
            mTimer = null;
        }
    }

    /**
     * 超时处理
     */
    private void waitCallElevatorTimeOut() {
        if (status == Status.WAIT_ELEVATOR) {
            //通知用户：呼梯成功但电梯一直未到达；
            onStatusUpdate(Definition.STATUS_UPDATE_RETRY_ERROR,
                    Definition.STATUS_CALL_SUC_ELEVATOR_NOT_ARRIVED);
            startWaitTimer(); //超时后，重新设置超时时间继续等待，直到电梯到达
            return;
        }
        onResult(Definition.ERROR_CALL_ELEVATOR_FAILED, Definition.PARAMS_TIMEOUT);
    }

    private void updateCurrentStatus(Status status) {
        Log.d(TAG, "updateCurrentStatus " + this.status + " --> " + status);
        this.status = status;
    }

    /**
     * 控制电梯开门时状态监听
     */
    private final ElevatorControlUtilListener elevatorControlUtilListener = new ElevatorControlUtilListener() {
        @Override
        public void onElevatorStatusUpdate(int id, String param, String extraData) {
            if (id == Definition.STATUS_ELEVATOR_FLOOR_INFO) {
                updateCurrentStatus(Status.WAIT_ELEVATOR);
            }
            onStatusUpdate(id, param, extraData);
        }

        @Override
        public void onElevatorResult(int id, String param) {
            onElevatorCmdResult(id, param);
        }

        @Override
        public void onSuccess() {
            elevatorArrived();
        }
    };

    private void onElevatorCmdResult(int id, String param) {
        Log.d(TAG, "onElevatorCmdResult status:" + id + " , data:" + param);
        switch (id) {
            case Definition.ERROR_ELEVATOR_CONTROL:
            case Definition.ERROR_CALL_ELEVATOR_FAILED:
            case Definition.ERROR_CLOSE_ELEVATOR_DOOR_FAILED:
            case Definition.ERROR_OPEN_ELEVATOR_DOOR_FAILED:
                onResult(Definition.ERROR_CALL_ELEVATOR_FAILED, param);
                break;
            default:
                onResult(id, param);
                break;
        }
    }
}
