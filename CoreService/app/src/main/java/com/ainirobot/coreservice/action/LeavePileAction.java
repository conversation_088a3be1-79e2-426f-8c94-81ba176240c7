package com.ainirobot.coreservice.action;

import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.actionbean.LeavePileBean;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.service.CoreService;
import com.ainirobot.coreservice.utils.DelayTimer;
import com.ainirobot.robotlog.RobotLog;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Timer;

/**
 * 离桩
 * 1.判断雷达是否开启，先启动雷达
 * 2.基础运动向前走
 */
public class LeavePileAction extends AbstractAction<LeavePileBean> {

    private static final String TAG = "LeavePileAction";

    private CoreService mCore;
    private LeavePileState mLeavePileState = LeavePileState.IDLE;
    private float mSpeed;
    private float mDistance;
    private DelayTimer mDelayTimer;
    private DelayTimer mForwardTimer;
    private static final long LEAVE_PILE_TIMEOUT = 15 * 1000;
    private String mChargingType = Definition.CHARGING_TYPE_PILE;

    private enum LeavePileState {
        IDLE, START_LEAVE_PILE
    }

    protected LeavePileAction(ActionManager manager, int id, String[] resList) {
        super(manager, id, resList);
        mCore = CoreService.mCore;
        mSpeed = 0.7f;
        mDistance = 0.2f;
    }

    @Override
    protected boolean startAction(LeavePileBean parameter, LocalApi api) {
        if (mLeavePileState != LeavePileState.IDLE) {
            return false;
        }

        checkChargingType();
        if (mChargingType.equals(Definition.CHARGING_TYPE_WIRE)) {
            Log.d(TAG, "startAction: wire-charging type");
            onResult(Definition.STATUS_LEAVE_PILE_FAILURE_WIRE_CHARGING, "wire-charging type");
            return true;
        }
        mLeavePileState = LeavePileState.START_LEAVE_PILE;
        mApi.queryRadarStatus(mReqId);
        startLeavePileTimer();
        return true;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        try {
            mCore.getSystemServer().enableRadar();
            onStatusUpdate(Definition.STATUS_LEAVE_PILE_ENABLE_RADAR, "enable radar status");
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        cancelMoveDirTimer();
        cancelForwardTimer();
        mLeavePileState = LeavePileState.IDLE;
        return true;
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return null;
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return null;
    }

    @Override
    protected boolean verifyParam(LeavePileBean param) {
        Log.d(TAG, "verifyParam = " + param);
        if (param != null) {
            if (param.getSpeed() != 0) {
                mSpeed = param.getSpeed();
            }
            if (param.getDistance() != 0) {
                mDistance = param.getDistance();
            }
            if (ProductInfo.isCarryProduct()) {
                int robotStructureMode = RobotSettingApi.getInstance()
                        .getRobotInt("robot_setting_navi_robot_structure_mode");
                float factoryRobotRadiusF = RobotSettingApi.getInstance()
                        .getRobotFloat("robot_setting_navi_factory_robot_radius");
                if (robotStructureMode == 1) {
                    mDistance += 0.17f;
                } else if (robotStructureMode == 2) {
                    mDistance += (factoryRobotRadiusF - 0.4f > 0 ? factoryRobotRadiusF - 0.4f + 0.1f : 0.1f);
                }
            }
        }
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        Log.d("LeavePileAction", "command=" + command + ",result=" + result);
        switch (command) {
            case Definition.CMD_NAVI_QUERY_RADAR_STATUS:
                try {
                    JSONObject jsonObject = new JSONObject(result);
                    boolean isRadarOpened = jsonObject.optBoolean(Definition.JSON_NAVI_OPEN_RADAR);
                    Log.d(TAG, "query isRadarOpened = " + isRadarOpened);
                    if (!isRadarOpened) {
                        mCore.getSystemServer().disableRadar();
                        onStatusUpdate(Definition.STATUS_LEAVE_PILE_DISABLE_RADAR, "disable radar status");
                        mApi.updateRadarStatus(mReqId, true);
                    } else {
                        onStatusUpdate(Definition.STATUS_LEAVE_PILE_OPEN_RADAR_SUCCESS, "radar open success");
                        startLeavePile();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                break;
            case Definition.CMD_NAVI_SET_RADAR_STATUS:
                if (Definition.SUCCEED.equals(result)) {
                    onStatusUpdate(Definition.STATUS_LEAVE_PILE_OPEN_RADAR_SUCCESS, "radar open success");
                    startLeavePile();
                } else {
                    onResult(Definition.STATUS_LEAVE_PILE_OPEN_RADAR_FAILURE, "radar open failure");
                    mLeavePileState = LeavePileState.IDLE;
                    break;
                }
                try {
                    mCore.getSystemServer().enableRadar();
                    onStatusUpdate(Definition.STATUS_LEAVE_PILE_ENABLE_RADAR, "enable radar status");
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
                break;
            case Definition.CMD_NAVI_MOVE_DIRECTION:
                mLeavePileState = LeavePileState.IDLE;
                cancelMoveDirTimer();
                boolean isCharging = mCore.getSystemManager().isCharging();
                Log.d(TAG, "isCharging=" + isCharging);
                //离桩成功
                if (Definition.SUCCEED.equals(result)) {
                    onResult(Definition.RESULT_OK, result);
                } else {
                    if (!isCharging) {
                        onResult(Definition.RESULT_OK, result);
                    } else {
                        //前方有障碍，离桩失败
                        Log.d(TAG, "leave failure, obstacles ahead");
                        onResult(Definition.RESULT_FAILURE_MOTION_AVOID_STOP, Definition.ERROR_MOTION_AVOID_STOP);
                    }
                }
                break;
        }
        return true;
    }

    private void startLeavePileTimer() {
        if (mDelayTimer == null) {
            mDelayTimer = new DelayTimer(LEAVE_PILE_TIMEOUT, new Runnable() {
                @Override
                public void run() {
                    onResult(Definition.RESULT_FAILURE_TIMEOUT, "leave pile timeout");
                }
            });
            mDelayTimer.start();
        }
    }

    private synchronized void cancelMoveDirTimer() {
        if (mDelayTimer != null) {
            mDelayTimer.destroy();
            mDelayTimer = null;
        }
    }

    private void startLeavePile() {
        if (mForwardTimer == null){
            mForwardTimer = new DelayTimer(ProductInfo.isSaiphXdOrBigScreen() ? 10000 : 100, new Runnable() {
                @Override
                public void run() {
                    onStatusUpdate(Definition.STATUS_LEAVE_PILE_GO_FORWARD_START, "start to goForward/goForwardAvoid");
                    if (ProductInfo.isSaiphXdOrBigScreen()){
                        Log.d(TAG, "XD goForward");
                        mApi.goForward(mReqId, mSpeed, mDistance);
                    }else {
                        mApi.goForward(mReqId, 0.1f, mDistance);
                    }
                }
            });
            mForwardTimer.start();
        }

    }

    private void cancelForwardTimer(){
        if (mForwardTimer != null) {
            mForwardTimer.destroy();
            mForwardTimer = null;
        }
    }

    private void checkChargingType() {
        String chargingType = mManager.getRobotSettingManager().
                getRobotSetting(Definition.ROBOT_SETTINGS_CHARGING_TYPE);
        RobotLog.d(TAG, "checkChargingType: chargingType=" + chargingType);
        if (!TextUtils.isEmpty(chargingType)) {
            this.mChargingType = chargingType;
        }
    }

}
