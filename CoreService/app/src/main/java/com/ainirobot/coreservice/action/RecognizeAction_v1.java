/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.coreservice.action;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.RecognizeBean;
import com.ainirobot.coreservice.client.person.FaceBean;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.utils.VisionUtils;
import com.google.gson.Gson;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class RecognizeAction_v1 extends AbstractAction<RecognizeBean> {

    private static final String TAG = RecognizeAction_v1.class.getSimpleName();
    private static final int PICTURE_NUMBER = 1;

    private enum State {
        IDLE, GET_PICTURE, VERITY_FACE
    }

    private int mReqId = 0;
    private int mPersonId;
    private String mPicPath;
    private State mState;

    RecognizeAction_v1(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_RECOGNIZE_v1, resList);
    }

    @Override
    protected boolean startAction(RecognizeBean parameter, LocalApi api) {
        mPersonId = parameter.getPersonId();
        String picPath = parameter.getPicturePath();
        Log.d(TAG, "startAction personId: " + mPersonId + ", picPath: " + picPath);
        if (TextUtils.isEmpty(picPath)) {
            updateState(State.GET_PICTURE);
            mApi.getPictureById(mReqId, mPersonId, PICTURE_NUMBER);
        } else {
            updateState(State.VERITY_FACE);
            mApi.verifyFace(mReqId, picPath);
        }
        return true;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        VisionUtils.deletePicture(mPicPath);
        mState = State.IDLE;
        return true;
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return null;
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return null;
    }

    @Override
    protected boolean verifyParam(RecognizeBean param) {
        return param != null;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        Log.d(TAG, "cmdResponse cmdId: " + cmdId + ", command: " + command
                + ", result: " + result + ", isRunning: " + isRunning());
        if (!isRunning()) {
            return false;
        }
        switch (command) {
            case Definition.CMD_HEAD_GET_PICTURE_BY_ID:
                handleGetPictureById(result);
                return true;

            case Definition.CMD_REMOTE_VERIFY_FACE:
                handleVerifyFace(result);
                return true;
        }
        return false;
    }

    private void handleGetPictureById(String result) {
        Log.d(TAG, "handleGetPictureById result: " + result + ", state: " + mState);
        if (mState != State.GET_PICTURE) {
            return;
        }
        String status = null;
        JSONArray pictures = null;
        int personId = -1;
        try {
            JSONObject json = new JSONObject(result == null ? "" :
                    result);
            status = json.optString("status");
            pictures = json.optJSONArray("pictures");
            personId = json.optInt("id", -1);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        Log.d(TAG, "handleGetPictureById status: " + status + ", id: " + personId
                + ", pictures: " + pictures);
        if (mPersonId != personId || pictures == null || pictures.length() <= 0
                || TextUtils.isEmpty(pictures.optString(0))) {
            onError(Definition.RESULT_FAILURE, "get picture failed");
        } else {
            updateState(State.VERITY_FACE);
            mPicPath = pictures.optString(0);
            mApi.verifyFace(mReqId, mPicPath);
        }
    }

    private void handleVerifyFace(String result) {
        Log.d(TAG, "handleVerifyFace result: " + result + ", state: " + mState);
        if (mState != State.VERITY_FACE) {
            return;
        }
        VisionUtils.deletePicture(mPicPath);
        int code = -1;
        List<FaceBean> faceBeanList = new ArrayList<>();
        try {
            JSONObject json = new JSONObject(result == null ? "" :
                    result);
            code = json.optInt("code", -1);
            JSONObject data = json.optJSONObject("data");
            if (data != null) {
                JSONArray matchList = data.optJSONArray("match_list");
                if (matchList != null && matchList.length() > 0) {
                    for (int i = 0; i < matchList.length(); i++) {
                        JSONObject faceData = matchList.optJSONObject(i);
                        if (faceData == null) {
                            continue;
                        }
                        faceBeanList.add(new FaceBean(faceData));
                    }
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        Log.d(TAG, "handleVerifyFace code: " + code + ", size: " + faceBeanList.size());
        if (faceBeanList.size() > 0) {
            Gson gson = new Gson();
            onResult(Definition.RESULT_SUCCEED, gson.toJson(faceBeanList));
        } else {
            onError(Definition.RESULT_FAILURE, "remote detect failed");
        }
    }

    private void updateState(State newState) {
        Log.d(TAG, "updateState: newState: " + newState + ", state: " + mState);
        mState = newState;
    }
}
