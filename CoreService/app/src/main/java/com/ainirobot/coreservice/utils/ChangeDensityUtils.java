package com.ainirobot.coreservice.utils;

import android.util.Log;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

public class ChangeDensityUtils {
    private static final String TAG = "ChangeDensityUtils";

    public static void onForegroundAppChange(String foregroundPackageName) {
        Log.d(TAG, "onForegroundAppChange foregroundPackageName = " + foregroundPackageName);
        if (foregroundPackageName.startsWith("com.ainirobot")) {
            changeScreenDensity(560);
        } else {
            int density_value = SettingDataHelper.getInstance().getRobotInt(SettingDataHelper.ROBOT_SETTING_SCREEN_DENSITY);
            Log.d(TAG, "onForegroundAppChange density_value = " + density_value);
            changeScreenDensity(density_value);
        }
    }

    public static void changeScreenDensity(int density) {
        try {
            String command = "wm density " + density;
            Log.d(TAG, "changeScreenDensity command=" + command);
            Process process = Runtime.getRuntime().exec(command);

            // 读取命令执行的输出
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                Log.d(TAG, "changeScreenDensity outputlLine=" + line);
            }

            // 等待命令执行完成
            int exitCode = process.waitFor();
            Log.d(TAG, "changeScreenDensity finish exitCode=" + exitCode);

        } catch (IOException e) {
            e.printStackTrace();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
}
