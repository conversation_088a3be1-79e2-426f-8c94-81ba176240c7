package com.ainirobot.coreservice.client.actionbean;

/**
 * Created by orion on 2018/4/12.
 */

public class WakeUpBean extends BaseBean {
    private float angle;
    private int vHeadAngle; // Head vertical angle
    private int bodySpeed; //Body rotation speed, Degree / s
    private boolean isNeedMoveHead;

    public boolean isNeedMoveHead() {
        return isNeedMoveHead;
    }

    public void setNeedMoveHead(boolean needMoveHead) {
        isNeedMoveHead = needMoveHead;
    }

    public float getAngle(){
        return angle;
    }
    public void setAngle(float angle){
        this.angle = angle;
    }

    public int getvHeadAngle() {
        return vHeadAngle;
    }

    public void setVHeadAngle(int vHeadAngle) {
        this.vHeadAngle = vHeadAngle;
    }

    public int getBodySpeed() {
        return bodySpeed;
    }

    public void setBodySpeed(int bodySpeed) {
        this.bodySpeed = bodySpeed;
    }
}
