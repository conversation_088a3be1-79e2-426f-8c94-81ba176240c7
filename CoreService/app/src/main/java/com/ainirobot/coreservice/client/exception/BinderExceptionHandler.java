/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.client.exception;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;

public class BinderExceptionHandler {

    private static Handler handler;

    static {
        handler = new Handler(Looper.getMainLooper()) {
            @Override
            public void handleMessage(Message msg) {
                onException(msg.obj);
            }
        };
    }

    private static void onException(Object object) {
        Exception e = (Exception) object;
        if (e instanceof RuntimeException) {
            throw (RuntimeException) e;
        } else {
            e.printStackTrace();
        }
    }

    public static void handle(Exception e) {
        if (handler == null) {
            throw new RuntimeException("BinderExceptionHandler not initialized");
        }

        Message message = new Message();
        message.obj = e;
        handler.sendMessage(message);
    }

}
