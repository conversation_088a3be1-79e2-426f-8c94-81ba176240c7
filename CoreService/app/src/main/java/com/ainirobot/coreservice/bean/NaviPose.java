package com.ainirobot.coreservice.bean;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 对应 NavigationService 中本地存储和读取的 Pose 属性值
 */
public class NaviPose implements Parcelable, Cloneable {

    /**
     * name : 接待点
     * postype : 0
     * theta : 1.1290649
     * x : -0.01416623
     * y : 0.15276624
     * messageType : 11
     * time : 1542622629085
     */

    private String name;
    private int postype;
    private double theta;
    private double x;
    private double y;
    private int messageType;
    private long time;

    public NaviPose() {
    }

    public NaviPose(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getPostype() {
        return postype;
    }

    public void setPostype(int postype) {
        this.postype = postype;
    }

    public double getTheta() {
        return theta;
    }

    public void setTheta(double theta) {
        this.theta = theta;
    }

    public double getX() {
        return x;
    }

    public void setX(double x) {
        this.x = x;
    }

    public double getY() {
        return y;
    }

    public void setY(double y) {
        this.y = y;
    }

    public int getMessageType() {
        return messageType;
    }

    public void setMessageType(int messageType) {
        this.messageType = messageType;
    }

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }

    @Override
    public String toString() {
        return "NaviPose{" +
                "name='" + name + '\'' +
                ", postype=" + postype +
                ", theta=" + theta +
                ", x=" + x +
                ", y=" + y +
                ", messageType=" + messageType +
                ", time=" + time +
                '}';
    }

    @Override
    protected Object clone() throws CloneNotSupportedException {
        return super.clone();
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof NaviPose)) {
            return false;
        }
        String otherName = ((NaviPose) obj).name;
        return name != null && otherName != null && name.equals(otherName);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.name);
        dest.writeInt(this.postype);
        dest.writeDouble(this.theta);
        dest.writeDouble(this.x);
        dest.writeDouble(this.y);
        dest.writeInt(this.messageType);
        dest.writeLong(this.time);
    }

    private NaviPose(Parcel in) {
        this.name = in.readString();
        this.postype = in.readInt();
        this.theta = in.readDouble();
        this.x = in.readDouble();
        this.y = in.readDouble();
        this.messageType = in.readInt();
        this.time = in.readLong();
    }

    public static final Creator<NaviPose> CREATOR = new Creator<NaviPose>() {
        @Override
        public NaviPose createFromParcel(Parcel source) {
            return new NaviPose(source);
        }

        @Override
        public NaviPose[] newArray(int size) {
            return new NaviPose[size];
        }
    };
}
