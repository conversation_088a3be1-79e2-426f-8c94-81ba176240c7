package com.ainirobot.coreservice.action.advnavi.state;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.action.advnavi.elevatorcmd.ElevatorControlUtil;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.MultiRobotStatus;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.utils.DelayTask;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Data: 2022/8/9 11:06
 * Author: wanglijing
 * Description: SwitchMapState
 */
public class SwitchMapState extends BaseNavigationState {

    private String switchMapName;
    /**
     * 切图失败次数
     */
    private int switchMapFailCount = 0;
    /**
     * 切图最大失败此次
     */
    private final int MAX_RETRY_SWITCH_MAP_COUNT = 5;
    /**
     * 重新加载地图次数
     */
    private int loadMapCount = 0;
    /**
     * 重新加载地图最大次数
     */
    private final int MAX_RETRY_LOAD_MAP_COUNT = 5;
    /**
     * 重定位次数
     */
    private int relocationCount = 0;
    /**
     * 重定位最大次数
     */
    private final int MAX_RELOCATION_COUNT = 6;
    /**
     * 检查新地图位姿timer循环次数
     */
    private int mCheckNewPoseCount = 0;
    private ElevatorControlUtil controlUtil;


    SwitchMapState(String name) {
        super(name);
    }

    @Override
    protected void preAction(StateData data, IStateListener listener) {
        super.preAction(data, listener);
        loadMapCount = 0;
        relocationCount = 0;
        switchMapFailCount = 0;
        switchMapName = mStateData.getTargetFloorMapName();
        controlUtil = new ElevatorControlUtil(this,
                mStateData.getTargetFloorIndex(),
                true,
                null);
    }


    @Override
    protected void doAction() {
        super.doAction();
        switchMap();
        if (null != controlUtil) {
            controlUtil.startCallElevator();
        }
    }

    @Override
    protected void exit() {
        DelayTask.cancel(TAG);
        super.exit();
    }

    @Override
    protected BaseState getNextState() {
        if (actionState == Definition.ElevatorAction.ACTION_SWITCH_MAP_SUC) {
            return StateEnum.WAIT_INSIDE_ELEVATOR.getState();
        }
        return super.getNextState();
    }

    @Override
    protected boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_SWITCH_MAP:
                parseSwitchMapResult(result);
                break;
            case Definition.CMD_NAVI_SET_FORCE_ESTIMATE:
            case Definition.CMD_NAVI_SET_FIXED_ESTIMATE:
                parseEstimateResult(result);
                break;
        }
        return super.cmdResponse(cmdId, command, result, extraData);
    }

    private void switchMap() {
        if (needSwitchMap()) {
            onStatusUpdate(Definition.STATUS_UPDATE_SWITCH_MAP_START,
                    "start switch map to " + switchMapName);
            startSwitchMap();
        } else {
            Log.d(TAG, "no need switch map");
            checkEstimate();
        }
    }

    /**
     * 判断是否有定位
     */
    private void checkEstimate() {
        if (TextUtils.equals(getRobotInfo(Definition.SYNC_ACTION_IS_ROBOT_ESTIMATE), Definition.RESULT_TRUE)) {
            switchMapSuc();
        } else {
            Log.d(TAG, "need relocation");
            startRelocation();
        }
    }

    /**
     * 当前地图和目的地地图名不同时，需要切图
     * @return true：需要切图
     */
    private boolean needSwitchMap() {
        return !TextUtils.equals(getRobotInfo(Definition.SYNC_ACTION_GET_MAP_NAME),
                switchMapName);
    }

    private void startSwitchMap() {
        mApi.switchMap(mReqId, switchMapName);
    }

    private void parseSwitchMapResult(String result) {
        if (!TextUtils.isEmpty(result)
                && result.equals(Definition.SUCCEED)) {
            Log.i(TAG, "switch map success");
            onStatusUpdate(Definition.STATUS_UPDATE_SWITCH_MAP_SUCCESS, switchMapName);
            waitReloadMapInfo();    //切图完成，需要等一段时间加载地图信息
        } else {
            retrySwitchMap();   //retry
        }
    }

    private void retrySwitchMap() {
        switchMapFailCount++;
        if (switchMapFailCount > MAX_RETRY_SWITCH_MAP_COUNT) {
            onResult(Definition.ERROR_SWITCH_MAP_FAILED, switchMapName);
            return;
        }
        Log.d(TAG, "retry switch map to " + switchMapName
                + " , count: " + switchMapFailCount);
        startSwitchMap();
    }

    /**
     * 判断地图是否加载完成
     * 最长等待10s，超时上报错误
     */
    private void waitReloadMapInfo() {
        //等2s获得一次地图是否加载完成
        DelayTask.cancel(TAG);
        DelayTask.submit(TAG, new Runnable() {
            @Override
            public void run() {
                if (!isAlive()) {
                    return;
                }
                if (isLoadingPose()) {
                    Log.d(TAG, "is loading pose, retry load map");
                    retryLoadMap();
                } else {
                    //加载地图信息完成，尝试重定位
                    startRelocation();
                }
            }
        }, TimeUnit.SECONDS.toMillis(2));
    }

    private void retryLoadMap() {
        loadMapCount++;
        if (loadMapCount > MAX_RETRY_LOAD_MAP_COUNT) {
            Log.e(TAG, "retryLoadMap loadMap timeout");
            onResult(Definition.ERROR_SWITCH_MAP_FAILED, switchMapName);
            return;
        }
        Log.d(TAG, "loading map pose ...  retry count: " + loadMapCount);
        waitReloadMapInfo();
    }


    /**
     * 切图完成后，重定位
     */
    private void startRelocation() {
        setHasNewPose(false);
        String estimatePose = getEstimatePoseParam();
        Log.d(TAG, "startRelocation to " + estimatePose);
        if (TextUtils.isEmpty(estimatePose)) {
            //未找到该点
            onResult(Definition.ERROR_NOT_FOUND_ELEVATOR_POSE_NAME, mStateData.getElevatorCenterPoseName());
            return;
        }
        onStatusUpdate(Definition.STATUS_UPDATE_START_RELOCATION,
                "start relocation " + switchMapName);
        //用进电梯时的偏移量，对于电梯中心点位进行矫正，使用校正后的位姿做强制定位
        estimatePose = mGson.toJson(getEnterPose(stringToPose(estimatePose)));
        Log.d(TAG, "startRelocation to,final " + estimatePose);

        //电梯中定位都用force定位；
        Log.d(TAG, "setForceEstimate: " + " , count: " + relocationCount);
        mApi.setForceEstimate(mReqId, estimatePose);
    }

    private Pose stringToPose(String jsonString) {
        Pose pose = null;
        try {
            JSONObject json = new JSONObject(jsonString);

            float x = json.has("x") ? Float.valueOf(json.get("x").toString())
                    : Float.valueOf(json.get("px").toString());
            float y = json.has("y") ? Float.valueOf(json.get("y").toString())
                    : Float.valueOf(json.get("py").toString());
            float theta = Float.parseFloat(json.get("theta").toString());
            pose = new Pose(x, y, theta);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return pose;
    }

    private void parseEstimateResult(String result) {
        if (!TextUtils.isEmpty(result)
                && result.equals(Definition.SUCCEED)) {
            onStatusUpdate(Definition.STATUS_UPDATE_RELOCATION_SUC,
                    "relocation suc");
            //延迟一秒，等新地图pose上报，新图定位成功后会延迟大概50ms才上报新的实时pose
            mCheckNewPoseCount = 0;
            DelayTask.submit(TAG, new Runnable() {
                @Override
                public void run() {
                    if (hasNewPose() || ++mCheckNewPoseCount >= 10) {
                        switchMapSuc();
                    }
                }
            }, 100, 100);
        } else {
            retryRelocation();
        }
    }

    private void retryRelocation() {
        relocationCount++;
        if (relocationCount > MAX_RELOCATION_COUNT) {
            onResult(Definition.ERROR_SET_FORCE_ESTIMATE_FAILED, mStateData.getElevatorCenterPoseName());
            return;
        }
        Log.d(TAG, "retry relocation , count: " + relocationCount);
        startSwitchMap();
    }


    private void switchMapSuc() {
        if(!updateElevatorCenterPose()){
            onResult(Definition.ERROR_GET_TARGET_CENTER_POSE_FAILED, "cant update center pose");
            return;
        }
        updateAction(Definition.ElevatorAction.ACTION_SWITCH_MAP_SUC);
        onSuccess();
    }

    private boolean updateElevatorCenterPose() {
//        Pose pose = getPoseByName(mStateData.getElevatorName() + "-" + Definition.ELEVATOR_CENTER_POSE);
        //TODO 改为按类型获取电梯中心
        Pose pose =  getPoseByTypeId(Definition.ELEVATOR_CENTER_TYPE);

        if (pose == null) {
            return false;
        }
        Log.d(TAG, "updateElevatorCenterPose :" + pose);
        mStateData.setElevatorCenterPose(pose);//切图后更新电梯中心点位
        return true;
    }

    /**
     * 重定位点位信息
     * 目标楼层的电梯中心点
     */
    private String getEstimatePoseParam() {
        return getRobotInfo(Definition.SYNC_ACTION_GET_SPECIAL_LOCATION,
                mStateData.getElevatorCenterPoseName());
    }

    private boolean isLoadingPose() {
        return TextUtils.equals(getRobotInfo(Definition.SYNC_ACTION_IS_LOADING_POSE), Definition.RESULT_TRUE);
    }

    @Override
    protected void onNavigationStatusUpdate(int result, String message, String extraData) {

    }

    @Override
    protected void onNavigationError(int result, String message, String extraData) {

    }

    @Override
    protected void onNavigationResult(int result, String message, String extraData) {

    }

    @Override
    protected void handleMultipleRobotStatus(List<MultiRobotStatus> robotStatusList) {

    }

    @Override
    protected boolean isEnableMultiRobotStatus() {
        return false;
    }
}
