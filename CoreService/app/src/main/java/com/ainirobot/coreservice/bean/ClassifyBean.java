package com.ainirobot.coreservice.bean;


import com.google.gson.annotations.SerializedName;

/**
 * 废弃，不再使用。
 * 数据格式不需要再core中定义，不方便后续维护。
 * 业务层根据数据定义自动解析就可以。
 */
@Deprecated
public class ClassifyBean {

    @SerializedName("label_id")
    private int label_id;
    @SerializedName("label")
    private String label;
    @SerializedName("score")
    private float score = 0f;

    public int getLabel_id() {
        return label_id;
    }

    public void setLabel_id(int label_id) {
        this.label_id = label_id;
    }

    /**
     * label字段的　"none"(表示未识别到) ; "plate" 表示空盘　，"food"表示食物，　＂soup＂表示汤水
     * @return
     */
    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public float getScore() {
        return score;
    }

    public void setScore(float score) {
        this.score = score;
    }

    @Override
    public String toString() {
        return "ClassifyBean{" +
                "label_id=" + label_id +
                ", label='" + label + '\'' +
                ", score=" + score +
                '}';
    }
}
