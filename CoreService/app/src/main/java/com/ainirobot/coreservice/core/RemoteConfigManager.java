/*
 *   Copyright (C) 2017 OrionStar Technology Project
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.ainirobot.coreservice.core;

import android.util.Log;

import com.ainirobot.coreservice.action.ActionManager;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.CommandBean;
import com.ainirobot.coreservice.core.status.StatusSubscriber;
import com.ainirobot.coreservice.service.CoreService;
import com.google.gson.Gson;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class RemoteConfigManager {

    private static final String TAG = RemoteConfigManager.class.getSimpleName();
    private static final String JSON_ITEMS = "items";
    private static final String JSON_CONFIG_NAME = "config_name";
    private static final String JSON_CONFIG_JSON = "config_json";
    private static final String TYPE_PICTURE_REPORT = "module_dz_picture_report";
    private static final String TYPE_UNQUALIFIED_PICTURE = "module_dz_unqualified_picture";

    private CoreService mCore;

    public RemoteConfigManager(CoreService core) {
        mCore = core;
        startStatusListener();
    }

    private void startStatusListener() {
        Log.i(TAG, "start status listener");
        mCore.getStatusManager().registerStatusListener(Definition.STATUS_REMOTE_CONFIG_UPDATE,
                new StatusSubscriber() {
                    @Override
                    public void onStatusUpdate(String type, String data) {
                        handleUpdate(data);
                    }

                    @Override
                    public boolean isRemote() {
                        return false;
                    }

                    @Override
                    public boolean isAlive() {
                        return true;
                    }
                });
    }

    private void handleUpdate(String data) {
        Log.i(TAG, "handle update:" + data);
        try {
            JSONObject jsonObject = new JSONObject(data);
            JSONObject value = jsonObject.getJSONObject(Definition.JSON_VALUE);
            JSONArray items = value.getJSONArray(JSON_ITEMS);
            int length = items.length();
            for (int i = 0; i < length; i++) {
                JSONObject item = items.getJSONObject(i);
                String name = item.getString(JSON_CONFIG_NAME);
                String config = item.getString(JSON_CONFIG_JSON);
                Log.i(TAG, "config name:" + name + ",config:" + config);
                switch (name) {
                    case TYPE_PICTURE_REPORT:
                        JSONObject param = new JSONObject();
                        param.put(Definition.JSON_CONFIG, config);
                        sendConfig(Definition.ACTION_HEAD_PICTURE_REPORT_CONFIG,
                                Definition.CMD_HEAD_PICTURE_REPORT_CONFIG, param.toString());
                        break;
                    case TYPE_UNQUALIFIED_PICTURE:
                        sendConfig(Definition.ACTION_HEAD_UNQUALIFIED_PICTURE_CONFIG,
                                Definition.CMD_HEAD_UNQUALIFIED_PICTURE_CONFIG, config);
                        break;
                    default:
                        break;
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void sendConfig(int action, String command, String param) {
        Log.i(TAG, "sendConfig:" + command + " param:" + param);
        Gson gson = new Gson();
        CommandBean bean = new CommandBean(command, param, false);
        ActionManager actionManager = mCore.getActionManager();
        actionManager.exCmd(action, gson.toJson(bean), null);
    }
}
