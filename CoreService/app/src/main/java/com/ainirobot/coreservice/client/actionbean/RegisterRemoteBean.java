package com.ainirobot.coreservice.client.actionbean;

public class RegisterRemoteBean {

    private int code ;
    private String flag;
    private String message;
    private String name;
    private String remoteId;
    private String user_id;
    private String role;
    private int isStaff = -1;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRemoteId() {
        return remoteId;
    }

    public void setRemoteId(String remoteId) {
        this.remoteId = remoteId;
    }

    public String getUserId() {
        return user_id;
    }

    public void setUserId(String user_id) {
        this.user_id = user_id;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public int getIsStaff() {
        return isStaff;
    }
    // 0 is not staff, 1 is staff
    public void setIsStaff(int isStaff) {
        this.isStaff = isStaff;
    }


    @Override
    public String toString() {
        return "RegisterRemoteBean{" +
                "code=" + code +
                ", flag='" + flag + '\'' +
                ", message='" + message + '\'' +
                ", name='" + name + '\'' +
                ", remoteId='" + remoteId + '\'' +
                ", user_id='" + user_id + '\'' +
                ", role='" + role + '\'' +
                '}';
    }
}
