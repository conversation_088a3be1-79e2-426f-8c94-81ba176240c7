package com.ainirobot.coreservice.client.actionbean;

public class LeavePileBean extends BaseBean {
    private float distance;
    private float speed;

    public float getDistance() {
        return distance;
    }

    public void setDistance(float distance) {
        this.distance = distance;
    }

    public float getSpeed() {
        return speed;
    }

    public void setSpeed(float speed) {
        this.speed = speed;
    }

    @Override
    public String toString() {
        return "LeavePileBean{" +
            "distance=" + distance +
            ", speed=" + speed +
            '}';
    }
}
