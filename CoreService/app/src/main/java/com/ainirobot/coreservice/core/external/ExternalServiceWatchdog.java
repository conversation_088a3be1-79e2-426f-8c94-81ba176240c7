/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.core.external;

import android.os.IBinder;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.utils.DelayTask;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * 外设Service守护
 */
public class ExternalServiceWatchdog {

    private static final String TAG = "ExternalServiceWatchdog";

    /**
     * 间隔时间10s
     */
    private final int INTERVAL = 10 * 1000;

    /**
     * 未启动Service
     */
    private final List<ExternalService> mUnStartServices = new ArrayList<>();

    public ExternalServiceWatchdog() {
        startWatchThread();
    }

    public void add(ExternalService service) {
        synchronized (mUnStartServices) {
            if (!mUnStartServices.contains(service)) {
                mUnStartServices.add(service);
            }
        }
    }

    public void remove(ExternalService service) {
        synchronized (mUnStartServices) {
            mUnStartServices.remove(service);
        }
    }

    private void startWatchThread() {
        DelayTask.submit(new Runnable() {
            @Override
            public void run() {
                synchronized (mUnStartServices) {
                    Iterator<ExternalService> iterator = mUnStartServices.iterator();
                    while (iterator.hasNext()) {
                        ExternalService service = iterator.next();
                        if (service.isConnected()) {
                            if (setDeathRecipient(service)) {
                                iterator.remove();
                                continue;
                            }
                        }
                        service.start();
                    }
                }
            }
        }, INTERVAL, INTERVAL);
    }

    /**
     * 设置死亡监听
     *
     * @param service 需要监听的Service
     * @return
     */
    private boolean setDeathRecipient(ExternalService service) {
        try {
            service.getCallback().asBinder().linkToDeath(new ExternalDeathRecipient(service), 0);
            return true;
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 外设Service死亡监听
     */
    private class ExternalDeathRecipient implements IBinder.DeathRecipient {
        private ExternalService service;

        ExternalDeathRecipient(ExternalService service) {
            this.service = service;
        }

        @Override
        public void binderDied() {
            if (this.service != null) {
                Log.i(TAG, "dead service: "+this.service.getPackageName());
                this.service.start();

                //重新加入未启动Service列表
                synchronized (mUnStartServices) {
                    mUnStartServices.add(this.service);
                }
            }
        }
    }

}
