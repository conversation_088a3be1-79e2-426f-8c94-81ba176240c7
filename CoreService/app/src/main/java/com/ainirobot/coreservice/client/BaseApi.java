/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.client;

/**
 * Client Base API
 *
 * <AUTHOR>
 */

import android.content.Context;

import com.ainirobot.coreservice.IRobotBinderPool;
import com.ainirobot.coreservice.client.messagedispatcher.MessageDispatcher;

import java.util.ArrayList;
import java.util.Vector;

public abstract class BaseApi {
    /**
     * API event listeners
     */
    public Vector<ApiListener> listeners = new Vector<ApiListener>();

    /**
     * Application context
     */
    protected Context ctx;

    /**
     * Is client connected to core service
     */
    public volatile boolean mIsServiceConnected = false;

    protected MessageDispatcher mMessageDispatcher = new MessageDispatcher();
    protected ArrayList<BaseSubApi> mSubApiList = new ArrayList<>();
    protected IRobotBinderPool mRobotBinderPool = null;

    /**
     * Constructor
     */
    public BaseApi(Context ctx) {
        this.ctx = ctx;
    }

    public BaseApi() {
    }

    /**
     * Connect API
     */
    public void connectApi() {
    }

    /**
     * Disconnect API
     */
    public void disconnectApi() {
    }

    /**
     * Add an API event listener
     *
     * @param listener Listener
     */
    public void addApiEventListener(ApiListener listener) {
        listeners.addElement(listener);
    }

    /**
     * Remove an API event listener
     *
     * @param listener Listener
     */
    public boolean removeApiEventListener(ApiListener listener) {
        return listeners.removeElement(listener);
    }

    /**
     * Remove all API event listeners
     */
    public void removeAllApiEventListeners() {
        listeners.removeAllElements();
    }

    /**
     * Notify listeners when API is disabled
     */
    protected void notifyEventApiDisabled() {
        for (int i = 0; i < listeners.size(); i++) {
            ApiListener listener = listeners.elementAt(i);
            listener.handleApiDisabled();
        }
    }

    /**
     * Notify listeners when API is connected to the server
     */
    protected void notifyEventApiConnected() {
        mIsServiceConnected = true;
        for (BaseSubApi subApi : mSubApiList) {
            subApi.onConnect(mRobotBinderPool, ctx);
        }
        for (int i = 0; i < listeners.size(); i++) {
            ApiListener listener = listeners.elementAt(i);
            listener.handleApiConnected();
        }
    }

    /**
     * Notify listeners when API is disconnected from the server
     */
    protected void notifyEventApiDisconnected() {
        mIsServiceConnected = false;
        for (int i = 0; i < listeners.size(); i++) {
            ApiListener listener = listeners.elementAt(i);
            listener.handleApiDisconnected();
        }
        for (BaseSubApi subApi : mSubApiList) {
            subApi.onDisconnect();
        }
    }

    /**
     * Returns true if the service is connected, else returns false
     */
    public boolean isApiConnectedService() {
        return mIsServiceConnected;
    }
}
