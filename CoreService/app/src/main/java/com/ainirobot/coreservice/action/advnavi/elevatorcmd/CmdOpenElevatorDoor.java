package com.ainirobot.coreservice.action.advnavi.elevatorcmd;

import android.util.Log;

import com.ainirobot.coreservice.bean.ElevatorState;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.utils.DelayTask;
import com.google.gson.Gson;

import java.util.concurrent.TimeUnit;

/**
 * Data: 2023/3/29 15:13
 * Author: wanglijing
 * <p>
 * 开电梯门
 * * 成功 延迟开门
 * * 失败 尝试调呼梯重新走呼梯 -> 开门
 * * 超时 重试
 */
public class CmdOpenElevatorDoor extends ElevatorCommand {
    //2秒重试一次开门
    private final long OPEN_DOOR_DELAY = TimeUnit.SECONDS.toMillis(2);

    private CmdCallElevator cmdCallElevator;

    private final Gson mGson;

    CmdOpenElevatorDoor(String name, CmdType intentType, ElevatorCommandInterface commandInterface, ElevatorCommandListener listener) {
        super(name, intentType, commandInterface, listener);
        mGson = new Gson();
        initCmdType(CmdType.OPEN_DOOR);
    }


    @Override
    public void start() {
        super.start();
        openElevatorDoor();
    }

    @Override
    public void cmdResponse(String command, String result, String extraData) {
        //招商项目中，楼层状态返回信息丢失，不进行校验
        //判断是否处于目标楼层，不处于目标楼层直接返回失败，
//        if (isOtherFloor(extraData)) {
//            Log.d(TAG, "Error: not target floor!");
//            /**
//             * 处理ERROR_OPEN_ELEVATOR_DOOR_FAILED错误的状态有电梯内和电梯外，
//             * 点体内外非目标楼层开门都报错，按照开门错误处理后续逻辑符合之前的逻辑
//             */
//            onResult(Definition.ERROR_OPEN_ELEVATOR_DOOR_FAILED, "not target floor");
//            return;
//        }
        super.cmdResponse(command, result, extraData);
        if (null != cmdCallElevator) {
            cmdCallElevator.cmdResponse(command, result, extraData);
        }
    }

    @Override
    public void onSuccess() {
        super.onSuccess();
//        delayOpenDoor();
    }

    @Override
    public void onFail() {
        DelayTask.cancel(OPEN_DOOR_DELAY);  //onFail
        if (null == cmdCallElevator) {
            cmdCallElevator = new CmdCallElevator(TAG, intentType, commandInterface, callCmdListener);
        }
        Log.d(TAG, "open door fail, try call elevator");
        cmdCallElevator.start();
    }

    @Override
    protected void onRetry() {
        openElevatorDoor();
    }

    @Override
    public void stop() {
        super.stop();
        DelayTask.cancel(OPEN_DOOR_DELAY);  //onStop
        if (null != cmdCallElevator && cmdCallElevator.isAlive) {
            cmdCallElevator.stop();
        }
    }

    private final ElevatorCommandListener callCmdListener = new ElevatorCommandListener() {
        @Override
        public void onStatusUpdate(int status, String data, String extraData) {
            onCmdStatusUpdate(status, data, extraData);
        }

        @Override
        public void onFinish(int status, String data) {
            if (status == Definition.RESULT_OK) {
                Log.d(TAG, "call elevator suc, open door");
                openElevatorDoor();
                return;
            }
            onResult(status, data);
        }
    };

    private void delayOpenDoor() {
        DelayTask.cancel(OPEN_DOOR_DELAY);
        DelayTask.submit(OPEN_DOOR_DELAY, new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "delay open door");
                start();
            }
        }, OPEN_DOOR_DELAY);
    }

    private boolean isOtherFloor(String extraData) {
        try {
            ElevatorState state = mGson.fromJson(extraData, ElevatorState.class);
            //和当前呼梯的楼层映射不同了，说明电梯已经去其他楼层了
            return state.getFloor() != getFloorIndex();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}
