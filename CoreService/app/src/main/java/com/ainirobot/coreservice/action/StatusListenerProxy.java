/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.action;

import com.ainirobot.coreservice.core.status.StatusSubscriber;

class StatusListenerProxy implements StatusSubscriber {
    private StatusListener mListener;
    private String mId;
    private String mKey;

    StatusListenerProxy(String type, StatusListener listener){
        mListener = listener;
        mKey = genKey(type, listener);
    }

    @Override
    public void onStatusUpdate(String type, String data) {
        mListener.onStatusUpdate(data);
    }

    @Override
    public boolean isRemote() {
        return false;
    }

    @Override
    public boolean isAlive() {
        return true;
    }

    public void setId(String id) {
        mId = id;
    }

    public String getId() {
        return mId;
    }

    static String genKey(String type, StatusListener listener){
        return type + listener.hashCode();
    }
}
