package com.ainirobot.coreservice.core;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotOS;

import java.util.HashMap;

public class SocketManager {

    private HashMap<String, String> mStatusMapTable = new HashMap<String, String>() {
        {
            put(Definition.STATUS_MAP, RobotOS.NAVIGATION_SERVICE);
            put(Definition.STATUS_POSE, RobotOS.NAVIGATION_SERVICE);
            put(Definition.STATUS_SPEED, RobotOS.NAVIGATION_SERVICE);
            put(Definition.STATUS_OBSTACLE_INFO, RobotOS.NAVIGATION_SERVICE);
            put(Definition.STATUS_PUSH_EXCEED_DISTANCE, RobotOS.NAVIGATION_SERVICE);
        }
    };

    public String getServiceName(String type) {
        return mStatusMapTable.get(type);
    }
}
