/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.utils;

import android.os.CountDownTimer;
import android.os.HandlerThread;
import android.os.Looper;
import android.util.Log;

import java.util.Timer;
import java.util.TimerTask;

public class DelayTimer extends HandlerThread {
    private final String TAG = "DelayTimer";
    private CountDownTimer mTimer;
    private final long mDelayTime;
    private Runnable mTask;

    public DelayTimer(long delayTime, Runnable task) {
        super("DelayTimer");
        mDelayTime = delayTime;
        mTask = task;
    }

    public DelayTimer(long delayTime) {
        this(delayTime, null);
    }

    public void setTask(Runnable task) {
        mTask = task;
    }

    @Override
    protected final void onLooperPrepared() {
        mTimer = new CountDownTimer(mDelayTime, mDelayTime) {
            @Override
            public void onTick(long millisUntilFinished) {
            }

            @Override
            public void onFinish() {
                if (mTask != null) {
                    mTask.run();
                }
            }
        };
        mTimer.start();
    }

    public void reset() {
        if (mTimer == null) {
            return;
        }

        synchronized (mTimer) {
            Log.d(TAG, "reset");
            mTimer.cancel();
            mTimer.start();
        }
    }

    public void cancel() {
        if (mTimer == null) {
            return;
        }

        synchronized (mTimer) {
            Log.d(TAG, "cancel");
            mTimer.cancel();
        }
    }

    public void destroy() {
        if (mTimer == null) {
            return;
        }

        synchronized (mTimer) {
            Log.d(TAG, "destroy");
            cancel();
            quit();
        }
    }
}
