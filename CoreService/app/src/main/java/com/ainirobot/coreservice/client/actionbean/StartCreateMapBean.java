package com.ainirobot.coreservice.client.actionbean;


public class StartCreateMapBean extends BaseBean {

    /**
     * 非必传参数，建图是持续状态，结束建图时会携带地图名称
     */
    private String mapName;
    /**
     * 非必传参数
     */
    private String mapLanguage;
    /**
     * 必传参数，视觉类型
     */
    private int visionType;
    /**
     * 必传参数，标识码类型
     */
    private int targetType;

    public String getMapName() {
        return mapName;
    }

    public StartCreateMapBean setMapName(String mapName) {
        this.mapName = mapName;
        return this;
    }

    public String getMapLanguage() {
        return mapLanguage;
    }

    public StartCreateMapBean setMapLanguage(String mapLanguage) {
        this.mapLanguage = mapLanguage;
        return this;
    }

    public int getVisionType() {
        return visionType;
    }

    public StartCreateMapBean setVisionType(int visionType) {
        this.visionType = visionType;
        return this;
    }

    public int getTargetType() {
        return targetType;
    }

    public StartCreateMapBean setTargetType(int targetType) {
        this.targetType = targetType;
        return this;
    }

    @Override
    public String toString() {
        return "StartCreateMapBean{" +
                "mapName='" + mapName + '\'' +
                ", mapLanguage='" + mapLanguage + '\'' +
                ", visionType=" + visionType +
                ", targetType=" + targetType +
                '}';
    }
}
