/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.service;

import android.app.Notification;
import android.app.Service;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.IBinder;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.IDumpRegistry;
import com.ainirobot.coreservice.IFirstConfigRegistry;
import com.ainirobot.coreservice.IModuleRegistry;
import com.ainirobot.coreservice.IOtaRegistry;
import com.ainirobot.coreservice.IRobotBinderPool;
import com.ainirobot.coreservice.IRobotCore;
import com.ainirobot.coreservice.ISystemApi;
import com.ainirobot.coreservice.action.ActionManager;
import com.ainirobot.coreservice.bi.BiReportService;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.actionbean.GongFuBean;
import com.ainirobot.coreservice.client.upload.bi.BiReport;
import com.ainirobot.coreservice.config.ConfigManager;
import com.ainirobot.coreservice.config.ServiceConfig;
import com.ainirobot.coreservice.core.CommandManager;
import com.ainirobot.coreservice.core.CoreStateMachine;
import com.ainirobot.coreservice.core.FaceAngleManager;
import com.ainirobot.coreservice.core.HeadTurnManager;
import com.ainirobot.coreservice.core.InternalDef;
import com.ainirobot.coreservice.core.LanguageManger;
import com.ainirobot.coreservice.core.RemoteConfigManager;
import com.ainirobot.coreservice.core.RequestHandler;
import com.ainirobot.coreservice.core.RequestManager;
import com.ainirobot.coreservice.core.ResponseHandler;
import com.ainirobot.coreservice.core.RobotSettingManager;
import com.ainirobot.coreservice.core.SocketManager;
import com.ainirobot.coreservice.core.account.AccountManager;
import com.ainirobot.coreservice.core.apiproxy.BaseApiProxy;
import com.ainirobot.coreservice.core.external.ExternalService;
import com.ainirobot.coreservice.core.external.ExternalServiceManager;
import com.ainirobot.coreservice.core.module.AppModule;
import com.ainirobot.coreservice.core.module.Module;
import com.ainirobot.coreservice.core.module.ModuleManager;
import com.ainirobot.coreservice.core.module.ModuleManager.Permission;
import com.ainirobot.coreservice.core.permission.PermissionManager;
import com.ainirobot.coreservice.core.person.PersonManager;
import com.ainirobot.coreservice.core.status.StatusManager;
import com.ainirobot.coreservice.core.system.BatteryManager;
import com.ainirobot.coreservice.core.system.CoreRunStateManager;
import com.ainirobot.coreservice.core.system.SystemManager;
import com.ainirobot.coreservice.core.system.TaskManager;
import com.ainirobot.coreservice.daemon.DaemonService;
import com.ainirobot.coreservice.daemon.PerformanceService;
import com.ainirobot.coreservice.data.RobotInfoManager;
import com.ainirobot.coreservice.dump.DataDumpService;
import com.ainirobot.coreservice.resmanage.ResManager;
import com.ainirobot.coreservice.server.DumpServer;
import com.ainirobot.coreservice.server.ExternalServer;
import com.ainirobot.coreservice.server.FirstConfigServer;
import com.ainirobot.coreservice.server.ModuleServer;
import com.ainirobot.coreservice.server.OtaServer;
import com.ainirobot.coreservice.server.PersonServer;
import com.ainirobot.coreservice.server.RobotBinderPoolServer;
import com.ainirobot.coreservice.server.RobotSettingServer;
import com.ainirobot.coreservice.server.ShareMemoryServer;
import com.ainirobot.coreservice.server.SurfaceShareServer;
import com.ainirobot.coreservice.server.SystemServer;
import com.ainirobot.coreservice.surfaceshare.SurfaceShareManager;
import com.ainirobot.coreservice.utils.ArgsIterator;
import com.ainirobot.coreservice.utils.PackageManagerUtils;
import com.ainirobot.coreservice.utils.SettingDataHelper;
import com.google.gson.Gson;

import java.io.FileDescriptor;
import java.io.PrintWriter;
import java.util.List;

public class CoreService extends Service {
    private static final int NOTIFICATION_ID = 1001;

    private static final String TAG = "CoreService";
    private SystemServer mSystemServer;
    private OtaServer mOtaServer;
    private SurfaceShareServer mSurfaceShareServer;
    private RobotBinderPoolServer mRobotBinderPoolServer;

    private CoreRunStateManager mCoreRunStateManager;
    private ExternalServiceManager mExternalServiceManager;
    private RequestManager mRequestManager;
    private CommandManager mCommandManager;
    private ModuleManager mModuleManager;
    private ActionManager mActionManager;
    private SurfaceShareManager mSurfaceShareManager;
    private CoreStateMachine mStateMachine;

    private RequestHandler mRequestHandler;
    private ResponseHandler mResponseHandler;
    private SystemManager mSystemManager;

    private StatusManager mStatusManager;
    private SocketManager mSocketManager;
    private ResManager mResManager;

    private HeadTurnManager mHeadTurnManager;
    private PersonManager mPersonManager;
    private FaceAngleManager mFaceAngleManager;
    private BatteryManager mBatteryManager;

    public static CoreService mCore;
    private FirstConfigServer mFirstConfigServer;

    private RobotInfoManager mRobotInfoManager;
    private RobotSettingManager mRobotSettingManager;
    private RobotSettingServer mRobotSettingServer;
    private ShareMemoryServer mShareMemoryServer;
    private AccountManager mAccountManager;
    private PermissionManager mPermissionManager;
    private LanguageManger mLanguageManger;

    @Override
    public void onCreate() {
        super.onCreate();
        //InternalModule.initModuleMap();
        ConfigManager.loadConfig(getApplication());

        mExternalServiceManager = new ExternalServiceManager(this);
        mRequestManager = new RequestManager();
        mCommandManager = new CommandManager(this);
        mModuleManager = new ModuleManager();
        mCoreRunStateManager = new CoreRunStateManager(this);

        mStateMachine = new CoreStateMachine(this);

        mSystemServer = new SystemServer(this);
        mOtaServer = new OtaServer(this);
        mSurfaceShareServer = new SurfaceShareServer(this);
        mShareMemoryServer = new ShareMemoryServer(this);
        mRobotBinderPoolServer = new RobotBinderPoolServer(this);

        HandlerThread requestThread = new HandlerThread(InternalDef.REQ_THREAD_NAME);
        requestThread.start();
        mRequestHandler = new RequestHandler(requestThread.getLooper(), this);

        HandlerThread responseThread = new HandlerThread(InternalDef.CMD_THREAD_NAME);
        responseThread.start();
        mResponseHandler = new ResponseHandler(mStateMachine, responseThread.getLooper());

        mStatusManager = new StatusManager(this);
        mSocketManager = new SocketManager();
        mResManager = new ResManager(this);
        mFirstConfigServer = new FirstConfigServer(this);
        mActionManager = new ActionManager(this);
        mSurfaceShareManager = SurfaceShareManager.getInstance().init(this);

        BiReport.init();

        mCore = this;

        registerDefaultExternalService();

        mRobotSettingManager = new RobotSettingManager(this);
        mRobotSettingServer = new RobotSettingServer(this);
        mRobotInfoManager = new RobotInfoManager(this);
        mSystemManager = new SystemManager(this);
        mModuleManager.addModule(mSystemManager, Permission.ROOT);
        new RemoteConfigManager(this);

        mHeadTurnManager = new HeadTurnManager(this);
        mBatteryManager = new BatteryManager(this);
        mPersonManager = new PersonManager(this);
        mFaceAngleManager = new FaceAngleManager(this);
        if (ConfigManager.getAccountConfig().isEnable()) {
            mAccountManager = new AccountManager(this);
        }
        mPermissionManager = new PermissionManager(this);
        mLanguageManger = new LanguageManger(this);
        TaskManager.getInstance().init(mStatusManager);
        SettingDataHelper.getInstance().setContext(this);
        
        // Disable conflicting service packages based on OS type
        disableConflictingServicesBasedOnOS();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        int result = super.onStartCommand(intent, flags, startId);
        Log.d(TAG, "onStartCommand result: " + result);
        Notification notification = new Notification();
        startForeground(NOTIFICATION_ID, notification);
        startPerformanceService();
        startBiReportService();
        startDaemonService();
        startDumpService();
        startExternalService();
        return START_NOT_STICKY;
    }

    @Override
    public IBinder onBind(Intent intent) {
        Log.i(TAG, "CoreService onBind" + intent.getAction());
        if (IDumpRegistry.class.getName().equals(intent.getAction())) {
            Log.i(TAG, "dump service request server connected");
            return new DumpServer(mCore, intent.getStringExtra("packageName"));
        } else if (IModuleRegistry.class.getName().equals(intent.getAction())) {
            Log.i(TAG, "Module registry server connected");
            return registerModule("Module", false);
        } else if (IOtaRegistry.class.getName().equals(intent.getAction())) {
            Log.i(TAG, "OTA registry server connected");
            return registerOtaServer(intent.getStringExtra("packageName"));
        } else if (IFirstConfigRegistry.class.getName().equals(intent.getAction())) {
            Log.i(TAG, "FirstConfig registry server connected");
            return mFirstConfigServer;
        } else if (ISystemApi.class.getName().equals(intent.getAction())) {
            Log.i(TAG, "Home server connected");
            return mSystemServer;
        } else if (IRobotCore.class.getName().equals(intent.getAction())) {
            return new ExternalServer(mCore, intent.getAction());
        } else if (Definition.BINDER_FLAG_EXTERNAL.equals(intent.getStringExtra(Definition.FLAG))) {
            Log.i(TAG, "External service connected : " + intent.getAction());
            return new ExternalServer(mCore, intent.getAction());
        } else if (IRobotBinderPool.class.getName().equals(intent.getAction())) {
            Log.i(TAG, "binder pool connected");
            return mRobotBinderPoolServer;
        } else {
            return registerModule(intent.getAction(), false);
        }
    }

    /**
     * 启动守护进程Service
     */
    private void startDaemonService() {
        Log.d(TAG, "Start daemon service");
        Intent intent = new Intent(this, DaemonService.class);
        startService(intent);
    }

    /**
     * 启动性能监控Service
     */
    private void startPerformanceService() {
        Log.d(TAG, "Start performance service");
        Intent intent = new Intent(this, PerformanceService.class);
        startService(intent);
    }

    /**
     * 启动快照Service
     */
    private void startDumpService() {
        Log.d(TAG, "Start dump service");
        Intent intent = new Intent(this, DataDumpService.class);
        startService(intent);
    }

    /**
     * 启动埋点上报Service
     */
    private void startBiReportService() {
        Log.d(TAG, "Start performance service");
        Intent intent = new Intent(this, BiReportService.class);
        startService(intent);
    }

    /**
     * 启动外设Service
     */
    private void startExternalService() {
        List<ExternalService> services = mExternalServiceManager.getExternalServices();

        String systemOSType = RobotSettings.getGlobalSettings(ApplicationWrapper.getContext(), Definition.ROBOT_SETTINGS_SYSTEM_OS_TYPE, "");
        Log.d(TAG, "startExternalService systemOSType=" + systemOSType);
        for (ExternalService service : services) {
            if (service.isEnable()
                    && !service.isConnected()
                    && service.isKeepAlive()) {
                Log.d(TAG, "startExternalService service name111=" + service.getName());
                if (TextUtils.equals(systemOSType, Definition.OSType.AGENTOS.getValue())) {
                    if (TextUtils.equals(service.getPackageName(), Definition.SPEECH_PACKAGE_NAME))
                        continue;
                } else {
                    if (TextUtils.equals(service.getPackageName(), Definition.AGENT_PACKAGE_NAME)) {
                        continue;
                    }
                }
                Log.d(TAG, "startExternalService service name=" + service.getPackageName());
                service.start();
            }
        }
    }
    
    /**
     * Disable conflicting service packages based on OS type
     * - AgentOS: disable speech service, enable agent service
     * - RobotOS: disable agent service, enable speech service
     */
    private void disableConflictingServicesBasedOnOS() {
        String systemOSType = RobotSettings.getGlobalSettings(ApplicationWrapper.getContext(), 
                Definition.ROBOT_SETTINGS_SYSTEM_OS_TYPE, "");
        Log.d(TAG, "disableConflictingServicesBasedOnOS: systemOSType=" + systemOSType);
        
        String packageToDisable = null;
        String packageToEnable = null;
        
        if (TextUtils.equals(systemOSType, Definition.OSType.AGENTOS.getValue())) {
            // AgentOS system - disable speech service, enable agent service
            packageToDisable = Definition.SPEECH_PACKAGE_NAME;
            packageToEnable = Definition.AGENT_PACKAGE_NAME;
            Log.d(TAG, "AgentOS detected, will disable: " + packageToDisable + ", enable: " + packageToEnable);
        } else {
            // RobotOS system - disable agent service, enable speech service
            packageToDisable = Definition.AGENT_PACKAGE_NAME;
            packageToEnable = Definition.SPEECH_PACKAGE_NAME;
            Log.d(TAG, "RobotOS detected, will disable: " + packageToDisable + ", enable: " + packageToEnable);
        }

        if (packageToEnable != null && PackageManagerUtils.isPackageDisabled(this, packageToEnable)) {
            Log.i(TAG, "Enabling " + packageToEnable);
            PackageManagerUtils.enablePackage(packageToEnable);
        }

        if (packageToDisable != null && !PackageManagerUtils.isPackageDisabled(this, packageToDisable)) {
            Log.i(TAG, "Disabling " + packageToDisable);
            PackageManagerUtils.disablePackage(packageToDisable);
        }
    }


    public ModuleServer registerModule(String packageName, boolean isBackground) {
        return realRegisterModule(packageName, isBackground);
    }

    private ModuleServer realRegisterModule(String packageName, boolean isBackground) {
        boolean isSuspend = (!isBackground && !mModuleManager.isActiveAppModule(packageName))
                || mCoreRunStateManager.isSystemControl();
        Log.i(TAG, "Module register : " + packageName + " " + isSuspend + "  " + mCoreRunStateManager.isSystemControl() + "  " + isBackground);
        Module module = new AppModule(this, packageName, isSuspend);
        if (isBackground) {
            mModuleManager.addModule(module, Permission.BACKGROUND);
        } else {
            mModuleManager.addModule(module, Permission.APP);
        }
        return new ModuleServer(module);
    }

    /**
     * 注册预置外设Service
     */
    private void registerDefaultExternalService() {
        List<ServiceConfig> configs = ConfigManager.getServiceConfig();
        for (ServiceConfig config : configs) {
            Log.d(TAG, "Register default external service : " + config.getService());
            ExternalService service = new ExternalService(config.getService(), config.getServiceName());
            service.setConfig(config);
            service.setPreset(true);

            //            List<Command> commands = mCommandManager.getCommandByFunction(function);
            //            service.addCommand(commands);
            mExternalServiceManager.addExternalService(service);
        }
    }

    private void recoveryModule(String packageName) {
        boolean isSuspend = !mModuleManager.isActiveAppModule(packageName)
                || mCoreRunStateManager.isSystemControl();
        Log.i(TAG, "Module rebind : " + packageName + " " + isSuspend + "  " + mCoreRunStateManager.isSystemControl());

        if (!isSuspend) {
            mModuleManager.recoveryModule(packageName);
        }
    }

    public PersonServer registerPersonServer(String packageName) {
        Module module = mModuleManager.getModule(packageName);
        return new PersonServer(mCore, module);
    }

    public OtaServer registerOtaServer(String packageName) {
        mOtaServer.setPackageName(packageName);
        return mOtaServer;
    }

    @Override
    public boolean onUnbind(Intent intent) {
        Log.i(TAG, "CoreService unbind " + intent.getAction());
        return true;
    }

    @Override
    public void onRebind(Intent intent) {
        Log.i(TAG, "CoreService Rebind " + intent.getAction());
        recoveryModule(intent.getAction());
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        SettingsUtil.unregisterSettingsListener(ApplicationWrapper.getContext());
    }

    @Override
    protected void dump(FileDescriptor fd, PrintWriter writer, String[] args) {
        try {
            final ArgsIterator iterator = new ArgsIterator(args);
            switch (iterator.next()) {
                case "cmd":
                    switch (iterator.next()) {
                        case "enable_dance_action":
                            GongFuBean bean = new GongFuBean();
                            String footAction = "[{\"angle\":30,\"distance\":0,\"speed\":50,\"begin_time\":9000},{\"angle\":-30,\"distance\":0,\"speed\":50,\"begin_time\":11000},{\"angle\":-30,\"distance\":0,\"speed\":50,\"begin_time\":13000},{\"angle\":30,\"distance\":0,\"speed\":50,\"begin_time\":15000}]";
                            String headAction = "[{\"angle_horizontal\":0,\"angle_vertical\":15,\"speed_vertical\":60,\"speed_horizontal\":0,\"begin_time\":0},{\"angle_horizontal\":0,\"angle_vertical\":-15,\"speed_vertical\":60,\"speed_horizontal\":0,\"begin_time\":500},{\"angle_horizontal\":0,\"angle_vertical\":15,\"speed_vertical\":60,\"speed_horizontal\":0,\"begin_time\":1000},{\"angle_horizontal\":0,\"angle_vertical\":-15,\"speed_vertical\":60,\"speed_horizontal\":0,\"begin_time\":1500},{\"angle_horizontal\":0,\"angle_vertical\":15,\"speed_vertical\":60,\"speed_horizontal\":0,\"begin_time\":2000},{\"angle_horizontal\":0,\"angle_vertical\":-15,\"speed_vertical\":60,\"speed_horizontal\":0,\"begin_time\":2500},{\"angle_horizontal\":0,\"angle_vertical\":15,\"speed_vertical\":60,\"speed_horizontal\":0,\"begin_time\":3000},{\"angle_horizontal\":15,\"angle_vertical\":-10,\"speed_vertical\":60,\"speed_horizontal\":60,\"begin_time\":3500},{\"angle_horizontal\":-15,\"angle_vertical\":10,\"speed_vertical\":60,\"speed_horizontal\":60,\"begin_time\":4000},{\"angle_horizontal\":-15,\"angle_vertical\":-10,\"speed_vertical\":60,\"speed_horizontal\":60,\"begin_time\":4500},{\"angle_horizontal\":15,\"angle_vertical\":10,\"speed_vertical\":60,\"speed_horizontal\":60,\"begin_time\":5000},{\"angle_horizontal\":15,\"angle_vertical\":-10,\"speed_vertical\":60,\"speed_horizontal\":60,\"begin_time\":5500},{\"angle_horizontal\":-15,\"angle_vertical\":10,\"speed_vertical\":60,\"speed_horizontal\":60,\"begin_time\":6000},{\"angle_horizontal\":0,\"angle_vertical\":-15,\"speed_vertical\":60,\"speed_horizontal\":0,\"begin_time\":6500},{\"angle_horizontal\":0,\"angle_vertical\":-15,\"speed_vertical\":60,\"speed_horizontal\":0,\"begin_time\":7000},{\"angle_horizontal\":0,\"angle_vertical\":-15,\"speed_vertical\":60,\"speed_horizontal\":0,\"begin_time\":7500},{\"angle_horizontal\":0,\"angle_vertical\":15,\"speed_vertical\":60,\"speed_horizontal\":0,\"begin_time\":8000},{\"angle_horizontal\":0,\"angle_vertical\":15,\"speed_vertical\":60,\"speed_horizontal\":0,\"begin_time\":8500},{\"angle_horizontal\":20,\"angle_vertical\":0,\"speed_vertical\":0,\"speed_horizontal\":120,\"begin_time\":9000},{\"angle_horizontal\":-40,\"angle_vertical\":0,\"speed_vertical\":0,\"speed_horizontal\":120,\"begin_time\":10000},{\"angle_horizontal\":40,\"angle_vertical\":0,\"speed_vertical\":0,\"speed_horizontal\":120,\"begin_time\":11000},{\"angle_horizontal\":-40,\"angle_vertical\":0,\"speed_vertical\":0,\"speed_horizontal\":120,\"begin_time\":12000},{\"angle_horizontal\":40,\"angle_vertical\":0,\"speed_vertical\":0,\"speed_horizontal\":120,\"begin_time\":13000},{\"angle_horizontal\":-40,\"angle_vertical\":0,\"speed_vertical\":0,\"speed_horizontal\":120,\"begin_time\":14000},{\"angle_horizontal\":20,\"angle_vertical\":0,\"speed_vertical\":0,\"speed_horizontal\":120,\"begin_time\":15000},{\"angle_horizontal\":0,\"angle_vertical\":15,\"speed_vertical\":60,\"speed_horizontal\":0,\"begin_time\":16000},{\"angle_horizontal\":0,\"angle_vertical\":-15,\"speed_vertical\":60,\"speed_horizontal\":0,\"begin_time\":16500},{\"angle_horizontal\":0,\"angle_vertical\":15,\"speed_vertical\":60,\"speed_horizontal\":0,\"begin_time\":17000},{\"angle_horizontal\":0,\"angle_vertical\":-15,\"speed_vertical\":60,\"speed_horizontal\":0,\"begin_time\":17500},{\"angle_horizontal\":0,\"angle_vertical\":15,\"speed_vertical\":60,\"speed_horizontal\":0,\"begin_time\":18000}]";
                            bean.setFootActionJson(footAction);
                            bean.setHeadActionJson(headAction);
                            getSystemServer().startAction(-1, Definition.ACTION_GONGFU, new Gson().toJson(bean), new BaseApiProxy.ApiListener() {
                                @Override
                                public void onResult(int status, String responseString) {
                                    Log.d(TAG, "onResult");
                                }

                                @Override
                                public void onError(int errorCode, String errorString) {
                                    Log.d(TAG, "onError:: errorCode=" + errorCode + " errorString=" + errorString);
                                }

                                @Override
                                public void onStatusUpdate(int status, String data) {
                                    Log.d(TAG, "onStatusUpdate");
                                }
                            });
                            break;
                        case "enable_red_fov_light":
                            getSystemServer().startAction(-1, Definition.ACTION_RED_FOV_LIGHT, "{}", new BaseApiProxy.ApiListener() {


                                @Override
                                public void onResult(int status, String responseString) {
                                    Log.d(TAG, "onResult");
                                }

                                @Override
                                public void onError(int errorCode, String errorString) {
                                    Log.d(TAG, "onError:: errorCode=" + errorCode + " errorString=" + errorString);
                                }

                                @Override
                                public void onStatusUpdate(int status, String data) {
                                    Log.d(TAG, "onStatusUpdate");
                                }
                            });
                            break;
                        case "disable_red_fov_light":
                            getSystemServer().stopAction(-1, Definition.ACTION_RED_FOV_LIGHT, false);
                            break;
                        case "enable_blue_fov_light":
                            getSystemServer().startAction(-1, Definition.ACTION_BLUE_FOV_LIGHT, "{}", new BaseApiProxy.ApiListener() {
                                @Override
                                public void onResult(int status, String responseString) {
                                    Log.d(TAG, "onResult");
                                }

                                @Override
                                public void onError(int errorCode, String errorString) {
                                    Log.d(TAG, "onError:: errorCode=" + errorCode + " errorString=" + errorString);
                                }

                                @Override
                                public void onStatusUpdate(int status, String data) {
                                    Log.d(TAG, "onStatusUpdate");
                                }
                            });
                            break;
                        case "disable_blue_fov_light":
                            getSystemServer().stopAction(-1, Definition.ACTION_BLUE_FOV_LIGHT, false);
                            break;
                        default:
                            writer.println("enable_red_fov_light");
                            writer.println("disable_red_fov_light");
                            writer.println("enable_blue_fov_light");
                            writer.println("disable_blue_fov_light");
                            break;
                    }
                    break;
                case "resource":
                    getResManager().dump(TAG, writer, iterator.next());
                    break;
                case "system":
                    getSystemManager().dump(TAG, writer, iterator);
                    break;
                default:
                    super.dump(fd, writer, args);
                    break;
            }
        } catch (Throwable throwable) {
            throwable.printStackTrace(writer);
        }
    }

    public ExternalServiceManager getExternalServiceManager() {
        return mExternalServiceManager;
    }

    public CoreRunStateManager getCoreRunSateManager() {
        return mCoreRunStateManager;
    }

    public RequestManager getRequestManager() {
        return mRequestManager;
    }

    public ActionManager getActionManager() {
        return mActionManager;
    }

    public CommandManager getCommandManager() {
        return mCommandManager;
    }

    public SystemManager getSystemManager() {
        return mSystemManager;
    }

    public ModuleManager getModuleManager() {
        return mModuleManager;
    }

    public Handler getRequestHandler() {
        return mRequestHandler;
    }

    public Handler getResponseHandler() {
        return mResponseHandler;
    }

    public Handler getCoreHandler() {
        return mCoreHandler;
    }

    public CoreStateMachine getCoreStateMachine() {
        return mStateMachine;
    }

    public SurfaceShareServer getSurfaceShareServer() {
        return mSurfaceShareServer;
    }

    public StatusManager getStatusManager() {
        return mStatusManager;
    }

    public SocketManager getSocketManager() {
        return mSocketManager;
    }

    public ResManager getResManager() {
        return mResManager;
    }

    public HeadTurnManager getHeadMoveManager() {
        return mHeadTurnManager;
    }

    public PersonManager getPersonManager() {
        return mPersonManager;
    }

    public FaceAngleManager getFaceAngleManager() {
        return mFaceAngleManager;
    }

    public SurfaceShareManager getSurfaceShareManager() {
        return mSurfaceShareManager;
    }

    public RobotInfoManager getRobotInfoManager() {
        return mRobotInfoManager;
    }

    public RobotSettingServer getRobotSettingServer() {
        return mRobotSettingServer;
    }

    public AccountManager getAccountManager() {
        return mAccountManager;
    }

    public PermissionManager getPermissionManager() {
        return mPermissionManager;
    }

    public LanguageManger getLanguageManger(){
        return mLanguageManger;
    }

    public RobotSettingManager getRobotSettingManager() {
        return mRobotSettingManager;
    }

    public SystemServer getSystemServer() {
        return mSystemServer;
    }

    public ShareMemoryServer getShareMemoryServer(){
        return mShareMemoryServer;
    }

    public BatteryManager getBatteryManager() {
        return mBatteryManager;
    }

    private Handler mCoreHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            Bundle bundle = msg.getData();
            switch (msg.what) {
                case InternalDef.MSG_CORE_ADD_COMMAND_ACTION:
                    mStateMachine.tryToSendCommand();
                    break;
                case InternalDef.MSG_CORE_HW_REGISTER:
                    //mStateMachine.updateHWStatus(bundle.getInt(InternalDef.BUNDLE_INT));
                    break;
                case InternalDef.MSG_CORE_HW_UNREGISTER:
                    // mStateMachine.removeHWStatus(bundle.getInt(InternalDef.BUNDLE_INT));
                    break;
                default:
                    break;
            }
        }
    };
}
