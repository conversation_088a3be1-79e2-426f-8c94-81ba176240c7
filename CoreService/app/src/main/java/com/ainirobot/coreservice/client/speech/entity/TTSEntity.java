package com.ainirobot.coreservice.client.speech.entity;

import java.util.HashMap;

public class TTSEntity {
    private String textSid;
    private String text;
    private String streamSid;
    /**
     * ttsParams {@link TTSParams}
     * 该设置仅本次请求生效
     */
    private HashMap<String, String> ttsParams;

    public TTSEntity() {
    }

    public TTSEntity(String text) {
        this.text = text;
    }

    public TTSEntity(String textSid, String text) {
        this.textSid = textSid;
        this.text = text;
    }

    public TTSEntity(String textSid, String streamSid, String text) {
        this.textSid = textSid;
        this.streamSid = streamSid;
        this.text = text;
    }

    public TTSEntity(String textSid, String text, HashMap<String, String> ttsParams) {
        this.textSid = textSid;
        this.text = text;
        this.ttsParams = ttsParams;
    }

    public TTSEntity(String textSid, String streamSid, String text, HashMap<String, String> ttsParams) {
        this.textSid = textSid;
        this.streamSid = streamSid;
        this.text = text;
        this.ttsParams = ttsParams;
    }

    public String getTextSid() {
        return textSid;
    }

    public void setTextSid(String textSid) {
        this.textSid = textSid;
    }

    public String getStreamSid() {
        return streamSid;
    }

    public void setStreamSid(String streamSid) {
        this.streamSid = streamSid;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public HashMap<String, String> getTtsParams() {
        return ttsParams;
    }

    public void setTtsParams(HashMap<String, String> ttsParams) {
        this.ttsParams = ttsParams;
    }

    @Override
    public String toString() {
        return "TTSEntity{" +
                "textSid='" + textSid + '\'' +
                "streamSid='" + streamSid + '\'' +
                ", text='" + text + '\'' +
                ", ttsParams=" + ttsParams +
                '}';
    }
}
