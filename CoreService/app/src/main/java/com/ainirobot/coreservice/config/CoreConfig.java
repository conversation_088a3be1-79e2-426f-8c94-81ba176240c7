package com.ainirobot.coreservice.config;

import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.exception.InvalidArgumentException;
import com.ainirobot.coreservice.config.core.AccountConfig;
import com.ainirobot.coreservice.config.core.BatteryConfig;
import com.ainirobot.coreservice.config.core.HeadConfig;
import com.ainirobot.coreservice.config.core.NavigationConfig;
import com.ainirobot.coreservice.config.core.PublicConfig;
import com.google.gson.JsonObject;
import com.google.gson.annotations.SerializedName;

import java.util.Set;

public class CoreConfig {
    private static final String TAG = CoreConfig.class.getSimpleName();

    private static final String CONFIG_DETAIL = "配置项";
    private static final String SERVICE_MANAGER = "服务管理";
    private static final String ACCOUNT_SERVICE = "账号服务";
    private static final String HEAD_SERVICE = "头部服务";
    private static final String BATTERY_SERVICE = "电池服务";
    private static final String NAVIGATION_SERVICE = "导航服务";

    @SerializedName(CONFIG_DETAIL)
    private ConfigDetail configDetail;

    @SerializedName(SERVICE_MANAGER)
    private JsonObject services;

    private PublicConfig publicConfig;
    private AccountConfig accountConfig;
    private HeadConfig headConfig;
    private BatteryConfig batteryConfig;
    private NavigationConfig naviConfig;

    public void parse() {
        Log.d(TAG, "parse services: " + services);
        if (services != null) {
            Set<String> keys = services.keySet();
            for (String key : keys) {
                Log.d(TAG, "parse key: " + key);
                JsonObject jsonElement = services.getAsJsonObject(key);
                if (ACCOUNT_SERVICE.equals(key)) {
                    accountConfig = new AccountConfig(jsonElement);
                } else if (HEAD_SERVICE.equals(key)) {
                    headConfig = new HeadConfig(jsonElement);
                } else if (BATTERY_SERVICE.equals(key)) {
                    batteryConfig = new BatteryConfig(jsonElement);
                } else if (NAVIGATION_SERVICE.equals(key)) {
                    naviConfig = new NavigationConfig(jsonElement);
                } else if (RobotConfig.PUBLIC_CONFIG.equals(key)) {
                    publicConfig = new PublicConfig(jsonElement);
                    publicConfig.parseConfig(jsonElement);
                    try {
                        RobotSettings.setRobotString(ApplicationWrapper.getContext(), "client_id", publicConfig.getClientID());
                    } catch (InvalidArgumentException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        Log.d(TAG, toString());
    }

    public void setPublicConfig(JsonObject publicConfig) {
        if (services != null) {
            services.add(RobotConfig.PUBLIC_CONFIG, publicConfig);
        }
    }

    public PublicConfig getPublicConfig() {
        return publicConfig;
    }

    public AccountConfig getAccountConfig() {
        return accountConfig;
    }

    public HeadConfig getHeadConfig() {
        return headConfig;
    }

    public BatteryConfig getBatteryConfig() {
        return batteryConfig;
    }

    public NavigationConfig getNavigationConfig() {
        return naviConfig;
    }

    public ConfigDetail getConfigDetail() {
        return configDetail;
    }

    @Override
    public String toString() {
        return "CoreConfig{"
                + "ConfigDetail='"
                + configDetail
                + '\''
                + ", AccountConfig="
                + accountConfig
                + ", HeadConfig="
                + headConfig
                + ", BatteryConfig="
                + batteryConfig
                + ", NavigationConfig="
                + naviConfig
                + '}';
    }

    public static class ConfigDetail {
        private static final String STANDBY_CONFIG = "待机配置";
        private static final String LOG_LEVEL = "Log等级";

        @SerializedName(STANDBY_CONFIG)
        private StandbyConfig standbyConfig;

        @SerializedName("定时关机配置")
        private ShutdownConfig shutdownConfig;

        @SerializedName(LOG_LEVEL)
        private LogConfig logConfig;

        @SerializedName("是否有TopIR")
        private boolean hasTopIR;

        @SerializedName("是否有回充IR")
        private boolean hasChargeIR;

        @SerializedName("是否使用ProZcbLed灯带接口")
        private boolean isUseAutoEffectLed;

        @SerializedName("是否使用锁骨灯")
        private boolean hasClavicleLed;

        @SerializedName("是否有胸口灯")
        private boolean hasChestLed;

        @SerializedName("是否有托盘指示灯")
        private boolean hasTrayLight;

        @SerializedName("是否有Mono")
        private boolean hasMono;

        public StandbyConfig getStandbyConfig() {
            return standbyConfig;
        }

        public ShutdownConfig getShutdownConfig() {
            return shutdownConfig;
        }

        public LogConfig getLogConfig() {
            return logConfig;
        }

        public boolean isHasTopIR() {
            return hasTopIR;
        }

        public boolean isHasChargeIR() {
            return hasChargeIR;
        }

        public boolean isUseAutoEffectLed() {
            return isUseAutoEffectLed;
        }

        public boolean isHasClavicleLed(){
            return hasClavicleLed;
        }

        public boolean isHasChestLed(){
            return hasChestLed;
        }

        public boolean isHasTrayLight(){
            return hasTrayLight;
        }

        public boolean isHasMono() {
            return hasMono;
        }

        @Override
        public String toString() {
            return "ConfigDetail{" +
                    "standbyConfig=" + standbyConfig +
                    ", shutdownConfig=" + shutdownConfig +
                    ", logConfig=" + logConfig +
                    ", hasTopIR=" + hasTopIR +
                    ", hasChargeIR=" + hasChargeIR +
                    ", isUseAutoEffectLed=" + isUseAutoEffectLed +
                    ", hasClavicleLed=" + hasClavicleLed +
                    ", hasChestLed=" + hasChestLed +
                    ", hasTrayLight=" + hasTrayLight +
                    ", hasMono=" + hasMono +
                    '}';
        }
    }

    public static class ShutdownConfig {

        @SerializedName("是否启用")
        private boolean enable;

        @SerializedName("默认关机时间")
        private String defaultTime;

        public boolean isEnabled() {
            return enable;
        }

        public String getDefaultTime() {
            return defaultTime;
        }

    }

    public static class StandbyConfig {
        private static final String MUTE_MUSIC = "静音";
        private static final String SCREEN_BRIGHTNESS = "降低屏幕亮度";
        private static final String DISABLE_VISION = "关闭视觉";
        private static final String DISABLE_SPEECH = "关闭语音";
        private static final String GOTO_SLEEP = "休眠";
        private static final String POWER_OFF = "关机";

        private static final String MODULE_BIT_RGB_LED = "关闭灯带";
        private static final String MODULE_BIT_IR_LED = "关闭IR灯";
        private static final String MODULE_BIT_RADAR = "关闭雷达";
        private static final String MODULE_BIT_WHEEL = "关闭轮子电机";
        private static final String DISABLE_DEPTH_IR_CAMERA = "关闭深度IR摄像头";

        @SerializedName(MUTE_MUSIC)
        private boolean mute_music;
        @SerializedName(SCREEN_BRIGHTNESS)
        private boolean screen_brightness;
        @SerializedName(DISABLE_VISION)
        private boolean disable_vision;
        @SerializedName(DISABLE_SPEECH)
        private boolean disable_speech;
        @SerializedName(GOTO_SLEEP)
        private boolean goto_sleep;
        @SerializedName(POWER_OFF)
        private boolean power_off;
        @SerializedName(DISABLE_DEPTH_IR_CAMERA)
        private boolean disable_depth_ir_camera;

        @SerializedName(MODULE_BIT_RGB_LED)
        private boolean module_bit_rgb_led;
        @SerializedName(MODULE_BIT_IR_LED)
        private boolean module_bit_ir_led;
        @SerializedName(MODULE_BIT_RADAR)
        private boolean module_bit_radar;
        @SerializedName(MODULE_BIT_WHEEL)
        private boolean module_bit_wheel;

        public boolean isMute_music() {
            return mute_music;
        }

        public boolean isScreen_brightness() {
            return screen_brightness;
        }

        public boolean isDisable_vision() {
            return disable_vision;
        }

        public boolean isDisable_speech() {
            return disable_speech;
        }

        public boolean isGoto_sleep() {
            return goto_sleep;
        }

        public boolean isPower_off() {
            return power_off;
        }

        public boolean isModule_bit_rgb_led() {
            return module_bit_rgb_led;
        }

        public boolean isModule_bit_ir_led() {
            return module_bit_ir_led;
        }

        public boolean isModule_bit_radar() {
            return module_bit_radar;
        }

        public boolean isModule_bit_wheel() {
            return module_bit_wheel;
        }

        public boolean isDisable_depth_ir_camera() {
            return disable_depth_ir_camera;
        }

        @Override
        public String toString() {
            return "StandbyConfig{" +
                    "mute_music='" + mute_music + '\'' +
                    ", screen_brightness='" + screen_brightness + '\'' +
                    ", disable_vision='" + disable_vision + '\'' +
                    ", disable_speech='" + disable_speech + '\'' +
                    ", goto_sleep='" + goto_sleep + '\'' +
                    ", power_off='" + power_off + '\'' +
                    ", module_bit_rgb_led='" + module_bit_rgb_led + '\'' +
                    ", module_bit_ir_led='" + module_bit_ir_led + '\'' +
                    ", module_bit_radar='" + module_bit_radar + '\'' +
                    ", module_bit_wheel='" + module_bit_wheel + '\'' +
                    ", disable_depth_ir_camera='" + disable_depth_ir_camera + '\'' +
                    '}';
        }
    }

    public static class LogConfig{
        @SerializedName("等级")
        private int level;

        public int getLevel() {
            return level;
        }
    }
}
