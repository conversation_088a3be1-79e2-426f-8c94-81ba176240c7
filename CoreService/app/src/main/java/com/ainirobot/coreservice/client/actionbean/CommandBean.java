package com.ainirobot.coreservice.client.actionbean;

/**
 * Created by orion on 2018/4/12.
 */

public class CommandBean extends BaseBean{

    private String cmdType;
    private String params;
    private boolean isContinue;

    public CommandBean(){
        super();
    }

    public CommandBean(String cmdType, String params, boolean isContinue){
        this.cmdType = cmdType;
        this.params = params;
        this.isContinue = isContinue;
    }

    public String getCmdType() {
        return cmdType;
    }

    public void setCmdType(String cmdType) {
        this.cmdType = cmdType;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public boolean isContinue() {
        return isContinue;
    }

    public void setContinue(boolean aContinue) {
        isContinue = aContinue;
    }

    @Override public String toString() {
        return "CommandBean{"
            + "cmdType='"
            + cmdType
            + '\''
            + ", params='"
            + params
            + '\''
            + ", isContinue="
            + isContinue
            + '}';
    }
}
