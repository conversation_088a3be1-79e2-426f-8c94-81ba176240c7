package com.ainirobot.coreservice.action.advnavi.elevatorcmd;

import com.ainirobot.coreservice.client.Definition;

/**
 * Data: 2023/3/29 16:21
 * Author: wanglijing
 * <p>
 * 释放电梯命令
 * * 成功/失败 都上报RESULT_OK
 * * 超时 重试释放
 */
public class CmdReleaseElevator extends ElevatorCommand {


    CmdReleaseElevator(String name, CmdType intentType, ElevatorCommandInterface commandInterface, ElevatorCommandListener listener) {
        super(name, intentType, commandInterface, listener);
        initCmdType(CmdType.RELEASE_ELEVATOR);
    }

    @Override
    public void start() {
        super.start();
        releaseElevatorControl();
    }

    @Override
    public void onSuccess() {
        super.onSuccess();
        onResult(Definition.RESULT_OK, "release elevator suc");
    }

    @Override
    public void onFail() {
        onResult(Definition.RESULT_OK, "release elevator suc");
    }

    @Override
    protected void onRetry() {
        releaseElevatorControl();
    }
}
