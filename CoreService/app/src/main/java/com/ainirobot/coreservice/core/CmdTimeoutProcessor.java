/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.core;

import com.ainirobot.coreservice.core.CommandManager.CmdAction;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class CmdTimeoutProcessor {

    private ScheduledExecutorService mExecutor;
    private ConcurrentHashMap<CmdAction, Future> mFutures;

    public CmdTimeoutProcessor() {
        this.mFutures = new ConcurrentHashMap<>();
        this.mExecutor = Executors.newScheduledThreadPool(4);
    }

    public void submit(CmdAction cmd) {
        DelayRunnable runnable = new DelayRunnable(cmd);
        long timeout = cmd.getTimeout();
        if (timeout <= 0) {
            return;
        }
        Future future = mExecutor.schedule(runnable, cmd.getTimeout(), TimeUnit.MILLISECONDS);


        mFutures.putIfAbsent(cmd, future);
    }

    public void cancel(CmdAction cmd) {
        Future future = mFutures.remove(cmd);
        if (future != null && !future.isCancelled()) {
            future.cancel(true);
        }
    }

    private class DelayRunnable implements Runnable {
        private CmdAction mCmdAction;

        public DelayRunnable(CmdAction cmd) {
            mCmdAction = cmd;
        }

        @Override
        public void run() {
            mFutures.remove(mCmdAction);
            mCmdAction.onTimeout();
        }
    }
}
