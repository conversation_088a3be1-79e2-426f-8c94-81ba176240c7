/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.coreservice.core.person;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.account.UserBean;
import com.ainirobot.coreservice.client.listener.Person;
import com.ainirobot.coreservice.client.person.FaceBean;
import com.ainirobot.coreservice.client.person.PersonResult;
import com.ainirobot.coreservice.client.person.PersonUtils;
import com.ainirobot.coreservice.config.ConfigManager;
import com.ainirobot.coreservice.core.apiproxy.impl.person.PersonApiProxy_v0;
import com.ainirobot.coreservice.core.apiproxy.impl.person.PersonApiProxy_v1;
import com.ainirobot.coreservice.core.apiproxy.impl.person.PersonApiProxy_v2;
import com.ainirobot.coreservice.core.apiproxy.impl.person.PersonBaseApiProxy;
import com.ainirobot.coreservice.core.apiproxy.result.BaseApiResult;
import com.ainirobot.coreservice.core.apiproxy.result.impl.LocalSyncFaceResult;
import com.ainirobot.coreservice.core.person.bean.RequestBean;
import com.ainirobot.coreservice.data.account.MemberDataHelper;
import com.ainirobot.coreservice.listener.IActionListener;
import com.ainirobot.coreservice.listener.IPersonListener;
import com.ainirobot.coreservice.client.log.RLog;
import com.ainirobot.coreservice.service.CoreService;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

import static com.ainirobot.coreservice.core.apiproxy.BaseApiProxy.MSG_API_RESULT;

public class PersonManager {

    private static final String TAG = PersonManager.class.getSimpleName();
    public static final int MSG_REGISTER = 0x1;
    public static final int MSG_RECOGNIZE = 0x2;
    public static final int MSG_RE_REGISTER = 0x3;
    private static final int MSG_LOCAL_DATA_SYNC_RETRY = 0x4;
    private static final int MSG_PERSON_DISAPPEAR = 0x5;
    private static final int MSG_CHECK_VISION_CONNECT_RETRY = 0x6;
    // only support v1 & v2 (which with AccountManager)
    private static final int MSG_DELETE_ALL_MARK_DELETE_FACES = 0x7;

    private static final long TIMEOUT_LOCAL_FACE_SYNC_RETRY = 1000;
    private static final long TIMEOUT_CHECK_VISION_CONNECT_RETRY = 10 * 1000;
    private static final long TIMEOUT_PERSON_DISAPPEAR = 1500;
    private static final long TIMEOUT_DELETE_ALL_MARK_FACES = 30 * 60 * 1000;

    private CoreService mCore;
    private PersonHandler mHandler;
    //    private List<Person> mPersonList;
    private Person mFocusPerson;
    private Gson mGson;
    private IPersonListener mRemotePersonListener;
    private IActionListener mActionListener;
    private PersonState mPersonState;
    private RegisterState mRegisterState;
    private RecognizeState mRecognizeState;
    private RequestBean mRegisterBean;
    private RequestBean mRecognizeBean;
    private PersonBaseApiProxy mApiProxy;
    private MemberDataHelper mDataHelper;
    private List<FaceBean> mDeleteFaceList;
    private List<Person> mAllPersonList;
    private List<Person> mOnlyFacesPersonList = new CopyOnWriteArrayList<>();
    private List<Person> mCompleteFacesPersonList = new CopyOnWriteArrayList<>();
    private List<Person> mOnlyBodyPersonList = new CopyOnWriteArrayList<>();

    private long lastPrintTime;

    private enum PersonState {
        IDLE, INITIALIZING, LOCAL_DATA_SYNCING
    }

    private enum RegisterState {
        IDLE, REGISTERING, RE_REGISTERING
    }

    private enum RecognizeState {
        IDLE, RECOGNIZING
    }

    public PersonManager(CoreService core) {
        HandlerThread thread = new HandlerThread("PersonManager");
        thread.start();
        mHandler = new PersonHandler(this, thread.getLooper());
        mCore = core;
        mGson = new Gson();
        if (!isAccountServiceEnable()) {
            mApiProxy = new PersonApiProxy_v0(mCore, mHandler);
        } else {
            if (isLocalRegisterEnable()) {
                mApiProxy = new PersonApiProxy_v2(mCore, mHandler);
            } else {
                mApiProxy = new PersonApiProxy_v1(mCore, mHandler);
            }
            mDataHelper = MemberDataHelper.getInstance(core);
            mApiProxy.registerNetworkStateListener();
            mHandler.sendEmptyMessageDelayed(MSG_DELETE_ALL_MARK_DELETE_FACES,
                    TIMEOUT_DELETE_ALL_MARK_FACES);
        }
        mPersonState = PersonState.INITIALIZING;
        mRegisterState = RegisterState.IDLE;
        mRecognizeState = RecognizeState.IDLE;
        mApiProxy.checkVisionConnect();

    }

    public static class PersonHandler extends Handler {

        private PersonManager mManager;

        private PersonHandler(PersonManager manager, Looper looper) {
            super(looper);
            mManager = manager;
        }

        @Override
        public void handleMessage(Message msg) {
//            Log.v(TAG, "PersonManager handleMessage what: " + msg.what
//                    + ", mPersonState: " + mManager.mPersonState
//                    + ", mRegisterState: " + mManager.mRegisterState
//                    + ", mRecognizeState: " + mManager.mRecognizeState);
            RequestBean bean;
            switch (msg.what) {
                case MSG_REGISTER:
                    Log.i(TAG, "FaceManager MSG_REGISTER mFaceState: " + mManager.mPersonState
                            + ", mRegisterState: " + mManager.mRegisterState);
                    bean = (RequestBean) msg.obj;

                    if (mManager.mPersonState == PersonState.INITIALIZING) {
                        mManager.processResult(bean, PersonResult
                                        .INITIALIZING,
                                "initializing");
                        return;
                    }

                    if (mManager.mPersonState == PersonState.LOCAL_DATA_SYNCING) {
                        mManager.processResult(bean, PersonResult
                                        .DATA_SYNCING,
                                "data syncing");
                        return;
                    }

                    if (mManager.mRegisterState == RegisterState.REGISTERING) {
                        mManager.processResult(bean, PersonResult.Register.REGISTER_ALREADY_RUN,
                                "register already run");
                        return;
                    }

                    if (mManager.mRegisterState == RegisterState.RE_REGISTERING) {
                        mManager.processResult(bean, PersonResult.ReRegister
                                        .RE_REGISTER_ALREADY_RUN,
                                "re register already run");
                        return;
                    }

                    mManager.startRegisterTask(bean);
                    break;
                case MSG_RE_REGISTER:
                    Log.i(TAG, "FaceManager MSG_RE_REGISTER" +
                            " mFaceState: " + mManager.mPersonState
                            + ", mRegisterState: " + mManager.mRegisterState);
                    bean = (RequestBean) msg.obj;

                    if (mManager.mPersonState == PersonState.INITIALIZING) {
                        mManager.processResult(bean, PersonResult
                                        .INITIALIZING,
                                "initializing");
                        return;
                    }

                    if (mManager.mPersonState == PersonState.LOCAL_DATA_SYNCING) {
                        mManager.processResult(bean, PersonResult
                                        .DATA_SYNCING,
                                "data syncing");
                        return;
                    }

                    if (mManager.mRegisterState == RegisterState.REGISTERING) {
                        mManager.processResult(bean, PersonResult.Register.REGISTER_ALREADY_RUN,
                                "register already run");
                        return;
                    }

                    if (mManager.mRegisterState == RegisterState.RE_REGISTERING) {
                        mManager.processResult(bean, PersonResult.ReRegister
                                        .RE_REGISTER_ALREADY_RUN,
                                "re register already run");
                        return;
                    }

                    mManager.startReRegisterTask(bean);
                    break;
                case MSG_RECOGNIZE:
                    Log.i(TAG, "FaceManager MSG_RECOGNIZE mFaceState: " + mManager.mPersonState
                            + ", mRecognizeState: " + mManager.mRecognizeState);
                    bean = (RequestBean) msg.obj;

                    if (mManager.mPersonState == PersonState.INITIALIZING) {
                        mManager.processResult(bean, PersonResult
                                        .INITIALIZING,
                                "initializing");
                        return;
                    }

                    if (mManager.mPersonState == PersonState.LOCAL_DATA_SYNCING) {
                        mManager.processResult(bean, PersonResult
                                        .DATA_SYNCING,
                                "data syncing");
                        return;
                    }

                    if (mManager.mRecognizeState == RecognizeState.RECOGNIZING) {
                        mManager.processResult(bean, PersonResult.Recognize.RECOGNIZE_ALREADY_RUN,
                                "recognize already run");
                        return;
                    }

                    mManager.startRecognizeTask(bean);
                    break;
                case MSG_API_RESULT:
                    mManager.handleApiResult((BaseApiResult) msg.obj);
                    break;
                case MSG_LOCAL_DATA_SYNC_RETRY:
                    if (mManager.mPersonState == PersonState.LOCAL_DATA_SYNCING) {
                        mManager.dataSync();
                    }
                    break;
                case MSG_PERSON_DISAPPEAR:
                    mManager.updateAllPersonList(new ArrayList<Person>());
                    break;
                case MSG_CHECK_VISION_CONNECT_RETRY:
                    mManager.mApiProxy.checkVisionConnect();
                    break;
                case MSG_DELETE_ALL_MARK_DELETE_FACES:
                    if (mManager.isAccountServiceEnable()) {
                        mManager.mDeleteFaceList = mManager.mDataHelper.getAllMarkDeleteFaces();
                        sendEmptyMessageDelayed(MSG_DELETE_ALL_MARK_DELETE_FACES,
                                TIMEOUT_DELETE_ALL_MARK_FACES);
                    }
                    break;
                default:
                    break;
            }
        }
    }

    private void dataSync() {
        Log.d(TAG, "dataSync");
        if (isAccountServiceEnable()) {
            updatePersonState(PersonState.LOCAL_DATA_SYNCING);
            mApiProxy.localFaceDataSync();
        } else {
            updatePersonState(PersonState.IDLE);
        }
    }

    private void handleApiResult(BaseApiResult apiResult) {
//        Log.d(TAG, "handleApiResult apiResult: " + apiResult);
        BaseApiResult.Type type = apiResult.getType();
        int result = apiResult.getResult();
        Object data = apiResult.getData();
        switch (type) {
            case VISION_STATE:
                if (result == BaseApiResult.RESULT_SUCCESS) {
                    mApiProxy.stopGetAllPersonInfo();
                    mApiProxy.startGetAllPersonInfo();
                    if (mRegisterState == RegisterState.REGISTERING) {
                        processResult(mRegisterBean, PersonResult.INITIALIZING,
                                "vision sdk disconnected");
                        updateRegisterState(RegisterState.IDLE);
                    }
                    if (mRecognizeState == RecognizeState.RECOGNIZING) {
                        processResult(mRecognizeBean, PersonResult.INITIALIZING,
                                "vision sdk disconnected");
                        updateRecognizeState(RecognizeState.IDLE);
                    }
                    dataSync();
                } else {
                    updatePersonState(PersonState.INITIALIZING);
                    mApiProxy.stopGetAllPersonInfo();
                    if (mRegisterState == RegisterState.REGISTERING) {
                        processResult(mRegisterBean, PersonResult.INITIALIZING,
                                "vision sdk disconnected");
                        updateRegisterState(RegisterState.IDLE);
                    }
                    if (mRecognizeState == RecognizeState.RECOGNIZING) {
                        processResult(mRecognizeBean, PersonResult.INITIALIZING,
                                "vision sdk disconnected");
                        updateRecognizeState(RecognizeState.IDLE);
                    }
                }
                break;
            case CHECK_VISION_CONNECT:
                if (mPersonState == PersonState.INITIALIZING) {
                    if (result == BaseApiResult.RESULT_SUCCESS) {
                        mApiProxy.stopGetAllPersonInfo();
                        mApiProxy.startGetAllPersonInfo();
                        if (mRegisterState == RegisterState.REGISTERING) {
                            processResult(mRegisterBean, PersonResult.INITIALIZING,
                                    "vision sdk disconnected");
                            updateRegisterState(RegisterState.IDLE);
                        }
                        if (mRecognizeState == RecognizeState.RECOGNIZING) {
                            processResult(mRecognizeBean, PersonResult.INITIALIZING,
                                    "vision sdk disconnected");
                            updateRecognizeState(RecognizeState.IDLE);
                        }
                        dataSync();
                        mApiProxy.registerVisionConnectStateListener();
                    } else {
                        mHandler.removeMessages(MSG_CHECK_VISION_CONNECT_RETRY);
                        mHandler.sendEmptyMessageDelayed(MSG_CHECK_VISION_CONNECT_RETRY, TIMEOUT_CHECK_VISION_CONNECT_RETRY);
                    }
                }
                break;
            case PERSON_DATA:
                notifyStatusUpdate(result, (String) data);
                List<Person> personResult;
                final Type personType = new TypeToken<List<Person>>() {
                }.getType();
                try {
                    personResult = mGson.fromJson((String) data, personType);
                } catch (Exception e) {
                    e.printStackTrace();
                    personResult = new ArrayList<>();
                }
                if (personResult.size() > 0) {
                    for (Person person : personResult) {
                        person.setGender("-1");
                        person.setAge(-1);
                    }
                }
                updateAllPersonList(personResult);
                break;
            case LOCAL_SYNC_FACE:
                if (mPersonState == PersonState.LOCAL_DATA_SYNCING) {
                    if (BaseApiResult.RESULT_FAILED == result) {
                        mHandler.sendEmptyMessageDelayed(MSG_LOCAL_DATA_SYNC_RETRY,
                                TIMEOUT_LOCAL_FACE_SYNC_RETRY);
                    } else {
                        LocalSyncFaceResult.LocalDataSyncData dataSyncData
                                = (LocalSyncFaceResult.LocalDataSyncData) data;
                        if (LocalSyncFaceResult.RESULT_LOCAL_SYNC_UPGRADED == result) {
                            List<FaceBean> lostFaceBeanList = mDataHelper
                                    .upgradeFaceList(((LocalSyncFaceResult.LocalDataSyncData) data)
                                            .getFaceList());
                            Log.d(TAG, "lostFaceBeanList size: " + lostFaceBeanList.size());
                        }
                        mDataHelper.updateRegisterAlgorithmVersion(dataSyncData.getVersion());
                        updatePersonState(PersonState.IDLE);
                    }
                }
                break;
            case REGISTER:
                if (mPersonState == PersonState.IDLE
                        && mRegisterState == RegisterState.REGISTERING) {
                    if (BaseApiResult.RESULT_SUCCESS == result) {
                        processResult(mRegisterBean, PersonResult.SUCCESS,
                                data == null ? "" : data.toString());
                    } else {
                        processResult(mRegisterBean, PersonResult.Register
                                .REGISTER_FAILED, data == null ? "" : data.toString());
                    }
                    updateRegisterState(RegisterState.IDLE);
                }
                break;
            case RE_REGISTER:
                if (mPersonState == PersonState.IDLE
                        && mRegisterState == RegisterState.RE_REGISTERING) {
                    if (BaseApiResult.RESULT_SUCCESS == result) {
                        processResult(mRegisterBean, PersonResult.SUCCESS,
                                data == null ? "" : data.toString());
                    } else {
                        processResult(mRegisterBean, PersonResult.ReRegister
                                .RE_REGISTER_FAILED, data == null ? "" : data.toString());
                    }
                    updateRegisterState(RegisterState.IDLE);
                }
                break;
            case RECOGNIZE:
                if (mPersonState == PersonState.IDLE
                        && mRecognizeState == RecognizeState.RECOGNIZING) {
                    if (BaseApiResult.RESULT_SUCCESS == result) {
                        processResult(mRecognizeBean, PersonResult.SUCCESS,
                                (String) data);
                    } else if (BaseApiResult.RESULT_RECOGNIZE_ONLY_FACE == result) {
                        processResult(mRecognizeBean, PersonResult.Recognize.RECOGNIZE_ONLY_FACE,
                                (String) data);
                    } else {
                        processResult(mRecognizeBean, PersonResult.Recognize.RECOGNIZE_FAILED,
                                data != null ? (String) data : "recognize failed");
                    }
                    updateRecognizeState(RecognizeState.IDLE);
                }
                break;
            case DELETE_FACE_LIST:
                if (isAccountServiceEnable()) {
                    mDataHelper.deleteFaceList(mDeleteFaceList);
                }
                break;
            default:
                break;
        }

    }

    private void startRegisterTask(RequestBean bean) {
        Log.d(TAG, "startRegisterTask bean: " + bean);
        if (TextUtils.isEmpty(bean.getName())) {
            processResult(bean, PersonResult.Register.REGISTER_NAME_EMPTY,
                    "name empty");
            return;
        }
        if (isAccountServiceEnable() && mDataHelper.checkNameExist(bean.getName())) {
            processResult(bean, PersonResult.Register.REGISTER_NAME_DUPLICATE,
                    "name duplicate");
            return;
        }
        updateRegisterState(RegisterState.REGISTERING);
        mRegisterBean = bean;
        if (!bean.isByPic()) {
            mApiProxy.registerById(mRegisterBean.getPersonId(), mRegisterBean.getName(),
                    false);
        } else {
            mApiProxy.registerByPic(mRegisterBean.getPicturePath(), mRegisterBean.getName(),
                    false);
        }
    }

    private void startReRegisterTask(RequestBean bean) {
        Log.d(TAG, "startReRegisterTask bean: " + bean);
        if (TextUtils.isEmpty(bean.getUserId())) {
            processResult(bean, PersonResult.ReRegister.RE_REGISTER_USER_ID_EMPTY,
                    "userId empty");
            return;
        }

        UserBean userBean = mDataHelper.getUserByUserId(bean.getUserId());
        if (null == userBean) {
            processResult(bean, PersonResult.ReRegister.RE_REGISTER_USER_ID_NOT_EXIST,
                    "userId not exist");
            return;
        }

        if (null != mDataHelper.getFaceByUserId(userBean.getUserId())) {
            processResult(bean, PersonResult.ReRegister.RE_REGISTER_USER_ID_HAS_FACE_PAIR,
                    mGson.toJson(mDataHelper.getMemberByFaceId(bean.getUserId())));
            return;
        }
        updateRegisterState(RegisterState.RE_REGISTERING);
        mRegisterBean = bean;
        if (!bean.isByPic()) {
            mApiProxy.registerById(mRegisterBean.getPersonId(), userBean.getNickName(),
                    true);
        } else {
            mApiProxy.registerByPic(mRegisterBean.getPicturePath(), userBean.getNickName(),
                    true);
        }
    }

    private void startRecognizeTask(RequestBean bean) {
        Log.d(TAG, "startRecognizeTask bean: " + bean);
        updateRecognizeState(RecognizeState.RECOGNIZING);
        mRecognizeBean = bean;
        if (!bean.isByPic()) {
            mApiProxy.recognizeById(mRecognizeBean.getPersonId());
        } else {
            mApiProxy.recognizeByPic(mRecognizeBean.getPicturePath());
        }
    }

    private void processResult(RequestBean bean, int result, String message) {
        if (bean.getListener() != null) {
            try {
                bean.getListener().onResultWithExtraData(result, message, mGson.toJson(bean));
                bean.getListener().onResult(result, message);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    public synchronized void setActionListener(IActionListener listener) {
        Log.i(TAG, "setActionListener:");
        if (mActionListener != null) {
            try {
                listener.onErrorWithExtraData(Definition.ACTION_RESPONSE_ALREADY_RUN, "", "");
                listener.onError(Definition.ACTION_RESPONSE_ALREADY_RUN, "");
            } catch (RemoteException e) {
                e.printStackTrace();
            }
            return;
        }
        Log.i(TAG, "setActionListener success");
        mActionListener = listener;
    }

    private synchronized void notifyStatusUpdate(int result, String data){
        if (mActionListener != null) {
            try {
                mActionListener.onStatusUpdateWithExtraData(result, data, "");
                mActionListener.onStatusUpdate(result, data);
            } catch (RemoteException e) {
                Log.e(TAG, "action listener disconnect");
                mActionListener = null;
            }
        }
    }

    private synchronized void notifyPersonChanged(){
        if (mRemotePersonListener != null) {
            try {
                mRemotePersonListener.personChanged();
            } catch (RemoteException e) {
                Log.e(TAG, "person listener disconnect");
                releasePersonListener();
            }
        }
    }

    public synchronized void releaseActionListener() {
        Log.i(TAG, "releaseActionListener");
        if (mActionListener != null) {
            try {
                mActionListener.onResultWithExtraData(Definition.ACTION_RESPONSE_STOP_SUCCESS, "", "");
                mActionListener.onResult(Definition.ACTION_RESPONSE_STOP_SUCCESS, "");
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        mActionListener = null;
    }

    public void setPersonListener(IPersonListener listener) {
        Log.i(TAG, "setPersonListener");
        mRemotePersonListener = listener;
    }

    public synchronized void releasePersonListener() {
        if (mRemotePersonListener != null) {
            Log.i(TAG, "releasePersonListener");
            mRemotePersonListener = null;
        }
    }

    private void updateAllPersonList(@NonNull List<Person> personList) {
        if (System.currentTimeMillis() - lastPrintTime > 1000) {
            lastPrintTime = System.currentTimeMillis();
            RLog.pruneLog(TAG+ "_PersonInfo", "updateAllPersonList personList: " + personList, 1000);
        }
        mAllPersonList = personList;
        mOnlyFacesPersonList.clear();
        mCompleteFacesPersonList.clear();
        mOnlyBodyPersonList.clear();
        mFocusPerson = null;
        if (personList.size() > 0) {
            mHandler.removeMessages(MSG_PERSON_DISAPPEAR);
            mHandler.sendEmptyMessageDelayed(MSG_PERSON_DISAPPEAR, TIMEOUT_PERSON_DISAPPEAR);
            for (Person person : personList) {
                if (person.isWithBody()) {
                    if (!person.isWithFace()) {
                        person.setId(-1);
                    }
                    mOnlyBodyPersonList.add(person);
                }
                if (person.isWithFace()) {
                    mOnlyFacesPersonList.add(person);
                    if (person.getId() >= 0 && !person.isOtherFace()) {
                        mCompleteFacesPersonList.add(person);
                    }

                    double distance = person.getDistance();
                    double faceAngelX = person.getFaceAngleX();
                    if (mFocusPerson == null || mFocusPerson.getDistance() > distance) {
                        mFocusPerson = person;
                    } else if (person.getDistance() == distance
                            && Math.abs(person.getFaceAngleX()) > Math.abs(faceAngelX)) {
                        mFocusPerson = person;
                    }
                }
            }
        }
        if (mRemotePersonListener != null) {
            try {
                mRemotePersonListener.personChanged();
            } catch (RemoteException e) {
                Log.e(TAG, "person listener disconnect");
                releasePersonListener();
            }
        }
        mCore.getFaceAngleManager().updatePersonDetailInfo(mFocusPerson);
    }

    public List<Person> getAllPersonList(double maxDistance) {
        return PersonUtils.getPersonList(mAllPersonList, maxDistance);
    }

    /**
     * 获取
     *
     * @param maxDistance
     * @return
     */
    public List<Person> getOnlyFacesPersonList(double maxDistance) {
        return PersonUtils.getPersonList(mOnlyFacesPersonList, maxDistance);
    }

    public List<Person> getCompleteFacesPersonList(double maxDistance) {
        return PersonUtils.getPersonList(mCompleteFacesPersonList, maxDistance);
    }

    public List<Person> getOnlyBodyPersonList(double maxDistance) {
        return PersonUtils.getPersonList(mOnlyBodyPersonList, maxDistance);
    }

    public Person getFocusPerson() {
        return mFocusPerson;
    }

    private void updatePersonState(PersonState newState) {
        Log.d(TAG, "updatePersonState newState: " + newState
                + ", mPersonState: " + mPersonState);
        mPersonState = newState;
    }

    private void updateRegisterState(RegisterState newState) {
        Log.d(TAG, "updateRegisterState newState: " + newState
                + ", mRegisterState: " + mRegisterState);
        mRegisterState = newState;
    }

    private void updateRecognizeState(RecognizeState newState) {
        Log.d(TAG, "updateRecognizeState newState: " + newState
                + ", mRecognizeState: " + mRecognizeState);
        mRecognizeState = newState;
    }

    public PersonHandler getHandler() {
        return mHandler;
    }

    private boolean isAccountServiceEnable() {
        return ConfigManager.getAccountConfig().isEnable();
    }

    public boolean isLocalRegisterEnable() {
        return ConfigManager.getAccountConfig().isLocalRegisterEnable();
    }
}
