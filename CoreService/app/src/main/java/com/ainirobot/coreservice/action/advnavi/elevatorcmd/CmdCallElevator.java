package com.ainirobot.coreservice.action.advnavi.elevatorcmd;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.bean.ElevatorState;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.utils.DelayTask;
import com.google.gson.Gson;

/**
 * Data: 2023/3/29 15:48
 * Author: wanglijing
 * <p>
 * 呼梯命令
 * * 成功 延迟继续呼梯 若是其他命令调用的则上报RESULT_OK
 * * 失败 释放电梯
 * * 超时 重试
 */
public class CmdCallElevator extends ElevatorCommand {

    private final Gson mGson;
    private CmdReleaseElevator cmdReleaseElevator;
    private Status status;
    private int lastFloor = -1;
    private int sameFloorCount = 0;
    private int statusTimeoutCount = 0;
    private final long WAIT_TIMER = 20 * 1000;

    private enum Status {
        IDLE,
        WAITING,
        CALLING,
        CALL_SUC,
        CALL_FAIL,
    }

    CmdCallElevator(String name, CmdType intentType, ElevatorCommandInterface commandInterface, ElevatorCommandListener listener) {
        super(name, intentType, commandInterface, listener);
        mGson = new Gson();
        initCmdType(CmdType.CALL_FLOOR);
    }


    private void updateStatus(Status status) {
        Log.d(TAG, "updateStatus " + this.status + " --> " + status);
        this.status = status;
    }

    @Override
    public void start() {
        super.start();
        updateStatus(Status.CALLING);
        Log.d(TAG, "call to " + getFloorIndex());
        callElevatorToFloor();
    }

    @Override
    public void onSuccess() {
        super.onSuccess();
        updateStatus(Status.IDLE);
        onResult(Definition.RESULT_OK, "call suc");
    }

    @Override
    public void onFail() {
        updateStatus(Status.CALL_FAIL);
        Log.d(TAG, "call elevator fail");
        //呼叫电梯失败并不需要释放电梯
//        if (null == cmdReleaseElevator) {
//            cmdReleaseElevator = new CmdReleaseElevator(TAG, intentType, commandInterface, releaseListener);
//        }
//        Log.d(TAG, "call elevator fail, try release elevator");
//        cmdReleaseElevator.start();
    }

    @Override
    public void stop() {
        super.stop();
        updateStatus(Status.IDLE);
        if (null != cmdReleaseElevator && cmdReleaseElevator.isAlive) {
            cmdReleaseElevator.stop();
        }
    }

    private final ElevatorCommandListener releaseListener = new ElevatorCommandListener() {
        @Override
        public void onStatusUpdate(int status, String data, String extraData) {
            onCmdStatusUpdate(status, data, extraData);
        }

        @Override
        public void onFinish(int status, String data) {
            if (status == Definition.RESULT_OK) {
                Log.d(TAG, "release suc ,delay start call floor to " + getFloorIndex());
                start();
                return;
            }
            onResult(status, data);
        }
    };

    @Override
    protected void onRetry() {
        callElevatorToFloor();
    }

    @Override
    public void cmdResponse(String command, String result, String extraData) {
        if (!isAlive) {
            Log.d(TAG, "cmdResponse call cmd not alive");
            return;
        }
        if (TextUtils.equals(command, Definition.CMD_CONTROL_CALL_ELEVATOR)) {
            callCmdResponse(result, extraData);
            return;
        }
        if (null != cmdReleaseElevator) {
            cmdReleaseElevator.cmdResponse(command, result, extraData);
        }
    }

    private void callCmdResponse(String result, String extraData) {
        Log.d(TAG, "call cmd response [ " + result + " ]  extraData:" + extraData);
        switch (result) {
            case Definition.OPENING:
                onSuccess();    //呼梯流程完成，电梯到达
                return;
            case Definition.FAILED:
                onFail();
                return;
            case Definition.ARRIVED://仅指到达目标楼层，门还未开，兼容呼梯指令没有应答的情况
            case Definition.SUCCEED://仅指指令应答成功，电梯移动状态未知，
                if (status == Status.CALLING) {
                    statusTimeoutCount = 0;
                    onCmdStatusUpdate(Definition.STATUS_CALL_ELEVATOR_AND_WAIT, "call elevator suc", extraData);
                    updateStatus(Status.CALL_SUC);
                }
                updateElevatorState(extraData);
                break;

            case Definition.PARAMS_TIMEOUT:
                onFail();
                break;
        }
        //招商项目中，楼层状态返回信息丢失，不进行校验
//        if (intentType == CmdType.OPEN_DOOR && isOtherFloor(extraData)) {
//            onResult(Definition.ERROR_OPEN_ELEVATOR_DOOR_FAILED, "is other floor");
//        }
    }

    /**
     * 上报当前电梯的楼层映射
     *
     * @param extraData ElevatorState对象
     */
    private void updateElevatorState(String extraData) {
        try {
            ElevatorState state = mGson.fromJson(extraData, ElevatorState.class);
//            updateFloorStatus(state.getFloor());
            //上报上去的是楼层映射
            onCmdStatusUpdate(Definition.STATUS_ELEVATOR_FLOOR_INFO, String.valueOf(state.getFloor()), extraData);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //TODO 楼层信息一直不变处理
    private void updateFloorStatus(int floor) {
        if (lastFloor == floor) {
            sameFloorCount++;
        } else {
            sameFloorCount = 0;
        }
        lastFloor = floor;
    }

    //TODO 状态长时间不更新处理
    private void restartWaitTimer() {
        DelayTask.cancel(WAIT_TIMER);
        DelayTask.submit(WAIT_TIMER, new Runnable() {
            @Override
            public void run() {


            }
        }, WAIT_TIMER);
    }

    private boolean isOtherFloor(String extraData) {
        try {
            ElevatorState state = mGson.fromJson(extraData, ElevatorState.class);
            //和当前呼梯的楼层映射不同了，说明电梯已经去其他楼层了
            return state.getFloor() != getFloorIndex();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}
