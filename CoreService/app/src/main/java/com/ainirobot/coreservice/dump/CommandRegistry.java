package com.ainirobot.coreservice.dump;

import com.ainirobot.coreservice.bean.Command;
import com.ainirobot.coreservice.client.Definition;

import java.util.ArrayList;
import java.util.List;

public class CommandRegistry {
    private static final String TAG = "CommandRegistry";

    private CommandRegistry() {
    }

    /**
     * 快照支持指令
     */
    private static List<Command> sDumpCommands;

    static {
        sDumpCommands = new ArrayList<Command>() {
            {
                add(new Command(Definition.CMD_DUMP_START_RECORD, 2 * Definition.SECOND));
                add(new Command(Definition.CMD_DUMP_STOP_RECORD, 2 * Definition.SECOND));
                add(new Command(Definition.CMD_DUMP_GET_KEY_FRAME, 2 * Definition.SECOND));
            }
        };
    }

    public static List<Command> getDumpCommands() {
        return sDumpCommands;
    }

}
