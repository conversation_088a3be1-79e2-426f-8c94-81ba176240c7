/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.data.core;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.provider.BaseColumns;
import android.text.TextUtils;

import com.ainirobot.coreservice.client.actionbean.Person;
import com.ainirobot.coreservice.data.provider.BaseDataHelper;
import com.ainirobot.coreservice.data.provider.Column.Constraint;
import com.ainirobot.coreservice.data.provider.Column.DataType;
import com.ainirobot.coreservice.data.provider.SQLiteTable;

import java.util.ArrayList;
import java.util.List;

public class PersonData<PERSON>elper extends BaseDataHelper {

    public static final SQLiteTable TABLE = new SQLiteTable(PersonField.TABLE_NAME,
            DataProvider.AUTHORITY, true)
            .addColumn(PersonField.REMOTE_ID, DataType.TEXT)
            .addColumn(PersonField.PERSON_NAME, DataType.TEXT)
            .addColumn(PersonField.PERSON_IDENTITY, DataType.INTEGER)
            .addColumn(PersonField.REGISTER_NAME, Constraint.UNIQUE, DataType.TEXT)
            .addColumn(PersonField.REGISTER_TYPE, DataType.INTEGER)
            .addColumn(PersonField.REGISTER_TIME, DataType.INTEGER);

    public PersonDataHelper(Context context) {
        super(context);
    }

    @Override
    public Uri getContentUri() {
        return TABLE.getContentUri();
    }

    public Person insert(Person person) {
        ContentValues values = getContentValues(person);
        Uri uri = insert(values);

        String newId = uri.getPathSegments().get(1);
        person.setPersonId(Integer.parseInt(newId));
        return person;
    }

    public boolean delete(int id) {
        if (id < 0) {
            return false;
        }
        int row = delete(PersonField._ID + "=?", new String[]{String.valueOf(id)});
        return row > 0;
    }

    public boolean delete(String registerName) {
        if (TextUtils.isEmpty(registerName)) {
            return false;
        }

        int row = delete(PersonField.REGISTER_NAME + "=?", new String[]{registerName});
        return row > 0;
    }


    public boolean deleteAllPerson() {
        int row = delete(null, null);
        return row > 0;
    }

    public void update(Person person) {
        if (person == null) {
            return;
        }

        ContentValues values = getContentValues(person);
        update(values, PersonField._ID + "=?", new String[]{String.valueOf(person.getPersonId())});
    }

    public Person getPersonByRemoteId(String remoteId) {
        Cursor cursor = query(PersonField.REMOTE_ID + "=?", new String[]{remoteId});
        return parseOneCursor(cursor);
    }

    public Person getPerson(String registerName) {
        if (TextUtils.isEmpty(registerName)) {
            return null;
        }

        Cursor cursor = query(PersonField.REGISTER_NAME + "=?", new String[]{registerName});
        return parseOneCursor(cursor);
    }

    public Person getPerson(int id) {
        if (id < 0) {
            return null;
        }

        Cursor cursor = query(PersonField._ID + "=?", new String[]{String.valueOf(id)});
        return parseOneCursor(cursor);
    }

    private ContentValues getContentValues(Person person) {
        ContentValues values = new ContentValues();
        values.put(PersonField.REMOTE_ID, person.getRemoteId());
        values.put(PersonField.PERSON_NAME, person.getName());
        values.put(PersonField.PERSON_IDENTITY, person.getRole());
        values.put(PersonField.REGISTER_NAME, person.getRegisterName());
        values.put(PersonField.REGISTER_TYPE, person.getRegisterType());
        values.put(PersonField.REGISTER_TIME, person.getRegisterTime());
        return values;
    }

    private Person parseOneCursor(Cursor cursor) {
        List<Person> persons = parseCursor(cursor);
        if (persons.size() > 0) {
            return persons.get(0);
        }
        return null;
    }

    private List<Person> parseCursor(Cursor cursor) {
        List<Person> persons = new ArrayList<>();
        if (cursor == null) {
            return persons;
        }

        while (cursor.moveToNext()) {
            Person person = new Person();
            int id = cursor.getInt(cursor.getColumnIndex(PersonField._ID));
            String name = cursor.getString(cursor.getColumnIndex(PersonField.PERSON_NAME));
            String remoteId = cursor.getString(cursor.getColumnIndex(PersonField.REMOTE_ID));
            int registerType = cursor.getInt(cursor.getColumnIndex(PersonField.REGISTER_TYPE));
            long registerTime = cursor.getLong(cursor.getColumnIndex(PersonField.REGISTER_TIME));


            String registerName = cursor.getString(cursor.getColumnIndex(PersonField.REGISTER_NAME));

            person.setName(name);
            person.setPersonId(id);
            person.setRemoteId(remoteId);
            person.setRegisterType(registerType);
            person.setRegisterTime(registerTime);

            person.setRegisterName(registerName);
            persons.add(person);
        }
        cursor.close();
        return persons;
    }

    public static final class PersonField implements BaseColumns {
        static final String TABLE_NAME = "person";

        static final String REMOTE_ID = "remote_id";
        static final String PERSON_NAME = "person_name";
        static final String PERSON_IDENTITY = "person_identity";
        static final String REGISTER_TYPE = "register_type";
        static final String REGISTER_TIME = "register_time";
        static final String REGISTER_NAME = "register_name";
    }
}
