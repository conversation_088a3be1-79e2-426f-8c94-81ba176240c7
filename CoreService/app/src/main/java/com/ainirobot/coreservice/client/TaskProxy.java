package com.ainirobot.coreservice.client;

import com.ainirobot.coreservice.bean.Task;
import com.ainirobot.coreservice.bean.TaskEvent;

public class TaskProxy {

    private Task mTask;
    public TaskProxy(){
        mTask = new Task();
    }

    public int getStatus() {
        return mTask.getStatus();
    }

    public void setStatus(int status) {
        this.mTask.setStatus(status);
    }

    public int getMode() {
        return mTask.getMode();
    }

    public void setMode(int mode) {
        this.mTask.setMode(mode);
    }

    public String getType() {
        return mTask.getType();
    }

    public void setType(String type) {
        this.mTask.setType(type);
    }

    public String getName() {
        return mTask.getName();
    }

    public void setName(String name) {
        this.mTask.setName(name);
    }

    public TaskEvent[] getSubTaskList() {
        return mTask.getSubTaskList();
    }

    public void setSubTaskList(TaskEvent[] subTaskList) {
        this.mTask.setSubTaskList(subTaskList);
    }

    public void setTaskId(String taskId) {
        this.mTask.setTaskId(taskId);
    }

    public String getTaskId() {
        return mTask.getTaskId();
    }

    protected Task generateTask(){
        return this.mTask;
    }
}
