/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.client;

public class Definition {
    //----- package and action name for auto-launcher----//
    public static final String CORE_PACKAGE_NAME = "com.ainirobot.coreservice";
    public static final String CORE_SERVICE_NAME = "com.ainirobot.coreservice.service.CoreService";
    public static final String CORE_ACTION_NAME = "com.ainirobot.coreservice.SERVICE";

    public static final String AGENT_PACKAGE_NAME = "com.ainirobot.agentservice";
    public static final String AGENT_SERVICE_NAME = "com.ainirobot.agentservice.SpeechService";
    public static final String SPEECH_PACKAGE_NAME = "com.ainirobot.speechasrservice";
    public static final String SPEECH_SERVICE_NAME = "com.ainirobot.speechasrservice.SpeechService";
    public static final String SPEECH_ACTION_NAME = "com.ainirobot.speechasrservice.SERVICE";

    public static final String NAVI_PACKAGE_NAME = "com.ainirobot.navigationservice";
    public static final String NAVI_SERVICE_NAME =
            "com.ainirobot.navigationservice.NavigationService";
    public static final String NAVI_ACTION_NAME = "com.ainirobot.navigationservice.SERVICE";

    public static final String HEADER_PACKAGE_NAME = "com.ainirobot.headservice";
    public static final String HEADER_SERVICE_NAME =
            "com.ainirobot.headservice.HeadService";
    public static final String HEADER_ACTION_NAME = "com.ainirobot.headservice.SERVICE";

    public static final String MODULE_PACKAGE_NAME = "com.ainirobot.moduleapp";
    public static final String MODULE_SERVICE_NAME =
            "com.ainirobot.moduleapp.service.ModuleService";
    public static final String MODULE_ACTION_NAME = "com.ainirobot.moduleapp.SERVICE";
    public static final String MODULE_CLASS_NAME = "com.ainirobot.moduleapp.MainActivity";

    public static final String REMOTECONTROL_PACKAGE_NAME = "com.ainirobot.remotecontrolservice";
    public static final String REMOTECONTROL_SERVICE_NAME =
            "com.ainirobot.remotecontrolservice.service.RemoteControlService";
    public static final String REMOTECONTROL_ACTION_NAME = "com.ainirobot.remotecontrolservice.SERVICE";

    public static final String CAN_PACKAGE_NAME = "com.ainirobot.canservice";
    public static final String CAN_SERVICE_NAME =
            "com.ainirobot.canservice.service.CanService";
    public static final String CAN_ACTION_NAME = "com.ainirobot.canservice.SERVICE";

    public static final String VISION_PACKAGE_NAME = "com.ainirobot.visionsdk";
    public static final String VISION_SERVICE_NAME =
            "com.ainirobot.visionsdk.service.VisionSdkService";
    public static final String VISION_ACTION_NAME = "com.ainirobot.visionsdk.SERVICE";

    public static final String UPLOAD_PACKAGE_NAME = "com.ainirobot.uploadservice";
    public static final String UPLOAD_SERVICE_NAME =
            "com.ainirobot.uploadservice.service.RobotUploadService";
    public static final String UPLOAD_ACTION_NAME = "com.ainirobot.uploadservice.SERVICE";

    public static final String ORION_UPLOAD_PACKAGE_NAME = "com.ainirobot.orionuploadservice";
    public static final String ORION_UPLOAD_SERVICE_NAME = "com.ainirobot.orionuploadservice.service.UploadService";
    public static final String ORION_UPLOAD_ACTION_NAME = "com.ainirobot.orionuploadservice.SERVICE";

    public static final String ACTION_EXTERNAL_SERVICE = "com.ainirobot.robotos.SERVICE";

    public static final String SETTINGS_PACKAGE_NAME = "com.ainirobot.settings";
    public static final String SETTINGS_CLASS_NAME = "com.ainirobot.settings.FirstSetActivity";

    public static final String HOME_PACKAGE_NAME = "com.ainirobot.home";
    public static final String MAPTOOL_PACKAGE_NAME = "com.ainirobot.maptool";
    public static final String MAPTOOL_PACKAGE_NAME_LAUNCHER_CLASS = "com.ainirobot.maptool.activity.GuideInitActivity";

    public static final String DAEMON_PACKAGE_NAME = CORE_PACKAGE_NAME + ":Daemon";

    public static final String UI_ACTION_NAME = "com.ainirobot.robotui.activity.START";

    public static final String FIRST_CONFIG_PACKAGE = "com.ainirobot.firstconfig";
    public static final String FIRST_CONFIG_ACTIVITY = "com.ainirobot.firstconfig.ui.activity.FirstConfigActivity";
    public static final String FIRST_CONFIG_ACTION_START_CREATE_MAP = "first_config_action_start_create_map";
    public static final String FIRST_CONFIG_INSPECT_COMPLETE = "first_config_inspect_complete";
    public static final String FIRST_CONFIG_ACTION_COMPLETE = "first_config_action_complete";
    public static final String FIRST_CONFIG_ACTION_COMPLETE_PERMISSION = "first_config_action_complete_permission";
    public static final String FIRST_CONFIG_FLAG = "first_config_flag";
    public static final String FIRST_CONFIG_IS_START_MAPPING = "first_config_is_start_mapping";

    public static final long SECOND = 1000L;
    public static final long MINUTE = SECOND * 60L;
    public static final long HOUR = MINUTE * 60L;
    public static final long DAY = HOUR * 24L;
    public static final long WEEK = DAY * 7;
    public static final long MONTH = WEEK * 4;
    public static final String START_CHARGE_PILE_POSE = "充电桩";
    public static final String START_BACK_CHARGE_POSE = "回充点";
    public static final String LOCATE_POSITION_POSE = "定位点";
    public static final String RECEPTION_POSITION_POSE = "接待点";

    /**
     * 电梯名称分隔符
     * 电梯中心点位名称：电梯名-电梯中心
     * 电梯口点位名称：电梯名-电梯口
     */
    public static final String ELEVATOR_NAME_SEPARATOR = "-";

    public static final String ELEVATOR_CENTER_POSE = "电梯中心";
    public static final String ELEVATOR_GATE_POSE = "电梯门";  //乘梯下沉后废弃
    public static final String ELEVATOR_ENTER_POSE = "电梯口";

    public static final String GATE_ENTER = "闸机入口";
    public static final String GATE_OUTER = "闸机出口";

    /**
     * 特殊点点位类型
     */
    public static final int NORMAL_POINT_TYPE = 0; // 普通点位
    public static final int CHARGING_POINT_TYPE = 1; // 回充点
    public static final int CHARGING_POLE_TYPE = 2; // 充电桩
    public static final int STAND_BY_POINT_TYPE = 3; // 待机点
    public static final int POSITIONING_POINT_TYPE = 4; // 定位点
    public static final int ELEVATOR_CENTER_TYPE = 5; // 电梯中心
    public static final int ELEVATOR_GATE_TYPE = 6; // 电梯门
    public static final int ELEVATOR_ENTRANCE_TYPE = 7; // 电梯口
    public static final int RECEPTION_POINT_TYPE = 8; // 接待点
    public static final int GATE_ENTER_TYPE = 9; // 闸机入口
    public static final int GATE_OUTER_TYPE = 10; // 闸机出口
    public static final int SPECIAL_PLACE_HIGH_PRIORITY = 0;


    public static final String CLIENT_PACKAGE_NAME = "client_package_name";

    //--------- HW functions definition-----------------//
    public static final int HW_FUNCTION_DEFAULT = 0;
    public static final int HW_FUNCTION_NAVI = 1;
    public static final int HW_FUNCTION_HEAD = 2;
    public static final int HW_FUNCTION_ARM = 3;
    public static final int HW_FUNCTION_STM32 = 4;
    public static final int HW_FUNCTION_REMOTE = 5;
    public static final int HW_FUNCTION_CAN = 6;
    public static final int HW_FUNCTION_OTA = 7;
    public static final int HW_FUNCTION_SPEECH = 10;
    public static final int HW_COMMON_ID = 11;

    //------------ Status --------------//
    public static final String STATUS_POSE = "navi_pose";
    public static final String STATUS_PUSH_EXCEED_DISTANCE = "navi_push_exceed_distance";
    public static final String STATUS_MAP = "navi_map";
    public static final String STATUS_SPEED = "navi_speed";
    public static final String STATUS_STATISTIC = "navi_statistic";
    public static final String STATUS_MAP_LIST = "navi_map_list";
    public static final String STATUS_EVENT = "navi_event";
    public static final String STATUS_ERROR_LOG = "status_error_log";
    public static final String STATUS_PACK_LOG_END = "status_pack_log_end";
    public static final String STATUS_TAKE_SNAPSHOT_END = "status_take_snapshot_end";
    public static final String STATUS_REQUEST_FINISHED = "request_finished";
    public static final String STATUS_MODULE_CHANGE = "status_module_change";
    public static final String STATUS_OTA_PROGRESS = "status_ota_progress";
    public static final String STATUS_SCANNER = "status_scanner";
    public static final String STATUS_HEAD_QR_CODE_SN = "status_head_qr_code_sn";
    public static final String STATUS_HEAD = "status_head";
    public static final String STATUS_HEAD_COUNT = "status_head_count";
    public static final String STATUS_BODY = "status_body";
    public static final String STATUS_HEAD_HORIZONTAL = "status_head_horizontal";
    public static final String STATUS_HEAD_VERTICAL = "status_head_vertical";
    public static final String STATUS_REMOTE_POST = "status_remote_post";
    public static final String STATUS_REMOTE_APP_DATA = "status_remote_app_data";
    public static final String STATUS_EMERGENCY = "status_emergency";
    public static final String STATUS_PROCESS_STATE = "status_process_state";
    public static final String STATUS_POSE_ESTIMATE = "status_pose_estimate";
    public static final String STATUS_AVOID_STOPPING = "status_avoid_stopping";//动态避停
    public static final String STATUS_STATIC_AVOID_STOPPING = "status_static_avoid_stopping";//静态避停
    public static final String STATUS_SET_CHARGE_PILE = "status_set_charge_pile";
    public static final String STATUS_SET_LOCATE_PILE = "status_set_locate_pile";
    public static final String STATUS_SWITCH_MAP = "status_switch_map";
    public static final String STATUS_MODULE_DATA_UPDATE = "status_module_data_update";
    public static final String STATUS_MODULE_DOWNLOAD_UPDATE = "status_module_download_update";
    public static final String STATUS_OTA_WARNING = "status_ota_warning";
    public static final String STATUS_WARNING_REPORT = "status_warning_report";
    public static final String STATUS_RELOCATE_EVENT = "relocate_event";
    public static final String STATUS_CHASSIS_COMMAND = "status_chassis_command";
    public static final String STATUS_CHASSIS_EVENT = "status_chassis_event";
    public static final String STATUS_CHASSIS_REMOTE = "status_chassis_remote";
    public static final String STATUS_PSB_ERROR = "status_psb_error";
    public static final String STATUS_UPDATE_CREATE_MAP_PROCESS = "status_update_create_map_process";
    public static final String STATUS_BMS_WARNING = "bms_warning";
    public static final String STATUS_RADAR = "status_radar";
    public static final String STATUS_OBSTACLE_INFO = "status_obstacle_info";
    public static final String STATUS_MANUAL_DOOR = "status_manual_door";
    public static final String STATUS_UPDATE_CHANNEL = "status_update_channel";
    public static final String STATUS_UPDATE_MQTTINFO = "status_update_mqttinfo";
    public static final String STATUS_ODOMETER_REPORT = "status_odometer_report";
    public static final String STATUS_MAP_DRIFT_WARNING = "status_map_drift_warning_report";
    public static final String STATUS_MAPPING_POSE_UPDATE = "status_mapping_pose_update";
    public static final String STATUS_MAP_OUTSIDE = "status_map_outside_report";
    public static final String STATUS_ROBOT_BEING_PUSHED = "status_robot_being_pushed";
    public static final String STATUS_WHEEL_OVER_CURRENT = "status_wheel_over_current";
    public static final String STATUS_MULTI_FUNCTION_SWITCH = "status_multi_function_switch";
    public static final String STATUS_MULTI_LANGUAGE_LIST = "status_multi_language_list";
    public static final String STATUS_MULTI_ROBOT_ERROR = "status_multi_robot_error";
    public static final String STATUS_MULTI_FLOOR_CONFIG_UPDATE = "status_multi_floor_config_update";
    public static final String STATUS_NAVI_SENSOR_EXCEPTION = "status_navi_sensor_exception";
    public static final String NAVI_SENSOR_STATE_NORMAL = "navi_sensor_state_normal";
    public static final String NAVI_SENSOR_STATE_ERROR = "navi_sensor_state_error";
    public static final String NAVI_SENSOR_STATE_SLIGHT = "navi_sensor_state_slight";
    public static final String STATUS_NAVI_LOAD_MAP = "status_navi_load_map";
    public static final String STATUS_CHARGE_AREA_CONFIG_UPDATE = "status_charge_area_update";

    public static final String STATUS_CHASSIS_DISCONNECT_EVENT = "status_chassis_disconnect_event";
    public static final String STATUS_CHANGE_MAP_LOCATION_LOST_EVENT = "status_change_map_location_lost_event";
    public static final String STATUS_STARTUP_LOCATION_LOST_EVENT = "status_startup_location_lost_event";
    public static final String STATUS_OUT_MAP_LOCATION_LOST_EVENT = "status_out_map_location_lost_event";
    public static final String STATUS_NO_MAP_LOCATION_LOST_EVENT = "status_no_map_location_lost_event";

    public static final String STATUS_CHARGING_PILE_RELOCATION_EVENT = "status_charging_pile_relocation_event";
    public static final String STATUS_STARTUP_RELOCATION_EVENT = "status_startup_relocation_event";
    public static final String STATUS_MAP_TOOL_RELOCATION_EVENT = "status_map_tool_relocation_event";
    public static final String STATUS_VISION_RELOCATION_EVENT = "status_vision_relocation_event";
    public static final String STATUS_REMOTE_RELOCATION_EVENT = "status_remote_relocation_event";

    public static final String STATUS_HW_CONNECTED = "connected";
    public static final String STATUS_HW_DISCONNECTED = "disconnected";
    public static final String STATUS_REMOTE_CONFIG_UPDATE = "status_remote_config_update";
    public static final String STATUS_FILE_UPLOAD = "status_file_upload";
    public static final String STATUS_FILE_UPLOAD_RESULT = "status_file_upload_result";

    public static final String STATUS_MAP_CREATE = "status_map_create";
    public static final String STATUS_MAP_UPDATE = "status_map_update";
    public static final String STATUS_MAP_DELETE = "status_map_delete";

    public static final String STATUS_LOGIN_REMOTE = "status_login_remote";
    public static final String STATUS_ROBOT_HAS_BEING_PUSHED = "status_robot_has_being_pushed";
    public static final String STATUS_BATTERY = "status_battery";
    public static final String STATUS_XD_DRY = "status_xd_dry";
    public static final String STATUS_TASK_CHANGE = "status_task_change";
    public static final String STATUS_BACKGROUND_TASK_CHANGE = "status_background_task_change";
    public static final String STATUS_TASK_EVENT_CHANGE = "status_task_event_change";
    public static final String STATUS_MULTIPLE_ROBOT_WORKING = "status_multiple_robot_working";
    public static final String STATUS_MULTIPLE_ROBOT_NO_DATA = "status_multiple_robot_no_data";
    public static final String STATUS_MAPPING_VISION_INFO_UPDATE = "status_mapping_vision_info_update";
    public static final String STATUS_UVC_CONTINUE_CLASSIFY = "status_uvc_continue_classify";
    public static final String STATUS_ELEVATOR = "status_elevator";
    public static final String ARRIVED = "arrived";
    public static final String OPENING = "opening";
    public static final String STATUS_SETTING_UPLOAD_RESULT = "status_setting_upload_result";
    public static final String STATUS_ANTI_COLLISION_STRIP = "status_anti_collision_strip";
    public static final String STATUS_GATE = "status_gate";
    public static final String STATUS_EXPOSURE = "status_exposure";
    public static final String STATUS_SPEAKER = "status_speaker";

    /**
     * 底盘上报上来的 雷达数据的状态监听
     */
    public static final String STATUS_LINE_DATA = "status_line_data";

    /**
     * 工厂多机Lora测试收发数据信息。
     */
    public static final String STATUS_FACTORY_TEST_LORA_DATA = "status_factory_test_lora_data";

    /**
     * language.db系统语言数据更新，上报本地和服务端都支持的语言信息
     */
    public static final String STATUS_SUPPORT_LANGUAGE_CHANGE = "status_support_language_change";
    /**
     * language.db发言人列表更新状态，上报所有发言人列表
     */
    public static final String STATUS_SUPPORT_SPOKESMAN_CHANGE = "status_support_spokesman_change";
    /**
     * RemoteControlService 上报语言列表信息
     */
    public static final String STATUS_GET_REMOTE_LANGUAGE_SUCCESS = "status_get_remote_language_success";
    /**
     * SpeechService 上报发言人列表信息
     */
    public static final String STATUS_GET_REMOTE_SPOKESMAN_SUCCESS = "status_get_remote_spokesman_success";
    /**
     * NavigationService 上报naviToGoal的异常信息，并由Home给出Toast提示
     */
    public static final String STATUS_NAVI_TO_GOAL_EXCEPTION = "status_navi_to_goal_exception";
    /**
     * NavigationService 上报托盘摄像头识别变化后照片的上报．
     */
    public static final String STATUS_UVC_IMAGE_REPORT = "status_uvc_image_report";

    public static final String STATUS_RELOAD_MAP = "status_reload_map";

    public static final String SUCCEED = "succeed";
    public static final String FAILED = "failed";
    public static final String FAILED_CREATING_MAP = "Creating map";
    public static final String FAILED_RADAR_OPENING = "Is opening";
    public static final String FAILED_RADAR_CLOSING = "Is closing";
    public static final String PARAMS_ERROR = "params error";

    public static final String PATROL_FINISHED = "finished";
    public static final String PATROL_ABORTED = "aborted";
    public static final String PATROL_CANCELED = "canceled";

    /*error*/
    public static final String ERROR_ORIGIN_NOT_CONNECT = "origin not connect";
    public static final String ERROR_REMOTE_ERROR = "aidl error";
    public static final String ERROR_INVALID_PROTO = "invalid proto";
    public static final String ERROR_START_CREATE_MAP = "start create map error";
    public static final String ERROR_START_CREATE_MAP_INVALID_MAP_NAME = "invalid map name";
    public static final String ERROR_START_CREATE_MAP_NOT_IN_WORKING_MODE_CREATE_MAP = "not create map working mode";
    public static final String ERROR_START_CREATE_MAP_NO_FEATURE_SPACE = "storage not enough";
    public static final String ERROR_START_CREATE_MAP_GET_FEATURE_SPACE_FAILED = "get feature space failed";
    public static final String ERROR_MOTION_AVOID_STOP = "Motion avoid stop";
    /*多层配置信息错误*/
    public static final String ERROR_ELEVATOR_INFO_NEED_MAP_NAME = "param need map name";   //多层配置缺少地图名
    public static final String ERROR_ELEVATOR_INFO_FLOOR_INDEX = "floorIndex needs to start from 1";   //多层配置中楼层映射应从1开始
    public static final String ERROR_ELEVATOR_INFO_NEED_ELEVATOR = "param need available elevator";   //多层配置缺少可用的电梯

    //------------ Relocation event type --------------//
    public static final String REMOTE_EVENT_RELOCATE = "relocate";
    public static final String REMOTE_EVENT_INFRARED = "infrared";
    public static final String REMOTE_EVENT_CLOSE = "close";

    //---------------- Exception ----------//
    public static final String HW_NAVI_ESTIMATE_LOST = "pose_estimate_lost";
    public static final String HW_NAVI_ESTIMATE_LOSE_CODE = "loseCode";
    public static final String HW_WHEEL_MOTOR_BLOCKED = "wheel_motor_blocked";
    public static final String HW_NAVI_ERROR_LOG = "bxm_tk1";
    public static final String HW_NAVI_PACK_LOG_END = "pack_log_end";
    public static final String HW_NAVI_TAKE_SNAPSHOT_END = "take_snapshot_end";

    //----------------- request/command list -------------------//
    public static final String CMD_SUPPORTED_ALL = "supported_command_all";
    public static final String CMD_GET_STATUS = "cmd_get_status";
    public static final String CMD_CANCEL_COMMAND = "cmd_cancel_command";
    //Navigation
    public static final String CMD_NAVI_TURN_BY_NAVIGATION = "cmd_navi_turn_by_navigation";
    public static final String CMD_NAVI_GO_LOCATION = "cmd_navi_go_location";
    public static final String CMD_NAVI_SET_LOCATION = "cmd_navi_set_location";
    public static final String CMD_NAVI_GET_LOCATION = "cmd_navi_get_location";
    public static final String CMD_NAVI_GET_LOCATION_HIGH_FREQUENCY = "cmd_navi_get_location_high_frequency";
    public static final String CMD_NAVI_IS_IN_NAVIGATION = "cmd_navi_is_in_navigation";
    public static final String CMD_NAVI_IS_IN_LOCATION = "cmd_navi_is_in_location";
    public static final String CMD_NAVI_SET_POSE_LOCATION = "cmd_navi_set_pose_location";
    public static final String CMD_NAVI_SET_POSE_ESTIMATE = "cmd_navi_set_pose_estimate";
    public static final String CMD_NAVI_SET_CONFIG = "cmd_navi_set_config";
    public static final String CMD_NAVI_GET_CONFIG = "cmd_navi_get_config";
    public static final String CMD_NAVI_START_CREATING_MAP = "cmd_navi_start_creating_map";
    public static final String CMD_NAVI_STOP_CREATING_MAP = "cmd_navi_stop_creating_map";
    public static final String CMD_NAVI_SWITCH_MAP = "cmd_navi_switch_map";
    public static final String CMD_NAVI_REMOVE_MAP = "cmd_navi_remove_map";
    public static final String CMD_NAVI_GET_PLACE_NAME = "cmd_navi_get_place_name";
    public static final String CMD_NAVI_GET_PLACE_LIST = "cmd_navi_get_place_list";
    public static final String CMD_NAVI_GET_INTERNATIONAL_PLACE_LIST = "cmd_navi_get_international_place_list";
    public static final String CMD_NAVI_GET_INTERNATIONAL_PLACE_LIST_FOR_REPORT = "cmd_navi_get_international_place_list_for_report";
    public static final String CMD_NAVI_GET_INTERNATIONAL_PLACE_LIST_INTER = "cmd_navi_get_international_place_list_inter";
    public static final String CMD_NAVI_UPDATE_PLACE_LIST = "cmd_navi_update_place_list";
    public static final String CMD_NAVI_GET_PLACE_LIST_WITH_NAME = "cmd_navi_get_place_list_with_name";
    public static final String CMD_NAVI_GET_PLACELIST_WITH_NAMELIST = "cmd_navi_get_placelist_with_namelist";
    public static final String CMD_NAVI_GO_POSITION = "cmd_navi_go_position";
    public static final String CMD_NAVI_GO_POSITION_BY_TYPE = "cmd_navi_go_position_by_type";
    public static final String CMD_NAVI_IS_ESTIMATE = "cmd_navi_is_estimate";
    public static final String CMD_NAVI_IS_HAS_VISIION = "cmd_navi_is_has_vision";
    public static final String CMD_NAVI_SAVE_ESTIMATE = "cmd_navi_save_estimate";
    public static final String CMD_NAVI_GO_DEFAULT_THETA = "cmd_navi_go_default_theta";
    public static final String CMD_NAVI_RESET_ESTIMATE = "cmd_navi_reset_estimate";
    public static final String CMD_NAVI_LOCATE_VISION = "cmd_navi_locate_vision";
    public static final String CMD_NAVI_SET_FIXED_ESTIMATE = "cmd_navi_set_fixed_estimate";
    public static final String CMD_NAVI_SET_FORCE_ESTIMATE = "cmd_navi_set_force_estimate";
    public static final String CMD_NAVI_SET_RELOCATION = "cmd_navi_set_relocation";
    public static final String CMD_NAVI_QUERY_MULTI_FLOOR_CONFIG = "cmd_navi_query_multi_floor_config";
    public static final String CMD_NAVI_INSERT_MULTI_FLOOR_CONFIG = "cmd_navi_insert_multi_floor_config";
    public static final String CMD_NAVI_UPDATE_MULTI_FLOOR_CONFIG = "cmd_navi_update_multi_floor_config";
    public static final String CMD_NAVI_REMOVE_MULTI_FLOOR_CONFIG = "cmd_navi_remove_multi_floor_config";
    //获得多层配置信息及对应地图的全部点位信息
    public static final String CMD_NAVI_GET_MULTI_FLOOR_CONFIG_AND_POSE = "cmd_navi_get_multi_floor_config_and_pose";
    //获得多层配置信息及对应地图的点位信息（过滤特殊点位）
    public static final String CMD_NAVI_GET_MULTI_FLOOR_CONFIG_AND_COMMON_POSE = "cmd_navi_get_multi_floor_config_and_common_pose";

    public static final String CMD_NAVI_WRITE_MULTI_ROBOT_EXTRA_DATA = "cmd_navi_write_multi_robot_extra_data";
    public static final String CMD_NAVI_QUERY_CHARGE_AREA_CONFIG = "cmd_navi_query_charge_area_config";
    public static final String CMD_NAVI_INSERT_CHARGE_AREA_CONFIG = "cmd_navi_insert_charge_area_config";
    public static final String CMD_NAVI_UPDATE_CHARGE_AREA_CONFIG = "cmd_navi_update_charge_area_config";
    public static final String CMD_NAVI_REMOVE_CHARGE_AREA_CONFIG = "cmd_navi_remove_charge_area_config";
    public static final String CMD_NAVI_REPORT_START_AVOID_OBSTACLE = "cmd_navi_report_start_avoid_obstacle";
    public static final String CMD_NAVI_REPORT_END_AVOID_OBSTACLE = "cmd_navi_report_end_avoid_obstacle";
    public static final String CMD_NAVI_REPORT_POSITION = "cmd_navi_report_position";
    public static final String CMD_NAVI_REPORT_VELOCITY_CHANGE = "cmd_navi_report_navigation_velocity_change";

    public static final String CMD_NAVI_GET_NAVIGATION_ANGLE_SPEED = "cmd_navi_get_navigation_angle_speed";
    public static final String CMD_NAVI_GET_NAVIGATION_LINE_SPEED = "cmd_navi_get_navigation_line_speed";
    public static final String CMD_NAVI_SET_NAVIGATION_SPEED = "cmd_navi_set_navigation_speed";
    public static final String CMD_NAVI_CHANGE_NAVIGATION_SPEED = "cmd_navi_change_navigation_speed";
    public static final String CMD_NAVI_START_VELOCITY_REPORT = "cmd_navi_start_velocity_report";
    public static final String CMD_NAVI_STOP_VELOCITY_REPORT = "cmd_navi_stop_velocity_report";
    public static final String CMD_NAVI_SET_MAX_ACCELERATION = "cmd_navi_set_max_acceleration";

    public static final String CMD_NAVI_CRUISELAYOUT_START = "cmd_navi_cruiselayout_start";
    public static final String CMD_NAVI_CRUISELAYOUT_STOP = "cmd_navi_cruiselayout_stop";
    public static final String CMD_NAVI_CRUISE_START = "cmd_navi_cruise_start";
    public static final String CMD_NAVI_CRUISE_STOP = "cmd_navi_cruise_stop";
    public static final String CMD_NAVI_RELOCATION = "cmd_navi_relocation";
    public static final String CMD_NAVI_START_UPDATE = "cmd_navi_start_update";
    public static final String CMD_NAVI_GET_VERSION = "cmd_navi_get_version";
    public static final String CMD_NAVI_GET_UPDATE_PARAMS = "cmd_navi_get_update_params";
    public static final String CMD_NAVI_SWITCH_AUTO_CHARGE_MODE = "cmd_navi_switch_auto_charge_mode";
    public static final String CMD_NAVI_LOAD_CURRENT_MAP = "cmd_navi_load_current_map";
    public static final String CMD_NAVI_CHECK_CUR_NAVI_MAP = "cmd_navi_check_cur_navi_map";
    public static final String CMD_NAVI_CLEAR_CUR_NAVI_MAP = "cmd_navi_clear_cur_navi_map";
    public static final String CMD_NAVI_EDIT_PLACE = "cmd_navi_edit_place";
    public static final String CMD_NAVI_GET_PLACELIST_BY_MAPNAME = "cmd_navi_get_placelist_by_mapname";
    public static final String CMD_NAVI_HAS_PLACE_IN_MAPNAME = "cmd_navi_has_place_in_mapname";
    public static final String CMD_NAVI_GET_PLACELIST_BY_MAPNAME_MINI = "cmd_navi_get_placelist_by_mapname_mini";
    public static final String CMD_NAVI_GET_PLACELIST_BY_TYPE = "cmd_navi_get_placelist_by_type";

    public static final String CMD_NAVI_GET_ERROR_LOG = "cmd_navi_get_error_log";
    public static final String CMD_NAVI_PACK_LOG_FILE = "cmd_navi_pack_log_file";
    public static final String CMD_NAVI_GET_LOG_FILE = "cmd_navi_get_log_file";
    public static final String CMD_NAVI_GET_SHOT_LOG = "cmd_navi_get_shot_log";
    public static final String CMD_NAVI_CHECK_OBSTACLE = "cmd_navi_check_obstacle";
    public static final String CMD_NAVI_HAS_OBSTACLE_IN_AREA = "cmd_navi_has_obstacle_in_area";
    public static final String CMD_NAVI_START_EXTEND_MAP = "cmd_navi_start_extend_map";
    public static final String CMD_NAVI_CANCEL_CREATE_MAP = "cmd_navi_cancel_create_map";

    public static final String CMD_NAVI_MOVE_DIRECTION = "cmd_navi_move_direction";
    public static final String CMD_NAVI_ROTATE_IN_PLACE = "cmd_navi_rotate_in_place";
    public static final String CMD_NAVI_MOVE_DIRECTION_ANGLE = "cmd_navi_move_direction_angle";
    public static final String CMD_NAVI_MOVE_DIRECTION_ANGLE_CONTROL = "cmd_navi_move_direction_angle_control";
    public static final String CMD_NAVI_MOVE_DIRECTION_ANGLE_SOFT = "cmd_navi_move_direction_angle_soft";
    public static final String CMD_NAVI_MOVE_DISTANCE_ANGLE = "cmd_navi_move_distance_angle";
    public static final String CMD_NAVI_MOVE_ANGLE_PID = "cmd_navi_move_angle_pid";
    public static final String CMD_NAVI_STOP_MOVE = "cmd_navi_stop_move";
    public static final String CMD_NAVI_STOP_NAVIGATION = "cmd_navi_stop_navigation";
    public static final String CMD_NAVI_MOVE_SUB_FORWARD = "forward";
    public static final String CMD_NAVI_MOVE_SUB_BACKWARD = "backward";
    public static final String CMD_NAVI_MOVE_SUB_TURN_LEFT = "turn_left";
    public static final String CMD_NAVI_MOVE_SUB_TURN_RIGHT = "turn_right";
    public static final String CMD_NAVI_MOVE_SUB_TURN_BACK = "turn_back";
    public static final String CMD_NAVI_MOVE_SUB_ROTATE = "rotate";
    public static final String CMD_NAVI_MOVE_SUB_STOP = "stop";
    public static final String CMD_NAVI_GET_FULL_CHECK_STATUS = "cmd_navi_get_full_check_status";
    public static final String CMD_NAVI_GET_SENSOR_STATUS = "cmd_navi_get_sensor_status";
    public static final String CMD_NAVI_GET_MAP_NAME = "cmd_navi_get_map_name";
    public static final String CMD_NAVI_GET_POSITION = "cmd_navi_get_position";
    public static final String CMD_NAVI_GET_POSITION_WITHOUT_ESTIMATE = "cmd_navi_get_position_without_estimate";
    public static final String CMD_NAVI_GET_SERIAL_NUMBER = "cmd_navi_get_serial_number";
    public static final String CMD_NAVI_GET_PATROL_LIST = "cmd_navi_get_patrol_list";
    public static final String CMD_NAVI_SET_PATROL_LIST = "cmd_navi_set_patrol_list";
    public static final String CMD_CAN_BATTERY_TIME_REMAIN = "cmd_can_battery_time_remain";
    public static final String CMD_CAN_CHARGE_TIME_REMAIN = "cmd_can_charge_time_remain";
    public static final String CMD_REMOTE_POST_BATTERY_TIME_REMAIN = "cmd_remote_post_battery_time_remain";
    public static final String CMD_REMOTE_POST_CHARGE_TIME_REMAIN = "cmd_remote_post_charge_time_remain";
    public static final String CMD_REMOTE_FIRST_CHARGING = "cmd_remote_first_charging";
    public static final String CMD_REMOTE_CHARGE_PILE = "cmd_remote_charge_pile";
    public static final String CMD_REMOTE_POST_PREPARED = "cmd_remote_post_prepared";
    public static final String CMD_REMOTE_POST_SET_PLACE = "cmd_remote_post_set_place";
    public static final String CMD_REMOTE_UPLOAD_MAP_TO_SERVER = "cmd_remote_upload_map_to_server";
    public static final String CMD_REMOTE_TEXT_TO_MP3 = "cmd_remote_text_to_mp3";
    public static final String CMD_NAVI_REFRESH_MD5 = "cmd_navi_refresh_md5";
    public static final String CMD_REMOTE_GET_MAP_ID = "cmd_remote_get_map_id";
    public static final String CMD_NAVI_RESUME_SPECIAL_PLACE_THETA = "cmd_navi_resume_special_place_theta";
    public static final String CMD_REMOTE_POST_CRUISE_STATUS = "cmd_remote_post_cruise_status";
    public static final String CMD_NAVI_GET_CRUISE_ROUTE = "cmd_navi_get_cruise_route";
    public static final String CMD_NAVI_SET_CRUISE_ROUTE = "cmd_navi_set_cruise_route";
    public static final String CMD_NAVI_SWITCH_MANUAL_MODE = "cmd_navi_switch_manual_mode";
    public static final String CMD_NAVI_TIME_OUT_REPORT = "cmd_navi_time_out_report";
    public static final String CMD_NAVI_TIME_OUT_MSG_DELETE = "cmd_navi_time_out_msg_delete";
    public static final String CMD_NAVI_GET_LOG_BY_ID = "cmd_navi_get_log_by_id";
    public static final String CMD_NAVI_UPDATE_LOG_STATUS_BY_ID = "cmd_navi_update_log_status_by_id";
    public static final String CMD_NAVI_RENAME_MAP = "cmd_navi_rename_map";
    public static final String CMD_NAVI_CLEAR_CRUISE_ROUTE = "cmd_navi_clear_cruise_route";
    public static final String CMD_NAVI_SET_RADAR_STATUS = "cmd_navi_set_radar_status";
    public static final String CMD_NAVI_QUERY_RADAR_STATUS = "cmd_navi_query_radar_status";
    public static final String CMD_NAVI_CHECK_POSE_POSITION = "cmd_navi_check_pose_position";
    public static final String CMD_REMOTE_GET_ASK_WAY_LIST = "cmd_remote_get_ask_way_list";
    public static final String CMD_NAVI_RECOVERY = "cmd_navi_recovery";
    public static final String CMD_NAVI_ADD_MAPPING_POSE = "cmd_navi_add_mapping_pose";
    public static final String CMD_NAVI_DELETE_MAPPING_POSE = "cmd_navi_delete_mapping_pose";
    public static final String CMD_NAVI_RENAME_MAPPING_POSE = "cmd_navi_rename_mapping_pose";
    public static final String CMD_NAVI_GET_MAP_STATUS = "cmd_navi_get_map_status";
    public static final String CMD_NAVI_MOVE_DISTANCE_ANGLE_WITH_OBSTACLES = "cmd_navi_move_distance_angle_with_obstacles";
    public static final String CMD_NAVI_MOVE_DIRECTION_ANGLE_OBSTACLES = "cmd_navi_move_direction_angle_obstacles";
    public static final String CMD_NAVI_SET_MIN_OBSTACLES_DISTANCE = "cmd_navi_set_min_obstacles_distance";
    public static final String CMD_NAVI_RESET_MIN_OBSTACLES_DISTANCE = "cmd_navi_reset_min_obstacles_distance";
    public static final String CMD_NAVI_CONNECT_STATUS = "cmd_navi_connect_status";
    public static final String CMD_NAVI_QUERY_MULTIPLE_MAP_STATUS = "cmd_navi_query_multiple_map_status";
    public static final String CMD_NAVI_GET_MAP_INFO = "cmd_navi_get_map_info";
    public static final String CMD_NAVI_MAP_HAS_VISION = "cmd_navi_map_has_vision";
    public static final String CMD_NAVI_GET_LOCAL_MAP_INFO_LIST = "cmd_navi_get_local_map_info_list";
    public static final String CMD_NAVI_SET_MAP_SYNC_STATE = "cmd_navi_set_map_sync_state";
    public static final String CMD_NAVI_SET_MAP_UUID = "cmd_navi_set_map_uuid";
    public static final String CMD_NAVI_SET_MAP_FINISH_STATE = "cmd_navi_set_map_finish_state";
    public static final String CMD_NAVI_SET_MAP_FORBID_LINE = "cmd_navi_set_map_forbid_line";
    public static final String CMD_NAVI_SET_MAP_UPDATE_TIME = "cmd_navi_set_map_update_time";
    public static final String CMD_NAVI_ADD_MAP_INFO = "cmd_navi_add_map_info";
    public static final String CMD_NAVI_SET_CAMERA_STATE = "cmd_navi_set_camera_state";
    public static final String CMD_NAVI_PARSE_MAP_DATA = "cmd_navi_parse_map_data";
    public static final String CMD_NAVI_SET_MULTI_ROBOT_CONFIG = "cmd_navi_set_multi_robot_config";
    public static final String CMD_NAVI_GET_MULTI_ROBOT_CONFIG = "cmd_navi_get_multiRobot_config";
    public static final String CMD_NAVI_SEND_MULTI_ROBOT_MESSAGE = "cmd_navi_send_multiRobot_message";
    public static final String CMD_NAVI_SET_MULTI_ROBOT_TEST_ENABLE = "cmd_navi_set_multi_robot_test_enable";
    public static final String CMD_NAVI_RESET_MULTI_ROBOT_CONFIG = "cmd_navi_reset_multi_robot_config";
    public static final String CMD_NAVI_SET_WHEEL_CONTROL_MODE = "cmd_navi_set_wheel_control_mode";
    public static final String CMD_NAVI_GET_NAVI_PATH_INFO = "cmd_navi_get_navi_path_info";
    public static final String CMD_NAVI_GET_NAVI_PATH_INFO_TO_GOALS = "cmd_navi_get_navi_path_info_to_goals";
    public static final String CMD_NAVI_GET_NAVI_PATH_DETAIL = "cmd_navi_get_navi_path_detail";
    public static final String CMD_NAVI_GET_NAVI_GATE_PASSING_ROUTE = "cmd_navi_get_gate_passing_route";
    public static final String CMD_NAVI_GET_NAVI_MULT_GATE_PASSING_ROUTE = "cmd_navi_get_navi_mult_gate_passing_route";
    public static final String CMD_NAVI_GET_ADDITIONAL_DEVICES = "cmd_navi_get_additional_devices";
    public static final String CMD_NAVI_GET_MAPPING_INFO = "cmd_navi_get_mapping_info";
    public static final String CMD_NAVI_SET_MAPPING_PLACE = "cmd_navi_set_mapping_place";
    public static final String CMD_NAVI_ENABLE_REPORT_LINE_DATA = "cmd_navi_enable_report_line_data";
    public static final String CMD_NAVI_ENABLE_DEPTH_IMAGE = "cmd_navi_enable_depth_image";
    public static final String CMD_NAVI_ENABLE_IR_IMAGE = "cmd_navi_enable_ir_image";
    public static final String CMD_NAVI_START_DATA_SET_RECORD = "cmd_navi_start_data_set_record";
    public static final String CMD_NAVI_STOP_DATA_SET_RECORD = "cmd_navi_stop_data_set_record";
    public static final String CMD_NAVI_UPLOAD_NAVI_DATA_SET = "cmd_navi_upload_navi_data_set";
    public static final String CMD_NAVI_SET_EXTRA_FILE_DATA = "cmd_navi_set_extra_file_data";
    public static final String CMD_NAVI_GET_EXTRA_FILE_DATA = "cmd_navi_get_extra_file_data";
    public static final String CMD_NAVI_HAS_EXTRA_FILE = "cmd_navi_has_extra_file";
    public static final String CMD_NAVI_ZIP_EXTRA_FILE = "cmd_navi_zip_extra_file";
    public static final String CMD_NAVI_UNZIP_EXTRA_FILE = "cmd_navi_unzip_extra_file";
    public static final String CMD_NAVI_ZIP_MAP_FILE = "cmd_navi_zip_map_file";
    public static final String CMD_NAVI_UNZIP_MAP_FILE = "cmd_navi_unzip_map_file";
    public static final String CMD_NAVI_COPY_IMPORT_MAP_FILE = "cmd_navi_copy_import_map_file";
    public static final String CMD_NAVI_GET_NAVI_ANGLE_SPEED = "cmd_navi_get_navi_angle_speed";
    public static final String CMD_NAVI_GET_MAP_INFO_BY_SD_MAP_NAMES = "cmd_navi_get_map_info_by_sd_map_names";
    public static final String CMD_NAVI_GET_CURRENT_MAP_NAME = "cmd_navi_get_current_map_name";
    public static final String CMD_NAVI_GET_MOTION_DISTANCE = "cmd_navi_get_motion_distance";

    public static final String CMD_NAVI_PAUSE_NAVIGATION = "cmd_navi_pause_navigation";
    public static final String CMD_NAVI_OTA_DOWNGRADE = "cmd_navi_ota_downgrade";

    //是否启用顶视辅助定位
    public static final String CMD_NAVI_SET_TOP_ASSISTED_ESTIMATE = "cmd_navi_set_top_assisted_estimate";

    public static final String CMD_VIDEO_APP_ANSWER = "cmd_video_app_answer";
    public static final String CMD_VIDEO_USER_LIST = "cmd_video_user_list";
    public static final String CMD_VIDEO_INVITE_CALL = "cmd_video_invite_call";
    public static final String CMD_UPLOAD_CRUISE_PICTURE = "cmd_upload_cruise_picture";

    public static final String CMD_REMOTE_REFRESH_ROBOT_PROFILE = "cmd_remote_refresh_robot_profile";

    public static final String CMD_REMOTE_SET_QUERY_FEEDBACK = "cmd_remote_set_query_feedback";
    public static final String CMD_REMOTE_GET_MAP_POS_SEPARATE = "cmd_remote_get_map_pos_separate";

    public static final String JSON_NAVI_TEST_MESSAGE = "test_msg";
    public static final String JSON_NAVI_LORA_DATA = "lora_data";
    public static final String JSON_NAVI_LORA_TEST_ENABLE = "lora_test_enable";
    public static final String JSON_NAVI_DERICTION = "direction";
    public static final String JSON_NAVI_LINEAR_SPEED = "linear_speed";
    public static final String JSON_NAVI_LINEAR_SPEED_X = "linear_speed_x";
    public static final String JSON_NAVI_LINEAR_SPEED_EXPECT_X = "linear_speed_expect_x";
    public static final String JSON_NAVI_LINEAR_SPEED_Y = "linear_speed_y";
    public static final String JSON_NAVI_LINEAR_SPEED_Z = "linear_speed_z";
    public static final String JSON_NAVI_ANGULAR_SPEED = "angular_speed";
    public static final String JSON_NAVI_LINEAR_ACCELERATION = "linear_acceleration";
    public static final String JSON_NAVI_ANGULAR_ACCELERATION = "angular_acceleration";
    public static final String JSON_NAVI_START_MODE_LEVEL = "start_mode_level";
    public static final String JSON_NAVI_BRAKE_MODE_LEVEL = "brake_mode_level";
    public static final String JSON_NAVI_MIN_DISTANCE = "min_distance";
    public static final String JSON_NAVI_ANGULAR_SPEED_X = "angular_speed_x";
    public static final String JSON_NAVI_ANGULAR_SPEED_Y = "angular_speed_y";
    public static final String JSON_NAVI_ANGULAR_SPEED_Z = "angular_speed_z";
    public static final String JSON_NAVI_ANGULAR_SPEED_EXPECT_Z = "angular_speed_expect_z";
    public static final String JSON_NAVI_ACCELERATION = "acceleration_speed";
    public static final String JSON_NAVI_ACCELERATION_VALUE = "acceleration_speed_value";
    public static final String JSON_NAVI_FILE_ID = "file_id";
    public static final String JSON_NAVI_OCCUR_TIME = "occur_time";
    public static final String JSON_NAVI_TYPE = "type";
    public static final String JSON_NAVI_FILE_PATH = "file_path";
    public static final String JSON_NAVI_SNAP_RESULT = "snap_result";
    public static final String JSON_NAVI_ISADJUST_ANGLE = "adjust_angle";
    public static final String JSON_NAVI_IS_KEEP_MOVE = "keep_move";
    public static final String JSON_NAVI_DESTINATION_RANGE = "destination_range";
    public static final String JSON_NAVI_OBSTACLE_DISTANCE = "obstacle_distance";
    public static final String JSON_NAVI_LEFT_OR_RIGHT = "left_or_right";
    public static final String JSON_NAVI_COMPARE_ANGLE = "compare_angle";
    public static final String JSON_NAVI_WAKEUP_ACTION = "json_navi_wakeup_action";
    public static final String JSON_NAVI_FORWARD_AVOID = "json_navi_forward_avoid";
    public static final String JSON_NAVI_ENALBE_LINE_DATA = "json_navi_enalbe_line_data";
    public static final String JSON_NAVI_ENALBE_DEPTH_IMAGE = "json_navi_enalbe_depth_image";
    public static final String JSON_NAVI_ENALBE_IR_IMAGE = "json_navi_enalbe_ir_image";
    public static final String JSON_NAVI_DEPTH_DEVICE = "json_navi_depth_device";
    public static final String JSON_NAVI_IR_DEVICE = "json_navi_ir_device";
    public static final String JSON_NAVI_FOLLOW_ID = "json_navi_follow_id";
    public static final String JSON_NAVI_FOLLOW_LOST_FIND_TIMEOUT = "json_navi_follow_lost_find_timeout";
    public static final String JSON_NAVI_DATA = "json_navi_data";
    public static final String JSON_NAVI_TIME = "json_navi_time";


    public static final String JSON_NAVI_SAVE_LOCAL_SENSOR_DATA = "json_navi_save_loacal_sensor_data";
    public static final String JSON_NAVI_SENSOR_LIST = "navi_data_type";
    public static final String JSON_NAVI_MULTI_FLOOR_UPDATE_FLOOR_STATE = "json_navi_multi_floor_update_floor_state";
    public static final String JSON_NAVI_MULTI_FLOOR_UPDATE_FLOOR_INDEX = "json_navi_multi_floor_update_floor_index";
    public static final String JSON_NAVI_MULTI_FLOOR_UPDATE_FLOOR_ALIAS = "json_navi_multi_floor_update_floor_alias";
    public static final String JSON_NAVI_MULTI_FLOOR_UPDATE_ELEVATORS = "json_navi_multi_floor_update_elevators";
    public static final String JSON_REMOTE_USER_NAME = "user_name";
    public static final String JSON_REMOTE_PASSWORD = "password";
    public static final String JSON_REMOTE_USER_ID = "user_id";
    public static final String JSON_REMOTE_USER_TOKEN = "user_token";

    public static final String JSON_DUMP_INOF_FILE_ID = "file_id";
    public static final String JSON_DUMP_INOF_ERROR_TYPE = "error_type";
    public static final String JSON_DUMP_INOF_ERROR_MODULE = "error_module";
    public static final String JSON_DUMP_INOF_ERROR_TIMESTAMP = "error_timestamp";
    public static final String JSON_DUMP_INOF_DESCRIPTION = "description";
    public static final String JSON_DUMP_INOF_FILE_SIZE = "file_size";

    // 埋点分级
    public static final String JSON_EVENT_CALLCHAIN_TRACK_LEVEL = "callchain_track_level";

    public static final String JSON_NAVI_RELOCATION_TYPE = "relocation_type";
    public static final String JSON_NAVI_RELOCATION_POSE = "relocation_pose";
    public static final String JSON_NAVI_WHEEL_MODE = "wheel_mode";

    public static final String JSON_START_TIME = "start_time";
    public static final String JSON_END_TIME = "end_time";
    public static final String JSON_CMD_TYPE = "cmd_type";
    public static final String JSON_SAVE_PATH = "save_path";

    public static final String JSON_COMMAND_EXTRA_DATA = "command_extra_data";
    public static final String JSON_COMMAND_RESULT = "command_result";
    public static final String JSON_COMMAND_STATUS = "command_status";
    public static final String JSON_CONTROL_ORIGIN_FLOOR = "origin_floor";
    public static final String JSON_CONTROL_TARGET_FLOOR = "target_floor";
    public static final String JSON_CONTROL_IS_IN_ELEVATOR = "is_in_elevator";
    public static final String JSON_CONTROL_ROBOT_STATE = "robot_state";

    public static final String JSON_MAP_DATA_IS_ZIP = "is_zip";
    public static final String JSON_MAP_ZIP_DATA = "zip_data";
    public static final String JSON_NAVI_TYPE_ID = "type_id";
    public static final String JSON_NAVI_PRIORITY = "priority";
    public static final String JSON_PLACE_NAME = "placeName";

    public enum SnapResult {
        success(0),
        cmd_error(1),
        file_id_error(2),
        file_path_error(3);

        int value;

        SnapResult(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    public static final String JSON_NAVI_STATISTIC_TYPE = "statistic_type";
    public static final String JSON_NAVI_STATISTIC_INT1 = "statistic_int1";
    public static final String JSON_NAVI_STATISTIC_INT2 = "statistic_int2";
    public static final String JSON_NAVI_STATISTIC_INT3 = "statistic_int3";
    public static final String JSON_NAVI_STATISTIC_INT4 = "statistic_int4";
    public static final String JSON_NAVI_STATISTIC_DOUBLE1 = "statistic_double1";
    public static final String JSON_NAVI_STATISTIC_DOUBLE2 = "statistic_double2";
    public static final String JSON_NAVI_STATISTIC_DOUBLE3 = "statistic_double3";
    public static final String JSON_NAVI_STATISTIC_DOUBLE4 = "statistic_double4";
    public static final String JSON_NAVI_STATISTIC_STR = "statistic_str";

    public static final String JSON_NAVI_NAVIGATION_LINEAR_SPEED = "navigation_linear_speed";
    public static final String JSON_NAVI_NAVIGATION_ACCLINEAR_SPEED = "navigation_acclinear_speed";
    public static final String JSON_NAVI_NAVIGATION_ANGULAR_SPEED = "navigation_angular_speed";
    public static final String JSON_NAVI_NAVIGATION_ACCANGULAR_SPEED = "navigation_accangular_speed";
    public static final String JSON_NAVI_X_ACCELERATE = "x_accelerate";
    public static final String JSON_NAVI_Y_ACCELERATE = "y_accelerate";
    public static final String JSON_NAVI_Z_ACCELERATE = "z_accelerate";

    public static final String JSON_NAVI_DISTANCE = "distance";
    public static final String JSON_NAVI_ANGLE = "angle";
    public static final String JSON_NAVI_LATENCY = "latency";
    public static final String JSON_NAVI_BODY_FOLLOW = "body_follow";
    public static final String JSON_NAVI_MIN_OBSTACLES_DISTANCE = "min_obstacles_distance";
    public static final String JSON_NAVI_SITE_EXIST = "siteexist";
    public static final String JSON_NAVI_IN_NAVIGATION = "innavigation";
    public static final String JSON_NAVI_DIRECTION_X = "x";
    public static final String JSON_NAVI_DIRECTION_Y = "y";
    public static final String JSON_NAVI_DIRECTION_Z = "z";
    public static final String JSON_NAVI_DIRECTION_W = "w";
    public static final String JSON_NAVI_POSITION_X = "px";
    public static final String JSON_NAVI_POSITION_Y = "py";
    public static final String JSON_NAVI_POSITION_THETA = "theta";
    public static final String JSON_NAVI_POSITION_STATUS = "status";
    public static final String JSON_NAVI_REALTIME_OBSTACLE_DISTANCE = "obs_distance";
    public static final String JSON_NAVI_OBSTACLE_DELTA_TIME = "deltatime";
    public static final String JSON_NAVI_TARGET_PLACE_NAME = "target_place_name";
    public static final String JSON_NAVI_IS_REVERSE_POSE_THETA = "is_reverse_pose_theta";
    public static final String JSON_NAVI_IS_FRONT_CHARGE = "is_front_charge";
    public static final String JSON_NAVI_PLACE_ID = "place_id";
    public static final String JSON_NAVI_COORDINATE_DEVIATION = "coordinate_deviation";
    public static final String JSON_NAVI_IS_IN_LOCATION = "isinlocation";
    public static final String IS_IN_LOCATION_CHECK_THETA = "is_in_location_check_theta";
    public static final String JSON_NAVI_NO_NEED_ACCELERATION = "no_need_acceleration";
    public static final String JSON_NAVI_DESTINATION_NAME = "destination_name";
    public static final String JSON_NAVI_IS_AVOID_STOPPING = "isAvoidStopping";
    public static final String JSON_NAVI_MULTIPLE_MAP_STATUS = "multipleMapStatus";
    public static final String JSON_NAVI_TASK_PRIORITY = "taskPriority";
    public static final String JSON_NAVI_IS_DELETE_LOCAL_MAP = "is_delete_local_map";
    public static final String JSON_NAVI_DOWNGRADE_TARGET_VERSION = "downgrade_target_version";
    public static final String JSON_NAVI_DOWNGRADE_TARGET_FULL_VERSION = "downgrade_target_full_version";

    // 边建图边设点
    public static final String JSON_NAVI_RENAME_POSE_OLD_POSENAME = "old_posename";
    public static final String JSON_NAVI_RENAME_POSE_NEW_POSENAME = "new_posename";
    public static final String JSON_NAVI_MAX_LINEAR_SPEED = "max_linear_speed";
    public static final String JSON_NAVI_MAX_ANGULAR_SPEED = "max_angular_speed";
    public static final String JSON_NAVI_CAMERA_TYPE = "camera_type";
    public static final String JSON_NAVI_ENABLE_STATE = "enable_state";
    public static final String JSON_NAVI_MAP_TYPE = "mapType";
    public static final String JSON_MAP_SUPPORT_TYPE = "mapSupportType";
    public static final String JSON_NAVI_VISION_TYPE = "visionType";
    public static final String JSON_NAVI_TARGET_TYPE = "targetType";

    //Head
    public static final String CMD_HEAD_REGISTER = "cmd_head_register";
    public static final String CMD_HEAD_UNREGISTER = "cmd_head_unregister";
    public static final String CMD_HEAD_MOVE_HEAD = "cmd_head_move_head";
    public static final String CMD_HEAD_TURN_HEAD = "cmd_head_turn_head";
    public static final String CMD_HEAD_SWITCH_CAMERA = "cmd_head_switch_camera";
    public static final String CMD_HEAD_SET_TRACK_TARGET = "cmd_head_set_track_target";
    public static final String CMD_HEAD_SWITCH_TRACK_TARGET = "cmd_head_switch_track_target";
    public static final String CMD_HEAD_STOP_TRACK_TARGET = "cmd_head_stop_track_target";
    public static final String CMD_HEAD_START_BODY_TRACKER = "cmd_head_start_body_tracker";
    public static final String CMD_HEAD_STOP_BODY_TRACKER = "cmd_head_stop_body_tracker";
    public static final String CMD_HEAD_GET_ALL_PERSON_INFOS = "cmd_head_get_all_person_infos";
    public static final String CMD_HEAD_STOP_SEND_PERSON_INFOS = "cmd_head_stop_send_person_infos";
    public static final String CMD_HEAD_GET_ALL_PERSON_INFOS_ON_TRACK = "cmd_head_get_all_person_infos_on_track";
    public static final String CMD_HEAD_STOP_ALL_PERSON_INFOS_ON_TRACK = "cmd_head_stop_all_person_infos_on_track";
    public static final String CMD_HEAD_GET_PERSON_INFO_BY_NAME = "cmd_head_get_person_info_by_name";
    public static final String CMD_HEAD_SEARCH_PERSON_BY_NAME = "cmd_head_search_person_by_name";
    public static final String CMD_HEAD_GET_LAST_POSITION = "cmd_head_get_last_position";
    public static final String CMD_HEAD_IS_HEADER_CONNECTED = "cmd_head_is_header_connected";
    public static final String CMD_HEAD_GET_PICTURE_BY_ID = "cmd_head_get_picture_by_id";
    public static final String CMD_HEAD_GET_MASK_INFO_BY_ID = "cmd_head_get_mask_info_by_id";
    public static final String CMD_REPORT_NAVIGATION_STATUS = "cmd_report_navigation_status";
    public static final String CMD_SWITCH_DETECT_ALGORITHM = "cmd_switch_detect_algorithm";
    public static final String CMD_HEAD_REGISTER_BY_PIC = "cmd_head_register_by_pic";
    public static final String CMD_HEAD_INSPECTION = "cmd_head_inspection";
    public static final String CMD_HEAD_START_HEAD_COUNT = "cmd_head_start_head_count";
    public static final String CMD_HEAD_STOP_HEAD_COUNT = "cmd_head_stop_head_count";
    public static final String CMD_HEAD_GET_HEAD_COUNT = "cmd_head_get_head_count";
    public static final String CMD_HEAD_REPORT_HEAD_COUNT = "cmd_head_report_head_count";
    public static final String CMD_HEAD_VIDEO_RECORD = "cmd_head_video_record";
    public static final String CMD_HEAD_GET_SERIAL_NUMBER = "cmd_head_get_serial_number";
    public static final String CMD_HEAD_TESTCASE = "cmd_head_testcase";
    public static final String CMD_HEAD_PICTURE_REPORT_CONFIG = "cmd_head_picture_report_config";
    public static final String CMD_VISION_START_VISION_RECORD = "cmd_vision_start_vision_record";
    public static final String CMD_VISION_STOP_VISION_RECORD = "cmd_vision_stop_vision_record";
    public static final String CMD_VISION_GET_PICTURE = "cmd_vision_get_picture";

    public static final String CMD_HEAD_START_SCP = "cmd_head_start_scp";
    public static final String CMD_HEAD_START_UPDATE = "cmd_head_start_update";
    public static final String CMD_HEAD_GET_VERSION = "cmd_head_get_version";
    public static final String CMD_HEAD_OTA_GET_VERSION = "cmd_head_ota_get_version";
    public static final String CMD_HEAD_GET_UPDATE_PARAMS = "cmd_head_get_update_params";
    public static final String CMD_HEAD_GET_GESTURE_INFOS = "cmd_head_get_gesture_infos";
    public static final String CMD_HEAD_STOP_GESTURE_INFOS = "cmd_head_stop_gesture_infos";
    public static final String CMD_HEAD_GET_MOVEMENT = "cmd_head_get_movement";
    public static final String CMD_HEAD_GET_ALL_FACE_POS = "cmd_head_get_all_face_pos";
    public static final String CMD_HEAD_STOP_ALL_FACE_POS = "cmd_head_stop_all_face_pos";
    public static final String CMD_HEAD_GET_STATUS = "cmd_head_get_status";
    public static final String CMD_HEAD_START_STATUS_LISTENER = "cmd_head_start_status_listener";
    public static final String CMD_HEAD_STOP_STATUS_LISTENER = "cmd_head_stop_status_listener";
    public static final String CMD_HEAD_RESTART = "cmd_head_restart";
    public static final String CMD_HEAD_GET_CAMERA_STATUS = "cmd_head_get_camera_status";

    public static final String CMD_HEAD_GET_DEPTH_STATUS = "cmd_head_get_depth_status";
    public static final String CMD_HEAD_GET_FOV_STATUS = "cmd_head_get_fov_status";
    public static final String CMD_HEAD_MASTER_SWITCH = "cmd_head_master_switch";

    public static final String CMD_HEAD_RECORD_FACE_START = "cmd_head_record_face_start";
    public static final String CMD_HEAD_RECORD_FACE_STOP = "cmd_head_record_face_stop";
    public static final String CMD_HEAD_ENABLE_AVATAR_FACE = "cmd_head_enable_avatar_face";
    public static final String CMD_HEAD_DISABLE_AVATAR_FACE = "cmd_head_disable_avatar_face";
    public static final String CMD_HEAD_RESET_HEAD = "cmd_head_reset_head";

    public static final String CMD_HEAD_START_VISION = "cmd_head_start_vision";
    public static final String CMD_HEAD_STOP_VISION = "cmd_head_stop_vision";
    public static final String CMD_BACKUP_START_VISION = "cmd_backup_start_vision";
    public static final String CMD_BACKUP_STOP_VISION = "cmd_backup_stop_vision";
    public static final String CMD_HEAD_START_PICTURE_REPORT = "cmd_head_start_picture_report";
    public static final String CMD_HEAD_STOP_PICTURE_REPORT = "cmd_head_stop_picture_report";
    public static final String CMD_HEAD_START_BODY_REPORT = "cmd_head_start_body_report";
    public static final String CMD_HEAD_STOP_BODY_REPORT = "cmd_head_stop_body_report";
    public static final String CMD_HEAD_BODY_REPORT = "cmd_head_body_report";
    public static final String CMD_HEAD_RECOVERY = "cmd_head_recovery";
    public static final String CMD_HEAD_GET_PICTURE = "cmd_head_get_picture";
    public static final String CMD_HEAD_UNQUALIFIED_PICTURE_CONFIG = "cmd_head_unqualified_picture_config";
    public static final String CMD_HEAD_GET_FOV_CAMERA_INFO = "cmd_head_get_fov_camera_info";
    public static final String CMD_VISION_GET_REGISTER_ALGORITHM_VERSION
            = "cmd_vision_get_register_algorithm_version";
    public static final String CMD_VISION_FACE_DATA_SYNC = "cmd_vision_face_data_sync";
    public static final String CMD_VISION_REGISTER_BY_ID = "cmd_vision_register_by_id";
    public static final String CMD_VISION_REGISTER_BY_PIC = "cmd_vision_register_by_pic";
    public static final String CMD_VISION_RECOGNIZE_BY_ID = "cmd_vision_recognize_by_id";
    public static final String CMD_VISION_RECOGNIZE_BY_PIC = "cmd_vision_recognize_by_pic";
    public static final String CMD_VISION_GET_RESOLUTION = "cmd_vision_get_resolution";
    public static final String CMD_VISION_GET_VISION_MODE = "cmd_vision_get_vision_mode";
    public static final String CMD_VISION_START_CACHE_PICTURES = "cmd_vision_start_cache_pictures";
    public static final String CMD_VISION_STOP_CACHE_PICTURES = "cmd_vision_stop_cache_pictures";
    public static final String CMD_VISION_SAVE_CACHE_PICTURES = "cmd_vision_save_cache_pictures";

    /*------- Can ----------*/
    public static final String CMD_CAN_GET_STATUS = "cmd_can_get_status";
    public static final String CMD_CAN_LAMP_OPEN = "cmd_can_lamp_open";
    public static final String CMD_CAN_LAMP_JNI2 = "cmd_can_lamp_jni2";
    public static final String CMD_CAN_LAMP_ANIM = "cmd_can_lamp_anim";
    public static final String CMD_CAN_LAMP_COLOR = "cmd_can_lamp_color";
    public static final String CMD_CAN_LAMP_CLOSE = "cmd_can_lamp_close";
    public static final String CMD_CAN_SHIPPING_MODE = "cmd_can_shipping_mode";
    public static final String CMD_CAN_AUTO_CHARGE_START = "cmd_can_auto_charge_start";
    public static final String CMD_CAN_AUTO_CHARGE_END = "cmd_can_auto_charge_end";
    public static final String CMD_CAN_GET_CHARGE_STATUS = "cmd_can_get_charge_status";
    public static final String CMD_CAN_ROBOT_REBOOT = "cmd_can_robot_reboot";
    public static final String CMD_CAN_ROBOT_SHUTDOWN = "cmd_can_robot_shutdown";
    public static final String CMD_CAN_GET_INFRARED_INFO = "cmd_can_get_infrared_info";
    public static final String CMD_CAN_STOP_SEND_INFRARED_INFO = "cmd_can_stop_send_infrared_info";
    public static final String CMD_CAN_START_CHARGE_WITHOUT_ESTIMATE = "cmd_can_start_charge_without_estimate";
    public static final String CMD_CAN_SET_VERTICAL_MAX_LIMIT_ANGLE = "cmd_can_set_vertical_max_limit_angle";
    public static final String CMD_CAN_SET_LOCK_ENABLE = "cmd_can_set_lock_enable";
    public static final String CMD_CAN_GET_DOOR_STATUS = "cmd_can_get_door_status";
    public static final String CMD_CAN_DORMANCY_START = "cmd_can_dormancy_start";
    public static final String CMD_CAN_GET_CPU_TEMPERATURE = "cmd_can_get_cpu_temperature";
    public static final String CMD_CAN_SET_POWER_LPM = "cmd_can_set_power_lpm";
    public static final String CMD_CAN_SET_LED_LIGHT = "cmd_can_set_led_light";
    public static final String CMD_CAN_SET_XD_POWER = "cmd_can_set_xd_power_enable";
    public static final String CMD_CAN_SET_XD_FAN_ENABLE = "cmd_can_set_xd_fan_enable";
    public static final String CMD_CAN_SET_XD_RANK = "cmd_can_set_xd_rank";


    public static final String CMD_CARD_INDICATE_CMD_CHANNEL = "cmd_card_indicate_cmd_channel";
    public static final String CMD_CARD_NOTIFY_CMD_CHANNEL = "cmd_card_notify_cmd_channel";
    public static final int AUTOCHARGE_STATUS_SHUTDOWN = 0;
    public static final int AUTOCHARGE_STATUS_INIT = 1;
    public static final int AUTOCHARGE_STATUS_READY = 2;
    public static final int AUTOCHARGE_STATUS_ONGOING = 3;
    public static final int AUTOCHARGE_STATUS_LOCKED = 4;
    public static final int AUTOCHARGE_STATUS_DONE = 5;
    public static final int AUTO_CHARGE_STATUS_NO_SIGNAL = 10;
    public static final int AUTOCHARGE_STATUS_FAIL = 255;

    public static final String CMD_CAN_GET_EMERGENCY_STATUS = "cmd_can_get_emergency_status";


    public static final String CMD_CAN_OTA_START = "cmd_can_ota_start";
    public static final String CMD_CAN_OTA_GET_STATE = "cmd_can_ota_get_state";
    public static final String CMD_CAN_GET_BOARD_VERSION = "cmd_can_get_board_version";

    public static final String CMD_D430_CALIBRATION_START = "cmd_d430_calibration_start";
    public static final String CMD_D430_CALIBRATION_STOP = "cmd_d430_calibration_stop";
    public static final String CMD_UVC_CAMERA_SINGLE_CLASSIFY = "cmd_uvc_camera_single_classify";
    public static final String CMD_UVC_CAMERA_CONTINUE_CLASSIFY = "cmd_uvc_camera_continue_classify";
    public static final String CMD_UVC_CAMERA_CONTINUE_CLASSIFY_STOP = "cmd_uvc_camera_continue_classify_stop";
    public static final String CMD_UVC_CAMERA_SINGLE_NO_CLASSIFY = "cmd_uvc_camera_single_no_classify";
    public static final String CMD_QUERY_UVC_CAMERA_CONNECTED_STATUS = "cmd_query_uvc_camera_connected_status";

    // 旧版本灯带命令，做旧版本兼容
    public static final String CMD_ZCB_LED_EFFECT = "cmd_zcb_led_effect";
    public static final String CMD_TRAY_LED_EFFECT = "cmd_tray_led_effect";
    public static final String CMD_IOB_LED_EFFECT = "cmd_iob_led_effect";

    public static final String CMD_CAN_BOTTOM_LED_EFFECT = "cmd_can_bottom_led_effect";
    public static final String CMD_CAN_CLAVICLE_LED_EFFECT = "cmd_can_clavicle_led_effect";
    public static final String CMD_CAN_TRAY_LIGHT_EFFECT = "cmd_can_tray_light_effect";
    public static final String CMD_CAN_TRAY_LED_EFFECT = "cmd_can_tray_led_effect";
    public static final String CMD_TRAY_LASER_INSPECTION = "cmd_tray_laser_inspection";

    public static final String CMD_CAN_GET_WHEEL_L_VERSION = "cmd_can_get_wheel_l_version";
    public static final String CMD_CAN_GET_WHEEL_R_VERSION = "cmd_can_get_wheel_r_version";
    public static final String CMD_CAN_GET_MOTOR_H_VERSION = "cmd_can_get_motor_h_version";
    public static final String CMD_CAN_GET_MOTOR_V_VERSION = "cmd_can_get_motor_v_version";
    public static final String CMD_CAN_GET_PSB_VERSION = "cmd_can_get_psb_version";
    public static final String CMD_CAN_GET_PSB_S_VERSION = "cmd_can_get_psb_s_version";
    public static final String CMD_CAN_GET_AUTO_CHARGE_VERSION = "cmd_can_get_auto_charge_version";
    public static final String CMD_CAN_GET_BATTERY_VERSION = "cmd_can_get_battery_version";
    public static final String CMD_CAN_GET_ROTATE_SUPPORT = "cmd_can_get_rotate_support";
    public static final String CMD_CAN_RESET_WHEEL = "cmd_can_reset_wheel";
    public static final String CMD_SET_POWER_MOTOR = "cmd_set_power_motor";
    public static final String CMD_CAN_GET_MULTI_FUNC_SWITCH_STATE = "cmd_can_get_multi_func_switch_state";
    public static final String CMD_CAN_GET_BATTERY_INFO = "cmd_can_get_battery_info";
    // 送餐机器人Pro电动门指令
    public static final String CMD_CAN_GET_ELECTRIC_DOOR_VERSION = "cmd_can_get_electric_door_version";
    public static final String CMD_CAN_ELECTRIC_DOOR_CTRL = "cmd_can_electric_door_ctrl";
    public static final String CMD_CAN_GET_ELECTRIC_DOOR_STATUS = "cmd_can_get_electric_door_status";
    public static final String JSON_CAN_ELECTRIC_DOOR_CTRL = "json_can_electric_door_ctrl";
    public static final String JSON_CAN_GET_ELECTRIC_DOOR_STATUS = "json_can_get_electric_door_status";
    public static final String STATUS_CAN_ELECTRIC_DOOR_CTRL = "status_can_electric_door_ctrl";
    public static final int CAN_DOOR_DOOR1_DOOR2_OPEN = 17;
    public static final int CAN_DOOR_DOOR1_DOOR2_CLOSE = 34;
    public static final int CAN_DOOR_DOOR3_DOOR4_OPEN = 4352;
    public static final int CAN_DOOR_DOOR3_DOOR4_CLOSE = 8704;
    public static final int CAN_DOOR_DOOR1_OPEN = 1;
    public static final int CAN_DOOR_DOOR1_CLOSE = 2;
    public static final int CAN_DOOR_DOOR2_OPEN = 16;
    public static final int CAN_DOOR_DOOR2_CLOSE = 32;
    public static final int CAN_DOOR_DOOR3_OPEN = 256;
    public static final int CAN_DOOR_DOOR3_CLOSE = 512;
    public static final int CAN_DOOR_DOOR4_OPEN = 8192;
    public static final int CAN_DOOR_DOOR4_CLOSE = 34;
    public static final int CAN_DOOR_ALL_OPEN = 4369;
    public static final int CAN_DOOR_ALL_CLOSE = 8738;
    public static final int CAN_DOOR_STATUS_OPEN = 51;
    public static final int CAN_DOOR_STATUS_CLOSE = 68;
    public static final int CAN_DOOR_STATUS_TIMEOUT = 85;
    public static final int CAN_DOOR_STATUS_FG2LOW = 238;
    public static final int CAN_DOOR_STATUS_RUNNING = 102;
    public static final int CAN_DOOR_STATUS_BLOCKING_STOP = 226;
    public static final int CAN_DOOR_STATUS_BLOCK_AND_BOUNCE = 227;
    public static final int CAN_DOOR_HEARTBEAT_STATUS_NORMAL = 170;
    public static final int CAN_DOOR_HEARTBEAT_STATUS_ABNORMAL = 238;

    public static final String JSON_CAN_BOARD = "board";
    public static final String JSON_CAN_FILE_PATH = "file_path";
    public static final String JSON_CAN_OTA_START_RESULT = "start_result";
    public static final String JSON_CAN_OTA_START_ERR_MSG = "error_message";
    public static final String JSON_CAN_BOARD_APP_VERSION = "board_app_version";
    public static final String JSON_CAN_UI_LEVEL = "ui_level";
    public static final String JSON_CAN_BMS_LEVEL = "bms_level";
    public static final String JSON_CAN_BMS_TEMP = "bms_temp";
    public static final String JSON_CAN_LOCK_ENABLE = "lock_enable";
    public static final String JSON_CAN_DOOR_TYPE = "door_type";
    public static final String JSON_CAN_DOOR_STATUS = "door_status";
    public static final String JSON_DORMANCY_TIME = "dormancy_time";
    public static final String JSON_DORMANCY_NAME = "dormancy_name";
    public static final String JSON_CAN_MULTI_FUNC_SWITCH_TYPE = "type";
    public static final String JSON_CAN_MULTI_FUNC_SWITCH_ENABLE = "enable";
    public static final String JSON_CAN_MULTI_FUNC_SWITCH_STATE = "state";
    public static final String JSON_REMOTE_ROBOT_NAME = "robot_name";
    public static final String JSON_CAN_XD_POWER = "param_xd_power";
    public static final String JSON_CAN_XD_FAN = "param_xd_fan";
    public static final String JSON_CAN_XD_RANK = "param_xd_rank";
    public static final String JSON_CAN_XD_DRY_STATUS = "param_xd_dry_status";
    public static final int JSON_CAN_XD_RANK_CLOSE = 0;
    public static final int JSON_CAN_XD_RANK_OPEN_SMALL = 1;
    public static final int JSON_CAN_XD_RANK_OPEN_LARGE = 2;
    public static final int JSON_CAN_XD_DRY_STATUS_DRY = 0;
    public static final int JSON_CAN_XD_DRY_STATUS_NORMAL = 1;
    public static final String JSON_STANDBY_FROM = "from";
    public static final String JSON_STANDBY_STATUS = "standby_status";
    public static final String JSON_STANDBY_START = "standby_start";
    public static final String JSON_STANDBY_BIT_RGB_LED = "module_bit_rgb_led";
    public static final String JSON_STANDBY_BIT_IR_LED = "module_bit_ir_led";
    public static final String JSON_STANDBY_BIT_RADAR = "module_bit_radar";
    public static final String JSON_STANDBY_BIT_WHEEL = "module_bit_wheel";
    public static final String JSON_CAN_ANTI_COLLISION_STRIP_STATUS = "param_anti_collision_strip_status";
    public static final int JSON_CAN_ANTI_COLLISION_STRIP_STATUS_COLLISION = 0;
    public static final int JSON_CAN_ANTI_COLLISION_STRIP_STATUS_RESUME = 1;
    public static final String JSON_NAV_SOUR_PLACENAME = "source_place_name";
    public static final String JSON_NAV_SOUR_POSE = "source_place_pose";
    public static final String JSON_NAV_DEST_PLACENAME = "dest_place_name";
    public static final String JSON_NAV_DEST_POSE = "dest_pose";
    public static final String JSON_NAV_DESTINATION= "destination";
    public static final String JSON_UPLOAD_FILE_PATH= "file_path";
    public static final String JSON_UPLOAD_FILE_TYPE= "file_type";
    public static final String JSON_REQUEST_API = "common_request_api";
    public static final String JSON_REQUEST_CONTENT_TYPE= "common_request_content_type";
    public static final String JSON_REQUEST_PARAMS= "common_request_params";


    public static final int DOOR_TYPE_MANUAL = 1;

    //Can内部状态
    public static final int BMS_WARNING_STATUS_CAN_CHARGING_NORMAL = 1;
    public static final int BMS_WARNING_STATUS_CAN_CHARGING_SLOW_RECHARGE = 2;
    //bms状态转化为Can内部状态处理
    public static final int BMS_WARNING_STATUS_BMS_STATUS_BASE = 1000;
    public static final int BMS_WARNING_STATUS_BMS_CHARGING_SLOW = BMS_WARNING_STATUS_BMS_STATUS_BASE + 1;

    /*------- Remote ----------*/
    public static final String CMD_REMOTE_RN_INSTALL_STATUS = "cmd_remote_rn_install_status";
    public static final String CMD_REMOTE_REGISTER = "cmd_remote_register";
    public static final String CMD_REMOTE_DETECT = "cmd_remote_detect";
    public static final String CMD_REMOTE_MODIFY_DETECT_NAME = "cmd_remote_modify_detect_name";
    public static final String CMD_REMOTE_GET_PERSON_INFO = "cmd_remote_get_person_info";
    public static final String CMD_REMOTE_GET_DOMAIN = "cmd_remote_get_domain";
    public static final String CMD_REMOTE_QRCODE = "cmd_remote_qrcode";
    public static final String CMD_REMOTE_QRCODE_MINI = "cmd_remote_qrcode_mini";
    public static final String CMD_REMOTE_REPORT_OS_TYPE = "cmd_remote_report_os_type";
    public static final String CMD_REMOTE_GET_DEFAULT_WELCOME_TTS = "cmd_remote_get_default_welcome_tts";
    public static final String CMD_REMOTE_AUTO_CHARGE_SUCCESS = "cmd_remote_auto_charge_success";
    public static final String CMD_REMOTE_AUTO_CHARGE_FAILURE_1_COUNT = "cmd_remote_auto_charge_failure_1_count";
    public static final String CMD_REMOTE_AUTO_CHARGE_FAILURE_3_COUNT = "cmd_remote_auto_charge_failure_3_count";
    public static final String CMD_REMOTE_VERIFY = "cmd_remote_verify";
    public static final String CMD_REMOTE_PHONE_VERIFY = "cmd_remote_phone_verify";
    public static final String CMD_REMOTE_SKILL = "cmd_remote_skill";
    public static final String CMD_REMOTE_POSTMSG = "cmd_remote_postmsg";
    public static final String CMD_REMOTE_GUESTINFO = "cmd_remote_guestinfo";
    public static final String CMD_REMOTE_REGISTER_TEMPID = "cmd_remote_register_tempid";
    public static final String CMD_REMOTE_BIND_STATUS = "cmd_remote_bind_status";
    public static final String CMD_REMOTE_CHECK_BIND_STATUS = "cmd_remote_check_bind_status";
    public static final String CMD_REMOTE_CHECK_BIND_STATUS_MINI = "cmd_remote_check_bind_status_mini";
    public static final String CMD_REMOTE_WAKEUP_TIMES = "cmd_remote_wakeup_times";
    public static final String CMD_REMOTE_POST_EMERGENCY_MSG = "cmd_remote_post_emergency_msg";
    public static final String CMD_REMOTE_FIRST_CONFIG_LOGIN_STATE = "cmd_remote_first_config_login_state";
    public static final String CMD_REMOTE_FIRST_CONFIG_ROBOT_INFO = "cmd_remote_first_config_robot_info";
    public static final String CMD_REMOTE_REFRESH_QRCODE = "cmd_remote_refresh_qrcode";
    public static final String CMD_REMOTE_NEXT_STATE = "cmd_remote_next_state";
    public static final String CMD_REMOTE_UNBIND_ROBOT = "cmd_remote_unbind_robot";
    public static final String CMD_REMOTE_UNBIND_ROBOT_MINI = "cmd_remote_unbind_robot_mini";
    public static final String CMD_REMOTE_ADD_FACE = "cmd_remote_add_face";
    public static final String CMD_REMOTE_ADD_FACE_LIST = "cmd_remote_add_face_list";
    public static final String CMD_REMOTE_GET_LANGUAGE_LIST = "cmd_remote_get_language_list";
    public static final String CMD_REMOTE_DELETE_FACE = "cmd_remote_delete_face";
    public static final String CMD_REMOTE_VERIFY_FACE = "cmd_remote_verify_face";
    public static final String CMD_REMOTE_DETECT_FACE = "cmd_remote_detect_face";
    public static final String CMD_REMOTE_SIMILAR_FACE = "cmd_remote_similar_face";
    public static final String CMD_REMOTE_GET_MEMBERS = "cmd_remote_get_members";
    public static final String CMD_REMOTE_REGISTER_FACE_LIST = "cmd_remote_register_face_list";
    public static final String CMD_REMOTE_UPLOAD_FACE_LIST = "cmd_remote_upload_face_list";
    public static final String CMD_REMOTE_MODIFY_ROBOT_NAME = "cmd_remote_modify_robot_name";
    public static final String CMD_REMOTE_SETTINGS_SCENARIO_LIST = "cmd_remote_settings_scenario_list";
    public static final String CMD_REMOTE_UPLOAD_SETTINGS_SCENARIO = "cmd_remote_upload_settings_scenario";
    public static final String CMD_REMOTE_SETTINGS_SCENARIO_INFO = "cmd_remote_settings_scenario_info";
    public static final String CMD_REMOTE_UPLOAD_EXTRA_FILE_PKG = "cmd_remote_upload_extra_file_pkg";
    public static final String CMD_REMOTE_DOWNLOAD_EXTRA_FILE_PKG = "cmd_remote_download_extra_file_pkg";
    public static final String CMD_REMOTE_NOTIFY_OTA_EXECUTION_EVENT = "cmd_remote_notify_otaexecution_event";
    public static final String CMD_REMOTE_UPLOAD_FILE = "cmd_remote_upload_file";
    public static final String CMD_REMOTE_REQUEST_POST = "cmd_remote_request_post";

    public static final String CMD_REMOTE_ELEVATOR_LOCK = "cmd_remote_elevator_lock";
    public static final String CMD_REMOTE_ELEVATOR_UNLOCK = "cmd_remote_elevator_unlock";
    public static final String CMD_REMOTE_ELEVATOR_STATUS = "cmd_remote_elevator_status";
    public static final String CMD_REMOTE_ELEVATOR_CONTROL = "cmd_remote_elevator_control";
    public static final String CMD_REMOTE_ELEVATOR_LIST = "cmd_remote_elevator_list";

    /*------- Dump command ----------*/
    public static final String CMD_DUMP_START_RECORD = "cmd_dump_start_record";
    public static final String CMD_DUMP_STOP_RECORD = "cmd_dump_stop_record";
    public static final String CMD_DUMP_GET_KEY_FRAME = "cmd_dump_get_key_frame";


    /*------- Dump report/upload ----------*/
    public static final String CMD_DUMP_REPORT = "cmd_dump_report";
    public static final String CMD_DUMP_UPLOAD_FILE = "cmd_dump_upload_file";
    public static final String CMD_DUMP_UPLOAD_REPORT_STATUS = "cmd_dump_upload_report_status";


    /*------- Event Tracking Level ----------*/
    public static final String CMD_CHECK_TRACK_LEVEL = "cmd_check_track_level";
    public static final String CMD_REPORT_TRACK_LEVEL = "cmd_report_track_level";

    /**
     * cap screen in special display category
     */
    public static final String CMD_CAN_CAP_SCREEN = "cmd_can_cap_screen";
    public static final String SCREEN_DISPLAY_TYPE = "screen_display_type";
    public static final String SCREEN_SHOT_RESULT = "screen_shot_result";
    public static final String SCREEN_SHOT_PATH = "screen_shot_path";
    public static final String DEFAULT_SCREEN_NAME = "default_tmp.png";
    public static final String EXTRA_SCREEN_NAME = "extra_tmp.png";

    /**
     * ---------ppt---------------
     **/
    public static final String CMD_REMOTE_PPT_MQTT_PIPE = "cmd_remote_ppt_mqtt_pipe";
    public static final String CMD_REMOTE_PPT_MQTT_ACTION = "cmd_remote_ppt_mqtt_action";

    /**
     * upload map, begin
     **/
    public static final String CMD_REMOTE_UPLOAD_MAP_PKG = "cmd_remote_upload_map_pkg";
    public static final String CMD_REMOTE_UPLOAD_PLACE_LIST = "cmd_remote_upload_place_list";
    public static final String CMD_REMOTE_DELETE_MAP = "cmd_remote_delete_map";
    public static final String CMD_REMOTE_SWITCH_MAP_INFO = "cmd_remote_switch_map_info";
    public static final String CMD_REMOTE_GET_MAP_LIST_INFO = "cmd_remote_get_map_list_info";
    public static final String CMD_REMOTE_GET_FLOOR_LIST_INFO = "cmd_remote_get_floor_list_info";
    public static final String CMD_REMOTE_DOWNLOAD_MAP_PKG = "cmd_remote_download_map_pkg";
    public static final String CMD_NAVI_PARSE_PLACE_LIST = "cmd_navi_parse_place_list";
    public static final String CMD_NAVI_SAVE_PLACE_LIST = "cmd_navi_save_place_list";
    public static final String CMD_REMOTE_UPLOAD_OPER_LOG = "cmd_remote_upload_oper_log";
    public static final String CMD_NAVI_VISION_CHARGE_START = "cmd_navi_vision_charge_start";
    public static final String CMD_NAVI_VISION_CHARGE_STOP = "cmd_navi_vision_charge_stop";
    public static final String CMD_REMOTE_DELETE_PLACE_LIST = "cmd_remote_delete_place_list";
    public static final String CMD_REMOTE_GET_PLACE_LIST = "cmd_remote_get_place_list";
    public static final String CMD_REMOTE_CANCEL_UPLOAD_FILE = "cmd_remote_cancel_upload_file";

    //巡线绘制
    public static final String CMD_NAVI_SAVE_ROAD_DATA = "cmd_navi_save_road_data";
    //自动绘制巡线
    public static final String CMD_NAVI_AUTO_DRAW_ROAD = "cmd_navi_auto_draw_road";
    //结束扩建地图
    public static final String CMD_NAVI_STOP_EXPANSION_MAP = "cmd_navi_stop_expansion_map";
    //闸机线
    public static final String CMD_NAVI_SAVE_GATE_DATA = "cmd_navi_save_gate_data";

    public static final String CMD_NAVI_SET_MAP_INFO = "cmd_navi_set_map_info";
    //开始导航精确对准
    public static final String CMD_NAVI_ALIGN_START = "cmd_navi_align_start";
    //取消导航精确对准
    public static final String CMD_NAVI_ALIGN_CANCEL = "cmd_navi_align_cancel";
    //开始人体跟随
    public static final String CMD_NAVI_START_HUMAN_FOLLOW = "cmd_navi_start_human_follow";
    //结束人体跟随
    public static final String CMD_NAVI_STOP_HUMAN_FOLLOW = "cmd_navi_stop_human_follow";
    //通过照片文件识别二维码（人体跟随二维码）
    public static final String CMD_NAVI_DETECT_QRCODE_BY_PIC = "cmd_navi_detect_qrcode_by_pic";

    public static final String CMD_NAVI_SET_LINE_SPEED = "cmd_navi_set_line_speed";


    //设置中切换语言
    public static final String CMD_REMOTE_SWITCH_LANGUAGE = "cmd_remote_switch_language";

    /**
     * skill data report
     */
    public static final String CMD_REMOTE_SKILL_DATA_REPORT = "cmd_remote_skill_data_report";

    /**
     * guide score
     **/
    public static final String CMD_GUIDE_SCORE = "cmd_guide_score";

    public static final String CMD_REMOTE_TASK_MODE_REPORT = "cmd_remote_task_mode_report";

    public static final String CMD_REMOTE_TASK_EXEC_REPORT = "cmd_remote_task_exec_report";

    public static final String CMD_REMOTE_COMMAND_EXEC_REPORT = "cmd_remote_command_exec_report";

    public static final String CMD_REMOTE_MODULE_CODE_CONFIG_UPLOAD = "cmd_remote_module_code_config_upload";

    public static final String CMD_REMOTE_PUSH_TASK_REPORT = "cmd_remote_push_task_report";

    public static final String CMD_REMOTE_GET_SPECIAL_DISH_LABELS = "cmd_remote_get_special_dish_labels";

    public static final String CMD_REMOTE_GET_APP_TOKEN = "cmd_remote_get_app_token";

    /**
     * 梯控相关远程接口
     */
    public static final String CMD_REMOTE_UPDATE_MULTI_FLOOR_INFO = "cmd_remote_update_multi_floor_info";
    public static final String CMD_REMOTE_CALL = "cmd_remote_call";
    public static final String CMD_REMOTE_DELIVER_REPORT = "cmd_deliver_report";

    /**
     * 通用递送接口
     */
    public static final String CMD_REMOTE_DELIVERY_ROBOTTASK = "cmd_remote_delivery_robottask";
    public static final String CMD_REMOTE_DELIVERY_ROBOTSTATUSNOTIFY = "cmd_remote_delivery_robotstatusnotify";
    public static final String CMD_REMOTE_DELIVERY_MONITORDETAIL= "cmd_remote_delivery_monitordetail";
    public static final String CMD_REMOTE_DELIVERY_ROBOTCOMMENT = "cmd_remote_delivery_robotcomment";

    public static final String CMD_REMOTE_TASK_EVENT_REPORT = "cmd_remote_task_event_report";

    /**
     * 梯控相关指令信息
     */
    public static final String CMD_CONTROL_GET_ELEVATOR_STATUS = "cmd_control_get_elevator_status";
    public static final String CMD_CONTROL_CALL_ELEVATOR = "cmd_control_call_elevator";
    public static final String CMD_CONTROL_RELEASE_ELEVATOR = "cmd_control_release_elevator";
    public static final String CMD_CONTROL_OPEN_ELEVATOR_DOOR = "cmd_control_open_elevator_door";
    public static final String CMD_CONTROL_CLOSE_ELEVATOR_DOOR = "cmd_control_close_elevator_door";
    public static final String CMD_CONTROL_REGISTER_ELEVATOR = "cmd_control_register_elevator";
    public static final String CMD_CONTROL_ROBOT_STATE_UPDATE = "cmd_control_robot_state_update";

    /**
     * 闸机相关指令信息
     */
    public static final String CMD_CONTROL_CALL_GATE = "cmd_control_call_gate";
    public static final String CMD_CONTROL_RELEASE_GATE= "cmd_control_release_gate";
    public static final String CMD_CONTROL_OPEN_GATE_DOOR = "cmd_control_open_gate_door";
    public static final String CMD_CONTROL_OPEN_REV_GATE_DOOR = "cmd_control_open_rev_gate_door";
    public static final String CMD_CONTROL_CLOSE_GATE_DOOR = "cmd_control_close_gate_door";
    public static final String CMD_CONFIG_GATE_DOOR = "cmd_config_gate_door";
    public static final String CMD_GATE_UPDATE_BIND_LINE_STATE = "cmd_gate_update_bind_line_state";
    public static final String CMD_GATE_GET_ALL_GATE_DEVICES = "cmd_gate_get_all_gate_devices";
    public static final String CMD_GATE_UPDATE_GATE_DEVICES_INFO = "cmd_gate_update_gate_devices_info";

    //闸机关系维护
    public static final String CMD_NAVI_FIND_GATE_RELATION = "cmd_navi_find_gate_relation";
    public static final String CMD_NAVI_FIND_BY_GATE_IDS = "cmd_navi_find_by_gate_ids";
    public static final String CMD_NAVI_FIND_BY_LINE_IDS = "cmd_navi_find_by_line_ids";
    public static final String CMD_NAVI_BATCH_INSERT_OR_UPDATE_GATE = "cmd_navi_batch_insert_or_update_gate";
    public static final String CMD_NAVI_DELETE_BY_LINE_IDS = "cmd_navi_delete_by_line_ids";
    public static final String CMD_NAVI_DELETE_BY_GATE_IDS = "cmd_navi_delete_by_gate_ids";
    public static final String CMD_NAVI_DELETE_EXCEPT_LINE_IDS = "cmd_navi_delete_except_line_ids";

    /*
     * upload map, end
     **/

    public static final String JSON_SWITCH_ANGLE = "angle";
    public static final String JSON_HEAD_NAME = "name";
    public static final String JSON_HEAD_ID = "id";
    public static final String JSON_HEAD_COUNT = "count";
    public static final String JSON_HEAD_MODE = "mode";
    public static final String JSON_HEAD_ABSOLUTE = "absolute";
    public static final String JSON_HEAD_RELATIVE = "relative";
    public static final String JSON_HEAD_HMODE = "hmode";
    public static final String JSON_HEAD_VMODE = "vmode";
    public static final String JSON_HEAD_HSPEED = "hspeed";
    public static final String JSON_HEAD_VSPEED = "vspeed";
    public static final String JSON_VISION_RECORD_PATH = "path";
    public static final String JSON_MAP_NAME = "mapName";
    public static final String JSON_MAP_URL = "mapUrl";
    public static final String JSON_MAP_IS_MAP_STATE = "isMapState";
    public static final String JSON_MAP_NEED_RE_UPLOAD = "needReUpload";
    public static final String JSON_MAP_FINISH_STATE = "finishState";
    public static final String JSON_MAP_FINISH_STATE_IS_RESET = "finishStateIsReset";
    public static final String JSON_MAP_UPDATE_TIME = "updateTime";
    public static final String JSON_MAP_OVERWRITE_LOCAL_PLACE = "overwriteLocalPlace";
    public static final String JSON_MAP_EXTRA_MD5 = "extraMd5";
    public static final String JSON_MAP_EXTRA_ID = "extraId";
    public static final String JSON_MAP_EXTRA_URL = "extraUrl";
    public static final String JSON_MAP_EXTRA_LOCAL_PATH = "extraLocalPath";
    public static final String JSON_MAP_FORBID_LINE = "forbidLine";
    public static final String JSON_MAP_UUID = "mapUuid";
    public static final String JSON_MAP_MD5 = "mapMd5";
    public static final String JSON_CAN_REMOTE_ENABLE = "enable";
    public static final String JSON_NAVI_TIME_OUT_MSG_ID = "naviTimeOutMsgId";
    public static final String JSON_NAVI_LOG_CACHE_ID = "logCacheId";
    public static final String JSON_NAVI_LOG_CACHE_STATUS = "logCacheStatus";
    public static final String JSON_NAVI_OPEN_RADAR = "openRadar"; //true-已开启 false-已关闭
    public static final String JSON_NAVI_RADAR_STATUS = "radarStatus"; //0-已开启 1-已关闭 2-开启中 3-关闭中
    public static final String JSON_NAVI_TARGET_DATA_STATE = "targetData";//0表示无Target数据  1表示有target数据

    public static final String JSON_NAVI_PAUSE_NAVIGATION = "naviPause";

    public static final String JSON_NAVI_POS_TOLERANCE = "poseTolerance";
    public static final String JSON_NAVI_ANGLE_TOLERANCE = "angleTolerance";
    public static final String JSON_NAVI_ROAD_MODE = "roadMode";

    public static final String JSON_DATA_TYPE = "dataType";
    public static final String JSON_DATA_ROAD = "roadData";
    public static final String JSON_CACHE_ID = "cacheId";
    public static final String JSON_MAP_HAS_LOCAL_MAP = "hasLocalMap";

    public static final String JSON_HEAD_HORIZONTAL = "horizontal";
    public static final String JSON_HEAD_VERTICAL = "vertical";
    public static final String JSON_HEAD_LOCATION = "location";
    public static final String JSON_HEAD_BACKWARD = "backward";
    public static final String JSON_HEAD_FORWARD = "forward";
    public static final String JSON_HEAD_CONTROL = "control";
    public static final String JSON_HEAD_START = "start";
    public static final String JSON_HEAD_LOOPER = "looper";
    public static final String JSON_HEAD_CONTINUOUS = "continuous";
    public static final String JSON_HEAD_ONE_SHOT = "oneshot";
    public static final String JSON_HEAD_INTERVAL = "interval";
    public static final String JSON_HEAD_IS_NEED_INVALID_PERSON = "isNeedInvalidPerson";
    public static final int JSON_HEAD_NEED_INVALID_PERSON = 1;
    public static final int JSON_HEAD_NOT_NEED_INVALID_PERSON = 0;
    public static final String JSON_HEAD_STOP = "stop";
    public static final String JSON_HEAD_LEFT_ANGLE = "leftangle";
    public static final String JSON_HEAD_RIGHT_ANGLE = "rightangle";
    public static final String JSON_HEAD_TIMEOUT = "timeout";
    public static final String JSON_HEAD_STATUS = "status";
    public static final String CMD_STATUS_OK = "ok";
    public static final String CMD_STATUS_FAILED = "failed";
    public static final String JSON_HEAD_PERSON_NAME = "person_name";
    public static final String JSON_HEAD_FILE_PATH = "file_path";
    public static final String JSON_TYPE = "type";
    public static final String JSON_TIMER = "timer";
    public static final String JSON_RANGE = "range";
    public static final String JSON_CONFIG = "config";
    public static final String JSON_VALUE = "value";
    public static final String JSON_FILE_PATH = "path";
    public static final String JSON_CACHE_SEC = "cacheSec";
    public static final String JSON_INTERVAL_SEC = "intervalSec";
    public static final String JSON_BEGIN_SEC = "beginSec";
    public static final String JSON_END_SEC = "endSec";

    public static final String JSON_OTA_FORCEUPATE = "is_force_update";
    public static final String JSON_OTA_IS_USER_TOUCH = "is_user_touch";
    public static final String JSON_OTA_NEED_DOWNLOAD = "need_download";
    public static final String JSON_OTA_IS_DOWNLOAD_PROGRESS = "is_download_progress";
    public static final String JSON_OTA_INSTALL_PERCENT = "install_percent";
    public static final String JSON_OTA_DOWNLOAD_FULL = "download_full_package";
    public static final String JSON_OTA_UPTIME = "command_uptime";

    public static final String JSON_OTA_TARGET_VERSION = "ota_target_version";
    public static final String JSON_OTA_TARGET_DESCRITION = "ota_target_description";
    public static final String JSON_OTA_TARGET_TYPE = "ota_target_type";
    public static final String OTA_TARGET_TYPE_UPDATE = "update";
    public static final String OTA_TARGET_TYPE_ROLL_BACK = "roll_back";
    public static final String JSON_HEAD_MASTER_SWITCH_TYPE = "switch_type";

    public static final String JSON_OTA_RESULT = "result";
    public static final String JSON_OTA_TYPE = "update_type";
    public static final String JSON_OTA_CODE = "code";
    public static final String JSON_OTA_REBOOT = "need_reboot";
    public static final String JSON_OTA_MESSAGE = "message";
    public static final String JSON_OTA_EXTRA = "extra";

    public static final String JSON_OTA_RESULT_SUCCESS = "success";
    public static final String JSON_OTA_RESULT_FAILED = "failed";
    public static final String JSON_OTA_RESULT_CONTINUE = "upgrading";
    public static final String JSON_OTA_RESULT_NO_UPDATE = "ota_no_update";
    public static final String JSON_OTA_TYPE_NORMAL = "normal";
    public static final String JSON_OTA_TYPE_ROLLBACK = "rollback";

    public static final String JSON_OTA_TASK_TYPE = "task_type";
    public static final String JSON_OTA_EVENT_TYPE = "event_type";
    public static final String JSON_OTA_EVENT_DATA = "event_data";
    public static final String JSON_OTA_FROM_VERSION = "from_version";
    public static final String JSON_OTA_TO_VERSION = "to_version";
    public static final String JSON_OTA_TO_VERSION_ID = "to_version_id";

    public static final int UPLOAD_FILE_TYPE_FACE_GALLERY = 1;
    public static final int UPLOAD_FILE_TYPE_PICTURE_REPORT = 2;
    public static final int UPLOAD_FILE_TYPE_UNQUALIFIED_PICTURE = 3;
    public static final int UPLOAD_FILE_TYPE_VIDEO = 4;
    public static final int UPLOAD_FILE_TYPE_SNAPSHOT = 5;
    public static final int UPLOAD_FILE_TYPE_LOG = 6;
    public static final int UPLOAD_FILE_TYPE_ORIONBASE = 10;
    public static final int UPLOAD_FILE_TYPE_OTHER = 100;

    public static final String UPLOAD_TYPE_TENCENT = "tencent";

    public static final String JSON_FACE_GROUP_ID = "face_group_id";
    public static final String JSON_IMAGE = "image";
    public static final String JSON_IMAGE2 = "image2";
    public static final String JSON_FACE_ID = "face_id";
    public static final String JSON_IMAGE_URL = "image_url";

    public static final String JSON_REMOTE_UPLOAD_FILE_ID = "upload_file_id";
    public static final String JSON_REMOTE_UPLOAD_FILE_TYPE = "upload_file_type";
    public static final String JSON_REMOTE_UPLOAD_SERVER_PATH = "upload_server_path";
    public static final String JSON_REMOTE_UPLOAD_FILE_PATH = "upload_file_path";
    public static final String JSON_REMOTE_UPLOAD_FILE_PARAM = "upload_file_param";
    public static final String JSON_REMOTE_UPLOAD_NEED_DELETE = "need_delete";
    public static final String JSON_REMOTE_UPLOAD_STATUS = "upload_file_status";
    public static final String JSON_REMOTE_UPLOAD_FAIL_REASON = "upload_file_fail_reason";
    public static final String REMOTE_UPLOAD_EXTRA_FILE_CANCEL = "cancel_upload_extra_file";
    public static final String REMOTE_UPLOAD_MAP_ZIP_CANCEL = "cancel_upload_map_zip_file";
    public static final String REMOTE_UPLOAD_PLACE_LIST_CANCEL = "cancel_upload_map_place_list";
    public static final String REMOTE_UPLOAD_BYTES_WRITTEN = "bytesWritten";
    public static final String REMOTE_UPLOAD_TOTAL_BYTES = "totalBytes";
    public static final String REMOTE_UPLOAD_PROGRESS = "progress";
    public static final String REMOTE_UPLOAD_REMAINING_TIME = "remainingTime";

    // remote lock
    public static final String JSON_REMOTE_LOCK_STATUS = "lock_status";
    public static final String JSON_REMOTE_LOCK_MSG = "lock_msg";
    public static final String JSON_REMOTE_LOCK_MSG_I18N = "lock_msg_i18n";

    //特殊点映射接口参数
    public static final String JSON_PLACE_TYPE = "place_type";
    public static final String JSON_PLACE_CN_NAME = "place_cn_name";
    public static final String JSON_MAPPING_PLACE_ID = "mapping_place_id";

    //输出日志
    public static final String JSON_PRINT_LOG = "print_log";

    /**
     * 危险码导致的定位丢失
     */
    public static final String ESTIMATE_LOST_BY_DANGER_CODE = "-703";

    public static final int DEFAULT_HORIZONTAL_ANGLE = 0;
    public static final int DEFAULT_VERTICAL_ANGLE = 70;

    // public static final String CMD_REPORT_NAVIGATION_STATUS

    //============== Response ===========//
    public static final String RESPONSE_OK = "ok";
    public static final String RESPONSE_ALREADY_IN_MODE = "busy";
    public static final String RESPONSE_TARGET_NOT_FOUND = "notfound";


    //Speech
    public static final String REQ_DEFAULT = "req_default";
    public static final String REQ_SPEECH_WAKEUP = "req_speech_wakeup";
    public static final String REQ_SPEECH_SLEEP = "req_speech_sleep";

    // OTA
    public static final String REQ_OTA_UPGRADE = "req_ota_upgrade";
    public static final String REQ_OTA_UPGRADE_FORCE = "req_ota_upgrade_force";
    public static final String REQ_OTA_FINISH = "req_ota_finish";
    public static final String REQ_OTA_DOWNLOAD_FULL_PAC = "req_ota_download";
    public static final String REQ_OTA_DOWNGRADE = "req_ota_downgrade";
    public static final String REQ_OTA_AUTH_DOWNGRADE = "req_ota_auth_downgrade";
    public static final String REQ_OTA_DOWNLOAD_SUCCESS = "req_ota_download_success";

    //Req
    public static final String REQ_START_NAVIGATION = "go_place";
    public static final String REQ_START_LEADING = "start_lead_person";
    public static final String REQ_START_RECEPTION = "start_reception";
    public static final String REQ_START_GUESTOUT = "guest_out_start";
    public static final String REQ_FORWARD = "go_forward";
    public static final String REQ_LEFT = "turn_left";
    public static final String REQ_RIGHT = "turn_right";
    public static final String REQ_NAVI_STOP = "navi_stop";
    public static final String REQ_HEAD_UP = "head_up";
    public static final String REQ_HEAD_DOWN = "head_down";
    public static final String REQ_HEAD_LEFT = "head_turn_left";
    public static final String REQ_HEAD_RIGHT = "head_turn_right";
    public static final String REQ_HEAD_STOP = "head_stop";
    public static final String REQ_SPEECH = "speech";
    public static final String REQ_SETLOCATION = "set_place";
    public static final String REQ_STOP_LEADING = "stop_lead_person";
    public static final String REQ_STOP_NAVIGATION = "cancel_navigation";
    public static final String REQ_STOP_RECEPTION = "exit_reception";
    public static final String REQ_STOP_GUESTOUT = "guest_out_stop";
    public static final String REQ_STOP_CURRENT = "req_stop";
    public static final String REQ_RETURN = "req_return";
    public static final String REQ_DIAGNOSTIC = "diagnostic_mode";
    public static final String REQ_SET_POSEPLACE = "set_pose_place";
    public static final String REQ_SET_POSEESTIMATE = "set_pose_estimate";
    public static final String REQ_START_CREATINGMAP = "start_creating_map";
    public static final String REQ_STOP_CREATINGMAP = "stop_creating_map";
    public static final String REQ_SWITCH_MAP = "switch_map";
    public static final String REQ_GET_PLACENAME = "get_place_name";
    public static final String REQ_GET_PLACELIST = "get_place_list";
    public static final String REQ_GO_POSITION = "go_position";
    //public static final String CMD_SPEECH_PLAYBACK = "cmd_speech_playback";
    public static final String REQ_REMOTE_CONTROL_START = "remote_control_start";
    public static final String REQ_REMOTE_CONTROL_STOP = "remote_control_stop";

    public static final String REQ_REMOTE_ROBOT_CONTROL_PPT = "ppt_robot_connect_state";
    public static final String REQ_REMOTE_MOBILE_CONTROL_PPT = "ppt_mobile_connect_state";
    public static final String REQ_REMOTE_PC_CONTROL_PPT = "ppt_pc_connect_state";

    public static final String REQ_CONTROL_PPT_PLAY = "multimedia_command&play";
    public static final String REQ_CONTROL_PPT_UP_PAGE = "general_command&return";
    public static final String REQ_REMOTE_DOWN_PAGE = "ppt_pc_down_page";
    public static final String REQ_REMOTE_PAUSE = "general_command&pause";


    public static final String REQ_PLAY_EMOJI = "play_emoji";
    public static final String REQ_STOP_EMOJI = "stop_emoji";
    public static final String REQ_SWITCH_EMOJI = "switch_emoji";
    public static final String REQ_GO_CHARGING = "go_charging";
    public static final String REQ_SYSTEM_GO_CHARGING = "system_go_charging";
    public static final String REQ_GET_MODULE_STATUS = "get_module_status";

    public static final String FIRST_CONFIG_REQ_TYPE = "first_config_req_type";
    public static final String REQ_FIRST_START_MAPPING = "start_mapping";
    public static final String REQ_FIRST_CHARGING = "first_recharging";
    public static final String REQ_FIRST_SET_CHARGING_PILE = "set_charging_pile";
    public static final String REQ_FIRST_EXIT_ALL = "exit_init";
    public static final String REQ_FIRST_POSE_ESTIMATE_OK = "pose_estimate_ok";
    public static final String REQ_FIRST_EXIT_BUILD = "exit_build";
    public static final String REQ_FIRST_TYPE_ROBOT_INFO = "type_robot_info";
    public static final String REQ_FIRST_INIT_SWITCH_MAP = "init_switch_map";
    public static final String REQ_FIRST_ROBOT = "robot";
    public static final String REQ_FIRST_LOGIN_FAIL = "req_first_login_fail";
    public static final String REQ_GO_RECEPTION_POINT = "req_go_reception_point";
    public static final String REQ_LOGIN_FAIL = "req_login_fail";
    public static final String REQ_STOP_CHARGING_BY_VOICE = "req_stop_charging_by_voice";
    public static final String REQ_STOP_CHARGING_BY_VOICE_NEW = "req_stop_charging_by_voice_new";

    //------------- login msg ------------------------ //
    public static final String REMOTE_LOGIN_HWID_ERROR = "remote_login_hwid_error";
    public static final String REMOTE_LOGIN_ROBOT_NOT_EXIST = "remote_login_robot_not_exist";
    public static final String FIRST_CONFIG_END = "first_config_end";
    public static final int FIRST_CONFIG_END_TO_NEW_MAP = 0;
    public static final int FIRST_CONFIG_END_TO_SYNC_CLOUD_MAP = 1;
    public static final int FIRST_CONFIG_END_TO_DEFAULT = 2;
    public static final String ACTIVITY_FINISH_RESULT = "activity_finish_result";
    public static final String MAP_TOOLS_FINISH_RESULT = "map_tools_finish_result";


    //------------- status report ---------------------//
    public static final String REPORT_STATUS_CHANGE = "report_status_change";
    public static final String REPORT_NAVI_ADD_LOCATION = "report_add_location";
    public static final String REPORT_NAVI_CONFIG = "navi_config";
    public static final String NAVI_UPDATE_FROM_OLD_PLACE = "update_from_old_place";
    public static final String NAVI_UPDATE_MAP_INFO = "navi_update_map_info";
    public static final String NAVI_UPDATE_PLACE = "navi_update_place";

    //------------- command running result ------------//
    public static final int CMD_SEND_SUCCESS = 1;
    public static final int CMD_SEND_WAITTING = 0;
    public static final int CMD_SEND_ERROR_UNKNOWN = -1;
    public static final int CMD_SEND_ERROR_UNSUPPORTED = -2;
    public static final int CMD_SEND_ERROR_NO_MATCH_REQ = -3;
    public static final int CMD_SEND_ERROR_CANT_RUNNING = -4;
    public static final int CMD_SEND_ERROR_RES_OCUPPIED = -5;
    public static final int CMD_SEND_ERROR_RES_NOT_HOLD = -6;
    public static final int CMD_SEND_ERROR_PERMISSION_DENIED = -7;
    public static final int CMD_SEND_ERROR_PARAMETERS_NULL = -8;

    //------------- hardware status -------------------//
    public static final int HW_STATUS_NONE = 0;
    public static final int HW_STATUS_NOT_AVAILABLE = 1;
    public static final int HW_STATUS_IDLE = 2;
    public static final int HW_STATUS_RUNING = 3;
    public static final int HW_STATUS_GO_AHEAD = 4;
    public static final int HW_STATUS_GO_BACK = 5;
    public static final int HW_STATUS_SOCKET_DISCONNECT = 6;
    public static final int HW_STATUS_SERVICE_UNUSABLE = 7;

    //------------- broadcast ------------------------//
    public static final String INTENT_SPEECH_WAKEUP = "com.ainirobot.intent.speech.WAKEUP";
    public static final String INTENT_SPEECH_SLEEP = "com.ainirobot.intent.speech.SLEEP";
    public static final String INTENT_SPEECH_FAILURE = "com.ainirobot.intent.speech.FAILURE";
    public static final String INTENT_CORE_RUNNING = "com.ainirobot.intent.core.RUNNING";
    public static final String INTENT_CORE_ERROR = "com.ainirobot.intent.core.ERROR";
    public static final String INTENT_REMOTE_LOGIN = "com.ainirobot.intent.remote.LOGIN";
    public static final String INTENT_POWER_KEY_PRESS = "com.ainirobot.intent.hardware.POWER_KEY_PRESS";
    public static final String INTENT_POWER_KEY_LONGPRESS = "com.ainirobot.action.POWER_KEY_LONGPRESS";
    public static final String INTENT_MULTI_ROBOT_STATUS_UPDATE = "com.ainirobot.intent.MULTI_ROBOT_STATUS_UPDATE";
    public static final String INTENT_PROBLEM_REPORT = "ainirobot.intent.action.PROBLEM_REPORT";


    public static final String ACTION_UPLOAD = "robot_action_upload";
    public static final String EXTRA_UPLOAD_ACTION_TYPE = "action_type";
    public static final int UPLOAD_ACTION_TYPE_UPLOAD = 1000;
    public static final int UPLOAD_ACTION_TYPE_DOWNLOAD = 1010;
    public static final String EXTRA_UPLOAD_DATA_TYPE = "upload_data_type";
    public static final int UPLOAD_DATA_TYPE_LOG = 100;
    public static final int UPLOAD_DATA_TYPE_VISION = 101;
    public static final int UPLOAD_DATA_TYPE_CHASSIS = 102;
    public static final int UPLOAD_DATA_TYPE_OTHER = 103;
    public static final int UPLOAD_DATA_TYPE_UNQUALIFIED = 104;
    public static final String EXTRA_UPLOAD_WITH_DEL = "upload_with_del";
    public static final String EXTRA_UPLOAD_DATA_PATH = "upload_data_path";
    public static final String EXTRA_UPLOAD_ANGLE = "angle";
    public static final String EXTRA_UPLOAD_ERROR_TYPE = "error_type";
    public static final String EXTRA_REMOTE_FAMILY_ID = "family_id";
    public static final String COMPANY_IN_FACE_WHITE_LIST = "company_in_face_white_list";

    //------------ Exception ------------------//
    public static final int ERROR_REMOTE = -1;


    public static final int MODULE_REQ_ID = 0;

    public static final int OTA_REQ_ID = 1001;
    public static final int REMOTE_REQ_ID = 1002;
    public static final int ELEVATOR_REQ_ID = 1003;

    //============ Debug ==============//
    public static final int DEBUG_REQ_ID = -101;

    //============ Action =============//
    public static final int ACTION_INVALID = -1;
    public static final int ACTION_LEADPERSON = 0;
    public static final int ACTION_PERSISTENT_MAX = 20;

    public static final int ACTION_COMMON_BEGIN = 100;
    public static final int ACTION_COMMON_MAX = 200;

    public static final int ACTION_DEFAULT = 11;
    public static final int ACTION_LEAD = 12;
    public static final int ACTION_FOLLOW = 13;
    public static final int ACTION_NAVIGATION = 14;
    public static final int ACTION_REGISTER = 15;
    public static final int ACTION_UNREGISTER = 16;
    public static final int ACTION_FOCUS_FOLLOW = 17;
    public static final int ACTION_WAKEUP = 18;
    public static final int ACTION_SEARCH_TARGET = 19;
    public static final int ACTION_INSPECTION = 20;
    public static final int ACTION_AUTO_NAVI_CHARGE = 21;
    public static final int ACTION_SET_START_CHARGE_POSE = 22;
    public static final int ACTION_CRUISE = 23;
    public static final int ACTION_RESET_ESTIMATE = 24;
    public static final int ACTION_LOCATE_VISION = 25;
    public static final int ACTION_SET_CURRENT_MAP = 26;
    public static final int ACTION_GONGFU = 27;
    public static final int ACTION_GET_FULL_CHECK_STATUS = 28;
    public static final int ACTION_SPECIAL_CRUISE = 29;
    public static final int ACTION_OBSTACLE_FOLLOW = 30;
    public static final int ACTION_POSE_NAVIGATION = 31;
    public static final int ACTION_NAVIGATION_BACK = 32;
    public static final int ACTION_ANGLE_RESET = 33;
    public static final int ACTION_GO_POSITION = 34;
    public static final int ACTION_BODY_FOLLOW = 35;
    public static final int ACTION_NAVIGATION_FOLLOW = 36;
    public static final int ACTION_SMART_FOCUS_FOLLOW = 37;

    public static final int ACTION_HORIZONTAL_TURN_HEAD = 40;
    public static final int ACTION_RECOGNIZE = 41;
    public static final int ACTION_ROBOT_STANDBY = 42;
    public static final int ACTION_FACE_TRACK = 43;
    public static final int ACTION_REGISTER_v0 = 44;
    public static final int ACTION_RECOGNIZE_v0 = 45;
    public static final int ACTION_REGISTER_v1 = 46;
    public static final int ACTION_RECOGNIZE_v1 = 47;
    public static final int ACTION_BLUE_FOV_LIGHT = 48;
    public static final int ACTION_RED_FOV_LIGHT = 49;
    public static final int ACTION_LEAVE_CHARGING_PILE = 50;
    public static final int ACTION_JUDGE_IN_CHARGING_PILE = 51;
    public static final int ACTION_ADVANCED_NAVIGATION = 52;
    public static final int ACTION_CONTROL_ELECTRIC_DOOR = 53;
    public static final int ACTION_HEAD_RESET_HEAD = 54;
    public static final int ACTION_RADAR_ALIGN = 55;

    public static final int ACTION_STOP_ALL_ACTIONS = 0xFFFF;

    public static final int ACTION_COMMON = 100;
    public static final int ACTION_GET_STATUS = 101;
    public static final int ACTION_NAVI_START_UPDATE = 102;
    public static final int ACTION_NAVI_GET_VERSION = 103;
    public static final int ACTION_NAVI_GO_LOCATION = 104;
    public static final int ACTION_NAVI_STOP_NAVIGATION = 105;
    public static final int ACTION_NAVI_IS_IN_NAVIGATION = 106;
    public static final int ACTION_NAVI_SET_LOCATION = 107;
    public static final int ACTION_NAVI_GET_LOCATION = 108;
    public static final int ACTION_NAVI_IS_IN_LOCATION = 109;
    public static final int ACTION_NAVI_MOVE_DIRECTION = 110;
    public static final int ACTION_NAVI_MOVE_DIRECTION_ANGLE = 111;
    public static final int ACTION_NAVI_MOVE_DISTANCE_ANGLE = 112;
    public static final int ACTION_NAVI_STOP_MOVE = 113;
    public static final int ACTION_NAVI_SET_MAX_ACCELERATION = 114;
    public static final int ACTION_NAVI_GET_NAVIGATION_ANGLE_SPEED = 115;
    public static final int ACTION_NAVI_GET_NAVIGATION_LINE_SPEED = 116;
    public static final int ACTION_NAVI_REPORT_NAVIGATION_VELOCITY_CHANGE = 117;
    public static final int ACTION_NAVI_CRUISELAYOUT_START = 118;
    public static final int ACTION_NAVI_CRUISELAYOUT_STOP = 119;
    public static final int ACTION_NAVI_CRUISE_START = 120;
    public static final int ACTION_NAVI_CRUISE_STOP = 121;
    public static final int ACTION_NAVI_RELOCATION = 122;
    public static final int ACTION_NAVI_SET_POSE_LOCATION = 123;
    public static final int ACTION_NAVI_SET_POSE_ESTIMATE = 124;
    public static final int ACTION_NAVI_SET_CONFIG = 207;
    public static final int ACTION_NAVI_GET_CONFIG = 208;
    public static final int ACTION_NAVI_START_CREATING_MAP = 125;
    public static final int ACTION_NAVI_STOP_CREATING_MAP = 126;
    public static final int ACTION_NAVI_SWITCH_MAP = 127;
    public static final int ACTION_NAVI_GET_PLACE_NAME = 128;
    public static final int ACTION_NAVI_GET_PLACE_LIST = 129;
    public static final int ACTION_NAVI_GO_POSITION = 131;
    public static final int ACTION_NAVI_IS_ESTIMATE = 133;
    public static final int ACTION_NAVI_SAVE_ESTIMATE = 134;
    public static final int ACTION_NAVI_GO_DEFAULT_THETA = 135;
    public static final int ACTION_HEAD_REGISTER = 136;
    public static final int ACTION_HEAD_UNREGISTER = 137;
    public static final int ACTION_HEAD_MOVE_HEAD = 138;
    public static final int ACTION_HEAD_SWITCH_CAMERA = 139;
    public static final int ACTION_HEAD_SET_TRACK_TARGET = 140;
    public static final int ACTION_HEAD_GET_ALL_PERSON_INFOS = 141;
    public static final int ACTION_HEAD_GET_PERSON_INFO_BY_NAME = 142;
    public static final int ACTION_HEAD_STOP_SEND_PERSON_INFOS = 143;
    public static final int ACTION_HEAD_SEARCH_PERSON_BY_NAME = 144;
    public static final int ACTION_HEAD_GET_PICTURE_BY_ID = 145;
    public static final int ACTION_REPORT_NAVIGATION_STATUS = 146;
    public static final int ACTION_HEAD_GET_LAST_POSITION = 147;
    public static final int ACTION_HEAD_IS_HEADER_CONNECTED = 148;
    public static final int ACTION_SWITCH_DETECT_ALGORITHM = 149;
    public static final int ACTION_HEAD_VIDEO_RECORD = 151;
    public static final int ACTION_HEAD_TESTCASE = 152;
    public static final int ACTION_HEAD_REGISTER_BY_PIC = 153;
    public static final int ACTION_HEAD_START_UPDATE = 154;
    public static final int ACTION_HEAD_GET_VERSION = 155;
    public static final int ACTION_HEAD_GET_UPDATE_PARAMS = 156;
    public static final int ACTION_HEAD_GET_GESTURE_INFOS = 157;
    public static final int ACTION_HEAD_STOP_GESTURE_INFOS = 158;
    public static final int ACTION_HEAD_GET_MOVEMENT = 159;
    public static final int ACTION_HEAD_GET_ALL_FACE_POS = 160;
    public static final int ACTION_HEAD_STOP_ALL_FACE_POS = 161;
    public static final int ACTION_HEAD_GET_STATUS = 162;
    public static final int ACTION_HEAD_RESTART = 163;
    public static final int ACTION_HEAD_GET_CAMERA_STATUS = 164;
    public static final int ACTION_NAVI_REMOVE_MAP = 165;
    public static final int ACTION_NAVI_REMOVE_LOCATION = 166;
    public static final int ACTION_REMOTE_REGISTER = 167;
    public static final int ACTION_REMOTE_DETECT = 168;
    public static final int ACTION_STOP_TRACK_TARGET = 169;
    public static final int ACTION_NAVI_GET_MAP_NAME = 170;
    public static final int ACTION_NAVI_GET_POSITION = 171;
    public static final int ACTION_AUTO_CHARGE_START = 172;
    public static final int ACTION_AUTO_CHARGE_END = 173;
    public static final int ACTION_AUTO_CHARGE_STATUS = 174;
    public static final int ACTION_SWITCH_AUTO_CHARGE_MODE = 175;
    public static final int ACTION_NAVI_SET_MAP_INFO = 176;
    public static final int ACTION_REMOTE_QRCODE = 177;
    public static final int ACTION_REMOTE_VERIFY = 178;
    public static final int ACTION_REMOTE_SKILL = 179;
    public static final int ACTION_REMOTE_POSTMSG = 180;
    public static final int ACTION_REMOTE_GUESTINFO = 181;
    public static final int ACTION_REMOTE_REGISTER_TEMPID = 182;
    public static final int ACTION_REMOTE_BIND_STATUS = 183;
    public static final int ACTION_EMERGENCY_STATUS = 184;
    public static final int ACTION_LAMP_ANIMATION = 185;
    public static final int ACTION_LAMP_COLOR = 186;
    public static final int ACTION_REMOTE_POST_REMOTE_MESSAGE = 187;
    public static final int ACTION_REMOTE_WAKEUP_TIMES = 188;

    public static final int ACTION_CAN_BATTERY_TIME_REMAIN = 189;
    public static final int ACTION_CAN_CHARGE_TIME_REMAIN = 190;
    public static final int ACTION_REMOTE_POST_BATTERY_TIME_REMAIN = 191;
    public static final int ACTION_REMOTE_POST_CHARGE_TIME_REMAIN = 192;

    public static final int ACTION_HEAD_GET_DEPTH_STATUS = 193;
    public static final int ACTION_HEAD_GET_FOV_STATUS = 194;

    public static final int ACTION_NAVI_GET_UPDATE_PARAMS = 195;
    public static final int ACTION_CAN_GET_MOTOR_H_VERSION = 196;
    public static final int ACTION_CAN_GET_MOTOR_V_VERSION = 197;
    public static final int ACTION_CAN_GET_PSB_VERSION = 198;
    public static final int ACTION_CAN_ROBOT_REBOOT = 199;
    public static final int ACTION_NAVI_RESET_ESTIMATE = 200;
    public static final int ACTION_REMOTE_FIRST_CHARGING = 201;
    public static final int ACTION_REMOTE_CHARGE_PILE = 202;
    public static final int ACTION_REMOTE_POST_PREPARED = 203;
    public static final int ACTION_HEAD_GET_SERIAL_NUMBER = 204;
    public static final int ACTION_NAVI_GET_SERIAL_NUMBER = 205;

    public static final int ACTION_REMOTE_POST_SET_PLACE = 206;
    public static final int ACTION_NAVI_SET_PATROL_LIST = 209;
    public static final int ACTION_NAVI_GET_PATROL_LIST = 210;
    public static final int ACTION_NAVI_GET_PLACE_LIST_WITH_NAME = 211;
    public static final int ACTION_NAVI_GET_PLACELIST_WITH_NAMELIST = 212;
    public static final int ACTION_HEAD_MASTER_SWITCH = 213;

    public static final int ACTION_CAN_GET_AUTO_CHARGE_VERSION = 214;
    public static final int ACTION_CAN_GET_BATTERY_VERSION = 215;
    public static final int ACTION_NAVI_RESUME_SPECIAL_PLACE_THETA = 216;
    public static final int ACTION_SHIPPING_MODE = 217;

    public static final int ACTION_REMOTE_UPLOAD_MAP_TO_SERVER = 218;
    public static final int ACTION_NAVI_REFRESH_MD5 = 219;

    public static final int ACTION_REMOTE_POST_CRUISE_STATUS = 220;
    public static final int ACTION_REMOTE_GET_MAP_ID = 221;

    public static final int ACTION_HEAD_RECORD_FACE_DATA = 222;

    public static final int ACTION_CAN_GET_ROTATE_SUPPORT = 223;

    public static final int ACTION_REMOTE_GET_PERSON_INFO = 224;

    public static final int ACTION_NAVI_LOAD_CURRENT_MAP = 225;

    public static final int ACTION_REMOTE_UPLOAD_MAP_PKG = 226;
    public static final int ACTION_REMOTE_UPLOAD_PLACE_LIST = 227;
    public static final int ACTION_REMOTE_DELETE_MAP = 228;
    public static final int ACTION_REMOTE_SWITCH_MAP_INFO = 229;
    public static final int ACTION_REMOTE_GET_MAP_LIST_INFO = 230;
    public static final int ACTION_DOWNLOAD_WHOLE_MAP_PKG = 231;
    public static final int ACTION_NAVI_PARSE_PLACE_LIST = 232;
    public static final int ACTION_REMOTE_UPLOAD_OPER_LOG = 233;
    public static final int ACTION_NAVI_SET_FIXED_ESTIMATE = 234;
    public static final int ACTION_NAVI_IS_HAS_VISION = 235;

    public static final int ACTION_NAVI_SET_CRUISE_ROUTE = 236;
    public static final int ACTION_NAVI_GET_CRUISE_ROUTE = 237;

    public static final int ACTION_NAVI_CLEAR_CUR_NAVI_MAP = 238;
    public static final int ACTION_NAVI_EDIT_PLACE = 239;
    public static final int ACTION_NAVI_GET_PLACELIST_BY_MAPNAME = 240;
    public static final int ACTION_REMOTE_MODIFY_DETECT_NAME = 241;
    public static final int ACTION_REMOTE_FIRST_CONFIG_LOGIN_STATE = 242;
    public static final int ACTION_REMOTE_FIRST_CONFIG_ROBOT_INFO = 243;

    public static final int ACTION_CAN_GET_INFRARED_INFO = 244;
    public static final int ACTION_CAN_START_CHARGE_WITHOUT_ESTIMATE = 245;

    public static final int ACTION_CAN_GET_WHEEL_L_VERSION = 246;
    public static final int ACTION_CAN_GET_WHEEL_R_VERSION = 247;

    public static final int ACTION_NAVI_GET_ERROR_LOG = 248;
    public static final int ACTION_NAVI_PACK_LOG_FILE = 249;
    public static final int ACTION_NAVI_GET_LOG_FILE = 250;
    public static final int ACTION_HEAD_SWITCH_TRACK_TARGET = 251;
    public static final int ACTION_CAN_SET_VERTICAL_MAX_LIMIT_ANGLE = 252;
    public static final int ACTION_REMOTE_TEXT_TO_MP3 = 253;
    public static final int ACTION_NAVI_CHECK_CUR_NAVI_MAP = 254;

    public static final int ACTION_NAVI_TIME_OUT_MSG_DELETE = 255;
    public static final int ACTION_NAVI_TIME_OUT_REPORT = 256;
    public static final int ACTION_NAVI_GET_SHOT_LOG = 257;
    public static final int ACTION_NAVI_GET_LOG_BY_ID = 258;
    public static final int ACTION_NAVI_UPDATE_LOG_STATUS_BY_ID = 259;
    public static final int ACTION_GUIDE_SCORE_RECORDING = 260;
    public static final int ACTION_NAVI_RENAME_MAP = 261;
    public static final int ACTION_NAVI_CLEAR_CRUISE_ROUTE = 262;
    public static final int ACTION_NAVI_SET_RADAR_STATUS = 263;
    public static final int ACTION_NAVI_QUERY_RADAR_STATUS = 264;
    public static final int ACTION_NAVI_CHECK_POSE_POSITION = 265;
    public static final int ACTION_HEAD_TURN_HEAD = 266;
    public static final int ACTION_REMOTE_GET_ASK_WAY_LIST = 267;
    public static final int ACTION_REMOTE_RN_INSTALL_STATUS = 268;

    public static final int ACTION_HEAD_START_VISION = 304;
    public static final int ACTION_HEAD_STOP_VISION = 305;
    public static final int ACTION_HEAD_START_HEAD_COUNT = 306;
    public static final int ACTION_HEAD_STOP_HEAD_COUNT = 307;
    public static final int ACTION_HEAD_START_PICTURE_REPORT = 308;
    public static final int ACTION_HEAD_STOP_PICTURE_REPORT = 309;
    public static final int ACTION_HEAD_START_BODY_REPORT = 310;
    public static final int ACTION_HEAD_STOP_BODY_REPORT = 311;
    public static final int ACTION_REMOTE_NEXT_STATE = 312;
    public static final int ACTION_REMOTE_REFRESH_QRCODE = 313;
    public static final int ACTION_REMOTE_SKILL_DATA_REPORT = 314;
    public static final int ACTION_NAVI_TURN_BY_NAVIGATION = 315;

    public static final int ACTION_HEAD_AVATAR_FACE_DATA = 316;
    public static final int ACTION_NAVI_CHECK_OBSTACLE = 317;
    public static final int ACTION_HEAD_RECOVERY = 318;
    public static final int ACTION_NAVI_RECOVERY = 319;
    public static final int ACTION_REMOTE_UNBIND_ROBOT = 320;
    public static final int ACTION_CAN_SET_LOCK_ENABLE = 321;
    public static final int ACTION_CAN_GET_DOOR_STATUS = 322;
    public static final int ACTION_CAN_DORMANCY_START = 323;
    public static final int ACTION_HEAD_ACTIONS = 324;
    public static final int ACTION_CAN_CAP_SCREEN = 325;
    public static final int ACTION_HEAD_PICTURE_REPORT_CONFIG = 326;
    public static final int ACTION_HEAD_UNQUALIFIED_PICTURE_CONFIG = 327;
    public static final int ACTION_HEAD_GET_HEAD_COUNT = 329;
    public static final int ACTION_CAN_GET_CPU_TEMPERATURE = 330;
    public static final int ACTION_NAVI_SAVE_PLACE_LIST = 331;
    public static final int ACTION_NAVI_GET_INTERNATIONAL_PLACE_LIST = 332;
    public static final int ACTION_NAVI_UPDATE_PLACE_LIST = 333;
    public static final int ACTION_HEAD_GET_FOV_CAMERA_INFO = 334;
    public static final int ACTION_REMOTE_TENCENT_UPLOAD = 335;
    public static final int ACTION_NAVI_ADD_MAPPING_POSE = 336;
    public static final int ACTION_NAVI_DELETE_MAPPING_POSE = 337;
    public static final int ACTION_NAVI_GET_MAP_STATUS = 338;
    public static final int ACTION_NAVI_SET_FORCE_ESTIMATE = 339;
    public static final int ACTION_NAVI_RENAME_MAPPING_POSE = 340;
    public static final int ACTION_NAVI_GET_SENSOR_STATUS = 341;
    public static final int ACTION_NAVI_CONNECT_STATUS = 342;
    public static final int ACTION_NAVI_MOVE_DISTANCE_ANGLE_WITH_OBSTACLES = 343;
    public static final int ACTION_NAVI_SET_MIN_OBSTACLES_DISTANCE = 344;
    public static final int ACTION_NAVI_MOVE_DIRECTION_ANGLE_OBSTACLES = 345;
    public static final int ACTION_NAVI_RESET_MIN_OBSTACLES_DISTANCE = 346;
    public static final int ACTION_NAVI_VISION_CHARGE_START = 347;
    public static final int ACTION_NAVI_VISION_CHARGE_STOP = 348;
    public static final int ACTION_NAVI_START_EXTEND_MAP = 349;
    public static final int ACTION_NAVI_RELOAD_MAP = 350;
    public static final int ACTION_NAVI_LOAD_MAP = 351;
    public static final int ACTION_NAVI_CANCEL_CREATE_MAP = 352;
    public static final int ACTION_NAVI_SET_RELOCATION = 353;
    public static final int ACTION_REMOTE_DELETE_PLACE_LIST = 354;
    public static final int ACTION_REMOTE_GET_PLACE_LIST = 355;
    public static final int ACTION_NAVI_QUERY_MULTIPLE_MAP_STATUS = 356;
    public static final int ACTION_NAVI_GET_MAP_INFO = 365;
    public static final int ACTION_NAVI_SET_MAP_SYNC_STATE = 366;
    public static final int ACTION_NAVI_SET_MAP_UUID = 367;
    public static final int ACTION_NAVI_SET_MAP_FINISH_STATE = 368;
    public static final int ACTION_NAVI_SET_MAP_UPDATE_TIME = 369;
    public static final int ACTION_NAVI_SET_MAP_FORBID_LINE = 370;

    public static final int ACTION_NAVI_LOAD_CURRENT_MAP_WITHOUT_POSE_ESTIMATE = 375;
    public static final int ACTION_HEAD_GET_MASK_INFO_BY_ID = 376;

    public static final int ACTION_NAVI_SAVE_ROAD_DATA = 377;
    public static final int ACTION_NAVI_AUTO_DRAW_ROAD = 378;


    public static final int ACTION_NAVI_QUERY_MULTI_FLOOR_CONFIG = 379;
    public static final int ACTION_NAVI_INSERT_MULTI_FLOOR_CONFIG = 380;
    public static final int ACTION_NAVI_UPDATE_MULTI_FLOOR_CONFIG = 381;
    public static final int ACTION_NAVI_REMOVE_MULTI_FLOOR_CONFIG = 382;
    public static final int ACTION_NAVI_GET_MULTI_FLOOR_CONFIG_AND_POSE = 383;
    /**
     * 梯控相关
     */
    public static final int ACTION_CONTROL_OPEN_ELEVATOR_DOOR = 384;
    public static final int ACTION_CONTROL_CLOSE_ELEVATOR_DOOR = 385;
    public static final int ACTION_CONTROL_RELEASE_ELEVATOR = 386;
    public static final int ACTION_CONTROL_CALL_ELEVATOR = 387;
    public static final int ACTION_CONTROL_GET_ELEVATOR_STATUS = 388;

    public static final int ACTION_NAVI_GET_MULTI_FLOOR_CONFIG_AND_COMMON_POSE = 389;

    public static final int ACTION_NAVI_STOP_EXPANSION_MAP = 390;
    public static final int ACTION_NAVI_GET_INTERNATIONAL_PLACE_LIST_FOR_REPORT = 391;

    public static final int ACTION_NAVI_MAP_HAS_VISION = 392;
    public static final int ACTION_NAVI_GET_LOCAL_MAP_NAME_LIST = 393;
    public static final int ACTION_NAVI_GET_LOCAL_MAP_INFO_LIST = 394;
    public static final int ACTION_NAVI_SAVE_GATE_DATA = 395;
    public static final int ACTION_NAVI_GET_PLACELIST_BY_TYPE = 396;
    public static final int ACTION_NAVI_GO_POSITION_BY_TYPE = 397;
    public static final int ACTION_REMOTE_CANCEL_UPLOAD_FILE = 398;
    public static final int ACTION_NAVI_QUERY_CHARGE_AREA_CONFIG = 399;
    public static final int ACTION_NAVI_INSERT_CHARGE_AREA_CONFIG = 400;
    public static final int ACTION_NAVI_UPDATE_CHARGE_AREA_CONFIG = 401;
    public static final int ACTION_NAVI_REMOVE_CHARGE_AREA_CONFIG = 402;
    public static final int ACTION_NAVI_HAS_OBSTACLE_IN_AREA = 403;
    public static final int ACTION_REMOTE_GET_FLOOR_LIST = 404;
    public static final int ACTION_NAVI_ROTATE_IN_PLACE = 405;

    public static final int ACTION_REMOTE_PPT_MQTT_PIPE = 130;
    public static final int ACTION_REMOTE_PPT_MQTT_ACTION = 132;

    //============ Action Response=====//
    public static final int ACTION_RESPONSE_RES_UNAVAILBALE = -9;
    public static final int ACTION_RESPONSE_PERMISSION_DENIED = -8;
    public static final int ACTION_RESPONSE_SEND_STOP_CMD_ERROR = -7;
    public static final int ACTION_RESPONSE_REQUEST_RES_ERROR = -6;
    public static final int ACTION_RESPONSE_PARAMETER_ERROR = -5;
    public static final int ACTION_RESPONSE_STOP_RES_ERROR = -4;
    public static final int ACTION_RESPONSE_START_ERROR = -3;
    public static final int ACTION_RESPONSE_NOT_SUPPORT_ACTION = -2;
    public static final int ACTION_RESPONSE_ALREADY_RUN = -1;
    public static final int ACTION_RESPONSE_SUCCESS = 0;
    public static final int ACTION_RESPONSE_STOP_SUCCESS = 3;

    //navigationElevatorAction action
    public enum ElevatorAction {
        ACTION_DEFAULT,
        ACTION_NEED_ELEVATOR,
        ACTION_MULTI_ROBOT,
        ACTION_DEFAULT_NAVI,
        ACTION_NO_NEED_ELEVATOR,
        ACTION_ALREADY_INSIDE_ELEVATOR,
        ACTION_NAVI_SUC,
        ACTION_NAVI_FAIL,
        ACTION_ELEVATOR_ARRIVED,
        ACTION_SWITCH_MAP_SUC,
        ACTION_BACK_GATE,
        ACTION_CONTINUE_TO_CENTER,
        ACTION_NEED_GATE,
        ACTION_NO_NEED_GATE,
        ACTION_GATE_NAV,
        ACTION_GATE_OPENED,
        ACTION_REGISTER_ELEVATOR_SUC,
        ACTION_NEED_ELEVATOR_WAIT_POINT,
        ACTION_NO_NEED_ELEVATOR_WAIT_POINT,
        ACTION_GO_ELEVATOR_WAIT_POINT_SUC,
    }

    //navigationElevatorAction error
    private static final int BASE_ELEVATOR_ERROR = -32750000;
    public static final int ERROR_ELEVATOR_INIT_CONTROL = BASE_ELEVATOR_ERROR - 29;
    public static final int ERROR_REGISTER_ELEVATOR_FAILED = BASE_ELEVATOR_ERROR - 28;
    public static final int ERROR_ELEVATOR_CONTROL = BASE_ELEVATOR_ERROR - 27;
    public static final int ERROR_PARAMS_JSON_PARSER_ERROR = BASE_ELEVATOR_ERROR - 26;
    public static final int ERROR_PARAMS_TARGET_FLOOR_INVALID = BASE_ELEVATOR_ERROR - 25;
    public static final int ERROR_ELEVATOR_BLACKOUT = BASE_ELEVATOR_ERROR - 24;
    public static final int ERROR_NOT_POSE_UPDATE = BASE_ELEVATOR_ERROR - 23;
    public static final int ERROR_NOT_FOUND_ELEVATOR_POSE_NAME = BASE_ELEVATOR_ERROR - 22;
    public static final int ERROR_NOT_FOUND_ELEVATOR_PATH = BASE_ELEVATOR_ERROR - 21;
    public static final int ERROR_ELEVATOR_CENTER_NOT_ARRIVED = BASE_ELEVATOR_ERROR - 20;
    public static final int ERROR_ELEVATOR_GATE_NOT_ARRIVED = BASE_ELEVATOR_ERROR - 19;
    public static final int ERROR_EXIT_ELEVATOR_FAILED = BASE_ELEVATOR_ERROR - 18;
    public static final int ERROR_RESTART_TAKE_ELEVATOR_FAILED = BASE_ELEVATOR_ERROR - 17;
    public static final int ERROR_ENTER_ELEVATOR_ROLLBACK_FAILED = BASE_ELEVATOR_ERROR - 16;
    public static final int ERROR_ENTER_ELEVATOR_FAILED = BASE_ELEVATOR_ERROR - 15;
    public static final int ERROR_CLOSE_ELEVATOR_DOOR_FAILED = BASE_ELEVATOR_ERROR - 14;
    public static final int ERROR_OPEN_ELEVATOR_DOOR_FAILED = BASE_ELEVATOR_ERROR - 13;
    public static final int ERROR_GET_CURRENT_MAP_FAILED = BASE_ELEVATOR_ERROR - 12;
    public static final int ERROR_GET_ELEVATOR_STATUS_FAILED = BASE_ELEVATOR_ERROR - 11;
    public static final int ERROR_TAKE_ELEVATOR_TIME_OUT = BASE_ELEVATOR_ERROR - 10;
    public static final int ERROR_CALL_ELEVATOR_TIME_OUT = BASE_ELEVATOR_ERROR - 9;
    public static final int ERROR_GET_CURRENT_MAP_GROUP_LIST_FAILED = BASE_ELEVATOR_ERROR - 8;
    public static final int ERROR_GET_TARGET_FLOOR_MAP_NAME_FAILED = BASE_ELEVATOR_ERROR - 7;
    public static final int ERROR_GET_TARGET_CENTER_POSE_FAILED = BASE_ELEVATOR_ERROR - 6;
    public static final int ERROR_CALL_ELEVATOR_FAILED = BASE_ELEVATOR_ERROR - 5;
    public static final int ERROR_SET_WHEEL_FREE_FAILED = BASE_ELEVATOR_ERROR - 4;
    public static final int ERROR_SET_WHEEL_LOCK_FAILED = BASE_ELEVATOR_ERROR - 3;
    public static final int ERROR_SET_FORCE_ESTIMATE_FAILED = BASE_ELEVATOR_ERROR - 2;
    public static final int ERROR_SWITCH_MAP_FAILED = BASE_ELEVATOR_ERROR - 1;

    //梯控
    private static final int BASE_ELEVATOR_STATUS = 32750000;
    public static final int STATUS_REGISTER_ELEVATOR_FAIL = BASE_ELEVATOR_STATUS + 30;   //注册电梯失败
    public static final int STATUS_REGISTER_ELEVATOR_SUC = BASE_ELEVATOR_STATUS + 29;   //注册电梯成功
    public static final int STATUS_START_REGISTER_ELEVATOR = BASE_ELEVATOR_STATUS + 28;   //开始注册电梯
    public static final int STATUS_EXIT_ELEVATOR_SUC = BASE_ELEVATOR_STATUS + 27;   //出梯成功
    public static final int STATUS_EXIT_ELEVATOR_START = BASE_ELEVATOR_STATUS + 26; //开始出梯
    public static final int STATUS_UPDATE_RELOCATION_SUC = BASE_ELEVATOR_STATUS + 25; //重定位成功
    public static final int STATUS_UPDATE_START_RELOCATION = BASE_ELEVATOR_STATUS + 24;//开始重定位
    public static final int STATUS_ARRIVED_ELEVATOR_CENTER_CORRECT_ORIENTATION = BASE_ELEVATOR_STATUS + 23;
    public static final int STATUS_PREPARE_TAKE_ELEVATOR = BASE_ELEVATOR_STATUS + 22;
    public static final int STATUS_BACK_TO_ELEVATOR_CENTER = BASE_ELEVATOR_STATUS + 21;
    public static final int STATUS_UPDATE_SWITCH_MAP_SUCCESS = BASE_ELEVATOR_STATUS + 20;
    public static final int STATUS_UPDATE_SWITCH_MAP_START = BASE_ELEVATOR_STATUS + 19;
    public static final int STATUS_UPDATE_ELEVATOR_DOOR_STATE = BASE_ELEVATOR_STATUS + 18;
    public static final int STATUS_ELEVATOR_FLOOR_NOT_FOUND_IN_MAP_MAPPING = BASE_ELEVATOR_STATUS + 17;
    public static final int STATUS_RETRY_EXIT_ELEVATOR = BASE_ELEVATOR_STATUS + 16;
    public static final int STATUS_START_GO_DESTINATION = BASE_ELEVATOR_STATUS + 15;
    public static final int STATUS_ROBOT_CURRENT_MAP_FLOOR_INFO = BASE_ELEVATOR_STATUS + 14;
    public static final int STATUS_ELEVATOR_ROBOT_POSITION_INFO = BASE_ELEVATOR_STATUS + 13;
    public static final int STATUS_ELEVATOR_FLOOR_INFO = BASE_ELEVATOR_STATUS + 12;
    public static final int STATUS_START_BACK_TO_ELEVATOR_GATE = BASE_ELEVATOR_STATUS + 11;
    public static final int STATUS_GO_CENTER_FAILED_AND_TRY_BACK = BASE_ELEVATOR_STATUS + 10;
    public static final int STATUS_ROBOT_OUT_OF_ELEVATOR = BASE_ELEVATOR_STATUS + 9;
    public static final int STATUS_TAKE_ELEVATOR_TO_TARGET_FLOOR_SUCCESS = BASE_ELEVATOR_STATUS + 8;
    public static final int STATUS_ELEVATOR_ARRIVED_TARGET_FLOOR = BASE_ELEVATOR_STATUS + 7;
    public static final int STATUS_ELEVATOR_ARRIVED_CURRENT_FLOOR = BASE_ELEVATOR_STATUS + 6;
    public static final int STATUS_ARRIVED_ELEVATOR_CENTER = BASE_ELEVATOR_STATUS + 5;
    public static final int STATUS_ARRIVED_ELEVATOR_GATE = BASE_ELEVATOR_STATUS + 4;
    public static final int STATUS_START_GO_ELEVATOR_CENTER = BASE_ELEVATOR_STATUS + 3;
    public static final int STATUS_START_GO_ELEVATOR_GATE = BASE_ELEVATOR_STATUS + 2;
    public static final int STATUS_CALL_ELEVATOR_AND_WAIT = BASE_ELEVATOR_STATUS + 1;

    public static final int STATUS_UPDATE_RETRY_ERROR = 32760000; //上报失败消息但不停止任务
    //呼梯成功，但电梯一直未到达
    public static final String STATUS_CALL_SUC_ELEVATOR_NOT_ARRIVED = "status_call_suc_elevator_not_arrived";
    //重试进梯次数过多
    public static final String STATUS_ENTER_ELEVATOR_MUCH_TIME = "status_enter_elevator_much_time";
    //重试出梯次数过多
    public static final String STATUS_EXIT_ELEVATOR_MUCH_TIME = "status_exit_elevator_much_time";
    //梯控异常，一直导航去电梯中心
    public static final String STATUS_CONTINUE_TO_CENTER = "status_continue_to_center";
    //回正机器人朝向失败
    public static final String STATUS_RESUME_TIME_OUT = "status_resume_time_out";

    //梯控初始化错误
    public static final String ERROR_INIT_ELEVATOR_CONTROL = "error_init_elevator_control";

    //闸机状态码
    private static final int BASE_PASS_GATE_STATUS = 32770000;
    public static final int STATUS_START_GO_FIRST_GATE = BASE_PASS_GATE_STATUS + 1;
    public static final int STATUS_ARRIVED_GATE_FIRST_POSE = BASE_PASS_GATE_STATUS + 2;
    public static final int STATUS_CALL_GATE_AND_WAIT = BASE_PASS_GATE_STATUS + 3;
    public static final int STATUS_GATE_OPENED = BASE_PASS_GATE_STATUS + 4;
    public static final int STATUS_START_GO_SECOND_GATE = BASE_PASS_GATE_STATUS + 5;
    public static final int STATUS_ARRIVED_GATE_SECOND_POSE = BASE_PASS_GATE_STATUS + 6;



    private static final int BASE_PASS_GATE_ERROR = -32770000;
    public static final int ERROR_CALL_GATE_FAILED = BASE_PASS_GATE_ERROR - 5;
    public static final int ERROR_CLOSE_GATE_DOOR_FAILED = BASE_PASS_GATE_ERROR - 14;
    public static final int ERROR_OPEN_GATE_DOOR_FAILED = BASE_PASS_GATE_ERROR - 13;
    public static final int ERROR_GATE_CONTROL = BASE_PASS_GATE_ERROR - 27;






    public static final int RESPONSE_INVALID_STATUS = Integer.MAX_VALUE;

    public static final int RESULT_OK = 1;
    public static final int RESULT_FAILURE = 2;
    public static final int RESULT_STOP = 0;

    public static final int RESULT_GUEST_FARAWAY = 100;
    public static final int RESULT_GUEST_LOST = 101;
    public static final int RESULT_NAVIGATION_ARRIVED = 102;
    public static final int RESULT_DESTINATION_AVAILABLE = 103;
    public static final int RESULT_DESTINATION_IN_RANGE = 104;
    public static final int RESULT_ROBOT_IN_ELEVATOR = 105;
    public static final int RESULT_ROBOT_NOT_IN_ELEVATOR = 106;

    public static final int ERROR_RESOURCE_LOCK = -101;
    public static final int ERROR_PARAMETER = -102;
    public static final int ERROR_SWITCH_CAMERA = -103;
    public static final int ERROR_NAVIGATION = -104;
    public static final int ERROR_HEAD = -105;
    public static final int ERROR_TARGET_LOST = -106;
    public static final int ERROR_TARGET_NOT_FOUND = -107;
    public static final int ERROR_DESTINATION_NOT_EXIST = -108;
    public static final int ERROR_DESTINATION_CAN_NOT_ARRAIVE = -109;
    public static final int ERROR_REGISTER_TIMEOUT = -110;
    public static final int ERROR_UNREGISTER_TIMEOUT = -111;
    public static final int ERROR_NO_MATCH_REQ = -112;
    public static final int ERROR_IN_DESTINATION = -113;
    public static final int ERROR_RESULT_PARSE = -114;
    public static final int ERROR_GUEST_STRANGE = -115;
    public static final int ERROR_NOT_ESTIMATE = -116;
    public static final int ERROR_SET_TRACK_FAILED = -117;
    public static final int ERROR_MOVE_HEAD_FAILED = -118;
    public static final int ERROR_LISTENER_INVALID = -119;
    public static final int ERROR_NAVIGATION_FAILED = -120;
    public static final int ERROR_FOLLOW_TIME_OUT = -121;
    public static final int ERROR_FOLLOW_NAVI_LOST_TIME_OUT = -122;
    public static final int ERROR_FOLLOW_OUT_OF_MAP_TIME_OUT = -123;
    public static final int ERROR_WHEEL_OVER_CURRENT_RUN_OUT = -124;
    public static final int ERROR_MULTI_ROBOT_WAITING_TIMEOUT = -125;
    public static final int ERROR_MULTI_ROBOT_CONFIG_ERROR = -126;
    public static final int ERROR_MULTIPLE_MODE_ERROR = -127;
    public static final int ERROR_ESTIMATE_ERROR = -128;
    public static final int ERROR_NO_AVAILABLE_DESTINATION = -129;
    public static final int ERROR_PATH_INFO = -130;

    public static final int ERROR_RECOGNIZE_GET_PICTURE_FAILED = -131;
    public static final int ERROR_RECOGNIZE_REMOTE_DETECT_FAILED = -132;
    public static final int ERROR_RECOGNIZE_TIMEOUT = -133;
    public static final int ERROR_FOLLOW_SET_TRACK_TIME_OUT = -134;
    public static final int ERROR_FEATURE_NOT_SUPPORT = -135;
    public static final int ERROR_NAVIGATION_AVOID_TIMEOUT = -136;
    public static final int ERROR_ELECTRIC_DOOR_BLOCK = -137;
    public static final int ERROR_ELECTRIC_DOOR_UPPER_BLOCK = -138;
    public static final int ERROR_ELECTRIC_DOOR_LOWER_BLOCK = -139;
    public static final int ERROR_ELECTRIC_DOOR_TIMEOUT = -140;
    public static final int ERROR_NAVIGATION_RESET_HEAD = -137;
    public static final int ERROR_FOLLWING_LOST_TIMEOUT = -138;

    public static final int STATUS_POSITION_REPORT = 1001;
    public static final int STATUS_GUEST_APPEAR = 1002;
    public static final int STATUS_GUEST_LOST = 1003;
    public static final int STATUS_GUEST_NOT_FOUND = 1004;
    public static final int STATUS_GUEST_INFO = 1005;
    public static final int STATUS_INFO_UPDATE = 1006;
    public static final int STATUS_GUEST_WAIT_TIMEOUT = 1007;
    public static final int STATUS_GUEST_NEAR = 1008;
    public static final int STATUS_DEST_NEAR = 1012;
    public static final int STATUS_TRACK_TARGET_SUCCEED = 1013;
    public static final int STATUS_START_NAVIGATION = 1014;
    public static final int STATUS_START_CRUISE = 1015;
    public static final int STATUS_GOAL_OCCLUDED = 1016;
    public static final int STATUS_GOAL_OCCLUDED_END = 1017;
    public static final int STATUS_NAVI_AVOID = 1018;
    public static final int STATUS_NAVI_AVOID_END = 1019;
    public static final int STATUS_NAVI_OUT_MAP = 1020;
    public static final int STATUS_CRUISE_REACH_POINT = 1021;
    public static final int STATUS_STOP_CREATE_MAP_EXPECTED_TIME = 1022;
    public static final int STATUS_NAVI_OBSTACLES_AVOID = 1023;
    public static final int STATUS_ALREADY_AT_START_CHARGE_POINT = 1024;
    public static final int STATUS_NAVI_GLOBAL_PATH_FAILED = 1025;
    public static final int STATUS_NAVI_OBSTACLES_DISAPPEAR = 1026;
    public static final int STATUS_PERSON_LOST_WARNING = 1027;
    public static final int STATUS_FOLLOW_TRACKING = 1028;
    public static final int STATUS_NAVI_GLOBAL_PATH_SUCCESS = 1029;
    public static final int STATUS_FOLLOW_PERSON_NULL_STOP = 1030;
    public static final int STATUS_FOLLOW_PERSON_FPS = 1031;
    public static final int STATUS_FOLLOW_RESET_TRACK = 1032;
    public static final int STATUS_VISION_CHARGE_START_GOTO_CHARGING_PILE = 1033;//开始回怼充电桩
    public static final int STATUS_NAVI_MULTI_ROBOT_WAITING = 1034;
    public static final int STATUS_NAVI_MULTI_ROBOT_WAITING_END = 1035;
    public static final int STATUS_NAVI_GO_STRAIGHT = 1036;
    public static final int STATUS_NAVI_TURN_LEFT = 1037;
    public static final int STATUS_NAVI_TURN_RIGHT = 1038;
    public static final int STATUS_NAVI_SET_PRIORITY_FAILED = 1039;
    public static final int STATUS_NAVI_MULTI_MAP_NOT_MATCH = 1040;
    public static final int STATUS_NAVI_MULTI_LORA_DISCONNECT = 1041;
    public static final int STATUS_NAVI_MULTI_LORA_CONFIG_FAIL = 1042;
    public static final int STATUS_NAVI_MULTI_VERSION_NOT_MATCH = 1043;
    public static final int STATUS_NAVI_WHEEL_SLIP = 1044;
    public static final int STATUS_ESTIMATE_LOST = 1045;
    public static final int STATUS_NAVI_REPLACE_DESTINATION = 1048;
    public static final int STATUS_NAVI_NEAR_DESTINATION = 1049;
    public static final int STATUS_DISTANCE_WITH_DESTINATION = 1050;
    public static final int STATUS_NAVI_AVOID_IMMEDIATELY = 1051;
    public static final int STATUS_NAVI_ARRIVED_REPLACE_DESTINATION = 1052;
    public static final int STATUS_DOWNLOAD_MAP_CLOUD_PLACE_EMPTY = 1053;
    public static final int STATUS_ELECTRIC_DOOR_BLOCK = 1054;
    public static final int STATUS_ELECTRIC_DOOR_UPPER_BLOCK = 1055;
    public static final int STATUS_ELECTRIC_DOOR_LOWER_BLOCK = 1056;
    public static final int STATUS_START_GOTO_RADAR_ALIGN = 1057;//开始雷达对准
    public static final int STATUS_RADAR_ALIGN_RETRY = 1058;//雷达对准重试
    public static final int STATUS_UPLOAD_MAP_ZIP_PROGRESS = 1059;
    public static final int STATUS_HUMAN_FOLLOWING_INIT_SUCCESS = 1060;//找到人体&&开始跟踪
    public static final int STATUS_HUMAN_FOLLOWING_TRACKING_LOST = 1061;//人体丢失
    public static final int STATUS_HUMAN_FOLLOWING_TRACKING_START_FAILED = 1062;//人体跟随 API 调用失败
    public static final int STATUS_HUMAN_FOLLOWING_ID_CHANGE = 1063;//跟随 ID 变化，只会发生在1060事件之前
    public static final int STATUS_HUMAN_FOLLOWING_LONG_TIME_NO_TAG = 1064;//长时间未找到人体
    public static final int STATUS_CHARGE_WAIT_IN_AREA = 1065; // 在充电等待区等待
    public static final int STATUS_CHARGE_GO_CHARGE_POINT = 1066; // 从充电等待区前往回充点




    //============ Sync Action : Get status from CoreService data area directly =============//
    public static final int SYNC_ACTION_IS_CHARGING = 101;
    public static final int SYNC_ACTION_GET_BATTERY_LEVEL = 102;
    public static final int SYNC_ACTION_GET_INSPECTION_RESULT = 103;
    public static final int SYNC_ACTION_GET_BIND_INFO = 104;
    public static final int SYNC_ACTION_IS_ROBOT_ESTIMATE = 105;
    public static final int SYNC_ACTION_IS_CHARGE_PILE_EXIT = 106;
    public static final int SYNC_ACTION_IS_SUPPORT_HEAD_REVERSE = 107;
    public static final int SYNC_ACTION_IS_IN_LOCATION = 108;
    public static final int SYNC_ACTION_IS_IN_RECEPTION_LOCATION = 109;
    public static final int SYNC_ACTION_GET_PLACE_DISTANCE = 110;
    public static final int SYNC_ACTION_GET_SPECIAL_LOCATION = 111;
    public static final int SYNC_ACTION_GET_CURRENT_LOCATION = 112;
    public static final int SYNC_ACTION_GET_CRUISE_ROUTE = 113;
    public static final int SYNC_ACTION_GET_MAP_NAME = 114;
    public static final int SYNC_ACTION_GET_ALL_LOCATION = 115;
    public static final int SYNC_ACTION_GET_CHANNEL_INFO = 116;
    public static final int SYNC_ACTION_GET_MQTT_INFO = 117;
    public static final int SYNC_ACTION_SYNC_APP_DATA_STATUS = 118;
    public static final int SYNC_ACTION_GET_LOCAL_AND_SERVER_LANGUAGE = 120;
    public static final int SYNC_ACTION_GET_LOCAL_SUPPORT_LANGUAGE = 121;
    public static final int SYNC_ACTION_GET_REMOTE_LANGUAGE = 122;
    public static final int SYNC_ACTION_IS_LOADING_POSE = 123;
    public static final int SYNC_ACTION_GET_CURRENT_MULTI_FLOOR_INFO = 124;
    public static final int SYNC_ACTION_GET_MULTI_FLOOR_INFOS = 125;
    public static final int SYNC_ACTION_IS_PLACE_EXISTS = 126;
    public static final int SYNC_ACTION_FLOOR_NAME = 127;
    public static final int SYNC_ACTION_MULTI_FLOOR_INFO_BY_INDEX = 128;
    public static final int SYNC_ACTION_GET_ELEVATOR_RANGE = 129;
    public static final int SYNC_ACTION_FIRST_CONFIG_ACTIVATION_STATE = 130;
    public static final int SYNC_ACTION_GET_PLACE_POSE_DISTANCE = 131;
    public static final int SYNC_ACTION_GET_SPECIAL_LOCATION_BY_TYPEID = 132;
    public static final int SYNC_ACTION_GET_ANGLE_SPEED = 133;
    public static final int SYNC_ACTION_GET_LINE_SPEED = 134;

    public static final String STATUS_RESULT_PARAMS_ERROR = "status_result_params_error";
    public static final String STATUS_RESULT_UNSUPPORT_CMDTYPE = "status_result_unsupport_cmdtype";

    public static final String RESULT_TRUE = "true";
    public static final String RESULT_FALSE = "false";

    public static final int RECOGNIZE_REASON_OK = 0;
    public static final int RECOGNIZE_REASON_OK_BUT_STRANGE = 1;
    public static final int RECOGNIZE_REASON_GET_PICTURE_FAILED = 2;
    public static final int RECOGNIZE_REASON_REMOTE_DETECT_FAILED = 3;
    public static final int RECOGNIZE_REASON_TIMEOUT = 4;

    //add by lijinxin
    public static final int STATUS_GUEST_FARAWAY = 1009;
    public static final int STATUS_LEAD_NORMAL = 1010;
    public static final int STATUS_LEAD_DESTINATION_ARRIVE = 1011;

    public static final int STATUS_GUEST_STOP_FOLLOW = 1100;
    public static final int STATUS_GUEST_STRANGE = 1101;


    //============ Res Id===============//
    public static final int RES_INVALID_ID = -1;
    public static final int RES_HEAD_ID = 0;
    public static final int RES_HEAD_CAMERA_ID = 1;
    public static final int RES_CHASSIS_ID = 2;
    public static final int RES_CHASSIS_LIGHT_ID = 3;
    public static final int RES_RADAR_ID = 4;
    public static final int RES_MAX_ID = 5;


    //============= Params key ============//
    public static final String PARAMS_PERSON_NAME = "personName";
    public static final String PARAMS_TIMEOUT = "timeout";


    /**
     * 高级导航策略
     */
    public enum AdvNaviStrategy {
        /**
         * 不用处理多机，默认导航
         */
        DEFAULT,
        /**
         * 乘梯导航
         */
        ELEVATOR,
        /**
         * 根据lora优先级调度
         */
        LORA_PRIORITY,
        /**
         * 导航去补位区最后点位
         */
        TO_LAST_POINT,
        /**
         * 动态计算补位区点位
         */
        DYNAMIC_POINT,
        /**
         * lora优先级星形补位
         */
        STAR_BY_LORA,
        /**
         * 点位优先级星形补位
         */
        STAR_BY_POINT,
        /**
         * 点位优先级火车补位
         */
        TRAIN_BY_POINT,
        /**
         * 点位优先级动态火车补位
         */
        DYNAMIC_TRAIN_BY_POINT,
        /**
         * 闸机导航
         */
        GATE,

    }

    public enum CameraType {
        FORWARD("forward"),
        BACKWARD("backward");

        String value;

        CameraType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public enum TrackMode {
        FACE(2),
        BODY_LEAD(4),
        BODY_FOLLOW(5);

        int value;

        TrackMode(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    //============= Result =========//
    public static final String NAVIGATION_OK = "true";
    public static final String NAVIGATION_FAILED = "false";
    public static final String NAVIGATION_CANCELED = "canceled";

    public static final String MESSAGE_SUCCEED = "Succeed";
    public static final String MESSAGE_FAILED = "Failed";

    //============= Follow Policy ===========//
    public static final int FOLLOW_FACE_POLICY = 0;
    public static final int FOLLOW_BODY_POLICY = 1;
    public static final int FOLLOW_MAX_POLICY = 2;

    //============= Speech Skill ===========//
    public static final String REQ_WEATHER_GET_WEATHER = "weather_get_weather";
    public static final String REQ_MEDICAL_LINE = "yi_hao_xian_yi_hao_xian";
    public static final String REQ_CHAT = "chat_chat";
    public static final String REQ_SEARCH_CALENDAR = "calendar_search_calendar";
    public static final String REQ_TELL_ME_WHY = "tell_me_why";
    public static final String REQ_ASK_SKILL = "chat_ask_skill";
    public static final String REQ_OTHER_OTHER = "other_other";
    public static final String REQ_OPEN_APP = "open_app";
    public static final String REQ_OPEN_PPT = "open_ppt";
    public static final String CATEGORY_OPEN_APP = "com.category.open.app";

    //============= lamb param ===========//
    @Deprecated
    public static final String JSON_LAMB_TYPE = "type";
    @Deprecated
    public static final String JSON_LAMB_TARGET = "target";
    @Deprecated
    public static final String JSON_LAMB_RGB_START = "rgb_start";
    @Deprecated
    public static final String JSON_LAMB_RGB_END = "rgb_end";
    @Deprecated
    public static final String JSON_LAMB_START_TIME = "start_time";
    @Deprecated
    public static final String JSON_LAMB_END_TIME = "end_time";
    @Deprecated
    public static final String JSON_LAMB_ON_TIME = "on_time";
    @Deprecated
    public static final String JSON_LAMB_REPEAT = "repeat";
    @Deprecated
    public static final String JSON_LAMB_RGB_FREEZE = "rgb_freeze";
    @Deprecated
    public static final String JSON_LAMB_COLOR_RGB_VALUE = "color_rgb_value";

    public static final String JSON_LAMP_TYPE = "type";
    public static final String JSON_LAMP_TARGET = "target";
    public static final String JSON_LAMP_RGB_START = "rgb_start";
    public static final String JSON_LAMP_RGB_END = "rgb_end";
    public static final String JSON_LAMP_START_TIME = "start_time";
    public static final String JSON_LAMP_END_TIME = "end_time";
    public static final String JSON_LAMP_ON_TIME = "on_time";
    public static final String JSON_LAMP_REPEAT = "repeat";
    public static final String JSON_LAMP_RGB_FREEZE = "rgb_freeze";
    public static final String JSON_LAMP_COLOR_RGB_VALUE = "color_rgb_value";
    public static final String JSON_LAMP_COLOR_RGB_SET = "color_rgb_set";
    public static final String JSON_LAMP_SIGLE_TIME = "sigle_time";
    public static final String JSON_LAMP_RGB_BASE = "rgb_base";
    public static final String JSON_LAMP_RGB_MOVE = "rgb_move";
    public static final String JSON_LAMP_RGB_MIDDLE = "rgb_middle";

    public static final int LAMP_TYPE_COLOR = 1;
    public static final int LAMP_TYPE_BREATH = 2;
    public static final int LAMP_TYPE_NAVIGATION = 7;
    public static final int LAMP_TYPE_DOOR = 10;

    public static final int LAMP_TARGET_FOOT = 0;
    public static final int LAMP_TARGET_POWER_INSIDE = 1;
    public static final int LAMP_TARGET_POWER_OUTSIDE = 2;

    //============== remote update ===========//
    public static final String REMOTE_SESSION_UPDATE = "session_update";
    public static final String REMOTE_SKILL_UPDATE = "skill_update";


    //==============  register ===========//
    public static final int STATUS_COLLECT_PICTURE = 1200;

    public static final String COLLECT_PERSON_INFOS_FAILED = "collect_person_infos_failed";
    public static final String COLLECT_PICTURE_FAILED = "collect_picture_failed";
    public static final String REGISTER_REMOTE_SERVER_EXIST = "register_remote_server_exist";
    public static final String REGISTER_REMOTE_SERVER_NEW = "register_remote_server_new";
    public static final String REGISTER_REMOTE_PARSE_NULL = "register_remote_parse_null";
    public static final String REGISTER_REMOTE_TYPE = "register_remote_type";
    public static final String REGISTER_REMOTE_NAME = "register_remote_name";
    public static final String REGISTER_REMOTE_DETECT_USER_ID = "register_remote_detect_user_id";
    public static final String REGISTER_REMOTE_DETECT_ROLE = "register_remote_detect_role";
    public static final String REGISTER_TIMEOUT = "register_timeout";
    public static final String REGISTER_DETECT = "register_detect";


    //==============  821apk version ===========//
    public static final String VERSION_ROBOT_OS = "version_robot_os";
    public static final String VERSION_MODULE_APP = "version_module_app";

    //==============  inspection result ===========//
    public static final int INSPECTION_PASS = 1;
    public static final int INSPECTION_ERROR_821 = -1;
    public static final int INSPECTION_ERROR_CAN = -2;
    public static final int INSPECTION_ERROR_HEAD = -3;
    public static final int INSPECTION_ERROR_NAVIGATION = -4;
    public static final int INSPECTION_ACTION_START_STATUS = 101;
    public static final String INSPECTION_ACTION_START_MSG = "start";

    //------------ Command status -----------//
    public static final String NAVIGATION_OCCLUDED = "occluded";
    public static final String NAVIGATION_OCCLUDED_END = "Occluded end";
    public static final String NAVIGATION_AVOID = "Avoid";
    public static final String NAVIGATION_AVOID_END = "Avoid end";
    public static final String NAVIGATION_OUT_MAP = "Out of map";
    public static final String NAVIGATION_OBSTACLES_AVOID = "obstacles avoid";
    public static final String NAVIGATION_GLOBAL_PATH_FAILED = "global_path_failed";
    public static final String NAVIGATION_PATH_SUCCESS = "path_success";
    public static final String NAVIGATION_STARTED = "navigation_started";
    public static final String NAVIGATION_MULTI_ROBOT_WAITING = "navigation_multi_wait";
    public static final String NAVIGATION_MULTI_ROBOT_WAITING_END = "navigation_multi_wait_end";
    public static final String NAVIGATION_GO_STRAIGHT = "navigation_go_straight";
    public static final String NAVIGATION_TURN_LEFT = "navigation_turn_left";
    public static final String NAVIGATION_TURN_RIGHT = "navigation_turn_right";
    public static final String NAVIGATION_NOT_MOVING_LONG_TIME = "navigation_not_moving_long_time";
    public static final String NAVIGATION_SET_PRIORITY_FAILED = "navigation_set_priority_failed";
    public static final String NAVIGATION_MULTI_LORA_CONFIG_FAIL = "navigation_multi_lora_config_fail";
    public static final String NAVIGATION_MULTI_MAP_NOT_MATCH = "navigation_multi_map_not_match";
    public static final String NAVIGATION_MULTI_LORA_DISCONNECT = "navigation_multi_lora_disconnect";
    public static final String NAVIGATION_MULTI_VERSION_NOT_MATCH = "navigation_multi_version_not_match";
    public static final String NAVIGATION_WHEEL_SLIP = "navigation_wheel_slip";
    public static final String NAVIGATION_HUMAN_FOLLOWING = "navigation_human_following";
    public static final String NAVIGATION_HUMAN_LOST = "navigation_human_lost";
    public static final String NAVIGATION_HUMAN_START_SUCCESS = "navigation_human_start_success";
    public static final String NAVIGATION_HUMAN_START_FAILED = "navigation_human_start_failed";
    public static final String NAVIGATION_HUMAN_ID_CHANGE = "navigation_human_id_change";
    public static final String NAVIGATION_HUMAN_FOLLOWING_LONG_TIME_NO_TAG = "navigation_human_following_long_time_no_tag";

    public static final int NAVIGATE_DEFAULT = 0;    // 默认导航
    public static final int NAVIGATE_MULTI_ROBOT = 1; // 多机导航时使用,基于taskPriority,NavigationAction stop 不执行cmd_stop_navi

    public static final int BIND_UNKNOWN = -1;
    public static final int BIND_FAILURE = 0;
    public static final int BIND_SUCCESS = 1;
    public static final int BIND_NONEED = 2;

    public static final String ROBOT_NAME = "robot_name";
    public static final String BIND_STATUS = "bind_status";
    public static final String QRCODE_DATA = "qrcode_data";
    public static final String EXPIRES_IN = "expires_in";
    public static final String BIND_CODE = "bind_code";
    public static final String BIND_SID = "bind_sid";

    public static final String REMOTE_ERRNO = "remote_errno";
    public static final String REMOTE_ERRMSG = "remote_errmsg";
    public static final String REMOTE_ERRID = "remote_errid";

    //------------- Request -------------//
    public static final String REQ_OTA_REMOTE = "req_ota_remote";
    public static final String REQ_EMERGENCY_PRESS = "req_emergency_press";
    public static final String REQ_EMERGENCY_RELEASE = "req_emergency_release";
    public static final String REQ_STANDBY_START = "req_standby_start";
    public static final String REQ_STANDBY_STOP = "req_standby_stop";
    public static final String REQ_STANDBY_FINISH = "req_standby_finish";
    public static final String REQ_BATTERY_LOW = "req_battery_low";
    public static final String REQ_BATTERY_CHARGING = "req_battery_charging";
    public static final String REQ_OPEN_RADAR = "req_open_radar";
    public static final String REQ_OPEN_RADAR_SUCCEED = "req_open_radar_succeed";
    public static final String REQ_BATTERY_FULL = "req_battery_full";
    public static final String REQ_NORMAL = "req_normal";
    public static final String REQ_OTA_AUTH = "req_ota_auth";
    public static final String REQ_OTA_INSPECT = "req_ota_inspect";
    public static final String REQ_OTA_DOWNGRADE_INSPECT = "req_ota_downgrade_inspect";
    public static final String REQ_SET_CHARGE_PILE = "req_set_charge_pile";
    public static final String REQ_OTA_RESULT = "req_ota_result";
    public static final String REQ_OTA_DOWNGRADE_RESULT = "req_ota_downgrade_result";
    public static final String REQ_START_CHARGE = "req_start_charge";
    public static final String REQ_LOAD_MAP = "req_load_map";
    public static final String REQ_REPOSITION = "req_reposition";
    public static final String REQ_MAP_DRIFT = "req_map_drift";
    public static final String REQ_MAP_OUTSIDE = "req_map_outside";
    public static final String REQ_MAP_OUSIDE_RELEASE = "req_map_outside_release";
    public static final String RELOCATE_SWITCH_CLOSE = "relocate_switch_close";
    public static final String RELOCATE_ACTION = "relocate_action";
    public static final String RELOCATE_SWITCH_OPEN = "relocate_switch_open";
    public static final String WECHAT_SET_CHARGING_PILE = "WeChat_set_charging_pile";
    public static final String WECHAT_FIRST_RECHARGING = "WeChat_first_recharging";
    public static final String REQ_STOP_CHARGING = "req_stop_charging";
    public static final String REQ_STOP_CHARGING_BY_APP = "req_stop_charging_by_app";
    public static final String REQ_STOP_CHARGING_BY_CHARGING_SLOW = "req_stop_charging_by_charging_slow";
    public static final String REQ_STOP_CHARGING_CONFIRM = "req_stop_charging_confirm";
    public static final String REQ_LEAVE_PILE_GO_POINT = "req_leave_pile_go_point";
    public static final String REQ_REMOTE_BIND_FAILED = "remote_bind_failed";
    public static final String REQ_REMOTE_BIND_SUCCESSFUL = "remote_bind_successful";
    public static final String REQ_BIND_FAILED = "req_bind_failed";
    public static final String REQ_REMOTE_RELOCATE = "req_remote_relocate";
    public static final String REQ_SYSTEM_RECOVERY = "req_system_recovery";
    public static final String REQ_START_DORMANCY = "req_start_dormancy";
    public static final String REQUEST_REMOTE_PUSH_MAP = "request_remote_push_map";
    public static final String STATUS_PUSH_MAP_PROCESS = "status_push_map_process";
    public static final String REMOTE_PUSH_MAP_NEED_SWITCH = "remote_push_map_need_switch";
    public static final String REMOTE_PUSH_MAP_NO_SWITCH = "remote_push_map_no_switch";
    public static final String REQ_ROBOT_BEING_PUSHED = "req_robot_being_pushed";
    public static final String REQ_ROBOT_BEING_PUSHED_RELEASE = "req_robot_being_pushed_release";
    public static final String REQ_D430_CALIBRATION_START = "req_d430_calibration_start";
    public static final String REQ_MULTI_ROBOT_ERROR = "req_multi_robot_error";

    public static final String REQ_REMOTE_STOP_CHARGING = "remote_stop_charging";
    public static final String REQ_REMOTE_STOP_CHARGING_STATUS = "remote_stop_charging_status";

    public static final String REQ_SYSTEM_STOP_CHARGING = "req_system_stop_charging";
    public static final String REQ_SYSTEM_STOP_CHARGING_STATUS = "req_system_stop_charging_status";
    /**
     * 底盘传感器正常和异常的REQ定义
     */
    public static final String REQ_CHASSIS_SENSOR_NORMAL = "req_chassis_sensor_normal";
    public static final String REQ_CHASSIS_SENSOR_ERROR = "req_chassis_sensor_error";

    /**
     * task任务命令
     */
    public static final String CMD_REMOTE_FLOOR_POSITION_LIST = "remote_floor_position_list";
    public static final String CMD_REMOTE_TASK_GROUP_QUEUE = "remote_task_group_queue";
    public static final String CMD_REMOTE_TASK_CREATE = "remote_task_create";
    public static final String CMD_REMOTE_TASK_TAKE = "remote_task_take";
    public static final String CMD_REMOTE_TASK_MANUAL = "remote_task_manual";
    public static final String CMD_REMOTE_TASK_CANCEL = "remote_task_cancel";
    public static final String CMD_REMOTE_TASK_SUCC = "remote_task_succ";
    public static final String CMD_REMOTE_TASK_POS_QRCODE = "remote_task_pos_qrcode";
    public static final String CMD_REMOTE_CABINET_PUT_GOOD_STATUS = "cabinet_put_goods_status";
    public static final String CMD_REMOTE_CABINET_AUTO_TEST = "cabinet_auto_test";

    public static final String JSON_REMOTE_ALLOW_TASK_TYPE = "allow_task_type";
    public static final String JSON_REMOTE_TASK_GROUP_TYPE = "task_group_type_list";
    public static final String JSON_REMOTE_PARENT_TASK_OUT_ID = "parent_task_outid";
    public static final String JSON_REMOTE_TASK_TYPE = "task_type";
    public static final String JSON_REMOTE_TASK_LIST = "task_list";
    public static final String JSON_REMOTE_TASK_ID = "task_id";
    public static final String JSON_REMOTE_STATION_TYPE = "station_type";
    public static final String JSON_REMOTE_STATION_ID = "station_id";
    public static final String JSON_REMOTE_STATION_ORDER_ID = "station_order_id";
    public static final String JSON_REMOTE_CANCEL_RESULT = "cancel_result";
    public static final String JSON_REMOTE_QRCODE_TYPE = "qrcode_type";
    public static final String JSON_REMOTE_POS_NAME = "pos_name";
    public static final String JSON_REMOTE_FLOOR_ID = "floor_id";
    public static final String JSON_REMOTE_PAD_ZCB_PLATE_BACK_TABLE_TASK_STATUS = "pad_zcb_plate_back_table_task_status";
    public static final String JSON_REMOTE_ROOM_NUMBER = "room_number";
    public static final String JSON_REMOTE_FLOOR_NUMBER = "floor_number";
    public static final String JSON_REMOTE_TEST_ID = "test_id";

    public static final String PUSH_MAP_DOWNLOAD_FAIL = "-1";
    public static final String PUSH_MAP_WAIT_TO_PULL = "0";
    public static final String PUSH_MAP_WAIT_TO_RELOCATE = "1";
    public static final String PUSH_MAP_SWITCH_AND_RELOCATE_SUC = "2";
    public static final String PUSH_MAP_NAVI_MAP_NO_SWITCH_ESTIMATE_OK = "3";
    public static final String PUSH_MAP_SWITCH_FAIL_OR_NO_SWITCH = "4";
    public static final String PUSH_MAP_RELOCATE_FAIL = "5";
    public static final String PUSH_MAP_NAVI_MAP_NO_SWITCH_ESTIMATE_FAIL = "6";
    public static final String PUSH_MAP_NEED_SWITCH_NO_RELOCATE = "7";

    public static final String REQ_REMOTE_GO_POSITION = "remote_go_position";
    public static final String REQ_REMOTE_GO_POSE = "remote_go_pose";
    public static final String REQ_REMOTE_STOP_NAVIGATION = "remote_stop_navigation";
    public static final String REQ_SYSTEM_GO_POSITION = "system_remote_go_position";
    public static final String REQ_SYSTEM_GO_POSE = "system_remote_go_pose";
    public static final String REQ_SYSTEM_STOP_NAVIGATION = "system_remote_stop_navigation";
    public static final String REQ_FUNCTION_KEY_PRESS = "req_function_key_press";
    public static final String REQ_FUNCTION_KEY_RELEASE = "req_function_key_release";
    public static final String REQ_REMOTE_STANDBY_START = "remote_standby_start";
    public static final String REQ_REMOTE_STANDBY_STOP = "remote_standby_stop";

    public static final String REQ_SYSTEM_TIME_WARNING_START = "req_system_time_warning_start";
    public static final String REQ_SYSTEM_TIME_WARNING_STOP = "req_system_time_warning_stop";
    public static final String REQ_ENABLE_TARGET_CUSTOM = "req_enable_target_custom";

    /**
     * 定时关机
     */
    public static final String REQ_SYSTEM_SHUTDOWN_TIMER = "req_system_shutdown_timer";


    public static final String REQ_SET_LIGHT = "req_set_light";
    public static final String REQ_SPEECH_TEXT = "req_speech_text";

    public static final String REQ_HW_RECOVERY = "req_hw_recovery";
    public static final String REQ_HW_MALFUNCTION = "req_hw_malfunction";
    public static final String REQ_HW_E70 = "req_hw_e70";
    public static final String REQ_HW_E70_RECOVERY = "req_hw_e70_recovery";

    //------------- Light ------------//
    public static final String LIGHT_EMERGENCY = "emergency";
    public static final String LIGHT_CHARGING = "charging";
    public static final String LIGHT_BATTERY_LOW = "battery_low";
    public static final String LIGHT_CHARGE_FULL = "charge_full";
    public static final String LIGHT_NORMAL = "normal";
    public static final String LIGHT_EMPTY = "empty";
    public static final String LIGHT_CLOSE = "close";
    public static final String LIGHT_REPOSITION = "reposition";
    public static final String LIGHT_INSPECTION = "inspection";

    //------------- Charge -------------//
    public static final String CHARGE_LOW = "battery_low";
    public static final String CHARGE_SETTING = "setting_charge";
    public static final String CHARGE_SETTING_TIMING = "setting_charge_timing";
    public static final String CHARGE_WECHAT = "WeChat_charge";
    public static final String CHARGE_OTA = "ota_charge";
    public static final String CHARGE_SLOW = "charge_slow";

    //-------------- Open Radar -----------//
    public static final String OPEN_BY_ALARM_CHARGING = "open_radar_by_alarm_charging";
    public static final String OPEN_BY_APP_STOP_CHARGING = "open_radar_by_app_stop_charging";
    public static final String OPEN_BY_STOP_CHARGING = "open_radar_by_stop_charging";
    public static final String OPEN_BY_CHARGING_SLOW_STOP_CHARGING = "open_radar_by_charging_slow_stop_charging";

    //------------- System status ---------//
    public static final String SYSTEM_START_CHARGE = "system_start_charge";
    public static final String SYSTEM_OTA = "system_ota";
    public static final String SYSTEM_RECOVERY = "system_recovery";
    public static final String SYSTEM_SET_CHARGE_PILE = "system_set_charge_pile";
    public static final String SYSTEM_INSPECTION = "system_inspection";
    public static final String SYSTEM_FUNCTION_KEY = "system_function_key";
    public static final String SYSTEM_STANDBY = "system_standby";
    public static final String SYSTEM_DORMANCY = "system_dormancy";
    public static final String SYSTEM_AUTO_OTA = "system_auto_ota";
    public static final String SYSTEM_EMERGENCY = "system_emergency";
    public static final String SYSTEM_BATTERY_LOW = "system_battery_low";
    public static final String SYSTEM_BATTERY = "system_battery";
    public static final String SYSTEM_REPOSITION = "system_reposition";
    public static final String SYSTEM_REMOTE_REPOSITION = "system_remote_reposition";
    public static final String SYSTEM_REMOTE_BIND = "system_remote_bind";
    public static final String SYSTEM_REMOTE_CONTROL = "system_remote_control";
    public static final String SYSTEM_SHUTDOWN = "system_shutdown";
    public static final String SYSTEM_RADAR = "system_radar";
    public static final String SYSTEM_SHUTDOWN_TIMER = "system_shutdown_timer";
    public static final String SYSTEM_TIME_WARNING = "system_time_warning";
    public static final String SYSTEM_BLE_MANUAL_EXIT = "system_ble_manual_exit";
    public static final String SYSTEM_CALIBRATION = "system_calibration";
    public static final String SYSTEM_WHEEL_OVER_DANGER_EXIT = "system_wheel_over_danger_exit";
    public static final String SYSTEM_ENABLE_TARGET_CUSTOM = "system_enable_target_custom";
    public static final String SYSTEM_NAVI_SENSOR_RECOVERY = "system_navi_sensor_recovery";
    public static final String SYSTEM_NAVI_LOAD_MAP = "system_navi_load_map";
    public static final String SYSTEM_PUSH_WARNING = "system_push_warning";
    public static final String SYSTEM_STOP_CHARGE_CONFIRM = "system_stop_charge_confirm";
    public static final String SYSTEM_LEAVE_PILE_TO_POINT_END = "system_leave_pile_to_point_end";
    public static final String SYSTEM_OUTSIDE_MAP = "system_outside_map";

    public static final String START = "start";
    public static final String STOP = "stop";
    public static final String FINISHED = "finished";
    public static final String SUCCESS = "success";
    public static final String INSPECTION_FINISHED_FIRST_CONFIG = "inspection_finished_first_config";
    public static final String INSPECTION_FINISHED_FIRST = "inspection_finished_first";
    public static final String INSPECTION_FINISHED_NORMAL = "inspection_finished_normal";
    public static final String START_CHARGE_OK = "start_charge_ok";
    public static final String START_CHARGE_FAIL = "start_charge_fail";
    public static final String RESULT_NEED_RELOCATION = "result_need_relocation";
    public static final String RESULT_NOT_NEED_RELOCATION = "result_not_need_relocation";

    //-------------- OTA startup mode ----------------//
    public static final String OTA_START_BOOT = "ota_start_boot";
    public static final String OTA_START_NIGHTTIME = "ota_start_nighttime";
    public static final String OTA_START_SETTING = "ota_start_setting";
    public static final String OTA_START_REMOTE = "ota_start_remote";

    //-------------- OTA interrupt reason ----------------//
    public static final String OTA_INTERRUPT_REASON_BATTERY_LESS = "battery_less";

    //-------------- Broadcast Action ----------//
    public static final String ACTION_REPOSITION = "action_reposition";
    public static final String REPOSITION_VISION = "repositionVision";
    public static final String REPOSITION_BY_BOOT = "reposition_by_boot";
    public static final String ACTION_STOP_CHARGING_BY_APP = "action_stop_charging_by_app";
    public static final String ACTION_STOP_CHARGE_BY_UI = "action_stop_charge_by_ui";
    public static final String ACTION_APP_LANGUAGE_CHANGE = "action_app_language_change";
    public static final String ACTION_E70_RECOVERY_REPOSITION = "action_e70_recovery_reposition";

    public static final String LANGUAGE = "language";

    //-------------- remote relocate ----------//
    public static final String REQ_SWITCH_OPEN_ID = "req_switch_open_id";

    public static final String ACTION_SWITCH_MAP = "action_switch_map";
    public static final String ESTIMATE_OK_REPORT = "estimate_ok_report";
    public static final String MOVE_CONFIRM = "move_confirm";

    public static final String OLD_NAME = "oldName";
    public static final String NEW_NAME = "newName";
    public static final String ADDPLACE = "ADD";
    public static final String DELPLACE = "DEL";
    public static final String RENAMEPLACE = "RENAME";
    public static final String REPLACEPLACE = "REPLACE";
    public static final String FORBIDLINE = "FORBIDLINE";
    public static final String TYPE_FORBIDLINE = "forbidLine";
    public static final String RENAMEBEAN = "renameBean";
    public static final String NEWPLACEBEAN = "newPlace";
    public static final String REPLACEBEAN = "replaceBean";
    public static final String REPLACEPLACENAME = "placeName";
    public static final String NEWPLACENAME = "name";
    public static final String DELNAME = "delName";
    public static final String POSE = "pose";
    public static final String NEWPLACE_IGNORE_DISTANCE = "ignoreDistance";
    public static final String NEWPLACE_SAFE_DISTANCE = "safeDistance";
    public static final String NO_DIRECTIONAL_PARKING = "noDirectionalParking";
    public static final String NEWPLACE_TYPE_ID = "typeId";
    public static final String NEWPLACE_PRIORITY = "priority";
    public static final String LOGIN_SUCCESS = "login_success";
    // remote face picture invalid
    public static final int REMOTE_CODE_FACE_INVALID = 401;
    public static final String REMOTE_FAIL_FACE_INVALID = "remote_fail_face_invalid";
    // remote reboot action
    public static final String ACTION_REMOTE_REBOOT = "action_remote_reboot";

    //-------------- navigation time out action type ----------//
    public static final String TAG_NAVI_LOG_REPORT = "tag_navi_log_report ";
    public static final String TYPE_ACTION_AUTO_NAVI_CHARGE = "type_action_auto_navi_charge";
    public static final String TYPE_ACTION_NAVI_INSPECTION_FAILURE = "type_action_navi_inspection_failure";
    public static final String TYPE_ACTION_NAVI_OPEN_RADAR_ERROR = "type_action_navi_open_radar_error";
    public static final String TYPE_ACTION_CHARGE_PILE_ESTIMATE_FAILUE = "type_action_charge_pile_estimate_failue";


    // log cache status
    public static final int STATUS_LOG_DEFAULT_TASK = 0;
    public static final int STATUS_LOG_PACKING_TASK = 1;
    public static final int STATUS_LOG_PACK_SUCCESS_TASK = 2;
    public static final int STATUS_LOG_PACK_FAILED_TASK = 3;

    // charge fail status
    public static final String CHARGE_FAIL_STATUS = "charge_fail_status";
    public static final String CHARGE_FAIL_REASON = "charge_fail_reason";
    public static final int CHARGE_FAIL_WHEN_NOT_ESTIMATE = 1001;
    public static final int CHARGE_FAIL_WHEN_NAVIGATION = 1002;
    public static final int CHARGE_FAIL_WHEN_LARGE_MAP_NAVI_TIMEOUT = 1003;
    public static final int CHARGE_FAIL_WHEN_PARSE_IN_LOCATION = 1004;
    public static final int CHARGE_FAIL_WHEN_NOT_MOVE_FOR_20S = 1005;
    public static final int CHARGE_FAIL_WHEN_PSB_CHARGE = 1006;
    public static final int CHARGE_FAIL_WHEN_PSB_NO_SIGNAL = 1007;
    public static final int CHARGE_FAIL_WHEN_VISION_CHARGE_START = 1008;//视觉回充失败
    public static final int CHARGE_FAIL_WHEN_VISION_CHARGE_STOP = 1009;
    public static final int CHARGE_FAIL_VISION_CHARGE_TIMEOUT = 1010;
    public static final int CHARGE_FAIL_NOT_EXIST_CHARGING_PILE_POSE = 1011;
    public static final int CHARGE_FAIL_NOT_EXIST_CHARGING_WIRE_POSE = 1012;

    // resume special theta
    public static final String SPECIAL_THETA_ALREADY_SUCCESS_NO_NEED_TO_RESUME = "special_theta_already_success_no_need_to_resume";

    // Enable all debug log
    private static final boolean DEBUG_ALL = false;

    // Enable dispatcher debug log
    public static final boolean DEBUG_ALL_DISPATHER = DEBUG_ALL || false;

    public static final String DISPATCHER_TAG = "DISPATCHER_TAG";

    public static final String SET_CHARGING_ENVIRONMENT = "set_charging_environment";

    public static final String OLD_MAP_NAME = "old_map_name";
    public static final String NEW_MAP_NAME = "new_map_name";

    public static final int CHECK_FAIL = 0;
    public static final int CHECK_SUC = 1;
    public static final int CHECK_SOCKET_SUC = 2;
    public static final int CHECK_PASS = -2;
    public static final int UNCHECK = -1;
    public static final int CHECK_CAN_FAIL = 3;
    public static final int CHECK_CHARGE_PANEL_ERROR = 255;

    public static final int STATUS_TURN_HEAD_SUPPORT_ROTATE = 1;
    public static final int STATUS_TURN_HEAD_CURRENT_POSITION = 2;
    public static final int STATUS_TURN_HEAD_PROCESS_UPDATE = 3;
    public static final int STATUS_TURN_HEAD_START = 4;
    public static final int STATUS_TURN_HEAD_MAX_UP_ANGLE = 5;
    public static final int STATUS_TURN_HEAD_MAX_DOWN_ANGLE = 6;

    public static final int RESULT_TURN_HEAD_SUCCESS = 1;
    public static final int RESULT_TURN_HEAD_TIMEOUT = 2;
    public static final int RESULT_TURN_HEAD_FAILED = 3;
    public static final int RESULT_TURN_HEAD_INTERRUPT = 4;
    public static final int RESULT_TURN_HEAD_UN_SUPPORT = 5;

    /**
     * 特殊区域 默认区域
     */
    public static final int SCENE_NORMAL = 0;
    /**
     * 特殊区域 缓行区域
     */
    public static final int SCENE_LARGE_PEOPLE = 1;


    public static final String RESULT_ESTIMATE_FAIL_MATCH_ERR = "matchError";
    public static final String RESULT_ESTIMATE_FAIL_CHECK_ERR = "checkError";
    public static final String RESULT_ESTIMATE_FAIL_POSE_NULL = "poseNull";
    public static final String RESULT_ESTIMATE_FAIL_CREATING_MAP = "creatingMap";

    public static final String STATUS_NAVI_SERVICE_OK = "status_navi_service_ok";

    public static final int STATUS_RN_INSTALL_START = 102;
    public static final int STATUS_RN_INSTALL_SUCCESS = 202;
    public static final int STATUS_RN_INSTALL_FAILED = 502;

    public static final String JSON_RN_INSTALL_STATUS = "json_rn_install_status";
    public static final String JSON_RN_INSTALL_MESSAGE = "json_rn_install_message";
    public static final String JSON_RN_INSTALL_ID = "json_rn_install_id";

    //--------------  LOGIN ERR --------------//
    public static final int STATUS_NOT_EXIST = 101013;
    public static final int STATUS_HWID_ERR = 101022;

    public static final int STATUS_LOGIN_ERR = -100;
    public static final int STATUS_PICTURE_ERR = -101;
    public static final int STATUS_RESPONSE_PARSE_ERR = -102;
    public static final int STATUS_DEPRECATED_API = -103;
    public static final int STATUS_ILLEGAL_CALL = -104;
    public static final int STATUS_ILLEGAL_STATE = -105;

    //--------------- Robot Standby ---------------//
    public static final int STATUS_STANDBY_SUCCESS = 1;

    public static final int RESULT_STANDBY_END_SUCCESS = 1;

    public static final String NAVI_OTA_UPDATE_DONE = "navi_ota_update_done";

    //--------------- Robot Config ---------------//
    public static final String CONFIG_INSPECTION = "inspection";
    public static final String CONFIG_VISION = "vision";

    public static final String FLAG = "flag";
    public static final String BINDER_FLAG_EXTERNAL = "External";

    //------------------bind---------------//
    public static final int BIND_ROBOT = 1;
    public static final int BIND_SYSTEM = 2;
    public static final int BIND_PERSON = 3;
    public static final int BIND_SETTING = 4;
    public static final int BIND_HW = 6;
    public static final int BIND_ACCOUNT = 7;
    public static final int BIND_OTA = 9;
    public static final int BIND_PERMISSION = 10;
    public static final int BIND_SURFACE_SHARE = 11;
    public static final int BIND_SHARE_MEMORY = 12;
    public static final int BIND_ROBOT_BACKGROUND = 13;

    public static final int SERVICE_NOT_SUPPORT = -1;

    //Default app
    public static final String ROBOT_SETTING_PACKAGE_NAME = "boot_app_package_name";

    public static final String ROBOT_STATUS_ALL = "robot_status_all";

    //------------------special person id for body follow---------------//
    //以距离中心最近的人体初始化人体模型，过程中底层算法对人体做区分
    public static final int BODY_FOLLOW_PERSON_ID_MIDDLE = 10000;
    //以面积最大的人体初始化人体模型，过程中底层算法对人体做区分
    public static final int BODY_FOLLOW_PERSON_ID_LARGE = 10001;

    public static final int RESULT_SUCCEED = 1;
    public static final int RESULT_FAILED = -1;
    public static final int RESULT_UNSUPPORT = -2;
    /**
     * ----------------------robot settings--------------------------------------
     */
    public static final String BOOT_APP_PACKAGE_NAME = "boot_app_package_name";
    public static final String BOSS_AVATAR_ON_MOBILE = "boss_avatar_on_mobile";
    public static final String BUILD_NEW_MAP_ON_MOBILE = "build_new_map_on_mobile";
    public static final String CHARGING_PILE_CONFIGURED = "charging_pile_configured";
    public static final String ROBOT_AUTO_BACK_RECEPTION = "robot_auto_back_reception";
    public static final String ROBOT_CHAT_REPLY = "robot_chat_reply";
    public static final String ROBOT_DROPDOWN_BAR_PASSWORD = "robot_dropdown_bar_password";
    public static final String ROBOT_SETTING_QUICK_SETTING_BAR_SUPPORT_METHOD = "robot_settings_quick_setting_bar_support_method";
    public static final String ROBOT_SETTING_BAR_CAMERA = "robot_setting_bar_camera";
    public static final String ROBOT_SETTING_BAR_MICRO = "robot_setting_bar_micro";
    public static final String ROBOT_SETTING_OPEN_OR_NOT_NIGHT_MODE_SWITCH = "robot_setting_open_or_not_night_mode_switch";
    public static final String ROBOT_SETTING_BRIGHTNESS_VALUE = "robot_setting_brightness_value";
    public static final String ROBOT_SETTING_NIGHT_SOUND_VALUE = "robot_setting_night_sound_value";
    public static final String ROBOT_SETTING_OPEN_OR_NOT_MESSAGE_MUTE_SWITCH = "robot_setting_open_or_not_message_mute_switch";
    public static final String ROBOT_SETTING_OPEN_OR_NOT_CALLS_MUTE_SWITCH = "robot_setting_open_or_not_calls_mute_switch";
    public static final String ROBOT_SETTING_NIGHT_START_TIME = "robot_setting_night_start_time";
    public static final String ROBOT_SETTING_NIGHT_END_TIME = "robot_setting_night_end_time";
    public static final String SETTINGS_FLIQLO_OPEN_CONDITION = "settings_fliqlo_open_condition";
    public static final String SETTINGS_FLIQLO_APPEARANCE_TYPE = "settings_fliqlo_appearance_type";
    public static final String SETTINGS_FLIQLO_APPEARANCE_URI = "settings_fliqlo_appearance_uri";
    public static final String SETTINGS_FLIQLO_FACE_WAKE_UP = "settings_fliqlo_face_wake_up";
    public static final String SETTINGS_FLIQLO_REST_SCREEN_OPPORTUNITY = "settings_fliqlo_rest_screen_opportunity";
    public static final int ROBOT_SETTING_NOT_CHECKED = 0;
    public static final int ROBOT_SETTING_CHECKED = 1;
    public static final String ROBOT_SETTING_FIRST_CONFIG_WIFI_ACTIVITY = "com.ainirobot.settings.wifi.view.WifiFirstConfigActivity";
    public static final String ROBOT_SETTING_NIGHT_MODE_SWITCH = "robot_setting_open_or_not_night_mode_switch";
    public static final String ROBOT_SETTING_MESSAGE_MUTE_SWITCH = "robot_setting_open_or_not_message_mute_switch";
    public static final String ROBOT_SETTING_CALLS_MUTE_SWITCH = "robot_setting_open_or_not_calls_mute_switch";
    public static final String ROBOT_SETTING_NIGHT_MODE_START_TIME = "robot_setting_night_start_time";
    public static final String ROBOT_SETTING_NIGHT_MODE_END_TIME = "robot_setting_night_end_time";
    public static final String ROBOT_SETTING_SENSOR_ABNORMAL_PROMPT = "robot_setting_sensor_abnormal_prompt";
    public static final String ROBOT_SETTING_SYSTEM_APP_CONTROL_PROMPT = "robot_setting_system_app_control_prompt";
    public static final String ROBOT_SETTING_AUTO_CHARGE_TIMER = "robot_setting_auto_charge_timer";
    public static final String ROBOT_SETTING_LOW_BATTERY_NAVI_LOCATION = "robot_setting_low_battery_navi_location";
    public static final String ROBOT_SETTING_NOT_ALLOW_SECONDARY_DEVELOPMENT = "robot_setting_not_allow_secondary_development";
    public static final String ROBOT_PLATFORM_TYPE_KEY = "robot_platform_type";
    public static final String ROBOT_PLATFORM_IS_OVERSEA_KEY = "robot_platform_is_oversea";
    public static final String ROBOT_PLATFORM_IS_DELIVERY_PRODUCT = "robot_platform_is_delivery_product";
    public static final String ROBOT_PLATFORM_MINI = "robot_platform_mini";
    public static final String ROBOT_PLATFORM_DELIVERY = "robot_platform_delivery";
    public static final String ROBOT_PLATFORM_MEISSA_PLUS = "robot_platform_meissa_plus";
    public static final String ROBOT_PLATFORM_MEISSA_2 = "robot_platform_meissa_2";
    public static final String ROBOT_PLATFORM_CARRY = "robot_platform_carry";
    public static final String ROBOT_PLATFORM_SAIPH_PRO = "robot_platform_saiph_pro";
    public static final String ROBOT_PLATFORM_SLIM = "robot_platform_slim";
    public static final String ROBOT_MAP_COMPATIBLE_VERSION = "mapCompatibleVersion";
    public static final String ROBOT_CHASSIS_RADIUS = "robot_chassis_radius";

    public static final String SETTING_HOME_TAB = "home_tab";
    public static final String SETTING_HOME_DRAWER_MEMBER = "drawer_member";
    public static final String SETTING_HOME_DRAWER_WIFI = "drawer_wifi";
    public static final String SETTING_HOME_DRAWER_NIGHT_MODE = "drawer_night_mode";
    public static final String SETTING_HOME_DRAWER_FLIPLO = "drawer_fliplo";
    public static final String SETTING_HOME_DRAWER_SOUND = "drawer_sound";
    public static final String SETTING_HOME_DRAWER_MOVEMENT = "drawer_movement";
    public static final String SETTING_HOME_DRAWER_CHARGE = "drawer_charge";
    public static final String SETTING_HOME_DRAWER_EQUIPMENT = "drawer_equipment";
    public static final String SETTING_HOME_DRAWER_DEVELOPER_MODE = "drawer_developer_mode";


    public static final String ROBOT_FOCUS_FOLLOW = "robot_focus_follow";
    public static final String ROBOT_REGULAR_CHARGE_TIME_LIST = "robot_regular_charge_time_list";
    public static final String ROBOT_OBSTACLES_AVOID = "robot_obstacles_avoid";
    public static final String ROBOT_PREVENT_COLLISION = "robot_prevent_collision";
    public static final String ROBOT_SMALL_ACTION = "robot_samll_action";
    public static final String ROBOT_SETTING_ALLOW_AUTO_SITU_SERVICE = "robot_setting_allow_auto_situ_service";
    public static final String ROBOT_SETTING_AUTO_CHARGE = "robot_setting_auto_charge";
    public static final String ROBOT_SETTING_ENABLE_NAVIGATION_INCHARGING = "robot_setting_enable_navigation_incharging";
    public static final String ROBOT_SETTING_MAP_OUTSIDE_WARNING = "robot_setting_map_outside_warning";
    public static final String ROBOT_SETTING_BEING_PUSHED_WARNING = "robot_setting_being_pushed_warning";
    public static final String ROBOT_SETTING_OTA_LOW_BATTERY_AUTO_CHARGE = "robot_setting_ota_low_battery_auto_charge";
    public static final String ROBOT_SETTING_CRUISE_ANGULAR_SPEED = "robot_setting_cruise_angular_speed";
    public static final String ROBOT_SETTING_CRUISE_LINEAR_SPEED = "robot_setting_cruise_linear_speed";
    public static final String ROBOT_SETTING_DISABLE_VOICE_CONTROL_VOLUME = "robot_setting_disable_voice_control_volume";
    public static final String ROBOT_SETTING_GREET_ANGULAR_SPEED = "robot_setting_greet_angular_speed";
    public static final String ROBOT_SETTING_GREET_LINEAR_SPEED = "robot_setting_greet_linear_speed";
    public static final String ROBOT_SETTING_GUIDE_ANGULAR_SPEED = "robot_setting_guide_angular_speed";
    public static final String ROBOT_SETTING_GUIDE_LINEAR_SPEED = "robot_setting_guide_linear_speed";
    public static final String ROBOT_SETTING_HIDE_MANUFACTURE_INFORMATION = "robot_setting_hide_manufacture_information";
    public static final String ROBOT_SETTING_LEAD_ANGULAR_SPEED = "robot_setting_lead_angular_speed";
    public static final String ROBOT_SETTING_LEAD_LINEAR_SPEED = "robot_setting_lead_linear_speed";
    public static final String ROBOT_SETTING_NAV_ANGULAR_SPEED = "robot_setting_nav_angular_speed";
    public static final String ROBOT_SETTING_NAV_LINEAR_SPEED = "robot_setting_nav_linear_speed";
    public static final String ROBOT_SETTING_OTA_UPDATE = "robot_setting_ota_update";
    public static final String ROBOT_SETTING_SHIPPING_MODE = "robot_setting_shipping_mode";
    public static final String ROBOT_SETTING_SITU_SERVICE_RUNNING_TIME = "robot_setting_situ_service_running_time";
    public static final String ROBOT_SETTING_SITU_SERVICE_STATUS = "robot_setting_situ_service_status";
    public static final String ROBOT_SETTING_SITU_SERVICE_TIME = "robot_setting_situ_service_time";
    public static final String ROBOT_SETTING_CLOSE_VOLUME_CHANGE_TOAST = "robot_setting_close_volume_change_toast";

    public static final String ROBOT_SETTING_VOICE_MODE = "robot_setting_voice_mode";
    public static final String ROBOT_SETTING_WAKEUP_FREE_HEAD_ANGLE = "robot_setting_wakeup_free_head_angle";

    /**
     * SpeechAsrService type for sound center angel and range angel, must be set together, angel
     * center is [0-360], default value is 0, angel range is (0-120], default value is 120.
     */
    public static final String ROBOT_SETTING_SOUND_ANGEL_CENTER = "robot_setting_sound_angel_center";
    public static final String ROBOT_SETTING_SOUND_ANGEL_RANGE = "robot_setting_sound_angel_range";
    /**
     * SpeechAsrService set speech speed, value range is 0-9, default value is 5.
     */
    public static final String ROBOT_SETTING_SPEECH_SPEED = "robot_setting_speech_speed";

    /**
     * SpeechAsrService type for speech volume, value is from 0 to 30, default value is 30.
     */
    public static final String ROBOT_SETTING_SPEECH_VOLUME = "robot_setting_speech_volume";

    /**
     * SpeechAsrService type for spoke man, value is 0 or 20, default value is 0.
     */
    public static final String ROBOT_SETTING_SPOKE_MAN = "robot_setting_spoke_man";
    public static final String ROBOT_SETTING_SPEAKER_ROLE = "robot_setting_speaker_role";
    public static final String ROBOT_SETTING_VAD_END = "robot_setting_vad_end";
    public static final String ROBOT_SETTING_DEMO_MODE = "robot_setting_demo_mode";
    public static final String ROBOT_SETTING_WAITING_TIME = "robot_setting_waiting_time";
    public static final String ROBOT_SETTINGS_CHARGING_ENVIRONMENT = "robot_settings_charging_environment";
    public static final String ROBOT_SETTINGS_OBSTACLES_AVOID_DISTANCE = "robot_settings_obstacles_avoid_distance";
    public static final String ROBOT_SHOW_AD = "robot_show_ad";
    public static final String ROBOT_SHOW_AD_AND_ROTATE = "robot_show_ad_and_rotate";
    public static final String ROBOT_SHUT_DOWN_PASSWORD = "robot_shut_down_password";
    public static final String ROBOT_USABLE_WHEN_CHARGING = "robot_usable_when_charging";
    public static final String SETTINGS_GLOBAL_SWITCH_FOLLOW_STYLE = "settings_global_switch_follow_style";
    public static final String SLEEP_LIST = "sleep_list";
    public static final String SWITCH_ALLOW_CHAT_WHEN_INTERPRET = "switch_allow_chat_when_interpret";
    public static final String ROBOT_SETTINGS_HCUSB_ENABLE = "robot_settings_hcusb_enable";
    public static final String SWITCH_START_APP_PASSWORD = "switch_start_app_password";
    public static final String VERSION_UPGRADE_ON_MOBILE = "version_upgrade_on_mobile";
    public static final String ROBOT_SETTING_BI_LEVEL_NORMAL = "robot_setting_bi_level_normal";
    public static final String ROBOT_SETTING_BI_LEVEL_CALL_CHAIN = "robot_setting_bi_level_call_chain";
    public static final String ROBOT_SETTING_SYSTEM_ENV = "robot_setting_system_environment";
    public static final String ROBOT_SETTING_RGBD_INSTALLED = "robot_setting_rgbd_installed";
    public static final String DATA_DUMP_RIGHTS_VISION_RECORD = "data_dump_rights_vision_record";

    public static final String ROBOT_SETTINGS_SYSTEM_APP_CONTROL = "robot_settings_system_app_control";
    public static final String ROBOT_SETTINGS_SYSTEM_OS_TYPE = "robot_settings_system_os_type";
    public static final int ROBOT_SYSTEM_CONTROL = 0;
    public static final int ROBOT_APP_CONTROL = 1;

    public static final String ROBOT_SETTING_SENSOR_ABNORMAL = "robot_setting_sensor_abnormal";
    public static final String ROBOT_TIME_ZONE_CODE = "robot_time_zone_code";
    public static final String ROBOT_LANGUAGE = "robot_language";
    //OTA SETTINGS
    public static final String ROBOT_SETTING_OTA_NEW_VERSION_CODE = "robot_setting_ota_new_version_code";
    public static final String ROBOT_SETTING_OTA_NEW_VERSION_DESC = "robot_setting_ota_new_version_desc";
    public static final String ROBOT_SETTING_OTA_UPDATE_STATUS = "robot_setting_ota_update_status";
    public static final String ROBOT_SETTING_OTA_UPDATE_PROGRESS = "robot_setting_ota_update_progress";

    public static final String ROBOT_SETTING_POWER_KEY_LONG_PRESS_DIRECT_SHUT_DOWN = "robot_settings_power_key_long_press_direct_shut_down";
    public static final String ROBOT_SETTING_NAVIGATION_NONSUPPORT = "robot_settings_navigation_nonsupport";
    public static final String ROBOT_SETTING_DEFAULT_BODY_SPEED = "robot_settings_default_body_speed";

    public static final String ROBOT_SETTING_CHARGE_POLICY_TYPE = "robot_settings_charge_policy";

    public static final String ROBOT_SETTING_FOCUS_FOLLOW_TRACK_TIMEOUT = "robot_settings_focus_follow_track_timeout";

    public static final String ROBOT_SETTING_SCREEN_LIGHT_BEFOR_STANDBY = "robot_setting_screen_light_befor_standby";

    public static final String ROBOT_SETTING_SYSTEM_ADB_ALWAYS_OPEN = "robot_setting_system_adb_always_open";

    public static final String PROP_SETTING_SYSTEM_WIFI_ADB_ALWAYS_OPEN = "robot_setting_system_wifi_adb_always_open";

    public static final String ROBOT_SETTING_ENABLE_ASR_SWITCH = "robot_enable_asr_switch";

    public static final String ROBOT_SETTING_ENABLE_RECOGNIZE_CONTINUE_SWITCH = "robot_enable_recognize_continue_switch";

    public static final String ROBOT_SETTING_PORTAL_OPK = "robot_portal_opk";

    public static final String ROBOT_SETTING_ELEVATOR_CONTROL_ENABLED = "robot_setting_elevator_control_enabled";

    public static final String ROBOT_SETTING_ELEVATOR_RANGE = "robot_setting_elevator_range";

    public static final String ROBOT_SETTING_STANDBY_OPK_MODE_SWITCH = "robot_setting_standby_opk_mode";

    public static final String ROBOT_SETTING_EMERGENCY_BRAKE_ON_SECURITY_CODE = "robot_emergency_brake_on_security_code";

    public static final String ROBOT_SETTING_IS_SECONDARY_DEVELOPMENT_PROHIBITED = "robot_setting_is_secondary_development_prohibited";

    /**
     * 充电提醒开关控制的key
     */
    public static final String ROBOT_SETTING_CHARGING_REMINDER_SWITCH = "robot_settings_charging_reminder_switch";

    /**
     * 禁用守护进程重启默认APP开关，默认为关（不禁用）
     */
    public static final String ROBOT_SETTING_DAEMON_SERVICE_DISABLE_RESTART_APP = "robot_settings_daemon_service_disable_restart_app";

    /**
     * 禁用头部规正（使用TOPMONO时），默认为关（不禁用）
     */

    public static final String ROBOT_SETTING_DISABLE_HEAD_MOVING = "robot_settings_disable_head_moving";

    /**
     * 托盘摄像头 RobotSetting 开关控制的key
     */
    public static final String ROBOT_SETTING_UVC_CAMERA_SWITCH = "robot_setting_uvc_camera_switch";
    /**
     * 托盘指示灯 RobotSetting 开关控制的key
     */
    public static final String ROBOT_SETTING_PRO_TRAY_LED_SWITCH = "robot_setting_pro_tray_led_switch";
    /**
     * 跟踪器网关LRS   保存其IP的key
     */
    public static final String ROBOT_SETTING_LRS_IP = "robot_setting_lrs_ip";


    /**
     * MapTool内部禁用遥控建图相关界面(引导页面和建图中的logo界面) 1表示隐藏，其他值表示显示
     */
    public static final String ROBOT_MAP_TOOL_DISABLE_REMOTE_MAPPING = "robot_map_tool_disable_remote_mapping";

    /**
     * MapTool内隐藏巡逻界面显示效果 1表示隐藏，其他值表示显示
     */
    public static final String ROBOT_MAP_TOOL_HIDE_CRUISE_SETTING = "robot_map_tool_hide_cruise_setting";

    /**
     * MapTool开始建图显示模式
     * 1表示招财豹，只显示普通模式，且隐藏？图标
     * 2表示豹小秘，显示普通模式和高级模式
     */
    public static final String ROBOT_MAP_TOOL_CREAT_MAP_MODE = "robot_map_tool_creat_map_mode";

    /**
     * 是否支持线充方式{@link #CHARGING_TYPE_WIRE}，默认不支持线充配置项只支持充电桩充电
     * 线充：建图工具提供设置选项，重定位依赖“定位点”，自动回充导航到“定位点”提示手动线充
     */
    public static final String ROBOT_SETTINGS_IS_SUPPORT_WIRED_CHARGING = "robot_settings_is_support_wired_charging";
    /**
     * 当前充电方式，默认“充电桩充电”{@link #CHARGING_TYPE_PILE}
     */
    public static final String ROBOT_SETTINGS_CHARGING_TYPE = "robot_settings_charging_type";
    /**
     * 结束充电是否离桩
     */
    public static final String ROBOT_SETTINGS_FINISH_CHARGE_LEAVE_PILE = "robot_settings_finish_charge_leave_pile";

    /**
     * 结束充电是否离桩
     */
    public static final String ROBOT_SETTINGS_ENABLLE_LOCK_WHEEL = "robot_settings_enable_lock_Wheel";

    public static final String ROBOT_EXPIRES_STATUS = "robot_expires_status";
    public static final String ROBOT_EXPIRES_TIME = "robot_expires_time";
    public static final String ROBOT_EXPIRES_LEFT_TIME = "robot_expires_left_time";
    /**
     * 云服务节点名称,本地存储
     */
    public static final String ROBOT_SETTING_CLOUD_SERVER_CODE = "robot_setting_cloud_server_code";
    public static final String SERVER_EU = "EU";
    public static final String SERVER_US = "US";
    public static final String SERVER_JP = "JP";
    public static final String SERVER_OTHER = "OTHER";

    /**
     * 消毒机器人中的消毒计划开关的key
     */
    public static final String ROBOT_SETTING_DISINFECTION_PLAN = "robot_setting_disinfection_plan";

    /**
     * 闸机线配置
     */
    public static final String ROBOT_GATE_EDGE_PIXEL_NODE = "robot_gate_edge_pixel_node";

    /**
     * 底盘的接入方式
     * client_tk1  tk1老架构
     * client_waiter 豹跑堂机器人，底盘新架构
     */
    public static final String ROBOT_SETTINGS_CHASSIS_CLIENT_TYPE = "robot_settings_chassis_client_type";

    public static final String SURFACE_SHARE_PACKAGE_NAME = "surface_share_package_name";
    public static final String SURFACE_SHARE_SERVICE_NAME =
            "surface_share_service_name";
    public static final String SURFACE_SHARE_ACTION_NAME = "surface_share_action_name";

    /**
     * 扫码器的接入方式
     * type_android  接入KTV 845的android上
     * type_tx1     ktv821接入在TX1上
     */
    public static final String ROBOT_SETTINGS_SCANNER_ACCESS_MODE = "robot_settings_scanner_access_mode";

    /**
     * 是否禁用充电桩重定位
     */
    public static final String ROBOT_SETTINGS_DISABLE_CHARGING_RELOCATION = "robot_settings_disable_charging_relocation";

    /**
     * Home内重定位执行流程需要执行的判断，用于确定执行哪套重定位逻辑
     * type_common 默认通用重定位流程
     * type_waiter 豹跑堂采用二维码重定位
     */
    public static final String ROBOT_SETTINGS_RELOCATION_TYPE = "robot_settings_relocation_type";
    /**
     * 是否打开防跌落开关
     */
    public static final String ROBOT_CHECK_CLIFF = "robot_check_cliff";

    /**
     * Mini项目 MapTool 高级设置－导航设置中,更新导航定位丢失的盲走检测最大距离范围 param
     * 其他项目如果不设置,底盘默认 param是 10
     */
    public static final String ROBOT_SETTINGS_LOST_MAX_DISTANCE_RANGE = "robot_settings_lost_max_distance_range";

    //服务器分区设置
    public static final String ROBOT_SETTINGS_CLOUD_SERVER_ZONE = "robot_settings_cloud_server_zone";

    //hardware service 蓝牙信号控制
    public static final String ROBOT_BLE_DEBUG = "robot_ble_debug";// Settings 配置的debug 模式开关
    public static final String ROBOT_BLE_DEBUG_VALUE = "robot_ble_debug_value";
    public static final String ROBOT_BLE_SIGNAL_SWITCH = "robot_ble_signal_switch";// Settings 配置的功能是否启用开关
    public static final String ROBOT_BLE_SIGNAL_SENSITIVITY = "robot_ble_signal_sensitivity";// Settings 配置的敏感度
    public static final int HARDWARE_BLE_NEAR = 0; //相距较近
    public static final int HARDWARE_BLE_FAR = 1;  //相距较远
    public static final String JSON_HARDWARE_BLE_DISTANCE = "json_hardware_ble_distance";
    public static final String STATUS_BLE_SIGNAL = "status_ble_signal";
    public static final String REQ_BLE_SIGNAL_NEAR = "req_ble_signal_near";
    public static final String REQ_BLE_SIGNAL_FAR = "req_ble_signal_far";

    // UWB定位 参数定义
    public static final String ROBOT_UWB_SWITCH = "robot_uwb_switch";
    public static final int ROBOT_UWB_SWITCH_ON = 1;
    public static final String ROBOT_UWB_CHECKING = "robot_uwb_checking";
    public static final int ROBOT_UWB_CHECKING_START = 1;
    public static final int ROBOT_UWB_CHECKING_STOP = 0;
    public static final String ROBOT_UWB_STATION_1 = "robot_uwb_station_1";
    public static final String ROBOT_UWB_STATION_2 = "robot_uwb_station_2";
    public static final String ROBOT_UWB_STATIONS_DISTANCE = "robot_uwb_stations_distance";
    public static final String ROBOT_CENTER_POINT_L1 = "robot_center_point_l1";
    public static final String ROBOT_CENTER_POINT_L2 = "robot_center_point_l2";
    public static final String ROBOT_CENTER_POINT_RADIUS = "robot_center_point_radius";

    public static final String ROBOT_SETTING_DYNAMIC_AVOID = "robot_setting_dynamic_avoid";

    //过流保护状态
    public static final String REQ_WHEEL_OVER_DANGER = "req_wheel_over_danger"; // 过流时轮子锁死
    public static final String REQ_WHEEL_OVER_NORMAL = "req_wheel_over_normal"; // 过流锁死状态解锁，轮子正常
    public static final int STATUS_WHEEL_NORMAL = 0;
    public static final int STATUS_WHEEL_OVER = 1;
    public static final int STATUS_WHEEL_ABNORMAL = 2;


    /**
     * setting value
     */
    public static final int ROBOT_SETTING_ENABLE = 1;
    public static final int ROBOT_SETTING_DISABLE = 0;

    public static final String ROBOT_SETTINGS_BATTERY_INFO = "robot_setting_battery_info";
    public static final String ROBOT_SETTINGS_EMERGENCY_INFO = "robot_setting_emergency_info";
    public static final String ROBOT_SETTINGS_BMS_WARNING_STATUS = "robot_setting_bms_warning_status";

    public static final String ROBOT_SETTINGS_POSE_ESTIMATE = "robot_setting_pose_estimate";
    public static final String ROBOT_SETTINGS_WHEEL_OVER_CURRENT = "robot_setting_wheel_over_current";
    public static final String ROBOT_SETTINGS_NAVIGATION_LINE_TRACKING = "robot_setting_navigation_line_tracking";
    public static final String ROBOT_SETTING_ENABLE_TARGET_CUSTOM = "robot_setting_enable_target_custom";
    public static final String ROBOT_SETTING_ENABLE_SINGLE_MARKER = "robot_setting_enable_single_marker";
    public static final String ROBOT_SETTING_ENABLE_DYNAMIC_MAP_FILTER = "robot_setting_enable_dynamic_map_filter";
    public static final String ROBOT_SETTING_NAVIGATION_BREAK_MODE_LEVEL = "robot_setting_navigation_break_mode_level";
    public static final String ROBOT_SETTING_NAVIGATION_START_MODE_LEVEL = "robot_setting_navigation_start_mode_level";
    public static final String ROBOT_SETTING_ENABLE_BLACKCHECK = "robot_setting_enable_blackcheck";
    public static final String ROBOT_SETTING_ENABLE_GRID_FILTER_USE_COSTMAP_OBS = "robot_setting_enable_grid_filter_use_costmap_obs";
    public static final String ROBOT_SETTING_NAVIGATION_WHEEL_SLIP_MODE_LEVEL = "robot_setting_navigation_wheel_slip_mode_level";
    public static final String ROBOT_SETTING_CUSTOM_ANGULAR_SPEED = "robot_setting_custom_angular_speed";
    public static final String ROBOT_SETTING_REMOTE_NAVIGATION_IS_NEED_PLAY_SOUND = "robot_setting_remote_navigation_is_need_play_sound";
    public static final String ROBOT_SETTING_FINAL_TIMEOUT = "robot_setting_final_timeout";
    public static final String ROBOT_SETTING_ASR_POP_UP_TYPE = "robot_setting_asr_pop_up_type";
    //点图分离setting配置key
    public static final String ROBOT_SETTING_MAP_POS_SEPARATE = "robot_setting_map_pos_separate";

    //点图分离setting设置项默认值
    public static final int SETTING_DEFAULT_MAP_POS_SEPARATE = 1;
    // ota 类型 升级1 降级2
    public static final String ROBOT_SETTING_OTA_TYPE = "robot_setting_ota_type";
    public static final String ROBOT_SETTING_PARKING_AREA = "robot_setting_parking_area";
    public static final String ROBOT_SETTING_NAVI_FACTORY_ROBOT_RADIUS = "robot_setting_navi_factory_robot_radius";
    public static final String ROBOT_SETTING_NAVI_ROBOT_STRUCTURE_MODE = "robot_setting_navi_robot_structure_mode";
    public static final String ROBOT_SETTING_NAVI_LETHAL_RADIUS_OFFSET = "robot_setting_navi_lethal_radius_offset";
    public static final String ROBOT_SETTING_DOCKING_AUTO_ADAPT_MODE = "robot_setting_docking_auto_adapt_mode";

    //加速度设置值的key
    public static final String ROBOT_SETTING_ACCELERATION_SPEED_VALUE = "robot_setting_acceleration_speed_value";

//    system_enable_single_marker

    public static final int ROBOT_STATUS_IDLE = 0;
    public static final int ROBOT_STATUS_BUSY = 1;

    public static final String STATUS_REMOTE_NAVI = "status_remote_navi";

    public static final String UNDERLINE = "_";

    //============= Charge Policy ===========//
    public static final int CHARGE_VISION_POLICY = 0;
    public static final int CHARGE_WIRE_POLICY = 1;
    public static final int CHARGE_MAX_POLICY = 2;

    //============= Set ChargePoint Policy ===========//
    public static final int BACKTO_CHARGEPOINT_POLICY = 0;//回充时，背对充电桩
    public static final int FACETO_CHARGEPOINT_POLICY = 1;//回充时，面对充电桩
    public static final int VISION_CHARGEPOINT_POLICY = 2;//视觉底充
    public static final int WIRE_CHARGING_POLICY = 3;//线充
    public static final int VISION_CHARGEPOINT_SIDE_POLICY = 4; //视觉侧充
    public static final int CHARGEPOINT_MAX_POLICY = 5;

    //Bi report level
    public static final int BI_LEVEL_VERBOSE = 0; //详细
    public static final int BI_LEVEL_DEBUG = 1;   //调式
    public static final int BI_LEVEL_INFO = 2;    //统计
    public static final int BI_LEVEL_WARN = 3;    //警告
    public static final int BI_LEVEL_ERROR = 4;   //错误
    public static final int BI_LEVEL_ASSET = 5;   //维护
    public static final int BI_LEVEL_NONE = 10001; //不上报

    //Bi type
    public static final String BI_TYPE_NORMAL = "bi_type_normal";
    public static final String BI_TYPE_CALL_CHAIN = "bi_type_call_chain";

    //Bi fields
    public static final String BI_PARAM_TABLE_NAME = "tableName";
    public static final String BI_PARAM_DATA = "data";
    public static final String BI_PARAM_TYPE = "type";
    public static final String BI_PARAM_LEVEL = "level";
    public static final String BI_PARAM_NOT_FORCE = "notForce";

    //OTA new version info
    //Extras:JSON_OTA_TARGET_VERSIONs
    //      JSON_OTA_TARGET_DESCRITION
    // if extras is null, It indicats there is no new version.
    public static final String ACTION_OTA_NEW_VERSION = "com.ainirobot.ota.intent.action.NEW_VERSION";

    //Ota Update file download status
    public static final String ACTION_OTA_DOWNLOAD_FILE_STATUS = "com.ainirobot.ota.intent.action.DOWNLOAD_STATUS";
    public static final String EXTRA_DOWNLOAD_FILE_STATUS = "download_status";
    public static final String DOWNLOAD_FILE_STATUS_IDLE = "idle";
    public static final String DOWNLOAD_FILE_STATUS_DOWLOADING = "downloading";
    public static final String DOWNLOAD_FILE_TYPE_PAUSE = "pause";
    public static final String DOWNLOAD_FILE_STATUS_DONE = "done";
    public static final String DOWNLOAD_FILE_STATUS_FAILED = "failed";


    /**
     * Ota UI display
     */
    public static final String ACTION_OTA_UPDATE_UI_DISPLAY = "com.ainirobot.ota.intent.action.UPDATE_UI_DISPLAY";
    public static final String EXTRA_UPDATE_UI_DISPLAY = "update_ui_display";

    public static final int DEFAULT_BODY_SPEED = 50;
    public static final int DEFAULT_BATTERY_LOW_LEVEL = 10;
    public static final int DEFAULT_FOCUS_FOLLOW_TRACK_TIMEOUT = 500;

    /**
     * NavigationService WorkMode status
     */
    public static final String NAVIGATION_WORK_MODE_CREATING_MAP = "CREATING_MAP";

    public static final String CHASSIS_CLIENT_TYPE_TK1 = "client_tk1";
    public static final String CHASSIS_CLIENT_TYPE_WAITER = "client_waiter";

    /**
     * 扫码器的接入方式，目前只有豹小递机器人使用扫码器。
     * 821版本豹小递扫码器接入在TX1上，845版豹小递扫码器接在android上。
     */
    public static final String SCANNER_ACCESS_MODE_ANDROID = "type_android";
    public static final String SCANNER_ACCESS_MODE_TX1 = "type_tx1";

    /**
     * 重定位执行的类型
     * client_common 通用重定位逻辑
     * client_waiter    豹跑堂机器人采用的重定位逻辑，二维码重定位
     */
    public static final String RELOCATION_TYPE_COMMON = "type_common";
    public static final String RELOCATION_TYPE_WAITER = "type_waiter";

    public enum RelocationMode {
        FORCE(0),
        MANUL(1),
        LASER(2),
        VISION(3),
        FIXED(4),
        TARGET(5);

        int value;

        RelocationMode(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /**
     * 服务器分区定义
     */
    public enum CloudServerZone {
        DEFAULT("default"),
        JP("jp"),
        US("us");

        String value;

        CloudServerZone(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static CloudServerZone fromValue(String value) {
            for (CloudServerZone zone : CloudServerZone.values()) {
                if (zone.getValue().equals(value)) {
                    return zone;
                }
            }
            return null;
        }
    }

    public static enum OSType {
        DEFAULT("default"),
        ROBOTOS("robotos"),
        AGENTOS("agentos");

        String value;

        private OSType(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }

        public static OSType fromValue(String value) {
            for(OSType type : values()) {
                if (type.getValue().equals(value)) {
                    return type;
                }
            }

            return null;
        }
    }

    /*-------------radar休眠时间--------------**/
    public static final String RADAR_CLOSE_DELAY_TIME = "radar_close_delay_time";
    public static final int RADAR_DEFAULT_DELAY_TIME = 5 * 60 * 1000;

    //============= leave pile ============//
    public static final int STATUS_LEAVE_PILE_DISABLE_RADAR = 1007;
    public static final int STATUS_LEAVE_PILE_ENABLE_RADAR = 1008;
    public static final int STATUS_LEAVE_PILE_OPEN_RADAR_SUCCESS = 1009;
    public static final int STATUS_LEAVE_PILE_GO_FORWARD_START = 1010;

    /*-------------在充电桩--------------**/
    public static final int RESULT_OK_NO_ESTIMATE_CHARGING = 1111;
    public static final int RESULT_OK_ESTIMATE_WITHIN_RANGE = 1112;
    public static final int RESULT_OK_NO_ESTIMATE_CHARGING_WIRE = 1115;
    public static final int RESULT_OK_ESTIMATE_WITHIN_RANGE_WIRE = 1116;
    /*-------------不在充电桩--------------**/
    public static final int RESULT_NO_ESTIMATE_NO_CHARGING = 1113;
    public static final int RESULT_ESTIMATE_WITHOUT_RANGE = 1114;
    public static final String KEY_COORDINATE_DEVIATION = "coordinateDeviation";

    /*-------------离桩--------------**/
    public static final int RESULT_FAILURE_MOTION_AVOID_STOP = -1001;
    public static final int RESULT_FAILURE_TIMEOUT = -1002;
    public static final int STATUS_LEAVE_PILE_OPEN_RADAR_FAILURE = -1010;
    public static final int STATUS_LEAVE_PILE_FAILURE_WIRE_CHARGING = -1117;

    public static final String JSON_TASK_MODE_ID = "task_id";
    public static final String JSON_TASK_MODE_NAME = "task_mode";
    public static final String JSON_TASK_MODE_RESULT = "task_result";

    public static final String UNBIND_NO_CHECK = "unbind_no_check";
    public static final int DEVELOP = 1;//测试环境

    public static final String JSON_CMD_ID = "cmd_id";
    //TODO:以下为业务代码
    public static final String JSON_CMD_FROM = "remote_";
    public static final String STANDBY_REPORT_SUCCESS = "0";
    public static final String ALREADY_IN_STANDBY = "-1";
    public static final String CAN_NOT_ENTER_STANDBY = "-2";
    public static final String NOT_IN_STANDBY = "-3";
    public static final String FUNCTION_STANDBY = "-4";
    public static final String CAN_NOT_EXIT_STANDBY = "-5";

    public static final String JSON_MODULE_CODE = "module_code";
    public static final String JSON_DESCRIPTION = "description";
    public static final String JSON_CONFIG_NAME = "config_name";
    public static final String JSON_CONFIG_JSON = "config_json";

    //雷达开关状态
    public static final int RADAR_STATUS_OPENED = 0;
    public static final int RADAR_STATUS_CLOSED = 1;
    public static final int RADAR_STATUS_OPENING = 2;
    public static final int RADAR_STATUS_CLOSING = 3;

    public static final String JSON_TASK_ID = "task_id";
    public static final String JSON_TASK_TYPE = "task_type";
    public static final String JSON_TASK_EXEC_RESULT = "exec_result";
    public static final String JSON_TASK_EXEC_DATA = "exec_data";

    public static final String JSON_APP_TYPE = "app_type";
    public static final String JSON_APP_NAME = "app_name";
    public static final String JSON_APP_VERSION = "app_version";

    /**
     * ChatMax上报赞踩
     */
    public static final String JSON_CHAT_MAX_SID = "chatmax_sid";
    public static final String JSON_FEEDBACK_RESULT = "feedback_result";

    /**
     * 主动问题上报数据Key
     */
    public static final String KEY_PROBLEM_OCCURRENCE_TIME = "key_problem_occurrenceTime";
    public static final String KEY_PROBLEM_ISSUE_TYPE = "key_problem_issueType";
    public static final String KEY_PROBLEM_PACKAGE_NAME = "key_problem_packageName";
    public static final String KEY_PROBLEM_PAGE_NAME = "key_problem_pageName";
    public static final String KEY_PROBLEM_DESCRIPTION = "key_problem_description";


    /**
     * 快照日志视觉VisionSDK缓存的照片信息,HeadService每次缓存快照时会清除无效的，打包成功后需手动删除
     * /sdcard/ninjia/log/vision/snapshot/ + cacheId
     */
    public static final String LOGS_DIR_VISION_SNAPSHOT_CACHE = "/ninjia/log/vision/snapshot/";

    public enum ChassisCameraType {
        TYPE_NONE(0),
        TYPE_MONO(1),
        TYPE_DEPTH(2),
        TYPE_TOP_IR(3),
        TYPE_CHARGE_IR(4);

        int value;

        ChassisCameraType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /**
     * 导航任务优先级
     * 远程遥控 ＞ 低电回充导航中 ＞ 领位过程中 ＞ 送餐过程中
     * ＞ 跑堂任务执行中 ＞ 巡航过程中 ＞ 返回起点 ＞ 返回任务处理点
     * ＞ 返回迎宾点 ＞ 返回领位点 ＞ 揽客前迎 ＞ 定时关机导航中 ＞  一键直达
     */
    public enum NavigationPriority {
        /**
         * 领位OPK  领位过程中
         **/
        PRIORITY_OPK_LEADING_GO(23),
        /**
         * 领位OPK  返回领位点
         **/
        PRIORITY_OPK_LEADING_BACK(16),
        /**
         * 送餐OPK  送餐过程中
         **/
        PRIORITY_OPK_MEAL_GO(22),
        /**
         * 送餐OPK  返回起点
         **/
        PRIORITY_OPK_MEAL_BACK(19),
        /**
         * 小食分发OPK任务执行中
         **/
        PRIORITY_OPK_CRUISE(22),
        /**
         * 跑堂OPK  任务执行中
         **/
        PRIORITY_OPK_SERVICE_GO(21),
        /**
         * 跑堂OPK  返回任务处理点
         **/
        PRIORITY_OPK_SERVICE_BACK(18),
        /**
         * 揽客OPK  领位过程中
         **/
        PRIORITY_OPK_PORTAL_LEADING(23),
        /**
         * 揽客OPK  返回迎宾点
         **/
        PRIORITY_OPK_PORTAL_BACK(17),
        /**
         * 揽客OPK  揽客前迎
         **/
        PRIORITY_OPK_PORTAL_GO_FORWARD(15),
        /**
         * 低电量回充
         **/
        PRIORITY_SYSTEM_LOW_BATTERY_CHARGE(24),
        /**
         * 定时关机
         **/
        PRIORITY_SYSTEM_TIMED_SHUT_DOWN(14),
        /**
         * 远程看店 一键直达
         **/
        PRIORITY_VIDEO_CALL_NAVIGATION(13),
        /**
         * 远程看店  远程遥控
         **/
        PRIORITY_VIDEO_CALL_REMOTE_CONTROL(25);

        int priority;

        NavigationPriority(int priority) {
            this.priority = priority;
        }

        public int getPriority() {
            return priority;
        }
    }

    /**
     * 地图类型，组合类型。
     * 本地序列化和服务端上传的都是这个统一的组合类型，
     * 注意：只可以增加，不允许修改；
     */
    public enum MapType {
        TYPE_UNKNOWN(0),//未知异常类型
        TYPE_NORMAL(1), //纯激光
        TYPE_VISION(2), //纯视觉
        TYPE_TARGET_NORMAL(3),  //猎户码
        TYPE_TARGET_CUSTOM(4),  //擎朗码
        TYPE_TARGET_CUSTOM_D(5),//普渡码
        TYPE_VISION_TARGET_NORMAL(11),//视觉+猎户码，2022.12.18之后版本开始支持
        TYPE_VISION_TARGET_QL(12),   //视觉+擎朗码
        TYPE_VISION_TARGET_PD(13),   //视觉+普渡码
        ;

        int value;

        MapType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /**
     * 地图，视觉类型。
     * 注意：只可以增加，不允许修改；
     */
    public enum VisionType {
        TYPE_NONE(0),//无视觉
        TYPE_VISION(1), //有视觉
        ;

        int value;

        VisionType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /**
     * 地图，标识码类型。
     * 注意：只可以增加，不允许修改；
     */
    public enum TargetType {
        TYPE_TARGET_NONE(0),//无标识码
        TYPE_TARGET_NORMAL(1),//猎户标识码
        TYPE_TARGET_QL(2), //擎朗标识码
        TYPE_TARGET_PD(3), //普渡标识码
        ;

        int value;

        TargetType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /*-------------充电方式--------------**/
    public static final String CHARGING_TYPE_PILE = "charging_pile";
    public static final String CHARGING_TYPE_WIRE = "charging_wire";

    //-------------- 地图漂移状态 -----------//

    public static final int MAP_DRIFT_WARNING = 1;
    public static final int MAP_DRIFT_DANGEROUS = 2;

    /**
     * 底盘轮子的控制状态，仅针对招财豹生效
     */
    public enum WheelControlMode {
        TYPE_LOCK(1),
        TYPE_RELEASE(2);

        int value;

        WheelControlMode(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    //-------------- 地图出界状态 -----------//
    public static final String MAP_OUTSIDE = "outside_map";
    public static final String MAP_INSIDE = "inside_map";
    public static final String MAP_OUTSIDE_STATUS_REMOVE = "map_outside_status_remove";

    public static final String TIME_WARNING_PARAM_NO_NOTIFY_ALWAYS = "isNoNotifyAlways";

    /**
     * 招财豹Lora多机方案中异常状态，对应MultiRobotStatus内的errorStatus字段解释
     * 0表示正常  1表示地图不匹配 2表示lora断连
     */
    public static final int MULTIPLE_STATUS_NORMAL = 0;
    public static final int MULTIPLE_STATUS_ERROR_MAP_NOT_MATCH = 1;
    public static final int MULTIPLE_STATUS_ERROR_LORA_DISCONNECT = 3;
    public static final int MULTIPLE_STATUS_ERROR_VERSION_NOT_MATCH = 2;
    public static final int MULTIPLE_STATUS_ERROR_LORA_DEVICES_EXCEPTION = 11;
    public static final int MULTIPLE_STATUS_ERROR_LORA_DEVICES_TOO_FAR = 12;


    /**
     * 导航定位丢失后,盲走检测范围,分为高-10m 中-7m 低-5m 三档
     */
    public enum LOST_MAX_DISTANCE {

        RANGE_HIGH(10),
        RANGE_MIDDLE(7),
        RANGE_LOW(5),
        RANGE_SUPER_LOW(3);

        int value;

        LOST_MAX_DISTANCE(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /**
     * 机器是否被推动
     */
    public static final int NOT_BEING_PUSHED = 0;
    public static final int BEING_PUSHED = 1;

    /**
     * 根据特殊点位功能添加点位类型
     * 充电桩：type=1, 功能：充电上电位置
     * 回充点：type=2, 功能：自动充电返回的默认位置（充电桩前方固定位置，和充电桩位置绑定）
     * 定位点：type=3，功能：重定位默认位置
     * 接待点：type=4，功能：揽客、迎接默认位置
     */
    public static final int START_CHARGE_PILE_TYPE = 1;
    public static final int START_BACK_CHARGE_TYPE = 2;
    public static final int LOCATE_POSITION_TYPE = 3;
    public static final int RECEPTION_PILE = 4;

    public static class MAPCOLOR {
        public final static int PASS = 0xFFFFFFFF;
        public final static int BLOCK = 0xFFFF0033;
        public final static int UNDETECT = 0xFF567C79;
        public final static int OBSTACLE = 0xFF1A1A1A;
        public final static int BLOCK_EXPANSION = 0x80FF0033;//禁行线膨胀区域
        public final static int OBSTACLE_EXPANSION = 0x801A1A1A;//障碍物膨胀区域
    }

    public static final String ROBOT_SETTINGS_CURRENT_APP = "robot_settings_current_app";

    public static final int POSE_SAFE_DISTANCE_DEFAULT = 10; //安全距离默认值
    public static final float POSE_IGNORE_DEPTH_OBS_DISTANCE_DEFAULT = 0.35f; //深度摄像头障碍物忽略半径

    /*
     * 招财豹Pro, 豹小秘2, 豹小递, 豹厂通, Slim的灯灯效
     * 适用于锁骨灯, 胸口灯, 底盘灯
     * 具体设备支持的灯效, 由业务处理
     */
    public static final String JSON_CAN_LED_EFFECT = "json_can_led_effect";
    //关闭所有灯光
    public static final int LED_EFFECT_ALLOFF = 0x00;
    //正常
    public static final int LED_EFFECT_GREENNORMAL = 0x01;
    public static final int LED_EFFECT_BLUENORMAL = 0x02;
    public static final int LED_EFFECT_ORANGENORMAL = 0x03;
    public static final int LED_EFFECT_YELLOWNORMAL = 0x04;
    public static final int LED_EFFECT_REDNORMAL = 0x05;
    //呼吸
    public static final int LED_EFFECT_GREENBREATH = 0x11;
    public static final int LED_EFFECT_BLUEBREATH = 0x12;
    public static final int LED_EFFECT_ORANGEBREATH = 0x13;
    public static final int LED_EFFECT_YELLOWBREATH = 0x14;
    public static final int LED_EFFECT_REDBREATH = 0x15;
    //闪烁
    public static final int LED_EFFECT_GREENFLASH = 0x21;
    public static final int LED_EFFECT_BLUEFLASH = 0x22;
    public static final int LED_EFFECT_ORANGEFLASH = 0x23;
    public static final int LED_EFFECT_YELLOWFLASH = 0x24;
    public static final int LED_EFFECT_REDFLASH = 0x25;
    //转向
    public static final int LED_EFFECT_TURNRIGHT = 0x31;
    public static final int LED_EFFECT_TURNLEFT = 0x32;
    public static final int LED_EFFECT_SLIM_CLAVICLE_TURNRIGHT = 0x33;
    public static final int LED_EFFECT_SLIM_CLAVICLE_TURNLEFT = 0x34;

    /**
     * Slim托盘氛围灯开关定义
     */
    public static final String JSON_CAN_TRAY_AUTO_LED_EFFECT = "json_can_tray_auto_led_effect";
    //单层托盘控制
    public static final int TRAY_LED_EFFECT_TRAY1 = 0x41; //托盘1常亮
    public static final int TRAY_LED_EFFECT_TRAY2 = 0x42; //托盘2常亮
    //双层托盘控制，用正向反向表示左转右转
    public static final int TRAY_LED_EFFECT_BLUEFORWARDON = 0x43; //流水灯顺流点亮
    public static final int TRAY_LED_EFFECT_BLUEFORWARDOFF = 0x44; //流水灯顺流熄灭
    public static final int TRAY_LED_EFFECT_BLUEMETEORFORWARD = 0x45; //流星正向
    public static final int TRAY_LED_EFFECT_BLUEMETEORREVERSE = 0x46; //流星反向


    /**
     * 托盘氛围灯开关定义
     */
    public static final String JSON_CAN_TRAY_LIGHT_EFFECT = "json_can_tray_light_effect";
    public static final int TRAY_LIGHT_EFFECT_ALLOFF = 0x00;       //三层全关
    public static final int TRAY_LIGHT_EFFECT_TRAY01OFF = 0x10;       //第一层氛围灯关
    public static final int TRAY_LIGHT_EFFECT_TRAY01ON = 0x11;       //第一层氛围灯开
    public static final int TRAY_LIGHT_EFFECT_TRAY02OFF = 0x20;
    public static final int TRAY_LIGHT_EFFECT_TRAY02ON = 0x21;
    public static final int TRAY_LIGHT_EFFECT_TRAY03OFF = 0x30;
    public static final int TRAY_LIGHT_EFFECT_TRAY03ON = 0x31;
    public static final int TRAY_LIGHT_EFFECT_ALLON = 0xFF;       //三层全开

    /*
     * 旧版本灯带适配参数，做旧版本兼容
     * 招财Pro灯带控制，等效常量值
     * interface to set the bottom uartlightLEDs in ZCB2_NEWPRO
     */
    public static final String JSON_CAN_PRO_ZCB_EFFECT = "json_can_pro_zcb_effect";
    public static final String JSON_CAN_PRO_IOB_EFFECT = "json_can_pro_iob_effect";
    public static final int ZCB2UARTLED_GREENBREATH = 0xDE10;  //绿色呼吸效果
    public static final int ZCB2UARTLED_BLUEBREATH = 0xDE11;  //蓝色呼吸效果
    public static final int ZCB2UARTLED_ORANGEBREATH = 0xDE12;  //橙色呼吸效果
    public static final int ZCB2UARTLED_YELLOWBREATH = 0xDE13;  //黄色呼吸效果
    public static final int ZCB2UARTLED_BLUENORMAL = 0xDE14;  //蓝色正常效果
    public static final int ZCB2UARTLED_REDNORMAL = 0xDE15;
    public static final int ZCB2UARTLED_ORANGENORMAL = 0xDE16;
    public static final int ZCB2UARTLED_YELLOWNORMAL = 0xDE17;
    public static final int ZCB2UARTLED_GREENNORMAL = 0xDE18;
    public static final int ZCB2UARTLED_TURNRIGHT = 0xDE19;  //右转效果
    public static final int ZCB2UARTLED_TURNLEFT = 0xDE20;  //左转效果
    public static final int ZCB2UARTLED_REGFLASH = 0xDE21;  //红色闪效果
    public static final int ZCB2UARTLED_YELLOWFLASH = 0xDE22;
    public static final int ZCB2UARTLED_ALLOFF = 0xDE00;  //关闭所有zcb效果
    /**
     * 托盘氛围灯开关定义
     */
    public static final String JSON_CAN_TRAY_LED_EFFECT = "JSON_CAN_TRAY_LED_EFFECT";
    public static final int TRAYLEDS_TRAY01ON = 0xDF11;       //第一层氛围灯开
    public static final int TRAYLEDS_TRAY01OFF = 0xDF10;       //第一层氛围灯关
    public static final int TRAYLEDS_TRAY02ON = 0xDF21;
    public static final int TRAYLEDS_TRAY02OFF = 0xDF20;
    public static final int TRAYLEDS_TRAY03ON = 0xDF31;
    public static final int TRAYLEDS_TRAY03OFF = 0xDF30;
    public static final int TRAYLEDS_ALLON = 0xDFFF;       //三层全开
    public static final int TRAYLEDS_ALLOFF = 0xDF00;       //三层全关


    public static final String KEY_DISABLE_LXC = "orion.disable.lxc";//lxc被禁用标志位的key

    /**
     * 导航高级设置默认值
     */
    public static final int DEFAULT_TEMPORARY_OBS_EXTRA_RADUIS_OFFSET = 0;
    public static final int DEFAULT_TEMPORARY_RGBD_FILTER_LEVEL = 2;
    public static final int DEFAULT_MONO_CAMERA_SWITCH = 1;
    public static final int DEFAULT_USE_NEW_MULTI_ROBOTS_SAFE_CHECKER = 1;
    public static final int DEFAULT_REDUCE_SPEED_ENCOUNTER_PEOPLE = 1;
    public static class MAPFINISHSTATE {
        public final static int STATE_NO_MAP = 0;
        public final static int STATE_CREATE_DONE = 1 << 0;
        public final static int STATE_POINT2_SET_DONE = 1 << 1;//招财豹底部栏第二个点为非必设点
        public final static int STATE_POINT1_SET_DONE = 1 << 2;//所有产品底部栏第一个点为必设点
        public final static int STATE_GATE_ENTER_SET_DONE = 1 << 3;
        public final static int STATE_GATE_OUTER_SET_DONE = 1 << 4;
        public final static int STATE_ELEVATOR_CENTER_SET_DONE = 1 << 5;
        public final static int STATE_COMPLETE_MAP;

        static {
            if (ProductInfo.isElevatorCtrlProduct()) {
                //有电梯功能的暂时不检查地图完整性
                STATE_COMPLETE_MAP = STATE_CREATE_DONE;
            } else if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMeissa2()) {
                STATE_COMPLETE_MAP = STATE_CREATE_DONE | STATE_POINT1_SET_DONE;
            } else if (ProductInfo.isMiniProduct()
                    && ProductInfo.getDeviceProperties() != null
                    && ("1".equals(ProductInfo.getDeviceProperties()
                    .getProperty("TOPIR_mini2", "0"))
                    || "1".equals(ProductInfo.getDeviceProperties()
                    .getProperty("esp32", "0")))) {
                STATE_COMPLETE_MAP = STATE_CREATE_DONE;
            } else {
                STATE_COMPLETE_MAP = STATE_CREATE_DONE | STATE_POINT2_SET_DONE
                        | STATE_POINT1_SET_DONE;
            }
        }

    }

    //共享内存状态
    public static final String TYPE_PFD_RGBD_STATUS = "type_pfd_rgbd_status";
    public static final String TYPE_PFD_TOPIR_STATUS = "type_pfd_topir_status";
    public static final String TYPE_PFD_MAP_PGM_GET = "type_pfd_map_pgm_get";
    public static final String TYPE_PFD_MAP_PGM_SET = "type_pfd_map_pgm_set";

    public enum DEPTH_DEVICE {
        DEPTH_1(0),
        DEPTH_2(1),
        DEPTH_3(2),
        DEPTH_4(3);

        int id;

        DEPTH_DEVICE(int id) {
            this.id = id;
        }

        public int getId() {
            return id;
        }
    }

    public enum IR_DEVICE {
        MONO(0),
        TOP_IR(1),
        BACK_IR(2),
        STEREO(3);

        int id;

        IR_DEVICE(int id) {
            this.id = id;
        }

        public int getId() {
            return id;
        }
    }

    //device.properties 中的key、value定义
    public static final String SMARTCAM_KEY = "SMARTCAM";

    public static final String HEIGHTCAM_KEY = "Carryheight";
    public static final String SMARTCAM_VALUE = "T1CAM_T2T3LASER";//2,3层有线激光雷达
    public static final String SMARTCAM_HIGHTLIMIT_VALUE = "T1LASER";//1层有线激光雷达

    public static final String MOTORS_KEY = "Motors"; //电机类型
    public static final String MOTORS_D150 = "LZ65_ABHALL"; //Carry 150公斤负重电梯型号

    public static final String ROBOT_SETTING_AUTO_RELOCATE_ENABLED = "robot_setting_auto_relocate_enabled";

    public static final String ACTION_IMPORT_MAP = "com.ainirobot.maptool.LoadMap";
    public static final String REMOTE_IMPORT_MAP_BEGIN = "system_import_map_begin";
    public static final String REMOTE_IMPORT_MAP_END = "system_import_map_end";

    // 机器人梯控服务配置
    public static final String SETTING_ELEVATOR_CONFIG = "elevator_config";
    // 机器人梯控支持的电梯配置
    public static final String SETTING_BUILDING_CONFIG = "building_info";

    /**
     * 机器人乘电梯状态
     * 这里的定义对core及更上层业务使用，统一概念，对于elevator层不同梯控厂商可以有不同定义做映射。
     * 目前1-5的状态定义适合octa lci 梯控协议保持一致的。
     */
    public enum RobotElevatorState  {
        ENTER_ELEVATOR_COMPLETE(1),//进入电梯完成，
        LEAVE_ELEVATOR_COMPLETE(2),//离开电梯完成，
        END_ENTER_ELEVATOR(3),//取消进入电梯，
        END_LEAVE_ELEVATOR(4),//取消离开电梯，
        REQUEST_KEEP_DOOR_OPEN(5);//请求电梯保持开门，

        private final int value;

        RobotElevatorState (int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }


    //============= Runtime error burying point Start ===========//
    public static final String START_VISION_FAILED = "start_vision_failed";
    public static final String VISION_OTHER_EXCEPTION = "vision_other_exception";
    public static final String ALGORITHM_INIT_EXCEPTION = "algorithm_init_exception";
    public static final String STOP_VISION_FAILED = "stop_vision_failed";
    public static final String CAMERA_DISCONNECT = "camera_disconnect";

    public static final String OVER_CURRENT = "over_current";
    public static final String CAN_DISCONNECT = "can_disconnect";
    public static final String CHASSIS_CRASH = "chassis_crash";
    public static final String LXC_MEMORY_LEAK = "lxc_memory_leak";

    public static final String RADAR_OPEN_FAILED = "radar_open_failed";
    public static final String RADAR_CLOSE_FAILED = "radar_close_failed";

    public static final String MAP_NOT_MATCH = "map_not_match";
    public static final String LORA_DISCONNECT = "lora_disconnect";
    public static final String LORA_CONFIGURATION_FAILURE = "lora_configuration_failure";
    public static final String VERSION_NOT_MATCH = "version_not_match";

    public static final String CHARGE_POWER_DOWN = "charge_power_down";
    public static final String CHARGE_POWER_STABLE = "charge_power_stable";
    public static final String UNKNOWN_ERROR = "unknown_error";

    public enum RUNNING_ERROR_TYPE {
        TYPE_VISION() {
            public String getErrorType() {
                return "vision_error";
            }
        },
        TYPE_NAVIGATION() {
            @Override
            public String getErrorType() {
                return "navigation_error";
            }
        },
        TYPE_RADAR() {
            @Override
            public String getErrorType() {
                return "radar_error";
            }
        },
        TYPE_MULTIPLE_ROBOT() {
            @Override
            public String getErrorType() {
                return "multiple_robot_error";
            }
        },
        TYPE_CHARGE() {
            @Override
            public String getErrorType() {
                return "charge_error";
            }

            @Override
            public String getErrorMsg(String errorType, Object... args) {
                try {
                    return args[0] + "-" + args[1];
                } catch (Exception e) {
                    return UNKNOWN_ERROR;
                }
            }
        },
        TYPE_CAN() {
            @Override
            public String getErrorType() {
                return "can_error";
            }
        };

        public String getErrorType() {
            return "";
        }

        public String getErrorMsg(String errorMsgType) {
            return errorMsgType;
        }

        public String getErrorMsg(String errorType, Object... args) {
            return "";
        }
    }
    //============= Runtime error burying point End ===========//

    /**
     * 建图模式类型枚举
     * "standard"：标准模式
     * "visionAndTarget"：高级模式
     * "vision"：高级视觉模式
     */
    public enum CreateMapType {
        STANDARD("standard"),
        VISION_AND_TARGET("visionAndTarget"),
        VISION("vision");

        private String type;

        CreateMapType(String type) {
            this.type = type;
        }

        public String getType() {
            return type;
        }
    }
}
