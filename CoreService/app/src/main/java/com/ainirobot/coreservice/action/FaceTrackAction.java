/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.action;

import android.util.Log;

import com.ainirobot.coreservice.action.StopCommandList.IStopCommandChecker;
import com.ainirobot.coreservice.action.StopCommandList.StopCommand;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.Definition.TrackMode;
import com.ainirobot.coreservice.client.actionbean.FaceTrackBean;
import com.ainirobot.coreservice.client.actionbean.Person;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.utils.DelayTimer;
import com.ainirobot.coreservice.utils.MessageParser;
import com.ainirobot.robotlog.RobotLog;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicInteger;

public class FaceTrackAction extends AbstractAction<FaceTrackBean> {
    private static final String TAG = "FaceTrackAction";
    private static final long MIN_LOST_TIMEOUT = 500;
    private static final long TRACK_TIMEOUT = 500;

    private static final int MIN_FRAME_COUNT = 3; //Distance to determine the minimum number of frames

    private int mPersonId;
    private Person mCurrentPerson;

    private long mLostTimeout;
    private double mAvailableDistance;
    private String mSpeedListener;
    private DelayTimer mLostTimer;
    private DelayTimer mTrackTimer;
    private AtomicInteger mFrameCount = new AtomicInteger();
    private boolean isAllowMoveBody = true;

    public FaceTrackAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_FACE_TRACK, resList);
    }

    @Override
    protected boolean startAction(FaceTrackBean parameter, LocalApi api) {
        RobotLog.d("Focus follow started");
        mReqId = parameter.getReqId();
        parseParams(parameter);

        mCurrentPerson = null;
        mFrameCount.set(0);

        registerSpeedListener();
        mApi.setTrackTarget(mReqId, "", mPersonId, TrackMode.FACE);
        initTimer();
        return true;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        RobotLog.d("Focus follow stopped : " + isResetHW);
        unregisterStatusListener(mSpeedListener);
        //mApi.stopTrack(mReqId);
        mApi.stopSendPersonInfos();
        if (isAllowMoveBody) {
            mApi.stopMove(mReqId);
        }

        mApi.stopTrack(mReqId);

        destroyTimer();

        if (isResetHW) {
            mApi.resetHead(mReqId);
        }
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_HEAD_GET_ALL_PERSON_INFOS:
                handleFaceInfo(result);
                break;

            case Definition.CMD_HEAD_SET_TRACK_TARGET:
                handleTrackResult(result);
                break;

            default:
                break;
        }
        return true;
    }

    private void initTimer() {
        Log.d(TAG, "Timer init");
        mLostTimer = new DelayTimer(mLostTimeout, new Runnable() {
            @Override
            public void run() {
                onPersonLost();
            }
        });
        mLostTimer.start();

        mTrackTimer = new DelayTimer(TRACK_TIMEOUT, new Runnable() {
            @Override
            public void run() {
                onTrackTimeout();
            }
        });
        mTrackTimer.start();
    }

    private void destroyTimer() {
        if (mLostTimer != null) {
            mLostTimer.destroy();
            mLostTimer = null;
        }
        if (mTrackTimer != null) {
            mTrackTimer.destroy();
            mTrackTimer = null;
        }
    }

    private void handleTrackResult(String params) {
        try {
            Log.d(TAG, "Set track target result : " + params);
            JSONObject json = new JSONObject(params);
            String status = json.getString("status");
            switch (status) {
                case Definition.RESPONSE_OK:
                    mApi.getAllPersonInfos(mReqId, Definition.JSON_HEAD_CONTINUOUS);
                    onStatusUpdate(Definition.STATUS_TRACK_TARGET_SUCCEED, "Track target succeed");
                    break;

                case Definition.RESPONSE_TARGET_NOT_FOUND:
                    onError(Definition.ERROR_TARGET_NOT_FOUND, "Target not found");
                    break;

                case Definition.RESPONSE_ALREADY_IN_MODE:
                    Log.d(TAG, "Current already int track mode");
                    break;

                default:
                    break;
            }
        } catch (JSONException e) {
            onError(Definition.ERROR_SET_TRACK_FAILED, "Set track target failed");
        }
    }

    private void handleFaceInfo(String params) {
        //deal with first frame null，fresh timer
        try {
            JSONArray jsonArray = new JSONArray(params);
            if (jsonArray.length() == 0) {
                mLostTimer.reset();
                mTrackTimer.reset();
                return;
            }
        } catch (JSONException e) {
            e.printStackTrace();
            mLostTimer.reset();
            mTrackTimer.reset();
            return;
        }

        Person person = MessageParser.parseFaceInfo(mPersonId, params);
        //Person person = MessageParser.parseFaceInfo(mCurrentPerson, params, Double.MAX_VALUE);
        if (person != null) {
            if (isAllowMoveBody) {
                mApi.motionArc(mReqId, 0, person.getFaceInfo().getAngle(),
                        person.getFaceInfo().getHeadSpeed(), person.getFaceInfo().getLatency());
            }

            double distance = person.getFaceInfo().getDistance();
            double faceXAngle = person.getFaceInfo().getFaceAngleX();
            Log.d(TAG, "handleFaceInfo faceXAngle: " + faceXAngle);
            if (isFaraway(distance)) {
                onResult(Definition.STATUS_GUEST_FARAWAY, "Person is too far away ：" + distance);
                return;
            }

            if (mCurrentPerson == null) {
                onStatusUpdate(Definition.STATUS_GUEST_APPEAR, "Person appear");
            }
            mCurrentPerson = person;

            Log.d(TAG, "Timer reset");
            if (mLostTimer != null) {
                mLostTimer.reset();
            }

            if (mTrackTimer != null) {
                mTrackTimer.reset();
            }
        } else {
            Log.d(TAG, "Get all person info : no person");
            //TODO: Deal with the situation where no one has been found
        }
    }

    private boolean isFaraway(double distance) {
        if (distance > mAvailableDistance) {
            return mFrameCount.incrementAndGet() >= MIN_FRAME_COUNT;
        } else {
            mFrameCount.set(0);
            return false;
        }
    }

    private void onPersonLost() {
        Log.d(TAG, "lost timeout");
        onResult(Definition.STATUS_GUEST_LOST, "Lost timeout : " + mLostTimeout);
        mCurrentPerson = null;
    }

    private void onTrackTimeout() {
        Log.d(TAG, "track timeout");
        if (isAllowMoveBody) {
            mApi.stopMove(mReqId);
        }
    }

    private void parseParams(FaceTrackBean params) {
        mPersonId = params.getPersonId();

        double maxDistance = params.getMaxDistance();
        mAvailableDistance = ((maxDistance == 0) ? Double.MAX_VALUE : maxDistance);

        long trackTimeout = params.getLostTimer();
        mLostTimeout = trackTimeout < MIN_LOST_TIMEOUT ? MIN_LOST_TIMEOUT : trackTimeout;

        isAllowMoveBody = params.isAllowMoveBody();
    }

    @Override
    protected ArrayList<StopCommand> getStopCommands() {
        return new ArrayList<StopCommand>() {{
            //add(new StopCommand(Definition.CMD_HEAD_STOP_TRACK_TARGET, null));
        }};
    }

    @Override
    protected IStopCommandChecker getStopCommandChecker() {
        return new IStopCommandChecker() {
            @Override
            public boolean executeSuccess(String command, String result) {
                RobotLog.d("Focus stop check : " + command);
                switch (command) {
                    case Definition.CMD_HEAD_STOP_TRACK_TARGET:
                        return true;

                    default:
                        return false;
                }
            }
        };
    }

    @Override
    protected boolean verifyParam(FaceTrackBean param) {
        return true;
    }

    private void registerSpeedListener() {
        mSpeedListener = registerStatusListener(Definition.STATUS_SPEED, new StatusListener() {
            @Override
            public void onStatusUpdate(String data) {
                try {
                    if (data == null) {
                        return;
                    }
                    JSONObject json = new JSONObject(data);
                    double speedZ = json.getDouble(Definition.JSON_NAVI_ANGULAR_SPEED_Z);
                    double speedX = json.getDouble(Definition.JSON_NAVI_LINEAR_SPEED_X);
                    mApi.reportNavigationStatus(mReqId, speedX, speedZ);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }
}
