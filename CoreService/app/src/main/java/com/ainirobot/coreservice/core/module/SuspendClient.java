/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.core.module;

import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.listener.IActionListener;
import com.ainirobot.coreservice.service.CoreService;

import java.util.Map;

public class SuspendClient extends ModuleClient {
    private final String TAG = "SuspendClient";

    public SuspendClient(CoreService core, String name) {
        super(core, name);
    }

    @Override
    public int startAction(int reqId, int actionId, String params, IActionListener listener) {
        if (Definition.ACTION_LAMP_ANIMATION == actionId) {
            return super.startAction(reqId, actionId, params, listener);
        }

        Log.d(TAG, "Start action permission denied : " + actionId + " params : " + params);
        return Definition.CMD_SEND_ERROR_PERMISSION_DENIED;
    }

    @Override
    public int startAction(int reqId, int actionId, String params, IActionListener listener, boolean isSystem) {
        if (Definition.ACTION_LAMP_ANIMATION == actionId) {
            return super.startAction(reqId, actionId, params, listener, true);
        }

        Log.d(TAG, "Start action permission denied : " + actionId + " params : " + params);
        return Definition.CMD_SEND_ERROR_PERMISSION_DENIED;
    }

    @Override
    public int stopAction(int reqId, int actionId, boolean isResetHW) {
        if (Definition.ACTION_LAMP_ANIMATION == actionId) {
            return super.stopAction(reqId, actionId, isResetHW);
        }

        Log.d(TAG, "Stop action permission denied : " + actionId + " isResetHW : " + isResetHW);
        return Definition.CMD_SEND_ERROR_PERMISSION_DENIED;
    }

    @Override
    public int setLampColor(int reqId, int target, int color) {
        Log.d(TAG, "Set lame color permission denied");
        return Definition.CMD_SEND_ERROR_PERMISSION_DENIED;
    }

    @Override
    public int setLampAnimation(int reqId, int target, int start, int end,
                                int startTime, int endTime, int repeat, int onTime, int freeze) {
        Log.d(TAG, "Set lame animation permission denied");
        return Definition.CMD_SEND_ERROR_PERMISSION_DENIED;
    }

    @Override
    public int setLedLight(int reqId, String properties) {
        Log.d(TAG, "Set led light permission denied");
        return Definition.CMD_SEND_ERROR_PERMISSION_DENIED;
    }

    @Override
    public void setSystemStatusEnabled(String status, boolean enabled) {
        Log.d(TAG, "Set system status permission denied");
    }

    @Override
    public void recoverySystemStatus(Map<String, Boolean> status) {
        Log.d(TAG, "Recovery system status permission denied");
    }

    @Override
    public void setLanguage(String lang) {
        Log.d(TAG, "Set language permission denied");
    }

    @Override
    public void resetSystemStatus() {
        Log.d(TAG, "Reset system status permission denied");
    }
}
