/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.config;

import android.text.TextUtils;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.RobotSettings;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Set;


public class RobotConfig {

    private static final String TAG = "RobotConfig";

    @SerializedName("项目名称")
    private String name;

    @SerializedName("版本")
    private String version;

    @SerializedName("服务配置")
    private JsonObject service;

    private CoreConfig coreConfig;

    private static final String HARDWARE_SERVICE = "外设管理";
    private static final String HARDWARE_CONFIG = "外设配置";
    public static final String SWIPE_CARD_SERVICE = "刷卡器服务";
    public static final String SWIPE_THERMAL_SERVICE = "红外测温服务";
    public static final String CODE_SCANNER_SERVICE = "扫码器服务";
    public static final String PASSENGER_FLOW_SERVICE = "客流服务";
    public static final String CODE_VIDEO_SERVICE = "视频服务";
    private static final String CORE_SERVICE = "核心服务";
    public static final String D430_SERVICE = "D430服务";
    public static final String ELEVATOR_SERVICE = "梯控服务";
    public static final String UVC_CAMERA_SERVICE = "托盘摄像头服务";
    public static final String PASS_GATE_SERVICE = "闸机服务";
    public static final String AGENT_SERVICE = "agent语音服务";



    /**
     * 公共配置所有Service都可以获取到
     */
    public static final String PUBLIC_CONFIG = "公共配置";

    /**
     * 默认预置外设Service
     */
    private HashMap<String, String> mExternalServiceNames = new HashMap<String, String>() {{
        put("语音服务", RobotOS.SPEECH_SERVICE);
        put(AGENT_SERVICE, RobotOS.AGENT_SERVICE);
        put("导航服务", RobotOS.NAVIGATION_SERVICE);
        put("视觉服务", RobotOS.VISION_SERVICE);
        put("头部控制服务", RobotOS.HEAD_SERVICE);
        put("远程服务", RobotOS.REMOTE_SERVICE);
        put("系统服务", RobotOS.SYSTEM_SERVICE);
        put("升级服务", RobotOS.OTA_SERVICE);
        put(SWIPE_CARD_SERVICE, RobotOS.SWIPE_CARD_SERVICE);
        put(SWIPE_THERMAL_SERVICE, RobotOS.SWIPE_THERMAL_SERVICE);
        put(CODE_SCANNER_SERVICE, RobotOS.CODE_SCANNER_SERVICE);
        put(PASSENGER_FLOW_SERVICE, RobotOS.PASSENGER_FLOW_SERVICE);
        put(D430_SERVICE, RobotOS.D430_SERVICE);
        put(CODE_VIDEO_SERVICE, RobotOS.VIDEO_SERVICE);
        put(ELEVATOR_SERVICE, RobotOS.ELEVATOR_SERVICE);
        put(UVC_CAMERA_SERVICE,RobotOS.UVC_CAMERA_SERVICE);
        put(PASS_GATE_SERVICE, RobotOS.PASS_GATE_SERVICE);
    }};

    private List<ServiceConfig> serviceConfig = new ArrayList<>();
    private Gson gson = new Gson();

    public void parse() {
        if (service != null) {
            JsonObject publicConfig = service.getAsJsonObject(PUBLIC_CONFIG);
            service.remove(PUBLIC_CONFIG);

            Set<String> keys = service.keySet();
            for (String key : keys) {
                JsonObject jsonElement = service.getAsJsonObject(key);
                ServiceConfig config = parseServiceConfig(key, jsonElement);
                if (HARDWARE_SERVICE.equals(key)) {
                    parseHardwareService(config.getService(), jsonElement, publicConfig);
                } else if (CORE_SERVICE.equals(key)) {
                    parseCoreService(jsonElement, publicConfig);
                } else {
                    config.setPublicConfig(publicConfig);
                    serviceConfig.add(config);
                }
//                Log.d(TAG,"config:"+serviceConfig.toString());
            }
        }
    }

    private void parseHardwareService(String packageName, JsonObject hwConfig, JsonObject publicConfig) {
        if (hwConfig.has(HARDWARE_CONFIG)) {
            JsonObject json = hwConfig.getAsJsonObject(HARDWARE_CONFIG);
            Set<String> keys = json.keySet();
            for (String key : keys) {
                JsonObject jsonElement = json.getAsJsonObject(key);
                ServiceConfig config = parseServiceConfig(key, jsonElement);
                config.setService(packageName);
                config.setPublicConfig(publicConfig);
                serviceConfig.add(config);
            }
        }
    }

    private void parseCoreService(JsonObject hwConfig, JsonObject publicConfig) {
        coreConfig = gson.fromJson(hwConfig, CoreConfig.class);
        coreConfig.setPublicConfig(publicConfig);
        coreConfig.parse();
    }

    private ServiceConfig parseServiceConfig(String key, JsonObject json) {
        ServiceConfig config = gson.fromJson(json, ServiceConfig.class);
        if (mExternalServiceNames.containsKey(key)) {
            config.setServiceName(mExternalServiceNames.get(key));
        } else {
            config.setServiceName(key);
        }

        return parseConfigBySnModel(key, config);
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public void setService(JsonObject service) {
        this.service = service;
    }

    public List<ServiceConfig> getServiceConfig() {
        return serviceConfig;
    }

    public ServiceConfig getElevatorServiceConfig() {
        return getServiceConfig(RobotOS.ELEVATOR_SERVICE);
    }

    private ServiceConfig getServiceConfig(String key) {
        for (ServiceConfig config : serviceConfig) {
            if(config != null){
                String serviceName = config.getServiceName();
                if (!TextUtils.isEmpty(serviceName) && serviceName.equals(key)) {
                    return config;
                }
            }
        }
        return null;
    }

    public CoreConfig getCoreConfig() {
        return coreConfig == null ? new CoreConfig() : coreConfig;
    }

    public JsonObject getService(){
        return this.service;
    }

    /**
     * 在同一个大的产品情况下，为了满足不同小产品之间通过ota切换，通过机器人model和sn动态配置config文件
     *
     * @param serviceConfig 修改前的 ServiceConfig
     * @return 修改后的 ServiceConfig
     */
    public static ServiceConfig parseConfigBySnModel(String key, ServiceConfig serviceConfig) {
        String model = RobotSettings.getProductModel();
        JsonObject config = serviceConfig.getConfig();
        if (key.equals(RobotConfig.SWIPE_CARD_SERVICE)) {
            if (TextUtils.equals(model, ProductInfo.ProductModel.CM_XIAOMI_SWIPE_CARD.model)) {
                serviceConfig.setEnable(true);
            }
        } else if (key.equals(RobotConfig.SWIPE_THERMAL_SERVICE)) {
            if (TextUtils.equals(model, ProductInfo.ProductModel.CM_XIAOMI_HEXIN.model)) {
                serviceConfig.setEnable(true);
                config.addProperty(ProductInfo.SWIPE_THERMAL_PRODUCT_ID, ProductInfo.HEXIN_ID);
            } else if (TextUtils.equals(model, ProductInfo.ProductModel.CM_XIAOMI_JUGE.model)) {
                config.addProperty(ProductInfo.SWIPE_THERMAL_PRODUCT_ID, ProductInfo.JUGE_ID);
                serviceConfig.setEnable(true);
            } else {
                //暂无其它产品不处理
            }
        } else if (key.equals(RobotConfig.CODE_SCANNER_SERVICE)) {
            if (TextUtils.equals(model, ProductInfo.ProductModel.CM_KTV_BASE.model)) {
                serviceConfig.setEnable(true);
            }
        }
        //豹小秘1.1版本比较特殊，需要单独进行判断
        return serviceConfig;
    }

    @Override
    public String toString() {
        return "projectName: " + this.name + ", version: " + version
                + ", coreConfig: " + getCoreConfig() + ", config: " + service;
    }
}
