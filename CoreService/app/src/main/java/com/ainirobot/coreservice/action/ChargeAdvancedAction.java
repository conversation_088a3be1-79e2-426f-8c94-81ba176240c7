package com.ainirobot.coreservice.action;

import android.util.Log;

import com.ainirobot.coreservice.action.advance_charge.AdvanceChargeStateEnum;
import com.ainirobot.coreservice.action.advance_charge.ChargeCmdStateListener;
import com.ainirobot.coreservice.action.advance_charge.bean.StateData;
import com.ainirobot.coreservice.action.advance_charge.state.BaseAdvanceChargeState;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.AutoChargeBean;
import com.ainirobot.coreservice.core.LocalApi;

import java.util.ArrayList;

public class ChargeAdvancedAction extends AbstractAction<AutoChargeBean> implements ChargeCmdStateListener {
    private static final String TAG = "ChargeAdvancedAction";
    private BaseAdvanceChargeState mCurrentChargeState;
    private AutoChargeBean mParams;

    protected ChargeAdvancedAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_AUTO_NAVI_CHARGE, resList);
    }

    @Override
    protected boolean startAction(AutoChargeBean parameter, LocalApi api) {
        Log.i(TAG, "startAction , parameter: " + parameter);
        this.mParams = parameter;
        mCurrentChargeState = AdvanceChargeStateEnum.PREPARE.getChargeState();
        StateData stateData = new StateData(parameter);
        mCurrentChargeState.start(this, stateData, this);
        return true;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        Log.i(TAG, "stopAction , isResetHW: " + isResetHW);
        if (null != mCurrentChargeState) {
            mCurrentChargeState.stop();
        }
        return true;
    }

    public ActionManager getActionManager() {
        return mManager;
    }
    public String getRobotInfo(int reqId, String param) {
        return mManager.getCoreService().getRobotInfoManager().getRobotInfo(reqId, param);
    }

    public LocalApi getApi() {
        return mApi;
    }

    public int getReqId() {
        return mReqId;
    }

    public void sendCommand(int reqId, String command, String params) {
        Log.d(TAG, "onSendCommand command:" + command + " params:" + params);
        mApi.sendCommand(reqId, command, params);
    }

    @Override
    public void onStateUpdate(int state, String msg) {
        Log.d(TAG, "onStateUpdate state:" + state + " msg:" + msg);
        onStatusUpdate(state, msg);
    }

    @Override
    public void onStateRunSuccess(AdvanceChargeStateEnum stateEnum, StateData data) {
        Log.d(TAG, "onStateRunSuccess " + stateEnum.name());
        mCurrentChargeState = stateEnum.getChargeState();
        mCurrentChargeState.start(this, data, this);
    }

    @Override
    public void onStateRunFailed(int errorCode, String errorMessage) {
        Log.d(TAG, "onStateRunFailed errorCode:" + errorCode + " errorMessage:" + errorMessage);
        onError(errorCode, errorMessage);
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        Log.d(TAG, "cmdResponse command:" + command + " result:" + result + " extraData:" + extraData);
        return mCurrentChargeState.cmdResponse(cmdId, command, result, extraData);
    }

    @Override
    public boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        Log.d(TAG, "cmdStatusUpdate command:" + command + " status:" + status + " extraData:" + extraData);
        return mCurrentChargeState.cmdStatusUpdate(cmdId, command, status, extraData);
    }

    public void finishAction(int status, String msg) {
        Log.d(TAG, " finishAction : " + status + " " + msg);
        onResult(status, msg);
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return new ArrayList<StopCommandList.StopCommand>() {{
            add(new StopCommandList.StopCommand(Definition.CMD_NAVI_STOP_NAVIGATION, null));
        }};
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        Log.d(TAG, "getStopCommandChecker call ");
        return new StopCommandList.IStopCommandChecker() {
            @Override
            public boolean executeSuccess(String command, String result) {
                Log.d(TAG, "stop check : " + command);
                switch (command) {
                    case Definition.CMD_NAVI_STOP_NAVIGATION:
                        return true;
                    default:
                        return false;
                }
            }
        };
    }

    @Override
    protected boolean verifyParam(AutoChargeBean param) {
        return true;
    }
}
