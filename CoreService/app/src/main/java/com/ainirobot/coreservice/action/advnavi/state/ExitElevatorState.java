package com.ainirobot.coreservice.action.advnavi.state;

import android.util.Log;

import com.ainirobot.coreservice.action.StatusListener;
import com.ainirobot.coreservice.action.StopCommandList;
import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.action.advnavi.elevatorcmd.ElevatorControlUtil;
import com.ainirobot.coreservice.action.advnavi.elevatorcmd.ElevatorControlUtilListener;
import com.ainirobot.coreservice.bean.ElevatorState;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.MultiRobotStatus;
import com.ainirobot.coreservice.client.actionbean.NavigationAdvancedBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.utils.DelayTask;

import java.util.ArrayList;
import java.util.List;

/**
 * Data: 2022/8/9 11:08
 * Author: wanglijing
 * Description: ExitElevator
 * <p>
 * 出梯状态
 * 出梯没有重试次数限制，只要电梯状态正常，开门正常，就一直尝试出梯
 * 如果电梯口点没有机器人，必须到达"电梯口"才算成功
 */
public class ExitElevatorState extends BaseNavigationState {

    private static final String DELAY_CHECK = "delay_check";
    /**
     * 导航去电梯中心超时时间：30s改为2分钟，多机器人时，可能会等待其他机器人
     * （最初设置30秒: 电梯口距离电梯中心很近，如果能出去会很快，
     * 如果出不去，就尽快放弃，回到电梯中心，）
     */
    private static final long EXIT_FAIL_TIME = 2 * 60 * 1000;

    private Status status;
    //导航失败次数
    private int naviFailCount = 0;
    //导航失败最大重试次数，30秒内最多重试5次导航，超过5次会上报出题次数失败过多，最终会走30超时后的重新进电梯流程
    private final int NAVI_FAIL_MAX_COUNT = 5;
    private ElevatorControlUtil controlUtil;
    private String elevatorGatePoseName;

    private enum Status {
        IDLE,
        //正在导航去电梯口
        GO_GATE,
        //成功到达电梯口
        ARRIVED,
        REPORT_LEAVE_ELEVATOR_COMPLETE,  //机器人离开电梯成功，状态上报
        //回电梯中心
        //机器人已经在电梯内，电梯状态异常，无法保证开门状态。
        //流转状态到进电梯状态
        CONTINUE_GO_CENTER,
    }

    ExitElevatorState(String name) {
        super(name);
    }

    @Override
    protected boolean isEnableMultiRobotStatus() {
        return true;
    }

    @Override
    protected void preAction(StateData data, IStateListener listener) {
        super.preAction(data, listener);
        naviFailCount = 0;
        controlUtil = new ElevatorControlUtil(this,
                mStateData.getTargetFloorIndex(),
                false,
                elevatorControlUtilListener);

//        elevatorGatePoseName = mStateData.getElevatorName() +  "-" + Definition.ELEVATOR_ENTER_POSE;
        //TODO 改为按类型获取电梯口
        Pose elevatorGatePose = getPoseByTypeId(Definition.ELEVATOR_ENTRANCE_TYPE);
        if (null != elevatorGatePose) {
            elevatorGatePoseName = elevatorGatePose.getName();
        }
        Log.d(TAG, "elevatorGatePoseName: " + elevatorGatePoseName);

        updateCurrentStatus(Status.IDLE);
    }

    @Override
    protected void doAction() {
        super.doAction();
        onStatusUpdate(Definition.STATUS_EXIT_ELEVATOR_START, "start exit elevator");
        onStatusUpdate(Definition.STATUS_START_GO_ELEVATOR_GATE, "navi to elevator gate");

        //多机 多机状态监听解析
        parseMultiRobotInfo();

        naviToElevatorGate();   //导航去电梯口
        continueOpenDoor(); //控制电梯开门

        registerStatusListener(Definition.STATUS_ELEVATOR, data -> {
            if (!isAlive()) {
                return;
            }
            Log.d(TAG, "onStatusUpdate: " + data);
            onElevatorStatusUpdate(data);
        });
    }

    //判断楼层信息，非目标楼层，停止出梯，返回电梯中心
    private void onElevatorStatusUpdate(String data) {
        try {
            ElevatorState state = mGson.fromJson(data, ElevatorState.class);
            if (state.getFloor() != mStateData.getTargetFloorIndex()) {
                Log.d(TAG, "onElevatorStatusUpdate not target floor, back to center");
                backToCenter();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void exit() {
        unregisterStatusListener(Definition.STATUS_ELEVATOR);
        DelayTask.cancel(DELAY_CHECK);

        if (null != controlUtil
                && !controlUtil.inElevatorRangeNoResult()) {
            Log.d(TAG, "release elevator from ExitElevatorState exit. when robot not in elevator range.");
            controlUtil.releaseElevatorControl(); //状态结束时，机器人在电梯外则释放电梯
        }
        stopControlElevator();  //exit，关门

        super.exit();
    }

    @Override
    protected BaseState getNextState() {
        switch (actionState) {
            //导航成功，成功到达电梯口
            case ACTION_NAVI_SUC:
                //接着导航去目标点
                return StateEnum.GO_DESTINATION.getState();
            //导航失败，梯控也异常了
            case ACTION_CONTINUE_TO_CENTER:
                //重试进梯
                return StateEnum.ENTER_ELEVATOR.getState();
        }
        return super.getNextState();
    }

    @Override
    protected boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        if (null != controlUtil
                && controlUtil.cmdResponse(command, result, extraData)) {
            //透传给elevatorControlUtil
            return true;
        }
        switch (command) {
            case Definition.CMD_CONTROL_ROBOT_STATE_UPDATE:
                handleRobotStatusUpdateResult(result, extraData);
                return true;
        }
        return super.cmdResponse(cmdId, command, result, extraData);
    }

    @Override
    protected boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        if (null != controlUtil
                && controlUtil.cmdResponse(command, command, extraData)) {
            //透传给elevatorControlUtil
            return true;
        }
        return super.cmdStatusUpdate(cmdId, command, status, extraData);
    }

    @Override
    protected void onNavigationStatusUpdate(int result, String message, String extraData) {
        switch (result) {
            case Definition.STATUS_START_NAVIGATION:
                Log.d(TAG, "start exit elevator");
                startExitElevatorTimer();
                break;
        }
        onStatusUpdate(result, message, extraData);
    }

    @Override
    protected void onNavigationError(int result, String message, String extraData) {
        switch (result) {
            case Definition.ERROR_DESTINATION_CAN_NOT_ARRAIVE:
            case Definition.ERROR_MULTI_ROBOT_WAITING_TIMEOUT:
            case Definition.ERROR_NAVIGATION_AVOID_TIMEOUT:
                naviRetry();    //重试
                return;
        }
        onResult(result, message, extraData);
    }

    @Override
    protected void onNavigationResult(int result, String message, String extraData) {
        switch (result) {
            case Definition.ERROR_IN_DESTINATION:
            case Definition.RESULT_OK:
                naviSuccess();
                break;
        }
    }

    @Override
    protected void handleMultipleRobotStatus(List<MultiRobotStatus> robotStatusList) {

    }

    @Override
    protected List<StopCommandList.StopCommand> getStopCommands() {
        return new ArrayList<StopCommandList.StopCommand>() {{
            add(new StopCommandList.StopCommand(Definition.CMD_CONTROL_RELEASE_ELEVATOR, null));
        }};
    }

    public void startExitElevatorTimer() {
        DelayTask.cancel(DELAY_CHECK);
        DelayTask.submit(DELAY_CHECK, new Runnable() {
            @Override
            public void run() {
                if (isAlive() && null != controlUtil) {
                    Log.d(TAG, "exit elevator timer out !!");
                    openDoorFail(controlUtil.inElevatorRange());//导航超时
                }
            }
        }, EXIT_FAIL_TIME);
    }


    private void updateCurrentStatus(Status status) {
        Log.d(TAG, "updateCurrentStatus " + this.status + " --> " + status);
        this.status = status;
    }

    /**
     * 调用上报机器人状态接口，离开电梯成功
     */
    public void reportRobotLeaveElevatorComplete() {
        Log.d(TAG, "reportRobotLeaveElevatorComplete");
        updateCurrentStatus(Status.REPORT_LEAVE_ELEVATOR_COMPLETE);
        super.updateRobotElevatorStatus(Definition.RobotElevatorState.LEAVE_ELEVATOR_COMPLETE);//出电梯成功，上报
    }

    //  ----------------  导航  ----------------  //

    /**
     * 多机 多机状态解析
     */
    private void parseMultiRobotInfo() {

    }

    /**
     * 导航去电梯口
     */
    private void naviToElevatorGate() {
        updateCurrentStatus(Status.GO_GATE);
        NavigationAdvancedBean bean = cloneNaviBean();
        bean.setDestination(elevatorGatePoseName);
        bean.setDestinationRange(NavigationAdvancedBean.NAVIGATION_DEFAULT_DESTINATION_RANGE);//电梯口不需要停到精确位置
        bean.setAdjustAngle(true); //到目的地后，不回正到建图角度
        bean.setLinearSpeed(ELEVATOR_NAVI_LINEAR_SPEED);
        bean.setAngularSpeed(ELEVATOR_NAVI_ANGULAR_SPEED);
        bean.setPriority(29); //出电梯优先级最高
        startNavigation(bean);
    }

    /**
     * 导航重试
     */
    private void naviRetry() {
        naviFailCount++;

        //云端 重试次数过多上报
        if (naviFailCount > NAVI_FAIL_MAX_COUNT
                && status == Status.GO_GATE) {
            Log.d(TAG, "naviRetry fail too match time ,count: " + naviFailCount);
            onStatusUpdate(Definition.STATUS_UPDATE_RETRY_ERROR, Definition.STATUS_EXIT_ELEVATOR_MUCH_TIME);
        }

        //还在电梯范围内，且状态是去电梯中心，状态会流转给enter_elevator,不再重试
        if (status == Status.CONTINUE_GO_CENTER) {
            Log.d(TAG, "naviRetry current status is go center");
            return;
        }

        //不在电梯内了，可以关门
        if (null != controlUtil
                && !controlUtil.inElevatorRange()) {
            Log.d(TAG, "naviRetry current not in elevator,close door");
            stopControlElevator();  //navi retry，关门
        }

        //电梯口点没有机器人，必须到达"电梯口"才算成功
        naviToElevatorGate();
    }

    private void stopControlElevator() {
        if (null != controlUtil) {
//            controlUtil.releaseElevatorControl();//这里只是要关门，并不是要是释放电梯
            controlUtil.cmdCloseElevatorDoor();
            controlUtil.stopControlElevator();
        }
    }

    /**
     * 导航成功
     * 出电梯轿厢范围了
     */
    private void naviSuccess() {
        updateCurrentStatus(Status.ARRIVED);
        updateAction(Definition.ElevatorAction.ACTION_NAVI_SUC);
        onStatusUpdate(Definition.STATUS_EXIT_ELEVATOR_SUC, "exit elevator suc");
        onStatusUpdate(Definition.STATUS_TAKE_ELEVATOR_TO_TARGET_FLOOR_SUCCESS, "take elevator suc");
        reportRobotLeaveElevatorComplete();//机器人出电梯成功,直接上报不关注结果，这里出电梯成功直接进行下一步。
        onSuccess();
    }

    /**
     * 处理上报机器人离开电梯成功结果
     */
    private void handleRobotStatusUpdateResult(String result, String extraData) {
        Log.d(TAG, "handleRobotStatusUpdateResult " + result + " extraData:" + extraData + " status:" + status);
//        if (Definition.SUCCEED.equals(result)) {
//            onSuccess();
//        } else {
//            reportRobotLeaveElevatorComplete();//上报失败，重新上报
//        }
    }

    /**
     * 出梯失败，流转到进梯状态
     */
    private void backToCenter() {
        updateAction(Definition.ElevatorAction.ACTION_CONTINUE_TO_CENTER);//电梯内出梯失败，继续去电梯中心
        updateCurrentStatus(Status.CONTINUE_GO_CENTER);
        onStatusUpdate(Definition.STATUS_BACK_TO_ELEVATOR_CENTER, "back to elevator center");
        onSuccess();
    }

    //  ----------------  梯控  ----------------  //

    private void continueOpenDoor() {
        if (null != controlUtil) {
            controlUtil.startOpenDoor();
        }
    }

    /**
     * 控制电梯开门时状态监听
     */
    private final ElevatorControlUtilListener elevatorControlUtilListener = new ElevatorControlUtilListener() {
        @Override
        public void onElevatorStatusUpdate(int id, String param, String extraData) {

        }

        @Override
        public void onElevatorResult(int id, String param) {
            onElevatorCmdResult(id, param);
        }

        @Override
        public void onSuccess() {

        }
    };

    private void onElevatorCmdResult(int id, String param) {
        Log.d(TAG, "onElevatorCmdResult status:" + id + " , data:" + param);
        switch (id) {
            case Definition.ERROR_ELEVATOR_CONTROL:
            case Definition.ERROR_CALL_ELEVATOR_FAILED:
            case Definition.ERROR_CLOSE_ELEVATOR_DOOR_FAILED:
            case Definition.ERROR_OPEN_ELEVATOR_DOOR_FAILED:
                stopNavigation();   //停止导航
                openDoorFail(controlUtil.inElevatorRange());//开电梯门失败
                break;
            default:
                onResult(id, param);
                break;
        }
    }

    /**
     * 控制开门失败或导航超时
     *
     * @param isInElevator 是否在电梯范围内
     */
    private void openDoorFail(boolean isInElevator) {
        //在电梯范围内出现异常了，回到电梯中心
        if (isInElevator) {
            backToCenter();
        } else {
            //不在电梯范围,电梯出现异常了，直接导航去目的地
            naviSuccess();
        }
    }

}
