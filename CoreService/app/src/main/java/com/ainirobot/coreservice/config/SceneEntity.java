package com.ainirobot.coreservice.config;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

public class SceneEntity implements Parcelable {
    private List<String> scenes;

    public List<String> getScenes() {
        return scenes;
    }

    public void setScenes(List<String> scenes) {
        this.scenes = scenes;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeStringList(this.scenes);
    }

    public SceneEntity() {
    }

    protected SceneEntity(Parcel in) {
        this.scenes = in.createStringArrayList();
    }

    public static final Creator<SceneEntity> CREATOR = new Creator<SceneEntity>() {
        @Override
        public SceneEntity createFromParcel(Parcel source) {
            return new SceneEntity(source);
        }

        @Override
        public SceneEntity[] newArray(int size) {
            return new SceneEntity[size];
        }
    };

    @Override
    public String toString() {
        return "SceneEntity{" +
                "scenes=" + scenes +
                '}';
    }
}
