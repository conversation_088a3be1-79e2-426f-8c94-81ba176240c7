package com.ainirobot.coreservice.config;

import android.content.Context;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbManager;
import android.util.Pair;

import java.util.ArrayList;
import java.util.List;

public class UVCCamera {

    private Context context;

    //包含CX_6369_0c45、HP_6369_0c45，为同一设备
    //数据为<VID, PID>
    private final Pair<Integer, Integer> topIr = new Pair<>(0x0c45, 0x6369);
    private final Pair<Integer, Integer> topMono = new Pair<>(0x636c, 0x636d);
    private final Pair<Integer, Integer> backCamera = new Pair<>(0x0c45, 0x6340);
    private final List<Pair<Integer, Integer>> frontCamera = new ArrayList<Pair<Integer, Integer>>() {
        {
            add(new Pair<>(0x0c45, 0x6341));
            add(new Pair<>(0x0edc, 0x2076));
        }
    };

    public UVCCamera(Context context) {
        this.context = context;
    }

    public boolean hasTopIr() {
        return hasDevice(context, topIr);
    }

    public boolean hasTopMono() {
        return hasDevice(context, topMono);
    }

    public boolean hasBackCamera() {
        return hasDevice(context, backCamera);
    }

    public boolean hasFrontCamera() {
        for (Pair<Integer, Integer> ids : frontCamera) {
            if (hasDevice(context, ids)) {
                return true;
            }
        }
        return false;
    }

    private boolean hasDevice(Context context, Pair<Integer, Integer> ids) {
        UsbManager usbManager = (UsbManager) context.getSystemService(Context.USB_SERVICE);
        for (UsbDevice device : usbManager.getDeviceList().values()) {
            if (device.getVendorId() == ids.first
                    && device.getProductId() == ids.second) {
                return true;
            }
        }
        return false;
    }
}
