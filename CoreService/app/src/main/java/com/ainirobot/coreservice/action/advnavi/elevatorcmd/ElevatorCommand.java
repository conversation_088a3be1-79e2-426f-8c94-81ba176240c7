package com.ainirobot.coreservice.action.advnavi.elevatorcmd;


import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.advnavi.util.NaviAdvGlobalData;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.utils.DelayTask;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Data: 2023/3/29 15:07
 * Author: wanglijing
 * Description: ElevatorCommand
 */
public abstract class ElevatorCommand {
    protected String TAG = ElevatorCommand.class.getSimpleName();
    protected ElevatorCommandListener listener;
    protected ElevatorCommandInterface commandInterface;
    /**
     * 开始时的命令类型
     */
    protected CmdType intentType;
    /**
     * 正在发送的命令类型
     * <p>
     * 多个命令嵌套时，当前正在执行的命令类型
     */
    private CmdType cmdType;
    protected volatile boolean isAlive = false;
    /**
     * 重试次数
     */
    protected int retryCount = 0;
    /**
     * 失败次数(也是MAX_RETRY_COUNT)
     */
    protected int failCount = 0;
    /**
     * 重试最大次数，默认5次
     * MapTool多层管理可配
     */
    protected int MAX_RETRY_COUNT = NaviAdvGlobalData.getInstance().elevatorCmdRetryCount();

    public enum CmdType {
        OPEN_DOOR,
        CLOSE_DOOR,
        CALL_FLOOR,
        RELEASE_ELEVATOR,
    }

    ElevatorCommand(String name,
                    ElevatorCommand.CmdType intentType,
                    ElevatorCommandInterface commandInterface,
                    ElevatorCommandListener listener) {

        updateFailCount(0);
        updateRetryCount(0);
        this.listener = listener;
        this.intentType = intentType;
        this.commandInterface = commandInterface;
        if (TextUtils.isEmpty(name)) {
            TAG = TAG + "-" + getClass().getSimpleName();
        } else {
            TAG = name + "-" + getClass().getSimpleName();
        }
    }

    public void start() {
        Log.d(TAG, "start");
        isAlive = true;
    }

    public void stop() {
        Log.d(TAG, "stop");
        isAlive = false;
        DelayTask.cancel(TAG);
        updateFailCount(0);
    }

    public void onSuccess() {
        updateFailCount(0);
        updateRetryCount(0);
    }

    public void onFail() {
    }

    protected abstract void onRetry();

    public void cmdResponse(String command, String result, String extraData) {
        if (!isAlive) {
            Log.d(TAG, "cmdResponse " + cmdType + " not alive");
            return;
        }
        //上报给当前正在运行的命令
        if (getCommandType(command) == cmdType) {
            Log.d(TAG, cmdType + " response [ " + result + " ] extraData:" + extraData);
            defaultCmdResponse(result);
        }
    }

    private CmdType getCommandType(String command) {
        switch (command) {
            case Definition.CMD_CONTROL_CALL_ELEVATOR:
                return CmdType.CALL_FLOOR;
            case Definition.CMD_CONTROL_OPEN_ELEVATOR_DOOR:
                return CmdType.OPEN_DOOR;
            case Definition.CMD_CONTROL_CLOSE_ELEVATOR_DOOR:
                return CmdType.CLOSE_DOOR;
            case Definition.CMD_CONTROL_RELEASE_ELEVATOR:
                return CmdType.RELEASE_ELEVATOR;
        }
        return null;
    }

    private void defaultCmdResponse(String result) {
        switch (result) {
            case Definition.SUCCEED:
                onSuccess();
                break;
            case Definition.FAILED:
                onFail();
                break;
            case Definition.PARAMS_TIMEOUT:
                retryCall();
                break;
        }
    }

    public void initCmdType(CmdType cmdType) {
        Log.d(TAG, "initCmdType: " + cmdType);
        this.cmdType = cmdType;

    }

    public void openElevatorDoor() {
        if (!isEnable()) {
            return;
        }
        commandInterface.cmdOpenElevatorDoor();
    }

    public void closeElevatorDoor() {
        if (!isEnable()) {
            return;
        }
        commandInterface.cmdCloseElevatorDoor();
    }

    public void callElevatorToFloor() {
        if (!isEnable()) {
            return;
        }
        commandInterface.cmdCallElevator();
    }

    public void releaseElevatorControl() {
        if (!isEnable()) {
            return;
        }
        commandInterface.cmdReleaseElevator();//已废弃
    }

    public int getFloorIndex() {
        if (null != commandInterface) {
            return commandInterface.getTargetFloorIndex();
        }
        return -1;
    }

    public void onResult(int status, String data) {
        Log.d(TAG, "onResult status:" + status + " ,data:" + data + " cmdType:" + cmdType);
        if (null != listener) {
            try {
                JSONObject object = new JSONObject();
                object.put(Definition.JSON_CMD_TYPE, cmdType.toString());
                object.put(Definition.JSON_VALUE, data);
                listener.onFinish(status, object.toString());
                this.stop();
                return;
            } catch (JSONException e) {
                e.printStackTrace();
            }
            listener.onFinish(status, data);
            this.stop();
        }
    }


    public void onCmdStatusUpdate(int status, String data, String extraData) {
        if (null != listener) {
            listener.onStatusUpdate(status, data, extraData);
        }
    }

    public boolean isEnable() {
        if (!isAlive) {
            Log.e(TAG, "not alive");
            return false;
        }
        if (null == commandInterface) {
            Log.e(TAG, "commandInterface is null");
            onResult(Definition.ERROR_ELEVATOR_CONTROL, "command interface is null");
            return false;
        }
        return true;
    }

    /**
     * 超时时默认重试
     */
//    public void onTimeout() {
//        Log.d(TAG, "onTimeout retry");
//        retryCall();
//    }

    public void retryCall() {
        Log.d(TAG, "retryCall:" + cmdType + " retryCount:" + retryCount + " MAX_RETRY_COUNT:" + MAX_RETRY_COUNT);
        if (retryCount > MAX_RETRY_COUNT) {
            retryTooMuchTime();
            return;
        }
        retryCount++;
        updateRetryCount(retryCount);
        onRetry();
    }

    public void updateRetryCount(int count) {
        retryCount = count;
    }

    public void updateFailCount(int count) {
        failCount = count;
        Log.d(TAG, "updateFailCount: " + failCount + " MAX_RETRY_COUNT:" + MAX_RETRY_COUNT);
        if (failCount > MAX_RETRY_COUNT) {
            Log.e(TAG, " fail too much time");
            onResult(Definition.ERROR_ELEVATOR_CONTROL, "fail too much time");
        }
    }

    public CmdType getCmdType() {
        return cmdType;
    }

    private void retryTooMuchTime() {
        Log.e(TAG, cmdType + " retry too much time");
        switch (cmdType) {
            case OPEN_DOOR:
                onResult(Definition.ERROR_OPEN_ELEVATOR_DOOR_FAILED, "open door retry too much time");
                break;
            case CLOSE_DOOR:
                onResult(Definition.ERROR_CLOSE_ELEVATOR_DOOR_FAILED, "close door retry too much time");
                break;
            case CALL_FLOOR:
                onResult(Definition.ERROR_CALL_ELEVATOR_FAILED, "call floor retry too much time");
                break;
            case RELEASE_ELEVATOR:
                onResult(Definition.ERROR_ELEVATOR_CONTROL, "release elevator retry too much time");
                break;
        }
    }
}
