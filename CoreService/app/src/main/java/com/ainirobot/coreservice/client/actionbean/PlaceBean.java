/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.client.actionbean;

import com.ainirobot.coreservice.client.Definition;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import org.json.JSONException;
import org.json.JSONObject;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Locale;
import java.util.Map;

public class PlaceBean {
    public static final int PLACE_TYPE_ORDINARY = 0;
    public static final int PLACE_TYPE_RECEPTION = 1;
    public static final int PLACE_TYPE_TOILET = 2;
    public static final int PLACE_TYPE_LOUNGE = 3;
    public static final int PLACE_TYPE_FINANCE = 4;
    public static final int PLACE_TYPE_MEETING = 5;
    public static final int PLACE_TYPE_CHARGING_PILE = 6;
    public static final int PLACE_TYPE_CHARGING_POINT = 7;

    public static final int PLACE_STATUS_NORMAL = 0;
    public static final int PLACE_STATUS_DANGER = 1;
    public static final int PLACE_STATUS_OBSTACLE = 2;
    public static final int PLACE_STATUS_OUTSIDE = 3;

    private String placeId = "";
    private String iconUrl = "";
    private int placeType;
    private int placeStatus;
    private float pointTheta;
    private float pointX;
    private float pointY;
    /**
     * @deprecated
     * 旧版本的时间戳，很早就不用了，
     * 地图包place.json和数据库中的place表中的time字段都是格式化的字符串，不是时间戳，数据解析时会抛出类型转换异常。
     */
    private long updateTime;
    /**
     * 新版本的时间，格式为 yyyy-MM-dd HH:mm:ss
     */
    private String updateTimeNew = "";
    private String mapName = "";
    private String alias = "";
    /**
     * 特殊点位类型
     */
    private int typeId;
    /**
     * 特殊点位优先级
     */
    private int priority;

    private Map<String, String> placeNameList;

    private String createTime="";
    private String mapId;
    private int syncState = 0;
    private boolean ignoreDistance = false;

    private boolean noDirectionalParking = false;

    private int safeDistance = Definition.POSE_SAFE_DISTANCE_DEFAULT;

    public PlaceBean() {
    }

    public String getPlaceId() {
        return placeId;
    }

    public void setPlaceId(String placeId) {
        this.placeId = placeId;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public int getPlaceType() {
        return placeType;
    }

    public void setPlaceType(int placeType) {
        this.placeType = placeType;
    }

    public int getPlaceStatus() {
        return placeStatus;
    }

    public void setPlaceStatus(int placeStatus) {
        this.placeStatus = placeStatus;
    }

    public float getPointTheta() {
        return pointTheta;
    }

    public void setPointTheta(float pointTheta) {
        this.pointTheta = pointTheta;
    }

    public float getPointX() {
        return pointX;
    }

    public void setPointX(float pointX) {
        this.pointX = pointX;
    }

    public float getPointY() {
        return pointY;
    }

    public void setPointY(float pointY) {
        this.pointY = pointY;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }

    public String getMapName() {
        return mapName;
    }

    public void setMapName(String mapName) {
        this.mapName = mapName;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public void setTypeId(int typeId) {
        this.typeId = typeId;
    }

    public int getTypeId() {
        return typeId;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public int getPriority() {
        return priority;
    }

    public Map<String, String> getPlaceNameList() {
        return placeNameList;
    }

    public String getPlaceName(String languageType) {
        if (null == placeNameList) {
            return "";
        }
        return placeNameList.get(languageType);
    }

    public String getPlaceName() {
        if (null == placeNameList) {
            return "";
        }
        return placeNameList.get(Locale.getDefault().getLanguage() + "_" + Locale.getDefault().getCountry());
    }

    public void addPlaceName(String languageType, String name) {
        if (null == placeNameList) {
            placeNameList = new HashMap<>();
        }
        placeNameList.put(languageType, name);
    }

    public void removePlaceName(String languageType) {
        if (placeNameList != null) {
            placeNameList.remove(languageType);
        }
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getMapId() {
        return mapId;
    }

    public void setMapId(String mapId) {
        this.mapId = mapId;
    }

    public int getSyncState() {
        return syncState;
    }

    public void setSyncState(int syncState) {
        this.syncState = syncState;
    }

    public boolean getIgnoreDistance() {
        return ignoreDistance;
    }

    public void setIgnoreDistance(boolean ignoreDistance) {
        this.ignoreDistance = ignoreDistance;
    }

    public boolean getNoDirectionalParking() {
        return noDirectionalParking;
    }

    public void setNoDirectionalParking(boolean noDirectionalParking) {
        this.noDirectionalParking = noDirectionalParking;
    }

    public int getSafeDistance() {
        return safeDistance;
    }

    public void setSafeDistance(int safeDistance) {
        this.safeDistance = safeDistance;
    }

    public String getUpdateTimeNew() {
        return updateTimeNew;
    }

    public void setUpdateTimeNew(String updateTimeNew) {
        this.updateTimeNew = updateTimeNew;
    }

    public static long parseDateTimeToTimestamp(String dateTimeString) {
        if (dateTimeString == null || dateTimeString.trim().isEmpty()) {
            return 0;
        }
        SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date dateTime = dateTimeFormat.parse(dateTimeString);
            return dateTime.getTime();
        } catch (ParseException e) {
            e.printStackTrace();
            return 0;
        }
    }

    public PlaceBean(JSONObject json, String mapName) {
        placeId = json.optString("id");
        iconUrl = json.optString("icon_url");
        placeType = json.optInt("type");
        placeStatus = json.optInt("status");
        pointTheta = (float)json.optDouble("theta");
        pointX = (float)json.optDouble("x");
        pointY = (float)json.optDouble("y");
        if (json.has("time")) {
            updateTimeNew = json.optString("time");
        } else if (json.has("updateTime")) {
            updateTimeNew = json.optString("updateTime");
        }
        updateTime = parseDateTimeToTimestamp(updateTimeNew);
        alias = json.optString("alaias");
        if (json.has("ignoreDistance")) {
            ignoreDistance = json.optBoolean("ignoreDistance",false);
        }
        if (json.has("noDirectionalParking")) {
            noDirectionalParking = json.optBoolean("noDirectionalParking",false);
        }
        if (json.has("safeDistance")) {
            safeDistance = json.optInt("safeDistance",Definition.POSE_SAFE_DISTANCE_DEFAULT);
        }
        if (json.has("typeId")) {
            typeId = json.optInt("typeId", 0);
        }
        if (json.has("priority")) {
            priority = json.optInt("priority", 0);
        }
        this.mapName = mapName;
        JSONObject name = json.optJSONObject("name");
        if (name != null) {
            Iterator<String> it = name.keys();
            while (it.hasNext()) {
                String key = it.next();
                addPlaceName(key, name.optString(key));
            }
        }
    }

    public PlaceBean(JsonObject json, String mapName) {
        placeId = json.get("id").getAsString();
        iconUrl = json.get("icon_url").getAsString();
        placeType = json.get("type").getAsInt();
        placeStatus = json.get("status").getAsInt();
        pointTheta = json.get("theta").getAsFloat();
        pointX = json.get("x").getAsFloat();
        pointY = json.get("y").getAsFloat();
        if (json.has("time")) {
            updateTimeNew = json.get("time").getAsString();
        } else if (json.has("updateTime")) {
            updateTimeNew = json.get("updateTime").getAsString();
        }
        updateTime = parseDateTimeToTimestamp(updateTimeNew);
        alias = json.get("alaias").getAsJsonObject().toString();
        if (json.has("ignoreDistance")) {
            ignoreDistance = json.get("ignoreDistance").getAsBoolean();
        }
        if (json.has("noDirectionalParking")) {
            noDirectionalParking = json.get("noDirectionalParking").getAsBoolean();
        }
        if (json.has("safeDistance")) {
            safeDistance = json.get("safeDistance").getAsInt();
        }
        if (json.has("typeId")) {
            typeId = json.get("typeId").getAsInt();
        }
        if (json.has("priority")) {
            priority = json.get("priority").getAsInt();
        }
        this.mapName = mapName;
        JsonObject name = json.get("name").getAsJsonObject();
        if (name != null) {
            for (Map.Entry<String, JsonElement> item : name.entrySet()) {
                addPlaceName(item.getKey(), item.getValue().getAsString());
            }
        }
    }


    public JSONObject getJson() {
        JSONObject place = new JSONObject();
        try {
            place.put("id", placeId);
            place.put("icon_url", iconUrl);
            place.put("type", placeType);
            place.put("status", placeStatus);
            place.put("theta", pointTheta);
            place.put("x", pointX);
            place.put("y", pointY);
            place.put("time", updateTime);
            JSONObject name = new JSONObject();
            if (null != placeNameList) {
                for (Map.Entry<String, String> stringStringEntry : placeNameList.entrySet()) {
                    name.put(stringStringEntry.getKey(), stringStringEntry.getValue());
                }
            }
            place.put("name", name);
            place.put("alaias", new JSONObject(alias));
            place.put("ignoreDistance", ignoreDistance);
            place.put("noDirectionalParking", noDirectionalParking);
            place.put("safeDistance", safeDistance);
            place.put("typeId", typeId);
            place.put("priority", priority);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return place;
    }

    @Override
    public String toString() {
        return "PlaceBean{" +
                "placeId='" + placeId + '\'' +
                ", iconUrl='" + iconUrl + '\'' +
                ", placeType=" + placeType +
                ", placeStatus=" + placeStatus +
                ", pointTheta=" + pointTheta +
                ", pointX=" + pointX +
                ", pointY=" + pointY +
                ", updateTime=" + updateTime +
                ", updateTimeNew='" + updateTimeNew + '\'' +
                ", mapName='" + mapName + '\'' +
                ", alias='" + alias + '\'' +
                ", typeId=" + typeId + '\'' +
                ", priority=" + priority + '\'' +
                ", placeNameList=" + placeNameList +
                ", createTime='" + createTime + '\'' +
                ", mapId='" + mapId + '\'' +
                ", syncState=" + syncState +
                ", ignoreDistance=" + ignoreDistance +
                ", noDirectionalParking=" + noDirectionalParking +
                ", safeDistance=" + safeDistance +
                '}';
    }
}