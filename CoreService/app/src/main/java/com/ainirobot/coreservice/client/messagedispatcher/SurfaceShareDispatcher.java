/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.client.messagedispatcher;

import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.client.surfaceshare.SurfaceShareListener;
import com.ainirobot.coreservice.listener.ISurfaceShareListener;

public class SurfaceShareDispatcher extends ISurfaceShareListener.Stub implements IRecyclable {
    private static final String TAG = SurfaceShareDispatcher.class.getSimpleName();
    private static RecyclablePool<SurfaceShareDispatcher> sPool = new RecyclablePool<>();

    private DispatchHandler mDispatchHandler;
    private SurfaceShareListener mListener;

    private SurfaceShareDispatcher(DispatchHandler dispatchHandler, SurfaceShareListener listener) {
        mDispatchHandler = dispatchHandler;
        mListener = listener;
    }

    static SurfaceShareDispatcher obtain(DispatchHandler dispatchHandler, SurfaceShareListener listener) {
        SurfaceShareDispatcher dispatcher = sPool.obtain();

        if (dispatcher == null) {
            dispatcher = new SurfaceShareDispatcher(dispatchHandler, listener);
        } else {
            dispatcher.mDispatchHandler = dispatchHandler;
            dispatcher.mListener = listener;
        }
        return dispatcher;
    }

    @Override
    public void onError(int errorCode, String errorString) throws RemoteException {
        DispatchHandler dispatchHandler = mDispatchHandler;
        SurfaceShareListener listener = mListener;
        if (dispatchHandler != null && listener != null) {
            SurfaceShareMessage actionMessage = SurfaceShareMessage.obtain(listener
                    , SurfaceShareMessage.MsgType.error, errorString, errorCode);
            dispatchHandler.sendMessage(dispatchHandler.obtainMessage(MessageDispatcher.MSG_SURFACE_SHARE
                    , actionMessage));
            sPool.recycle(this);
        } else {
            Log.e(TAG, "onError status = " + errorCode + "; data = " + errorString);
        }
    }

    @Override
    public void onStatusUpdate(int status, String data) throws RemoteException {
        DispatchHandler dispatchHandler = mDispatchHandler;
        SurfaceShareListener listener = mListener;
        if (dispatchHandler != null && listener != null) {
            SurfaceShareMessage actionMessage = SurfaceShareMessage.obtain(listener
                    , SurfaceShareMessage.MsgType.status, data, status);
            dispatchHandler.sendMessage(dispatchHandler.obtainMessage(MessageDispatcher.MSG_SURFACE_SHARE
                    , actionMessage));
        } else {
            Log.e(TAG, "onStatusUpdate status = " + status + "; data = " + data);
        }
    }

    @Override
    public void recycle() {
        mDispatchHandler = null;
        mListener = null;
    }
}
