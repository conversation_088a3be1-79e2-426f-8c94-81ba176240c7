/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.ainirobot.coreservice.client.messagedispatcher;

import android.content.Intent;

import com.ainirobot.coreservice.client.permission.PermissionListener;
import com.ainirobot.coreservice.listener.IPermissionListener;

public class PermissionDispatcher extends IPermissionListener.Stub {

    private DispatchHandler mDispatchHandler;
    private PermissionListener mListener;
    private static PermissionDispatcher permissionDispatcher;

    private PermissionDispatcher(DispatchHand<PERSON> dispatchHandler, PermissionListener listener) {
        mDispatchHandler = dispatchHandler;
        mListener = listener;
    }

    private void changeListener(PermissionListener listener) {
        mListener = listener;
    }

    static synchronized PermissionDispatcher obtain(DispatchHandler dispatchHandler, PermissionListener listener) {
        if (permissionDispatcher == null) {
            permissionDispatcher = new PermissionDispatcher(dispatchHandler, listener);
        } else {
            permissionDispatcher.changeListener(listener);
        }
        return permissionDispatcher;
    }

    @Override
    public void activityStarting(Intent intent, String pkg) {
        PermissionListener listener = mListener;
        DispatchHandler handler = mDispatchHandler;
        if (listener != null && handler != null) {
            PermissionMessage permissionMessage = new PermissionMessage(PermissionMessage
                    .TYPE_ACTIVITY_STARTING, intent, pkg, listener);
            handler.sendMessage(handler.obtainMessage(MessageDispatcher.MSG_PERMISSION,
                    permissionMessage));
        }
    }

    @Override
    public void activityResuming(String pkg) {
        PermissionListener listener = mListener;
        DispatchHandler handler = mDispatchHandler;
        if (listener != null && handler != null) {
            PermissionMessage permissionMessage = new PermissionMessage(PermissionMessage
                    .TYPE_ACTIVITY_RESUMING, pkg, null, listener);
            handler.sendMessage(handler.obtainMessage(MessageDispatcher.MSG_PERMISSION, permissionMessage));
        }
    }

    @Override
    public void appCrashed(String processName, int pid, String shortMsg, String longMsg,
                           long timeMillis, String stackTrace) {
        PermissionListener listener = mListener;
        DispatchHandler handler = mDispatchHandler;
        if (listener != null && handler != null) {
            PermissionMessage permissionMessage = new PermissionMessage(PermissionMessage
                    .TYPE_APP_CRASHED, processName, pid, shortMsg, longMsg,
                    timeMillis, stackTrace, listener);
            handler.sendMessage(handler.obtainMessage(MessageDispatcher.MSG_PERMISSION, permissionMessage));
        }
    }

    @Override
    public void appEarlyNotResponding(String processName, int pid, String annotation) {
        PermissionListener listener = mListener;
        DispatchHandler handler = mDispatchHandler;
        if (listener != null && handler != null) {
            PermissionMessage permissionMessage = new PermissionMessage(PermissionMessage
                    .TYPE_APP_EARLY_NOT_RESPONDING, processName, pid, annotation, listener);
            handler.sendMessage(handler.obtainMessage(MessageDispatcher.MSG_PERMISSION, permissionMessage));
        }
    }

    @Override
    public void appNotResponding(String processName, int pid, String processStats) {
        PermissionListener listener = mListener;
        DispatchHandler handler = mDispatchHandler;
        if (listener != null && handler != null) {
            PermissionMessage permissionMessage = new PermissionMessage(PermissionMessage
                    .TYPE_APP_NOT_RESPONDING, processName, pid, processStats, listener);
            handler.sendMessage(handler.obtainMessage(MessageDispatcher.MSG_PERMISSION, permissionMessage));
        }
    }

    @Override
    public void systemNotResponding(String msg) {
        PermissionListener listener = mListener;
        DispatchHandler handler = mDispatchHandler;
        if (listener != null && handler != null) {
            PermissionMessage permissionMessage = new PermissionMessage(PermissionMessage
                    .TYPE_SYSTEM_NOT_RESPONDING, msg, listener);
            handler.sendMessage(handler.obtainMessage(MessageDispatcher.MSG_PERMISSION, permissionMessage));
        }
    }

    @Override
    public void onForegroundActivitiesChanged(int pid, int uid, boolean foregroundActivities) {
        PermissionListener listener = mListener;
        DispatchHandler handler = mDispatchHandler;
        if (listener != null && handler != null) {
            PermissionMessage permissionMessage = new PermissionMessage(PermissionMessage
                    .TYPE_FOREGROUND_ACTIVITIES_CHANGED, pid, uid, foregroundActivities, listener);
            handler.sendMessage(handler.obtainMessage(MessageDispatcher.MSG_PERMISSION, permissionMessage));
        }
    }

    @Override
    public void onProcessDied(int pid, int uid) {
        PermissionListener listener = mListener;
        DispatchHandler handler = mDispatchHandler;
        if (listener != null && handler != null) {
            PermissionMessage permissionMessage = new PermissionMessage(PermissionMessage
                    .TYPE_PROCESS_DIED, pid, uid, listener);
            handler.sendMessage(handler.obtainMessage(MessageDispatcher.MSG_PERMISSION, permissionMessage));
        }
    }
}
