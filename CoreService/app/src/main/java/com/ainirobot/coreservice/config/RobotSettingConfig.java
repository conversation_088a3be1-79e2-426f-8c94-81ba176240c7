/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.config;

import com.google.gson.JsonObject;
import com.google.gson.annotations.SerializedName;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

public class RobotSettingConfig {

    @SerializedName("项目名称")
    private String name;

    @SerializedName("版本")
    private String version;

    @SerializedName("设置")
    private JsonObject setting;

    private Map<String, String> settingMap = new ConcurrentHashMap<>();

    public int getVersion() {
        return Integer.valueOf(version);
    }

    public void parseSetting() {
        if (setting != null) {
            Set<String> keys = setting.keySet();
            for (String key : keys) {
                String value = setting.get(key).getAsString();
                settingMap.put(key, value);
            }
        }
    }

    public Set<String> getSettingKeys() {
        return settingMap.keySet();
    }

    public String getValue(String key) {
        return settingMap.get(key);
    }
}
