/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.server;

import android.os.Binder;
import android.os.Bundle;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.IModuleCallback;
import com.ainirobot.coreservice.IModuleRegistry;
import com.ainirobot.coreservice.IStatusListener;
import com.ainirobot.coreservice.bean.Task;
import com.ainirobot.coreservice.bean.TaskEvent;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.config.ConfigManager;
import com.ainirobot.coreservice.core.module.Module;
import com.ainirobot.coreservice.core.system.TaskManager;
import com.ainirobot.coreservice.exception.BinderDeathRecipient;
import com.ainirobot.coreservice.listener.IActionListener;
import com.ainirobot.coreservice.utils.FileUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

public class ModuleServer extends IModuleRegistry.Stub {
    private final String TAG;

    private Module mModule;

    public ModuleServer(Module module) {
        this.mModule = module;
        this.TAG = " ModuleServer[" + module.getPackageName() + "]";
    }

    @Override
    public void setCallback(IModuleCallback cb) throws RemoteException {
        Log.d(TAG, "Set app call back : " + this.mModule.getPackageName());
        this.mModule.setCallback(cb);
        if (cb != null) {
            cb.asBinder().linkToDeath(new BinderDeathRecipient(mModule.getPackageName()), 0);
        }
    }

    @Override
    public int startAction(int reqId, int actionId, String params, IActionListener listener)
            throws RemoteException {
        Log.d(TAG, "Start action : " + actionId);
        long token = Binder.clearCallingIdentity();
        try {
            //添加语言参数
            JSONObject json = new JSONObject(params);
            json.put("language", mModule.getLanguage());
            params = json.toString();
        } catch (JSONException e) {
            e.printStackTrace();
        }
        int result = mModule.getClient().startAction(reqId, actionId, params, listener, mModule.isSystem());
        Binder.restoreCallingIdentity(token);
        return result;
    }

    @Override
    public int stopAction(int reqId, int actionId, boolean isResetHW) throws RemoteException {
        Log.d(TAG, "Stop action : " + actionId);
        long token = Binder.clearCallingIdentity();
        if (actionId == Definition.ACTION_STOP_ALL_ACTIONS) {
            if (mModule.isSystem()) {
                return mModule.getClient().stopAllAction();
            } else {
                return Definition.ACTION_RESPONSE_PERMISSION_DENIED;
            }
        }
        int result = mModule.getClient().stopAction(reqId, actionId, isResetHW);
        Binder.restoreCallingIdentity(token);
        return result;
    }

    @Override
    public int stopActionCancelCmd(int reqId, int actionId, boolean isResetHW, boolean isCancelStopCommand) throws RemoteException {
        Log.d(TAG, "Stop action : " + actionId);
        long token = Binder.clearCallingIdentity();
        if (actionId == Definition.ACTION_STOP_ALL_ACTIONS) {
            if (mModule.isSystem()) {
                return mModule.getClient().stopAllAction();
            } else {
                return Definition.ACTION_RESPONSE_PERMISSION_DENIED;
            }
        }
        int result = mModule.getClient().stopActionCancelCmd(reqId, actionId, isResetHW, isCancelStopCommand);
        Binder.restoreCallingIdentity(token);
        return result;
    }

    @Override
    public int setLambColor(int reqId, int target, int color) throws RemoteException {
        if (!mModule.isAllowModifyLight()) {
            Log.d(TAG, "Set lame color permission denied : current is system light");
            return Definition.CMD_SEND_ERROR_PERMISSION_DENIED;
        }
        return mModule.getClient().setLampColor(reqId, target, color);
    }

    @Override
    public void setDefaultHeadAngle(int hAngle, int vAngle) throws RemoteException {
        mModule.setDefaultHeadAngle(hAngle, vAngle);
    }

    @Override
    public int setLambAnimation(int reqId, int target, int start, int end,
                                int startTime, int endTime, int repeat, int onTime, int freeze)
            throws RemoteException {
        if (!mModule.isAllowModifyLight()) {
            Log.d(TAG, "Set lame animation permission denied : current is system light");
            return Definition.CMD_SEND_ERROR_PERMISSION_DENIED;
        }
        return mModule.getClient().setLampAnimation(reqId, target, start, end,
                startTime, endTime, repeat, onTime, freeze);
    }

    @Override
    public boolean finishModuleParser(int reqId, boolean result) throws RemoteException {
        return finishModuleWithResponse(reqId, result, null);
    }

    @Override
    public boolean isActive() throws RemoteException {
        return !mModule.isSuspend();
    }

    @Override
    public boolean finishModuleWithResponse(int reqId, boolean result, String response)
            throws RemoteException {
        Log.d(TAG, "Finish request : " + reqId + " result : " + result);
        return mModule.getClient().finishModuleParser(reqId, result, response);
    }

    @Deprecated
    @Override
    public int getHWStatus(int function) throws RemoteException {
        return Definition.HW_STATUS_IDLE;
    }

    @Override
    public String registerStatusListener(String type, IStatusListener listener)
            throws RemoteException {
        return mModule.getClient().registerStatusListener(type, listener);
    }

    @Override
    public boolean unregisterStatusListener(String id) throws RemoteException {
        return mModule.getClient().unregisterStatusListener(id);
    }

    @Override
    public boolean updateSystemStatus(String status, String data) throws RemoteException {
        return mModule.getClient().updateSystemStatus(status, data);
    }

    @Override
    public boolean startStatusSocket(String type, int socketPort) throws RemoteException {
        Log.d(TAG, "Start status socket");
        return mModule.getClient().startStatusSocket(type, socketPort);
    }

    @Override
    public boolean closeStatusSocket(String type, int socketPort) throws RemoteException {
        return mModule.getClient().closeStatusSocket(type, socketPort);
    }

    @Override
    public void sendStatusReport(String type, String data)
            throws RemoteException {
        mModule.getClient().sendStatusReport(type, data);
    }

    @Override
    public void disableEmergency() throws RemoteException {
        Log.d(TAG, "Disable emergency");
        mModule.setSystemStatusEnabled(Definition.SYSTEM_EMERGENCY, false);
    }

    @Override
    public void enableEmergency() throws RemoteException {
        Log.d(TAG, "enable emergency");
        mModule.setSystemStatusEnabled(Definition.SYSTEM_EMERGENCY, true);
    }

    @Override
    public void disableBattery() {
        Log.d(TAG, "Disable battery");
        mModule.setSystemStatusEnabled(Definition.SYSTEM_BATTERY, false);
    }

    @Override
    public void enableBattery() {
        Log.d(TAG, "enable battery : ");
        mModule.setSystemStatusEnabled(Definition.SYSTEM_BATTERY, true);
        //当前正在充电且充电状态可用，重新更新一次Battery状态
        mModule.updateBatteryStatus();
    }

    @Override
    public String reportTask(Task task) throws RemoteException {
        return TaskManager.getInstance().addTask(task);
    }

    @Override
    public void reportTaskEvent(TaskEvent taskEvent) throws RemoteException {
        TaskManager.getInstance().addTakEvent(taskEvent);
    }

    @Override
    public List<Task> getCurrentTask() throws RemoteException {
        return TaskManager.getInstance().getCurrentTask();
    }

    @Override
    public void resetSystemStatus() {
        Log.d(TAG, "Reset system status");
        mModule.resetSystemStatus();
    }

    @Override
    public int setLight(int reqId, String params)
            throws RemoteException {
        if (!mModule.isAllowModifyLight()) {
            Log.d(TAG, "Set lame animation permission denied : current is system light");
            return Definition.CMD_SEND_ERROR_PERMISSION_DENIED;
        }
        return mModule.getClient().setLight(reqId, params);
    }

    @Override
    public boolean installPatch(Bundle bundle)
            throws RemoteException {
        Log.d(TAG, "installPatch");
        return mModule.getClient().installPatch(bundle);
    }

    @Override
    public String getRobotInfo(int reqType, String params) {
        Log.d(TAG, "getRobotInfo reqType:" + reqType + " params=" + params);
        return mModule.getCore().getRobotInfoManager().getRobotInfo(reqType, params);
    }

    @Override
    public void getRobotStatus(String type, IStatusListener listener) {
        mModule.getCore().getStatusManager().getRobotStatus(type, listener);
    }

    @Override
    public boolean switchScreen(boolean onOff) throws RemoteException {
        return mModule.getClient().switchScreen(onOff);
    }

    public boolean updateRobotStatus(int status) throws RemoteException {
        Log.i(TAG, "module package name: " + mModule.getPackageName() + " update business status: " + status);
        Module.State state;
        switch (status) {
            case Definition.ROBOT_STATUS_IDLE:
                state = Module.State.IDLE;
                break;

            case Definition.ROBOT_STATUS_BUSY:
                state = Module.State.BUSY;
                break;

            default:
                state = null;
                break;
        }
        if (state == null) {
            return false;
        }
        mModule.updateModuleState(state);
        return true;
    }

    @Override
    public void setLanguage(String language) throws RemoteException {
        Log.d(TAG, "ModuleServer Set language : " + language);
        mModule.setLanguage(language);
    }

    @Override
    public void disableFunctionKey() {
        Log.d(TAG, "Disable function key");
        mModule.updateFunctionKeyStatus(false);
    }

    @Override
    public void enableFunctionKey() {
        Log.d(TAG, "enable function key");
        mModule.updateFunctionKeyStatus(true);
    }

    @Override
    public int setLedLight(int reqId, String properties)
            throws RemoteException {
        if (!mModule.isAllowModifyLight()) {
            Log.d(TAG, "Set led light permission denied : current is system light");
            return Definition.CMD_SEND_ERROR_PERMISSION_DENIED;
        }
        return mModule.getClient().setLedLight(reqId, properties);
    }

    @Override
    public boolean hasTopIR() {
        return ConfigManager.isHasTopIR();
    }

    @Override
    public boolean hasTopMono() {
        return ConfigManager.hasTopMono();
    }

    @Override
    public boolean hasChargeIR() {
        return ConfigManager.isHasChargeIR();
    }

    @Override
    public boolean isUseProZcbLed() throws RemoteException {
        return ConfigManager.isUseAutoEffectLed();
    }

    @Override
    public boolean isHasClavicleLight() throws RemoteException {
        return ConfigManager.isHasClavicleLed();
    }

    @Override
    public boolean isHasChestLight() throws RemoteException {
        return ConfigManager.isHasChestLed();
    }

    @Override
    public boolean isHasProTrayLED() throws RemoteException {
        return ConfigManager.isHasTrayLight();
    }

    @Override
    public boolean hasMono() throws RemoteException {
        return ConfigManager.hasMono();
    }

    @Override
    public void disablePushWarning() throws RemoteException {
        Log.d(TAG, "Disable push warning");
        mModule.setSystemStatusEnabled(Definition.SYSTEM_PUSH_WARNING, false);
    }

    @Override
    public void enablePushWarning() throws RemoteException {
        Log.d(TAG, "enable push warning");
        mModule.setSystemStatusEnabled(Definition.SYSTEM_PUSH_WARNING, true);
    }

    @Override
    public boolean hasHeightLimitCamera() throws RemoteException {
        return FileUtils.hasHeightLimitCamera();
    }

    @Override
    public void disableOutsideMapAlarm() throws RemoteException {
        mModule.setSystemStatusEnabled(Definition.SYSTEM_OUTSIDE_MAP, false);
    }

    @Override
    public void enableOutsideMapAlarm() throws RemoteException {
        mModule.setSystemStatusEnabled(Definition.SYSTEM_OUTSIDE_MAP, true);
    }

    @Override
    public boolean isUseAutoEffectLed() throws RemoteException {
        return ConfigManager.isUseAutoEffectLed();
    }

    @Override
    public boolean isHasClavicleLed() throws RemoteException {
        return ConfigManager.isHasClavicleLed();
    }

    @Override
    public boolean isHasChestLed() throws RemoteException {
        return ConfigManager.isHasChestLed();
    }

    @Override
    public boolean isHasTrayLight() throws RemoteException {
        return ConfigManager.isHasTrayLight();
    }

    @Override
    public String getCreateMapType() throws RemoteException {
        return ConfigManager.getCreateMapType();
    }

    public boolean isSupportElevator() {
        return ConfigManager.isSupportElevatorFunction();
    }

    public String isAlreadyInElevator() throws RemoteException {
        return mModule.getCore().getRobotInfoManager().isAlreadyInElevator();
    }
}
