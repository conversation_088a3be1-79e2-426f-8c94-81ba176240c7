package com.ainirobot.coreservice;

import android.app.Application;
import android.content.Context;

import com.ainirobot.robotlog.RobotLog;

public class ApplicationWrapper extends Application {

    private static ApplicationWrapper wrapper;

    @Override
    public void onCreate() {
        RobotLog.init(this);
        wrapper = this;
        super.onCreate();
    }

    public static Context getContext(){
        return wrapper.getApplicationContext();
    }
}
