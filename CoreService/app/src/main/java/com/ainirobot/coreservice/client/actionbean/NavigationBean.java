package com.ainirobot.coreservice.client.actionbean;

import com.ainirobot.coreservice.client.SettingsUtil;

/**
 * Created by orion on 2018/4/12.
 */

public class NavigationBean extends BaseBean {
    //String destination, long time

    private String destination;
    private long time;
    private double coordinateDeviation;
    private double mLinearSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED;
    private double mAngularSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED;
    private boolean adjustAngle = false;
    private boolean isReversePoseTheta = false;
    private boolean mNeedRotate;
    private double mDestinationRange = 0.0D;
    /**
     * 最大避障距离，传0表示默认值，navi中默认值为0.75
     */
    private double mObsDistance = 0.0D;
    private int wheelOverCurrentRetryCount;
    private long mMultipleWaitTime;
    private int priority;
    private double linearAcceleration;
    private double angularAcceleration;
    private int startModeLevel;
    private int brakeModeLevel;
    private double posTolerance;
    private double angleTolerance;
    private int mRunStopCmdParam = 0; // 默认值0
    private boolean isInLocationCheckTheta;
    private int roadMode = 1; // 是否依据地图巡线导航 0 不遵循 1 遵循

    public String getDestination() {
        return destination;
    }

    public long getTime() {
        return time;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public double getCoordinateDeviation() {
        return coordinateDeviation;
    }

    public void setCoordinateDeviation(double coordinateDeviation) {
        this.coordinateDeviation = coordinateDeviation;
    }

    public double getLinearSpeed(){
        return mLinearSpeed;
    }

    public void setLinearSpeed(double linearSpeed){
        this.mLinearSpeed = linearSpeed;
    }

    public double getAngularSpeed(){
        return mAngularSpeed;
    }

    public void setAngularSpeed(double angularSpeed){
        this.mAngularSpeed = angularSpeed;
    }

    public boolean ismNeedRotate() {
        return mNeedRotate;
    }

    public void setmNeedRotate(boolean mNeedRotate) {
        this.mNeedRotate = mNeedRotate;
    }

    public boolean isAdjustAngle() {
        return adjustAngle;
    }

    public void setAdjustAngle(boolean adjustAngle) {
        this.adjustAngle = adjustAngle;
    }
    public boolean getIsReversePoseTheta() {
        return isReversePoseTheta;
    }

    public void setIsReversePoseTheta(boolean isReverse) {
        this.isReversePoseTheta = isReverse;
    }

    public boolean isNeedRotate() {
        return mNeedRotate;
    }

    public void setNeedRotate(boolean needRotate) {
        mNeedRotate = needRotate;
    }

    public double getDestinationRange() {
        return mDestinationRange;
    }

    public void setDestinationRange(double destinationRange) {
        mDestinationRange = destinationRange;
    }

    public double getObsDistance() {
        return mObsDistance;
    }

    public void setObsDistance(double obsDistance) {
        mObsDistance = obsDistance;
    }

    public int getWheelOverCurrentRetryCount() {
        return wheelOverCurrentRetryCount;
    }

    public void setWheelOverCurrentRetryCount(int wheelOverCurrentRetryCount) {
        this.wheelOverCurrentRetryCount = wheelOverCurrentRetryCount;
    }

    public long getMultipleWaitTime() {
        return mMultipleWaitTime;
    }

    public void setMultipleWaitTime(long multipleWaitTime) {
        mMultipleWaitTime = multipleWaitTime;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public double getLinearAcceleration() {
        return linearAcceleration;
    }

    public void setLinearAcceleration(double linearAcceleration) {
        this.linearAcceleration = linearAcceleration;
    }

    public double getAngularAcceleration() {
        return angularAcceleration;
    }

    public void setAngularAcceleration(double angularAcceleration) {
        this.angularAcceleration = angularAcceleration;
    }

    public int getStartModeLevel() {
        return startModeLevel;
    }

    public void setStartModeLevel(int startModeLevel) {
        this.startModeLevel = startModeLevel;
    }

    public int getBrakeModeLevel() {
        return brakeModeLevel;
    }

    public void setBrakeModeLevel(int brakeModeLevel) {
        this.brakeModeLevel = brakeModeLevel;
    }

    public double getPosTolerance() {
        return posTolerance;
    }

    public void setPosTolerance(double posTolerance) {
        this.posTolerance = posTolerance;
    }

    public double getAngleTolerance() {
        return angleTolerance;
    }

    public void setAngleTolerance(double angleTolerance) {
        this.angleTolerance = angleTolerance;
    }

    public int getRunStopCmdParam() {
        return mRunStopCmdParam;
    }

    public void setRunStopCmdParam(int mRunStopCmdParam) {
        this.mRunStopCmdParam = mRunStopCmdParam;
    }

    public boolean isInLocationCheckTheta() {
        return isInLocationCheckTheta;
    }

    public void setIsInLocationCheckTheta(boolean isInLocationCheckTheta) {
        this.isInLocationCheckTheta = isInLocationCheckTheta;
    }

    public void setRoadMode(int roadMode) {
        this.roadMode = roadMode;
    }

    public int getRoadMode() {
        return roadMode;
    }
}
