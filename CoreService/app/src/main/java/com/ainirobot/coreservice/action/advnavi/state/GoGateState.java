package com.ainirobot.coreservice.action.advnavi.state;

import android.util.Log;

import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.action.advnavi.elevatorcmd.ElevatorControlUtil;
import com.ainirobot.coreservice.action.advnavi.elevatorcmd.ElevatorControlUtilListener;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.MultiRobotStatus;
import com.ainirobot.coreservice.client.actionbean.NavigationAdvancedBean;
import com.ainirobot.coreservice.client.actionbean.Pose;

import java.util.List;

/**
 * Data: 2022/8/9 11:02
 * Author: wanglijing
 * Description: GoElevatorGateState
 * 导航去电梯门点位状态
 */
public class GoGateState extends BaseNavigationState {

    //电梯门点位名称
    private String elevatorGatePlaceName;
    //导航超时失败重试次数
    private int naviRetryCount = 0;
    //导航重试最多次数
    private final int MAX_NAVI_RETRY_COUNT = 5;
    private ElevatorControlUtil controlUtil;

    GoGateState(String name) {
        super(name);
    }

    @Override
    protected boolean isEnableMultiRobotStatus() {
        return true;
    }

    @Override
    protected void preAction(StateData data, IStateListener listener) {
        super.preAction(data, listener);
        naviRetryCount = 0;
//        elevatorGatePlaceName = mStateData.getElevatorName() +  "-" + Definition.ELEVATOR_ENTER_POSE;
        //TODO 改为按类型获取电梯口
        Pose elevatorGatePose = getPoseByTypeId(Definition.ELEVATOR_ENTRANCE_TYPE);
        if (null != elevatorGatePose) {
            elevatorGatePlaceName = elevatorGatePose.getName();
        }
        Log.d(TAG, "elevatorGatePlaceName: " + elevatorGatePlaceName);

        //如果是进梯失败事件触发的该状态
        //需要设置对电梯状态的监听,避免电梯关门，还要出梯的情况
        controlUtil = new ElevatorControlUtil(this,
                mStateData.getCurrentFloorIndex(),
                false,
                elevatorControlUtilListener);
        if (isToCenterFail()) {
            //这里保留调用开门的接口，是否真的需要开门由elevator协议层决定，不需要开门则空实现，如Octa
            controlUtil.startOpenDoor();
        }
    }

    @Override
    protected void doAction() {
        super.doAction();
        onStatusUpdate(Definition.STATUS_START_GO_ELEVATOR_GATE, "start to elevator gate");

        naviToElevatorGate();   //do action
    }

    @Override
    protected void exit() {
        Log.d(TAG, "exit: " + actionState);
        controlUtil.cmdCloseElevatorDoor();//关闭电梯门
        controlUtil.stopControlElevator();

        //首次达到成功，不需要释放电梯控制
        //已经在电梯内，需要一直导航去电梯中心，不需要释放电梯控制
        //首次到达失败，需要释放电梯控制，放弃本次任务
        //返回成功，需要释放电梯控制，让电梯给人用（*** 只是放弃本次乘梯，应该立即注册电梯抢到占用权 ***）
        if (actionState != Definition.ElevatorAction.ACTION_CONTINUE_TO_CENTER
                && actionState != Definition.ElevatorAction.ACTION_NAVI_SUC) {
            Log.d(TAG, "release elevator from GoGateState exit");
            controlUtil.releaseElevatorControl();//首次到达失败，返回电梯口成功，都需要释放电梯控制
        }

        super.exit();
    }

    @Override
    protected BaseState getNextState() {
        switch (actionState) {
            //进梯失败，返回电梯口成功 本次导航是否前往等待点，是则先去等待点，否则进到在电梯外等待状态
            case ACTION_BACK_GATE:
                if (mStateData.getIsGoElevatorWaitPoint()) {
                    return StateEnum.GO_ELEVATOR_WAIT_POINT.getState();
                } else {
                    return StateEnum.WAIT_OUTSIDE_ELEVATOR.getState();
                }
            //导航成功事件 进到在电梯外等待状态
            case ACTION_NAVI_SUC:
                if (mStateData.getIsGoElevatorWaitPoint()) {
                    return StateEnum.WAIT_OUTSIDE_ELEVATOR.getState();
                } else {
                    return StateEnum.REGISTER_ELEVATOR.getState();
                }
            ////返回电梯口失败，且已经在电梯内，需要一直导航去电梯中心
            case ACTION_CONTINUE_TO_CENTER:
                return StateEnum.ENTER_ELEVATOR.getState();
        }
        return super.getNextState();
    }

    @Override
    protected boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        if (null != controlUtil
                && controlUtil.cmdResponse(command, result, extraData)) {
            //透传给elevatorControlUtil
            return true;
        }
        switch (command) {
            case Definition.CMD_NAVI_RESUME_SPECIAL_PLACE_THETA:
                parseResumeRobotTheta();
                return true;
        }
        return super.cmdResponse(cmdId, command, result, extraData);
    }


    @Override
    protected boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        if (null != controlUtil
                && controlUtil.cmdResponse(command, status, extraData)) {
            //透传给elevatorControlUtil
            return true;
        }
        return super.cmdStatusUpdate(cmdId, command, status, extraData);
    }

    @Override
    protected void onNavigationStatusUpdate(int result, String message, String extraData) {
        onStatusUpdate(result, message, extraData);
    }

    @Override
    protected void onNavigationError(int result, String message, String extraData) {
        switch (result) {
            case Definition.ERROR_DESTINATION_CAN_NOT_ARRAIVE:
            case Definition.ERROR_MULTI_ROBOT_WAITING_TIMEOUT:
            case Definition.ERROR_NAVIGATION_AVOID_TIMEOUT:
                naviRetry();    //重试
                return;
        }
        onResult(result, message, extraData);
    }

    @Override
    protected void onNavigationResult(int result, String message, String extraData) {
        switch (result) {
            case Definition.ERROR_IN_DESTINATION:
            case Definition.RESULT_OK:
                //有可能机器人朝向不对，尝试归正方向一次
                resumeRobotTheta();
                break;
        }
    }

    @Override
    protected void handleMultipleRobotStatus(List<MultiRobotStatus> robotStatusList) {

    }

    /**
     * 根据actionCode 判断是否是 进梯失败回电梯口 事件触发的本状态
     *
     * @return true: 是进梯失败回电梯口事件触发的本状态
     */
    private boolean isToCenterFail() {
        return mStateData.getActionCode() == Definition.ElevatorAction.ACTION_NAVI_FAIL;
    }

    /**
     * 控制电梯开门时状态监听
     */
    private final ElevatorControlUtilListener elevatorControlUtilListener = new ElevatorControlUtilListener() {
        @Override
        public void onElevatorStatusUpdate(int id, String param, String extraData) {

        }

        @Override
        public void onElevatorResult(int id, String param) {
            onElevatorCmdResult(id, param);
        }

        @Override
        public void onSuccess() {

        }
    };

    private void onElevatorCmdResult(int id, String param) {
        Log.d(TAG, "onElevatorCmdResult status:" + id + " , data:" + param);
        switch (id) {
            case Definition.ERROR_ELEVATOR_CONTROL:
            case Definition.ERROR_CALL_ELEVATOR_FAILED:
            case Definition.ERROR_CLOSE_ELEVATOR_DOOR_FAILED:
            case Definition.ERROR_OPEN_ELEVATOR_DOOR_FAILED:
                stopNavigation();   //停止导航
                openDoorFail(controlUtil.inElevatorRange());
                break;

            default:
                onResult(id, param);
                break;
        }
    }

    //回电梯口时，开门失败了
    private void openDoorFail(boolean inElevator) {
        Log.d(TAG, "openDoorFail inElevator: " + inElevator);
        //如果在电梯内
        if (inElevator) {
            //更新事件为一直尝试去电梯事件，交给去进梯状态处理
            updateAction(Definition.ElevatorAction.ACTION_CONTINUE_TO_CENTER);//电梯内返回电梯口失败，继续去电梯中心
            onSuccess();
        } else {
            //虽然控梯失败了，但已经在电梯外，接着导航去电梯口
            naviToElevatorGate();   //back gate suc
        }
    }

    private void naviToElevatorGate() {
        Log.d(TAG, "naviToElevatorGate");
        NavigationAdvancedBean bean = cloneNaviBean();
        bean.setDestination(elevatorGatePlaceName);
        bean.setDestinationRange(NavigationAdvancedBean.NAVIGATION_DEFAULT_DESTINATION_RANGE);//电梯口不需要停到精确位置
        startNavigation(bean);
    }

    private void naviRetry() {
        //超过最大重试次数
        naviRetryCount++;
        if (naviRetryCount > MAX_NAVI_RETRY_COUNT) {
            naviFail();
            return;
        }
        //重试
        naviToElevatorGate();   //retry
    }

    private void naviFail() {
        onResult(Definition.ERROR_NAVIGATION_FAILED);
    }

    private void naviSuccess() {
        //如果是进梯失败触发的该状态
        if (isToCenterFail()) {
            Log.d(TAG, "back gate navi success ");
            //导航成功，说明回电梯口成功，更新 ACTION_BACK_GATE 事件
            updateAction(Definition.ElevatorAction.ACTION_BACK_GATE);
        } else {
            Log.d(TAG, " navi success ");
            //否则就是正常的导航成功
            updateAction(Definition.ElevatorAction.ACTION_NAVI_SUC);
        }

        onStatusUpdate(Definition.STATUS_ARRIVED_ELEVATOR_GATE, "arrived elevator gate");
        onSuccess();
    }

    /**
     * 发送归正方向命令
     * 归正机器人朝向与建图时方向相同
     */
    private void resumeRobotTheta() {
        Log.d(TAG, "start resume robot theta");
        mApi.resumeSpecialPlaceTheta(mReqId, elevatorGatePlaceName);
    }

    /**
     * 处理归正机器人朝向
     */
    private void parseResumeRobotTheta() {
        naviSuccess();
    }
}
