package com.ainirobot.coreservice.core.status.biz;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.core.status.IStatus;
import com.ainirobot.coreservice.core.status.StatusSubscriber;
import com.ainirobot.coreservice.core.status.queue.StatusMessage;
import com.ainirobot.coreservice.core.status.queue.StatusMessageQueue;
import com.ainirobot.coreservice.service.CoreService;
import com.google.gson.Gson;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

public class StatusProvider implements IStatus {

    public static final String TAG = "StatusProvider:";
    private static final int QUERY_INDEX = 1000;
    private ConcurrentHashMap<String, HWState> mDefaultModules = new ConcurrentHashMap<>();
    private ConcurrentHashMap<String, CopyOnWriteArrayList<StatusSubscriber>> mHWListeners = new ConcurrentHashMap<>();
    private StatusMessageQueue mMessageQueue;
    private CopyOnWriteArrayList<String> mAlreadyRegisters;
    private int mQueryIndex = QUERY_INDEX;
    private Gson mGson;

    public StatusProvider(CoreService coreService) {
        init();
        mMessageQueue = new StatusMessageQueue(coreService);
        mAlreadyRegisters = new CopyOnWriteArrayList<>();
        mGson = new Gson();
    }

    private void init() {
        mDefaultModules.put(RobotOS.UNKNOWN_SERVICE, new HWState(RobotOS.UNKNOWN_SERVICE));
        mDefaultModules.put(RobotOS.NAVIGATION_SERVICE, new HWState(RobotOS.NAVIGATION_SERVICE));
        mDefaultModules.put(RobotOS.HEAD_SERVICE, new HWState(RobotOS.HEAD_SERVICE));
        mDefaultModules.put(RobotOS.REMOTE_SERVICE, new HWState(RobotOS.REMOTE_SERVICE));
        mDefaultModules.put(RobotOS.SYSTEM_SERVICE, new HWState(RobotOS.SYSTEM_SERVICE));
        mDefaultModules.put(RobotOS.OTA_SERVICE, new HWState(RobotOS.OTA_SERVICE));
        mDefaultModules.put(RobotOS.SPEECH_SERVICE, new HWState(RobotOS.SPEECH_SERVICE));
        mDefaultModules.put(RobotOS.DUMP_SERVICE, new HWState(RobotOS.DUMP_SERVICE));
        mDefaultModules.put(RobotOS.ELEVATOR_SERVICE, new HWState(RobotOS.ELEVATOR_SERVICE));
        mDefaultModules.put(RobotOS.PASS_GATE_SERVICE, new HWState(RobotOS.PASS_GATE_SERVICE));
        mDefaultModules.put(RobotOS.AGENT_SERVICE, new HWState(RobotOS.AGENT_SERVICE));
        initListener();
    }

    private void initListener() {
        if (mDefaultModules.size() > 0) {
            Set<Map.Entry<String, HWState>> entries = mDefaultModules.entrySet();
            for (Map.Entry<String, HWState> entry : entries) {
                entry.getValue().setOnStatusChangeListener(mStatusChangeListener);
            }
        }
    }

    @Override
    public void registerHWCallback(String serviceName) {
        Log.i(TAG, "registerHWCallback:" + serviceName);
        if (TextUtils.isEmpty(serviceName) || !mDefaultModules.containsKey(serviceName)) {
            Log.i(TAG, "registerHWCallback: serviceName is invalid : " + serviceName);
            return;
        }

        if (mAlreadyRegisters.contains(serviceName)) {
            Log.i(TAG, "registerHWCallback: : " + serviceName + " is already register");
            return;
        }

        mAlreadyRegisters.add(serviceName);
        HWState hwState = mDefaultModules.get(serviceName);
        if (hwState != null) {
            firstSyncModuleStatus(hwState);
        }
    }

    private void firstSyncModuleStatus(HWState hwState) {
        Set<String> mappings = hwState.getTypeSet();
        int count = 0;
        for (String status : mappings) {
            StatusMessage message = StatusMessage.obtain(hwState, createId(status, count), status);
            mMessageQueue.offer(message);
            count++;
        }
    }

    @Override
    public void unregisterHWCallback(String serviceName) {
        Set<String> keys = mDefaultModules.keySet();
        if (!TextUtils.isEmpty(serviceName) && keys.contains(serviceName)) {
            mDefaultModules.remove(serviceName);
        }
    }

    @Override
    public void sendStatusReport(String serviceName, String statusType, String data) {
        if (TextUtils.isEmpty(statusType) || TextUtils.isEmpty(data)) {
            Log.i(TAG, "sendStatusReport: moduleId:" + serviceName + " statusType:" + statusType + "listener :" + data);
            return;
        }

        if (TextUtils.isEmpty(serviceName)) {
            sendStatusReport(statusType, data);
        } else {
            HWState hwState = getHWState(serviceName);
            if (hwState == null) {
                hwState = getHWState(RobotOS.UNKNOWN_SERVICE);
            }
            if (hwState != null) {
                hwState.reportStatus(statusType, data);
            }
        }
    }

    private void sendStatusReport(String statusType, String data) {
        if (TextUtils.isEmpty(statusType) || TextUtils.isEmpty(data)) {
            Log.i(TAG, "sendStatusReport: statusType:" + statusType + " data :" + data);
            return;
        }

        HWState hwState = getHWStatusByType(statusType);
        if (hwState == null) {
            hwState = getHWState(RobotOS.UNKNOWN_SERVICE);
        }

        if (hwState != null) {
            hwState.reportStatus(statusType, data);
        }
    }

    private HWState getHWStatusByType(String statusType) {
        Set<Map.Entry<String, HWState>> entries = mDefaultModules.entrySet();
        for (Map.Entry<String, HWState> entry : entries) {
            if (entry.getValue().getStatusMap().containsKey(statusType)) {
                return entry.getValue();
            }
        }
        return null;
    }

    @Override
    public void getRobotStatus(String statusType, StatusSubscriber listener) {
        if (TextUtils.isEmpty(statusType) || listener == null) {
            Log.i(TAG, "queryStatus: statusType:" + statusType + "listener :" + listener);
            if (listener != null && listener.isAlive()) {
                listener.onStatusUpdate(statusType, "current statusType: " + statusType + " is empty");
            }
            return;
        }

        if (TextUtils.equals(Definition.ROBOT_STATUS_ALL, statusType)) {
            if (listener.isAlive()) {
                listener.onStatusUpdate(statusType, getAllRobotStatus());
            }
            return;
        }

        final HWState hwState = getHWStatusByType(statusType);
        if (hwState == null) {
            if (listener.isAlive()) {
                listener.onStatusUpdate(statusType, "current statusType:" + statusType + " do not exist");
            }
            return;
        }

        String value = hwState.getStatusMap().get(statusType);
        Log.d(TAG, "getRobotStatus value :"+value);
        if (listener.isAlive()) {
            listener.onStatusUpdate(statusType, value);
        }

        if (TextUtils.isEmpty(value)) {
            StatusMessage msg = StatusMessage.obtain(hwState, createId(statusType, mQueryIndex++), statusType);
            mMessageQueue.offer(msg);
        }
    }

    private String getAllRobotStatus() {
        HashMap<String, String> allStatusMap = new HashMap<>(128);
        Set<Map.Entry<String, HWState>> entries = mDefaultModules.entrySet();
        for (Map.Entry<String, HWState> entry : entries) {
            allStatusMap.putAll(entry.getValue().getStatusMap());
        }
        return mGson.toJson(allStatusMap);
    }

    private String createId(String status, int index) {
        return status.concat("_") + index;
    }

    @Override
    public void registerStatusListener(String statusType, StatusSubscriber listener) {
        if (TextUtils.isEmpty(statusType) || listener == null) {
            Log.i(TAG, "registerStatusListener: statusType:" + statusType + "listener :" + listener);
            if (listener != null && listener.isAlive()) {
                listener.onStatusUpdate(statusType, "current statusType: " + statusType + " is empty");
            }
            return;
        }

        realRegisterStatusListener(statusType, listener);
    }

    private void realRegisterStatusListener(String statusType, StatusSubscriber listener) {
        Log.i(StatusProvider.TAG, "registerStatusListener: statusType :" + statusType + " contains: " + mHWListeners.containsKey(statusType));

        CopyOnWriteArrayList<StatusSubscriber> statusListeners;
        if (mHWListeners.containsKey(statusType)) {
            statusListeners = mHWListeners.get(statusType);
        } else {
            statusListeners = new CopyOnWriteArrayList<>();
            mHWListeners.put(statusType, statusListeners);
        }

        if (statusListeners != null && !statusListeners.contains(listener)) {
            statusListeners.add(listener);
            Log.i(StatusProvider.TAG, "registerStatusListener: size :" + mHWListeners.get(statusType).size());
        }
    }

    @Override
    public boolean unregisterStatusListener(String statusType, int hashCode) {

        if (TextUtils.isEmpty(statusType) || hashCode == 0) {
            Log.i(TAG, "unRegisterStatusListener: statusType:" + statusType + " hashCode :" + hashCode);
            return false;
        }
        return realUnRegisterStatusListener(statusType, hashCode);
    }

    private boolean realUnRegisterStatusListener(String statusType, int hashCode) {
        if (mHWListeners.containsKey(statusType)) {
            CopyOnWriteArrayList<StatusSubscriber> statusListeners = mHWListeners.get(statusType);
            if (statusListeners == null || statusListeners.isEmpty()) {
                return false;
            }
            for (StatusSubscriber listener : statusListeners) {
                if (listener.hashCode() == hashCode) {
                    statusListeners.remove(listener);
                    return true;
                }
            }
        }
        return false;
    }

    private HWState.OnStatusChangeListener mStatusChangeListener = new HWState.OnStatusChangeListener() {
        @Override
        public void onChange(String statusType, String data) {
            invokeListener(statusType, data);
        }
    };

    private void invokeListener(String statusType, String data) {
        if (mHWListeners.containsKey(statusType)) {
            CopyOnWriteArrayList<StatusSubscriber> statusListeners = mHWListeners.get(statusType);
            if (statusListeners == null || statusListeners.isEmpty()) {
                return;
            }
            for (StatusSubscriber subscriber : statusListeners) {
                if (subscriber.isAlive()) {
                    subscriber.onStatusUpdate(statusType, data);
                } else {
                    Log.d(StatusProvider.TAG, "Remove listener : " + statusType + "   " + data);
                    statusListeners.remove(subscriber);
                }
            }
        }
    }

    private HWState getHWState(String serviceName) {
        if (mDefaultModules.containsKey(serviceName)) {
            return mDefaultModules.get(serviceName);
        }
        return null;
    }
}
