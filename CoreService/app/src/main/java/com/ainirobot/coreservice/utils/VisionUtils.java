/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.coreservice.utils;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.listener.Person;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class VisionUtils {

    private static final String TAG = VisionUtils.class.getSimpleName();
    private static final String MESSAGE_TIMEOUT = "timeout";
    private static final String MESSAGE_NO_REGISTERED = "HWService not registered";
    private static final int CODE_OK = 0;

    public static boolean isRemoteDetectSuccess(String jsonMsg) {
        if (jsonMsg == null || MESSAGE_TIMEOUT.equals(jsonMsg)
                || MESSAGE_NO_REGISTERED.equals(jsonMsg)) {
            return false;
        }

        try {
            JSONObject json = new JSONObject(jsonMsg);
            int code = json.optInt("code", -1);
            if (CODE_OK != code) {
                return false;
            }
        } catch (JSONException e) {
            return false;
        }
        return true;
    }

    public static Person updatePersonData(Person person, String jsonMsg) {
        if (person == null) {
            return null;
        }
        try {
            JSONObject json = new JSONObject(jsonMsg);
            String remoteReqId = json.optString("reqId");
            String remoteWakeupId = json.optString("wakeupId");
            JSONObject obj = new JSONObject(json.optString("data"));
            JSONObject personObj = new JSONObject(obj.optString("people"));
            JSONArray actions = obj.optJSONArray("actions");
            String name = personObj.optString("name");
            int age = personObj.optInt("age", 0);
            String role = personObj.optString("role");
            String gender = personObj.optString("gender");
            String userId = personObj.optString("user_id");
            int roleId = personObj.optInt("role_id", 0);
            String avatarUrl = personObj.optString("avatar_url");
            String staffMobile = personObj.optString("staff_mobile");
            String staffJob = personObj.optString("staff_job");
            String staffDept = personObj.optString("staff_dept");
            boolean isStaff = personObj.optInt("is_staff") == 1;
            String remoteFaceId = personObj.optString("face_id");
            String registerTime = personObj.optString("user_register_time");
            person.setUserId(userId);
            person.setName(name);
            person.setStaff(isStaff);
            person.setAge(age);
            person.setRole(role);
            person.setGender(gender);
            person.setRoleId(roleId);
            person.setAvatarUrl(avatarUrl);
            person.setStaffMobile(staffMobile);
            person.setStaffJob(staffJob);
            person.setStaffDept(staffDept);
            person.setRemoteReqId(remoteReqId);
            person.setRemoteFaceId(remoteFaceId);
            person.setUserRegisterTime(registerTime);
            person.setRemoteWakeupId(remoteWakeupId);

            if (actions != null && actions.length() > 0) {
                ArrayList<Person.WelcomeAction> list = new ArrayList<>();
                for (int i = 0; i < actions.length(); i++) {
                    try {
                        JSONObject object = actions.getJSONObject(i);
                        Person.WelcomeAction action = person.obtainNewWelcomeAction();
                        if ("play_tts".equals(object.optString("action"))) {
                            action.setAction("play_tts");
                            JSONObject object1 = new JSONObject(object.optString("tts"));
                            action.setValue(object1.optString("value"));
                        } else if ("query_by_text".equals(object.optString("action"))) {
                            action.setAction("query_by_text");
                            action.setValue(object.optString("param"));
                            action.setConfirmTTS(object.optString("confirm_tts"));
                            action.setHelloTTS(object.optString("hello_tts"));
                            action.setIsConfirm(object.optInt("enable_confirm"));
                        }
                        list.add(action);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
                person.setWelcomeActions(list);
            }
            Log.d(TAG, "updatePersonData name:" + name);
        } catch (JSONException e) {
            e.printStackTrace();
        } finally {
            return person;
        }
    }

    public static void deletePictures(final List<String> fileNames) {
        if (fileNames == null || fileNames.size() <= 0) {
            Log.i(TAG, "file name length error, return!!");
            return;
        }
        for (String filename : fileNames) {
            File file = new File(filename);
            if (file.exists() && file.isFile()) {
                Log.i(TAG, "delete file:" + filename);
                file.delete();
            } else {
                Log.i(TAG, "no exits, file name:" + fileNames);
            }
        }
    }

    public static void deletePicture(String fileName) {
        if (TextUtils.isEmpty(fileName)) {
            Log.i(TAG, "file name error, return!!");
            return;
        }
        File file = new File(fileName);
        if (file.exists() && file.isFile()) {
            Log.i(TAG, "delete file:" + fileName);
            file.delete();
        } else {
            Log.i(TAG, "no exits, file name:" + fileName);
        }
    }

    public static Person.Remote updateRemoteData(String jsonMsg) {
        JSONObject json = null;
        try {
            json = new JSONObject(jsonMsg);
            JSONObject obj = new JSONObject(json.optString("data"));
            JSONObject personObj = new JSONObject(obj.optString("people"));
            Person.Remote remote = new Person.Remote();
            remote.setUser_id(personObj.optString("user_id"));
            remote.setUser_is_new(personObj.optBoolean("user_is_new"));
            remote.setFace_id(personObj.optString("face_id"));
            remote.setUser_register_time(personObj.optLong("user_register_time"));
            remote.setName(personObj.optString("name"));
            remote.setStaff_mobile(personObj.optString("staff_mobile"));
            remote.setStaff_job(personObj.optString("staff_job"));
            remote.setStaff_dept(personObj.optString("staff_dept"));
            remote.setIs_staff(personObj.optInt("is_staff"));
            remote.setIdentity(personObj.optString("identity"));
            remote.setRole(personObj.optString("role"));
            remote.setStatus(personObj.optString("status"));
            remote.setRole_id(personObj.optInt("role_id"));
            remote.setAvatar_url(personObj.optString("avatar_url"));
            if (personObj.has("quality")) {
                remote.setQuality(personObj.optInt("quality"));
            }
            remote.setAge(personObj.optInt("age"));
            remote.setGender(personObj.optString("gender"));
            remote.setGlasses(personObj.optInt("glasses"));
            remote.setReqId(json.optString("reqId"));
            remote.setWakeupId(json.optString("wakeupId"));
            return remote;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return null;
    }
}

