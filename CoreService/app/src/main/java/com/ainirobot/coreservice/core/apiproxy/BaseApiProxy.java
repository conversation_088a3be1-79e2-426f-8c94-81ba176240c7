package com.ainirobot.coreservice.core.apiproxy;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.ContentObserver;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Environment;
import android.os.Handler;
import android.os.Message;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.IStatusListener;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.core.apiproxy.result.BaseApiResult;
import com.ainirobot.coreservice.core.status.RemoteSubscriber;
import com.ainirobot.coreservice.core.status.StatusManager;
import com.ainirobot.coreservice.data.account.MemberDataHelper;
import com.ainirobot.coreservice.listener.IActionListener;
import com.ainirobot.coreservice.service.CoreService;
import com.google.gson.Gson;

public abstract class BaseApiProxy {

    private static final String TAG = BaseApiProxy.class.getSimpleName();

    public static final int MSG_API_RESULT = 0xff;
    protected static final String PICTURE_PATH = Environment.getExternalStorageDirectory()
            + "/robot/register";

    protected CoreService mCore;
    protected Context mContext;
    protected Gson mGson;
    protected Handler mHandler;

    public BaseApiProxy(CoreService core, Handler handler) {
        mCore = core;
        mContext = core.getApplicationContext();
        mHandler = handler;
        mGson = new Gson();
    }

    public void registerRemoteLoginStateListener() {
        Log.i(TAG, "registerRemoteLoginStateListener");
        StatusManager statusManager = mCore.getStatusManager();
        RemoteSubscriber subscriber = new RemoteSubscriber(new IStatusListener.Stub() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Message message = new Message();
                message.what = MSG_API_RESULT;
                if (Definition.SUCCEED.equalsIgnoreCase(data)) {
                    BaseApiResult result = new BaseApiResult(BaseApiResult.Type
                            .REMOTE_LOGIN_STATE, BaseApiResult.RESULT_SUCCESS);
                    if (mHandler != null) {
                        message.obj = result;
                        mHandler.sendMessage(message);
                    }
                }
            }
        });
        statusManager.registerStatusListener(Definition.STATUS_LOGIN_REMOTE, subscriber);
    }

    public void registerNetworkStateListener() {
        Log.i(TAG, "registerNetworkStateListener");
        BroadcastReceiver receiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (intent == null || intent.getAction() == null) {
                    return;
                }
                if (ConnectivityManager.CONNECTIVITY_ACTION.equals(intent.getAction())) {
                    ConnectivityManager connectivityManager = (ConnectivityManager) mCore
                            .getApplicationContext()
                            .getSystemService(Context.CONNECTIVITY_SERVICE);
                    if (connectivityManager == null) {
                        return;
                    }
                    NetworkInfo activeNetwork = connectivityManager.getActiveNetworkInfo();
                    if (activeNetwork != null) {
                        //如果当前的网络连接成功并且网络连接可用
                        if (NetworkInfo.State.CONNECTED == activeNetwork.getState()
                                && activeNetwork.isAvailable()) {
                            if (mHandler != null) {
                                Message message = new Message();
                                message.what = MSG_API_RESULT;
                                message.obj = new BaseApiResult(BaseApiResult
                                        .Type.NETWORK_STATE, BaseApiResult.RESULT_SUCCESS);
                                mHandler.sendMessage(message);
                            }
                        }
                    }
                }
            }
        };
        IntentFilter filter = new IntentFilter();
        filter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
        mCore.registerReceiver(receiver, filter);
    }

    public void registerTokenTableChangeListener() {

        mContext.getContentResolver().registerContentObserver(MemberDataHelper.getInstance(mCore)
                        .getTokenDataHelperUri(), true,
                new ContentObserver(new Handler()) {
                    @Override
                    public void onChange(boolean selfChange) {
                        Log.d(TAG,"onChange() selfChange: " + selfChange);
                        if (mHandler != null) {
                            Message msg = new Message();
                            msg.what = MSG_API_RESULT;
                            msg.obj = new BaseApiResult(BaseApiResult.Type.TOKEN_TABLE_CHANGED,
                                    BaseApiResult.RESULT_SUCCESS, null);
                            mHandler.sendMessage(msg);
                        }
                    }
                });
    }

    public void registerFaceTableChangeListener() {
        mContext.getContentResolver().registerContentObserver(MemberDataHelper.getInstance(mCore)
                        .getFaceDataHelperUri(),
                true, new ContentObserver(new Handler()) {
                    @Override
                    public void onChange(boolean selfChange) {
                        Log.d(TAG,"onChange() selfChange: " + selfChange);
                        if (mHandler != null) {
                            Message msg = new Message();
                            msg.what = MSG_API_RESULT;
                            msg.obj = new BaseApiResult(BaseApiResult.Type.FACE_TABLE_CHANGED,
                                    BaseApiResult.RESULT_SUCCESS, null);
                            mHandler.sendMessage(msg);
                        }
                    }
                });
    }

    public static class ApiListener extends IActionListener.Stub {
        @Override
        public void onResult(int status, String responseString) throws RemoteException {

        }

        @Override
        public void onError(int errorCode, String errorString) throws RemoteException {

        }

        @Override
        public void onStatusUpdate(int status, String data) throws RemoteException {

        }

        @Override
        public void onResultWithExtraData(int result, String message, String extraData) throws RemoteException {
        }

        @Override
        public void onErrorWithExtraData(int errorCode, String errorString, String extraData) throws RemoteException {
        }

        @Override
        public void onStatusUpdateWithExtraData(int status, String data, String extraData) throws RemoteException {
        }
    }
}
