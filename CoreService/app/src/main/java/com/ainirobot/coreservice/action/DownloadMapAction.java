package com.ainirobot.coreservice.action;

import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.download_map.DownloadMapStateEnum;
import com.ainirobot.coreservice.action.download_map.StateListener;
import com.ainirobot.coreservice.action.download_map.state.BaseDownloadMapPkgState;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.DownloadMapBean;
import com.ainirobot.coreservice.core.LocalApi;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import static com.ainirobot.coreservice.action.download_map.DownloadMapStateEnum.DOWNLOAD_MAP_PKG;

public class DownloadMapAction extends AbstractAction<DownloadMapBean> implements StateListener {

    private static final String TAG = "DownloadMapAction";
    private final static String MAP_DIR = "/robot/map";
    private String mParamsStr;
    private DownloadMapBean mParams;
    private BaseDownloadMapPkgState mCurrentDownloadState;

    protected DownloadMapAction(ActionManager manager, int id, String[] resList) {
        super(manager, id, resList);
    }

    @Override
    protected boolean startAction(DownloadMapBean parameter, LocalApi api) {
        mParamsStr = mGson.toJson(parameter);
        mParams = parameter;
        mParams.setOldMap(getMapList().contains(mParams.getMapName()));
        mCurrentDownloadState = DOWNLOAD_MAP_PKG.getDownloadMapPkgState();
        mCurrentDownloadState.start(mParamsStr, mParams, this);
        return true;
    }

    @Override
    public void onSendCommand(int reqId, String command, String params) {
        Log.d(TAG, "onSendCommand command:" + command + " params:" + params);
        mApi.sendCommand(reqId, command, params);
    }

    @Override
    public void onStateUpdate(int state, String msg) {
        Log.d(TAG, "onStateUpdate state:" + state + " msg:" + msg);
        onStatusUpdate(state, msg);
    }

    @Override
    public void onStateRunSuccess(DownloadMapStateEnum stateEnum, Object data) {
        Log.d(TAG, "onStateRunSuccess " + stateEnum.name());
        if (stateEnum == DownloadMapStateEnum.DOWNLOAD_SUCCEED) {
            onResult(Definition.RESULT_OK, Definition.SUCCEED);
            return;
        }
        mCurrentDownloadState = stateEnum.getDownloadMapPkgState();
        mCurrentDownloadState.setData(data);
        mCurrentDownloadState.start(mParamsStr, mParams, this);
    }

    @Override
    public void onStateRunFailed(int errorCode, String errorMessage) {
        Log.d(TAG, "onStateRunFailed errorCode:" + errorCode + " errorMessage:" + errorMessage);
        onError(errorCode, errorMessage);
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        if (null != mCurrentDownloadState) {
            mCurrentDownloadState.stop();
        }
        return true;
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return null;
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return null;
    }

    @Override
    protected boolean verifyParam(DownloadMapBean param) {
        return null != param && !TextUtils.isEmpty(param.getMapName());
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        Log.d(TAG, "cmdResponse command:" + command + " result:" + result + " extraData:" + extraData);
        return mCurrentDownloadState.cmdResponse(cmdId, command, result, extraData);
    }

    private List<String> getMapList() {
        List<String> list = new ArrayList<>();
        File root = Environment.getExternalStorageDirectory();
        File mapDir = new File(root, MAP_DIR);
        if (!mapDir.isDirectory() || mapDir.list() == null
                || (mapDir.list() != null && mapDir.list().length == 0)) {
            return list;
        }

        File[] files = mapDir.listFiles();
        if (files == null) {
            return list;
        } else {
            for (File file : files) {
                if (file.isDirectory()) {
                    list.add(file.getName());
                }
            }
        }
        Log.d(TAG, "getMapList: list=" + list);
        return list;
    }
}
