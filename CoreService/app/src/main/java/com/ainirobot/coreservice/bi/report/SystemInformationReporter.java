package com.ainirobot.coreservice.bi.report;

import com.ainirobot.coreservice.client.upload.bi.CallChainReport;

public class SystemInformationReporter {

    private static final String TABLE_NAME_INFORMATION = "robot_os_information";

    protected ReportEditor mReportEditor;

    protected SystemInformationReporter(String businessName, String reportClassName) {
        mReportEditor = ReportEditor.newInstance(businessName, reportClassName);
    }

    protected static class ReportEditor extends CallChainReport {
        private String mBusinessName;
        private String mReportClassName;

        private ReportEditor(final String businessName, final String reportClassName) {
            super(TABLE_NAME_INFORMATION, businessName);
            mBusinessName = businessName;
            mReportClassName = reportClassName;
        }

        static ReportEditor newInstance(String businessName, String reportClassName) {
            ReportEditor reportEditor = new ReportEditor(businessName, reportClassName);
            reportEditor.addSourceRobotOs().addPackageNameCoreService();
            return reportEditor;
        }

        protected ReportEditor reset() {
            initData();
            addSourceRobotOs().addPackageNameCoreService()
                .addBusinessName(mBusinessName)
                .addClassName(mReportClassName);
            return this;
        }
    }
}
