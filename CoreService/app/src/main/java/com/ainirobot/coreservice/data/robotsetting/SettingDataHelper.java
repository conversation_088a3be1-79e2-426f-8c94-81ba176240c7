/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.data.robotsetting;

import android.content.ContentValues;
import android.content.Context;
import android.database.ContentObserver;
import android.database.Cursor;
import android.net.Uri;
import android.provider.BaseColumns;

import com.ainirobot.coreservice.core.system.RobotSetting;
import com.ainirobot.coreservice.data.provider.BaseDataHelper;
import com.ainirobot.coreservice.data.provider.Column;
import com.ainirobot.coreservice.data.provider.SQLiteTable;

public class SettingDataHelper extends BaseDataHelper {
    public static final SQLiteTable TABLE = new SQLiteTable(SettingField.TABLE_NAME, RobotSettingProvider.AUTHORITY, false)
            .addColumn(SettingField.COLUMN_ID, Column.Constraint.PRIMARY_KEY, Column.DataType.TEXT)
            .addColumn(SettingField.COLUMN_VALUE, Column.DataType.TEXT);

    public static final String NOT_SUPPORT = "not support";

    private Context mContext;

    public SettingDataHelper(Context context) {
        super(context);
        mContext = context;
    }

    @Override
    public Uri getContentUri() {
        return TABLE.getContentUri();
    }

    public String getRobotSetting(String key) {
        Cursor cursor = query(SettingField.COLUMN_ID + "=?", new String[]{key});
        if (cursor != null && cursor.moveToNext()) {
            String value = cursor.getString(cursor.getColumnIndex(SettingField.COLUMN_VALUE));
            cursor.close();
            return value;
        }
        return NOT_SUPPORT;
    }

    public int setRobotSetting(String key, String value) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(SettingField.COLUMN_VALUE, value);
        return update(contentValues, SettingField.COLUMN_ID + "=?", new String[]{key});
    }

    public void registerListener(ContentObserver contentObserver) {
        mContext.getContentResolver().registerContentObserver(
                getContentUri(), true, contentObserver);
    }

    public void registerListener(String key, ContentObserver contentObserver) {
        mContext.getContentResolver().registerContentObserver(
                Uri.withAppendedPath(getContentUri(), key), false, contentObserver);
    }

    public void unRegisterListener(ContentObserver contentObserver) {
        mContext.getContentResolver().unregisterContentObserver(contentObserver);
    }

    public static final class SettingField implements BaseColumns {
        static final String TABLE_NAME = "setting";

        static final String COLUMN_ID = "id";
        static final String COLUMN_VALUE = "value";
    }
}
