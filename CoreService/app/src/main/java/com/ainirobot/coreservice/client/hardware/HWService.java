package com.ainirobot.coreservice.client.hardware;

import android.os.Binder;
import android.os.ParcelFileDescriptor;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.IHWService;
import com.ainirobot.coreservice.IInspectCallBack;
import com.ainirobot.coreservice.bean.Command;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.exception.BinderExceptionHandler;
import com.ainirobot.coreservice.config.ServiceConfig;
import com.ainirobot.coreservice.core.external.ExternalService;
import com.ainirobot.coreservice.core.external.ExternalServiceManager;

import java.util.List;

public abstract class HWService extends IHWService.Stub {

    private final String TAG = this.getClass().getSimpleName();

    private ServiceConfig config;
    private List<Command> commands;
    private volatile boolean isMounted = false;

    @Override
    public int getHWStatus() throws RemoteException {
        return Definition.HW_STATUS_IDLE;
    }

    @Override
    public final List<Command> mount(String serviceName, ServiceConfig config) throws RemoteException {
        if (isMounted) {
            return commands;
        }
        long identity = Binder.clearCallingIdentity();
        try {
            this.config = config;
            this.commands = onMount(serviceName, config);
            isMounted = true;
        } catch (Exception e) {
            BinderExceptionHandler.handle(e);
        } finally {
            Binder.restoreCallingIdentity(identity);
        }
        return commands;
    }

    @Override
    public final void startInspect(IInspectCallBack callback) throws RemoteException {
        long identity = Binder.clearCallingIdentity();
        try {
            if (!isEnable()) {
                Log.d(TAG, "Service is disabled : start inspect");
                return;
            }

            onInspectStart(callback);
        } catch (Exception e) {
            BinderExceptionHandler.handle(e);
        } finally {
            Binder.restoreCallingIdentity(identity);
        }
    }

    @Override
    public final void reset() throws RemoteException {
        long identity = Binder.clearCallingIdentity();
        try {
            if (!isEnable()) {
                Log.d(TAG, "Service is disabled : reset");
                return;
            }

            onReset();
        } catch (Exception e) {
            BinderExceptionHandler.handle(e);
        } finally {
            Binder.restoreCallingIdentity(identity);
        }
    }

    @Override
    public final void startUpgrade(String subsystem, String params) throws RemoteException {
        long identity = Binder.clearCallingIdentity();
        try {
            onUpgrade(subsystem, params);
        } catch (Exception e) {
            BinderExceptionHandler.handle(e);
        } finally {
            Binder.restoreCallingIdentity(identity);
        }
    }

    @Override
    public final String exeSyncCommand(String cmdType, String params, String language) throws RemoteException {
        long identity = Binder.clearCallingIdentity();
        try {
            if (!isEnable()) {
                Log.d(TAG, "Service is disabled : " + cmdType + "  " + params);
                return null;
            }

            Log.d(TAG, "Exe sync command : " + cmdType + "  " + params);
            return onSyncCommand(cmdType, params, language);
        } catch (Exception e) {
            BinderExceptionHandler.handle(e);
        } finally {
            Binder.restoreCallingIdentity(identity);
        }
        return null;
    }

    @Override
    public final void exeAsyncCommand(String cmdType, String params, String language) throws RemoteException {
        long identity = Binder.clearCallingIdentity();
        try {
            if (!isEnable()) {
                Log.d(TAG, "Service is disabled : " + cmdType + "  " + params);
                return;
            }

            if (TextUtils.isEmpty(cmdType)) {
                Log.d(TAG, "Invalid  command type");
                return;
            }
            Log.d(TAG, "Exe async command : " + cmdType + "  " + params);
            onAsyncCommand(cmdType, params, language);
        } catch (Exception e) {
            BinderExceptionHandler.handle(e);
        } finally {
            Binder.restoreCallingIdentity(identity);
        }
    }

    private boolean isEnable() {
        return config == null || config.isEnable();
    }

    @Override
    public boolean startStatusSocket(String type, int socketPort) throws RemoteException {
        return false;
    }

    @Override
    public boolean closeStatusSocket(String type, int socketPort) throws RemoteException {
        return false;
    }

    public abstract List<Command> onMount(String serviceName, ServiceConfig config);

    /**
     * Start inspect
     *
     * @param callBack inspect result callback
     */
    public abstract void onInspectStart(IInspectCallBack callBack);

    /**
     * Start upgrade hardware function
     *
     * @param params upgrade params
     * @return
     */
    public abstract boolean onUpgrade(String subsystem, String params);

    /**
     * Reset hardware status
     */
    public abstract void onReset();

    public abstract void onAsyncCommand(String type, String params, String language);

    public abstract String onSyncCommand(String type, String params, String language);

    @Override
    public abstract String getBoardName() throws RemoteException;

    @Override
    public abstract boolean isNeedUpgrade(String subsystem, String version) throws RemoteException;

    @Override
    public abstract String getVersion(String subsystem) throws RemoteException;

    @Override
    public void switchAppControl(String packageName, String lastPackageName) {
    }

    @Override
    public void setLanguage(String language) {
    }

    @Override
    public ParcelFileDescriptor getParcelFileDescriptor(String type, String params) throws RemoteException {
        return null;
    }

    @Override
    public boolean setParcelFileDescriptor(String type, ParcelFileDescriptor pfd, String params) throws RemoteException {
        return false;
    }

    @Override
    public void releaseParcelFileDescriptor(String type) throws RemoteException {
    }

}
