package com.ainirobot.coreservice.data;

import android.content.Context;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.action.advnavi.util.ElevatorLocationUtils;
import com.ainirobot.coreservice.bean.ElevatorRangeInfo;
import com.ainirobot.coreservice.bean.MultiFloorInfo;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.actionbean.CruiseRouteBean;
import com.ainirobot.coreservice.client.actionbean.InspectionResult;
import com.ainirobot.coreservice.client.actionbean.LanguageBean;
import com.ainirobot.coreservice.client.actionbean.PlaceBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.speech.entity.LangParamsEnum;
import com.ainirobot.coreservice.core.CoreStateMachine;
import com.ainirobot.coreservice.core.InternalDef;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.core.RobotSettingManager;
import com.ainirobot.coreservice.core.external.ExternalService;
import com.ainirobot.coreservice.core.external.ExternalServiceManager;
import com.ainirobot.coreservice.core.status.LocalSubscriber;
import com.ainirobot.coreservice.core.status.StatusManager;
import com.ainirobot.coreservice.inspection.InspectionStatusManager;
import com.ainirobot.coreservice.service.CoreService;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.coreservice.utils.SystemUtils;
import com.ainirobot.coreservice.utils.ZipUtils;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 机器人状态、信息等数据维护，通过 RobotApi 提供同步接口对外暴露数据，
 * 减少每次获取外设数据都需要走整条链路的次数.
 * <p>
 * Created by Orion on 2019/8/26.
 */
public class RobotInfoManager implements LocalApi.OnCmdResponse {
    private static final String TAG = "RobotInfoManager";
    private static final String FAILED = "failed";
    /**
     * 获取地图信息失败后重试间隔，默认5秒
     */
    private static final long RETRY_INTERVAL = 5 * 1000;
    /**
     * 获取地图名称失败后重试间隔 1000ms
     */
    private static final long RETRY_INTERVAL_GET_MAP_NAME = 1000;
    private static final String H401 = "H401";
    private static final String RECEPTION_POINT = "接待点";
    private static final String HW_NO_REGISTERED = "HWService not registered";
    private static final String HW_UN_SUPPORT = "unsupported";
    private static final String HW_NOT_SUPPORT = "not supported";

    private static final String TABLE_NAME_MAP_INFO = "map_info";
    private static final String COLUMN_MAP_LANGUAGE = "map_language";
    private static final String COLUMN_MAP_NAME = "map_name";
    private static final String FIRST_ACTIVATION = "first_activation";


    private Gson mGson;
    private CoreService mCoreService;
    private volatile boolean mIsPoseEstimate = false;
    private volatile String mSpeedX = "";
    private volatile String mSpeedExpectX = "";
    private volatile AtomicReference<Map<String, Pose>> mAllLocations;
    private volatile AtomicReference<Map<String, String>> mLocaitonIds;
    private volatile AtomicReference<List<MultiFloorInfo>> mMultiFloorInfos;
    private volatile String mMapName;
    private volatile String mMapLanguage;
    private AtomicReference<CruiseRouteBean> mCruiseRoute;
    private boolean mFirstConfigFinishedNotReboot = false;// 首次配置结束后，还没有重启过的标识,首次配置刚刚结束时设置
    private AtomicReference<Pose> mCurrentPose;
    private String mBindInfo = null;
    private String mChannelInfo = null;
    private String mMqttServer = null;
    private String mRemoteLanguageStateInfo = null;
    private ElevatorLocationUtils mLocationUtils = null;

    /**
     * 同步应用商店App数据的状态
     * succeed 成功 failed 失败
     */
    private String mSyncAppDataStatus;

    /**
     * 语言
     */
    private String language = LangParamsEnum.ZH_CN.codeName;

    /**
     * 接待点位置范围
     */
    private static final double RECEPTION_RANGE = 1d;

    /**
     * 头部版本
     * <p>
     * H401 支持270度旋转
     */
    private String mHeadVersion;
    private boolean isLoadingPose;
    private ElectricDoorStatusListener mElectricDoorStatusListener;

    public RobotInfoManager(CoreService coreService) {
        this.mCoreService = coreService;
        this.mGson = new Gson();
        this.mAllLocations = new AtomicReference<>();
        this.mLocaitonIds = new AtomicReference<>();
        this.mCurrentPose = new AtomicReference<>();
        this.mCruiseRoute = new AtomicReference<>();
        this.mMultiFloorInfos = new AtomicReference<>();

        initMonitor();
    }

    /**
     * 初始化状态监听器
     */
    private void initMonitor() {
        ExternalServiceManager mServices = mCoreService.getExternalServiceManager();
        ExternalService naviService = mServices.getExternalService(RobotOS.NAVIGATION_SERVICE);
        Log.d(TAG, "Init monitor naviService: " + naviService);
        if (naviService != null && naviService.isEnable()) {
            setPoseEstimateMonitor();
            setNavigationConfigMonitor();
            setPoseMonitor();
            setVelocityMonitor();
        }
        setRemoteStatusMonitor();

        getHeadVersion();

        setLanguageMonitor();
        setTimezoneMonitor();
        setMultiFloorMonitor();
        setSettingsGlobalMonitor();
    }

    @Override
    public void onCmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_IS_ESTIMATE:
                processPoseEstimate(result);
                break;
            case Definition.CMD_NAVI_GET_MAP_NAME:
                processMapName(result);
                break;
            case Definition.CMD_NAVI_GET_INTERNATIONAL_PLACE_LIST_INTER:
                processAllLocations(result);
                break;
            case Definition.CMD_CAN_GET_ROTATE_SUPPORT:
                processHeadVersion(result);
                break;
            case Definition.CMD_NAVI_GET_CRUISE_ROUTE:
                processCruiseRoute(result);
                break;
            case Definition.CMD_NAVI_QUERY_MULTI_FLOOR_CONFIG:
                loadMultiFloorInfo(result);
                break;
            case Definition.CMD_NAVI_GET_MAP_INFO:
                updateMapLanguage(result);
                break;
            case Definition.CMD_CAN_GET_ELECTRIC_DOOR_STATUS:
                if (this.mElectricDoorStatusListener != null) {
                    this.mElectricDoorStatusListener.onResult(result);
                }
                break;
            default:
                break;
        }
    }

    private void updateMapLanguage(String result) {
        try {
            JSONObject jsonObject = new JSONObject(result);
            mMapLanguage = jsonObject.optString("mapLanguage");
            getAllLocations();
            return;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        retry(new Runnable() {
            @Override
            public void run() {
                getMapLanguage(mMapName);
            }
        });
    }

    private void processCruiseRoute(String result) {
        Log.d(TAG, "Process cruise route : " + result);
        if (!TextUtils.isEmpty(result) && !HW_NO_REGISTERED.equals(result)
                && !HW_UN_SUPPORT.equals(result)) {
            try {
                if (FAILED.equals(result)) {
                    mCruiseRoute.set(null);
                    Log.e(TAG, "Load cruise route failed : not exits");
                    return;
                }

                CruiseRouteBean route = mGson.fromJson(result, CruiseRouteBean.class);
                if (route != null) {
                    mCruiseRoute.set(route);
                    return;
                }
            } catch (Exception e) {
                e.printStackTrace();
                mCruiseRoute.set(null);
            }
        }

        retry(new Runnable() {//processCruiseRoute
            @Override
            public void run() {
                getCruiseRoute();
            }
        });
    }

    private void processHeadVersion(String result) {
        Log.d(TAG, "Process head version : " + result);
        if (!TextUtils.isEmpty(result) && !HW_NO_REGISTERED.equals(result)
                && !HW_UN_SUPPORT.equals(result) && !HW_NOT_SUPPORT.equals(result)) {
            try {
                JSONObject jsonObject = new JSONObject(result);
                mHeadVersion = jsonObject.getString(Definition.JSON_CAN_BOARD_APP_VERSION);
                return;
            } catch (JSONException e) {
                e.printStackTrace();
            }
        } else if (HW_NOT_SUPPORT.equals(result)) {
            mHeadVersion = "";
            return;
        }

        retry(new Runnable() {//processHeadVersion
            @Override
            public void run() {
                getHeadVersion();
            }
        });
    }

    private void processAllLocations(String result) {
        Log.d(TAG, "Process all locations : " + result);
        isLoadingPose = true;
        if (!TextUtils.isEmpty(result) && !HW_NO_REGISTERED.equals(result)
                && !HW_UN_SUPPORT.equals(result)) {
            Type type = new TypeToken<ArrayList<PlaceBean>>() {
            }.getType();
            try {
                result = ZipUtils.unzipMapData(mGson, result);
                List<PlaceBean> placeList = mGson.fromJson(result, type);

                Map<String, Pose> locations = new HashMap<>();
                Map<String, String> ids = new HashMap<>();
                if (placeList != null && placeList.size() > 0) {
                    for (PlaceBean naviPose : placeList) {
                        Pose pose = new Pose();
                        pose.setX((float) naviPose.getPointX());
                        pose.setY((float) naviPose.getPointY());
                        pose.setTheta((float) naviPose.getPointTheta());
                        pose.setTypeId(naviPose.getTypeId());
                        pose.setPriority(naviPose.getPriority());
//                        Log.d(TAG, "Debug===placeName:" + naviPose.getPlaceName());
                        if (TextUtils.isEmpty(naviPose.getPlaceName())) {
                            //当前语言地点名不存在，取地图默认语言名字
                            Map<String, String> names = naviPose.getPlaceNameList();
//                            Log.d(TAG, "Debug===mapDefaultLangPoseName:" + names.get(mMapLanguage));
                            pose.setName(names.get(mMapLanguage));
                        } else {
                            pose.setName(naviPose.getPlaceName());
                        }
                        locations.put(naviPose.getPlaceId(), pose);
                        Map<String, String> names = naviPose.getPlaceNameList();
                        for (String name : names.values()) {
                            ids.put(name.toLowerCase(), naviPose.getPlaceId());
                        }
                    }

                    mAllLocations.set(locations);
                    mLocaitonIds.set(ids);
                    isLoadingPose = false;
                    Log.d(TAG, "Process all locations update location: " + locations);
                    return;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        retry(new Runnable() {//processAllLocations
            @Override
            public void run() {
                getAllLocations();//processAllLocations.retry
            }
        });
    }

    private void processMapName(String result) {
        Log.d(TAG, "Process map name :: " + result);
        if (!TextUtils.isEmpty(result) && !HW_NO_REGISTERED.equals(result)
                && !HW_UN_SUPPORT.equals(result) && !"timeout".equals(result)) {
            if (!result.equals(mMapName)) {
                getMapLanguage(result);
                mMapName = result;
            }
            getCruiseRoute();
            return;
        }

        retry(new Runnable() {//processMapName
            @Override
            public void run() {
                getMapName();//processMapName.retry
            }
        }, RETRY_INTERVAL_GET_MAP_NAME);
    }

    private Object TAG_GET_MAP_NAME = new Object();

    /**
     * 获取地图名称指令重发
     * 添加延时任务前先清空，防止指令未取消积压过多导致指令重复执行，进而导致core崩溃
     */
    private synchronized void retry(Runnable runnable, long delay) {
        DelayTask.cancel(TAG_GET_MAP_NAME);
        DelayTask.submit(TAG_GET_MAP_NAME, runnable, delay);
    }

    private void processPoseEstimate(String result) {
        Log.d(TAG, "Process pose estimate : " + result);
        if (!TextUtils.isEmpty(result) && !HW_NO_REGISTERED.equals(result)
                && !HW_UN_SUPPORT.equals(result)) {
            mIsPoseEstimate = Boolean.parseBoolean(result);
            return;
        }

        retry(new Runnable() {//processPoseEstimate
            @Override
            public void run() {
                getRobotEstimateStatus();
            }
        });
    }

    @Override
    public void onCmdStatusUpdate(int cmdId, String command, String status, String extraData) {

    }

    public String getRobotInfo(int reqType, String params) {
        Log.d(TAG, "getRobotInfo: reqType=" + reqType + " params=" + params);
        switch (reqType) {
            case Definition.SYNC_ACTION_IS_ROBOT_ESTIMATE:
                return String.valueOf(mIsPoseEstimate);

            case Definition.SYNC_ACTION_IS_CHARGING:
                return String.valueOf(mCoreService.getBatteryManager().isCharging());

            case Definition.SYNC_ACTION_GET_BATTERY_LEVEL:
                return Integer.toString(mCoreService.getBatteryManager().getLevel());

            case Definition.SYNC_ACTION_IS_CHARGE_PILE_EXIT:
                return String.valueOf(isChargePileExits());

            case Definition.SYNC_ACTION_IS_SUPPORT_HEAD_REVERSE:
                return String.valueOf(H401.equals(mHeadVersion));

            case Definition.SYNC_ACTION_IS_IN_LOCATION:
                return String.valueOf(isInLocation(params));

            case Definition.SYNC_ACTION_IS_IN_RECEPTION_LOCATION:
                return String.valueOf(isInLocation(RECEPTION_POINT, RECEPTION_RANGE));

            case Definition.SYNC_ACTION_GET_PLACE_DISTANCE:
                return String.valueOf(getPlaceDistance(params));

            case Definition.SYNC_ACTION_GET_PLACE_POSE_DISTANCE:
                return String.valueOf(getPlaceOrPoseDistance(params));

            case Definition.SYNC_ACTION_GET_SPECIAL_LOCATION:
                return mGson.toJson(getPose(params));

            case Definition.SYNC_ACTION_GET_SPECIAL_LOCATION_BY_TYPEID:
                return mGson.toJson(getSpecialPoseByTypeId(params));

            case Definition.SYNC_ACTION_GET_CURRENT_LOCATION:
                return mGson.toJson(getCurrentPose());

            case Definition.SYNC_ACTION_GET_CRUISE_ROUTE:
                return mGson.toJson(mCruiseRoute.get());

            case Definition.SYNC_ACTION_GET_ALL_LOCATION:
                return mGson.toJson(getAllPose());

            case Definition.SYNC_ACTION_GET_MAP_NAME:
                return mMapName;

            case Definition.SYNC_ACTION_GET_INSPECTION_RESULT:
                return getInspectResult();

            case Definition.SYNC_ACTION_GET_BIND_INFO:
                return getBindInfo();

            case Definition.SYNC_ACTION_GET_CHANNEL_INFO:
                return getChannelInfo();

            case Definition.SYNC_ACTION_GET_MQTT_INFO:
                return getMqttServer();

            case Definition.SYNC_ACTION_SYNC_APP_DATA_STATUS:
                return mSyncAppDataStatus;

            case Definition.SYNC_ACTION_GET_LOCAL_AND_SERVER_LANGUAGE:
                return getLocalAndServerSupportLanguageList();

            case Definition.SYNC_ACTION_GET_LOCAL_SUPPORT_LANGUAGE:
                return getLocalSupportLanguageList();

            case Definition.SYNC_ACTION_GET_REMOTE_LANGUAGE:
                return getRemoteLanguageState();

            case Definition.SYNC_ACTION_IS_LOADING_POSE:
                return mGson.toJson(isLoadingPose());

            case Definition.SYNC_ACTION_GET_CURRENT_MULTI_FLOOR_INFO:
                return mGson.toJson(getCurrentMultiFloorInfo());

            case Definition.SYNC_ACTION_GET_MULTI_FLOOR_INFOS:
                return mGson.toJson(getMultiFloorInfos());

            case Definition.SYNC_ACTION_IS_PLACE_EXISTS:
                return mGson.toJson(isPlaceExists(params));

            case Definition.SYNC_ACTION_FLOOR_NAME:
                return mGson.toJson(getFloorName(params));

            case Definition.SYNC_ACTION_MULTI_FLOOR_INFO_BY_INDEX:
                return mGson.toJson(getMultiFloorInfoByIndex(Integer.parseInt(params)));

            case Definition.SYNC_ACTION_GET_ELEVATOR_RANGE:
                return mGson.toJson(getElevatorRangeByName(params));

            case Definition.SYNC_ACTION_FIRST_CONFIG_ACTIVATION_STATE:
                return String.valueOf(mFirstConfigFinishedNotReboot);

            case Definition.SYNC_ACTION_GET_LINE_SPEED:
                JsonObject speed = new JsonObject();
                speed.addProperty(Definition.JSON_NAVI_LINEAR_SPEED_X, mSpeedX);
                speed.addProperty(Definition.JSON_NAVI_LINEAR_SPEED_EXPECT_X, mSpeedExpectX);
                return mGson.toJson(speed);

            default:
                return Definition.STATUS_RESULT_UNSUPPORT_CMDTYPE;
        }
    }

    /**
     * 获取系统语言
     *
     * @return
     */
    public String getSystemLanguage() {
        return this.language;
    }

    /**
     * 定位状态监听
     */
    private void setPoseEstimateMonitor() {
        getRobotEstimateStatus();

        StatusManager statusManager = mCoreService.getStatusManager();
        statusManager.registerStatusListener(Definition.STATUS_POSE_ESTIMATE, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                try {
                    Log.d(TAG, "On pose estimate change : " + data);
                    JSONObject json = new JSONObject(data);
                    mIsPoseEstimate = json.getBoolean("isPoseEstimate");
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    /**
     * 设置 NavigationService 配置文件监听
     */
    private void setNavigationConfigMonitor() {
        getMapName();//initMonitor.setNavigationConfigMonitor

        StatusManager statusManager = mCoreService.getStatusManager();
        statusManager.registerStatusListener(Definition.REPORT_NAVI_CONFIG, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Log.d(TAG, "On config change : type=" + type + " data=" + data);
                if (Definition.NAVI_UPDATE_MAP_INFO.equals(data)) {
                    getMapName();//navi config change
                }else if (Definition.NAVI_UPDATE_PLACE.equals(data)) {
                    getAllLocations();
                } else if (Definition.NAVI_UPDATE_FROM_OLD_PLACE.equals(data)) {
                    DelayTask.submit(new Runnable() {
                        @Override
                        public void run() {
                            sendCommand(Definition.CMD_REMOTE_UPLOAD_PLACE_LIST, "");
                        }
                    }, 5000);
                }
            }
        });
    }

    private void setTimezoneMonitor() {
        final RobotSettingManager settingManager = mCoreService.getRobotSettingManager();
        settingManager.registerListener(Definition.ROBOT_TIME_ZONE_CODE, new ContentObserver(new Handler()) {
            @Override
            public void onChange(boolean selfChange) {
                setTimezone();
            }
        });
    }

    private void setTimezone() {
        Context context = ApplicationWrapper.getContext();
        RobotSettings.closeAutoTimeZone(context);
        RobotSettingManager settingManager = mCoreService.getRobotSettingManager();
        String timezone = settingManager.getRobotSetting(Definition.ROBOT_TIME_ZONE_CODE, true);

        SystemUtils.setTimeZone(context, timezone);
    }

    private void setLanguageMonitor() {
        getLanguage();

        final RobotSettingManager settingManager = mCoreService.getRobotSettingManager();
        settingManager.registerListener(Definition.ROBOT_LANGUAGE, new ContentObserver(new Handler()) {
            @Override
            public void onChange(boolean selfChange) {
                getLanguage();
                getAllLocations();//语言切换后，重新获取所有地点
                switchLanguageFromSettings(language);
            }
        });
    }

    private void switchLanguageFromSettings(String lang){
        Log.d(TAG," switchLanguageFromSettings:: lang = " + lang);
        sendCommand(Definition.CMD_REMOTE_SWITCH_LANGUAGE, lang);
    }

    private void getLanguage() {
        RobotSettingManager settingManager = mCoreService.getRobotSettingManager();
        String lang = settingManager.getRobotSetting(Definition.ROBOT_LANGUAGE, true);
        if (!TextUtils.isEmpty(lang)) {
            language = lang;
            updateLanguage(language);
        } else {
            Log.d(TAG, "Language error : value is empty");
        }
    }

    private void updateLanguage(String language) {
        if (TextUtils.isEmpty(language)) {
            return;
        }

        SystemUtils.setLanguage(language);
        for (ExternalService service :
                mCoreService.getExternalServiceManager().getExternalServices()) {
            service.setLanguage(language);
        }

        StatusManager statusManager = mCoreService.getStatusManager();
        statusManager.handleStatus(Definition.ROBOT_LANGUAGE, language);
    }

    /**
     * 设置坐标点变化监控
     */
    private void setPoseMonitor() {
        StatusManager statusManager = mCoreService.getStatusManager();
        statusManager.registerStatusListener(Definition.STATUS_POSE, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Pose pose = mGson.fromJson(data, Pose.class);
                mCurrentPose.set(pose);
            }
        });
    }

    private void setVelocityMonitor() {
        StatusManager statusManager = mCoreService.getStatusManager();
        statusManager.registerStatusListener(Definition.STATUS_SPEED, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                try {
                    if (data == null) {
                        return;
                    }
                    JSONObject json = new JSONObject(data);
                    mSpeedX = String.valueOf(json.getDouble(Definition.JSON_NAVI_LINEAR_SPEED_X));
                    mSpeedExpectX = String.valueOf(json.getDouble(Definition.JSON_NAVI_LINEAR_SPEED_EXPECT_X));
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    public void setRemoteStatusMonitor() {
        StatusManager statusManager = mCoreService.getStatusManager();
        statusManager.registerStatusListener(Definition.STATUS_REMOTE_POST, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Log.d(TAG, "On remote status change : type=" + type + " data=" + data);
                mBindInfo = data;
            }
        });

        // remotecontrol 联网登录后,coreservice缓存从服务端获取的企业支持的所有语言
        statusManager.registerStatusListener(Definition.STATUS_MULTI_LANGUAGE_LIST, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Log.d(TAG, "On remote language_list change : type=" + type + " data=" + data);
                setRemoteLanguageList(data);
                mRemoteLanguageStateInfo = data;
            }
        });


        statusManager.registerStatusListener(Definition.STATUS_UPDATE_CHANNEL, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Log.d(TAG, "On pipe info change : type=" + type + " data=" + data);
                mChannelInfo = data;
            }
        });
        statusManager.registerStatusListener(Definition.STATUS_UPDATE_MQTTINFO, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Log.d(TAG, "On mqtt info change : type=" + type + " data=" + data);
                mMqttServer = data;
            }
        });

        statusManager.registerStatusListener(Definition.STATUS_REMOTE_APP_DATA, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                mSyncAppDataStatus = data;
            }
        });
    }

    private boolean isChargePileExits() {
        Map<String, Pose> allLocations = mAllLocations.get();
        return allLocations.containsKey(Definition.START_BACK_CHARGE_POSE);
    }

    private boolean isInLocation(String params) {
        if (!TextUtils.isEmpty(params)) {
            try {
                JSONObject jsonObject = new JSONObject(params);
                String name = jsonObject.getString("name");
                double range = jsonObject.getDouble("range");
                return isInLocation(name, range);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        return false;
    }

    /**
     * 判断机器人是否在某一位置范围内
     *
     * @param name  位置名称
     * @param range 位置范围
     * @return 在范围内返回true
     */
    private boolean isInLocation(String name, double range) {
        Pose pose = getPose(name);
        Pose currentPose = getCurrentPose();
        if (pose == null || currentPose == null) {
            Log.e(TAG, "Pose[" + name + "] : " + pose);
            return false;
        }
        Log.d(TAG, "isInLocation name: " + name
                + ", distance: " + pose.getDistance(currentPose) + ", pose: " + pose
                + ", currentPose: " + currentPose);

        return pose.getDistance(currentPose) <= range;
    }

    /**
     * 获取指定名称地点和机器当前位置间的距离
     *
     * @param placeName 地点名称
     */
    private double getPlaceDistance(String placeName) {
        double distance = 0;
        Pose pose = getPose(placeName);
        if (pose != null) {
            distance = pose.getDistance(getCurrentPose());
        }
        Log.d(TAG, "getPoseDistance placeName: " + placeName + ", distance: " + distance);
        return distance;
    }

    /**
     * 获取指定名称地点和指定位置间的距离
     *
     * @param param 地点名称+destpose
     */
    private double getPlaceOrPoseDistance(String param) {
        if (TextUtils.isEmpty(param)) {
            return -1;
        }
        try {
            JSONObject json = new JSONObject(param);
            String placeName = json.optString(Definition.JSON_NAV_SOUR_PLACENAME);
            String sourcePoseJson = json.optString(Definition.JSON_NAV_SOUR_POSE);
            String destPlaceName = json.optString(Definition.JSON_NAV_DEST_PLACENAME);
            String destPoseJson = json.optString(Definition.JSON_NAV_DEST_POSE);
            Log.d(TAG, "getPoseDistance before sourcePoseJson: " + sourcePoseJson + ", destPoseJson: " + destPoseJson);
            Pose pose, destPose;
            if (TextUtils.isEmpty(sourcePoseJson)) {
                pose = getPose(placeName);
                Log.d(TAG, "getPoseDistance 1");
            } else {
                pose = mGson.fromJson(sourcePoseJson.toString(), Pose.class);
                Log.d(TAG, "getPoseDistance 2");
            }
            if (TextUtils.isEmpty(destPoseJson)) {
                destPose = getPose(destPlaceName);
                Log.d(TAG, "getPoseDistance 3");
            } else {
                destPose = mGson.fromJson(destPoseJson.toString(), Pose.class);
                Log.d(TAG, "getPoseDistance 4");
            }
            double distance = -1;
            if (pose != null && destPose != null) {
                Log.d(TAG, "getPoseDistance 5");
                distance = pose.getDistance(destPose);
                Log.d(TAG, "getPoseDistance 6" + pose.toString() + "-----" + destPose.toString());
            }
            Log.d(TAG, "getPoseDistance placeName: " + placeName + ", distance: " + distance);
            return distance;
        } catch (JSONException e) {
            e.printStackTrace();
            Log.d(TAG, "getPoseDistance JSONException: "+e.toString());
        }
        return -1;
    }

    private List<Pose> getAllPose() {
        Map<String, Pose> allLocations = mAllLocations.get();
        if (allLocations != null) {
            List<Pose> list = new ArrayList<>(allLocations.values());
            for (Pose pose : list) {
                Log.d(TAG, "Debug===poseName" + pose.getName());
            }
            return new ArrayList<>(allLocations.values());
        }
        return null;
    }

    private Pose getPose(String placeName) {
        if (TextUtils.isEmpty(placeName)) {
            return null;
        }

        Map<String, String> ids = mLocaitonIds.get();
        if (ids == null) {
            return null;
        }

        String id = ids.get(placeName.toLowerCase());
        Log.i(TAG, "get pose name: " + id);
        Map<String, Pose> allLocations = mAllLocations.get();
        if (allLocations != null) {
            return allLocations.get(id);
        }
        Log.i(TAG, "not found this pose");
        return null;
    }

    private Pose getSpecialPoseByTypeId(String typeIdStr) {
        Log.d(TAG, "getSpecialPoseByTypeId: typeId=" + typeIdStr);
        if (TextUtils.isEmpty(typeIdStr)) {
            Log.e(TAG, "getSpecialPoseByTypeId: typeId is empty");
            return null;
        }

        int typeId = Integer.parseInt(typeIdStr);
        if(typeId <= 0){
            Log.e(TAG, "getSpecialPoseByTypeId: typeId is invalid");
            return null;
        }

        Map<String, String> ids = mLocaitonIds.get();
        if (ids == null) {
            Log.e(TAG, "getSpecialPoseByTypeId: ids is null");
            return null;
        }

        //遍历mAllLocations，找出typeId对应的pose，如果有多个取优先级最高的，0为最高
        Map<String, Pose> allLocations = mAllLocations.get();
        if (allLocations != null) {
            Pose resultPose = null;
            for (Map.Entry<String, Pose> entry : allLocations.entrySet()) {
                Pose pose = entry.getValue();
                if (pose.getTypeId() == typeId) {
                    if (resultPose == null || pose.getPriority() < resultPose.getPriority()) {
                        resultPose = pose;
                    }
                }
            }
            Log.d(TAG, "getSpecialPoseByTypeId: resultPose=" + resultPose);
            return resultPose;
        }

        Log.i(TAG, "getSpecialPoseByTypeId: not found this pose");
        return null;
    }

    private Pose getCurrentPose() {
        if (!mIsPoseEstimate) {
            return null;
        }
        return mCurrentPose.get();
    }

    private void retry(Runnable runnable) {
        DelayTask.submit(runnable, RETRY_INTERVAL);
    }

    /**
     * 获取地图名称
     */
    private void getMapName() {
        sendCommand(Definition.CMD_NAVI_GET_MAP_NAME, null);
    }

    private void getMapLanguage(String mapName) {
        Log.d(TAG, "getMapLanguage: mapName=" + mapName);
        if (TextUtils.isEmpty(mapName)) {
            Log.e(TAG, "getMapLanguage failed, map name is empty");
            return;
        }
        sendCommand(Definition.CMD_NAVI_GET_MAP_INFO, mapName);
    }

    /**
     * 加载所有坐标点
     */
    private void getAllLocations() {
        if (TextUtils.isEmpty(mMapName)) {
            mAllLocations.set(null);
            Log.e(TAG, "Get all locations failed, map name is empty");
            return;
        }
        isLoadingPose = true;
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("mapName", mMapName);
            jsonObject.put("noCache", true);
            sendCommand(Definition.CMD_NAVI_GET_INTERNATIONAL_PLACE_LIST_INTER, jsonObject.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 加载巡逻路线
     */
    private void getCruiseRoute() {
        if (TextUtils.isEmpty(mMapName)) {
            mCruiseRoute.set(null);
            Log.e(TAG, "Get cruise route failed, map name is empty");
            return;
        }
        JSONObject object = new JSONObject();
        try {
            object.put("map_name", mMapName);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        sendCommand(Definition.CMD_NAVI_GET_CRUISE_ROUTE, object.toString());
    }

    public void getElectricDoorStatus(ElectricDoorStatusListener electricDoorStatusListener) {
        this.mElectricDoorStatusListener = electricDoorStatusListener;
        sendCommand(Definition.CMD_CAN_GET_ELECTRIC_DOOR_STATUS, "");
    }

    /**
     * 获取定位状态
     */
    private void getRobotEstimateStatus() {
        sendCommand(Definition.CMD_NAVI_IS_ESTIMATE, null);
    }

    /**
     * 获取云台硬件版本
     */
    private void getHeadVersion() {
        sendCommand(Definition.CMD_CAN_GET_ROTATE_SUPPORT, null);
    }

    private String getInspectResult() {
        InspectionResult result = InspectionStatusManager.getmInspectionStatusManager().getInspectionResult();
        if (result == null) {
            return "";
        }
        return mGson.toJson(result);
    }

    private String getBindInfo() {
        return mBindInfo;
    }

    private String getChannelInfo() {
        return mChannelInfo;
    }

    private String getMqttServer() {
        return mMqttServer;
    }

    private String getLocalAndServerSupportLanguageList() {
        List<LanguageBean> list =
                mCoreService.getLanguageManger().getLocalAndServerSupportLanguageList();
        if (list == null || list.size() <= 0) {
            return null;
        }
        return mGson.toJson(list);
    }

    private String getLocalSupportLanguageList() {
        List<LanguageBean> list =
                mCoreService.getLanguageManger().getLocalSupportLanguageList();
        if (list == null || list.size() <= 0) {
            return null;
        }
        return mGson.toJson(list);
    }


    /**
     * 判断是否已经成功同步到 RobotInfoManager
     * @return
     */
    private String getRemoteLanguageState() {
        return mRemoteLanguageStateInfo;
    }
    /**
     * 数据库缓存从服务端获取的企业所支持的语言列表
     */
    private void setRemoteLanguageList(String remoteLanguageList){
        mCoreService.getLanguageManger().setServerLanguageList(remoteLanguageList);
    }

    private int sendCommand(String cmdType, String cmdParam) {
        CoreStateMachine coreStateMachine = mCoreService.getCoreStateMachine();
        int cmdId = coreStateMachine.addCommand(Definition.DEBUG_REQ_ID, cmdType, cmdParam, false, this);
        Log.d(TAG, "Send command : cmdId=" + cmdId + "  cmdType=" + cmdType + " cmdParam=" + cmdParam);
        if (cmdId < 0) {
            Log.e(TAG, "Send command error : cmdId=" + cmdId);
            return cmdId;
        }

        Handler coreHandler = mCoreService.getCoreHandler();
        Message msg = coreHandler.obtainMessage(InternalDef.MSG_CORE_ADD_COMMAND_ACTION);
        coreHandler.sendMessage(msg);

        return cmdId;
    }

    private void setMultiFloorMonitor() {
        loadMultiFloorInfo();
        StatusManager statusManager = mCoreService.getStatusManager();
        statusManager.registerStatusListener(Definition.STATUS_MULTI_FLOOR_CONFIG_UPDATE, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                loadMultiFloorInfo(data);
            }
        });
    }

    private void loadMultiFloorInfo() {
        sendCommand(Definition.CMD_NAVI_QUERY_MULTI_FLOOR_CONFIG, null);
    }

    private void loadMultiFloorInfo(final String message) {
        Log.d(TAG, "updateMultiFloorInfo: " + message);
        Type type = new TypeToken<ArrayList<MultiFloorInfo>>() {
        }.getType();
        try {
            List<MultiFloorInfo> placeList = new Gson().fromJson(message, type);
            mMultiFloorInfos.set(placeList);
        } catch (Exception e) {
            e.printStackTrace();
            DelayTask.submit(new Runnable() {
                @Override
                public void run() {
                    loadMultiFloorInfo();
                }
            },1000);
        }
    }

    private MultiFloorInfo getCurrentMultiFloorInfo() {
        List<MultiFloorInfo> multiFloorInfos = mMultiFloorInfos.get();
        if (null == multiFloorInfos) {
            return null;
        }
        for (int i = 0; i < multiFloorInfos.size(); i++) {
            MultiFloorInfo multiFloorInfo = multiFloorInfos.get(i);
            if (TextUtils.equals(multiFloorInfo.getMapName(), mMapName)) {
                return multiFloorInfo;
            }
        }
        return null;
    }

    private MultiFloorInfo getMultiFloorInfoByIndex(int index){
        List<MultiFloorInfo> multiFloorInfos = mMultiFloorInfos.get();
        if (null == multiFloorInfos) {
            return null;
        }
        for (int i = 0; i < multiFloorInfos.size(); i++) {
            MultiFloorInfo multiFloorInfo = multiFloorInfos.get(i);
            if (multiFloorInfo.getFloorIndex() == index) {
                return multiFloorInfo;
            }
        }
        return null;
    }

    private List<MultiFloorInfo> getMultiFloorInfos() {
        return mMultiFloorInfos.get();
    }


    /**
     * 获得某电梯的范围信息
     * @param elevatorName  电梯名
     * @return  ElevatorRangeInfo
     */
    private ElevatorRangeInfo getElevatorRangeByName(String elevatorName) {
        String rangeInfo = mCoreService.getRobotSettingManager().getRobotSetting(Definition.ROBOT_SETTING_ELEVATOR_RANGE);
        try {
            Type type = new TypeToken<List<ElevatorRangeInfo>>() {
            }.getType();
            List<ElevatorRangeInfo> rangeInfoList = mGson.fromJson(rangeInfo, type);
            for (ElevatorRangeInfo info : rangeInfoList) {
                if (TextUtils.equals(info.getElevatorName(), elevatorName)) {
                    return info;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private boolean isPlaceExists(String placeName) {
        Map<String, Pose> allLocations = mAllLocations.get();
        return allLocations.containsKey(placeName);
    }

    private String getFloorName(String floorStr) {
        try {
            int floor = Integer.parseInt(floorStr);
            List<MultiFloorInfo> mapInfo = mMultiFloorInfos.get();
            for (int i = 0; i < mapInfo.size(); i++) {
                MultiFloorInfo info = mapInfo.get(i);
                if (info.getFloorIndex() == floor) {
                    return info.getFloorAlias();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public boolean isLoadingPose() {
        return isLoadingPose;
    }

    private void setSettingsGlobalMonitor() {
        registerGlobalContentObserver(FIRST_ACTIVATION, observer);
    }

    public void registerGlobalContentObserver(String key, ContentObserver observer) {
        mCoreService.getContentResolver().registerContentObserver(
                Settings.Global.getUriFor(key), false, observer);
    }

    private final ContentObserver observer = new ContentObserver(new Handler(Looper.getMainLooper())) {

        @Override
        public void onChange(boolean selfChange, Uri uri) {
            super.onChange(selfChange, uri);
            Log.d(TAG, "mContentObserver uri : " +uri);
            if (uri == null || TextUtils.isEmpty(uri.getLastPathSegment())) {
                return;
            }

            String key = uri.getLastPathSegment();
            Log.d(TAG, "mContentObserver selfChange=" + selfChange + " key =" + key);

            switch (key) {
                case FIRST_ACTIVATION:
                    int result = Settings.Global.getInt(mCoreService.getContentResolver(), FIRST_ACTIVATION, 0);
                    Log.d(TAG, "onChange FIRST_ACTIVATION : "+ result);
                    if (result == 1){
                        mFirstConfigFinishedNotReboot = true;
                    }
                    break;
                default:
                    break;
            }
        }
    };

    public String isAlreadyInElevator() {
        //获得所有电梯名
        MultiFloorInfo currentMultiFloorInfo = getCurrentMultiFloorInfo();
        List<String> elevatorList;
        if (currentMultiFloorInfo == null || (elevatorList = currentMultiFloorInfo.getAvailableElevators()) == null || elevatorList.isEmpty()) {
            return getIsAlreadyInElevatorResult(Definition.ERROR_NOT_FOUND_ELEVATOR_PATH, "not found elevator");
        }
        if (!mIsPoseEstimate) {
            return getIsAlreadyInElevatorResult(Definition.ERROR_NOT_ESTIMATE, "not estimate");
        }

        //遍历在哪个电梯内
        for (String elevatorName : elevatorList) {
            //电梯轿厢范围
            ElevatorRangeInfo rangeInfo = getElevatorRangeByName(elevatorName);
            if (rangeInfo == null) {
                return getIsAlreadyInElevatorResult(Definition.ERROR_NOT_FOUND_ELEVATOR_PATH, "elevator range is null");
            }
            if (isInElevatorRange(elevatorName, rangeInfo)) {
                return getIsAlreadyInElevatorResult(Definition.RESULT_ROBOT_IN_ELEVATOR, elevatorName);
            }
        }
        return getIsAlreadyInElevatorResult(Definition.RESULT_ROBOT_NOT_IN_ELEVATOR, "not in elevator");
    }

    private String getIsAlreadyInElevatorResult(int result, String data) {
        Map<String, Object> map = new HashMap<>();
        map.put(Definition.JSON_TASK_EXEC_RESULT, result);
        map.put(Definition.JSON_TASK_EXEC_DATA, data);
        return mGson.toJson(map);
    }

    /**
     * 判断是否在电梯范围内
     *
     * @param elevatorName 电梯名
     * @param rangeInfo
     * @return true:在该电梯轿厢范围内
     */
    private boolean isInElevatorRange(String elevatorName, ElevatorRangeInfo rangeInfo) {
        if (mLocationUtils == null) {
            mLocationUtils = new ElevatorLocationUtils();
        }

        ElevatorLocationUtils.LocationState state = mLocationUtils.relativeElevatorPosition(
                true,  //避免机器人没完全进来，设为进梯状态下的轿厢范围
                getCurrentPose(),
                getElevatorCenterPose(),
                rangeInfo);
        Log.d(TAG, "isInElevatorRange: " + state);
        return state == ElevatorLocationUtils.LocationState.INSIDE_ELEVATOR;
    }

    private Pose getElevatorCenterPose() {
        return getSpecialPoseByTypeId(String.valueOf(Definition.ELEVATOR_CENTER_TYPE));
    }

    public interface ElectricDoorStatusListener {
        void onResult(String message);
    }

}
