package com.ainirobot.coreservice.client;

public class SpeechRecognizeDef {

    public static final String DOMAIN_REGISTER = "register";
    public static final String DOMAIN_LEADING_PERSON = "guide";
    public static final String DOMAIN_FOLLOW_ME = "follow_me";
    public static final String DOMAIN_PHOTO = "photo";
    public static final String DOMAIN_NAVIGATION = "robot_navigation";
    public static final String DOMAIN_MOTION = "motion";
    public static final String DOMAIN_RECEPTION = "verification_code";

    public class Register {
        public static final String PARAM_NAME = "start";
    }

    public class LeadingPerson {
        public static final String PARAM_DESTINATION = "destination";
        public static final String PARAM_WHO = "who";
    }

    public class FollowMe {
        public static final String INTENT_START = "start";
        public static final String INTENT_END = "end";
    }

    public class Photo {
        public static final String INTENT_TAKE = "take";
    }

    public class Navigation {
        public static final String PARAM_LOCATION = "location";
    }

    public class Reception {
        public static final String INTENT_INFORM = "inform";
        public static final String INTENT_STOP = "stop";
    }

    public static boolean isRobotDomain(String domain) {
        if (DOMAIN_NAVIGATION.equals(domain) || DOMAIN_FOLLOW_ME.equals(domain)
                || DOMAIN_LEADING_PERSON.equals(domain)
                || DOMAIN_PHOTO.equals(domain)
                || DOMAIN_RECEPTION.equals(domain)
                || DOMAIN_REGISTER.equals(domain)
                || DOMAIN_MOTION.equals(domain)) {
            return true;
        }
        return false;
    }
}
