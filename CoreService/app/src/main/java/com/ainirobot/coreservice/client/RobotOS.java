package com.ainirobot.coreservice.client;

public class RobotOS {

    public static final String NAVIGATION_SERVICE = "navigation";

    public static final String HEAD_SERVICE = "head";

    public static final String OTA_SERVICE = "ota";

    public static final String REMOTE_SERVICE = "remote";

    public static final String SPEECH_SERVICE = "speech";

    public static final String VISION_SERVICE = "vision";

    public static final String SYSTEM_SERVICE = "system";

    public static final String SWIPE_CARD_SERVICE = "swipe_card";

    public static final String SWIPE_THERMAL_SERVICE = "swipe_thermal";

    public static final String CODE_SCANNER_SERVICE = "code_scanner";

    public static final String DUMP_SERVICE = "dump_service";

    public static final String UNKNOWN_SERVICE = "unknown";

    public static final String PASSENGER_FLOW_SERVICE = "passenger_flow";

    public static final String VIDEO_SERVICE = "video";

    public static final String HARDWARE_SERVICE = "hardware";

    public static final String D430_SERVICE = "d430";

    public static final String ELEVATOR_SERVICE = "elevator";

    public static final String UVC_CAMERA_SERVICE = "uvc_camera";

    public static final String BMS_SERVICE = "bms_service";

    public static final String PASS_GATE_SERVICE = "passgate";
    public static final String AGENT_SERVICE = "agent";


}
