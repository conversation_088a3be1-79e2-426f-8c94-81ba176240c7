/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.client.permission;

import android.content.Intent;

public abstract class PermissionListener {
    public void activityStarting(Intent intent, String pkg) {
    }

    public void activityResuming(String pkg) {
    }

    public void appCrashed(String processName, int pid,
                    String shortMsg, String longMsg,
                    long timeMillis, String stackTrace) {
    }

    public void appEarlyNotResponding(String processName, int pid, String annotation) {
    }

    public void appNotResponding(String processName, int pid, String processStats) {
    }

    public void systemNotResponding(String msg) {
    }

    public void onForegroundActivitiesChanged(int pid, int uid, boolean foregroundActivities) {
    }

    public void onProcessDied(int pid, int uid) {
    }
}