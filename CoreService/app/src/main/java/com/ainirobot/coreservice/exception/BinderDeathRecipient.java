package com.ainirobot.coreservice.exception;

import android.content.Context;
import android.content.Intent;
import android.os.IBinder;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.IntentUtil;
import com.ainirobot.coreservice.daemon.DaemonService;
import com.ainirobot.coreservice.surfaceshare.SurfaceShareManager;

public class BinderDeathRecipient implements IBinder.DeathRecipient {
    private static final String TAG = BinderDeathRecipient.class.getSimpleName();

    private int function;
    private String appName;

    public BinderDeathRecipient(int function) {
        this.function = function;
    }

    public BinderDeathRecipient(String appName) {
        this.appName = appName;
    }

    @Override
    public void binderDied() {
        Log.d(TAG, "binderDied appName: " + appName);
        if (appName != null) {
            restartModuleServer(appName);
        }
    }

    private void restartModuleServer(String appName) {
        //TODO: Try to restart module server
        Log.d(TAG, "Restart app : " + appName);
        if (TextUtils.isEmpty(appName)) {
            return;
        }

        switch (appName) {
            case Definition.DAEMON_PACKAGE_NAME:
                startDaemonService();
                break;
            default:
                SurfaceShareManager.getInstance().onDisconnect(appName);
                break;
        }

    }

    private void startDaemonService() {
        Log.d(TAG, "Restart daemon service");
        Context context = ApplicationWrapper.getContext();
        Intent intent = new Intent(context, DaemonService.class);
        context.startService(intent);
    }

}
