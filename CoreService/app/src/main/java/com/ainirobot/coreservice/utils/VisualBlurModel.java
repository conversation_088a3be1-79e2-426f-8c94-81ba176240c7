package com.ainirobot.coreservice.utils;

import android.util.Log;

import com.ainirobot.coreservice.client.listener.Person;

/**
 * Created by Orion on 2020/1/15.
 * 视觉模糊模型
 * RGBD 测距偏差较大,角度准确性较高
 * 原则:模糊结果只能降低距离,不能增大
 */
public class VisualBlurModel {
    private final String TAG = this.getClass().getSimpleName();

    private VisualBlurModel() {

    }

    private static final class SingletonHolder {
        private static VisualBlurModel instance = new VisualBlurModel();
    }

    public static VisualBlurModel getInstance() {
        return SingletonHolder.instance;
    }

    private volatile double mLastEffectiveDistance = 0;
    private volatile int mLastEffectiveAngle = 0;
    private volatile long mLastEffectiveTime;

    public Person getBlurPerson(Person person) {
        if (person == null) {
            throw new NullPointerException("Person can not be null");
        }

        Person blurPerson = person;
        //非法距离,直接过滤掉影响跟随流畅度
//        if (blurPerson.getDistance() >= 10) {
        if (blurPerson.getDistance() > 4.8
                || Math.abs(blurPerson.getAngle()) > 60
                || (Math.abs(blurPerson.getAngle()) > 40 && blurPerson.getDistance() > 1.0)) {
            if (mLastEffectiveDistance < 1.0) { //导航最小距离
                blurPerson.setDistance(0.0);
                return blurPerson;
            } else {
                return null;
            }
        }

        //连续两个有效帧且时间间隔小于1s，向上最大跳变距离0.6m
        if ((System.currentTimeMillis() - mLastEffectiveTime) < 1 * 1000
                && (blurPerson.getDistance() - mLastEffectiveDistance) > 0.6) {
            if ((mLastEffectiveDistance > 1.0 && mLastEffectiveDistance < 3.0)
                    && (blurPerson.getDistance() > 1.0 && blurPerson.getDistance() < 3.0)) {
                blurPerson.setDistance(mLastEffectiveDistance); //TODO 优化近距离绕圈跟丢问题，边缘数据跳变被过滤掉了
                return blurPerson;
            }
            Log.i(TAG, "Throw away large distance : " + blurPerson.getDistance() + ", angle : " + blurPerson.getAngle());
            return null;
        } else if ((System.currentTimeMillis() - mLastEffectiveTime) < 2 * 1000
                && (blurPerson.getDistance() - mLastEffectiveDistance) > 1.2) {
            Log.i(TAG, "Throw away large distance : " + blurPerson.getDistance() + ", angle : " + blurPerson.getAngle());
            return null;
        } else if ((System.currentTimeMillis() - mLastEffectiveTime) < 3 * 1000
                && (blurPerson.getDistance() - mLastEffectiveDistance) > 1.8) {
            Log.i(TAG, "Throw away large distance : " + blurPerson.getDistance() + ", angle : " + blurPerson.getAngle());
            return null;
        }

        mLastEffectiveDistance = blurPerson.getDistance();
        mLastEffectiveTime = System.currentTimeMillis();

        //距离纠偏,临界距离越小存在偏差的概率越小,4.8m以上异常数据可纠正，但1m左右的严重跳变是算法计算问题并非误差
//        if (blurPerson.getDistance() <= 4.8) {
//            mLastEffectiveDistance = blurPerson.getDistance();
//            mLastEffectiveAngle = blurPerson.getAngle();
//        } else {
//            blurPerson.setDistance(mLastEffectiveDistance);
//            blurPerson.setAngle(mLastEffectiveAngle);
//        }
        return blurPerson;
    }

}
