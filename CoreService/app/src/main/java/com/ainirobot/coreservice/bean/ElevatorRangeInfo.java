package com.ainirobot.coreservice.bean;

/**
 * Data: 2022/8/17 18:31
 * Author: wanglijing
 * Description: ElevatorRangeInfo
 */
public class ElevatorRangeInfo {
    private String elevatorName;
    private double toFront; // 单位：米
    private double toBack;  // 单位：米
    private double toLeft;  // 单位：米
    private double toRight; // 单位：米

    public String getElevatorName() {
        return elevatorName;
    }

    public void setElevatorName(String elevatorName) {
        this.elevatorName = elevatorName;
    }

    public double getToFront() {
        return toFront;
    }

    public void setToFront(double toFront) {
        this.toFront = toFront;
    }

    public double getToBack() {
        return toBack;
    }

    public void setToBack(double toBack) {
        this.toBack = toBack;
    }

    public double getToLeft() {
        return toLeft;
    }

    public void setToLeft(double toLeft) {
        this.toLeft = toLeft;
    }

    public double getToRight() {
        return toRight;
    }

    public void setToRight(double toRight) {
        this.toRight = toRight;
    }

    @Override
    public String toString() {
        return "{" +
                "elevatorName='" + elevatorName + '\'' +
                ", toFront=" + toFront +
                ", toBack=" + toBack +
                ", toLeft=" + toLeft +
                ", toRight=" + toRight +
                '}';
    }
}
