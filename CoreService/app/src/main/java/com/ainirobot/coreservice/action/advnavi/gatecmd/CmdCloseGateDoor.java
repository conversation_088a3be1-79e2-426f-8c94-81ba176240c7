package com.ainirobot.coreservice.action.advnavi.gatecmd;

import android.util.Log;

import com.ainirobot.coreservice.client.Definition;

/**
 * Date: 2024/2/22
 * Author: dengting
 * <p>
 * 关闭 闸机
 * * 成功 上报RESULT_OK
 * * 失败 不需要处理
 * * 超时 不需要处理
 */
public class CmdCloseGateDoor extends GateCommand {

   private CmdCallGate cmdCallGate;

    CmdCloseGateDoor(String name, CmdType intentType, GateCommandInterface commandInterface, GateCommandListener listener) {
        super(name, intentType, commandInterface, listener);
        initCmdType(CmdType.CLOSE_DOOR);

    }


    @Override
    public void start() {
        super.start();
        closeGateDoor();
    }

    @Override
    public void stop() {
        super.stop();
        if (null != cmdCallGate && cmdCallGate.isAlive) {
            cmdCallGate.stop();
        }
    }

    @Override
    public void cmdResponse(String command, String result, String extraData) {
        super.cmdResponse(command, result, extraData);
        if (null != cmdCallGate) {
            cmdCallGate.cmdResponse(command, result, extraData);
        }
    }

    @Override
    public void onSuccess() {
        super.onSuccess();
        onResult(Definition.RESULT_OK, "close door suc");
    }

    @Override
    public void onFail() {
        super.onFail();
        if (null == cmdCallGate) {
            cmdCallGate = new CmdCallGate(TAG, intentType, commandInterface, callListener);
        }
        Log.d(TAG, "close door fail, call gate");
        cmdCallGate.start();
    }

    private final GateCommandListener callListener = new GateCommandListener() {
        @Override
        public void onStatusUpdate(int status, String data, String extraData) {
            onCmdStatusUpdate(status, data, extraData);
        }

        @Override
        public void onFinish(int status, String data) {
            if (status == Definition.RESULT_OK) {
                Log.d(TAG, "call gate suc, close door");
                closeGateDoor();
                return;
            }
            onResult(status, data);
        }
    };

    @Override
    protected void onRetry() {
        closeGateDoor();
    }

}
