/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.client.messagedispatcher;

import android.support.annotation.NonNull;

import java.util.ArrayDeque;
import java.util.concurrent.locks.ReentrantLock;

class RecyclablePool<T extends IRecyclable> {
    private ArrayDeque<T> mPool = new ArrayDeque<>();
    private ReentrantLock mLock = new ReentrantLock();

    T obtain() {
        T result;
        mLock.lock();
        try {
            result = mPool.poll();
        } finally {
            mLock.unlock();
        }
        return result;
    }

    void recycle(@NonNull T recycleItem) {
        recycleItem.recycle();
        mLock.lock();
        try {
            if (!mPool.contains(recycleItem)) {
                mPool.add(recycleItem);
            }
        } finally {
            mLock.unlock();
        }
    }
}
