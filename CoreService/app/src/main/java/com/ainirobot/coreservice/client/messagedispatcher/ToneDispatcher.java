/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.client.messagedispatcher;

import com.ainirobot.coreservice.client.listener.ToneListener;
import com.ainirobot.coreservice.listener.IToneListener;

public class ToneDispatcher extends IToneListener.Stub implements IRecyclable {
    private static final String TAG = ToneDispatcher.class.getSimpleName();
    private static RecyclablePool<ToneDispatcher> sPool = new RecyclablePool<>();

    private DispatchHandler mDispatchHandler;
    private ToneListener mListener;

    private ToneDispatcher(DispatchHandler dispatchHandler, ToneListener listener) {
        mDispatchHandler = dispatchHandler;
        mListener = listener;
    }

    static ToneDispatcher obtain(DispatchHandler dispatchHandler, ToneListener listener) {
        ToneDispatcher toneDispatcher = sPool.obtain();

        if (toneDispatcher == null) {
            toneDispatcher = new ToneDispatcher(dispatchHandler, listener);
        } else {
            toneDispatcher.mDispatchHandler = dispatchHandler;
            toneDispatcher.mListener = listener;
        }
        return toneDispatcher;
    }

    @Override
    public void onStart() {
        ToneListener listener = mListener;
        DispatchHandler handler = mDispatchHandler;
        if (listener != null && handler != null) {
            ToneMessage toneMessage = new ToneMessage(ToneMessage.TYPE_START, listener);
            handler.sendMessage(handler
                    .obtainMessage(MessageDispatcher.MSG_TONE, toneMessage));
        }
    }

    @Override
    public void onStop() {
        ToneListener listener = mListener;
        DispatchHandler handler = mDispatchHandler;
        if (listener != null && handler != null) {
            ToneMessage toneMessage = new ToneMessage(ToneMessage.TYPE_STOP, listener);
            handler.sendMessage(handler
                    .obtainMessage(MessageDispatcher.MSG_TONE, toneMessage));
        }
        sPool.recycle(this);
    }

    @Override
    public void onError() {
        ToneListener listener = mListener;
        DispatchHandler handler = mDispatchHandler;
        if (listener != null && handler != null) {
            ToneMessage toneMessage = new ToneMessage(ToneMessage.TYPE_ERROR, listener);
            handler.sendMessage(handler
                    .obtainMessage(MessageDispatcher.MSG_TONE, toneMessage));
        }
        sPool.recycle(this);
    }

    @Override
    public void onComplete() {
        ToneListener listener = mListener;
        DispatchHandler handler = mDispatchHandler;
        if (listener != null && handler != null) {
            ToneMessage toneMessage = new ToneMessage(ToneMessage.TYPE_COMPLETE, listener);
            handler.sendMessage(handler
                    .obtainMessage(MessageDispatcher.MSG_TONE, toneMessage));
        }
        sPool.recycle(this);
    }

    @Override
    public void recycle() {
        mDispatchHandler = null;
        mListener = null;
    }
}
