package com.ainirobot.coreservice.action.download_map.state;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.download_map.DownloadMapStateEnum;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.CloudPlaceBean;
import com.ainirobot.coreservice.client.actionbean.DownloadMapBean;
import com.ainirobot.coreservice.client.actionbean.PlaceBean;
import com.ainirobot.coreservice.utils.ZipUtils;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class GetCloudMapPlaceListState extends BaseDownloadMapPkgState {
    private DownloadMapBean downloadMapBean;
    private List<PlaceBean> placeBeanList;

    @Override
    protected void onStart(String paramsStr, DownloadMapBean downloadMapBean) {
        this.downloadMapBean = downloadMapBean;
        String mapUuid = downloadMapBean.getMapUuid();
        Log.d(TAG, "onStart: mapUuid = " + mapUuid);
        if (TextUtils.isEmpty(mapUuid)) {
            /**
             * 没有地图uuid，则获取不到地图分离点位，直接跳过。
             * 导入地图时uuid是从地图包中获取的，为空概率比较大。推送下载地图包时，uuid是从云端获取的，不为空。
             */
            onStateRunSuccess();
        }else {
            sendCommand(0, Definition.CMD_REMOTE_GET_PLACE_LIST, downloadMapBean.getMapName());
        }
    }

    @Override
    public boolean onCmdResponse(int cmdId, String command, String result, String extraData) {
        if (TextUtils.equals(command, Definition.CMD_REMOTE_GET_PLACE_LIST)) {
            handlerGetResult(result);
            return true;
        }
        return false;
    }

    private void handlerGetResult(String result) {
        try {
            Gson gson = new Gson();
            result = ZipUtils.unzipMapData(gson, result);
            JSONObject jsonObject = new JSONObject(result);
            String item = jsonObject.getJSONObject("data").getString("items");
            List<CloudPlaceBean> cloudPlaceBeanList = gson.fromJson(item,
                    new TypeToken<List<CloudPlaceBean>>() {
                    }.getType());
            if (null != cloudPlaceBeanList && !cloudPlaceBeanList.isEmpty()) {
                CloudPlaceBean cloudPlaceBean = cloudPlaceBeanList.get(0);
                List<JsonObject> placeList = cloudPlaceBean.getSites();
                placeBeanList = new ArrayList<>();
                for (JsonObject object : placeList) {
                    PlaceBean placeBean = new PlaceBean(object, downloadMapBean.getMapName());
//                    Log.d(TAG, "placeBean == " + placeBean.toString());
                    placeBeanList.add(placeBean);
                }
            }
            Log.d(TAG, " placeBeanList == " + new Gson().toJson(placeBeanList));
            onStateRunSuccess();
        } catch (Exception e) {
            e.printStackTrace();
            onStateRunFailed(0, "get cloud map is failed");
        }
    }

    @Override
    protected DownloadMapStateEnum getNextState() {
        if (placeListIsEmpty()) {
            return DownloadMapStateEnum.UPDATE_PLACE_LIST_TO_NET;
        }
        return DownloadMapStateEnum.UPDATE_PLACE_LIST_TO_NAVI;
    }

    @Override
    protected Object getData() {
        return placeListIsEmpty() ? null : placeBeanList;
    }

    private boolean placeListIsEmpty() {
        return placeBeanList == null || placeBeanList.size() == 0;
    }
}
