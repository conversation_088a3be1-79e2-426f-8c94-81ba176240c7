package com.ainirobot.coreservice.client.actionbean;

public class LedLightBean {
    protected int type;
    protected int target;
    protected int rgbStart;
    protected int rgbEnd;
    protected int startTime;
    protected int endTime;
    protected int onTime;
    protected int repeat;
    protected int rgbFreeze;
    protected int rgbValue;
    protected int[] rgbSet;
    protected int speed;
    protected int rgbNum;
    protected int singleNum;
    protected int[] rgbValueArray;
    protected int singleTime;
    protected int rgbBase;
    protected int rgbMove;
    protected int rgbMiddle;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getTarget() {
        return target;
    }

    public void setTarget(int target) {
        this.target = target;
    }

    public int getSpeed() {
        return speed;
    }

    public void setSpeed(int speed) {
        this.speed = speed;
    }

    public int getRgbStart() {
        return rgbStart;
    }

    public void setRgbStart(int rgbStart) {
        this.rgbStart = rgbStart;
    }

    public int getRepeat() {
        return repeat;
    }

    public void setRepeat(int repeat) {
        this.repeat = repeat;
    }

    public int getEndTime() {
        return endTime;
    }

    public void setEndTime(int endTime) {
        this.endTime = endTime;
    }

    public int getOnTime() {
        return onTime;
    }

    public void setOnTime(int onTime) {
        this.onTime = onTime;
    }

    public int getRgbBase() {
        return rgbBase;
    }

    public void setRgbBase(int rgbBase) {
        this.rgbBase = rgbBase;
    }

    public int getRgbEnd() {
        return rgbEnd;
    }

    public void setRgbEnd(int rgbEnd) {
        this.rgbEnd = rgbEnd;
    }

    public int getRgbFreeze() {
        return rgbFreeze;
    }

    public void setRgbFreeze(int rgbFreeze) {
        this.rgbFreeze = rgbFreeze;
    }

    public int getRgbMiddle() {
        return rgbMiddle;
    }

    public void setRgbMiddle(int rgbMiddle) {
        this.rgbMiddle = rgbMiddle;
    }

    public int getRgbMove() {
        return rgbMove;
    }

    public void setRgbMove(int rgbMove) {
        this.rgbMove = rgbMove;
    }

    public int getRgbNum() {
        return rgbNum;
    }

    public void setRgbNum(int rgbNum) {
        this.rgbNum = rgbNum;
    }

    public int getRgbValue() {
        return rgbValue;
    }

    public void setRgbValue(int rgbValue) {
        this.rgbValue = rgbValue;
    }

    public int getSingleNum() {
        return singleNum;
    }

    public void setSingleNum(int singleNum) {
        this.singleNum = singleNum;
    }

    public void setSingleTime(int singleTime) {
        this.singleTime = singleTime;
    }

    public int getSingleTime() {
        return singleTime;
    }

    public void setStartTime(int startTime) {
        this.startTime = startTime;
    }

    public int getStartTime() {
        return startTime;
    }

    public void setRgbValueArray(int[] rgbValueArray) {
        this.rgbValueArray = rgbValueArray;
    }

    public int[] getRgbValueArray() {
        return rgbValueArray;
    }

    public void setRgbSet(int[] rgbSet) {
        this.rgbSet = rgbSet;
    }

    public int[] getRgbSet() {
        return rgbSet;
    }
}