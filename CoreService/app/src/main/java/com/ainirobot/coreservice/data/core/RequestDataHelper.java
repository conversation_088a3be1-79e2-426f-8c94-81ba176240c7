/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.data.core;

import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import android.provider.BaseColumns;

import com.ainirobot.coreservice.core.module.ModuleManager.Permission;
import com.ainirobot.coreservice.data.provider.BaseDataHelper;
import com.ainirobot.coreservice.data.provider.Column;
import com.ainirobot.coreservice.data.provider.SQLiteTable;

import java.util.HashMap;
import java.util.Map;

public class RequestDataHelper extends BaseDataHelper {

    public static final SQLiteTable TABLE = new SQLiteTable(RequestField.TABLE_NAME,
            DataProvider.AUTHORITY, true) {
        @Override
        public void importData(Context context, SQLiteDatabase db) {
            DataImportUtils.importDataFromAsset(context, db, "Request.sql");
        }

        @Override
        public void update(Context context, SQLiteDatabase db, int oldVersion, int newVersion) {
            DataImportUtils.importDataFromAsset(context, db, "Request.sql");
        }
    };

    static {
        TABLE.addColumn(RequestField.TYPE, Column.Constraint.UNIQUE, Column.DataType.TEXT)
                .addColumn(RequestField.PERMISSION, Column.DataType.INTEGER);
    }

    public RequestDataHelper(Context context) {
        super(context);
    }

    @Override
    public Uri getContentUri() {
        return TABLE.getContentUri();
    }

    public Map<String, Permission> getRequestInfos() {
        Cursor cursor = query(null, null, null, null);
        Map<String, Permission> data = new HashMap<>();
        if (cursor == null) {
            return data;
        }
        while (cursor.moveToNext()) {
            String type = cursor.getString(cursor.getColumnIndex(RequestField.TYPE));
            int permission = cursor.getInt(cursor.getColumnIndex(RequestField.PERMISSION));
            data.put(type, Permission.fromValue(permission));
        }
        cursor.close();
        return data;
    }

    public static final class RequestField implements BaseColumns {
        static final String TABLE_NAME = "request";
        static final String TYPE = "type";
        static final String PERMISSION = "permission";
    }
}
