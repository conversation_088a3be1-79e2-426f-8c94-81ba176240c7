package com.ainirobot.coreservice.daemon;

import android.app.ActivityManager;
import android.app.ActivityManager.RunningAppProcessInfo;
import android.app.ActivityManager.RunningServiceInfo;
import android.app.ActivityManager.RunningTaskInfo;
import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.IBinder;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.bi.BiReportService;
import com.ainirobot.coreservice.bi.report.ModuleAppCrashReport;
import com.ainirobot.coreservice.client.ApiListener;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.IntentUtil;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.module.ModuleCallbackApi;
import com.ainirobot.coreservice.client.upload.bi.BiReport;
import com.ainirobot.coreservice.dump.DataDumpService;
import com.ainirobot.coreservice.utils.DelayTask;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 守护进程
 */
public class DaemonService extends Service {
    private final String TAG = "DaemonService";

    private static final String PROCESS_PERFORMANCE = Definition.CORE_PACKAGE_NAME + ":Performance";
    private static final String PROCESS_BI = Definition.CORE_PACKAGE_NAME + ":BiReport";
    private static final String PROCESS_DUMP = Definition.CORE_PACKAGE_NAME + ":DataDump";
    private static final String PROCESS_HOME = Definition.HOME_PACKAGE_NAME;

    /**
     * 进程检测间隔
     */
    private final long PROCESS_INTERVAL = 10000;

    /**
     * 权限检查间隔
     */
    private final long PERMISSION_INTERVAL = 2 * 1000;

    /**
     * 重启App延迟时间
     */
    private final long RESTART_DELAY = 1000;

    private final String RESTART_MODULEAPP = "restart_moduleapp";
    private final String MODULEAPP_SERVICE = "ModuleService";

    private SystemApi mApi;
    private String mDaemonApp;
    private List<String> mDaemonList = new ArrayList<String>() {{
        add(Definition.CORE_PACKAGE_NAME);
        add(PROCESS_PERFORMANCE);
        add(PROCESS_BI);
        add(PROCESS_DUMP);
    }};

    private ModuleAppCrashReport mBI = new ModuleAppCrashReport();

    @Override
    public void onCreate() {
        super.onCreate();

        Log.d(TAG, "Daemon service start");
        mApi = SystemApi.getInstance();

        BiReport.init();

        connectCore();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        return START_STICKY;
    }

    @Override
    public IBinder onBind(Intent intent) {
        throw new UnsupportedOperationException("Not yet implemented");
    }

    /**
     * 连接CoreService
     */
    private void connectCore() {
        Log.d(TAG, "Connect to core service");
        mApi.connect(this, new ApiListener() {
            @Override
            public void handleApiDisabled() {

            }

            @Override
            public void handleApiConnected() {
                Log.d(TAG, "Handle api connected : DaemonService");
                DelayTask.cancel(TAG);

                //注册Binder，用于CoreService对守护进程的保活
                mApi.setCallback(new ModuleCallbackApi());
                mDaemonApp = getDefaultApp();
                mDaemonList.add(mDaemonApp);
                startProcessDaemonTask();
                startPermissionCheckTask();
            }

            @Override
            public void handleApiDisconnected() {
                Log.d(TAG, "Handle api disconnected : DaemonService");
                reconnect();
            }
        });

        reconnect();
    }

    /**
     * 重连CoreService
     */
    private void reconnect() {
        Log.d(TAG, "Reconnect to core service");
        DelayTask.submit(TAG, new Runnable() {
            @Override
            public void run() {
                if (mApi.isApiConnectedService()) {
                    Log.d(TAG, "The core service is connected");
                    return;
                }
                connectCore();
            }
        }, 3000);
    }

    /**
     * 延迟检测进程信息
     */
    private void startProcessDaemonTask() {
        Log.d(TAG, "Start delay detect process : " + PROCESS_INTERVAL);
        DelayTask.submit(TAG, new Runnable() {
            @Override
            public void run() {
                Map<String, RunningAppProcessInfo> map = queryProcessInfo();

                Log.d(TAG, "Start query process : " + map);
                if (map == null) {
                    Log.d(TAG, "Get ActivityManager failed");
                    //TODO: 获取ActivityManager失败，处理策略待定
                    return;
                }

                if (!map.containsKey(PROCESS_PERFORMANCE)) {
                    startPerformanceService();
                }

                if (!map.containsKey(PROCESS_BI)) {
                    startBiReportService();
                }

                if (!map.containsKey(PROCESS_DUMP)) {
                    startDumpService();
                }

                if (!map.containsKey(mDaemonApp)) {
                    if (isRestartApp(mDaemonApp)) {
//                        delayRestartModuleApp();
                        restartApp(mDaemonApp);
                    }
                    return;
                }

                //判断Activity 或 Service 是否存活，其中一个不存活则kill后重启
                if (!isActivityAlive(mDaemonApp)) {
                    if (isRestartApp(mDaemonApp)) {
                        killApp(mDaemonApp);
                        restartApp(mDaemonApp);
                    }
                }

            }
        }, PROCESS_INTERVAL, PROCESS_INTERVAL);
    }

    /**
     * 权限检测
     */
    private void startPermissionCheckTask() {
        DelayTask.submit(TAG, new Runnable() {
            @Override
            public void run() {
                if (!isPermissionMatch()) {
                    ComponentName topActivity = getTopActivity();
                    if (topActivity != null
                            && mDaemonApp.equals(topActivity.getPackageName())) {
                        //当前在权限不匹配的情况下，只对默认App做重授权操作
                        Log.d(TAG, "Start app control for ModuleApp");
                        mApi.startAppControl(mDaemonApp);
                    }
                }
            }
        }, PERMISSION_INTERVAL, PERMISSION_INTERVAL);
    }


    /**
     * 启动性能监控Service
     */
    private void startPerformanceService() {
        Log.d(TAG, "Start performance service");
        Intent intent = new Intent(this, PerformanceService.class);
        startService(intent);
    }

    /**
     * 启动埋点上报Service
     */
    private void startBiReportService() {
        Log.d(TAG, "Start performance service");
        Intent intent = new Intent(this, BiReportService.class);
        startService(intent);
    }

    /**
     * 启动快照Service
     */
    private void startDumpService() {
        Log.d(TAG, "Start dump service");
        Intent intent = new Intent(this, DataDumpService.class);
        startService(intent);
    }

    /**
     * 权限是否匹配
     *
     * @return
     */
    private boolean isPermissionMatch() {
        ComponentName topActivity = getTopActivity();
        if (topActivity == null) {
            return true;
        }

        String top = topActivity.getPackageName();
        String current = mApi.getActiveApp();

        Log.d(TAG, "Check permission, top : " + top + "  current : " + current);

        //栈顶的Activity与CoreService内部保存的一致
        if (top.equals(current)) {
            return true;
        }

        String systemStatus = mApi.getSystemStatus();
        Log.d(TAG, "Check permission, systemStatus : " + systemStatus);
        //不一致的情况下，判断是否在进行自检或ota等系统事件
        if (!TextUtils.isEmpty(systemStatus)) {
            return true;
        }

        return false;
    }

    /**
     * 判断是否重启ModuleApp
     *
     * @return
     */
    private boolean isRestartApp(String packageName) {
        if (TextUtils.isEmpty(packageName)) {
            return false;
        }

        if (getDisableRestartAppSwitch()) {
            return false;
        }

        ComponentName topActivity = getTopActivity();
        if (topActivity != null
                && Definition.SETTINGS_PACKAGE_NAME.equals(topActivity.getPackageName())) {
            //当前显示为Settings, 暂缓重启ModuleApp
            Log.d(TAG, "Current is settings, not restart ModuleApp");
            return false;
        }

        //当前处于自检或OTA中，暂缓重启ModuleApp
        String systemStatus = mApi.getSystemStatus();
        if (!TextUtils.isEmpty(mApi.getSystemStatus())) {
            Log.d(TAG, "Current is " + systemStatus + " , not restart ModuleApp");
            return false;
        }

        String activeApp = mApi.getActiveApp();
        if (!TextUtils.isEmpty(activeApp) && !packageName.equals(activeApp)) {
            Log.d(TAG, "Current active app is " + activeApp + " , not allowed to restart ModuleApp");
            return false;
        }

        return true;
    }


    private void killApp(String packageName) {
        Log.d(TAG, "Kill module app");
        ActivityManager am = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
        try {
            Method forceStopPackage = am.getClass().getDeclaredMethod("forceStopPackage", String.class);
            forceStopPackage.setAccessible(true);
            forceStopPackage.invoke(am, packageName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 延迟1s启动ModuleApp
     */
    private void delayRestartModuleApp() {
        Log.d(TAG, "Delay restart module app : " + RESTART_DELAY);
        DelayTask.cancel(RESTART_MODULEAPP);
        DelayTask.submit(RESTART_MODULEAPP, new Runnable() {
            @Override
            public void run() {
                if (restartApp(mDaemonApp)) {
                    //埋点
                    mBI.addState(ModuleAppCrashReport.STATE_ABNORMAL);
                    String uuid = UUID.randomUUID().toString();
                    mBI.addCrashId(uuid);
                    mBI.addType(ModuleAppCrashReport.TYPE_RESTART);
                    mBI.report();
                }
            }
        }, RESTART_DELAY);
    }

    /**
     * 重启ModuleApp
     */
    private boolean restartApp(String packageName) {
        Log.d(TAG, "Restart module app");
        //重启之前，再次查询确认进程是否已经启动
        if (isActivityAlive(packageName)) {
            Log.d(TAG, "ModuleApp process already exists");
            return false;
        }

        PackageManager pm = this.getPackageManager();
        Intent intent = pm.getLaunchIntentForPackage(packageName);
        if (intent != null) {
            startActivity(intent);

            if (mDaemonApp.equals(Definition.MODULE_PACKAGE_NAME)) {
                mBI.addState(ModuleAppCrashReport.STATE_ABNORMAL);
                String uuid = UUID.randomUUID().toString();
                mBI.addCrashId(uuid);
                mBI.addType(ModuleAppCrashReport.TYPE_RESTART);
                mBI.report();
            }
            return true;
        }
        return false;
    }

    /**
     * 检测是否有Activity存活
     *
     * @param pkg 包名
     * @return
     */
    private boolean isActivityAlive(String pkg) {
        ActivityManager am = (ActivityManager) this.getSystemService(Context.ACTIVITY_SERVICE);
        if (am == null || TextUtils.isEmpty(pkg)) {
            return false;
        }

        try {
            List<ActivityManager.RunningTaskInfo> list = am
                    .getRunningTasks(Integer.MAX_VALUE);
            for (RunningTaskInfo taskInfo : list) {
                if (pkg.equals(taskInfo.topActivity.getPackageName())) {
                    return true;
                }
            }
        } catch (SecurityException e) {
            e.printStackTrace();
        }

        Log.d(TAG, "Activity is not exit : " + pkg);
        return false;
    }

    public boolean isServiceRunning(String pkg, String name) {
        Context context = ApplicationWrapper.getContext();
        ActivityManager manager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        if (manager == null || TextUtils.isEmpty(pkg) || TextUtils.isEmpty(name)) {
            return false;
        }
        try {
            List<RunningServiceInfo> list = manager.getRunningServices(Integer.MAX_VALUE);
            if (list != null && list.size() > 0) {
                for (RunningServiceInfo info : list) {
                    if (name.equals(info.service.getClassName())
                            && pkg.equals(info.service.getPackageName())) {
                        return true;
                    }
                }
            }
        } catch (SecurityException e) {
            e.printStackTrace();
        }

        Log.d(TAG, "Service is not running : " + pkg + "   " + name);
        return false;
    }

    /**
     * 查询被守护进程信息
     *
     * @return
     */
    private Map<String, RunningAppProcessInfo> queryProcessInfo() {
        ActivityManager am = (ActivityManager) this.getSystemService(Context.ACTIVITY_SERVICE);
        if (am == null) {
            return null;
        }

        Map<String, RunningAppProcessInfo> map = new HashMap<>();
        List<RunningAppProcessInfo> processes = am.getRunningAppProcesses();
        for (RunningAppProcessInfo processInfo : processes) {
            String processName = processInfo.processName;
            if (mDaemonList.contains(processInfo.processName)) {
                Log.d(TAG, "ProcessName : " + processName + "  " + processInfo.pid);
                map.put(processName, processInfo);
            }
        }
        return map;
    }

    /**
     * 获取栈顶Activity
     *
     * @return
     */
    public ComponentName getTopActivity() {
        ActivityManager manager = (ActivityManager) this.getSystemService(Context.ACTIVITY_SERVICE);
        try {
            List<ActivityManager.RunningTaskInfo> list = manager
                    .getRunningTasks(1);
            if (list != null && list.size() > 0) {
                return list.get(0).topActivity;
            }
        } catch (SecurityException e) {
            e.printStackTrace();
        }
        return null;
    }

    private String getDefaultApp() {
        String defaultApp = RobotSettingApi.getInstance().
                getRobotString(Definition.BOOT_APP_PACKAGE_NAME);
        return TextUtils.isEmpty(defaultApp) ? Definition.MODULE_PACKAGE_NAME : defaultApp;
    }

    private boolean getDisableRestartAppSwitch() {
        int value = RobotSettingApi.getInstance().
                getRobotInt(Definition.ROBOT_SETTING_DAEMON_SERVICE_DISABLE_RESTART_APP);
        return value == Definition.ROBOT_SETTING_ENABLE;
    }

}
