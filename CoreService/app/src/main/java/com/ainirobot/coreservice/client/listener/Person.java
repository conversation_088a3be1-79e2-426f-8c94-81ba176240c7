package com.ainirobot.coreservice.client.listener;

import com.ainirobot.coreservice.client.account.MemberBean;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by orion on 2018/4/12.
 */

public class Person {
    private String name;
    private int id;
    private int angle;
    private double distance;
    private double faceAngleX;
    private double faceAngleY;
    private int headSpeed; // head speed;
    private int latency;
    private int facewidth;
    private int faceheight;
    private int faceX;
    private int faceY;
    private int bodyX;
    private int bodyY;
    private int bodywidth;
    private int bodyheight;
    private double angleInView;
    private String userId;
    private boolean isStaff;
    private String gender;
    private int age;
    private int glasses;
    private String role;
    private int role_id;
    private String remoteReqId;
    private String remoteFaceId;
    private String faceRegisterTime;
    private boolean with_body;
    private boolean with_face;
    private String avatar_url;
    private String staff_mobile;
    private String staff_job;
    private String staff_dept;
    private boolean other_face;
    private long timestamp = System.currentTimeMillis();
    private boolean fake_face;
    private String remoteWakeupId;
    private boolean isNewUser;
    private CoatColor coat_color;
    /**
     * 质量
     * 是否有口罩: 0: normal 正常，1: light 高光，2: blur 模糊，3: hide 遮挡
     */
    private String quality;
    private Remote remote;
    private KeyPoints keyPoints;
    private double mouthmove_score;
    private int mouthstate;
    private double ukfBodyVel;//目标沿径向运动的线速度，远离为正
    private double ukfBodyOmega;//目标绕机器人转动的角速率，顺时针为正
    private MemberBean member;

    private int liveNess;

    public boolean isLiveNess() {
        return liveNess != 0;
    }

    public double getUkfBodyVel() {
        return ukfBodyVel;
    }

    public void setUkfBodyVel(double ukfBodyVel) {
        this.ukfBodyVel = ukfBodyVel;
    }

    public double getUkfBodyOmega() {
        return ukfBodyOmega;
    }

    public void setUkfBodyOmega(double ukfBodyOmega) {
        this.ukfBodyOmega = ukfBodyOmega;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getAngle() {
        return angle;
    }

    public void setAngle(int angle) {
        this.angle = angle;
    }

    public double getDistance() {
        return distance;
    }

    public void setDistance(double distance) {
        this.distance = distance;
    }

    public double getFaceAngleX() {
        return faceAngleX;
    }

    public void setFaceAngleX(double faceAngleX) {
        this.faceAngleX = faceAngleX;
    }

    public double getFaceAngleY() {
        return faceAngleY;
    }

    public void setFaceAngleY(double faceAngleY) {
        this.faceAngleY = faceAngleY;
    }

    public double getAngleInView() {
        return angleInView;
    }

    public void setAngleInView(double angleInView) {
        this.angleInView = angleInView;
    }

    public int getHeadSpeed() {
        return headSpeed;
    }

    public void setHeadSpeed(int headSpeed) {
        this.headSpeed = headSpeed;
    }

    public int getLatency() {
        return latency;
    }

    public void setLatency(int latency) {
        this.latency = latency;
    }

    public void setLiveNess(int liveNess) {
        this.liveNess = liveNess;
    }

    public String toGson() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public boolean isStaff() {
        return isStaff;
    }

    public void setStaff(boolean staff) {
        isStaff = staff;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }
    public int getGlasses() {
        return glasses;
    }

    public void setGlasses(int glasses) {
        this.glasses = glasses;
    }

    public int getRoleId() {
        return role_id;
    }

    public void setRoleId(int role_id) {
        this.role_id = role_id;
    }

    public String getAvatarUrl() {
        return avatar_url;
    }

    public void setAvatarUrl(String avatar_url) {
        this.avatar_url = avatar_url;
    }

    public String getStaffMobile() {
        return staff_mobile;
    }

    public void setStaffMobile(String staff_mobile) {
        this.staff_mobile = staff_mobile;
    }

    public String getStaffJob() {
        return staff_job;
    }

    public void setStaffJob(String staff_job) {
        this.staff_job = staff_job;
    }

    public String getStaffDept() {
        return staff_dept;
    }

    public void setStaffDept(String staff_dept) {
        this.staff_dept = staff_dept;
    }

    public String getRemoteFaceId() {
        return remoteFaceId;
    }

    public void setRemoteFaceId(String remoteFaceId) {
        this.remoteFaceId = remoteFaceId;
    }

    public void setUserRegisterTime(String registerTime) {
        this.faceRegisterTime = registerTime;
    }

    public String getUserRegisterTime() {
        return faceRegisterTime;
    }

    public String getRemoteReqId() {
        return remoteReqId;
    }

    public void setRemoteReqId(String remoteReqId) {
        this.remoteReqId = remoteReqId;
    }

    public String getRemoteWakeupId() {
        return remoteWakeupId;
    }

    public void setRemoteWakeupId(String remoteWakeupId) {
        this.remoteWakeupId = remoteWakeupId;
    }

    public List<WelcomeAction> mWelcomeActions;

    private List<Config> configs;

    public List<Config> getConfigs() {
        return configs;
    }

    public void setConfigs(List<Config> configs) {
        this.configs = configs;
    }

    public Config obtainNewConfig() {
        return new Config();
    }

    public static class Config {
        private String type;
        private ConfigDetail config;

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public ConfigDetail getConfig() {
            return config;
        }

        public void setConfig(ConfigDetail config) {
            this.config = config;
        }
    }

    public static class ConfigDetail {
        private String configId;
        private String configVersion;
        private String lang;
        private String rangeType;

        public String getConfigId() {
            return configId;
        }

        public void setConfigId(String configId) {
            this.configId = configId;
        }

        public String getConfigVersion() {
            return configVersion;
        }

        public void setConfigVersion(String configVersion) {
            this.configVersion = configVersion;
        }

        public String getLang() {
            return lang;
        }

        public void setLang(String lang) {
            this.lang = lang;
        }

        public String getRangeType() {
            return rangeType;
        }

        public void setRangeType(String rangeType) {
            this.rangeType = rangeType;
        }
    }
    public class WelcomeAction {
        String action;
        String value;
        String recommend;

        int isConfirm = 0;
        String confirmTTS;
        String helloTTS;

        public String getAction() {
            return action;
        }

        public void setAction(String action) {
            this.action = action;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public String getRecommend() {
            return recommend;
        }

        public void setRecommend(String recommend) {
            this.recommend = recommend;
        }

        public int getIsConfirm() {
            return isConfirm;
        }

        public void setIsConfirm(int isConfirm) {
            this.isConfirm = isConfirm;
        }

        public String getConfirmTTS() {
            return confirmTTS;
        }

        public void setConfirmTTS(String confirmTTS) {
            this.confirmTTS = confirmTTS;
        }

        public String getHelloTTS() {
            return helloTTS;
        }

        public void setHelloTTS(String helloTTS) {
            this.helloTTS = helloTTS;
        }
    }

    public WelcomeAction obtainNewWelcomeAction() {
        return new WelcomeAction();
    }

    public void setWelcomeActions(ArrayList<WelcomeAction> actions) {
        mWelcomeActions = actions;
    }

    public List<WelcomeAction> getWelcomeActions() {
        return mWelcomeActions;
    }

    public boolean isWithBody() {
        return with_body;
    }

    public void setWithBody(boolean withBody) {
        this.with_body = withBody;
    }

    public boolean isWithFace() {
        return with_face && isLiveNess();
    }

    public void setWithFace(boolean withFace) {
        this.with_face = withFace;
    }

    public boolean isOtherFace() {
        return other_face;
    }

    public void setOtherFace(boolean other_face) {
        this.other_face = other_face;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public boolean isFakeFace() {
        return fake_face;
    }

    public void setFakeFace(boolean fakeFace) {
        this.fake_face = fakeFace;
    }

    public boolean isNewUser() {
        return isNewUser;
    }

    public void setNewUser(boolean newUser) {
        isNewUser = newUser;
    }

    @Override
    public String toString() {
        return toGson();
    }

    public String getQuality() {
        return quality;
    }

    public void setQuality(String quality) {
        this.quality = quality;
    }

    public Remote obtainRemote() {
        return new Remote();
    }

    public void setRemote(Remote remote) {
        this.remote = remote;
    }

    public Remote getRemote() {
        return remote;
    }

    /**
     * 服务端识别结果
     */
    public static class Remote {
        private String user_id;
        private boolean user_is_new;
        private String face_id;
        private long user_register_time;
        private String name;
        private String staff_mobile;
        private String staff_job;
        private String staff_dept;
        private int is_staff;
        private String identity;
        private String role;
        private String status;
        private int role_id;
        private String avatar_url;
        private int quality = -2;
        private int age;
        private String gender;
        private int glasses;
        private String reqId;
        private String wakeupId;

        public String getUser_id() {
            return user_id;
        }

        public void setUser_id(String user_id) {
            this.user_id = user_id;
        }

        public boolean isUser_is_new() {
            return user_is_new;
        }

        public void setUser_is_new(boolean user_is_new) {
            this.user_is_new = user_is_new;
        }

        public String getFace_id() {
            return face_id;
        }

        public void setFace_id(String face_id) {
            this.face_id = face_id;
        }

        public long getUser_register_time() {
            return user_register_time;
        }

        public void setUser_register_time(long user_register_time) {
            this.user_register_time = user_register_time;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getStaff_mobile() {
            return staff_mobile;
        }

        public void setStaff_mobile(String staff_mobile) {
            this.staff_mobile = staff_mobile;
        }

        public String getStaff_job() {
            return staff_job;
        }

        public void setStaff_job(String staff_job) {
            this.staff_job = staff_job;
        }

        public String getStaff_dept() {
            return staff_dept;
        }

        public void setStaff_dept(String staff_dept) {
            this.staff_dept = staff_dept;
        }

        public int getIs_staff() {
            return is_staff;
        }

        public void setIs_staff(int is_staff) {
            this.is_staff = is_staff;
        }

        public String getIdentity() {
            return identity;
        }

        public void setIdentity(String identity) {
            this.identity = identity;
        }

        public String getRole() {
            return role;
        }

        public void setRole(String role) {
            this.role = role;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public int getRole_id() {
            return role_id;
        }

        public void setRole_id(int role_id) {
            this.role_id = role_id;
        }

        public String getAvatar_url() {
            return avatar_url;
        }

        public void setAvatar_url(String avatar_url) {
            this.avatar_url = avatar_url;
        }

        public int getQuality() {
            return quality;
        }

        public void setQuality(int quality) {
            this.quality = quality;
        }

        public int getAge() {
            return age;
        }

        public void setAge(int age) {
            this.age = age;
        }

        public String getGender() {
            return gender;
        }

        public void setGender(String gender) {
            this.gender = gender;
        }

        public int getGlasses() {
            return glasses;
        }

        public void setGlasses(int glasses) {
            this.glasses = glasses;
        }

        public String getReqId() {
            return reqId;
        }

        public void setReqId(String reqId) {
            this.reqId = reqId;
        }

        public String getWakeupId() {
            return wakeupId;
        }

        public void setWakeupId(String wakeupId) {
            this.wakeupId = wakeupId;
        }

        public String toGson() {
            Gson gson = new Gson();
            return gson.toJson(this);
        }

        @Override
        public String toString() {
            return toGson();
        }
    }

    public KeyPoints getKeyPoints() {
        return keyPoints;
    }

    public class KeyPoints {
        private List<Integer> keyPointsX;
        private List<Integer> keyPointsY;

        public List<Integer> getKeyPointsX() {
            return keyPointsX;
        }

        public List<Integer> getKeyPointsY() {
            return keyPointsY;
        }
    }

    public CoatColor getCoatColor() {
        return coat_color;
    }

    public class CoatColor {
        private String name;
        private double hue;
        private double lightness;
        private double saturation;
        private double confidence;

        public String getName() {
            return name;
        }

        public double getHue() {
            return hue;
        }

        public double getLightness() {
            return lightness;
        }

        public double getSaturation() {
            return saturation;
        }

        public double getConfidence() {
            return confidence;
        }
    }

    public double getMouthMoveScore() {
        return mouthmove_score;
    }

    public void setMouthMoveScore(double score) {
        this.mouthmove_score = score;
    }

    public int getMouthState() {
        return mouthstate;
    }

    public void setMouthState(int state) {
        this.mouthstate = state;
    }

    public int getFaceHeight() {
        return faceheight;
    }

    public void setFaceHeight(int faceheight) {
        this.faceheight = faceheight;
    }

    public int getFaceWidth() {
        return facewidth;
    }

    public void setFaceWidth(int facewidth) {
        this.facewidth = facewidth;
    }

    public int getFaceX() {
        return faceX;
    }

    public void setFaceX(int faceX) {
        this.faceX = faceX;
    }

    public int getFaceY() {
        return faceY;
    }

    public void setFaceY(int faceY) {
        this.faceY = faceY;
    }

    public int getBodyHeight() {
        return bodyheight;
    }

    public void setBodyHeight(int bodyheight) {
        this.bodyheight = bodyheight;
    }

    public int getBodyWidth() {
        return bodywidth;
    }

    public void setBodyWidth(int bodywidth) {
        this.bodywidth = bodywidth;
    }

    public int getBodyX() {
        return bodyX;
    }

    public void setBodyX(int bodyX) {
        this.bodyX = bodyX;
    }

    public int getBodyY() {
        return bodyY;
    }

    public void setBodyY(int bodyY) {
        this.bodyY = bodyY;
    }

    public MemberBean getMember() {
        return member;
    }

    public void setMember(MemberBean member) {
        this.member = member;
    }
}
