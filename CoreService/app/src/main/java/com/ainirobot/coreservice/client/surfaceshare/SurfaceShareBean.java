package com.ainirobot.coreservice.client.surfaceshare;

public class SurfaceShareBean {

    private String name;
    private int priority;
    private String packageName;
    private boolean isUseToPreview;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPackageName() {
        return packageName;
    }

    void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public boolean getIsUseToPreview() {
        return isUseToPreview;
    }

    public void setUseToPreview(boolean useToPreview) {
        isUseToPreview = useToPreview;
    }

    @Override
    public String toString() {
        return "SurfaceShareBean {\n" +
                "    name: " + name + ",\n" +
                "    priority: " + priority + ",\n" +
                "    packageName: " + packageName + "\n" +
                "    isUseToPreview: " + isUseToPreview +
                "}";
    }
}
