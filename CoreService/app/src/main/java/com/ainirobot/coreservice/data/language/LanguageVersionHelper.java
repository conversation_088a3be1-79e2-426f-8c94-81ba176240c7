/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.data.language;

import android.content.ContentValues;
import android.content.Context;
import android.database.ContentObserver;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import android.provider.BaseColumns;

import com.ainirobot.coreservice.client.actionbean.LanguageVersionInfo;
import com.ainirobot.coreservice.data.provider.BaseDataHelper;
import com.ainirobot.coreservice.data.provider.Column.DataType;
import com.ainirobot.coreservice.data.provider.SQLiteTable;

/**
 * 系统语言版本号
 */
public class LanguageVersionHelper extends BaseDataHelper {
    private static final String TAG = "LanguageVersionHelper";

    private Context mContext;

    public static final SQLiteTable TABLE = new SQLiteTable(VersionFiled.TABLE_NAME,
            LanguageProvider.AUTHORITY, true) {
        @Override
        public void importData(Context context, SQLiteDatabase db) {
            LanguageImportUtils.initLanguageVersion(db,context);
        }

        @Override
        public void update(Context context, SQLiteDatabase db, int oldVersion, int newVersion) {
        }
    };

    static {
        TABLE.addColumn(VersionFiled.VERSION, DataType.INTEGER);
    }

    public LanguageVersionHelper(Context context) {
        super(context);
        this.mContext = context;
    }

    @Override
    public Uri getContentUri() {
        return TABLE.getContentUri();
    }

    private ContentValues getContentValues(LanguageVersionInfo info) {
        ContentValues values = new ContentValues();
        values.put(VersionFiled.VERSION, info.getVersion());
        return values;
    }

    public int getVersion() {
        LanguageVersionInfo versionInfo = getLanguageVersionInfo();
        return versionInfo != null ? versionInfo.getVersion() : 0;
    }

    private LanguageVersionInfo getLanguageVersionInfo() {
        LanguageVersionInfo versionInfo = new LanguageVersionInfo();
        Cursor cursor = query(null, null, null, null);
        if (cursor == null) {
            return versionInfo;
        }
        while (cursor.moveToNext()) {
            int version = cursor.getInt(cursor.getColumnIndex(VersionFiled.VERSION));
            versionInfo.setVersion(version);
        }
        cursor.close();
        return versionInfo;
    }

    public void registerListener(ContentObserver contentObserver) {
        mContext.getContentResolver().registerContentObserver(
                getContentUri(), true, contentObserver);
    }

    public static final class VersionFiled implements BaseColumns {
        static final String TABLE_NAME = "language_version";

        static final String VERSION = "version";
    }

}
