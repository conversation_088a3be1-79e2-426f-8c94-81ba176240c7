package com.ainirobot.coreservice.action.advnavi.state;

import com.ainirobot.coreservice.action.advnavi.bean.StateData;

/**
 * Data: 2022/8/8 14:55
 * Author: wanglijing
 * IdleState
 */
public class IdleState extends BaseState{


    IdleState(String name) {
        super(name);
    }

    @Override
    protected void preAction(StateData stateData, IStateListener listener) {
        super.preAction(stateData, listener);
    }

    private boolean isResultData(StateData data) {
        try {
            if (null != data) {
                return data.getResultCode() != 0;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    protected void doAction() {
    }

    @Override
    protected BaseState getNextState() {
        return null;
    }

}
