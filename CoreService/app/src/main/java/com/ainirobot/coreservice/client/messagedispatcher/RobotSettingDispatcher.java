/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.client.messagedispatcher;

import com.ainirobot.coreservice.listener.IRobotSettingListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingListener;

public class RobotSettingDispatcher extends IRobotSettingListener.Stub implements IRecyclable {
    private DispatchHandler mDispatchHandler;
    private RobotSettingListener mListener;
    private static RecyclablePool<RobotSettingDispatcher> sPool = new RecyclablePool<>();

    private RobotSettingDispatcher(DispatchHand<PERSON> dispatchHandler, RobotSettingListener listener) {
        mDispatchHandler = dispatchHandler;
        mListener = listener;
    }

    static RobotSettingDispatcher obtain(DispatchHandler dispatchHandler, RobotSettingListener listener) {
        RobotSettingDispatcher robotSettingDispatcher = sPool.obtain();
        if (robotSettingDispatcher == null) {
            robotSettingDispatcher = new RobotSettingDispatcher(dispatchHandler, listener);
        } else {
            robotSettingDispatcher.mDispatchHandler = dispatchHandler;
            robotSettingDispatcher.mListener = listener;
        }
        return robotSettingDispatcher;
    }

    @Override
    public void recycle() {
        mDispatchHandler = null;
        mListener = null;
    }

    @Override
    public void onRobotSettingChanged(String key) {
        if (mListener != null && mDispatchHandler != null) {
            RobotSettingMessage message = new RobotSettingMessage(key, mListener);
            mDispatchHandler.sendMessage(
                    mDispatchHandler.obtainMessage(MessageDispatcher.MSG_SETTING, message));
        }
    }

    @Override
    public void onUnregister() {
        this.recycle();
    }
}
