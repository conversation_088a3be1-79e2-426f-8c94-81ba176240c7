/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.coreservice.server;

import android.os.RemoteException;

import com.ainirobot.coreservice.IPersonApi;
import com.ainirobot.coreservice.core.module.Module;
import com.ainirobot.coreservice.core.person.PersonManager;
import com.ainirobot.coreservice.core.person.bean.RequestBean;
import com.ainirobot.coreservice.listener.IActionListener;
import com.ainirobot.coreservice.listener.IPersonListener;
import com.ainirobot.coreservice.service.CoreService;
import com.google.gson.Gson;

import static com.ainirobot.coreservice.core.person.PersonManager.MSG_RECOGNIZE;
import static com.ainirobot.coreservice.core.person.PersonManager.MSG_REGISTER;

public class PersonServer extends IPersonApi.Stub {

    private CoreService mCore;
    private Gson mGson;
    private Module mModule;

    public PersonServer(CoreService core, Module module) {
        mCore = core;
        mGson = new Gson();
        mModule = module;
    }

    @Override
    public boolean setPersonListener(IPersonListener listener) {
        if (mModule != null && mModule.isSuspend()) {
            return false;
        }
        PersonManager manager = mCore.getPersonManager();
        manager.setPersonListener(listener);
        return true;
    }

    @Override
    public void releasePersonListener() {
        if (mModule != null && mModule.isSuspend()) {
            return;
        }
        PersonManager manager = mCore.getPersonManager();
        manager.releasePersonListener();
    }

    @Override
    public String getAllPersons() {
        if (mModule != null && mModule.isSuspend()) {
            return mGson.toJson(null);
        }
        PersonManager manager = mCore.getPersonManager();
        return mGson.toJson(manager.getAllPersonList(Double.MAX_VALUE));
    }

    @Override
    public String getAllPersonsByDistance(double maxDistance) {
        if (mModule != null && mModule.isSuspend()) {
            return mGson.toJson(null);
        }
        PersonManager manager = mCore.getPersonManager();
        return mGson.toJson(manager.getAllPersonList(maxDistance));
    }

    @Override
    public String getAllFaceList() {
        if (mModule != null && mModule.isSuspend()) {
            return mGson.toJson(null);
        }
        PersonManager manager = mCore.getPersonManager();
        return mGson.toJson(manager.getOnlyFacesPersonList(Double.MAX_VALUE));
    }

    @Override
    public String getAllFaceListByDistance(double maxDistance) {
        if (mModule != null && mModule.isSuspend()) {
            return mGson.toJson(null);
        }
        PersonManager manager = mCore.getPersonManager();
        return mGson.toJson(manager.getOnlyFacesPersonList(maxDistance));
    }

    @Override
    public String getCompleteFaceList() {
        if (mModule != null && mModule.isSuspend()) {
            return mGson.toJson(null);
        }
        PersonManager manager = mCore.getPersonManager();
        return mGson.toJson(manager.getCompleteFacesPersonList(Double.MAX_VALUE));
    }

    @Override
    public String getCompleteFaceListByDistance(double maxDistance) {
        if (mModule != null && mModule.isSuspend()) {
            return mGson.toJson(null);
        }
        PersonManager manager = mCore.getPersonManager();
        return mGson.toJson(manager.getCompleteFacesPersonList(maxDistance));
    }

    @Override
    public String getAllBodyList() {
        if (mModule != null && mModule.isSuspend()) {
            return mGson.toJson(null);
        }
        PersonManager manager = mCore.getPersonManager();
        return mGson.toJson(manager.getOnlyBodyPersonList(Double.MAX_VALUE));
    }

    @Override
    public String getAllBodyListByDistance(double maxDistance) {
        if (mModule != null && mModule.isSuspend()) {
            return mGson.toJson(null);
        }
        PersonManager manager = mCore.getPersonManager();
        return mGson.toJson(manager.getOnlyBodyPersonList(maxDistance));
    }

    @Override
    public String getFocusPerson() {
        if (mModule != null && mModule.isSuspend()) {
            return mGson.toJson(null);
        }
        PersonManager manager = mCore.getPersonManager();
        return mGson.toJson(manager.getFocusPerson());
    }

    @Override
    public boolean registerById(int personId, String name, IActionListener listener) {
        if (mModule != null && mModule.isSuspend()) {
            return false;
        }
        RequestBean bean = new RequestBean(personId, null, name, listener);
        mCore.getPersonManager().getHandler().obtainMessage(MSG_REGISTER, bean).sendToTarget();
        return true;
    }

    @Override
    public boolean registerByPic(String picturePath, String name, IActionListener listener) {
        if (mModule != null && mModule.isSuspend()) {
            return false;
        }
        RequestBean bean = new RequestBean(picturePath, null, name, listener);
        mCore.getPersonManager().getHandler().obtainMessage(MSG_REGISTER, bean).sendToTarget();
        return true;
    }

    @Override
    public boolean recognizeById(int personId, IActionListener listener) {
        if (mModule != null && mModule.isSuspend()) {
            return false;
        }
        RequestBean bean = new RequestBean(personId, listener);
        mCore.getPersonManager().getHandler().obtainMessage(MSG_RECOGNIZE, bean).sendToTarget();
        return true;
    }

    @Override
    public boolean recognizeByPic(String picturePath, IActionListener listener) {
        if (mModule != null && mModule.isSuspend()) {
            return false;
        }
        RequestBean bean = new RequestBean(picturePath, listener);
        mCore.getPersonManager().getHandler().obtainMessage(MSG_RECOGNIZE, bean).sendToTarget();
        return true;
    }

    @Override
    public String getMultipleModeInfos(int index) {
        return mCore.getFaceAngleManager().getMultipleModeInfo(index);
    }

    @Override
    public String getRegisteredFaceList() throws RemoteException {
        // FIXME(toBtoC 合并接口，空实现)
        new RuntimeException("合并接口，空实现").printStackTrace();
        return null;
    }

    @Override
    public boolean reRegisterWithUserIdById(int personId, String userId, IActionListener listener) throws RemoteException {
        // FIXME(toBtoC 合并接口，空实现)
        new RuntimeException("合并接口，空实现").printStackTrace();
        return false;
    }

    @Override
    public boolean reRegisterWithUserIdByPic(String picturePath, String userId, IActionListener listener) throws RemoteException {
        // FIXME(toBtoC 合并接口，空实现)
        new RuntimeException("合并接口，空实现").printStackTrace();
        return false;
    }
}
