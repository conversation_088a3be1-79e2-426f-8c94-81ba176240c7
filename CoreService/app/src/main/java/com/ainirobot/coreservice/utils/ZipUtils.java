package com.ainirobot.coreservice.utils;

import android.util.Base64;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

public class ZipUtils {

    private static final String TAG = "ZipUtils";

    public static String zipMapData(String cmdType, Gson gson, String originalStr) {
        int size = originalStr.getBytes().length / 1024;
        if (size >= 254) {
            java.util.Map<String, Object> map = new HashMap<>();
            map.put(Definition.JSON_MAP_DATA_IS_ZIP, true);
            map.put(Definition.JSON_MAP_ZIP_DATA, compressionString(originalStr));
            Log.d(TAG, cmdType + " zip data size:" + size);
            return gson.toJson(map);
        }
        return originalStr;
    }

    public static String unzipMapData(Gson gson, String mapData) {
        JsonElement jsonElement = gson.fromJson(mapData, JsonElement.class);
        if (null == jsonElement || !jsonElement.isJsonObject()) {
            return mapData;
        }
        JsonObject jsonObject = jsonElement.getAsJsonObject();
        if (null != jsonObject
                && jsonObject.has(Definition.JSON_MAP_DATA_IS_ZIP)
                && jsonObject.get(Definition.JSON_MAP_DATA_IS_ZIP).getAsBoolean()) {
            String zipMapData = jsonObject.get(Definition.JSON_MAP_ZIP_DATA).getAsString();
            return decompress(zipMapData);
        } else {
            return mapData;
        }
    }

    public static String decompress(String compressedString) {
        try {
            byte[] compressedBytes = Base64.decode(compressedString, Base64.DEFAULT);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(compressedBytes);
            GZIPInputStream gzipInputStream = new GZIPInputStream(inputStream);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int length;
            while ((length = gzipInputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, length);
            }
            gzipInputStream.close();
            outputStream.close();
            return new String(outputStream.toByteArray(), StandardCharsets.UTF_8);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return compressedString;
    }

    public static String compressionString(String originalStr) {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            GZIPOutputStream gzipOutputStream = new GZIPOutputStream(outputStream);
            gzipOutputStream.write(originalStr.getBytes(StandardCharsets.UTF_8));
            gzipOutputStream.close();
            byte[] compressedBytes = outputStream.toByteArray();
            outputStream.close();
            return Base64.encodeToString(compressedBytes, Base64.DEFAULT);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return originalStr;
    }
}
