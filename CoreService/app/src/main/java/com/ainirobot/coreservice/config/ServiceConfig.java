package com.ainirobot.coreservice.config;

import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.annotations.SerializedName;

public class ServiceConfig implements Parcelable {

    /**
     * Service名称
     */
    private String serviceName;

    @SerializedName("是否启用")
    private boolean enable;

    /**
     * Service包名
     */
    private String service;

    @SerializedName("配置项")
    private JsonObject config;

    @SerializedName("自检项")
    private JsonObject inspection;

    private JsonObject publicConfig;

    private JsonObject skuConfig = new JsonObject();

    private ServiceConfig(Parcel in) {
        Gson gson = new Gson();

        serviceName = in.readString();
        enable = in.readByte() != 0;
        service = in.readString();

        String conf = in.readString();
        if (!TextUtils.isEmpty(conf)) {
            config = gson.fromJson(conf, JsonObject.class);
        }

        String inspec = in.readString();
        if (!TextUtils.isEmpty(inspec)) {
            inspection = gson.fromJson(inspec, JsonObject.class);
        }

        String pubConfig = in.readString();
        if (!TextUtils.isEmpty(pubConfig)) {
            publicConfig = gson.fromJson(pubConfig, JsonObject.class);
        }

        String skuConfig = in.readString();
        if (!TextUtils.isEmpty(skuConfig)) {
            this.skuConfig = gson.fromJson(skuConfig, JsonObject.class);
        }
    }

    public JsonObject getPublicConfig() {
        return publicConfig;
    }

    public JsonObject getSkuConfig() {
        return skuConfig;
    }

    public void setSkuConfig(JsonObject skuConfig) {
        this.skuConfig = skuConfig;
    }

    public void setPublicConfig(JsonObject publicConfig) {
        this.publicConfig = publicConfig;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public boolean isEnable() {
        return enable;
    }

    public boolean isInspection() {
        return inspection != null && !inspection.keySet().isEmpty();
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public JsonObject getConfig() {
        return config;
    }

    public void setConfig(JsonObject config) {
        this.config = config;
    }

    public JsonObject getInspection() {
        return inspection;
    }

    public void setInspection(JsonObject inspection) {
        this.inspection = inspection;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof ServiceConfig)) {
            return false;
        }

        ServiceConfig config = (ServiceConfig) obj;
        return TextUtils.equals(this.serviceName, config.getServiceName());
    }

    @Override
    public int hashCode() {
        int hashCode = 1;
        hashCode = 31 * hashCode + (this.serviceName == null ? -1 : this.serviceName.hashCode());
        return hashCode;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(serviceName);
        dest.writeByte((byte) (enable ? 1 : 0));
        dest.writeString(service);
        dest.writeString(config == null ? "" : config.toString());
        dest.writeString(inspection == null ? "" : inspection.toString());
        dest.writeString(publicConfig == null ? "" : publicConfig.toString());
        dest.writeString(skuConfig == null ? "" : skuConfig.toString());
    }

    public static final Creator<ServiceConfig> CREATOR = new Creator<ServiceConfig>() {
        @Override
        public ServiceConfig createFromParcel(Parcel in) {
            return new ServiceConfig(in);
        }

        @Override
        public ServiceConfig[] newArray(int size) {
            return new ServiceConfig[size];
        }
    };

    @Override public String toString() {
        return "ServiceConfig{"
            + "serviceName='"
            + serviceName
            + '\''
            + ", enable="
            + enable
            + ", service='"
            + service
            + '\''
            + ", config="
            + config
            + ", inspection="
            + inspection
            + '}';
    }
}
