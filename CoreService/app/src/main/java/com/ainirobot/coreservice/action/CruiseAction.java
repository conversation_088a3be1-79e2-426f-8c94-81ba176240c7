/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.action;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.StopCommandList.IStopCommandChecker;
import com.ainirobot.coreservice.action.StopCommandList.StopCommand;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.actionbean.CruiseParams;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.robotlog.RobotLog;

import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

public class CruiseAction extends AbstractAction<CruiseParams> {
    private static final String TAG = "CruiseAction";
    private static final int NAN = -1;

    private int mReqId;
    private List<Pose> mRoute;
    private List<Integer> mDockingPoints;
    private Pose mCurrentPose;
    private int mCurrentPoint;
    private int mStartPoint;
    private String mPoseListener;
    private double mPointRange;
    private double mLinearSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED;
    private double mAngularSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED;
    private static final long DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT = 300 * 1000;
    /**多机模式等待超时时长，默认300s **/
    private long mWaitTimeout;
    /**是否是在多机避障状态，如果是则停止处理机器N秒不动退出导航的逻辑 **/
    private volatile boolean isMultipleWaitStatus = false;
    private Timer mWaitTimer;

    public CruiseAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_CRUISE, resList);
    }

    @Override
    protected boolean startAction(CruiseParams parameter, LocalApi api) {
        parseParams(parameter);
        checkPoseEstimate();
        return true;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        updateMultipleRobotWaitingStatus(false);
        cancelWaitTimer();
        unregisterStatusListener(mPoseListener);
        mCurrentPose = null;
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_IS_ESTIMATE:
                processPoseEstimate(result);
                return true;

            case Definition.CMD_NAVI_GO_POSITION:
                processNavigationResult(result);
                return true;
        }
        return true;
    }

    @Override
    public boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_GO_POSITION:
                processNavigationStatus(status);
                return true;
        }
        return false;
    }

    @Override
    protected boolean verifyParam(CruiseParams param) {
        List<Pose> route = param.getRoute();
        int startPoint = param.getStartPoint();
        return route != null
                && !route.isEmpty()
                && route.size() > startPoint;
    }

    private void parseParams(CruiseParams params) {
        mReqId = params.getReqId();
        mRoute = params.getRoute();
        mDockingPoints = params.getDockingPoints();
        mStartPoint = params.getStartPoint();
        mPointRange = params.getPointRange();
        mLinearSpeed = params.getLinearSpeed();
        mAngularSpeed = params.getAngularSpeed();
        long waitTimeout = params.getMultipleWaitTime();
        mWaitTimeout = (waitTimeout <= 0) ? DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT: waitTimeout;
        isMultipleWaitStatus = false;
        Log.d(TAG, "Start cruise route : " + mGson.toJson(mRoute)
                + "   startPoint : " + mStartPoint
                + "   dockingPoints : " + mGson.toJson(mDockingPoints));
    }

    private void checkPoseEstimate() {
        Log.d(TAG, "Check pose estimate");
        mApi.isRobotEstimate(mReqId);
    }

    private void processNavigationStatus(String status) {
        switch (status) {
            case Definition.NAVIGATION_OCCLUDED:
                onStatusUpdate(Definition.STATUS_GOAL_OCCLUDED, "The target is occluded");
                break;

            case Definition.NAVIGATION_OCCLUDED_END:
                onStatusUpdate(Definition.STATUS_GOAL_OCCLUDED_END, "The occluded end");
                break;

            case Definition.NAVIGATION_AVOID:
                onStatusUpdate(Definition.STATUS_NAVI_AVOID, "The local path search failed");
                break;

            case Definition.NAVIGATION_AVOID_END:
                onStatusUpdate(Definition.STATUS_NAVI_AVOID_END, "The local path search succeed");
                break;

            case Definition.NAVIGATION_OUT_MAP:
                onStatusUpdate(Definition.STATUS_NAVI_OUT_MAP, "The goal is out of map or more dangerous");
                break;

            case Definition.NAVIGATION_GLOBAL_PATH_FAILED:
                onStatusUpdate(Definition.STATUS_NAVI_GLOBAL_PATH_FAILED, "The global is path search failed");
                break;

            case Definition.NAVIGATION_OBSTACLES_AVOID:
                onStatusUpdate(Definition.STATUS_NAVI_OBSTACLES_AVOID, "obstacles avoid");
                break;

            case Definition.NAVIGATION_STARTED:
                onStatusUpdate(Definition.STATUS_START_NAVIGATION, "Navigation has been started");
                break;
            case Definition.NAVIGATION_MULTI_ROBOT_WAITING:
                updateMultipleRobotWaitingStatus(true);
                startWaitingTimer();
                onStatusUpdate(Definition.STATUS_NAVI_MULTI_ROBOT_WAITING, "Multiple robots are waiting to avoid obstacles");
                break;

            case Definition.NAVIGATION_MULTI_ROBOT_WAITING_END:
                updateMultipleRobotWaitingStatus(false);
                cancelWaitTimer();
                onStatusUpdate(Definition.STATUS_NAVI_MULTI_ROBOT_WAITING_END, "Multiple robots passageways returned to normal");
                break;
            default:
                Log.i(TAG, "Command status: " + status +  " doesn't be handled");
                break;
        }
    }

    private void processPoseEstimate(String result) {
        if (!"true".equals(result)) {
            onError(Definition.ERROR_NOT_ESTIMATE, "Not pose estimate");
            return;
        }
        startCruise();
    }

    private void processNavigationResult(String result) {
        Log.d(TAG, "Navigation result : " + result
                + "  current point : " + mCurrentPoint
                + "  docking : " + isDockingPoint(mCurrentPoint));

        if (Definition.NAVIGATION_CANCELED.equals(result)) {
            return;
        }

        if (Definition.NAVIGATION_OK.equals(result)) {
            if (isDockingPoint(mCurrentPoint)) {
                goNextPoint();
            }
            return;
        }

        onError(Definition.ERROR_NAVIGATION_FAILED, "Navigation failed : " + result);
    }

    private void startCruise() {
        Log.d(TAG, "Start cruise");
        startNavigation(mStartPoint);
        registerPoseListener();
        onStatusUpdate(Definition.STATUS_START_CRUISE, "Start cruise");
    }

    private void startNavigation(int index) {
        Log.d(TAG, "Start navigation point : " + index + "  docking : " + isDockingPoint(index));
        updateMultipleRobotWaitingStatus(false);
        Pose pose = mRoute.get(index);
        mApi.goPosition(mReqId, pose.getX(), pose.getY(), pose.getTheta(), mLinearSpeed, mAngularSpeed);
        mCurrentPoint = index;
        mCurrentPose = mRoute.get(mCurrentPoint);
    }

    private synchronized void goNextPoint() {
        int index = getNextPoint(mCurrentPoint);
        Log.d(TAG, "Go next point : " + index);
        if (index == NAN) {
            onResult(Definition.RESULT_OK, "Cruise finished");
            return;
        }

        onStatusUpdate(Definition.STATUS_CRUISE_REACH_POINT, String.valueOf(mCurrentPoint));
        startNavigation(index);
    }

    private int getNextPoint(int index) {
        int size = mRoute.size();
        if (++index >= size) {
            return NAN;
        }
        return index;
    }

    private boolean isDockingPoint(int index) {
        return mDockingPoints.contains(index);
    }

    private void registerPoseListener() {
        mPoseListener = registerStatusListener(Definition.STATUS_POSE, new StatusListener() {
            @Override
            public void onStatusUpdate(String data) {
                if (TextUtils.isEmpty(data)
                        || isDockingPoint(mCurrentPoint)
                        || mCurrentPose == null) {
                    return;
                }

                synchronized (CruiseAction.this) {
                    if (!isRunning()) {
                        return;
                    }
                    Pose pose = mGson.fromJson(data, Pose.class);
                    double distance = mCurrentPose.getDistance(pose);
                    if (distance < mPointRange) {
                        goNextPoint();
                    }
                }
            }
        });
    }

    /**
     * 更新多机避障状态，如果是多机避障状态，则机器人超时等待策略需忽略
     * @param isWaiting 是否正在多机避障等待状态
     */
    private void updateMultipleRobotWaitingStatus(boolean isWaiting){
        isMultipleWaitStatus = isWaiting;
    }

    private void startWaitingTimer() {
        cancelWaitTimer();
        mWaitTimer = new Timer();
        mWaitTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (!isRunning()) {
                    return;
                }
                onError(Definition.ERROR_MULTI_ROBOT_WAITING_TIMEOUT, "Navigation multiple waiting timeout");
            }
        }, mWaitTimeout);
    }

    private void cancelWaitTimer() {
        if (mWaitTimer != null) {
            mWaitTimer.cancel();
            mWaitTimer = null;
        }
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return new ArrayList<StopCommand>() {{
            add(new StopCommand(Definition.CMD_NAVI_STOP_NAVIGATION, null));
        }};
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return new IStopCommandChecker() {
            @Override
            public boolean executeSuccess(String command, String result) {
                RobotLog.d("Cruise stop check : " + command);
                switch (command) {
                    case Definition.CMD_NAVI_STOP_NAVIGATION:
                        return true;
                }
                return false;
            }
        };
    }
}
