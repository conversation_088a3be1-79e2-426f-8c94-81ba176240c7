package com.ainirobot.coreservice.client.actionbean;

import com.ainirobot.coreservice.bean.GateLineNode;

import java.util.List;

public class GateLineUnit {
    private String gateId; // 闸机主键
    private int gateLineId; //闸机线主键
    private final GateLineNode.Node enterNode;
    private final GateLineNode.Node outerNode;

    public GateLineUnit(int gateLineId, GateLineNode.Node enterNode, GateLineNode.Node outerNode) {
        this.enterNode = enterNode;
        this.outerNode = outerNode;
        this.gateLineId= gateLineId;
    }

    public GateLineNode.Node getEnterNode() {
        return enterNode;
    }

    public GateLineNode.Node getOuterNode() {
        return outerNode;
    }

    public String getGateId() {
        return gateId;
    }

    public void setGateId(String gateId) {
        this.gateId = gateId;
    }

    public int getGateLineId() {
        return gateLineId;
    }

    public void setGateLineId(int gateLineId) {
        this.gateLineId = gateLineId;
    }


    public static class GatePassingInfo {
        private String pkGate;
        private int pkGateLine;
        private List<CustomVector2d> passingPoints;
        private boolean isForwardPassing; // 新增字段：是否正向通过闸机
        private int pathIndex; // 新增字段：在路径中的索引位置，用于排序

        public GatePassingInfo(int pkGateLine,String pkGate, List<CustomVector2d> passingPoints) {
            this.pkGateLine = pkGateLine;
            this.pkGate = pkGate;
            this.passingPoints = passingPoints;
        }

        public GatePassingInfo(String pkGate, List<CustomVector2d> passingPoints) {
            this.pkGate = pkGate;
            this.passingPoints = passingPoints;
        }

        // 新增构造函数，包含通过方向
        public GatePassingInfo(String pkGate, List<CustomVector2d> passingPoints, boolean isForwardPassing, int pathIndex) {
            this.pkGate = pkGate;
            this.passingPoints = passingPoints;
            this.isForwardPassing = isForwardPassing;
            this.pathIndex = pathIndex;
        }

        // 新增构造函数，包含闸机线ID和通过方向
        public GatePassingInfo(int pkGateLine, String pkGate, List<CustomVector2d> passingPoints, boolean isForwardPassing, int pathIndex) {
            this.pkGateLine = pkGateLine;
            this.pkGate = pkGate;
            this.passingPoints = passingPoints;
            this.isForwardPassing = isForwardPassing;
            this.pathIndex = pathIndex;
        }

        public String getPkGate() {
            return pkGate;
        }

        public void setPkGate(String pkGate) {
            this.pkGate = pkGate;
        }

        public int getPkGateLine() {
            return pkGateLine;
        }

        public void setPkGateLine(int pkGateLine) {
            this.pkGateLine = pkGateLine;
        }

        public List<CustomVector2d> getPassingPoints() {
            return passingPoints;
        }

        public void setPassingPoints(List<CustomVector2d> passingPoints) {
            this.passingPoints = passingPoints;
        }

        public boolean isForwardPassing() {
            return isForwardPassing;
        }

        public void setForwardPassing(boolean forwardPassing) {
            isForwardPassing = forwardPassing;
        }

        public int getPathIndex() {
            return pathIndex;
        }

        public void setPathIndex(int pathIndex) {
            this.pathIndex = pathIndex;
        }
    }

    public static class CustomVector2d {
        private double px;
        private double py;

        public CustomVector2d(){

        }

        public CustomVector2d(double px, double py){
            this.px = px;
            this.py = py;
        }

        public double getX() {
            return px;
        }

        public void setX(double px) {
            this.px = px;
        }

        public double getY() {
            return py;
        }

        public void setY(double py) {
            this.py = py;
        }

        @Override
        public String toString() {
            return "CustomVector2d{" +
                    "px=" + px +
                    ", py=" + py +
                    '}';
        }
    }
}