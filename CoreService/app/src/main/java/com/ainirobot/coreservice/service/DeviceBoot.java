/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.service;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.IntentUtil;

/**
 * Device boot event receiver: automatically starts the core service
 *
 * <AUTHOR>
 */
public class DeviceBoot extends BroadcastReceiver {

    @Override
    public void onReceive(final Context context, Intent intent) {
        if (Intent.ACTION_BOOT_COMPLETED.equals(intent.getAction())) {
            handleBootBroadcast(context);
        }
        Log.d("Network", intent.getAction());
    }

    private void handleBootBroadcast(Context context) {
        //启动UploadService
        Intent uploadIntent = IntentUtil.createExplicitIntent(Definition.UPLOAD_PACKAGE_NAME,
                Definition.UPLOAD_SERVICE_NAME,
                Definition.UPLOAD_ACTION_NAME);

        //启动CoreService
        Intent mIntent = IntentUtil.createExplicitIntent(Definition.CORE_PACKAGE_NAME,
                Definition.CORE_SERVICE_NAME,
                Definition.CORE_ACTION_NAME);
        context.startService(mIntent);

        Intent orionUploadIntent = IntentUtil.createExplicitIntent(Definition.ORION_UPLOAD_PACKAGE_NAME,
                Definition.ORION_UPLOAD_SERVICE_NAME,
                Definition.ORION_UPLOAD_ACTION_NAME);
        context.startService(orionUploadIntent);
    }

}
