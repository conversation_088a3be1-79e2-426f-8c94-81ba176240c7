package com.ainirobot.coreservice.dump.callback;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.client.module.ModuleCallbackApi;
import com.ainirobot.coreservice.dump.DataDumpService;
import com.ainirobot.coreservice.dump.DumpDef;

/**
 * Handle request and reporter from other module and service.
 * Created by Orion on 2020/5/25.
 */
public class DumpModuleCallBack extends ModuleCallbackApi {
    private static final String TAG = "DumpCallBack";

    private Context mContext;
    private DataDumpService mService;
    private Handler mHandler;

    public DumpModuleCallBack(DataDumpService service) {
        this.mContext = service.getApplicationContext();
        this.mService = service;
        initHandler();
    }

    @Override
    public boolean onSendRequest(int reqId, String reqType, String reqText, String reqParam) throws RemoteException {
        Log.d(TAG, "New request: " + " type is:" + reqType + " text is:" + reqText);

        Message msg = mHandler.obtainMessage(DumpDef.MSG_NEW_REQUEST);
        Bundle bundle = new Bundle();
        bundle.putInt(DumpDef.REQ_ID, reqId);
        bundle.putString(DumpDef.REQ_INTENT, reqType);
        bundle.putString(DumpDef.REQ_TEXT, reqText);
        bundle.putString(DumpDef.REQ_PARAMS, reqParam);
        msg.setData(bundle);
        mHandler.sendMessage(msg);
        return true;
    }

    @Override
    public void onHWReport(int hwFunction, String cmdType, String hwReport) throws RemoteException {
        Log.d(TAG, "HW report: " + " hwFunction is:" + hwFunction + " cmdType:" + cmdType +
                " hwReport is:" + hwReport);

        Message msg = mHandler.obtainMessage(DumpDef.MSG_HW_RESPORT);
        Bundle bundle = new Bundle();
        bundle.putInt(DumpDef.FUNCTION, hwFunction);
        bundle.putString(DumpDef.TYPE, cmdType);
        bundle.putString(DumpDef.MESSAGE, hwReport);
        msg.setData(bundle);
        mHandler.sendMessage(msg);
    }

    private void initHandler() {
        HandlerThread handlerThread = new HandlerThread("HandleRequestThread:DataDump");
        handlerThread.start();
        mHandler = new Handler(handlerThread.getLooper()) {
            public void handleMessage(Message msg) {
                Log.i(TAG, "main handler receive message:" + msg.what);
                final Bundle bundle = msg.getData();
                switch (msg.what) {
                    case DumpDef.MSG_NEW_REQUEST:
                        handleNewRequest(bundle);
                        break;
                    case DumpDef.MSG_HW_RESPORT:
                        handleHWReport(bundle);
                        break;
                    default:
                        break;
                }
            }
        };
    }

    public Handler getHandler() {
        return mHandler;
    }

    private void handleNewRequest(Bundle bundle) {
        int cmdId = bundle.getInt(DumpDef.REQ_ID);
        String intent = bundle.getString(DumpDef.REQ_INTENT);
        String text = bundle.getString(DumpDef.REQ_TEXT);
        String params = bundle.getString(DumpDef.REQ_PARAMS);
        Log.d(TAG, "handleNewRequest cmdId:" + cmdId + " intent:" + intent
                + " text:" + text + " params:" + params);
        //TODO
    }

    private void handleHWReport(Bundle bundle) {
        int function = bundle.getInt(DumpDef.FUNCTION);
        String type = bundle.getString(DumpDef.TYPE);
        String message = bundle.getString(DumpDef.MESSAGE);
        Log.d(TAG, "handleNewRequest function:" + function + " type:" + type
                + " message:" + message);
        //TODO
    }

}
