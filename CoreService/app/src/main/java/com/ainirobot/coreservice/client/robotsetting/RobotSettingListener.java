/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.client.robotsetting;

import com.ainirobot.coreservice.client.messagedispatcher.RobotSettingDispatcher;

import java.util.List;

public class RobotSettingListener {

    private RobotSettingDispatcher mDispatcher = null;
    private List<String> mKeyList;

    public void onRobotSettingChanged(String key) {

    }

    public final void onRegistered(RobotSettingDispatcher dispatcher, List<String> keyList) {
        mDispatcher = dispatcher;
        mKeyList = keyList;
    }

    public final void onUnRegistered() {
        mDispatcher = null;
        mKeyList = null;
    }

    public final RobotSettingDispatcher getDispatcher() {
        return mDispatcher;
    }

    public final List<String> getKeyList() {
        return mKeyList;
    }
}
