package com.ainirobot.coreservice.action.advnavi.gatecmd;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.google.gson.Gson;

/**
 * Date: 2024/2/22
 * Author: dengting
 * <p>
 * 控制命令
 * * 成功 延迟继续控制 若是其他命令调用的则上报RESULT_OK
 * * 失败 释放闸机
 * * 超时 重试
 */
public class CmdCallGate extends GateCommand {

    private final Gson mGson;
    private CmdReleaseGate cmdReleaseGate;
    private Status status;

    private enum Status {
        IDLE,
        CALLING,
        CALL_SUC,
        CALL_FAIL,
    }

    CmdCallGate(String name, CmdType intentType, GateCommandInterface commandInterface, GateCommandListener listener) {
        super(name, intentType, commandInterface, listener);
        mGson = new Gson();
        initCmdType(CmdType.CALL_GATE);
    }


    private void updateStatus(Status status) {
        Log.d(TAG, "updateStatus " + this.status + " --> " + status);
        this.status = status;
    }

    @Override
    public void start() {
        super.start();
        updateStatus(Status.CALLING);
        Log.d(TAG, "call to ");
        callGateDoor();
    }

    @Override
    public void onSuccess() {
        super.onSuccess();
        updateStatus(Status.IDLE);
        onResult(Definition.RESULT_OK, "call suc");
    }

    @Override
    public void onFail() {
        super.onFail();
        updateStatus(Status.CALL_FAIL);
        if (null == cmdReleaseGate) {
            cmdReleaseGate = new CmdReleaseGate(TAG, intentType, commandInterface, releaseListener);
        }
        Log.d(TAG, "call gate fail, try release gate");
        cmdReleaseGate.start();
    }

    @Override
    public void stop() {
        super.stop();
        updateStatus(Status.IDLE);
        if (null != cmdReleaseGate && cmdReleaseGate.isAlive) {
            cmdReleaseGate.stop();
        }
    }

    private final GateCommandListener releaseListener = new GateCommandListener() {
        @Override
        public void onStatusUpdate(int status, String data, String extraData) {
            onCmdStatusUpdate(status, data, extraData);
        }

        @Override
        public void onFinish(int status, String data) {
            if (status == Definition.RESULT_OK) {
                Log.d(TAG, "release suc ,delay start call floor to ");
                start();
                return;
            }
            onResult(status, data);
        }
    };

    @Override
    protected void onRetry() {
        callGateDoor();
    }

    @Override
    public void cmdResponse(String command, String result, String extraData) {
        if (!isAlive) {
            Log.d(TAG, "cmdResponse call cmd not alive");
            return;
        }
        if (TextUtils.equals(command, Definition.CMD_CONTROL_CALL_GATE)) {
            callCmdResponse(result, extraData);
            return;
        }
        if (null != cmdReleaseGate) {
            cmdReleaseGate.cmdResponse(command, result, extraData);
        }
    }

    private void callCmdResponse(String result, String extraData) {
        Log.d(TAG, "call cmd response [ " + result + " ]  extraData:" + extraData);
        switch (result) {
            case Definition.FAILED:
                onFail();
                return;
            case Definition.SUCCEED:
                onSuccess();
                break;
            case Definition.PARAMS_TIMEOUT:
                onFail();
                break;
        }
    }
}
