package com.ainirobot.coreservice.client.upload.bi;

/**
 * Created by Orion on 2020/4/28.
 */
public class CallChainReport extends BaseBiReport {

    private static final String TABLE_NAME = "mini_robot_gb_call_chain";

    private static final String BUSINESS_NAME = "business_name";
    private static final String SOURCE = "source";
    private static final String PACKAGE_NAME = "package_name";
    private static final String NODE_CLASS_NAME = "node_class_name";
    private static final String NODE_METHOD_TYPE = "node_method_type";
    private static final String NODE_METHOD_NAME = "node_method_name";
    private static final String NODE_METHOD_NOTE = "node_method_note";
    private static final String NODE_PARAMS_NOTE = "node_params_note";
    private static final String NODE_PARAMS = "node_params";
    private static final String NODE_RESULT = "node_result";
    private static final String NODE_NOTE = "node_note";
    private static final String NODE_ENTRANCE = "node_entrance";
    private static final String NODE_MIDDLE = "node_middle";
    private static final String NODE_EXPORT = "node_export";
    private static final String STATUS = "status";
    private static final String RESULT = "result";
    private static final String WAKEUP_ID = "wakeup_id";
    private static final String CTIME = "ctime";

    public CallChainReport(String businessName) {
        this(TABLE_NAME,businessName);
    }

    public CallChainReport(String tableName, String businessName) {
        super(tableName);
        initData();
        addBusinessName(businessName);
    }

    protected void initData() {
        addBusinessName("");
        addSource("");
        addPackageName("");
        addNodeMethodType("");
        addNodeMethodName("");
        addNodeMethodAnnotation("");
        addNodeParamsAnnotation("");
        addNodeParams("");
        addNodeResult("");
        addNodeEntrance("");
        addNodeMiddle("");
        addNodeExport("");
        addStatus("");
        addResult("");
        addWakeupId("");
    }

    public CallChainReport addBusinessName(String system) {
        addData(BUSINESS_NAME, system);
        return this;
    }

    public CallChainReport addSource(String source) {
        addData(SOURCE, source);
        return this;
    }

    public CallChainReport addPackageName(String packageName) {
        addData(PACKAGE_NAME, packageName);
        return this;
    }

    public CallChainReport addClassName(String packageName) {
        addData(NODE_CLASS_NAME, packageName);
        return this;
    }

    public CallChainReport addNodeMethodType(String nodeMethodType) {
        addData(NODE_METHOD_TYPE, nodeMethodType);
        return this;
    }

    public CallChainReport addNodeMethodName(String nodeMethodName) {
        addData(NODE_METHOD_NAME, nodeMethodName);
        return this;
    }

    public CallChainReport addNodeMethodAnnotation(String nodeMethodAnnotation) {
        addData(NODE_METHOD_NOTE, nodeMethodAnnotation);
        return this;
    }

    public CallChainReport addNodeParamsAnnotation(String nodeParamsAnnotation) {
        addData(NODE_PARAMS_NOTE, nodeParamsAnnotation);
        return this;
    }

    public CallChainReport addNodeParams(String nodeParams) {
        addData(NODE_PARAMS, nodeParams);
        return this;
    }

    public CallChainReport addNodeResult(String nodeResult) {
        addData(NODE_RESULT, nodeResult);
        return this;
    }

    public CallChainReport addNodeNote(String nodeNote) {
        addData(NODE_NOTE, nodeNote);
        return this;
    }

    public CallChainReport addNodeEntrance(String nodeEntrance) {
        addData(NODE_ENTRANCE, nodeEntrance);
        return this;
    }

    public CallChainReport addNodeMiddle(String nodeMiddle) {
        addData(NODE_MIDDLE, nodeMiddle);
        return this;
    }

    public CallChainReport addNodeExport(String nodeExport) {
        addData(NODE_EXPORT, nodeExport);
        return this;
    }

    public CallChainReport addStatus(String status) {
        addData(STATUS, status);
        return this;
    }

    public CallChainReport addResult(String result) {
        addData(RESULT, result);
        return this;
    }

    public CallChainReport addWakeupId(String wakeupId) {
        addData(WAKEUP_ID, wakeupId);
        return this;
    }

    public CallChainReport addSourceRom() {
        addSource(CallChainDef.SOURCE_ROM);
        return this;
    }

    public CallChainReport addSourceRobotOs() {
        addSource(CallChainDef.SOURCE_ROBOT_OS);
        return this;
    }

    public CallChainReport addSourceVisionSdk() {
        addSource(CallChainDef.SOURCE_VISION_SDK);
        return this;
    }

    public CallChainReport addSourceRoverService() {
        addSource(CallChainDef.SOURCE_ROVER_SERVICE);
        return this;
    }

    public CallChainReport addPackageNamePlatform() {
        addPackageName(CallChainDef.APP_PLATFORM);
        return this;
    }

    public CallChainReport addPackageNameCoreService() {
        addPackageName(CallChainDef.APP_CORESERVICE);
        return this;
    }

    public CallChainReport addPackageNameNavigationService() {
        addPackageName(CallChainDef.APP_NAVIGATIONSERVICE);
        return this;
    }

    public CallChainReport addPackageNameHeadService() {
        addPackageName(CallChainDef.APP_HEADSERVICE);
        return this;
    }

    public CallChainReport addNodeMethodTypeApiCall() {
        addNodeMethodType(CallChainDef.TYPE_API_CALL);
        return this;
    }

    public CallChainReport addNodeMethodTypeApiCallback() {
        addNodeMethodType(CallChainDef.TYPE_API_CALLBACK);
        return this;
    }

    public CallChainReport addNodeMethodTypeEvent() {
        addNodeMethodType(CallChainDef.TYPE_EVENT);
        return this;
    }

    public CallChainReport addNodeMethodTypeLifecycle() {
        addNodeMethodType(CallChainDef.TYPE_LIFECYCLE);
        return this;
    }

    public CallChainReport addNodeMethodTypeFunction() {
        addNodeMethodType(CallChainDef.TYPE_FUNCTION);
        return this;
    }

    @Override
    public void report() {
        reportV();
    }

    public void reportV() {
        addData(CTIME, System.currentTimeMillis());
//        super.reportCallChainV();
    }

    public void reportD() {
        addData(CTIME, System.currentTimeMillis());
        super.reportCallChainD();
    }

}
