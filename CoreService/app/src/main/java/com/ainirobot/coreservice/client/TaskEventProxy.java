package com.ainirobot.coreservice.client;

import com.ainirobot.coreservice.bean.TaskEvent;

public class TaskEventProxy {

    private TaskEvent taskEvent;

    public TaskEventProxy() {
        taskEvent = new TaskEvent();
    }

    public TaskEventProxy(TaskEvent event) {
        taskEvent = event;
    }

    public String getEventType() {
        return taskEvent.getEventType();
    }

    public void setEventType(String type) {
        this.taskEvent.setEventType(type);
    }

    public String getTaskData() {
        return taskEvent.getTaskData();
    }

    public void setTaskData(String data) {
        this.taskEvent.setTaskData(data);
    }

    public String getTaskId() {
        return this.taskEvent.getTaskId();
    }

    public void setTaskId(String taskId) {
        this.taskEvent.setTaskId(taskId);
    }

    public String getEventData() {
        return taskEvent.getEventData();
    }

    public void setEventData(String eventData) {
        this.taskEvent.setEventData(eventData);
    }

    public String getSubTaskId() {
        return this.taskEvent.getSubTaskId();
    }

    public void setSubTaskId(String subTaskId) {
        this.taskEvent.setSubTaskId(subTaskId);
    }

    public String getSubTaskType() {
        return this.taskEvent.getSubTaskType();
    }

    public void setSubTaskType(String subTaskType) {
        this.taskEvent.setSubTaskType(subTaskType);
    }

    public String getSubTaskData() {
        return this.taskEvent.getSubTaskData();
    }

    public void setSubTaskData(String subTaskData) {
        this.taskEvent.setSubTaskData(subTaskData);
    }

    public int getIsCreateOnStart() {
        return this.taskEvent.getIsCreateOnStart();
    }

    public void setIsCreateOnStart(int isCreateOnStart) {
        this.taskEvent.setIsCreateOnStart(isCreateOnStart);
    }

    protected TaskEvent generateTaskEvent(){
        return this.taskEvent;
    }

}
