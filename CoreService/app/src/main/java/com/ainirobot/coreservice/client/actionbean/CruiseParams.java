/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.client.actionbean;

import com.ainirobot.coreservice.client.SettingsUtil;

import java.util.ArrayList;
import java.util.List;

public class CruiseParams extends BaseBean {
    private List<Pose> route;
    private int startPoint;
    private List<Integer> dockingPoints;
    private double pointRange = 0.5;
    private long dockingTime = 1000; // ms
    private long avoidTime = 20 * 1000;
    private double mLinearSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED;
    private double mAngularSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED;
    private long mMultipleWaitTime = 300*1000;

    public List<Pose> getRoute() {
        return route;
    }

    public void setRoute(List<Pose> route) {
        this.route = route;
    }

    public int getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(int startPoint) {
        this.startPoint = startPoint;
    }

    public List<Integer> getDockingPoints() {
        if (dockingPoints == null) {
            dockingPoints = new ArrayList<>();
        }
        return dockingPoints;
    }

    public void setDockingPoints(List<Integer> dockingPoints) {
        this.dockingPoints = dockingPoints;
    }

    public double getPointRange() {
        return pointRange;
    }

    public long getDockingTime() {
        return dockingTime;
    }

    public long getAvoidTime() {
        return avoidTime;
    }

    public void setAvoidTime(long avoidTime) {
        this.avoidTime = avoidTime;
    }

    public double getLinearSpeed(){
        return mLinearSpeed;
    }

    public void setLinearSpeed(double linearSpeed){
        this.mLinearSpeed = linearSpeed;
    }

    public double getAngularSpeed(){
        return mAngularSpeed;
    }

    public void setAngularSpeed(double angularSpeed){
        this.mAngularSpeed = angularSpeed;
    }

    public long getMultipleWaitTime() {
        return mMultipleWaitTime;
    }

    public void setMultipleWaitTime(long multipleWaitTime) {
        mMultipleWaitTime = multipleWaitTime;
    }

}
