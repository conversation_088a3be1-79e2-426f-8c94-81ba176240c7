package com.ainirobot.coreservice.action;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.action.advnavi.state.StateEnum;
import com.ainirobot.coreservice.action.advnavi.state.StateMachine;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.actionbean.NavigationAdvancedBean;
import com.ainirobot.coreservice.config.ConfigManager;
import com.ainirobot.coreservice.core.LocalApi;

import java.util.ArrayList;
import java.util.List;

/**
 * Data: 2022/8/8 15:09
 * Author: wanglijing
 * 高级导航action
 */
public class NavigationAdvancedAction extends AbstractAction<NavigationAdvancedBean> {
    private static final String TAG = "NaviAdvancedAction";
    private final StateMachine mStateMachine;
    private int mRetryEnterElevatorCount = 0;
    //进梯重试n次后通知用户
    private final int RETRY_ENTER_ELEVATOR_MAX_COUNT = 5;
    private NavigationResetHeadUtils mNavigationResetHeadUtils;

    protected NavigationAdvancedAction(ActionManager manager, int id, String[] resList) {
        super(manager, id, resList);
        mStateMachine = new StateMachine(this);
        mNavigationResetHeadUtils = new NavigationResetHeadUtils(mApi, mManager.getCoreService().getHeadMoveManager());
    }

    @Override
    protected boolean startAction(NavigationAdvancedBean parameter, LocalApi api) {
        Log.i(TAG, "startAction , parameter: " + parameter);
        if (null == mStateMachine || !isFreeState()) {
            Log.i(TAG, "startAction fail, isFree: " + isFreeState());
            return false;
        }
        mReqId = Definition.ELEVATOR_REQ_ID;
        mRetryEnterElevatorCount = 0;
        if (verifyParam(parameter)) {
            StateData stateData = new StateData(parameter);
            return mStateMachine.start(stateData);
        }
        onResult(Definition.ERROR_PARAMS_JSON_PARSER_ERROR, "json parse exception");
        return false;
    }

    private boolean isFreeState() {
        if (null != mStateMachine) {
            return mStateMachine.getCurrentState() == StateEnum.IDLE.getState();
        }
        return false;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        Log.i(TAG, "stopAction , isResetHW: " + isResetHW);
        if (null != mStateMachine) {
            mStateMachine.stopAction();
        }

        //梯控项目释放电梯
        if (ConfigManager.isSupportElevatorFunction()) {
            Log.d(TAG, "release elevator from NavigationAdvancedAction stopAction");
            mApi.releaseElevator(Definition.MODULE_REQ_ID);
        }

        mNavigationResetHeadUtils.stop();
        return true;
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        ArrayList<StopCommandList.StopCommand> list = new ArrayList<>();
        list.add(new StopCommandList.StopCommand(Definition.CMD_NAVI_STOP_NAVIGATION, null));
        List<StopCommandList.StopCommand> currentStopCommand;
        if (mStateMachine != null && (currentStopCommand = mStateMachine.getStopCommands()) != null) {
            list.addAll(currentStopCommand);
        }
        return list;
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        Log.d(TAG, "getStopCommandChecker call ");
        return new StopCommandList.IStopCommandChecker() {
            @Override
            public boolean executeSuccess(String command, String result) {
                Log.d(TAG, "stop check : " + command);
                switch (command) {
                    case Definition.CMD_NAVI_STOP_NAVIGATION:
                    case Definition.CMD_CONTROL_RELEASE_ELEVATOR:
                    case Definition.CMD_CONTROL_CLOSE_GATE_DOOR:
                    case Definition.CMD_CONTROL_RELEASE_GATE:
                        return true;
                    default:
                        return false;
                }
            }
        };
    }

    @Override
    protected boolean verifyParam(NavigationAdvancedBean param) {
        if (null == param) {
            return false;
        }
        if (TextUtils.isEmpty(param.getDestination())) {
            return false;
        }
        //乘梯策略但是目的地楼层为空，返回false
        if (param.getNaviStrategy() == Definition.AdvNaviStrategy.ELEVATOR
                && param.getTargetFloor() == 0) {
            Log.e(TAG, "elevator target floor is 0");
            return false;
        }
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        if (Definition.ERROR_INIT_ELEVATOR_CONTROL.equals(extraData)) {
            Log.d(TAG, "init elevator control error");
            onResult(Definition.ERROR_ELEVATOR_INIT_CONTROL, "init elevator control error");
            return true;
        }
        if (mStateMachine != null) {
            return mStateMachine.cmdResponse(cmdId, command, result, extraData);
        }
        return true;
    }

    @Override
    public boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        if (mStateMachine != null) {
            return mStateMachine.cmdStatusUpdate(cmdId, command, status, extraData);
        }
        return true;
    }

    @Override
    public boolean onStatusUpdate(int status, String data, String extraData) {
        switch (status){
            case Definition.STATUS_START_BACK_TO_ELEVATOR_GATE:
                parseRetryEnterElevator();
                break;
        }
        Log.i(TAG, "onStatusUpdate ,status=" + status + " ,data=" + data + " ,extraData=" + extraData);
        return super.onStatusUpdate(status, data, extraData);
    }

    private void parseRetryEnterElevator(){
        mRetryEnterElevatorCount++;
        if (mRetryEnterElevatorCount > RETRY_ENTER_ELEVATOR_MAX_COUNT) {
            //进梯次数过多，通知用户，但不结束
            onStatusUpdate(Definition.STATUS_UPDATE_RETRY_ERROR, Definition.STATUS_ENTER_ELEVATOR_MUCH_TIME);
        }
    }

    public void sendCommand(String command, String params) {
        mApi.sendCommand(mReqId, command, params);
    }

    public String getRobotInfo(int reqId, String param) {
        return mManager.getCoreService().getRobotInfoManager().getRobotInfo(reqId, param);
    }

    public String isAlreadyInElevator() {
        return mManager.getCoreService().getRobotInfoManager().isAlreadyInElevator();
    }

    public LocalApi getApi() {
        return mApi;
    }

    public int getReqId() {
        return mReqId;
    }

    public NavigationResetHeadUtils getNavigationResetHeadUtils() {
        return mNavigationResetHeadUtils;
    }
}
