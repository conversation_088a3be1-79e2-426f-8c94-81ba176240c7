package com.ainirobot.coreservice.action.advnavi.state;

import static com.ainirobot.coreservice.client.actionbean.NavigationAdvancedBean.TYPE_POSE;

import android.util.Log;

import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.BasePoseBean;
import com.ainirobot.coreservice.client.actionbean.MultiRobotStatus;
import com.ainirobot.coreservice.client.actionbean.NavigationAdvancedBean;
import com.ainirobot.coreservice.client.actionbean.Pose;

import java.util.List;

/**
 * 前往电梯等待点
 */
public class GoElevatorWaitPointState extends BaseNavigationState {
    private volatile Status mStatus;
    private List<Pose> mWaitPointList;    //电梯等待点

    private enum Status {
        START,
        GO_ELEVATOR_WAIT_POINT,     //导航去电梯等待点

    }

    private void updateStatus(Status status) {
        Log.i(TAG, "updateStatus: " + status);
        mStatus = status;
    }

    GoElevatorWaitPointState(String name) {
        super(name);
    }

    @Override
    protected void preAction(StateData data, IStateListener listener) {
        super.preAction(data, listener);
    }

    @Override
    protected void doAction() {
        super.doAction();

        mWaitPointList = mStateData.getElevatorWaitPoints();
        updateStatus(Status.START);
        registerMultiRobotStatusListener();
    }

    @Override
    protected void onNavigationStatusUpdate(int result, String message, String extraData) {
        onStatusUpdate(result, message, extraData);
    }

    @Override
    protected void onNavigationError(int result, String message, String extraData) {
        onResult(result, message, extraData);
    }

    @Override
    protected void onNavigationResult(int result, String message, String extraData) {
        switch (result) {
            case Definition.ERROR_IN_DESTINATION:
            case Definition.RESULT_OK:
                naviSuccess();
                break;
        }
    }

    private void naviSuccess() {
        updateAction(Definition.ElevatorAction.ACTION_GO_ELEVATOR_WAIT_POINT_SUC);
        onSuccess();
    }

    @Override
    protected BaseState getNextState() {
        switch (actionState) {
            case ACTION_GO_ELEVATOR_WAIT_POINT_SUC:
                return StateEnum.REGISTER_ELEVATOR.getState();
            default:
                return super.getNextState();
        }
    }

    @Override
    protected void handleMultipleRobotStatus(List<MultiRobotStatus> robotStatusList) {
        switch (mStatus) {
            case START:
                startStatus(robotStatusList);
                break;
            case GO_ELEVATOR_WAIT_POINT:
                break;
        }
    }

    @Override
    protected boolean isEnableMultiRobotStatus() {
        return true;
    }

    private void startStatus(List<MultiRobotStatus> robotStatusList) {
        //去电梯等待点中可用的点
        Pose backupDes = findAvailableDestination(robotStatusList);
        if (backupDes == null) {
            Log.d(TAG, "startStatus no available destination");
            onStatusUpdate(Definition.ERROR_NO_AVAILABLE_DESTINATION, "no available destination");
            return;
        }

        updateStatus(Status.GO_ELEVATOR_WAIT_POINT);
        startGoWaitPoint(backupDes);
    }

    private void startGoWaitPoint(Pose backupDes) {
        if (backupDes == null) {
            Log.e(TAG, "startGoWaitPoint backupDes is null");
            return;
        }
        NavigationAdvancedBean bean = cloneNaviBean();
        bean.setNaviType(TYPE_POSE);
        bean.setDestination("");
        bean.setNaviPose(backupDes);
        bean.setAdjustAngle(false); //到电梯等待点需要归正位姿
        startNavigation(bean);
    }

    /**
     * 寻找可以到达的备用点
     *
     * @return 如果所有备用点都不可到达，返回null，否则返回可到达点
     */
    public Pose findAvailableDestination(List<MultiRobotStatus> robotStatusList) {
        Log.d(TAG, "findAvailableDestination mWaitPointList:" + mGson.toJson(mWaitPointList));
        for (int i = 0; i < mWaitPointList.size(); i++) {
            Pose pose = mWaitPointList.get(i);
            boolean inUse = isPoseInUseCurrently(pose, robotStatusList);
            Log.d(TAG, "findAvailableChargeAreaDestination pose:" + pose.getName() + " inUse: " + inUse);
            if (!inUse) {
                return pose;
            }
        }
        return null;
    }

    /**
     * 判断某个点位当前是不是可用
     */
    protected boolean isPoseInUseCurrently(Pose pose, List<MultiRobotStatus> robotStatusList) {
        if (pose == null) {
            return false;
        }
        for (int i = 0; i < robotStatusList.size(); i++) {
            MultiRobotStatus robotStatus = robotStatusList.get(i);
            if (robotStatus.isCurRobot()) {
                Log.d(TAG, "isPoseInUseCurrently current robot");
                continue;
            }
            //status 为1表示无定位 2表示未在导航 3表示在导航中
            BasePoseBean poseBean = null;
            switch (robotStatus.getStatus()) {
                case 2:
                    //不在导航中，如果机器人当前点位在目标点，则目标点也属于不可达
                    poseBean = robotStatus.getPose();
                    break;
                case 3:
                    //处于导航模式，如果目标点被占用，属于不可达
                    poseBean = robotStatus.getGoal();
                    break;
                default:
                    break;
            }
            if (poseBean == null) {
                continue;
            }
            boolean inUse = calculateTargetPoseInUse(pose, poseBean);
            if (inUse) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否到达某点位范围内
     *
     * @param pose     机器人想要去的目标点
     * @param goalPose 另外一台机器当前导航的目标点
     * @return true 目标点不能到达 false  目标点可以去
     */
    protected boolean calculateTargetPoseInUse(Pose pose, BasePoseBean goalPose) {
        if (pose == null && goalPose == null) {
            return false;
        }
        double distance = Math.sqrt(Math.pow((pose.getX() - goalPose.getX()), 2)
                + Math.pow((pose.getY() - goalPose.getY()), 2));
        boolean inUse = isInPoseRange(distance);
        Log.d(TAG, "Target Pose " + pose.getName() + "distance: " + distance
                + ", inRange: " + inUse
                + ", pose:" + pose.toString()
                + ", goal:" + goalPose.toString());
        return inUse;
    }

    /**
     * 判断距离是否小于参数设置的，多机点位膨胀范围内
     *
     * @return true：在范围内
     */
    protected boolean isInPoseRange(double distance) {
        boolean inRange = distance < 0.5;
        Log.d(TAG, "isInPoseRange: " + inRange);
        return inRange;
    }

}
