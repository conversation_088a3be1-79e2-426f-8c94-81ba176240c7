/*
 *
 *   Copyright (C) 2017 OrionStar Technology Project
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *          http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 *
 *
 */
package com.ainirobot.coreservice.client.person;

import android.text.TextUtils;

import com.ainirobot.coreservice.client.listener.Person;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

public class PersonUtils {
    /**
     * Get all persons with face
     */
    public static List<Person> getAllFaces(List<Person> personList) {
        if (null == personList || personList.size() <= 0) {
            return null;
        }

        List<Person> allFaceList = new CopyOnWriteArrayList<>();
        for (Person person : personList) {
            if (person.isWithFace()) {
                allFaceList.add(person);
            }
        }
        return allFaceList;
    }

    /**
     * Get all persons with complete face (person id >= 0)
     */
    public static List<Person> getAllCompleteFaces(List<Person> personList) {
        if (null == personList || personList.size() <= 0) {
            return null;
        }

        List<Person> completeFaceList = new CopyOnWriteArrayList<>();
        for (Person person : personList) {
            if (isCompleteFace(person)) {
                completeFaceList.add(person);
            }
        }
        return completeFaceList;
    }

    /**
     * Get all persons with body
     */
    public static List<Person> getAllBodys(List<Person> personList) {
        if (null == personList || personList.size() <= 0) {
            return null;
        }

        List<Person> bodyList = new CopyOnWriteArrayList<>();
        for (Person person : personList) {
            if (isBody(person)) {
                if (!isFace(person)) {
                    person.setId(-1);
                }
                bodyList.add(person);
            }
        }
        return bodyList;
    }

    public static Person getBestFace(List<Person> list) {
        if (list == null || list.size() <= 0) {
            return null;
        }

        Person person = null;
        for (Person newPerson : list) {
            double distance = newPerson.getDistance();
            double faceAngelX = newPerson.getFaceAngleX();
            if (isFace(newPerson)) {
                if (person == null || person.getDistance() > distance) {
                    person = newPerson;
                } else if (person.getDistance() == distance
                        && Math.abs(person.getFaceAngleX()) > Math.abs(faceAngelX)) {
                    person = newPerson;
                }
            }
        }
        return person;
    }

    public static Person getBestFace(List<Person> list, double maxDistance,
                                     double maxFaceAngelX) {
        if (list == null || list.size() <= 0) {
            return null;
        }

        Person person = null;
        for (Person newPerson : list) {
            double distance = newPerson.getDistance();
            double faceAngelX = newPerson.getFaceAngleX();
            if (isFace(newPerson) && distance <= maxDistance && Math.abs(faceAngelX) <= Math.abs(maxFaceAngelX)) {
                if (person == null || person.getDistance() > distance) {
                    person = newPerson;
                } else if (person.getDistance() == distance
                        && Math.abs(person.getFaceAngleX()) > Math.abs(faceAngelX)) {
                    person = newPerson;
                }
            }
        }
        return person;
    }

    public static Person getBestBody(List<Person> list, double maxDistance) {
        if (list == null || list.size() <= 0) {
            return null;
        }

        Person person = null;
        for (Person newPerson : list) {
            double distance = newPerson.getDistance();
            double faceAngelX = newPerson.getFaceAngleX();
            if (isBody(newPerson) && distance <= maxDistance) {
                if (!isFace(newPerson)) {
                    newPerson.setId(-1);
                }
                if (person == null || person.getDistance() > distance) {
                    person = newPerson;
                } else if (person.getDistance() == distance
                        && Math.abs(person.getFaceAngleX()) > Math.abs(faceAngelX)) {
                    person = newPerson;
                }
            }
        }
        return person;
    }

    public static List<Person> getPersonList(List<Person> personList, double maxDistance) {
        if (personList == null || personList.size() <= 0) {
            return null;
        }

        List<Person> newPersonList = new CopyOnWriteArrayList<>();
        for (Person person : personList) {
            if (person.getDistance() <= maxDistance) {
                newPersonList.add(person);
            }
        }
        return newPersonList;
    }

    public static String parseRegisterName(String params) {
        String name = "";
        if (TextUtils.isEmpty(params) || "[]".equals(params) || "{}".equals(params)) {
            return name;
        }

        try {
            JSONObject jsonObject = new JSONObject(params);
            String slots = jsonObject.optString("slots");
            if (!TextUtils.isEmpty(slots) && !"[]".equals(slots) && !"{}".equals(slots)) {
                JSONArray start = new JSONObject(slots).optJSONArray("start");
                if (start != null) {
                    JSONObject jsonObj = start.getJSONObject(0);
                    name = jsonObj.optString("value");
                }
            }
            return name;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return name;
    }

    public static Person getSpecialPerson(List<Person> personList, int personId) {
        if (personList == null || personList.size() <= 0) {
            return null;
        }

        for (Person newPerson : personList) {
            if (newPerson.getId() == personId) {
                return newPerson;
            }
        }
        return null;
    }

    public static boolean isFace(Person person) {
        return person.isWithFace();
    }

    public static boolean isCompleteFace(Person person) {
        return person.getId() >= 0 && person.isWithFace() && !person.isOtherFace();
    }

    public static boolean isBody(Person person) {
        return person.isWithBody();
    }
}
