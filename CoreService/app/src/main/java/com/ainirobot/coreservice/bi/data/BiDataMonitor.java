package com.ainirobot.coreservice.bi.data;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.util.Log;

import com.google.gson.Gson;

public class BiDataMonitor extends BroadcastReceiver {

    private static final String TAG = "BiDataReceiver";

    private static BiDataMonitor receiver;

    private static final String ACTION_WAKEUP_ID = "action_wakeup_id";
    private static final String ACTION_CHANGE_MODULE = "action_change_module";
    private static final String ACTION_WAIT_MODULE = "action_wait_module";
    private static final String ACTION_REMOVE_WAIT_MODULE = "action_remove_wait_module";
    private static final String ACTION_NLP_STATE_CHANGED = "com.ainirobot.action.nlp_state_changed";

    private static final String KEY_WAKEUP_ID = "data_wakeup_id";
    private static final String KEY_MODULE = "data_change_module";
    private static final String KEY_WAIT_MODULE = "data_wait_module";
    private static final String KEY_NLP_STATE = "data_nlp_state";

    private BiData biData = BiData.getInstance();

    private BiDataMonitor() {
    }

    public static void start(Context context) {
        synchronized (TAG) {
            if (receiver != null) {
                return;
            }
            receiver = new BiDataMonitor();
        }

        Log.d(TAG, "BiDataMonitor start");

        IntentFilter filter = new IntentFilter();
        filter.addAction(ACTION_WAKEUP_ID);
        filter.addAction(ACTION_CHANGE_MODULE);
        filter.addAction(ACTION_WAIT_MODULE);
        filter.addAction(ACTION_REMOVE_WAIT_MODULE);
        filter.addAction(ACTION_NLP_STATE_CHANGED);
        context.registerReceiver(receiver, filter);
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent == null || intent.getAction() == null) {
            return;
        }

        switch (intent.getAction()) {
            case ACTION_WAKEUP_ID:
                onWakeupIdChange(intent);
                break;

            case ACTION_CHANGE_MODULE:
                onModuleChange(intent);
                break;

            case ACTION_WAIT_MODULE:
                onWaitModuleChange(intent);
                break;

            case ACTION_REMOVE_WAIT_MODULE:
                removeWaitModule();
                break;

            case ACTION_NLP_STATE_CHANGED:
                onNlpStateChange(intent);
                break;

            default:
                break;
        }
    }

    private void onNlpStateChange(Intent intent) {
        String nlpState = intent.getStringExtra(KEY_NLP_STATE);
        Gson gson = new Gson();
        BiData.NlpData data = null;
        try {
            data = gson.fromJson(nlpState, BiData.NlpData.class);
        } catch (Exception e) {
            Log.d(TAG, "onNlpStateChange invalid");
        }
        Log.d(TAG, "onNlpStateChange : " + (data == null ? "null" : data.toString()));
        biData.setNlpData(data);
    }

    private void onWakeupIdChange(Intent intent) {
        String wakeupId = intent.getStringExtra(KEY_WAKEUP_ID);
        Log.d(TAG, "On wakeup id change : " + wakeupId);
        biData.setWakeUpId(wakeupId);
    }

    private void onModuleChange(Intent intent) {
        String module = intent.getStringExtra(KEY_MODULE);
        Log.d(TAG, "On module change : " + module);
        biData.setCurrentModule(module);
    }

    private void onWaitModuleChange(Intent intent) {
        String waitModule = intent.getStringExtra(KEY_WAIT_MODULE);
        Log.d(TAG, "On wait module change : " + waitModule);
        biData.setWaitModule(waitModule);
    }

    private void removeWaitModule() {
        biData.setWaitModule("");
    }
}
