package com.ainirobot.coreservice.bi.report;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

public class TimeWarningReport extends BaseBiReport {

    public static final int TYPE_SYNC_TIME_FAILED = 1;
    public static final int TYPE_OVER_EXPIRE = 2;

    public static final int ACTION_TRIGGER = 1;

    private static final String TYPE = "type";
    private static final String ACTION = "action";
    private static final String CTIME = "ctime";

    public TimeWarningReport() {
        super("base_robot_check_time");
    }

    public TimeWarningReport addType(int type) {
        addData(TYPE, type);
        return this;
    }

    public TimeWarningReport addAction(int action) {
        addData(ACTION, action);
        return this;
    }

    @Override
    public void report() {
        addData(CTIME, System.currentTimeMillis());
        super.report();
    }
}
