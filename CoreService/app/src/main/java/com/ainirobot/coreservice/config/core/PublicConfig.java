/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.config.core;

import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import java.util.Map;

public class PublicConfig extends BaseConfig {
    private static final String TAG = PublicConfig.class.getSimpleName();

    private String clientId;
    private LanguageConfig mLangConfig;
    private MapCompatibilityConfig mMapCompatibilityConfig;

    public PublicConfig(JsonObject jsonObject) {
        super(jsonObject, true);
    }

    @Override
    public void parseConfig(JsonObject config) {
        clientId = "";

        if (config == null) {
            Log.d(TAG, "parseConfig config null");
            return;
        }
        for (Map.Entry<String, JsonElement> entry : config.entrySet()) {
            String enKey = ConfigDictionary.parsePublicConfig(entry.getKey());
            if ("clientId".equals(enKey)) {
                clientId = entry.getValue().getAsString();
            } else if ("systemLanguage".equals(enKey)) {
                parseLangConfig(entry.getValue());
            }else if ("mapCompatibilityConfig".equals(enKey)) {
                parseMapCompatibilityConfig(entry.getValue());
            }
        }
        Log.d(TAG, toString());
    }

    public String getClientID() {
        return clientId;
    }

    public LanguageConfig getLanguageConfig() {
        return this.mLangConfig;
    }

    public MapCompatibilityConfig getMapCompatibilityConfig() {
        return this.mMapCompatibilityConfig;
    }

    private void parseLangConfig(JsonElement jsonElement) {
        if (jsonElement == null ||
                !jsonElement.isJsonObject()) {
            Log.e(TAG, "parseLangConfig: No language config!");
            return;
        }
        String jsonStr = new Gson().toJson(jsonElement);
        Log.i(TAG, "parseLangConfig: jsonStr=" + jsonStr);
        mLangConfig = new Gson().fromJson(jsonStr, LanguageConfig.class);
        Log.i(TAG, "parseLangConfig: mLangConfig=" + mLangConfig.toString());
    }

    private void parseMapCompatibilityConfig(JsonElement jsonElement) {
        if (jsonElement == null ||
                !jsonElement.isJsonObject()) {
            Log.e(TAG, "parseMapCompatibilityConfig: No map compatibility config!");
            return;
        }
        String jsonStr = new Gson().toJson(jsonElement);
        Log.i(TAG, "parseMapCompatibilityConfig: jsonStr=" + jsonStr);
        mMapCompatibilityConfig = new Gson().fromJson(jsonStr, MapCompatibilityConfig.class);
        Log.i(TAG, "parseMapCompatibilityConfig: mMapCompatibilityConfig=" +
                mMapCompatibilityConfig.toString());
    }

}
