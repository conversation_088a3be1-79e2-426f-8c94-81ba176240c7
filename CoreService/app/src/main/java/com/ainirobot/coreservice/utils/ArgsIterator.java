package com.ainirobot.coreservice.utils;

public class ArgsIterator {
    private final String[] args;
    private int pos = 0;

    public ArgsIterator(String[] args) {
        this.args = args;
    }

    /***
     *
     * @return EMPTY String (Not null) if next argument not exit
     */
    public String next() {
        return next("");
    }

    /***
     *
     * @param def
     * @return def if next argument not exist
     */
    public String next(String def) {
        return (args.length <= pos) ? def : args[pos++];
    }

    /***
     *
     * @return next argument, otherwise throw IllegalArgumentException
     * @exception IllegalArgumentException if next argument not exist
     */
    public String nextRequired() {
        if (args.length <= pos) {
            throw new IllegalArgumentException("Argument expected after \"" + current() + "\"");
        } else {
            return next();
        }
    }

    /***
     *
     * @return current argument
     */
    public String current() {
        return args[pos - 1];
    }

    public String get(int offset) {
        return get(offset, "");
    }

    public String get(int offset, String def) {
        if (args.length <= pos + offset) {
            return def;
        } else {
            return args[pos + offset];
        }
    }
}
