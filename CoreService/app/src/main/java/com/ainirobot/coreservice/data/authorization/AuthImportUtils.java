package com.ainirobot.coreservice.data.authorization;

import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.database.sqlite.SQLiteDatabase;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.client.actionbean.AuthBean;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class AuthImportUtils {
    private static final String TAG = "AuthImportUtils";
    private static final String ACTION = "action.orionstar.default.app";
    private static final String MODULE_APP_PACKAGE = "com.ainirobot.moduleapp";

    /**
     * 初始化应用列表
     *
     * @param db
     */
    public static void initAuth(SQLiteDatabase db) {
        Log.d(TAG, "initAuth: ----start");

        List<ApplicationInfo> apps = queryFilterAppInfo();
        for (ApplicationInfo app : apps) {
            String pkgName = app.packageName;
            int isAuth = AuthBean.AuthState.NOT_AUTH;
            Log.d(TAG, "initAuth: pkgName=" + pkgName);
            ContentValues contentValues = new ContentValues();
            contentValues.put(AuthDataHelper.AuthField.PKG_NAME, pkgName);
            contentValues.put(AuthDataHelper.AuthField.IS_AUTH, isAuth);
            db.insert(AuthDataHelper.AuthField.TABLE_NAME, null,
                    contentValues);
        }
        Log.d(TAG, "initAuth: ----end");
    }

    private static List<ApplicationInfo> queryFilterAppInfo() {
        PackageManager pm = ApplicationWrapper.getContext().getPackageManager();
        // 查询所有已经安装的应用程序
        List<ApplicationInfo> appInfos = pm
                .getInstalledApplications(PackageManager.GET_UNINSTALLED_PACKAGES);
        // GET_UNINSTALLED_PACKAGES代表已删除，但还有安装目录的
        List<ApplicationInfo> applicationInfos = new ArrayList<>();

        // 创建一个类别为CATEGORY_LAUNCHER的该包名的Intent
        Intent resolveIntent = new Intent(ACTION, null);
        resolveIntent.addCategory(Intent.CATEGORY_DEFAULT);

        // 通过getPackageManager()的queryIntentActivities方法遍历,得到所有能打开的app的packageName
        List<ResolveInfo> resolveinfoList = pm.queryIntentActivities(resolveIntent, 0);
        Set<String> allowPackages = new HashSet<>();
        for (ResolveInfo resolveInfo : resolveinfoList) {
            allowPackages.add(resolveInfo.activityInfo.packageName);
        }

        for (ApplicationInfo app : appInfos) {
            if (allowPackages.contains(app.packageName)
                    && !TextUtils.equals(MODULE_APP_PACKAGE, app.packageName)) {
                applicationInfos.add(app);
            }
        }
        return applicationInfos;
    }
}
