/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.resmanage;

import com.ainirobot.coreservice.action.AbstractAction;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.core.InternalDef;
import com.ainirobot.coreservice.core.LocalApi;

import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;

public class AssignedRes {
    private static final String TAG = "AssignedRes";
    private final ArrayList<AbstractResProxy> mRes = new ArrayList<>();
    private HashMap<String, AbstractResProxy> mCmdToResMap = new HashMap<>();
    private ResManager mResManager;
    private int mAssignedId = Definition.ACTION_INVALID;

    private String language;


    AssignedRes(ResManager resManager, AbstractAction action) {
        mResManager = resManager;
        mAssignedId = action.getActionId();
    }

    void addRes(AbstractResProxy res) {
        synchronized (mRes) {
            if (res != null) {
                mRes.add(res);
                Iterator<String> cmds = res.getSupportCmds();
                while (cmds.hasNext()) {
                    mCmdToResMap.put(cmds.next(), res);
                }
            }
        }
    }

    public boolean hasNavigation() {
        for (AbstractResProxy res : mRes) {
            if (res instanceof Chassis) {
                return true;
            }
        }
        return false;
    }

    public boolean hasHead() {
        for (AbstractResProxy res : mRes) {
            if (res instanceof Head) {
                return true;
            }
        }
        return false;
    }

    public int sendCommand(int reqId, String command, String params, boolean isContinues,
                           LocalApi.OnCmdResponse response) {
        return mResManager.sendCommand(reqId, command, params, this.language, isContinues, response);
    }

    public boolean cancelCommand(int cmdId, String command, boolean isForceStop) {
        return mResManager.cancelCommand(cmdId, isForceStop);
    }

    public boolean stop() {
        try {
            boolean result = true;
            synchronized (mRes) {
                for (AbstractResProxy res : mRes) {
                    if (!res.stop()) {
                        result = false;
                        break;
                    }
                }
            }
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return true;
        }
    }

    public boolean release() {
        boolean result = true;
        try {
            synchronized (mRes) {
                for (AbstractResProxy res : mRes) {
                    if (!res.release()) {
                        result = false;
                        break;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (result) {
            synchronized (mRes) {
                for (AbstractResProxy res : mRes) {
                    mResManager.finish(res);
                }
                mRes.clear();
            }
            mCmdToResMap.clear();
            mResManager.recycleAssignedRes(this);
            mAssignedId = Definition.ACTION_INVALID;
        }
        return result;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    int getAssignedId() {
        return mAssignedId;
    }

     void dump(String prefix, PrintWriter writer, String args) {
        for (AbstractResProxy res : mRes) {
            if (res.getResId() < 0) {
                writer.println(TAG + " shared res");
                continue;
            }
            writer.println(TAG + " [" + InternalDef.RES_NAMES[res.getResId()] + "]isAvailable:" + res.isAvailable());
        }
         for (HashMap.Entry<String, AbstractResProxy> entry : mCmdToResMap.entrySet()) {
             writer.println(TAG + " cmd " + entry.getKey());
         }
    }
}