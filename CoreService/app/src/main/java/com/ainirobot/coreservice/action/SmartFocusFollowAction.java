/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.action;

import android.os.SystemClock;
import android.util.Log;

import com.ainirobot.coreservice.action.StopCommandList.IStopCommandChecker;
import com.ainirobot.coreservice.action.StopCommandList.StopCommand;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.actionbean.Person;
import com.ainirobot.coreservice.client.actionbean.SmartFocusFollowBean;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.core.module.Module;
import com.ainirobot.coreservice.core.system.RobotSetting;
import com.ainirobot.coreservice.test.TestModule;
import com.ainirobot.coreservice.utils.DelayTimer;
import com.ainirobot.coreservice.utils.MessageParser;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicInteger;

public class SmartFocusFollowAction extends AbstractAction<SmartFocusFollowBean> {

    enum State {
        IDLE, //空闲
        TRY_TRACK, //尝试追踪(有失败可能)
        TRACKING, //单人追踪
        MOVING, //转动
        MULTI, //多人模式
        WAITING, //等待，多人模式，只有一个人的时候，转向等待模式，等待一秒后开始追踪
    }

    private static final String TAG = "SmartFocusFollowAction";
    private static final long MIN_LOST_TIMEOUT = 500;
    private static final long TRACK_TIMEOUT = RobotSetting.getFocusFollowTrackTimeout();

    private static final int MIN_FRAME_COUNT = 3; //Distance to determine the minimum number of frames
    private static final long BASE_TIME = 100;

    //讲话完一秒内都算有效讲话人
    private static final long SPEAK_TIMEOUT = 1000;
    private static final long WAIT_TIMEOUT = 1000;
    private static final long MOVE_TIMEOUT = 2000;

    private static final long FRAME_INTERVAL = 50;

    private long mLostTimeout;
    private double mAvailableDistance;
    private String mSpeedListener;
    private String mSpeakerListener;
    private DelayTimer mLostTimer;
    private DelayTimer mTrackTimer;
    private AtomicInteger mFrameCount = new AtomicInteger();

    private boolean isAllowMoveBody = true;
    private float bodySpeed = 0.5f;

    private long mMoveTime = 0;
    private int mLastAngle = 0;
    private float mOffset = 3.0f;

    private static final int OFFSET_ANGLE_MINI = 10;

    private State mState = State.IDLE;
    private int initMode = SmartFocusFollowBean.InitMode.TRACK;

    private int speakerId = -1; //当前讲话人id
    private boolean isSpeaking = false; //是否还在讲话中
    private long speakFinishTime = 0; //讲话结束时间

    private long waitStartTime = 0;
    private long moveStartTime = 0;

    private Person trackingPerson;
    private boolean isTrackTimeout = false;

    private DelayTimer mFrameTimer; //用来补帧
    private Person preFrame;
    private Person.FaceInfo mFaceInfo;

    public SmartFocusFollowAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_SMART_FOCUS_FOLLOW, resList);
    }

    @Override
    protected boolean startAction(SmartFocusFollowBean parameter, LocalApi api) {
        Log.d(TAG, "Smart focus follow started : " + TestModule.initMode);
        mState = State.IDLE;
        mReqId = parameter.getReqId();
        parseParams(parameter);

        mFrameCount.set(0);

        mLastAngle = 0;
        mMoveTime = 0;

        mOffset = RobotSetting.getFocusFollowAngleAdjust();
        Log.d(TAG, "mOffset:" + mOffset);

        registerSpeedListener();
        registerSpeakerListener();

        //获取多人脸模式下的人脸信息
        mApi.getAllPersonInfos(mReqId, Definition.JSON_HEAD_CONTINUOUS);

        //单人脸模式下获取其它人脸信息
        mApi.getAllPersonInfosOnTrack(mReqId, Definition.JSON_HEAD_CONTINUOUS);
        return true;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        Log.d(TAG, "Smart focus follow stopped : " + isResetHW);
        unregisterStatusListener(mSpeedListener);
        unregisterStatusListener(mSpeakerListener);

        if (mState == State.MULTI) {
            TestModule.initMode = SmartFocusFollowBean.InitMode.MULTI_STATION;
            TestModule.timeStamp = System.currentTimeMillis();
            mState = State.IDLE;
        }

        mApi.stopSendPersonInfos();
        mApi.stopSendPersonInfosOnTrack();
        if (isAllowMoveBody) {
            mApi.stopMove(mReqId);
        }

        mApi.stopTrack(mReqId);
        mLastAngle = 0;
        mMoveTime = 0;

        destroyTimer();

        if (isResetHW) {
            mApi.resetHead(mReqId);
        }
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_HEAD_GET_ALL_PERSON_INFOS:
                if (mState == State.TRACKING) {
                    handleSingleFaceInfo(result);
                } else {
                    handleMultipleFaceInfo(result);
                }
                break;

            case Definition.CMD_HEAD_GET_ALL_PERSON_INFOS_ON_TRACK:
                handleMultipleFaceInfo(result);
                break;

            case Definition.CMD_HEAD_SET_TRACK_TARGET:
                handleTrackResult(result);
                break;

            case Definition.CMD_NAVI_STOP_MOVE:
                handleOtherSpeaker();
                break;

            default:
                break;
        }
        return true;
    }

    private void initTimer() {
        destroyTimer();

        Log.d(TAG, "Timer init");
        mLostTimer = new DelayTimer(mLostTimeout, new Runnable() {
            @Override
            public void run() {
                onPersonLost();
            }
        });
        mLostTimer.start();

        mTrackTimer = new DelayTimer(TRACK_TIMEOUT, new Runnable() {
            @Override
            public void run() {
                onTrackTimeout();
            }
        });
        mTrackTimer.start();

        mFrameTimer = new DelayTimer(FRAME_INTERVAL, new Runnable() {
            @Override
            public void run() {
                if (preFrame != null) {
                    Log.d(TAG, "Move body frame : " + preFrame.getFaceInfo().getAngle());
                    moveBody(preFrame);
                }
            }
        });
        mFrameTimer.start();
    }

    private void destroyTimer() {
        if (mLostTimer != null) {
            mLostTimer.destroy();
            mLostTimer = null;
        }
        if (mTrackTimer != null) {
            mTrackTimer.destroy();
            mTrackTimer = null;
        }
        if (mFrameTimer != null) {
            mFrameTimer.destroy();
            mFrameTimer = null;
        }
    }

    private void handleTrackResult(String params) {
        try {
            Log.d(TAG, "Set track target result : " + params);
            JSONObject json = new JSONObject(params);
            String status = json.getString("status");
            switch (status) {
                case Definition.RESPONSE_OK:
                    mState = State.TRACKING;
                    initTimer();
                    onStatusUpdate(Definition.STATUS_TRACK_TARGET_SUCCEED, "Track target succeed");
                    break;

                case Definition.RESPONSE_TARGET_NOT_FOUND:
                    updateState(State.IDLE);
                    //onError(Definition.ERROR_TARGET_NOT_FOUND, "Target not found");
                    break;

                case Definition.RESPONSE_ALREADY_IN_MODE:
                    Log.d(TAG, "Current already int track mode");
                    break;

                default:
                    break;
            }
        } catch (JSONException e) {
            e.printStackTrace();
            updateState(State.IDLE);
//            onError(Definition.ERROR_SET_TRACK_FAILED, "Set track target failed");
        }
    }

    /**
     * 处理多人脸信息
     */
    private void handleMultipleFaceInfo(String params) {
//        Log.d(TAG, "SmartFocusFollowAction handleMultipleFaceInfo : " + params + " mState:" + mState.name());
        ArrayList<Person> persons = MessageParser.parseAllFaceInfo(params, mAvailableDistance);
        switch (mState) {
            case IDLE:
                if (!persons.isEmpty()) {
                    onPersonAppear(persons);
                }
                break;

            case TRACKING:
                if (!persons.isEmpty()) {
                    if (isOtherSpeaker()) {
                        switchMulti(persons);
                    } else {
                        //因人脸id不准确，有可能匹配错误，暂时不做纠偏
//                        correctAngle(persons);
                    }
                }
                break;

            case MULTI:
                if (persons.size() <= 1) {
                    updateState(State.WAITING);
                    waitStartTime = System.currentTimeMillis();
                }
                break;

            case WAITING:
                onWaiting(persons);
                break;

            case MOVING:
                if (System.currentTimeMillis() - moveStartTime > MOVE_TIMEOUT) {
                    updateState(State.WAITING);
                    waitStartTime = System.currentTimeMillis();
                }
                break;

            default:
                break;
        }
    }

    private void handleOtherSpeaker() {
        if (mState != State.MOVING){
            Log.d(TAG, "Not moving, ignore");
            mFaceInfo = null;
            return;
        }
        if (mFaceInfo == null) {
            Log.d(TAG, "No FaceInfo, ignore");
            return;
        }

        //之前的追踪者已经不在了，直接转向说话者，
        int angle = mFaceInfo.getAngle() / 2;
        Log.d(TAG, "Other speaker angle : " + angle + " , associateId : " + mFaceInfo.getAssociateId());

        if (angle > 0) {
            mApi.turnRight(0, bodySpeed, angle);
        } else {
            mApi.turnLeft(0, bodySpeed, -angle);
        }
        moveStartTime = System.currentTimeMillis();
        mFaceInfo = null;
    }

    private void correctAngle(ArrayList<Person> persons) {
        if (isTrackTimeout) {
            return;
        }
        for (Person person : persons) {
            if (person.getFaceInfo().getAssociateId()
                    == trackingPerson.getFaceInfo().getAssociateId()) {
                if (isAllowMoveBody) {
                    int angle = person.getFaceInfo().getAngle();
                    Log.d(TAG, "Move body correct angle : " + angle);
                    moveBody(person);
                }
                break;
            }
        }
    }

    private void onPersonAppear(ArrayList<Person> persons) {
        int mode = mode();
        if (mode == SmartFocusFollowBean.InitMode.MULTI_STATION
                && persons.size() > 1) {
            updateState(State.MULTI);
            return;
        }
        updateState(State.TRY_TRACK);
        trackingPerson = selectTrackTarget(persons);
        Log.d(TAG, "Set track target : " + trackingPerson.getFaceInfo().getAssociateId());
        mApi.setFaceTrackTarget(mReqId, trackingPerson.getFaceInfo().getFaceId(), true);
    }

    /**
     * 选择跟踪目标
     */
    private Person selectTrackTarget(ArrayList<Person> persons) {
        if (isValidSpeaker()) {
            for (Person person : persons) {
                if (speakerId == person.getFaceInfo().getAssociateId()) {
                    return person;
                }
            }
        }
        return persons.get(0);
    }

    /**
     * 判断是否为说话人
     */
    private boolean isValidSpeaker() {
        if (speakerId < 0) {
            return false;
        }

        if (isSpeaking) {
            return true;
        }
        return System.currentTimeMillis() - speakFinishTime < SPEAK_TIMEOUT;
    }

    private boolean isOtherSpeaker() {
        if (!isValidSpeaker()) {
            return false;
        }
        return speakerId != trackingPerson.getFaceInfo().getAssociateId();
    }

    private void onWaiting(ArrayList<Person> persons) {
        boolean isTimeout = System.currentTimeMillis() - waitStartTime > WAIT_TIMEOUT;
        if (!isTimeout) {
            if (persons.size() > 1) {
                updateState(State.MULTI);
            }
            return;
        }
        updateState(State.IDLE);
    }

    /**
     * 切换到多人模式
     */
    private void switchMulti(ArrayList<Person> persons) {
        updateState(State.MOVING);
        Person tracking = null;
        Person speaker = null;
        for (Person person : persons) {
            int associateId = person.getFaceInfo().getAssociateId();
            if (associateId == trackingPerson.getFaceInfo().getFaceId()) {
                tracking = person;
            }

            if (person.getFaceInfo().getAssociateId() == speakerId) {
                speaker = person;
            }
        }

        //未找到讲话者
        if (speaker == null) {
            updateState(State.TRACKING);
            return;
        }

        //停止跟踪
        destroyTimer();
        mApi.stopTrack(mReqId);

        //之前的追踪者已经不在了，直接转向说话者，
//        int angle = speaker.getFaceInfo().getAngle() / 2;
        //之前的追踪者还在，转向二者之间，因机器朝向追踪者，说话者的角度既相对机器的角度，也相对追踪者的角度，只转一半
//        if (tracking != null) {
//            angle = angle / 2;
//        }
//        Log.d(TAG, "Other speaker angle : " + angle + " , associateId : " + speaker.getFaceInfo().getAssociateId());
        // 后面的实现，等待stopMove回调后再执行
        mFaceInfo = speaker.getFaceInfo();
        mApi.stopMove(Definition.MODULE_REQ_ID);
//        if (angle > 0) {
//            mApi.turnRight(0, bodySpeed, angle);
//        } else {
//            mApi.turnLeft(0, bodySpeed, -angle);
//        }
//        mApi.motionPid(mReqId, angle, speaker.getFaceInfo().getLatency());
//        moveStartTime = System.currentTimeMillis();
    }

    /**
     * 处理单人脸信息
     */
    private synchronized void handleSingleFaceInfo(String params) {
//        Log.d(TAG, "SmartFocusFollowAction handleSingleFaceInfo : " + params);
        if (mState != State.TRACKING){
            Log.d(TAG, "Not tracking, ignore");
            return;
        }
        ArrayList<Person> persons = MessageParser.parseAllFaceInfo(params, mAvailableDistance);
        if (persons.isEmpty()) {
            Log.d(TAG, "Get all person info : no person");
            return;
        }

        isTrackTimeout = false;
        Person person = persons.get(0);
        if (isAllowMoveBody) {
            preFrame = person;
            int angle = person.getFaceInfo().getAngle();
            Log.d(TAG, "Move body : " + angle);
            moveBody(person);
        }

        double distance = person.getFaceInfo().getDistance();
        if (isFaraway(distance)) {
            onStatusUpdate(Definition.STATUS_GUEST_FARAWAY, "Person is too far away ：" + distance);
        }

        Log.d(TAG, "Timer reset");
        if (mLostTimer != null) {
            mLostTimer.reset();
        }

        if (mTrackTimer != null) {
            mTrackTimer.reset();
        }

//        if (mFrameTimer != null) {
//            mFrameTimer.reset();
//        }
    }

    private void moveBody(Person person) {
        int angle = person.getFaceInfo().getAngle();
        int angleInView = -Double.valueOf(person.getFaceInfo().getAngleInView()).intValue();
        if (isNewAngle(angle) || isMoveTimeout(BASE_TIME)) {
            if (ProductInfo.isMiniProduct() || ProductInfo.isMeissa2()) {
                angle = angle + (int) mOffset;
                mApi.motionPid(mReqId, angle, person.getFaceInfo().getLatency());
            } else if (ProductInfo.isMeissaPlus()) {
//                        mApi.motionPid(mReqId, angleInView, person.getFaceInfo().getLatency());
            } else {
                mApi.motionArc(mReqId, 0, angle, person.getFaceInfo()
                                                       .getHeadSpeed(), person.getFaceInfo()
                                                                              .getLatency());
            }
            mLastAngle = angle;
            mMoveTime = SystemClock.uptimeMillis();
        }
    }

    private boolean isFaraway(double distance) {
        if (distance > mAvailableDistance) {
            return mFrameCount.incrementAndGet() >= MIN_FRAME_COUNT;
        } else {
            mFrameCount.set(0);
            return false;
        }
    }

    private synchronized void onPersonLost() {
        Log.d(TAG, "lost timeout");
        onStatusUpdate(Definition.STATUS_GUEST_LOST, "Lost timeout : " + mLostTimeout);
        destroyTimer();
        mApi.stopTrack(mReqId);
        updateState(State.IDLE);
        mApi.stopMove(mReqId);
        mApi.resetHead(mReqId);
    }

    private void onTrackTimeout() {
        Log.d(TAG, "track timeout");
        isTrackTimeout = true;
        preFrame = null;
        if (isAllowMoveBody) {
            mApi.stopMove(mReqId);
        }
    }

    private void parseParams(SmartFocusFollowBean params) {

        double maxDistance = params.getMaxDistance();
        mAvailableDistance = ((maxDistance == 0) ? Double.MAX_VALUE : maxDistance);

        long trackTimeout = params.getLostTimer();
        initMode = params.getInitMode();
        mLostTimeout = trackTimeout < MIN_LOST_TIMEOUT ? MIN_LOST_TIMEOUT : trackTimeout;
//        mLostTimeout = MIN_LOST_TIMEOUT;
        isAllowMoveBody = params.isAllowMoveBody();
        bodySpeed = RobotSetting.getDefaultBodySpeed();
        Log.d(TAG, "Smart focus follow params: mLostTimeout=" + mLostTimeout + " TRACK_TIMEOUT=" + TRACK_TIMEOUT);
    }

    @Override
    protected ArrayList<StopCommand> getStopCommands() {
        return new ArrayList<StopCommand>() {{
            //add(new StopCommand(Definition.CMD_HEAD_STOP_TRACK_TARGET, null));
        }};
    }

    @Override
    protected IStopCommandChecker getStopCommandChecker() {
        return new IStopCommandChecker() {
            @Override
            public boolean executeSuccess(String command, String result) {
                Log.d(TAG, "Focus stop check : " + command);
                switch (command) {
                    case Definition.CMD_HEAD_STOP_TRACK_TARGET:
                        return true;

                    default:
                        return false;
                }
            }
        };
    }

    @Override
    protected boolean verifyParam(SmartFocusFollowBean param) {
        return true;
    }

    private void registerSpeakerListener() {
        mSpeakerListener = registerStatusListener(Definition.STATUS_SPEAKER, new StatusListener() {
            @Override
            public void onStatusUpdate(String data) {
                if (data != null) {
                    try {
                        JSONObject json = new JSONObject(data);
                        speakerId = json.optInt("speakerId", -1);
                        isSpeaking = json.optBoolean("speaking", false);
                        onSpeakerUpdate(speakerId, isSpeaking);

                        Log.d(TAG, "On speaker update : " + data);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            }
        });
    }

    private void onSpeakerUpdate(int speakerId, boolean isSpeaking) {
        if (speakerId == -1) {
            return;
        }

        //讲话结束，更新时间
        if (!isSpeaking) {
            speakFinishTime = System.currentTimeMillis();
        }
    }

    private void registerSpeedListener() {
        mSpeedListener = registerStatusListener(Definition.STATUS_SPEED, new StatusListener() {
            @Override
            public void onStatusUpdate(String data) {
                try {
                    if (data == null) {
                        return;
                    }
                    JSONObject json = new JSONObject(data);
                    double speedZ = json.getDouble(Definition.JSON_NAVI_ANGULAR_SPEED_Z);
                    double speedX = json.getDouble(Definition.JSON_NAVI_LINEAR_SPEED_X);
                    mApi.reportNavigationStatus(mReqId, speedX, speedZ);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private boolean isNewAngle(int angle) {
        return mLastAngle != angle;
    }

    private boolean isMoveTimeout(long baseTime) {
        long currentTime = SystemClock.uptimeMillis();
        return (currentTime - mMoveTime) > baseTime;
    }

    private synchronized void updateState(State state) {
        if (mState != state) {
            Log.d(TAG, "State : " + mState + ", change to : " + state);
            mState = state;
        }
    }

    private int mode() {
        if (System.currentTimeMillis() - TestModule.timeStamp < 2000) {
            return TestModule.initMode;
        }
        return SmartFocusFollowBean.InitMode.TRACK;
    }
}
