package com.ainirobot.coreservice.client.actionbean;

/**
 * Created by Orion on 2019/4/1.
 */
public class NaviCmdTimeOutBean extends BaseBean {

    private long timestamp;
    private String cacheId;
    private String type;
    private String params;

    public NaviCmdTimeOutBean(long timestamp, String cacheId, int reqId, String type, String params) {
        this.timestamp = timestamp;
        this.cacheId = cacheId;
        super.setReqId(reqId);
        this.type = type;
        this.params = params;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getCacheId() {
        return cacheId;
    }

    public void setCacheId(String cacheId) {
        this.cacheId = cacheId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    @Override
    public String toString() {
        return "NaviCmdTimeOutBean{" +
                "timestamp=" + timestamp +
                ", cacheId='" + cacheId + '\'' +
                "reqId=" + super.getReqId() +
                ", type='" + type + '\'' +
                ", params='" + params + '\'' +
                '}';
    }

}
