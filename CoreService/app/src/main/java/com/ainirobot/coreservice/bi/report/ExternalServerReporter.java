package com.ainirobot.coreservice.bi.report;

import com.google.gson.JsonObject;

public class ExternalServerReporter extends SystemInformationReporter {
    public ExternalServerReporter() {
        super("ExternalService", "ExternalServer.java");
    }

    public void reportRegisterHWCallback(final String externalService, final String supportCommands) {
        JsonObject params = new JsonObject();
        params.addProperty("externalService", externalService);
        params.addProperty("supportCommands", supportCommands);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("registerHWCallback")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportUnregisterHWCallback(final String externalService) {
        JsonObject params = new JsonObject();
        params.addProperty("externalService", externalService);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("unregisterHWCallback")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportSendAsyncResponse(final String cmdType, final int result,
        final String message) {
        JsonObject params = new JsonObject();
        params.addProperty("cmdType", cmdType);
        params.addProperty("result", result);
        params.addProperty("message", message);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("sendAsyncResponse")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportSendAsyncStatus(final String cmdType, final String status) {
        JsonObject params = new JsonObject();
        params.addProperty("cmdType", cmdType);
        params.addProperty("status", status);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("sendAsyncStatus")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportSendStatus(final String serviceName, final String type, final String param) {
        JsonObject params = new JsonObject();
        params.addProperty("serviceName", serviceName);
        params.addProperty("type", type);
        params.addProperty("param", param);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("sendStatusReport")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportSendException(final String serviceName, final String type,
        final String param) {
        JsonObject params = new JsonObject();
        params.addProperty("serviceName", serviceName);
        params.addProperty("type", type);
        params.addProperty("param", param);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("sendExceptionReport")
            .addNodeParams(params.toString())
            .reportV();
    }
}
