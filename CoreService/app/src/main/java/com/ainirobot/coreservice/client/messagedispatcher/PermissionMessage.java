package com.ainirobot.coreservice.client.messagedispatcher;

import android.content.Intent;

import com.ainirobot.coreservice.client.permission.PermissionListener;

public class PermissionMessage implements IMsgHandle {

    static final int TYPE_ACTIVITY_STARTING = 1;
    static final int TYPE_ACTIVITY_RESUMING = 2;
    static final int TYPE_APP_CRASHED = 3;
    static final int TYPE_APP_EARLY_NOT_RESPONDING = 4;
    static final int TYPE_APP_NOT_RESPONDING = 5;
    static final int TYPE_SYSTEM_NOT_RESPONDING = 6;
    static final int TYPE_FOREGROUND_ACTIVITIES_CHANGED = 7;
    static final int TYPE_PROCESS_DIED = 8;

    private int mType;
    private PermissionListener mListener;
    private Intent mIntent;
    private String mPackage;
    private int mPid;
    private int mUid;
    private String mProcessName;
    private boolean mForegroundActivities;
    private String mMsg;
    private String mMsg2;
    private long mTimeMillis;
    private String mStackTrace;

    PermissionMessage(int type, String processName, int pid, String shortMsg, String longMsg,
                      long timeMillis, String stackTrace, PermissionListener listener) {
        mType = type;
        mProcessName = processName;
        mPid = pid;
        mMsg = shortMsg;
        mMsg2 = longMsg;
        mTimeMillis = timeMillis;
        mStackTrace = stackTrace;
        mListener = listener;
    }

    PermissionMessage(int type, int pid, int uid, boolean foregroundActivities, PermissionListener listener) {
        mType = type;
        mPid = pid;
        mUid = uid;
        mForegroundActivities = foregroundActivities;
        mListener = listener;
    }

    PermissionMessage(int type, String processName, int pid, String msg, PermissionListener listener) {
        mType = type;
        mProcessName = processName;
        mPid = pid;
        mMsg = msg;
        mListener = listener;
    }

    PermissionMessage(int type, Intent intent, String pkg, PermissionListener listener) {
        mType = type;
        mIntent = intent;
        mPackage = pkg;
        mListener = listener;
    }

    PermissionMessage(int type, String pkg, String msg, PermissionListener listener) {
        mType = type;
        mPackage = pkg;
        mMsg = msg;
        mListener = listener;
    }

    PermissionMessage(int type, String msg, PermissionListener listener) {
        mType = type;
        mMsg = msg;
        mListener = listener;
    }

    PermissionMessage(int type, int pid, int uid, PermissionListener listener) {
        mType = type;
        mPid = pid;
        mUid = uid;
        mListener = listener;
    }

    @Override
    public void handleMessage() {
        switch (mType) {
            case TYPE_ACTIVITY_STARTING:
                mListener.activityStarting(mIntent, mPackage);
                break;
            case TYPE_ACTIVITY_RESUMING:
                mListener.activityResuming(mPackage);
                break;
            case TYPE_APP_CRASHED:
                mListener.appCrashed(mProcessName, mPid, mMsg, mMsg2, mTimeMillis, mStackTrace);
                break;
            case TYPE_APP_EARLY_NOT_RESPONDING:
                mListener.appEarlyNotResponding(mProcessName, mPid, mMsg);
                break;
            case TYPE_APP_NOT_RESPONDING:
                mListener.appNotResponding(mProcessName, mPid, mMsg);
                break;
            case TYPE_SYSTEM_NOT_RESPONDING:
                mListener.systemNotResponding(mMsg);
                break;
            case TYPE_FOREGROUND_ACTIVITIES_CHANGED:
                mListener.onForegroundActivitiesChanged(mPid, mUid, mForegroundActivities);
                break;
            case TYPE_PROCESS_DIED:
                mListener.onProcessDied(mPid, mUid);
                break;
            default:
                break;
        }
    }
}
