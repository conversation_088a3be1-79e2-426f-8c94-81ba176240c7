/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.ainirobot.coreservice.action;

import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.Person;
import com.ainirobot.coreservice.client.actionbean.PictureInfo;
import com.ainirobot.coreservice.client.actionbean.RegisterBean;
import com.ainirobot.coreservice.client.actionbean.RegisterRemoteBean;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.utils.MessageParser;
import com.ainirobot.coreservice.utils.Utils;
import com.ainirobot.robotlog.RobotLog;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.atomic.AtomicInteger;

public class RegisterActionNew extends AbstractAction<RegisterBean> {

    private static final String Tag = "RegiNew";
    private static final int PICTURE_NUMBER = 3;
    private static final int REMOTE_REGISTER = 1;
    private static final int REMOTE_DETECT = 2;
    private static final long TIME_OUT_GAIN_PIC = 800L;
    private int mRetry = 1; // times to register , only once default .
    private int DEFAULT_RETRY = 1;
    private int mRemoteType = 0;
    private AtomicInteger atomicInteger = new AtomicInteger(DEFAULT_RETRY);
    private volatile boolean mIsSpeechOnce = false;
    private long mStartTime = Long.MAX_VALUE;
    private long mStartRemote = Long.MAX_VALUE;
    private PictureInfo mPictureInfo;
    private Timer mGetPicTimer;
    private long mSecondDelay;
    private Timer mGetPicFailTimer;
    private volatile boolean mIsGetPicFail = false;
    private volatile boolean mIsStartRemoteReq = false;

    private enum State {
        REGISTER, REGISTERING, REGISTERED, STOPPED, TIMEOUT
    }

    private Person mPerson = null;
    private Timer mRegisterTimer;
    private String mPersonName;
    private long mTimeout;
    private String welcomeContent;

    private int mReqId;
    private volatile State mStatus = State.REGISTER;


    protected RegisterActionNew(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_REGISTER, resList);
    }

    @Override
    protected boolean startAction(RegisterBean parameter, LocalApi api) {

        if (verifyParam(parameter)){
            cancelGetPicTimer();
            resetInitState();
            mStartTime = System.currentTimeMillis();
            RobotLog.d(Tag, "Register action started, reqId = " + mReqId+",mPersonName = "+mPersonName
                    +", mTimeout : "+mTimeout+", mRemoteType : "+mRemoteType +", welcomeContent : "+welcomeContent);
            mApi.getAllPersonInfos(mReqId, Definition.JSON_HEAD_CONTINUOUS);
            startRegisterTimer();
            speechDelayTimer();
            startGetPicFailTimer();
        }
        return true;
    }


    @Override
    protected boolean verifyParam(RegisterBean param) {
        mReqId = param.getReqId();
        mPersonName = param.getPersonName();
        welcomeContent = param.getWelcomeContent();
        mTimeout = param.getTime();
        if (mTimeout < 0){
            mTimeout = 0;
        }
        mRetry = param.getCount();
        mSecondDelay = param.getSecondDelay();
        if (mSecondDelay < 0){
            mSecondDelay = 0;
        }
        mRemoteType = param.getRemoteType();
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_HEAD_GET_ALL_PERSON_INFOS:
                // will callback all the time when capturing faces.
                if (mStatus != State.REGISTER) {
                    return false;
                }
                if (mStatus == State.STOPPED){
                    return true;
                }
                long time = System.currentTimeMillis() - mStartTime;
                RobotLog.d(Tag, "getPersonInfos = " + result + ", getAllPersonInfos time = " + time );
                mPerson = MessageParser.parseFaceInfo(null, result);
                if (mPerson != null) {
                    boolean isFaceIdOK = checkFaceIdOK();
                    Log.d(Tag,"isFaceIdOK : "+isFaceIdOK);
                    if (!isFaceIdOK){
                        return false;
                    }
                    mStatus = State.REGISTERING;
                    if (mIsSpeechOnce){
                        RobotLog.d(Tag,"getPersonInfo mIsSpeechOnce is true");
                        cancelGetPicTimer();
                        startGetPicAgainTimer(false);
                    }else {
                        mApi.getPictureById(mReqId, mPerson.getFaceInfo().getFaceId(), PICTURE_NUMBER);
                    }
                } else {
                    return false;
                }
                return true;

            case Definition.CMD_HEAD_GET_PICTURE_BY_ID:
                handlePicture(result);
                return true;
            case Definition.CMD_REMOTE_REGISTER:
                handleRemoteRegister(result);
                return true;
            case Definition.CMD_REMOTE_DETECT:
                handleRemoteDetect(result);
                return true;
            default:
                return false;
        }
    }

    private boolean checkFaceIdOK() {
        Person.FaceInfo faceInfo = mPerson.getFaceInfo();
        if (faceInfo != null){
            Log.d(Tag,"check FaceId : "+faceInfo.getFaceId());
        }
        return faceInfo != null && faceInfo.getFaceId() >= 0;
    }


    private void speechDelayTimer() {
        Timer timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                speechDelay();
            }
        },500);
    }

    private void speechDelay() {
        // because will getPersonInfos many times. if faceInfos is null , speech tts only once;
        if (mPerson == null && !mIsSpeechOnce && mStatus != State.REGISTERED){
            RobotLog.d(Tag,"getPersonInfo mIsSpeechOnce is false,atomicInteger.get() = 1");
            mIsSpeechOnce = true;
            onStatusUpdate(Definition.STATUS_COLLECT_PICTURE, atomicInteger.get() + "");
        }
    }


    private void handlePicture(String result) {
        RobotLog.d(Tag, "handlePicture mState == REGISTERING ? " + (mStatus == State.REGISTERING)
                + ", getPic time = " + (System.currentTimeMillis() - mStartTime));
        mPictureInfo = MessageParser.parsePictureInfo(result);
        if (mStatus == State.STOPPED){
            Log.d(Tag,"handlePicture RESULT_STOP ");
            return;
        }

        if (mStatus != State.REGISTERING) {
            return;
        }
        RobotLog.d(Tag, "handle picture result = " + result);
//        PictureInfo mPictureInfo = mGson.fromJson(result, PictureInfo.class);

        if (isGetPicFail())
            return;

        mStartRemote = System.currentTimeMillis();
        RobotLog.d(Tag, "getPicture success time = " + (mStartRemote - mStartTime));

        // no matter what size of pictures is three ; if size >0 then go to remoteRegister ;
        mPictureInfo.setName(mPersonName);
        mPictureInfo.setWelcomeContent(welcomeContent);
        mIsStartRemoteReq = true;
        if (mRemoteType == REMOTE_DETECT){
            mApi.remoteDetect(mReqId, mPictureInfo);
        }else {
            mApi.remoteRegister(mReqId, mPictureInfo);
        }
    }


    private boolean isGetPicFail() {
        if (mPictureInfo == null || mPictureInfo.getPictures().isEmpty()) {

            // 如果超时 800毫秒,则直接收集照片失败
            if (mIsGetPicFail){
                return true;
            }

            if (atomicInteger.get() < mRetry) {
                RobotLog.d(Tag,"mIsGetPicFail pic is null , atomicInteger.get() = "+atomicInteger.get()+", mIsSpeechOnce = "+ mIsSpeechOnce);
                if (!mIsSpeechOnce){
                    onStatusUpdate(Definition.STATUS_COLLECT_PICTURE, atomicInteger.get() + "");
                }
                if (!mIsSpeechOnce && atomicInteger.get() == 1){
                    startGetPicAgainTimer(true);
                }else {
                    cancelGetPicTimer();
                    getPictureByIdAgain();
                }
            } else {
                if (mStatus == State.STOPPED){
                    return true;
                }
                onResult(Definition.RESULT_FAILURE, Definition.COLLECT_PICTURE_FAILED);
                resetInitState();
                mStatus = State.REGISTERED;
                cancelRegisterTimer();
            }
            return true;
        }
        return false;
    }

    // 设置取照片超时时间　800ms
    private void startGetPicFailTimer() {
        mGetPicFailTimer = new Timer();
        mGetPicFailTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (mIsStartRemoteReq){
                    Log.d(Tag,"cancelGetPicFailTimer ");
                    cancelGetPicFailTimer();
                    return;
                }
                Log.d(Tag,"800ms GetPicFail timeout , RESULT_FAILURE");
                mIsGetPicFail = true;
                mApi.stopSendPersonInfos();
                onResult(Definition.RESULT_FAILURE, Definition.COLLECT_PICTURE_FAILED);
                resetInitState();
                mStatus = State.REGISTERED;
                cancelRegisterTimer();
            }
        },TIME_OUT_GAIN_PIC);
    }

    private void cancelGetPicFailTimer(){
        if(mGetPicFailTimer != null){
            mGetPicFailTimer.cancel();
            mGetPicFailTimer = null;
        }
    }

    private void startGetPicAgainTimer(boolean isNeedAdd) {
        mGetPicTimer = new Timer();
        long delay = mSecondDelay;
        if (isNeedAdd){
            delay = mSecondDelay ;
        }
        mGetPicTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                getPictureByIdAgain();
            }
        },delay);

    }



    private void handleRemoteRegister(String result) {
        RobotLog.d(Tag, "handle remote Register = " + result + ", time = " + ((System.currentTimeMillis() - mStartRemote)));
        if (mStatus == State.STOPPED){
            Log.d(Tag,"handleRemoteRegister RESULT_STOP ");
            return;
        }
        if (mStatus != State.REGISTERING) {
            return;
        }
        RegisterRemoteBean bean = MessageParser.parseRemoteResult(result);
        if (bean != null) {
            if (bean.getCode() == 0) { // 0  success ;  else  fail
                JSONObject json = new JSONObject();
                if ("exist".equals(bean.getFlag())) {                    // already register
                    try {
                        json.put(Definition.REGISTER_REMOTE_TYPE,Definition.REGISTER_REMOTE_SERVER_EXIST);
                        json.put(Definition.REGISTER_REMOTE_NAME,bean.getName());
                        onResult(Definition.RESULT_OK, json.toString());
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                } else {                                                // new register
                    try {
                        json.put(Definition.REGISTER_REMOTE_TYPE,Definition.REGISTER_REMOTE_SERVER_NEW);
                        json.put(Definition.REGISTER_REMOTE_NAME,bean.getName());
                        onResult(Definition.RESULT_OK, json.toString());
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            }else if (bean.getCode() == Definition.REMOTE_CODE_FACE_INVALID){ // face picture invalid in Server
                onResult(Definition.RESULT_FAILURE,Definition.REMOTE_FAIL_FACE_INVALID);
            }else {
                onResult(Definition.RESULT_FAILURE, "parseRemoteResult code = " + bean.getCode() + ", message = " + bean.getMessage());
            }
        } else {
            onResult(Definition.RESULT_FAILURE, Definition.REGISTER_REMOTE_PARSE_NULL);
        }

        resetEndState();
    }

    private void resetEndState() {
        resetInitState();
        mStatus = State.REGISTERED;
        cancelRegisterTimer();
        cancelGetPicTimer();
        deletePictures();
    }

    private void deletePictures(){
        if (mPictureInfo != null && !mPictureInfo.getPictures().isEmpty()) {
            removeLocalPictures(mPictureInfo.getPictures());
        }
    }

    /**
     * handle remote detect result
     * @param result
     */
    private void handleRemoteDetect(String result) {
        RobotLog.d(Tag, "handle remote Detect = " + result + ", time1 = " + ((System.currentTimeMillis() - mStartRemote)));
        if (mStatus == State.STOPPED){
            Log.d(Tag,"handleRemoteDetect RESULT_STOP ");
            return;
        }

        if (mStatus != State.REGISTERING) {
            return;
        }
        RegisterRemoteBean bean = MessageParser.parseRemoteResult(result);
        Log.d(Tag,"RegisterRemoteBean : "+bean);
        if (bean != null) {
            if (bean.getCode() == 0) { //  0  success ;  else  fail
                JSONObject json = new JSONObject();
                try {
                    json.put(Definition.REGISTER_REMOTE_TYPE,Definition.REGISTER_DETECT);
                    json.put(Definition.REGISTER_REMOTE_NAME,bean.getName());
                    json.put(Definition.REGISTER_REMOTE_DETECT_USER_ID,bean.getUserId());
                    json.put(Definition.REGISTER_REMOTE_DETECT_ROLE,bean.getIsStaff());
                    onResult(Definition.RESULT_OK, json.toString());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            } else if (bean.getCode() == Definition.REMOTE_CODE_FACE_INVALID){ // face picture invalid in Server
                onResult(Definition.RESULT_FAILURE,Definition.REMOTE_FAIL_FACE_INVALID);
            } else {
                onResult(Definition.RESULT_FAILURE, "parse detect code = " + bean.getCode() + ", message = " + bean.getMessage());
            }
        } else {
            onResult(Definition.RESULT_FAILURE, Definition.REGISTER_REMOTE_PARSE_NULL);
        }

        resetEndState();
    }


    /**
     * register gain pic for another mRetry-1 times .
     */
    private void getPictureByIdAgain() {
        RobotLog.e("getPictureByIdAgain , curRetry  = " + atomicInteger.get());
        if (mStatus != State.REGISTERING)
            return;

        if (atomicInteger.get() < mRetry) {
            int i = atomicInteger.incrementAndGet();
            mApi.getPictureById(mReqId, mPerson.getFaceInfo().getFaceId(), PICTURE_NUMBER);
            RobotLog.d(Tag, "registerAgain incrementGet  = " + i + ", get = " + atomicInteger.get());
        } else {
            atomicInteger.set(DEFAULT_RETRY);
        }
    }


    /**
     * if remoteRegister success ; remove the pictures in sdcard.
     * @param picPaths
     */
    private void removeLocalPictures(final List<String> picPaths) {
        if (picPaths.isEmpty())
            return;
        new Thread(new Runnable() {
            @Override
            public void run() {
                for (String path : picPaths) {
                    boolean b = Utils.removeFileFromSDCard(path);
                    RobotLog.d(Tag, "removeLocalPictures path = " + path+", isDeleted = "+b);
                }
            }
        }).start();

    }

    private void startRegisterTimer() {
        cancelRegisterTimer();
        mRegisterTimer = new Timer();
        mRegisterTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                RobotLog.d(Tag, "Register timeout mStatus = " + mStatus+", mIsStartRemoteReq : "+mIsStartRemoteReq);
                if (mIsStartRemoteReq){     // 临近超时时刻,此时正好发起了远程请求.需要等此次请求结束.否则前端和后端结果不一致.服务端有超时逻辑
                    mApi.stopSendPersonInfos();
                    return;
                }
                mApi.stopSendPersonInfos();
                if (mStatus == State.REGISTERING) {
                    resetInitState();
                }
                mStatus = State.REGISTERED;
                onResult(Definition.RESULT_FAILURE, Definition.REGISTER_TIMEOUT);
                deletePictures();
                cancelGetPicTimer();
//                if (mStatus != State.REGISTER) {
//                    return;
//                }
//                //stop();
            }
        }, mTimeout);
    }


    private void cancelRegisterTimer() {
        if (mRegisterTimer != null) {
            mRegisterTimer.cancel();
            mRegisterTimer = null;
        }
    }

    private void cancelGetPicTimer(){
        if (mGetPicTimer != null) {
            mGetPicTimer.cancel();
            mGetPicTimer = null;
        }
    }


    private void resetInitState(){
        mStatus = State.REGISTER;
        atomicInteger.set(DEFAULT_RETRY);
        mIsSpeechOnce = false;
        mIsGetPicFail = false;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        Log.d(Tag,"RegisterAction stopAction ============== ");
        mIsStartRemoteReq = false;
        mStatus = State.STOPPED;
        mRemoteType = 0;
        mApi.stopSendPersonInfos();
        cancelRegisterTimer();
        cancelGetPicFailTimer();
        cancelGetPicTimer();
        deletePictures();
        return true;
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return null;
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return new StopCommandList.IStopCommandChecker() {
            @Override
            public boolean executeSuccess(String command, String result) {
                RobotLog.d("RegisterNew stop check : " + command);
                switch (command) {
                    case Definition.CMD_NAVI_STOP_NAVIGATION:
                        return true;
                }
                return false;
            }
        };
    }
}
