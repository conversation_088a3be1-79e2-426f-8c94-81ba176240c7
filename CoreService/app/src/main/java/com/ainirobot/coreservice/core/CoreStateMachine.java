/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.core;

import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.actionbean.NaviCmdTimeOutBean;
import com.ainirobot.coreservice.core.CommandManager.CmdAction;
import com.ainirobot.coreservice.core.CommandManager.TimeoutListener;
import com.ainirobot.coreservice.core.LocalApi.OnCmdResponse;
import com.ainirobot.coreservice.core.RequestManager.ReqAction;
import com.ainirobot.coreservice.core.external.ExternalService;
import com.ainirobot.coreservice.service.CoreService;
import com.google.gson.Gson;

import java.util.List;

public class CoreStateMachine {
    private final static String TAG = "CoreStateMachine";

    //TODO: will extends state machine, remove below boolean valuable
    private boolean isRunningState;
    private CoreService mCoreService;

    public CoreStateMachine(CoreService coreService) {
        mCoreService = coreService;
        isRunningState = false;
    }

    //------------------ command -------------------//
    public int addCommand(int reqId, String cmdAction,
                          String cmdParam, boolean isContinues, OnCmdResponse callback) {
//        RobotLog.d("addCommand reqId is " + reqId + " cmdAction is " + cmdAction
//                + " cmdParam is " + cmdParam);
        //add command list into command queue, check if cmdAction is legally
        CmdAction action = newCmdAction(reqId, cmdAction, cmdParam, isContinues, callback);

        return action != null ? action.getId() : Definition.CMD_SEND_ERROR_UNSUPPORTED;
    }

    public int addCommand(int reqId, String cmdAction, String cmdParams,
                          String language, boolean isContinues, OnCmdResponse callback) {
        CmdAction action = newCmdAction(reqId, cmdAction, cmdParams, isContinues, callback);

        if (action == null) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }

        if (TextUtils.isEmpty(language)) {
            language = mCoreService.getRobotInfoManager().getSystemLanguage();
        }
        action.setLanguage(language);
        return action.getId();
    }

    private CmdAction newCmdAction(int reqId, String cmdAction,
                                   String cmdParam, boolean isContinues, OnCmdResponse callback) {
        CommandManager commandManager = mCoreService.getCommandManager();

        if (!commandManager.isCmdSupported(cmdAction)) {
            return null;
        }

        CmdAction cmd = commandManager.newCmdAction(reqId, cmdAction,
                cmdParam, isContinues, callback);
        commandManager.addCmdAction(cmd);
        return cmd;
    }

    public synchronized boolean cancelCommand(int cmdId, boolean isForceStop) {
        if (cmdId < 0) {
            return false;
        }

        CommandManager commandManager = mCoreService.getCommandManager();
        CmdAction cmd = commandManager.getCmdAction(cmdId);
        if (cmd != null) {
            //Log.d("CoreStateMachine", "Cancel pending command  : " + cmd.getType());
            commandManager.removeCmdAction(cmd);
            //return true;
        }

        cmd = commandManager.getWaitingCmdAction(cmdId);
        if (cmd != null) {
            //Log.d("CoreStateMachine", "Cancel waiting command  : " + cmd.getType());
//            String stopCommand = cmd.getStopCommand();
//            List<CmdAction> cmdActions = commandManager.getPendingStopList(stopCommand);
//            cmdActions.remove(cmd);
            commandManager.removeFromWaiting(cmd);
//            if (isForceStop && !TextUtils.isEmpty(stopCommand) && cmdActions.isEmpty()) {
//                Log.d("CoreStateMachine", "Stop : " + stopCommand);
//                addCommand(cmd.getReqId(), stopCommand, null, false, null);
//            }
        }
        return true;
    }

    public int tryToSendCommand() {
        //TODO: add state machine strategy
//        if (isRunningState) {
//            RobotLog.d("tryToSendCommand isRunningState " +
//                    mCoreService.getCommandManager().peekFirstAction().getType());
//            return Definition.CMD_SEND_WAITTING;
//        }
        return sendCommand();
    }

    private synchronized int sendCommand() {
        //RobotLog.d("send command");
        CommandManager commandManager = mCoreService.getCommandManager();
        CmdAction cmdAction = commandManager.peekFirstAction();
        if (cmdAction == null) {
            return Definition.CMD_SEND_SUCCESS;
        }
        //send command to IHWService
        ExternalService hwService =
                mCoreService.getExternalServiceManager().getExternalService(cmdAction.getServiceName());

        //处理该指令的Service被禁用
        if (hwService != null && !hwService.isEnable()) {
            commandManager.pollFirstAction();
            //handleCommandResponse(cmdAction, "HWService not registered");
            sendCmdResponse(cmdAction, "unsupported");
            Log.d(TAG, "Command not supported : " + cmdAction.getType());
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }

        //处理该指令的Service未启动
        if (hwService == null || !hwService.isConnected()) {
            commandManager.pollFirstAction();
            sendCmdResponse(cmdAction, "HWService not registered");
            Log.d(TAG, "HWService not connected : " + cmdAction.getType() + "   " + (hwService != null ? hwService.isConnected() : null));
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }

        try {
            if (cmdAction.isAsync()) {
                handleAsyncCommand(hwService, cmdAction);
            } else {
                handleSyncCommand(hwService, cmdAction);
            }
        } catch (Exception e) {
            e.printStackTrace();
            sendNextCommand();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
        return Definition.CMD_SEND_SUCCESS;
    }

    private boolean handleSyncCommand(ExternalService hwService,
                                      CmdAction cmd) throws RemoteException {
        //RobotLog.d("sendSyncCommand cmd : " + cmd.getType() + " params : " + cmd.getParams());
        String language = cmd.getLanguage();
        if (TextUtils.isEmpty(language)) {
            language = mCoreService.getRobotInfoManager().getSystemLanguage();
        }
        String result = hwService.exeSyncCommand(cmd.getType(), cmd.getParams(), language);
        handleCommandResponse(cmd, result, "");
        sendNextCommand();
        return true;
    }

    private boolean handleAsyncCommand(ExternalService hwService,
                                       final CmdAction cmd) throws RemoteException {
        Log.d(TAG, "Command send : " + cmd.getType() + " params : " + cmd.getParams());
        CommandManager commandManager = mCoreService.getCommandManager();
        cmd.setTimeoutListener(new TimeoutListener() {
            @Override
            public void onTimeout() {
                handleCommandTimeout(cmd);
            }
        });
        commandManager.joinWaiting(cmd);

        String language = cmd.getLanguage();
        if (TextUtils.isEmpty(language)) {
            language = mCoreService.getRobotInfoManager().getSystemLanguage();
        }
        boolean result = hwService.exeAsyncCommand(cmd.getType(), cmd.getParams(), language);
        sendNextCommand();
        return result;
    }

    public void handleCommandTimeout(CmdAction cmd) {
        CommandManager commandManager = mCoreService.getCommandManager();
        Log.d(TAG, "Command response : " + cmd.getType() + "  timeout");
        handleCommandResponse(cmd, "timeout", "");
        nviCmdTimeOutReport(cmd);
        if (!cmd.isContinues()) {
            commandManager.removeFromWaiting(cmd);
        }
    }

    /**
     * Send the command response to the module
     *
     * @param type   command type
     * @param result command execute result
     */
    public void handleAsyncCommandResponse(String type, String result, String extraData) {
        CommandManager commandManager = mCoreService.getCommandManager();
        List<CmdAction> actions = commandManager.getWaitingCmdList(type);
        if(!Definition.CMD_HEAD_GET_ALL_PERSON_INFOS.equals(type)){
            Log.d(TAG, "Command response : " + type + " ,result:" + result
                    + " ,extraData:" + extraData+ ", size : " + actions);
        }
        for (CmdAction cmdAction : actions) {
            handleCommandResponse(cmdAction, result, extraData);
            if (!cmdAction.isContinues()) {
                commandManager.removeFromWaiting(cmdAction);
            }
        }
    }

    public void handleAsyncCommandStatus(String type, String status, String extraData) {
        CommandManager commandManager = mCoreService.getCommandManager();
        List<CmdAction> actions = commandManager.getWaitingCmdList(type);
        Log.d(TAG, "Command status : " + type + "  " + status + "  size : " + actions);
        for (CmdAction cmdAction : actions) {
            handleCommandStatus(cmdAction, status, extraData);
        }
    }

    public void handleCommandStatus(CmdAction cmd, String status, String extraData) {
        if (cmd.getCallBack() != null) {
            cmd.getCallBack().onCmdStatusUpdate(cmd.getId(), cmd.getType(), status, extraData);
        }
    }

    public void handleCommandResponse(CmdAction cmd, String result, String extraData) {
        ReqAction req = mCoreService.getRequestManager().getReqAction(cmd.getReqId());
        if (req == null) {
//            RobotLog.d("Handle command response type : "
//                    + cmd.getType() + " reqId : " + cmd.getReqId());
            // return;
        }

        if (cmd.getCallBack() != null) {
            cmd.getCallBack().onCmdResponse(cmd.getId(), cmd.getType(), result, extraData);
        }
    }


    private void sendNextCommand() {
        CommandManager commandManager = mCoreService.getCommandManager();
        commandManager.pollFirstAction(); //Delete the sent command

        if (!commandManager.isEmpty()) {
            tryToSendCommand();
        }
    }

    private void sendCmdResponse(CmdAction cmdAction, String response) {
        CommandManager commandManager = mCoreService.getCommandManager();
        commandManager.joinWaiting(cmdAction);

        Handler handler = mCoreService.getResponseHandler();
        Message msg = handler.obtainMessage(InternalDef.MSG_CORE_HW_CMD_RESPONSE);
        Bundle bundle = new Bundle();
        bundle.putString(InternalDef.BUNDLE_TYPE, cmdAction.getType());
        bundle.putString(InternalDef.BUNDLE_RESPONSE, response);
        msg.setData(bundle);
        handler.sendMessage(msg);
    }

    //------------------ core status and HW status -------------------//
    //TODO: will change state to several integer definition
    public boolean getCurMachineState() {
        return isRunningState;
    }

    private void nviCmdTimeOutReport(CmdAction cmd) {
        if (!RobotOS.NAVIGATION_SERVICE.equals(cmd.getServiceName())) {
            return;
        }

        if (!Definition.CMD_NAVI_START_CREATING_MAP.equals(cmd.getType())) {
            return;
        }

        long timestamp = System.currentTimeMillis();
        String cacheId = RobotSettings.getSystemSn() + "-" + timestamp;
        NaviCmdTimeOutBean bean = new NaviCmdTimeOutBean(timestamp, cacheId, cmd.getReqId(),
                cmd.getType(), cmd.getParams());
        String type = Definition.CMD_NAVI_TIME_OUT_REPORT;
        String params = new Gson().toJson(bean);

        RequestManager requestManager = mCoreService.getRequestManager();
        ReqAction req = requestManager.addReqAction(type, params);
        req.reqFunction = InternalDef.INPUT_FUNCTION_DIRECT_CONNECTION;

        int cmdId = addCommand(req.reqId, type, params, false, null);
        Log.i(TAG, Definition.TAG_NAVI_LOG_REPORT + " type : " + type);
        Handler coreHandler = mCoreService.getCoreHandler();
        Message msg = coreHandler.obtainMessage(InternalDef.MSG_CORE_ADD_COMMAND_ACTION);
        coreHandler.sendMessage(msg);
    }

}
