package com.ainirobot.coreservice.core.status.queue;


import com.ainirobot.coreservice.core.status.biz.HWState;

import java.util.ArrayDeque;
import java.util.concurrent.locks.ReentrantLock;

public class StatusMessage {

    private static ArrayDeque<StatusMessage> mPool = new ArrayDeque<>();
    private static ReentrantLock lock = new ReentrantLock();

    private int cmdId;

    private HWState mHWState;

    private String statusType;

    private StatusMessage() {
    }

    public static StatusMessage obtain(HWState state, String msgId, String statusType) {
        StatusMessage msg;
        lock.lock();
        try {
            msg = mPool.poll();
        } finally {
            lock.unlock();
        }
        if (msg == null) {
            msg = new StatusMessage();
        }
        msg.mHWState = state;
        msg.statusType = statusType;
        return msg;
    }

    public void recycle() {
        mHWState = null;
        statusType = null;
        cmdId = -1;

        lock.lock();
        try {
            mPool.offer(this);
        } finally {
            lock.unlock();
        }
    }

    public HWState getHWState() {
        return mHWState;
    }

    public String getStatusType() {
        return statusType;
    }

    public void setCmdId(int cmdId) {
        this.cmdId = cmdId;
    }

    public int getCmdId(){
        return cmdId;
    }
}
