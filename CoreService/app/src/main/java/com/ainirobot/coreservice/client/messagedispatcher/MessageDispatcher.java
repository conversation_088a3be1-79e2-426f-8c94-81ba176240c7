/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.client.messagedispatcher;

import android.os.Looper;

import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.account.AccountListener;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.coreservice.client.listener.ToneListener;
import com.ainirobot.coreservice.client.permission.PermissionListener;
import com.ainirobot.coreservice.client.person.PersonListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingListener;
import com.ainirobot.coreservice.client.surfaceshare.SurfaceShareListener;

public class MessageDispatcher {
    static final int MSG_ACTION = 1;
    static final int MSG_STATUS = 2;
    static final int MSG_TEXT = 3;
    static final int MSG_TONE = 4;
    static final int MSG_PERSON = 5;
    static final int MSG_SETTING = 6;
    static final int MSG_ACCOUNT = 7;
    static final int MSG_PERMISSION = 8;
    static final int MSG_SURFACE_SHARE = 9;

    private ThreadLocal<DispatchHandler> mHandlers = new ThreadLocal<>();
    private DispatchHandler mMainThreadHandler = new DispatchHandler(Looper.getMainLooper());

    private DispatchHandler getDispatcher(Looper looper) {
        DispatchHandler dispatchHandler = mHandlers.get();
        if (dispatchHandler == null) {
            if (looper != null) {
                dispatchHandler = new DispatchHandler(looper);
                mHandlers.set(dispatchHandler);
            } else if (Looper.myLooper() == null) {
                dispatchHandler = mMainThreadHandler;
            } else if (Looper.myLooper() == Looper.getMainLooper()) {
                mHandlers.set(mMainThreadHandler);
                dispatchHandler = mMainThreadHandler;
            } else {
                dispatchHandler = new DispatchHandler(Looper.myLooper());
                mHandlers.set(dispatchHandler);
            }
        }
        return dispatchHandler;
    }

    public ActionDispatcher obtainActionDispatcher(ActionListener listener) {
        return obtainActionDispatcher(null, listener);
    }

    public StatusDispatcher obtainStatusDispatcher(StatusListener listener) {
        return obtainStatusDispatcher(null, listener);
    }

    public TextDispatcher obtainTextDispatcher(TextListener listener) {
        return obtainTextDispatcher(null, listener);
    }

    public ToneDispatcher obtainToneDispatcher(ToneListener listener) {
        return obtainToneDispatcher(null, listener);
    }

    public PersonDispatcher obtainPersonDispatcher(PersonListener listener) {
        return obtainPersonDispatcher(null, listener);
    }

    public PermissionDispatcher obtainPermissionDispatcher(PermissionListener listener) {
        return PermissionDispatcher.obtain(null, listener);
    }

    public RobotSettingDispatcher obtainRobotSettingDispatcher(RobotSettingListener listener) {
        return obtainRobotSettingDispatcher(null, listener);
    }

    public SurfaceShareDispatcher obtainSurfaceShareDispatcher(SurfaceShareListener listener) {
        return obtainSurfaceShareDispatcher(null, listener);
    }

    public ActionDispatcher obtainActionDispatcher(Looper looper, ActionListener listener) {
        return ActionDispatcher.obtain(getDispatcher(looper), listener);
    }

    public StatusDispatcher obtainStatusDispatcher(Looper looper, StatusListener listener) {
        return StatusDispatcher.obtain(getDispatcher(looper), listener);
    }

    public TextDispatcher obtainTextDispatcher(Looper looper, TextListener listener) {
        return TextDispatcher.obtain(getDispatcher(looper), listener);
    }

    public ToneDispatcher obtainToneDispatcher(Looper looper, ToneListener listener) {
        return ToneDispatcher.obtain(getDispatcher(looper), listener);
    }

    public PersonDispatcher obtainPersonDispatcher(Looper looper, PersonListener listener) {
        return PersonDispatcher.obtain(getDispatcher(looper), listener);
    }

    public PermissionDispatcher obtainPermissionDispatcher(Looper looper, PermissionListener listener) {
        return PermissionDispatcher.obtain(getDispatcher(looper), listener);
    }

    public AccountDispatcher obtainAccountDispatcher(Looper looper, AccountListener listener) {
        return AccountDispatcher.obtain(getDispatcher(looper), listener);
    }

    public RobotSettingDispatcher obtainRobotSettingDispatcher(Looper looper, RobotSettingListener listener) {
        return RobotSettingDispatcher.obtain(getDispatcher(looper), listener);
    }

    public SurfaceShareDispatcher obtainSurfaceShareDispatcher(Looper looper, SurfaceShareListener listener) {
        return SurfaceShareDispatcher.obtain(getDispatcher(looper), listener);
    }

    public void unregisterStatusDispatcher(String id) {
        StatusDispatcher.unregister(id);
    }
}
