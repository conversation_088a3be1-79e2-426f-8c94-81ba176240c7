package com.ainirobot.coreservice.action.download_map.state;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.download_map.DownloadMapStateEnum;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.DownloadMapBean;

import java.io.File;

public class UnzipMapFileState extends BaseDownloadMapPkgState {
    private DownloadMapBean downloadMapBean;

    @Override
    protected void onStart(String paramsStr, DownloadMapBean downloadMapBean) {
        this.downloadMapBean = downloadMapBean;
        sendCommand(0, Definition.CMD_NAVI_UNZIP_MAP_FILE, downloadMapBean.getMapName());
    }

    @Override
    public boolean onCmdResponse(int cmdId, String command, String result, String extraData) {
        if (TextUtils.equals(command, Definition.CMD_NAVI_UNZIP_MAP_FILE)) {
            if (TextUtils.equals(Definition.SUCCEED, result)) {
                onStateRunSuccess();
            } else {
                onStateRunFailed(0, "unzip is failed");
            }
            return true;
        }
        return false;
    }

    @Override
    protected DownloadMapStateEnum getNextState() {
        if (downloadMapBean.isOldMap() && !downloadMapBean.isOverwriteLocalPlace()) {
            return DownloadMapStateEnum.PARSE_ROAD_DATA;
        }
        return DownloadMapStateEnum.PARSE_PLACE_LIST;
    }
}
