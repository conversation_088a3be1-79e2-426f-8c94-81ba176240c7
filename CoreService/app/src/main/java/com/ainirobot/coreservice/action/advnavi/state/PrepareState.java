package com.ainirobot.coreservice.action.advnavi.state;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.advnavi.bean.ElevatorInfoBean;
import com.ainirobot.coreservice.action.advnavi.util.ElevatorLocationUtils;
import com.ainirobot.coreservice.action.advnavi.util.ElevatorStationPlan;
import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.bean.ElevatorRangeInfo;
import com.ainirobot.coreservice.bean.MultiFloorInfo;
import com.ainirobot.coreservice.bean.NaviPose;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.core.system.RobotSetting;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.coreservice.utils.ZipUtils;
import com.google.gson.reflect.TypeToken;

import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Data: 2022/8/8 19:05
 * Author: wanglijing
 * PrepareState
 * <p>
 * 准备阶段
 * 判断目的地信息、多层信息、与电梯的位置信息
 */
public class PrepareState extends BaseState {

    PrepareState(String name) {
        super(name);
    }
    private ElevatorLocationUtils locationUtils;

    @Override
    protected void preAction(StateData data, IStateListener listener) {
        super.preAction(data, listener);
        locationUtils = new ElevatorLocationUtils();
    }

    @Override
    protected void doAction() {
        super.doAction();
        //根据参数区分导航类型
        handleNaviTypeByParam();
    }

    /**
     * 根据参数区分导航类型
     */
    private void handleNaviTypeByParam() {
        //先判断是否电梯->闸机->多机
        if (mStateData.isElevatorStrategy()) {
            //判断乘梯功能是否开启
            checkElevatorFuncState();
        } else if (mStateData.isGateStrategy()) {
            gateNavi();
        } else {
            //不需要乘梯导航
            noNeedTakeElevator();
        }
    }

    @Override
    protected BaseState getNextState() {
        switch (actionState) {
            //需要坐电梯，下一状态：去电梯口
            case ACTION_NEED_ELEVATOR:
//                return StateEnum.GO_ELEVATOR_GATE.getState();
                return StateEnum.PREPARE_ELEVATOR.getState();

            //不需要坐电梯，下一状态：导航去目的地
            case ACTION_NO_NEED_ELEVATOR:
                return StateEnum.GO_DESTINATION.getState();

            //不需要坐电梯，但是需要多机导航
            case ACTION_MULTI_ROBOT:
                return StateEnum.PREPARE_MULTI_ROBOT.getState();

            //已经在电梯轿厢范围内，下一状态：进电梯
            case ACTION_ALREADY_INSIDE_ELEVATOR:
                return StateEnum.ENTER_ELEVATOR.getState();

            //需要过闸机，下一状态：闸机准备
            case ACTION_GATE_NAV:
                return StateEnum.PREPARE_GATE_NAV.getState();
            default:
                return null;
        }
    }


    @Override
    protected boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        if (!isAlive()) {
            Log.d(TAG, "current is not alive");
            return false;
        }
        if (Definition.CMD_NAVI_HAS_PLACE_IN_MAPNAME.equals(command)) {//判断目的地楼层的地图中，是否有导航点
            verifyTargetFloorPlace(result);
            return true;
        }
        return super.cmdResponse(cmdId, command, result, extraData);
    }

    /**
     * 判断乘梯功能是否开启
     */
    private void checkElevatorFuncState() {
        if (RobotSetting.isNavigationEnableElevator()) {
            //乘梯开启，获取多层配置
            Log.d(TAG, "elevator enable");
            checkMultiFloorInfo();
        } else {
            //乘梯未开启，判断目的地是否在当前楼层，直接导航去
            Log.d(TAG, "elevator unable");
            checkDestinationInCurrentFloor();
        }
    }

    /**
     * 获得多层配置信息
     */
    private void checkMultiFloorInfo() {
        if (getMultiFloorInfo()) {
            //多层配置正常，判断是否在电梯里
            Log.d(TAG, "get multi floor info suc");
            checkIsInElevator();
        }
    }

    /**
     * 判断是否在电梯轿厢范围内
     */
    private void checkIsInElevator() {
        String alreadyInElevator = isAlreadyInElevator();
        if (TextUtils.isEmpty(alreadyInElevator)) {
            //未找到电梯
            onResult(Definition.ERROR_NOT_FOUND_ELEVATOR_PATH);
            return;
        }
        Log.d(TAG, "checkIsInElevator: " + alreadyInElevator);
        Map<String, Object> map = mGson.fromJson(alreadyInElevator, Map.class);
        Double resultDouble = (Double) map.get(Definition.JSON_TASK_EXEC_RESULT);
        int result = resultDouble.intValue();
        String data = (String) map.get(Definition.JSON_TASK_EXEC_DATA);
        switch (result){
            case Definition.ERROR_NOT_FOUND_ELEVATOR_PATH:
                onResult(Definition.ERROR_NOT_FOUND_ELEVATOR_PATH);
                break;
            case Definition.ERROR_NOT_ESTIMATE:
                onResult(Definition.ERROR_NOT_ESTIMATE);
                break;
            case Definition.RESULT_ROBOT_IN_ELEVATOR:
                //执行在电梯内流程
                mStateData.setElevatorCenterPose(getElevatorCenterPose(data));
                Log.d(TAG, "already in elevator :" + mStateData.getElevatorName());
                insideElevator();
                break;
            case Definition.RESULT_ROBOT_NOT_IN_ELEVATOR:
                //正在加载点位也可能导致不 在电梯内
                checkPoseState();
                break;
        }
    }

    /**
     * 判断有没有正在加载点位
     */
    private void checkPoseState() {
        if (isLoadingPose()) {
            //点位是正在加载中，延迟1s再次判断是否在电梯轿厢范围内
            Log.d(TAG, "is loading pose ...");
            delayCheckElevatorState();
        } else {
            //在电梯外流程
            Log.d(TAG, "is out of elevator");
            outsideElevator();
        }
    }

    /**
     * 延迟1s，再次判断是否在电梯范围内
     */
    private void delayCheckElevatorState() {
        DelayTask.submit(TAG, new Runnable() {
            @Override
            public void run() {
                checkIsInElevator();
            }
        }, 1000);
    }

    /**
     * 执行在电梯外流程
     */
    private void outsideElevator() {
        //目的地楼层就是本楼层
        if (mStateData.isSameFloorIndex()) {
            //不需要乘梯，判断目的地是否存在，直接导航去
            checkDestinationInCurrentFloor();
        } else {
            Log.d(TAG, "need elevator");
            //判断目的地楼层是否有导航点
            checkTargetFloorPlace();
        }
    }

    /**
     * 获得目的地楼层地图是否有该点位
     */
    private void checkTargetFloorPlace() {
        String mapName = mStateData.getTargetFloorMapName();
        if (TextUtils.isEmpty(mapName)) {
            //目的地楼层多层配置信息错误
            onResult(Definition.ERROR_PARAMS_TARGET_FLOOR_INVALID,
                    String.valueOf(mStateData.getTargetFloorIndex()));
            return;
        }

        try {
            String destination = mStateData.getDestination();
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_MAP_NAME, mapName);
            param.put(Definition.JSON_PLACE_NAME, destination);
            sendCommand(Definition.CMD_NAVI_HAS_PLACE_IN_MAPNAME, param.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 目标楼层的地图是否有导航点
     *
     * @param result place list
     */
    private void verifyTargetFloorPlace(String result) {
        //不同楼层，需要坐电梯，找到要做的电梯
        if ("true".equals(result)) {
            //不同楼层，需要坐电梯，找到要做的电梯
            findTakeElevator();
        } else {
            //目标楼层没有该点
            Log.d(TAG, "not found destination in target floor");
            notFoundDestination();
        }
    }


    /**
     * 找到要坐的电梯
     */
    private void findTakeElevator() {
        List<MultiFloorInfo> multiFloorInfoList = getAllMultiFloorInfo();
        if (multiFloorInfoList == null || multiFloorInfoList.size() <= 0) {
            //获得当前地图的多层配置信息错误
            onResult(Definition.ERROR_GET_CURRENT_MAP_GROUP_LIST_FAILED);
            return;
        }

        ElevatorStationPlan.getInstance().getCurrentMapElevator(
                mStateData.getCurrentFloorIndex(),
                mStateData.getTargetFloorIndex(),
                multiFloorInfoList,
                new ElevatorStationPlan.ElevatorStationListener() {
                    @Override
                    public void updateStation(ElevatorInfoBean elevator) {

                    }

                    @Override
                    public void processResult(int result, String error) {
                        //没找到电梯
                        onResult(result, error);
                    }

                    @Override
                    public void onFinish(String elevatorName) {
                        Log.d(TAG, "find elevator :" + elevatorName);
                        mStateData.setElevatorCenterPose(getElevatorCenterPose(elevatorName));//路径规划，找到要乘坐的电梯名称
                        needTakeElevator();
                    }
                }
        );
    }


    /**
     * 在当前地图找是否有目的地点
     */
    private void checkDestinationInCurrentFloor() {
        if (currentFloorHasDestination()) {
            //当前楼层有目的地，直接导航去
            Log.d(TAG, "no need elevator");
            noNeedTakeElevator();
        } else {
            //当前楼层没有该点
            Log.d(TAG, "not found destination in current floor");
            notFoundDestination();
        }
    }

    /**
     * 不需要坐电梯
     * 判断是否需要多机导航
     */
    private void noNeedTakeElevator() {
        if(isMultiRobotStrategy()){
            Log.i(TAG, "need multi robot navi ");
            multiRobotNavi();
        }else {
            Log.i(TAG, "no need multi robot navi");
            normalNavi();
        }
    }

    /**
     * 是否是多机导航策略
     * NULL ： 不用多机导航
     * @return true: 是多机导航策略
     */
    private boolean isMultiRobotStrategy() {
        Definition.AdvNaviStrategy advNaviStrategy = mStateData.getNavigationBean().getNaviStrategy();
        Log.i(TAG, "isMultiRobotStrategy: " + advNaviStrategy);
        return advNaviStrategy != Definition.AdvNaviStrategy.DEFAULT
                && advNaviStrategy != Definition.AdvNaviStrategy.ELEVATOR;
    }


    /**
     * 判断当前是否已经在电梯轿厢范围内
     *
     * @return true：在电梯里
     */
//    private boolean isAlreadyInElevator() {
//        //获得所有电梯名
//        List<String> elevatorList = getAllElevator();
//        if (null == elevatorList || elevatorList.size() <= 0) {
//            onResult(Definition.ERROR_NOT_FOUND_ELEVATOR_PATH); //未找到电梯
//            return false;
//        }
//
//        //遍历在哪个电梯内
//        for (String name : elevatorList) {
//            if (isInElevatorRange(name)) {
//                mStateData.setElevatorCenterPose(getElevatorCenterPose(name));//在电梯内，更新电梯中心点位
//                return true;
//            }
//        }
//        return false;
//    }

    /**
     * 判断是否在电梯范围内
     *
     * @param elevatorName 电梯名
     * @return true:在该电梯轿厢范围内
     */
    private boolean isInElevatorRange(String elevatorName) {
        //电梯轿厢范围
        ElevatorRangeInfo rangeInfo = getElevatorRange(elevatorName);
        if (null == rangeInfo || null == locationUtils) {
            onResult(Definition.ERROR_NOT_FOUND_ELEVATOR_PATH, "elevator range is null");
            return false;
        }

        ElevatorLocationUtils.LocationState state = locationUtils.relativeElevatorPosition(
                true,  //避免机器人没完全进来，设为进梯状态下的轿厢范围
                getCurrentPose(),
                getElevatorCenterPose(elevatorName),//判断是否在电梯中心点位，需要用到电梯中心点位Pose
                rangeInfo);

        switch (state) {
            case NOT_ESTIMATE:
            case TIMEOUT:
                if (!isEstimate()) {
                    onResult(Definition.ERROR_NOT_ESTIMATE, "not estimate");
                }
                return false;
            case INSIDE_ELEVATOR:
                return true;
            case OUTSIDE_ELEVATOR:
                return false;
        }
        return false;
    }

    private boolean isEstimate() {
        return TextUtils.equals(getRobotInfo(Definition.SYNC_ACTION_IS_ROBOT_ESTIMATE), Definition.RESULT_TRUE);
    }

    private boolean isLoadingPose() {
        return TextUtils.equals(getRobotInfo(Definition.SYNC_ACTION_IS_LOADING_POSE), Definition.RESULT_TRUE);
    }

    //当前点位信息
    private Pose getCurrentPose() {
        try {
            String poseInfo = getRobotInfo(Definition.SYNC_ACTION_GET_CURRENT_LOCATION);
            return mGson.fromJson(poseInfo, Pose.class);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private Pose getElevatorCenterPose(String elevatorName) {
//        return getPoseByName(elevatorName +  "-" + Definition.ELEVATOR_CENTER_POSE);

        //TODO 改为按类型获取电梯中心
        Pose pose = getPoseByTypeId(Definition.ELEVATOR_CENTER_TYPE);
        Log.d(TAG, "getElevatorCenterPose: " + pose);
        return pose;
    }

    /**
     * 找到当前楼层的地图中所有可用的电梯名称
     *
     * @return 可用的电梯名称
     */
    private List<String> getAllElevator() {
        String currentFloorInfo = getRobotInfo(Definition.SYNC_ACTION_GET_CURRENT_MULTI_FLOOR_INFO);
        MultiFloorInfo currentFloorMultiInfo = stringToMultiFloorInfo(currentFloorInfo);
        return null == currentFloorMultiInfo
                ? null
                : currentFloorMultiInfo.getAvailableElevators();

    }

    /**
     * 获得当前楼层 和 目标楼层 的多层配置信息
     *
     * @return false:某一层配置信息错误
     */
    private boolean getMultiFloorInfo() {
        //找到当前地图的楼层映射
        String currentInfo = getRobotInfo(Definition.SYNC_ACTION_GET_CURRENT_MULTI_FLOOR_INFO);
        MultiFloorInfo currentMultiFloorInfo = stringToMultiFloorInfo(currentInfo);
        if (null == currentMultiFloorInfo) {
            //当前楼层楼层多层配置信息错误
            onResult(Definition.ERROR_GET_CURRENT_MAP_GROUP_LIST_FAILED,
                    getRobotInfo(Definition.SYNC_ACTION_GET_MAP_NAME));
            return false;
        }

        //根据楼层映射，获得目的地楼层的多层配置信息
        int targetFloorIndex = mStateData.getTargetFloorIndex();
        String targetInfo = getRobotInfo(Definition.SYNC_ACTION_MULTI_FLOOR_INFO_BY_INDEX,
                String.valueOf(targetFloorIndex));
        MultiFloorInfo targetMultiFloorInfo = stringToMultiFloorInfo(targetInfo);
        if (null == targetMultiFloorInfo) {
            //目的地楼层多层配置信息错误
            onResult(Definition.ERROR_PARAMS_TARGET_FLOOR_INVALID, String.valueOf(targetFloorIndex));
            return false;
        }

        //当前楼层对应的映射未知，在这里设置上
        mStateData.setCurrentFloorIndex(currentMultiFloorInfo.getFloorIndex());
        mStateData.setCurrentFloorMultiFloorInfo(currentMultiFloorInfo);
        mStateData.setTargetFloorMultiFloorInfo(targetMultiFloorInfo);

        return true;
    }

    /**
     * 目的地楼层的地图是否有导航点
     *
     * @param result place list
     * @return true:地图中有导航点
     */
    private boolean isTargetFloorHasDestination(String result) {
        try {
            String destination = mStateData.getDestination();
            Type type = new TypeToken<List<NaviPose>>() {
            }.getType();
            result = ZipUtils.unzipMapData(mGson, result);
            List<NaviPose> poseList = mGson.fromJson(result, type);
            for (NaviPose pose : poseList) {
                if (TextUtils.equals(pose.getName(), destination)) {
                    return true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private List<MultiFloorInfo> getAllMultiFloorInfo() {
        List<MultiFloorInfo> multiFloorInfoList = new ArrayList<>();
        try {
            String infos = getRobotInfo(Definition.SYNC_ACTION_GET_MULTI_FLOOR_INFOS);
            Type type = new TypeToken<ArrayList<MultiFloorInfo>>() {
            }.getType();
            multiFloorInfoList = mGson.fromJson(infos, type);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return multiFloorInfoList;
    }

    private ElevatorRangeInfo getElevatorRange(String elevatorName) {
        String rangeInfo = getRobotInfo(Definition.SYNC_ACTION_GET_ELEVATOR_RANGE, elevatorName);
        try {
            return mGson.fromJson(rangeInfo, ElevatorRangeInfo.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 当前楼层是否有目的地
     *
     * @return true:有
     */
    private boolean currentFloorHasDestination() {
        String destination = mStateData.getDestination();
        return null != getPoseByName(destination);
    }

    private MultiFloorInfo stringToMultiFloorInfo(String info) {
        try {
            return mGson.fromJson(info, MultiFloorInfo.class);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 没找到目的地，上报错误
     */
    private void notFoundDestination() {
        onResult(Definition.ERROR_DESTINATION_NOT_EXIST, mStateData.getDestination());
    }

    /**
     * 已经在电梯内
     * 更新在电梯内事件
     */
    private void insideElevator() {
        updateAction(Definition.ElevatorAction.ACTION_ALREADY_INSIDE_ELEVATOR);
        onSuccess();
    }

    /**
     * 需要坐电梯
     * 更需要坐电梯事件
     */
    private void needTakeElevator() {
        updateAction(Definition.ElevatorAction.ACTION_NEED_ELEVATOR);
        onSuccess();
    }

    /**
     * 平层导航，没有多机避障
     */
    private void normalNavi() {
        updateAction(Definition.ElevatorAction.ACTION_NO_NEED_ELEVATOR);
        onSuccess();
    }

    /**
     * 平层多机导航
     */
    private void multiRobotNavi() {
        updateAction(Definition.ElevatorAction.ACTION_MULTI_ROBOT);
        onSuccess();
    }
    /**
     * 闸机导航
     */
    private void gateNavi() {
        updateAction(Definition.ElevatorAction.ACTION_GATE_NAV);
        onSuccess();
    }

}
