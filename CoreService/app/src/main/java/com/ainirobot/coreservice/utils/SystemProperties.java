package com.ainirobot.coreservice.utils;

import android.content.Context;

import java.lang.reflect.Method;

public class SystemProperties {

    private static final String PROPERTIES_FILE_NAME = "android.os.SystemProperties";

    public static String get(Context context, String key) throws IllegalArgumentException {
        String ret;
        try {
            ClassLoader cl = context.getClassLoader();
            Class properties = cl.loadClass(PROPERTIES_FILE_NAME); //参数类型
            Class[] paramTypes = new Class[1];
            paramTypes[0] = String.class;
            Method get = properties.getMethod("get", paramTypes); //参数
            Object[] params = new Object[1];
            params[0] = new String(key);
            ret = (String) get.invoke(properties, params);
        } catch (Exception e) {
            ret = "";
        }
        return ret;
    }

    public static String get(Context context, String key, String def) throws
            IllegalArgumentException {
        String ret = def;
        try {
            ClassLoader cl = context.getClassLoader();
            Class properties = cl.loadClass(PROPERTIES_FILE_NAME);
            Class[] paramTypes = new Class[2];
            paramTypes[0] = String.class;
            paramTypes[1] = String.class;
            Method get = properties.getMethod("get", paramTypes);
            Object[] params = new Object[2];
            params[0] = new String(key);
            params[1] = new String(def);
            ret = (String) get.invoke(properties, params);
        } catch (Exception e) {
            ret = def;
        }
        return ret;
    }

    public static Integer getInt(Context context, String key, int def) throws
            IllegalArgumentException {
        Integer ret = def;
        try {
            ClassLoader cl = context.getClassLoader();
            Class properties = cl.loadClass(PROPERTIES_FILE_NAME);
            Class[] paramTypes = new Class[2];
            paramTypes[0] = String.class;
            paramTypes[1] = int.class;
            Method getInt = properties.getMethod("getInt", paramTypes);
            Object[] params = new Object[2];
            params[0] = new String(key);
            params[1] = new Integer(def);
            ret = (Integer) getInt.invoke(properties, params);
        } catch (Exception e) {
            ret = def;
        }
        return ret;
    }

    public static void setProperty(Context context, String key, String value) {
        try {
            ClassLoader cl = context.getClassLoader();
            Class properties = cl.loadClass(PROPERTIES_FILE_NAME);
            Class[] paramTypes = new Class[2];
            paramTypes[0] = String.class;
            paramTypes[1] = String.class;
            Method set = properties.getMethod("set", paramTypes);
            Object[] params = new Object[2];
            params[0] = new String(key);
            params[1] = new String(value);
            set.invoke(properties, key, value);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}