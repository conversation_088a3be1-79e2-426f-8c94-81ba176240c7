package com.ainirobot.coreservice.data.account;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.account.TokenBean;
import com.ainirobot.coreservice.data.account.bean.Tables;
import com.ainirobot.coreservice.data.provider.BaseDataHelper;

import static com.ainirobot.coreservice.data.account.bean.Tables.AUTHORITY_URI;
import static com.ainirobot.coreservice.data.account.bean.Tables.TOKEN;

public class TokenDataHelper extends BaseDataHelper {

    private static final String TAG = TokenDataHelper.class.getSimpleName();
    private static final Uri CONTENT_URI = Uri.withAppendedPath(AUTHORITY_URI, TOKEN);

    TokenDataHelper(Context context) {
        super(context);
    }

    @Override
    public Uri getContentUri() {
        return CONTENT_URI;
    }

//    boolean isTokenExist(String tokenId) {
//        Log.d(TAG, "isTokenExist tokenId: " + tokenId);
//        Cursor cursor = query(Tables.TokenColumns.TOKEN_ID + "=?",
//                new String[]{
//                        tokenId
//                });
//        if (cursor != null && cursor.moveToNext()) {
//            cursor.close();
//            return true;
//        }
//        return false;
//    }

    TokenBean getToken(String tokenId) {
        Log.d(TAG, "getToken tokenId: " + tokenId);
        TokenBean bean = null;
        Cursor cursor = query(Tables.TokenColumns.TOKEN_ID + "=?",
                new String[]{
                        tokenId
                });
        if (cursor != null && cursor.moveToNext()) {
            bean = cursorToTokenBean(cursor);
            cursor.close();
        }
        return bean;
    }

    void updateToken(TokenBean bean) {
        if (!TextUtils.isEmpty(bean.getValue())) {
            Cursor cursor = query(Tables.TokenColumns.TOKEN_ID + "=?", new String[]{
                    bean.getTokenId()
            });
            ContentValues contentValues = bindTokenBeanToContentValue(bean);
            if (cursor != null && cursor.moveToNext()) {
                update(contentValues, Tables.TokenColumns.TOKEN_ID + "=?", new String[]{
                        bean.getTokenId()
                });
            } else {
                insert(contentValues);
            }
            if (cursor != null) {
                cursor.close();
            }
        } else {
            delete(Tables.TokenColumns.TOKEN_ID + "=?", new String[]{
                    bean.getTokenId()
            });
        }
    }

    void deleteAllToken() {
        delete(null, null);
    }

    private ContentValues bindTokenBeanToContentValue(TokenBean bean) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(Tables.TokenColumns.TOKEN_ID, bean.getTokenId());
        contentValues.put(Tables.TokenColumns.VALUE, bean.getValue());
        contentValues.put(Tables.TokenColumns.EXPIRES_IN, bean.getExpiresIn());
        contentValues.put(Tables.TokenColumns.CREATE_TIME, bean.getCreateTime());
        return contentValues;
    }

    private TokenBean cursorToTokenBean(Cursor cursor) {
        TokenBean bean = new TokenBean();
        bean.setTokenId(cursor.getString(cursor.getColumnIndex(Tables.TokenColumns.TOKEN_ID)));
        bean.setValue(cursor.getString(cursor.getColumnIndex(Tables.TokenColumns.VALUE)));
        bean.setExpiresIn(cursor.getInt(cursor.getColumnIndex(Tables.TokenColumns.EXPIRES_IN)));
        bean.setCreateTime(cursor.getInt(cursor.getColumnIndex(Tables.TokenColumns.CREATE_TIME)));
        return bean;
    }
}
