package com.ainirobot.coreservice.action.advnavi.state;

import static com.ainirobot.coreservice.client.actionbean.NavigationAdvancedBean.TYPE_POSE;

import android.util.Log;

import com.ainirobot.coreservice.action.StopCommandList;
import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.action.advnavi.gatecmd.GateControlUtil;
import com.ainirobot.coreservice.action.advnavi.gatecmd.GateControlUtilListener;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.MultiRobotStatus;
import com.ainirobot.coreservice.client.actionbean.NavigationAdvancedBean;
import com.ainirobot.coreservice.client.actionbean.Pose;

import java.util.ArrayList;
import java.util.List;

/**
 * Date: 2024/2/22
 * Author: dengting
 * Description: GoGateSecondState
 * 导航第二个闸机点位
 */
public class GoGateSecondState extends BaseNavigationState {

    //导航超时失败重试次数
    private int naviRetryCount = 0;
    //导航重试最多次数
    private final int MAX_NAVI_RETRY_COUNT = 5;
    private GateControlUtil controlUtil;
    private Pose secondPose;
    private BaseState cachedNextState = null;

    GoGateSecondState(String name) {
        super(name);
    }

    @Override
    protected boolean isEnableMultiRobotStatus() {
        return false;
    }

    @Override
    protected void preAction(StateData data, IStateListener listener) {
        super.preAction(data, listener);
        naviRetryCount = 0;
        if (mStateData.isMultiGateMode()) {
            secondPose = mStateData.getCurrentGateSecondPose();
        } else {
            secondPose = mStateData.getGateSecondPose();
        }
        controlUtil = new GateControlUtil(this,
                elevatorControlUtilListener);
    }

    @Override
    protected void doAction() {
        super.doAction();
        onStatusUpdate(Definition.STATUS_START_GO_SECOND_GATE, "start to second gate");
        continueOpenDoor();
        naviToGate();   //do action
    }

    /**
     * 保持开门
     */
    private void continueOpenDoor() {
        if (null != controlUtil) {
            controlUtil.startOpenDoor();
        }
    }

    @Override
    protected void exit() {
        super.exit();
        // 重置缓存的状态，确保下次进入状态时重新计算
        cachedNextState = null;
        releaseControlGate();
    }

    @Override
    protected BaseState getNextState() {
        // 如果已经计算过下一个状态，直接返回缓存的结果
        if (cachedNextState != null) {
            Log.d(TAG, "getNextState: returning cached state " + cachedNextState.getStateName());
            return cachedNextState;
        }

        Log.d(TAG, "getNextState START: currentGateIndex=" + mStateData.getCurrentGateIndex() +
                ", isLastGate=" + mStateData.isLastGate() +
                ", totalGates=" + mStateData.getGatePairPoses().size() +
                ", actionState=" + actionState);
        switch (actionState) {
            case ACTION_BACK_GATE:
            case ACTION_NAVI_SUC:
                // 检查当前索引，而不是调用 isLastGate()
                int currentIndex = mStateData.getCurrentGateIndex();
                int totalGates = mStateData.getGatePairPoses().size();

                if (currentIndex < totalGates - 1) {
                    // 只有在确定要转换状态时才移动到下一个闸机
                    boolean moveSuccess = mStateData.moveToNextGate();
                    if (moveSuccess) {
                        Log.d(TAG, "Moving to next gate: " + (currentIndex + 2) +
                                " of " + totalGates);
                        cachedNextState = StateEnum.GO_GATE_FIRST.getState();
                        return cachedNextState;
                    } else {
                        Log.i(TAG, "Failed to move to next gate or no more gates");
                        cachedNextState = StateEnum.GO_DESTINATION.getState();
                        return cachedNextState;
                    }
                } else {
                    Log.d(TAG, "This is the last gate, going to destination");
                    cachedNextState = StateEnum.GO_DESTINATION.getState();
                    return cachedNextState;
                }
        }
        cachedNextState = super.getNextState();
        return cachedNextState;
    }

    @Override
    protected boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        if (null != controlUtil
                && controlUtil.cmdResponse(command, result, extraData)) {
            return true;

        }
        return super.cmdResponse(cmdId, command, result, extraData);
    }


    @Override
    protected boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        if (null != controlUtil
                && controlUtil.cmdResponse(command, status, extraData)) {
            return true;
        }
        return super.cmdStatusUpdate(cmdId, command, status, extraData);
    }

    @Override
    protected void onNavigationStatusUpdate(int result, String message, String extraData) {
        onStatusUpdate(result, message, extraData);
    }

    @Override
    protected void onNavigationError(int result, String message, String extraData) {
        switch (result) {
            case Definition.ERROR_DESTINATION_CAN_NOT_ARRAIVE:
            case Definition.ERROR_MULTI_ROBOT_WAITING_TIMEOUT:
            case Definition.ERROR_NAVIGATION_AVOID_TIMEOUT:
                naviRetry();    //重试
                return;
        }
        onResult(result, message, extraData);
    }

    @Override
    protected void onNavigationResult(int result, String message, String extraData) {
        switch (result) {
            case Definition.ERROR_IN_DESTINATION:
            case Definition.RESULT_OK:
                //有可能机器人朝向不对，尝试归正方向一次
                releaseControlGate();
                naviSuccess();
                break;
        }
    }

    @Override
    protected void handleMultipleRobotStatus(List<MultiRobotStatus> robotStatusList) {

    }

    @Override
    protected List<StopCommandList.StopCommand> getStopCommands() {
        return new ArrayList<StopCommandList.StopCommand>() {{
            add(new StopCommandList.StopCommand(Definition.CMD_CONTROL_CLOSE_GATE_DOOR, null));
            add(new StopCommandList.StopCommand(Definition.CMD_CONTROL_RELEASE_GATE, null));
        }};
    }

    /**
     * 控制闸机开门时状态监听
     */
    private final GateControlUtilListener elevatorControlUtilListener = new GateControlUtilListener() {
        @Override
        public void onGateStatusUpdate(int id, String param, String extraData) {

        }

        @Override
        public void onGateResult(int id, String param) {
            onGatecmdResult(id, param);
        }

        @Override
        public void onSuccess() {

        }
    };

    private void onGatecmdResult(int id, String param) {
        Log.d(TAG, "onGatecmdResult status:" + id + " , data:" + param);
        switch (id) {
            case Definition.ERROR_GATE_CONTROL:
            case Definition.ERROR_CALL_GATE_FAILED:
            case Definition.ERROR_CLOSE_GATE_DOOR_FAILED:
            case Definition.ERROR_OPEN_GATE_DOOR_FAILED:
                stopNavigation();   //停止导航
                openDoorFail();
                break;

            default:
                onResult(id, param);
                break;
        }
    }

    private void openDoorFail() {
        Log.d(TAG, "openDoorFail");
        naviToGate();   //back gate suc
    }

    private void naviToGate() {
        Log.d(TAG, "naviToElevatorGate");
        NavigationAdvancedBean bean = cloneNaviBean();
        bean.setNaviType(TYPE_POSE);
        bean.setNaviPose(secondPose);
        bean.setAdjustAngle(true);
        startNavigation(bean);
    }

    private void naviRetry() {
        //超过最大重试次数
        naviRetryCount++;
        if (naviRetryCount > MAX_NAVI_RETRY_COUNT) {
            // TODO: 如果是多闸机，可以略过当前闸机执行下一个
            naviFail();
            return;
        }
        //重试
        naviToGate();   //retry
    }

    private void naviFail() {
        onResult(Definition.ERROR_NAVIGATION_FAILED);
    }

    private void naviSuccess() {
        Log.d(TAG, " navi success ");
        //否则就是正常的导航成功
        updateAction(Definition.ElevatorAction.ACTION_NAVI_SUC);
        if (mStateData.isMultiGateMode()) {
            int currentIndex = mStateData.getCurrentGateIndex();
            int totalCount = mStateData.getGatePairPoses().size();
            String gateId = mStateData.getCurrentGateId();
            onStatusUpdate(Definition.STATUS_ARRIVED_GATE_SECOND_POSE,
                    "Arrived at first point of gate " + (currentIndex + 1) +
                            " of " + totalCount + ", ID: " + gateId);
        } else {
            onStatusUpdate(Definition.STATUS_ARRIVED_GATE_SECOND_POSE, "Arrived at first gate");
        }
        onSuccess();
    }

    private void releaseControlGate() {
        if (null != controlUtil) {
            controlUtil.cmdCloseGateDoor();
            controlUtil.releaseGateControl();
        }
    }
}
