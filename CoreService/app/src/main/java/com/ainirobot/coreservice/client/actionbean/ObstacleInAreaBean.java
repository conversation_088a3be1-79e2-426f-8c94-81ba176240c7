package com.ainirobot.coreservice.client.actionbean;

public class ObstacleInAreaBean extends BaseBean {
    double startAngle;
    double endAngle;
    double minDistance;
    double maxDistance;

    public ObstacleInAreaBean(double startAngle, double endAngle, double minDistance, double maxDistance) {
        this.startAngle = startAngle;
        this.endAngle = endAngle;
        this.minDistance = minDistance;
        this.maxDistance = maxDistance;
    }

    public double getStartAngle() {
        return startAngle;
    }

    public void setStartAngle(double startAngle) {
        this.startAngle = startAngle;
    }

    public double getEndAngle() {
        return endAngle;
    }

    public void setEndAngle(double endAngle) {
        this.endAngle = endAngle;
    }

    public double getMinDistance() {
        return minDistance;
    }

    public void setMinDistance(double minDistance) {
        this.minDistance = minDistance;
    }

    public double getMaxDistance() {
        return maxDistance;
    }

    public void setMaxDistance(double maxDistance) {
        this.maxDistance = maxDistance;
    }
}
