package com.ainirobot.coreservice.daemon;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import android.util.Log;

import com.ainirobot.base.OrionBase;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.utils.SettingDataHelper;

/**
 * 性能监控
 */
public class PerformanceService extends Service {

    private static final String TAG = PerformanceService.class.getSimpleName();

    @Override
    public void onCreate() {
        super.onCreate();
        SettingDataHelper.getInstance().setContext(this);
        String zone = SettingDataHelper.getInstance().getCloudServerZone();
        Log.d(TAG,"initOrionBase zone: " + zone);
        OrionBase.setDomainEnv(ProductInfo.isOverSea(), zone, RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_SYSTEM_ENV) == 1);
        OrionBase.startProcess(this);
    }

    @Override
    public IBinder onBind(Intent intent) {
        throw new UnsupportedOperationException("Not yet implemented");
    }
}
