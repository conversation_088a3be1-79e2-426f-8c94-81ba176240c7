package com.ainirobot.coreservice.core.system;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.bi.report.TimeWarningReport;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.exception.InvalidArgumentException;
import com.ainirobot.coreservice.client.exception.NoSuchKeyException;
import com.ainirobot.coreservice.core.InternalDef;
import com.ainirobot.coreservice.core.apiproxy.BaseApiProxy;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Calendar;
import java.util.Date;

public class RemoteBindManager {
    private final String TAG = "RemoteBindManager";

    private Context mContext;
    private SystemManager mSystemManager;

    public RemoteBindManager(Context context, SystemManager manager) {
        mContext = context;
        mSystemManager = manager;
        registerReceiver();
    }

    private void registerReceiver() {
        IntentFilter intentFilter = new IntentFilter(InternalDef.ACTION_TIMING_TASK);
        TimerReceiver receiver = new TimerReceiver();
        mContext.registerReceiver(receiver, intentFilter);

    }

    private class TimerReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            checkRemoteBind();
            checkNtpTime();
        }
    }

    private void checkRemoteBind() {
        Calendar calendar = Calendar.getInstance();
        Log.d(TAG, "Alarm: " + calendar.get(Calendar.HOUR_OF_DAY));
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        if (0 == hour || 12 == hour) {
            mSystemManager.getClient().remoteBindStatus(Definition.DEBUG_REQ_ID, new BaseApiProxy.ApiListener() {
                @Override
                public void onResult(int status, String responseString) {
                    Log.d(TAG, "remote bind:" + responseString);
                    if (TextUtils.isEmpty(responseString)
                            || "timeout".equals(responseString)
                            || "HWService not registered".equals(responseString)) {
                        Log.d(TAG, "handleBindStatus, error");
                    } else {
                        try {
                            JSONObject json = new JSONObject(responseString);
                            String ret = json.optString("ret");
                            if (ret.equals("0")) { //request success
                                JSONObject data = json.optJSONObject("data");
                                String bindRet = data.optString("bind_ret");//bind status
                                if (!TextUtils.isEmpty(bindRet) &&
                                        Integer.parseInt(bindRet) == Definition.BIND_FAILURE) {
                                    mSystemManager.updateStatus(SystemStatus.REMOTE_BIND);
                                }
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }
                }
            });
        }
    }

    private void checkNtpTime() {
        if (!ProductInfo.isDeliveryProduct() && !ProductInfo.isMeissaPlus() && !ProductInfo.isMeissa2()) {
            return;
        }

        //只针对租赁的机器执行"租期漏洞"的保护逻辑
        try {
            String expireTime = RobotSettings.getRobotString(mContext, Definition.ROBOT_EXPIRES_TIME);
            Log.d(TAG,"checkNtpTime expireTime: " + expireTime);
            if("0".equals(expireTime)){
                return;
            }
        } catch (InvalidArgumentException e) {
            e.printStackTrace();
        } catch (NoSuchKeyException e) {
            e.printStackTrace();
        }

        // finalData: 2023/01/01
        Date finalData = new Date(1672502400000L);
        final Date date = new Date();
        Log.d(TAG, "checkNtpTime current: " + date.getTime() + ", final: " + finalData.getTime());
        if (date.before(finalData)) {
            SystemStatus.TIME_WARNING.update(true);
            mSystemManager.updateStatus(SystemStatus.TIME_WARNING, false);
            TimeWarningReport report = new TimeWarningReport();
            report.addAction(TimeWarningReport.ACTION_TRIGGER)
                    .addType(TimeWarningReport.TYPE_SYNC_TIME_FAILED).report();
            return;
        }
        try {
            long expireTime = Long.parseLong(RobotSettings.getRobotString(mContext,
                    Definition.ROBOT_EXPIRES_TIME)) * 1000;
            Log.d(TAG, "checkNtpTime expire: " + expireTime);
            if (expireTime > 0) {
                Date expireData = new Date(expireTime);
                if (date.after(expireData)) {
                    mSystemManager.getClient().refreshRobotProfile(Definition.DEBUG_REQ_ID, new BaseApiProxy.ApiListener() {
                        @Override
                        public void onResult(int status, String responseString) {
                            Log.d(TAG, "remote bind:" + responseString);
                            try {
                                long expireTime = Long.parseLong(RobotSettings.getRobotString(mContext,
                                        Definition.ROBOT_EXPIRES_TIME)) * 1000;
                                Log.d(TAG, "checkNtpTime after refresh expire: " + expireTime);
                                if (expireTime > 0) {
                                    Date expireData = new Date(expireTime);
                                    if (date.after(expireData)) {
                                        SystemStatus.TIME_WARNING.update(true);
                                        mSystemManager.updateStatus(SystemStatus.TIME_WARNING, false);
                                        TimeWarningReport report = new TimeWarningReport();
                                        report.addAction(TimeWarningReport.ACTION_TRIGGER)
                                                .addType(TimeWarningReport.TYPE_OVER_EXPIRE).report();
                                        return;
                                    }
                                }
                            } catch (InvalidArgumentException | NoSuchKeyException e) {
                                e.printStackTrace();
                            }
                            SystemStatus.TIME_WARNING.update(false);
                            mSystemManager.removeSystemStatus(SystemStatus.TIME_WARNING);
                        }
                    });
                    return;
                }
            }
        } catch (InvalidArgumentException | NoSuchKeyException | NumberFormatException e) {
            e.printStackTrace();
        }
        SystemStatus.TIME_WARNING.update(false);
        mSystemManager.removeSystemStatus(SystemStatus.TIME_WARNING);
    }
}
