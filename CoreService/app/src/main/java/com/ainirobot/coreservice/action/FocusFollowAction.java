/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.action;

import android.os.SystemClock;
import android.util.Log;

import com.ainirobot.coreservice.action.StopCommandList.IStopCommandChecker;
import com.ainirobot.coreservice.action.StopCommandList.StopCommand;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.Definition.TrackMode;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.actionbean.FocusFollowBean;
import com.ainirobot.coreservice.client.actionbean.Person;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.core.system.RobotSetting;
import com.ainirobot.coreservice.utils.DelayTimer;
import com.ainirobot.coreservice.utils.MessageParser;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicInteger;

public class FocusFollowAction extends AbstractAction<FocusFollowBean> {
    private static final String TAG = "FocusFollowAction";
    private static final long MIN_LOST_TIMEOUT = 500;
    private static final long TRACK_TIMEOUT = RobotSetting.getFocusFollowTrackTimeout();

    private static final int MIN_FRAME_COUNT = 3; //Distance to determine the minimum number of frames
    private static final long BASE_TIME = 100;

    private int mPersonId;
    private Person mCurrentPerson;

    private long mLostTimeout;
    private double mAvailableDistance;
    private String mSpeedListener;
    private DelayTimer mLostTimer;
    private DelayTimer mTrackTimer;
    private AtomicInteger mFrameCount = new AtomicInteger();
    private boolean isAllowMoveBody = true;

    private long mMoveTime = 0;
    private int mLastAngle = 0;
    private float mOffset = 3.0f;

    private static final int OFFSET_ANGLE_MINI = 10;


    public FocusFollowAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_FOCUS_FOLLOW, resList);
    }

    @Override
    protected boolean startAction(FocusFollowBean parameter, LocalApi api) {
        Log.d(TAG, "Focus follow started");
        mReqId = parameter.getReqId();
        parseParams(parameter);

        mCurrentPerson = null;
        mFrameCount.set(0);

        mLastAngle = 0;
        mMoveTime = 0;

        mOffset = RobotSetting.getFocusFollowAngleAdjust();
        Log.d(TAG, "mOffset:" + mOffset);

        registerSpeedListener();
        mApi.setTrackTarget(mReqId, "", mPersonId, TrackMode.FACE);
        initTimer();
        return true;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        Log.d(TAG, "Focus follow stopped : " + isResetHW);
        unregisterStatusListener(mSpeedListener);
        //mApi.stopTrack(mReqId);
        mApi.stopSendPersonInfos();
        if (isAllowMoveBody) {
            mApi.stopMove(mReqId);
        }

        mApi.stopTrack(mReqId);
        mLastAngle = 0;
        mMoveTime = 0;

        destroyTimer();

        if (isResetHW) {
            mApi.resetHead(mReqId);
        }
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_HEAD_GET_ALL_PERSON_INFOS:
                handleFaceInfo(result);
                break;

            case Definition.CMD_HEAD_SET_TRACK_TARGET:
                handleTrackResult(result);
                break;

            default:
                break;
        }
        return true;
    }

    private void initTimer() {
        Log.d(TAG, "Timer init");
        mLostTimer = new DelayTimer(mLostTimeout, new Runnable() {
            @Override
            public void run() {
                onPersonLost();
            }
        });
        mLostTimer.start();

        mTrackTimer = new DelayTimer(TRACK_TIMEOUT, new Runnable() {
            @Override
            public void run() {
                onTrackTimeout();
            }
        });
        mTrackTimer.start();
    }

    private void destroyTimer() {
        if (mLostTimer != null) {
            mLostTimer.destroy();
            mLostTimer = null;
        }
        if (mTrackTimer != null) {
            mTrackTimer.destroy();
            mTrackTimer = null;
        }
    }

    private void handleTrackResult(String params) {
        try {
            Log.d(TAG, "Set track target result : " + params);
            JSONObject json = new JSONObject(params);
            String status = json.getString("status");
            switch (status) {
                case Definition.RESPONSE_OK:
                    mApi.getAllPersonInfos(mReqId, Definition.JSON_HEAD_CONTINUOUS);
                    onStatusUpdate(Definition.STATUS_TRACK_TARGET_SUCCEED, "Track target succeed");
                    break;

                case Definition.RESPONSE_TARGET_NOT_FOUND:
                    onError(Definition.ERROR_TARGET_NOT_FOUND, "Target not found");
                    break;

                case Definition.RESPONSE_ALREADY_IN_MODE:
                    Log.d(TAG, "Current already int track mode");
                    break;

                default:
                    break;
            }
        } catch (JSONException e) {
            onError(Definition.ERROR_SET_TRACK_FAILED, "Set track target failed");
        }
    }

    private void handleFaceInfo(String params) {
        //deal with first frame null，fresh timer
        if (deelExceptionFrame(params))
            return;
        Person person = MessageParser.parseFaceInfo(mPersonId, params);
        //Person person = MessageParser.parseFaceInfo(mCurrentPerson, params, Double.MAX_VALUE);
        if (person != null) {
            if (isAllowMoveBody) {
                int angle = person.getFaceInfo().getAngle();
                int angleInView = -Double.valueOf(person.getFaceInfo().getAngleInView()).intValue();
//                Log.i(TAG, "angle:" + angle
//                        + ", inView:" + person.getFaceInfo().getAngleInView()
//                        + ", faceX:" + person.getFaceInfo().getFaceAngleX()
//                        + ", faceY:" + person.getFaceInfo().getFaceAngleY()
//                        + ", speed:" + person.getFaceInfo().getHeadSpeed()
//                        + ", latency:" + person.getFaceInfo().getLatency());


                if (isNewAngle(angle) || isMoveTimeout(BASE_TIME)) {
                    if (ProductInfo.isMiniProduct() || ProductInfo.isMeissa2()) {
                        angle = angle + (int)mOffset;
                        mApi.motionPid(mReqId, angle, person.getFaceInfo().getLatency());
                    } else if (ProductInfo.isMeissaPlus()) {
//                        mApi.motionPid(mReqId, angleInView, person.getFaceInfo().getLatency());
                    } else {
                        mApi.motionArc(mReqId, 0, angle,
                            person.getFaceInfo().getHeadSpeed(), person.getFaceInfo().getLatency());
                    }
                    mLastAngle = angle;
                    mMoveTime = SystemClock.uptimeMillis();
                }
            }

            double distance = person.getFaceInfo().getDistance();
            if (isFaraway(distance)) {
                onStatusUpdate(Definition.STATUS_GUEST_FARAWAY, "Person is too far away ：" + distance);
            }

            if (mCurrentPerson == null) {
                onStatusUpdate(Definition.STATUS_GUEST_APPEAR, "Person appear");
            }
            mCurrentPerson = person;

            Log.d(TAG, "Timer reset");
            if (mLostTimer != null) {
                mLostTimer.reset();
            }

            if (mTrackTimer != null) {
                mTrackTimer.reset();
            }
        } else {
            Log.d(TAG, "Get all person info : no person");
            //TODO: Deal with the situation where no one has been found
        }
    }

    private boolean deelExceptionFrame(String params) {
        if (ProductInfo.isMiniProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMeissa2())
            return false;
        try {
            JSONArray jsonArray = new JSONArray(params);
            if (jsonArray.length() == 0) {
                mLostTimer.reset();
                mTrackTimer.reset();
                return true;
            }
        } catch (JSONException e) {
            e.printStackTrace();
            mLostTimer.reset();
            mTrackTimer.reset();
            return true;
        }
        return false;
    }

    private boolean isFaraway(double distance) {
        if (distance > mAvailableDistance) {
            return mFrameCount.incrementAndGet() >= MIN_FRAME_COUNT;
        } else {
            mFrameCount.set(0);
            return false;
        }
    }

    private void onPersonLost() {
        Log.d(TAG, "lost timeout");
        onStatusUpdate(Definition.STATUS_GUEST_LOST, "Lost timeout : " + mLostTimeout);
        mCurrentPerson = null;
    }

    private void onTrackTimeout() {
        Log.d(TAG, "track timeout");
        if (isAllowMoveBody) {
            mApi.stopMove(mReqId);
        }
    }

    private void parseParams(FocusFollowBean params) {
        mPersonId = params.getPersonId();

        double maxDistance = params.getMaxDistance();
        mAvailableDistance = ((maxDistance == 0) ? Double.MAX_VALUE : maxDistance);

        long trackTimeout = params.getLostTimer();
        mLostTimeout = trackTimeout < MIN_LOST_TIMEOUT ? MIN_LOST_TIMEOUT : trackTimeout;
//        mLostTimeout = MIN_LOST_TIMEOUT;
        isAllowMoveBody = params.isAllowMoveBody();
        Log.d(TAG, "Focus follow params:mPersonId=" + mPersonId +
                " mLostTimeout=" + mLostTimeout + " TRACK_TIMEOUT=" + TRACK_TIMEOUT);
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return new ArrayList<StopCommand>() {{
            //add(new StopCommand(Definition.CMD_HEAD_STOP_TRACK_TARGET, null));
        }};
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return new IStopCommandChecker() {
            @Override
            public boolean executeSuccess(String command, String result) {
                Log.d(TAG, "Focus stop check : " + command);
                switch (command) {
                    case Definition.CMD_HEAD_STOP_TRACK_TARGET:
                        return true;

                    default:
                        return false;
                }
            }
        };
    }

    @Override
    protected boolean verifyParam(FocusFollowBean param) {
        return true;
    }

    private void registerSpeedListener() {
        mSpeedListener = registerStatusListener(Definition.STATUS_SPEED, new StatusListener() {
            @Override
            public void onStatusUpdate(String data) {
                try {
                    if (data == null) {
                        return;
                    }
                    JSONObject json = new JSONObject(data);
                    double speedZ = json.getDouble(Definition.JSON_NAVI_ANGULAR_SPEED_Z);
                    double speedX = json.getDouble(Definition.JSON_NAVI_LINEAR_SPEED_X);
                    mApi.reportNavigationStatus(mReqId, speedX, speedZ);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private boolean isNewAngle(int angle) {
        return mLastAngle != angle;
    }

    private boolean isMoveTimeout(long baseTime) {
        long currentTime = SystemClock.uptimeMillis();
        return (currentTime - mMoveTime) > baseTime;
    }

}
