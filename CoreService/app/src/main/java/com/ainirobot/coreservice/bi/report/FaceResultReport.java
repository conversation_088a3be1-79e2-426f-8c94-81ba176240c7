/*
 *   Copyright (C) 2017 OrionStar Technology Project
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.ainirobot.coreservice.bi.report;

import com.ainirobot.coreservice.client.listener.Person;
import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

public class FaceResultReport extends BaseBiReport {

    private static final String TABLE_NAME = "gb_face_result";

    public FaceResultReport() {
        super(TABLE_NAME);
    }

    public FaceResultReport addNum(String num) {
        addData("num", num);
        return this;
    }

    public FaceResultReport addPerson(Person person) {
        addData("person_id", person.getUserId());
        addData("name", person.getName());
        addData("age", person.getAge());
        addData("gender", person.getGender());
        addData("role", person.getRole());
        addData("face_id", person.getRemoteFaceId());
        addData("face_register_time", person.getUserRegisterTime());
        addData("req_id", person.getRemoteReqId());
        return this;
    }

    @Override
    public void report() {
        addData("ctime", System.currentTimeMillis());
        super.report();
    }
}
