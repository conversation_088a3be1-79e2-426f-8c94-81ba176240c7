/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.data.language;

import android.content.ContentValues;
import android.content.Context;
import android.database.ContentObserver;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import android.provider.BaseColumns;
import android.util.Log;

import com.ainirobot.coreservice.client.actionbean.LanguageBean;
import com.ainirobot.coreservice.data.provider.BaseDataHelper;
import com.ainirobot.coreservice.data.provider.Column.Constraint;
import com.ainirobot.coreservice.data.provider.Column.DataType;
import com.ainirobot.coreservice.data.provider.SQLiteTable;

import java.util.ArrayList;
import java.util.List;

/**
 * 系统语言
 * <p>
 * 数据初始化依赖本地配置和请求服务端支持列表
 */
public class LanguageDataHelper extends BaseDataHelper {
    private static final String TAG = "LanguageDataHelper";

    private Context mContext;

    public static final SQLiteTable TABLE = new SQLiteTable(LanguageField.TABLE_NAME,
            LanguageProvider.AUTHORITY, true) {
        @Override
        public void importData(Context context, SQLiteDatabase db) {
            LanguageImportUtils.initLanguage(db, context);
        }

        @Override
        public void update(Context context, SQLiteDatabase db, int oldVersion, int newVersion) {
        }
    };

    static {
        TABLE.addColumn(LanguageField.LANG_CODE, Constraint.UNIQUE, DataType.TEXT)
                .addColumn(LanguageField.LANG_NAME, DataType.TEXT)
                .addColumn(LanguageField.IS_DEFAULT, DataType.INTEGER)
                .addColumn(LanguageField.SUPPORT, DataType.INTEGER);
    }


    public LanguageDataHelper(Context context) {
        super(context);
        this.mContext = context;
    }

    @Override
    public Uri getContentUri() {
        return TABLE.getContentUri();
    }

    private ContentValues getContentValues(LanguageBean info) {
        ContentValues values = new ContentValues();
        values.put(LanguageField.LANG_CODE, info.getLangCode());
        values.put(LanguageField.LANG_NAME, info.getLangName());
        values.put(LanguageField.IS_DEFAULT, info.getIsDefault());
        values.put(LanguageField.SUPPORT, info.getSupport());
        return values;
    }

    public void bulkInsert(List<LanguageBean> infos) {
        ArrayList<ContentValues> contentValues = new ArrayList<>();
        for (LanguageBean data : infos) {
            ContentValues values = getContentValues(data);
            contentValues.add(values);
        }

        ContentValues[] valueArray = new ContentValues[contentValues.size()];
        bulkInsert(contentValues.toArray(valueArray));
    }

    public void insert(LanguageBean info) {
        ContentValues values = getContentValues(info);
        insert(values);
    }

    public void deleteJustSupportServerLanguage() {
        int deleteRows = delete(LanguageField.SUPPORT + "=?",
                new String[]{LanguageBean.SupportState.SERVER + ""});
        Log.i(TAG, "deleteJustSupportServerLanguage: deleteRows=" + deleteRows);
    }

    public void deleteAll() {
        int deleteRows = delete(null, null);
        Log.i(TAG, "deleteAll: deleteRows=" + deleteRows);
    }

    public void updateSupportState(String langCode, int state) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(LanguageField.SUPPORT, state);
        int updateRows = update(contentValues,
                LanguageField.LANG_CODE + "=?", new String[]{langCode});
        Log.i(TAG, "updateLanguageSupportState: updateRows=" + updateRows);
    }

    public void setSpecialLanguageDefault(String langCode) {
        Log.i(TAG, "setSpecialLanguageDefault: langCode=" + langCode);
        ContentValues contentValues = new ContentValues();
        contentValues.put(LanguageField.IS_DEFAULT, LanguageBean.UseState.DEFAULT);
        int updateRows = update(contentValues,
                LanguageField.LANG_CODE + "=?", new String[]{langCode});
        Log.i(TAG, "setSpecialLanguageDefault: updateRows=" + updateRows);
    }

    public void setAllLanguageNotDefault() {
        ContentValues contentValues = new ContentValues();
        contentValues.put(LanguageField.IS_DEFAULT, LanguageBean.UseState.NOT_DEFAULT);
        int updateRows = update(contentValues, null, null);
        Log.i(TAG, "setAllLanguageNotDefault: updateRows=" + updateRows);
    }

    public List<LanguageBean> getAllLanguageList() {
        List<LanguageBean> langInfos = new ArrayList<>();
        Cursor cursor = query(null, null, null, null);
        if (cursor == null) {
            return langInfos;
        }
        while (cursor.moveToNext()) {
            LanguageBean info = new LanguageBean();
            info.setLangCode(cursor.getString(cursor.getColumnIndex(LanguageField.LANG_CODE)));
            info.setLangName(cursor.getString(cursor.getColumnIndex(LanguageField.LANG_NAME)));
            info.setIsDefault(cursor.getInt(cursor.getColumnIndex(LanguageField.IS_DEFAULT)));
            info.setSupport(cursor.getInt(cursor.getColumnIndex(LanguageField.SUPPORT)));
            langInfos.add(info);
        }
        cursor.close();
        return langInfos;
    }

    public void registerListener(ContentObserver contentObserver) {
        mContext.getContentResolver().registerContentObserver(
                getContentUri(), true, contentObserver);
    }

    public static final class LanguageField implements BaseColumns {
        static final String TABLE_NAME = "language_list";

        static final String LANG_CODE = "langCode";
        static final String LANG_NAME = "langName";
        static final String IS_DEFAULT = "isDefault";
        static final String SUPPORT = "support";
    }

}
