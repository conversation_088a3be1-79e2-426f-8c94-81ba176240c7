package com.ainirobot.coreservice.client.upload.bi;

/**
 * Created by Orion on 2020/4/28.
 */
public class CallChainDef {

    //调用链业务名称
    public static final String NAME_MOTION_BODY_FOLLOW = "motion_body_follow";
    public static final String NAME_NAVIGATION_BODY_FOLLOW = "navigation_body_follow";

    //信息来源
    public static final String SOURCE_ROM = "rom";
    public static final String SOURCE_ROBOT_OS = "robot_os";
    public static final String SOURCE_VISION_SDK = "vision_sdk";
    public static final String SOURCE_ROVER_SERVICE = "rover_service";

    //节点所属app包名
    public static final String APP_PLATFORM = "com.ainirobot.platform";
    public static final String APP_CORESERVICE = "com.ainirobot.coreservice";
    public static final String APP_NAVIGATIONSERVICE = "com.ainirobot.navigationservice";
    public static final String APP_HEADSERVICE = "com.ainirobot.headservice";

    //节点方法类型
    public static final String TYPE_API_CALL = "api_call";
    public static final String TYPE_API_CALLBACK = "api_callback";
    public static final String TYPE_EVENT = "event";
    public static final String TYPE_LIFECYCLE = "lifecycle";
    public static final String TYPE_FUNCTION = "function";

}
