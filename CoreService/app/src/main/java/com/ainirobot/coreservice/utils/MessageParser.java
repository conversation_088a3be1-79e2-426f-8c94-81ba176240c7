/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.utils;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.Person;
import com.ainirobot.coreservice.client.actionbean.Person.FaceInfo;
import com.ainirobot.coreservice.client.actionbean.PictureInfo;
import com.ainirobot.coreservice.client.actionbean.RegisterRemoteBean;
import com.ainirobot.coreservice.data.core.PersonDataHelper;
import com.ainirobot.robotlog.RobotLog;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class MessageParser {

    private static final double AVAILABLE_DISTANCE = 3d;
    private static final float SWITCH_TRACKING_DISTANCE = 0.2f;
    private static final int NEAR_TRACKING = 15;

    public static final String RESULT_OK = Definition.CMD_STATUS_OK;
    public static final String RESULT_FAILED = Definition.CMD_STATUS_FAILED;

    public static Person parsePersonInfo(String trackingName, String message) {
        if (TextUtils.isEmpty(message) || "[]".equals(message)
                || TextUtils.isEmpty(trackingName)) {
            return null;
        }

        Context context = ApplicationWrapper.getContext();
        PersonDataHelper mPersonData = new PersonDataHelper(context);
        try {
            JSONArray persons = new JSONArray(message);
            for (int i = 0; i < persons.length(); i++) {
                JSONObject json = persons.getJSONObject(i);
                int id = json.optInt("id");
                String name = json.optString("name");
                float distance = (float) json.optDouble("distance");
                int angle = json.optInt("angle");
                int headSpeed = json.optInt("headSpeed");
                int latency = json.optInt("latency");

                if (distance > AVAILABLE_DISTANCE) {
                    continue;
                }

                if (trackingName.equals(name)) {
                    Person temp = mPersonData.getPerson(trackingName);
                    if (temp == null) {
                        temp = new Person();
                    }
                    temp.setRegisterName(trackingName);

                    FaceInfo faceInfo = temp.getFaceInfo();
                    faceInfo.setDistance(distance);
                    faceInfo.setAngle(angle);
                    faceInfo.setHeadSpeed(headSpeed);
                    faceInfo.setLatency(latency);
                    faceInfo.setFaceId(id);

                    return temp;
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Person parseFaceInfo(int personId, String message) {
        try {
            JSONArray jsonArray = new JSONArray(message);
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                int id = jsonObject.optInt("id");
                if (id < 0) {
                    break;
                }
                if (personId == id) {
                    String name = jsonObject.optString("name");
                    float distance = (float) jsonObject.optDouble("distance");
                    int angle = jsonObject.optInt("angle");
                    int headSpeed = jsonObject.optInt("headSpeed");
                    int latency = jsonObject.optInt("latency");
                    double faceAngleX = jsonObject.optDouble("faceAngleX");
                    double faceAngleY = jsonObject.optDouble("faceAngleY");
                    double angleInView = jsonObject.optDouble("angleInView");
                    int spec = jsonObject.optInt("spec");

                    Person person = new Person();
                    person.setRegisterName(name);
                    person.setSpec(spec);
                    person.getFaceInfo().setFaceId(id);
                    person.getFaceInfo().setDistance(distance);
                    person.getFaceInfo().setAngle(angle);
                    person.getFaceInfo().setHeadSpeed(headSpeed);
                    person.getFaceInfo().setLatency(latency);
                    person.getFaceInfo().setFaceAngleX(faceAngleX);
                    person.getFaceInfo().setFaceAngleY(faceAngleY);
                    person.getFaceInfo().setAngleInView(angleInView);
                    return person;
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static ArrayList<Person> parseAllFaceInfo(String message, double maxDistance) {
        ArrayList<Person> persons = new ArrayList<>();
        try {
            JSONArray jsonArray = new JSONArray(message);
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                int id = jsonObject.optInt("id");
                if (id < 0) {
                    break;
                }

                String name = jsonObject.optString("name");
                float distance = (float) jsonObject.optDouble("distance");
                int angle = jsonObject.optInt("angle");
                int headSpeed = jsonObject.optInt("headSpeed");
                int latency = jsonObject.optInt("latency");
                double faceAngleX = jsonObject.optDouble("faceAngleX");
                double faceAngleY = jsonObject.optDouble("faceAngleY");
                double angleInView = jsonObject.optDouble("angleInView");
                int spec = jsonObject.optInt("spec");
                int associateId = jsonObject.optInt("associateId");

                if (distance > maxDistance) {
                    continue;
                }

                Person person = new Person();
                person.setRegisterName(name);
                person.setSpec(spec);
                person.getFaceInfo().setFaceId(id);
                person.getFaceInfo().setAssociateId(associateId);
                person.getFaceInfo().setDistance(distance);
                person.getFaceInfo().setAngle(angle);
                person.getFaceInfo().setHeadSpeed(headSpeed);
                person.getFaceInfo().setLatency(latency);
                person.getFaceInfo().setFaceAngleX(faceAngleX);
                person.getFaceInfo().setFaceAngleY(faceAngleY);
                person.getFaceInfo().setAngleInView(angleInView);
                persons.add(person);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return persons;
    }


    public static Person parseFaceInfo(Person trackingPerson, String message) {
        return parseFaceInfo(trackingPerson, message, AVAILABLE_DISTANCE);
    }

    public static Person parseFaceInfo(Person trackingPerson, String message, double maxDistance) {
        if (TextUtils.isEmpty(message) || "[]".equals(message)) {
            return null;
        }

        Context context = ApplicationWrapper.getContext();
        PersonDataHelper mPersonData = new PersonDataHelper(context);
        try {
            Person person = null;
            Person tracking = null;
            Person nearTracking = null;
            JSONArray persons = new JSONArray(message);
            for (int i = 0; i < persons.length(); i++) {
                JSONObject json = persons.getJSONObject(i);
                int id = json.optInt("id");
                String name = json.optString("name");
                float distance = (float) json.optDouble("distance");
                int angle = json.optInt("angle");
                int headSpeed = json.optInt("headSpeed");
                int latency = json.optInt("latency");
                double faceAngleX = json.optDouble("faceAngleX");
                double faceAngleY = json.optDouble("faceAngleY");
                double angleInView = json.optDouble("angleInView");

                if (distance > maxDistance) {
                    continue;
                }

                if (trackingPerson != null
                        && ((!TextUtils.isEmpty(name) && name.equals(trackingPerson.getRegisterName()))
                        || trackingPerson.getFaceInfo().getFaceId() == id)) {
                    if (TextUtils.isEmpty(trackingPerson.getRegisterName())
                            && !TextUtils.isEmpty(name)) {
                        Person temp = mPersonData.getPerson(name);
                        if (temp != null) {
                            trackingPerson = temp;
                        }
                    }
                    FaceInfo faceInfo = trackingPerson.getFaceInfo();
                    faceInfo.setDistance(distance);
                    faceInfo.setAngle(angle);
                    faceInfo.setHeadSpeed(headSpeed);
                    faceInfo.setLatency(latency);
                    faceInfo.setFaceId(id);
                    faceInfo.setFaceAngleX(faceAngleX);
                    faceInfo.setFaceAngleY(faceAngleY);
                    faceInfo.setAngleInView(angleInView);

                    tracking = trackingPerson;
                } else {
                    if (trackingPerson != null) {
                        float trackingAngle = trackingPerson.getFaceInfo().getAngle();
                        float nearAngle = (nearTracking == null ?
                                0 : nearTracking.getFaceInfo().getAngle());
                        float diffAngle = Math.abs(angle - trackingAngle);
                        float nearDiffAngle = Math.abs(nearAngle - trackingAngle);

                        if (diffAngle < NEAR_TRACKING &&
                                (nearTracking == null || diffAngle < nearDiffAngle)) {
                            if (nearTracking == null) {
                                nearTracking = new Person();
                            }
                            nearTracking.setRegisterName(name);
                            nearTracking.getFaceInfo().setFaceId(id);
                            nearTracking.getFaceInfo().setDistance(distance);
                            nearTracking.getFaceInfo().setAngle(angle);
                            nearTracking.getFaceInfo().setHeadSpeed(headSpeed);
                            nearTracking.getFaceInfo().setLatency(latency);
                            nearTracking.getFaceInfo().setFaceAngleX(faceAngleX);
                            nearTracking.getFaceInfo().setFaceAngleY(faceAngleY);
                            nearTracking.getFaceInfo().setAngleInView(angleInView);
                        }
                    }

                    if (person != null) {
                        float currentDistance = person.getFaceInfo().getDistance();
                        if (currentDistance != 0
                                && (currentDistance < distance || distance == 0)) {
                            continue;
                        } else if (distance == 0) {
                            float currentAngle = person.getFaceInfo().getAngle();
                            if (Math.abs(currentAngle) < Math.abs(angle)) {
                                continue;
                            }
                        }
                    }

                    if (person == null) {
                        person = new Person();
                    }

                    person.setRegisterName(name);
                    person.getFaceInfo().setFaceId(id);
                    person.getFaceInfo().setDistance(distance);
                    person.getFaceInfo().setAngle(angle);
                    person.getFaceInfo().setHeadSpeed(headSpeed);
                    person.getFaceInfo().setLatency(latency);
                    person.getFaceInfo().setFaceAngleX(faceAngleX);
                    person.getFaceInfo().setFaceAngleY(faceAngleY);
                    person.getFaceInfo().setAngleInView(angleInView);
                }
            }

            if (person == null) {
                return tracking;
            }

            if (!TextUtils.isEmpty(person.getRegisterName())) {
                Person temp = mPersonData.getPerson(person.getRegisterName());
                if (temp != null) {
                    person = temp;
                } else {
                    person.setName("");
                }
            }

            if (nearTracking != null && !TextUtils.isEmpty(nearTracking.getRegisterName())) {
                Person temp = mPersonData.getPerson(nearTracking.getRegisterName());
                if (temp != null) {
                    nearTracking = temp;
                } else {
                    nearTracking.setName("");
                }
            }

            if (tracking == null) {
                int trackingId = (trackingPerson == null ? -1 : trackingPerson.getFaceInfo()
                                                                              .getFaceId());
                RobotLog.e("switch person : tracking is null, "
                        + " tracking id = " + trackingId
                        + " person id = " + person.getFaceInfo().getFaceId());
                if (nearTracking != null) {
                    RobotLog.d("switch person detail : tracking angle is "
                            + trackingPerson.getFaceInfo().getAngle() +
                            "  near angle is " + nearTracking.getFaceInfo().getAngle());
                } else {
                    RobotLog.d("switch person detail : person angle is "
                            + person.getFaceInfo().getAngle() +
                            "  distance is " + person.getFaceInfo().getDistance());
                }
                return nearTracking != null ? nearTracking : person;
            }

            float trackingDistance = tracking.getFaceInfo().getDistance();
            float minDistance = person.getFaceInfo().getDistance();
            if (trackingDistance == 0) {
                return tracking;
            } else {
                if (minDistance == 0) {
                    return tracking;
                } else if (trackingDistance - minDistance > SWITCH_TRACKING_DISTANCE) {
                    RobotLog.e("switch person : " +
                            "tracking distance is " + trackingDistance +
                            ", min distance is " + minDistance);
                    return person;
                } else {
                    return tracking;
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return null;
    }


    public static Person parseFaceInfo(String message) {
        if (TextUtils.isEmpty(message) || "[]".equals(message)) {
            return null;
        }

        try {
            JSONObject json = new JSONObject(message);
            String name = json.optString("name");
            Log.d("MessageParser", "parseFaceInfo name: " + name);
            Person person = new Person();
            person.setRegisterName(name);
            person.getFaceInfo().setFaceId(json.optInt("id"));
            person.getFaceInfo().setDistance((float) json.optDouble("distance"));
            person.getFaceInfo().setAngle(json.optInt("angle"));
            person.getFaceInfo().setHeadSpeed(json.optInt("headSpeed"));
            person.getFaceInfo().setLatency(json.optInt("latency"));
            return person;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static boolean parseResult(String message) {
        try {
            JSONObject json = new JSONObject(message);
            return RESULT_OK.equals(json.getString("status"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public static Person parseOneFaceInfo(String message) {
        if (TextUtils.isEmpty(message) || "[]".equals(message)) {
            return null;
        }

        try {
            Person person;
            JSONObject json = new JSONObject(message);
            String name = json.optString("name");
            Log.d("MessageParser", "parseFaceInfo name: " + name);
            if (!TextUtils.isEmpty(name)) {
                Context context = ApplicationWrapper.getContext();
                PersonDataHelper mPersonData = new PersonDataHelper(context);

                person = mPersonData.getPerson(name);
                if (person == null) {
                    person = new Person();
                }
            } else {
                person = new Person();
            }
            person.setRegisterName(name);
            person.getFaceInfo().setFaceId(json.optInt("id"));
            person.getFaceInfo().setDistance((float) json.optDouble("distance"));
            person.getFaceInfo().setAngle(json.optInt("angle"));
            person.getFaceInfo().setHeadSpeed(json.optInt("headSpeed"));
            person.getFaceInfo().setLatency(json.optInt("latency"));
            return person;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Person parseBodyInfo(String trackingName, String message) {
        if (TextUtils.isEmpty(message) || "[]".equals(message) || TextUtils.isEmpty(trackingName)) {
            return null;
        }

        Person person = null;

        try {
            JSONArray persons = new JSONArray(message);
            if (persons != null && persons.length() > 0) {
                for (int i = 0; i < persons.length(); i++) {
                    JSONObject json = persons.getJSONObject(i);
                    String name = json.optString("name");
                    if (trackingName.equals(name)) {
                        float distance = (float) json.optDouble("distance");
                        Log.d("MessageParser", "parseFaceInfo name: " + name
                                + ", distance: " + distance);
                        person = new Person();
                        person.getFaceInfo().setFaceId(json.optInt("id"));
                        person.getFaceInfo().setDistance(distance);
                        person.getFaceInfo().setAngle(json.optInt("angle"));
                        person.getFaceInfo().setHeadSpeed(json.optInt("headSpeed"));
                        person.getFaceInfo().setLatency(json.optInt("latency"));
                        person.setRegisterName(trackingName);
                        break;
                    }
                }
            }
            return person;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return person;
    }

    public static RegisterRemoteBean parseRemoteResult(String result) {
        if (TextUtils.isEmpty(result) || "{}".equals(result) || "[]".equals(result))
            return null;
        try {
            JSONObject json = new JSONObject(result);
            RegisterRemoteBean remoteBean = new RegisterRemoteBean();
            int code = json.optInt("code");
            if (code == 0) {
                remoteBean.setCode(code);
                remoteBean.setFlag(json.optString("flag"));
                remoteBean.setMessage(json.optString("message"));
                remoteBean.setName(json.optString("name"));
                remoteBean.setRemoteId(json.optString("remoteId"));
                String userId = "";
                String data1 = json.optString("data");
                JSONObject data1Json = new JSONObject(data1);
                userId = data1Json.optString("user_id");
                Log.d("RegiNew", "data1 : " + data1 + " , user_id : " + userId);
                remoteBean.setUserId(userId);
                remoteBean.setIsStaff(data1Json.optInt("is_staff"));
                return remoteBean;
            } else {
                remoteBean.setCode(code);
                return remoteBean;
            }
        } catch (JSONException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static PictureInfo parsePictureInfo(String json) {
        if (TextUtils.isEmpty(json) || "{}".equals(json) || "[]".equals(json))
            return null;
        try {
            JSONObject jsonObj = new JSONObject(json);
            String id = jsonObj.optString("id");
            String status = jsonObj.optString("status");
            List<String> picList = new ArrayList<>();
            JSONArray jsonArray = jsonObj.optJSONArray("pictures");
            if (jsonArray != null && jsonArray.length() > 0) {
                for (int i = 0; i < jsonArray.length(); i++) {
                    String pic = jsonArray.getString(i);
                    picList.add(pic);
                }
            }
            PictureInfo info = new PictureInfo();
            info.setId(id);
            info.setStatus(status);
            info.setPictures(picList);
            return info;
        } catch (JSONException e) {
            e.printStackTrace();
            return null;
        }

    }

}
