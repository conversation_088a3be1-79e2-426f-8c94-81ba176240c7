package com.ainirobot.coreservice.client.techreport;

import android.content.Context;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

public class AbnormalReport extends BaseBiReport {

    private static final String APP_NAME = "app_name";
    private static final String CLASS_NAME = "class_name";
    private static final String METHOD_NAME = "method_name";
    private static final String ABNORMAL_NAME = "abnormal_name";
    private static final String CONDITION = "condition";
    private static final String CONTENT = "content";
    private static final String STACKTRACE = "stacktrace";
    private static final String CTIME = "ctime";
    private static final String WAKEUP_ID = "wakeup_id";

    private Context mContext;

    private AbnormalReport(){
        super("gb_robot_os_abnormal");
    }

    public AbnormalReport(Context context){
        super("gb_robot_os_abnormal");
        mContext = context;
    }

    public AbnormalReport(String className, String methodName){
        super("gb_robot_os_abnormal");
        addData(CLASS_NAME, className);
        addData(METHOD_NAME, methodName);
    }

    public void setAbnormalInfo(String name, String condition, String content){
        addData(ABNORMAL_NAME, name);
        addData(CONDITION, condition);
        addData(CONTENT, content);
    }

    public void setStacktrace(String stacktrace){
        addData(STACKTRACE, stacktrace);
    }

    @Override
    public void report() {
        if(mContext != null){
            addData(APP_NAME, mContext.getPackageName());
        }
        addData(CTIME, System.currentTimeMillis());
        super.report();
    }
}
