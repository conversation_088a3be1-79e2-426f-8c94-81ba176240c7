package com.ainirobot.coreservice.core;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.robotlog.RobotLog;

public class ResponseHandler extends Handler {

    private CoreStateMachine mStateMachine;

    public ResponseHandler(CoreStateMachine stateMachine, Looper looper) {
        super(looper);
        this.mStateMachine = stateMachine;
    }

    @Override
    public void handleMessage(Message msg) {
        Bundle bundle = msg.getData();
        String command = bundle.getString(InternalDef.BUNDLE_TYPE);
        String response = bundle.getString(InternalDef.BUNDLE_RESPONSE);
        String extraData = bundle.getString(InternalDef.BUNDLE_EXTRA_DATA);
        switch (msg.what){
            case InternalDef.MSG_CORE_HW_CMD_RESPONSE:
                mStateMachine.handleAsyncCommandResponse(command, response, extraData);
                break;

            case InternalDef.MSG_CORE_HW_CMD_STATUS:
                mStateMachine.handleAsyncCommandStatus(command, response, extraData);
                break;
        }
    }
}
