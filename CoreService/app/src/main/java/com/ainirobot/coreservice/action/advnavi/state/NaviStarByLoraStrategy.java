package com.ainirobot.coreservice.action.advnavi.state;

import android.util.Log;

import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.MultiRobotStatus;
import com.ainirobot.coreservice.utils.DelayTask;

import java.util.List;

/**
 * Data: 2022/9/28 11:37
 * Author: wanglijing
 * <p>
 * NAVI_LORA_PRIOR_STAR
 * lora优先级星形补位
 * 确认目的地可达后，状态结束；只上报状态，不启动导航
 */
public class NaviStarByLoraStrategy extends BaseMultiRobotNaviState {

    private volatile MultipleStatus multipleStatus;

    private enum MultipleStatus {
        IDLE,
        START,
        WAIT_HANDLE_STATUS,   //处理状态
        HANDLE_STATUS_FINISH, //完成处理F状态
        WAIT_CONFORM,
        WAIT_CONFORM_FINISH
    }

    NaviStarByLoraStrategy(String name) {
        super(name);
    }

    @Override
    protected void preAction(StateData data, IStateListener listener) {
        super.preAction(data, listener);
        updateMultiStatus(MultipleStatus.IDLE);
    }

    @Override
    protected void doAction() {
        super.doAction();
        updateMultiStatus(MultipleStatus.START);
    }

    @Override
    protected void handleMultipleRobotStatus(List<MultiRobotStatus> robotStatusList) {
        switch (multipleStatus) {
            case START:
            case WAIT_CONFORM_FINISH:
            case HANDLE_STATUS_FINISH:
                handleDestinationStatus(robotStatusList);
                break;
        }
    }


    private void handleDestinationStatus(List<MultiRobotStatus> robotStatusList) {
        boolean isConformedStatus = multipleStatus == MultipleStatus.WAIT_CONFORM_FINISH;

        updateMultiStatus(MultipleStatus.WAIT_HANDLE_STATUS);
        DestinationState state = judgeDestinationIsAvailable(robotStatusList);
        Log.i(TAG, "handleDestinationStatus: " + state);
        switch (state) {
            case AVAILABLE:
                if (isConformedStatus) {
                    updateMultiStatus(MultipleStatus.IDLE);
                    //RESULT_NAVIGATION_DESTINATION_AVAILABLE
                    onResult(Definition.RESULT_DESTINATION_AVAILABLE);
                    return;
                }
                conformDestination(robotStatusList);
                break;
            case NERA_DESTINATION:
                //确认在目标点范围内，结束任务
                updateMultiStatus(MultipleStatus.IDLE);
                //RESULT_NAVIGATION_IN_DESTINATION_RANGE
                onResult(Definition.RESULT_DESTINATION_IN_RANGE);
                break;
            case DESTINATION_INVALID:
                //目的地为空，上报异常
                Log.e(TAG, "handleDestinationStatus error !!!");
                updateMultiStatus(MultipleStatus.IDLE);
                onResult(Definition.ERROR_NO_AVAILABLE_DESTINATION);
                break;
            case OCCUPIED:
            case NAVI_IN_RANGE:
            default:
                updateMultiStatus(MultipleStatus.HANDLE_STATUS_FINISH);
                break;
        }
    }

    private void conformDestination(List<MultiRobotStatus> robotStatusList) {
        //如果可以到达，延迟一定时间后确认是否真的可以到达，
        updateMultiStatus(MultipleStatus.WAIT_CONFORM);
        //
        int deviceCnt = calculateHigherPriorityDeviceCnt(robotStatusList);
        long delayTime = deviceCnt * Definition.SECOND + 500;
        Log.i(TAG, "delayTime = " + delayTime);
        delayConfirmDestination(delayTime);
    }


    private void delayConfirmDestination(long delayTime) {
        DelayTask.cancel(DELAY_TAG);
        DelayTask.submit(DELAY_TAG, new Runnable() {
            @Override
            public void run() {
                if (multipleStatus == MultipleStatus.WAIT_CONFORM) {
                    updateMultiStatus(MultipleStatus.WAIT_CONFORM_FINISH);
                }
            }
        }, delayTime);
    }

    /**
     * 根据机器人loraId的大小,获得延迟确认出餐口是否可用的时间
     * loraId越大，延迟确认的时间越短
     * （loraId越大，越先确认目的地是否可达）
     */
    private int calculateHigherPriorityDeviceCnt(List<MultiRobotStatus> robotStatusList) {
        int deviceCnt = 0;
        int curPriority = mMultiRobotConfig.getLoraId();
        for (int i = 0; i < robotStatusList.size(); i++) {
            MultiRobotStatus robotStatus = robotStatusList.get(i);
            if (robotStatus.getId() > curPriority) {
                deviceCnt++;
            }
        }
        return deviceCnt;
    }

    private void updateMultiStatus(MultipleStatus status) {
        Log.i(TAG, "updateMultiStatus: " + status);
        this.multipleStatus = status;
    }

}
