package com.ainirobot.coreservice.config.core;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 地图兼容性配置
 */
public class MapCompatibilityConfig {

    /**
     * 通过Service配置可以获取
     * 或者Global.Settings获取(key为 Definition.ROBOT_MAP_COMPATIBLE_VERSION)
     */
    @SerializedName("最高兼容版本")
    private int mapCompatibleVersion;
    @SerializedName("视觉类型")
    private List<Integer> visionTypes;
    @SerializedName("标识码类型")
    private List<Integer> targetTypes;

    public int getMapCompatibleVersion() {
        return mapCompatibleVersion;
    }

    public void setMapCompatibleVersion(int version) {
        this.mapCompatibleVersion = version;
    }

    public List<Integer> getVisionTypes() {
        return visionTypes;
    }

    public void setVisionTypes(List<Integer> visionTypes) {
        this.visionTypes = visionTypes;
    }

    public List<Integer> getTargetTypes() {
        return targetTypes;
    }

    public void setTargetTypes(List<Integer> targetTypes) {
        this.targetTypes = targetTypes;
    }

    @Override
    public String toString() {
        return "MapCompatibilityConfig{" +
                "mapCompatibleVersion='" + mapCompatibleVersion + '\'' +
                ", visionTypes=" + visionTypes +
                ", targetTypes=" + targetTypes +
                '}';
    }
}
