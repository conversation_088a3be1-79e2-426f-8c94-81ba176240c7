/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.action;

import android.util.Log;

import com.ainirobot.coreservice.action.StopCommandList.IStopCommandChecker;
import com.ainirobot.coreservice.action.StopCommandList.StopCommand;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.ResetEstimateParams;
import com.ainirobot.coreservice.core.LocalApi;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;

public class ResetEstimateAction extends AbstractAction<ResetEstimateParams> {
    private final String TAG = "ResetEstimateAction";
    private int mRetryCount = 1;
    private int mReqId;
    private ActionStatus mStatus = ActionStatus.IDLE;
    private boolean mEnableFishEye = false;

    private enum ActionStatus {
        IDLE, GET_VISION_STATUS, GET_FISHEYE_STATUS, VISION_START, MENUAL_START
    }

    protected ResetEstimateAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_RESET_ESTIMATE, resList);
    }

    @Override
    protected boolean startAction(ResetEstimateParams parameter, LocalApi api) {
        if (mStatus != ActionStatus.IDLE) {
            return false;
        }
        mRetryCount = parameter.getRetryCount();
        mReqId = parameter.getReqId();
        updateStatus(ActionStatus.GET_VISION_STATUS);

        mApi.isRobotHasVision(mReqId);
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        Log.d(TAG, "cmdResponse command: " + command + " result: " + result);
        switch (command) {
            case Definition.CMD_NAVI_IS_HAS_VISIION:
                handleIsHasVisionResult(result);
                return true;

            case Definition.CMD_NAVI_GET_CONFIG:
                handleGetConfigResult(result);
                return true;

            case Definition.CMD_NAVI_LOCATE_VISION:
                handleLocateVisionResult(result);
                return true;

            case Definition.CMD_NAVI_RESET_ESTIMATE:
                handleResetEstimateResult(result);
                return true;
        }
        return false;
    }

    private void handleLocateVisionResult(String result) {
        if ("succeed".equals(result)) {
            onResult(Definition.RESULT_OK, result);
        } else if (mRetryCount > 1) {
            mApi.locateVision(mReqId);
            mRetryCount--;
        } else {
            onResult(Definition.RESULT_FAILURE, result);
        }
    }

    private void handleGetConfigResult(String result) {
        try {
            JSONObject jsonObject = new JSONObject(result);
            mEnableFishEye = jsonObject.optBoolean("enableFishEye", false);
            if (mEnableFishEye) {
                updateStatus(ActionStatus.VISION_START);
                mApi.locateVision(mReqId);
            } else {
                updateStatus(ActionStatus.MENUAL_START);
                mApi.resetEstimate(mReqId);
            }
        } catch (JSONException e) {
            mEnableFishEye = false;
            updateStatus(ActionStatus.MENUAL_START);
            mApi.resetEstimate(mReqId);
        }
    }

    private void handleIsHasVisionResult(String result) {
        if ("true".equals(result)) {
            updateStatus(ActionStatus.GET_FISHEYE_STATUS);
            mApi.getNavigationConfig(mReqId);
        } else {
            updateStatus(ActionStatus.MENUAL_START);
            mApi.resetEstimate(mReqId);
        }
    }

    private synchronized void updateStatus(ActionStatus status) {
        if (status != mStatus) {
            Log.d(TAG, "update status " + mStatus + " to " + status);
            mStatus = status;
        }
    }

    private void handleResetEstimateResult(String result) {
        Log.d(TAG, "Reset estimate result : " + result + "  " + mRetryCount);
        if ("succeed".equals(result)) {
            onResult(Definition.RESULT_OK, result);
        } else if (mRetryCount > 1) {
            mApi.resetEstimate(mReqId);
            mRetryCount--;
        } else {
            onResult(Definition.RESULT_FAILURE, result);
        }
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        mRetryCount = 0;
        mEnableFishEye = false;
        updateStatus(ActionStatus.IDLE);
        return true;
    }

    @Override
    protected ArrayList<StopCommand> getStopCommands() {
        return null;
    }

    @Override
    protected IStopCommandChecker getStopCommandChecker() {
        return null;
    }

    @Override
    protected boolean verifyParam(ResetEstimateParams param) {
        return param.getRetryCount() >= 1;
    }
}
