/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.data.authorization;

import android.content.ContentValues;
import android.content.Context;
import android.database.ContentObserver;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import android.provider.BaseColumns;
import android.util.Log;

import com.ainirobot.coreservice.client.actionbean.AuthBean;
import com.ainirobot.coreservice.data.provider.BaseDataHelper;
import com.ainirobot.coreservice.data.provider.Column.Constraint;
import com.ainirobot.coreservice.data.provider.Column.DataType;
import com.ainirobot.coreservice.data.provider.SQLiteTable;

public class AuthDataHelper extends BaseDataHelper {
    private static final String TAG = "AuthDataHelper";

    private Context mContext;

    public static final SQLiteTable TABLE = new SQLiteTable(AuthField.TABLE_NAME,
            AuthProvider.AUTHORITY, true) {
        @Override
        public void importData(Context context, SQLiteDatabase db) {
            AuthImportUtils.initAuth(db);
        }

        @Override
        public void update(Context context, SQLiteDatabase db, int oldVersion, int newVersion) {
        }
    };

    static {
        TABLE.addColumn(AuthField.PKG_NAME, Constraint.UNIQUE, DataType.TEXT)
                .addColumn(AuthField.PKG_SIGN, DataType.TEXT)
                .addColumn(AuthField.APP_ID, DataType.TEXT)
                .addColumn(AuthField.IS_AUTH, DataType.INTEGER);
    }


    public AuthDataHelper(Context context) {
        super(context);
        this.mContext = context;
    }

    @Override
    public Uri getContentUri() {
        return TABLE.getContentUri();
    }

    private ContentValues getContentValues(AuthBean info) {
        ContentValues values = new ContentValues();
        values.put(AuthField.PKG_NAME, info.getPkgName());
        values.put(AuthField.PKG_SIGN, info.getPkgSign());
        values.put(AuthField.APP_ID, info.getAppId());
        values.put(AuthField.IS_AUTH, info.getAuth());
        return values;
    }

    public void create(AuthBean info) {
        ContentValues values = getContentValues(info);
        insert(values);
    }

    public void update(String pkgName, String pkgSign, String appId) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(AuthField.PKG_SIGN, pkgSign);
        contentValues.put(AuthField.APP_ID, appId);
        contentValues.put(AuthField.IS_AUTH, AuthBean.AuthState.NOT_AUTH);
        int updateRows = update(contentValues,
                AuthField.PKG_NAME + "=?", new String[]{pkgName});
        Log.i(TAG, "update: updateRows=" + updateRows);
    }

    public int compareData(String pkgName, String pkgSign, String appId) {
        Cursor cursor = query(AuthField.PKG_NAME + "=?", new String[]{String.valueOf(pkgName)});
        if (cursor == null) {
            return -1;
        }
        int result = 0;
        while (cursor.moveToNext()) {
            String sign = cursor.getString(cursor.getColumnIndex(AuthField.PKG_SIGN));
            String id = cursor.getString(cursor.getColumnIndex(AuthField.APP_ID));

            if (!pkgSign.equals(sign) || !appId.equals(id)) {
                result = 1;
            }
        }
        cursor.close();
        return result;
    }

    public void registerListener(ContentObserver contentObserver) {
        mContext.getContentResolver().registerContentObserver(
                getContentUri(), true, contentObserver);
    }

    public static final class AuthField implements BaseColumns {
        static final String TABLE_NAME = "AuthInfo";

        static final String PKG_NAME = "pkgName";
        static final String PKG_SIGN = "pkgSign";
        static final String APP_ID = "appId";
        static final String IS_AUTH = "isAuth";
    }

}
