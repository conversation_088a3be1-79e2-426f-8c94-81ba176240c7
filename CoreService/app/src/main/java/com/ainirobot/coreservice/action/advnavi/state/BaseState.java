/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.action.advnavi.state;

import android.content.Context;
import android.support.annotation.NonNull;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.action.NavigationAdvancedAction;
import com.ainirobot.coreservice.action.NavigationResetHeadUtils;
import com.ainirobot.coreservice.action.StatusListener;
import com.ainirobot.coreservice.action.StopCommandList;
import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.Invoker;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.core.LocalApi;
import com.google.gson.Gson;

import java.util.List;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

public class BaseState extends AbstractState implements Invoker {
    public String TAG = "NaviAdvancedAction";
    protected Context mContext;
    public Gson mGson = new Gson();

    private String mStateName;
    public StateData mStateData;
    private volatile boolean bIsAlive = false;
    private static final Object sSyncLock = new Object();
    private IStateListener mListener = null;
    private NavigationAdvancedAction mNavigationAdvancedAction;
    public volatile Definition.ElevatorAction actionState = Definition.ElevatorAction.ACTION_DEFAULT;
    public LocalApi mApi;
    public int mReqId;
    public volatile int callElevatorCmdId;
    public volatile int openElevatorCmdId;
    public volatile int closeElevatorCmdId;
    public volatile int releaseElevatorCmdId;
    public volatile int registerElevatorCmdId;
    public volatile int updateRobotElevatorStatusCmdId;
    public volatile int callGateCmdId;
    public volatile int openGateCmdId;
    public volatile int openATGateCmdId;
    public volatile int closeGateCmdId;
    public volatile int releaseGateCmdId;
    private static final ReadWriteLock sLock = new ReentrantReadWriteLock();
    /**
     * 进出电梯时，导航的线速度
     */
    public final double ELEVATOR_NAVI_LINEAR_SPEED = 0.5;
    /**
     * 进出电梯时，导航的角速度
     */
    public final double ELEVATOR_NAVI_ANGULAR_SPEED = 0.8;
    protected NavigationResetHeadUtils mNavigationResetHeadUtils;

    BaseState(String name) {
        mContext = ApplicationWrapper.getContext();
        TAG += "-" + getClass().getSimpleName();
        if (mContext != null) {
            mStateName = name;
        } else {
            Log.e(TAG, "Context is null");
        }
    }

    @Override
    protected void preAction(StateData data, IStateListener listener) {
        sLock.writeLock().lock();
        try {
            actionState = Definition.ElevatorAction.ACTION_DEFAULT;
            mStateData = data;
            mListener = listener;
        } finally {
            sLock.writeLock().unlock();
            updateAlive(true);
            Log.d(TAG, "preAction: " + mStateData);
        }
    }

    @Override
    protected void doAction() {
    }

    @Override
    protected void exit() {
        Log.d(TAG, mStateName + " state exit");
        updateAlive(false);
        sLock.writeLock().lock();
        try {
            mListener = null;
        } finally {
            sLock.writeLock().unlock();
        }
    }

    @Override
    protected BaseState getNextState() {
        return null;
    }

    @Override
    protected StateData getData() {
        return mStateData;
    }

    @Override
    protected boolean isAvailable() {
        return true;
    }

    @Override
    protected void setAction(NavigationAdvancedAction action) {
        mNavigationAdvancedAction = action;
        if (null != mNavigationAdvancedAction) {
            mApi = mNavigationAdvancedAction.getApi();
            mReqId = mNavigationAdvancedAction.getReqId();
            mNavigationResetHeadUtils = mNavigationAdvancedAction.getNavigationResetHeadUtils();
        }
    }

    @Override
    protected List<StopCommandList.StopCommand> getStopCommands() {
        return null;
    }

    public final String getStateName() {
        return mStateName;
    }

    @Override
    public final boolean isAlive() {
        return bIsAlive;
    }

    private void updateAlive(boolean isAlive){
        synchronized (sSyncLock) {
            Log.i(TAG, "updateAlive " + bIsAlive + " --> " + isAlive);
            bIsAlive = isAlive;
        }
    }

    protected final void onSuccess() {
        if (!isAlive()) {
            Log.e(TAG, "onSuccess but cur state not alive");
            return;
        }
        IStateListener listener = null;
        sLock.readLock().lock();
        try {
            listener = mListener;
        } finally {
            sLock.readLock().unlock();
            if (listener != null) {
                listener.onSuccess();
            } else {
                Log.e(TAG, "onSuccess but no listener");
            }
        }
    }

    public final void onResult(int resultCode) {
        onResult(resultCode, "");
    }

    public final void onResult(int resultCode, String resultMsg) {
        onResult(resultCode, resultMsg, "");
    }

    public final void onResult(int resultCode, String resultMsg, String extraData) {
        StateData stateData = new StateData(null);
        stateData.setResultCode(resultCode);
        stateData.setMsg(resultMsg);
        stateData.setExtraData(extraData);
        onResult(stateData);
    }

    protected final void onResult(@NonNull StateData data) {
        Log.d(TAG, "onResult data:" + data);
        if (!isAlive()) {
            Log.e(TAG, "onResult but cur state not alive");
            return;
        }
        actionResult(data.getResultCode(), data.getMsg(), data.getExtraData());
        if (null != mListener) {
            mListener.onStop();
        }
    }

    protected boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        return true;
    }

    protected boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        return true;
    }

    @Override
    public String toString() {
        return mStateName;
    }

    public void sendCommand(String command, String param) {
        if (!hasAction(command)) {
            return;
        }
        mNavigationAdvancedAction.sendCommand(command, param);
    }

    public String getRobotInfo(int reqId) {
        return getRobotInfo(reqId, "");
    }

    public String getRobotInfo(int reqId, String param) {
        if (hasAction("RobotInfo")) {
            String info = mNavigationAdvancedAction.getRobotInfo(reqId, param);
            Log.d("NaviAdvancedAction", "getRobotInfo reqId: " + reqId + "  info: " + info);
            return info;
        }
        return null;
    }

    public String isAlreadyInElevator() {
        if (hasAction("isAlreadyInElevator")) {
            return mNavigationAdvancedAction.isAlreadyInElevator();
        }
        return null;
    }

    public void actionResult(int result, String message, String extraData) {
        if (!hasAction(String.valueOf(result))) {
            return;
        }
        updateAlive(false); //result
        //TODO 结束错误上报
        switch (result) {

        }
        mNavigationAdvancedAction.onResult(result, message, extraData);
    }

    public void onStatusUpdate(int state, String message) {
        onStatusUpdate(state, message, "");
    }

    public void onStatusUpdate(int state, String message, String extraData) {
        if (!hasAction(String.valueOf(state))) {
            return;
        }
        //TODO 中间状态上报
        switch (state) {

        }
        mNavigationAdvancedAction.onStatusUpdate(state, message, extraData);
    }


    /**
     * 更新action
     * 并设置到 mStateData 方便后续状态知道是什么事件触发的
     *
     * @param action
     */
    public void updateAction(Definition.ElevatorAction action) {
        Log.i(TAG, "updateAction: " + action);
        actionState = action;
        mStateData.setActionCode(action);
    }

    /**
     * 释放电梯
     */
    public void releaseElevator() {
        if (null == mApi || !hasAction("releaseElevator")) {
            return;
        }
        mApi.cancelCommand(releaseElevatorCmdId, Definition.CMD_CONTROL_RELEASE_ELEVATOR);
        releaseElevatorCmdId = mApi.releaseElevator(mReqId);
        Log.d(TAG, "releaseElevator " + Log.getStackTraceString(new Exception()));
    }

    /**
     * 注册电梯
     */
    public void registerElevator() {
        if (null == mApi || !hasAction("registerElevator")) {
            return;
        }
        mApi.cancelCommand(registerElevatorCmdId, Definition.CMD_CONTROL_REGISTER_ELEVATOR);
        registerElevatorCmdId = mApi.registerElevator(mReqId);
        Log.d(TAG, "registerElevator send done. registerElevatorCmdId=" + registerElevatorCmdId);
    }

    /**
     * 上报机器人状态给梯控
     */
    public void updateRobotElevatorStatus(Definition.RobotElevatorState robotState) {
        if (null == mApi || !hasAction("updateRobotElevatorStatus")) {
            return;
        }
        mApi.cancelCommand(updateRobotElevatorStatusCmdId, Definition.CMD_CONTROL_ROBOT_STATE_UPDATE);
        updateRobotElevatorStatusCmdId = mApi.updateRobotElevatorStatus(mReqId, robotState);
        Log.d(TAG, "updateRobotElevatorStatus robotState=" + robotState);
    }

    /**
     * 呼梯到指定楼层
     * @param inElevator 是否在电梯内
     * @param originFloor 出发楼层
     * @param targetFloor 目标楼层
     */
    public void callElevator(boolean inElevator, int originFloor, int targetFloor){
        if (null == mApi || !hasAction("callElevator")) {
            return;
        }
        mApi.cancelCommand(callElevatorCmdId, Definition.CMD_CONTROL_CALL_ELEVATOR);
        callElevatorCmdId = mApi.callElevatorToTargetFloor(mReqId, inElevator, originFloor, targetFloor);
        Log.d(TAG, "callElevator inElevator: " + inElevator + " originFloor: " + originFloor
                + " targetFloor: " + targetFloor + " cmdId: " + callElevatorCmdId);
    }

    /**
     * 取消呼梯
     */
    public void cancelCallElevator() {
        if (null == mApi || !hasAction("cancelCallElevator")) {
            return;
        }
        mApi.cancelCommand(callElevatorCmdId, Definition.CMD_CONTROL_CALL_ELEVATOR);
    }

    /**
     * 取消控制闸机
     */
    public void cancelCallGate() {
        if (null == mApi || !hasAction("cancelCallGate")) {
            return;
        }
        mApi.cancelCommand(callGateCmdId, Definition.CMD_CONTROL_CALL_GATE);
    }
    /**
     * 电梯开门命令
     */
    public void openElevatorDoor() {
        if (null == mApi || !hasAction("openElevatorDoor")) {
            return;
        }
        mApi.cancelCommand(openElevatorCmdId, Definition.CMD_CONTROL_OPEN_ELEVATOR_DOOR);
        openElevatorCmdId = mApi.openElevatorDoor(mReqId);
        Log.d(TAG, "open elevator door  ,cmdId: " + openElevatorCmdId);
    }

    /**
     * 电梯开门命令
     */
    public void closeElevatorDoor() {
        if (null == mApi || !hasAction("closeElevatorDoor")) {
            return;
        }
        mApi.cancelCommand(closeElevatorCmdId, Definition.CMD_CONTROL_CLOSE_ELEVATOR_DOOR);
        closeElevatorCmdId = mApi.closeElevatorDoor(mReqId);
        Log.d(TAG, "close elevator door  ,cmdId: " + closeElevatorCmdId);
    }

    /**
     * 闸机开门命令
     */
    public void openGateDoor() {
        if (null == mApi || !hasAction("openGateDoor")) {
            return;
        }

        if (mStateData.isMultiGateMode()) {
            openMultiGateDoor();
        } else {
            openSingleGateDoor();
        }
    }
    /**
     * 闸机关门命令
     */
    public void closeGateDoor() {
        if (null == mApi || !hasAction("closeGateDoor")) {
            return;
        }
        mApi.cancelCommand(closeGateCmdId, Definition.CMD_CONTROL_CLOSE_GATE_DOOR);
        if (mStateData.isMultiGateMode()) {
            closeGateCmdId = mApi.closeGateDoor(mReqId, getGateIdJsonString());
        } else {
            closeGateCmdId = mApi.closeGateDoor(mReqId, getRobotInfo(Definition.SYNC_ACTION_GET_MAP_NAME));
        }
        Log.d(TAG, "close closeGateDoor door  ,cmdId: " + closeGateCmdId);
    }
    /**
     * 控制闸机
     */
    public void controlGate() {
        if (null == mApi || !hasAction("controlGate")) {
            return;
        }
        mApi.cancelCommand(callGateCmdId, Definition.CMD_CONTROL_CALL_GATE);
        if (mStateData.isMultiGateMode()) {
            callGateCmdId = mApi.controlGate(mReqId, getGateIdJsonString());
        } else {
            callGateCmdId = mApi.contralGate(mReqId, getRobotInfo(Definition.SYNC_ACTION_GET_MAP_NAME));
        }
        Log.d(TAG, "controlGate ");
    }

    /**
     * 释放闸机
     */
    public void releaseGate() {
        if (null == mApi || !hasAction("releaseGate")) {
            return;
        }
        mApi.cancelCommand(releaseGateCmdId, Definition.CMD_CONTROL_RELEASE_GATE);
        if (mStateData.isMultiGateMode()) {
            releaseGateCmdId = mApi.releaseGate(mReqId, getGateIdJsonString());
        } else {
            releaseGateCmdId = mApi.releaseGate(mReqId, getRobotInfo(Definition.SYNC_ACTION_GET_MAP_NAME));
        }
        Log.d(TAG, "releaseGate ");
    }

    public Pose getPoseByName(String name) {
        try {
            String poseInfo = getRobotInfo(Definition.SYNC_ACTION_GET_SPECIAL_LOCATION, name);
            return mGson.fromJson(poseInfo, Pose.class);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 根据点位类型获取点位信息
     */
    public Pose getPoseByTypeId(int typeId) {
        Log.d(TAG, "getPoseByTypeId: " + typeId);
        try {
            String poseInfo = getRobotInfo(Definition.SYNC_ACTION_GET_SPECIAL_LOCATION_BY_TYPEID, typeId+"");
            Log.d(TAG, "getPoseByTypeId: " + poseInfo);
            return mGson.fromJson(poseInfo, Pose.class);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    protected String registerStatusListener(String type, StatusListener listener) {
        if (!hasAction(type)) {
            return null;
        }
        return mNavigationAdvancedAction.registerStatusListener(type, listener);
    }

    protected final void unregisterStatusListener(String key) {
        if (mNavigationAdvancedAction != null) {
            mNavigationAdvancedAction.unregisterStatusListener(key);
        }
    }

    private boolean hasAction(String method) {
        if (!isAlive()) {
            Log.e(TAG, method + " fail, stats is exit");
            return false;
        }
        if (mNavigationAdvancedAction == null) {
            Log.e(TAG, method + " fail, mAction is null");
            return false;
        }
        if (!mNavigationAdvancedAction.isRunning()) {
            Log.e(TAG, method + " fail, mAction not running");
            return false;
        }
        return true;
    }

    private void openSingleGateDoor() {
        // 做正向还是反向开门，通过闸机名字区分
        String firstName = mStateData.getGateFirstPose().getName();
        String mapName = getRobotInfo(Definition.SYNC_ACTION_GET_MAP_NAME);
        Log.d(TAG, "openGateDoor firstName: " + firstName + "  ,secondName: " + mStateData.getGateSecondPose().getName() + " mapName: " + mapName);
        if (firstName.contains(Definition.GATE_OUTER)) {
            mApi.cancelCommand(openATGateCmdId, Definition.CMD_CONTROL_OPEN_REV_GATE_DOOR);
            openATGateCmdId = mApi.openATGateDoor(mReqId, mapName);
            Log.d(TAG, "rev open openGateDoor  ,cmdId: " + openATGateCmdId);
        } else {
            mApi.cancelCommand(openGateCmdId, Definition.CMD_CONTROL_OPEN_GATE_DOOR);
            openGateCmdId = mApi.openGateDoor(mReqId, mapName);
            Log.d(TAG, "open openGateDoor  ,cmdId: " + openGateCmdId);
        }
    }

    private void openMultiGateDoor() {
        if (mStateData.getCurrentGateFirstPose().getTypeId() == Definition.GATE_OUTER_TYPE) {
            mApi.cancelCommand(openATGateCmdId, Definition.CMD_CONTROL_OPEN_REV_GATE_DOOR);
            openATGateCmdId = mApi.openATGateDoor(mReqId, getGateIdJsonString());
            Log.d(TAG, "open openGateDoor  ,cmdId: " + openATGateCmdId);
        } else {
            mApi.cancelCommand(openGateCmdId, Definition.CMD_CONTROL_OPEN_GATE_DOOR);
            openGateCmdId = mApi.openGateDoor(mReqId, getGateIdJsonString());
            Log.d(TAG, "open openGateDoor  ,gateId: " + getGateIdJsonString());
            Log.d(TAG, "open openGateDoor  ,cmdId: " + openGateCmdId);
        }
    }

    private String getGateIdJsonString() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("gateId", mStateData.getCurrentGateId());
            return jsonObject.toString();
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jsonObject.toString();
    }
}
