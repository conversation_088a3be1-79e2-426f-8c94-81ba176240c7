package com.ainirobot.coreservice.core.person.bean;

import com.ainirobot.coreservice.listener.IActionListener;

public class RequestBean {

    private boolean isByPic;
    private int personId;
    private String name;
    private String picturePath;
    private String userId;
    private long requestTime = System.currentTimeMillis();
    private IActionListener listener;

    public RequestBean(int personId, String userId, String name, IActionListener listener) {
        this.isByPic = false;
        this.personId = personId;
        this.userId = userId;
        this.name = name;
        this.listener = listener;
    }

    public RequestBean(String picturePath, String userId, String name, IActionListener listener) {
        this.isByPic = true;
        this.picturePath = picturePath;
        this.userId = userId;
        this.name = name;
        this.listener = listener;
    }

    public RequestBean(int personId, IActionListener listener) {
        this.isByPic = false;
        this.personId = personId;
        this.listener = listener;
    }

    public RequestBean(String picturePath, IActionListener listener) {
        this.isByPic = true;
        this.picturePath = picturePath;
        this.listener = listener;
    }

    public boolean isByPic() {
        return isByPic;
    }

    public int getPersonId() {
        return personId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUserId() {
        return userId;
    }

    public String getPicturePath() {
        return picturePath;
    }

    public long getRequestTime() {
        return requestTime;
    }

    public IActionListener getListener() {
        return listener;
    }
}
