package com.ainirobot.coreservice.utils;

import android.content.ContentResolver;
import android.provider.Settings;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;

public class BrightUtils {

    private static final String TAG = "BrightUtils:Core";

    /**
     * 设置手动调节模式
     */
    public static void setScreenManualMode() {
        ContentResolver contentResolver = ApplicationWrapper.getContext().getContentResolver();
        try {
            int mode = Settings.System.getInt(contentResolver,
                    Settings.System.SCREEN_BRIGHTNESS_MODE);
            Log.d(TAG, "setScreenManualMode cur mode: " + mode);
            if (mode == Settings.System.SCREEN_BRIGHTNESS_MODE_AUTOMATIC) {
                Settings.System.putInt(contentResolver, Settings.System.SCREEN_BRIGHTNESS_MODE,
                        Settings.System.SCREEN_BRIGHTNESS_MODE_MANUAL);
            }
            Log.d(TAG, "setScreenManualMode modify mode: " + Settings.System.getInt(contentResolver,
                    Settings.System.SCREEN_BRIGHTNESS_MODE));
        } catch (Settings.SettingNotFoundException e) {
            e.printStackTrace();
        }
    }

    /**
     * 设置自动调节模式
     */
    public static void setScreenAutoMode() {
        ContentResolver contentResolver = ApplicationWrapper.getContext().getContentResolver();
        try {
            int mode = Settings.System.getInt(contentResolver,
                    Settings.System.SCREEN_BRIGHTNESS_MODE);
            Log.d(TAG, "setScreenAutoMode cur mode: " + mode);
            if (mode == Settings.System.SCREEN_BRIGHTNESS_MODE_MANUAL) {
                Settings.System.putInt(contentResolver, Settings.System.SCREEN_BRIGHTNESS_MODE,
                        Settings.System.SCREEN_BRIGHTNESS_MODE_AUTOMATIC);
            }
            Log.d(TAG, "setScreenAutoMode modify mode: " + Settings.System.getInt(contentResolver,
                    Settings.System.SCREEN_BRIGHTNESS_MODE));
        } catch (Settings.SettingNotFoundException e) {
            e.printStackTrace();
        }
    }

}
