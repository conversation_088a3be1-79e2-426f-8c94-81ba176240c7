/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.coreservice.server;

import android.os.Bundle;
import android.os.RemoteException;
import android.view.Surface;

import com.ainirobot.coreservice.ISurfaceShareApi;
import com.ainirobot.coreservice.client.surfaceshare.SurfaceShareBean;
import com.ainirobot.coreservice.listener.ISurfaceShareListener;
import com.ainirobot.coreservice.service.CoreService;
import com.ainirobot.coreservice.surfaceshare.SurfaceShareManager;
import com.google.gson.Gson;

public class SurfaceShareServer extends ISurfaceShareApi.Stub {

    private CoreService mCore;
    private Gson mGson;

    public SurfaceShareServer(CoreService core) {
        mCore = core;
        mGson = new Gson();
    }

    @Override
    public int requestImageFrame(Bundle bundle, String param, ISurfaceShareListener listener) {
        SurfaceShareManager manager = mCore.getSurfaceShareManager();
        SurfaceShareBean bean = mGson.fromJson(param, SurfaceShareBean.class);
        return manager.requestImageFrame(bundle, bean, listener);
    }

    @Override
    public int abandonImageFrame(String param) {
        SurfaceShareManager manager = mCore.getSurfaceShareManager();
        SurfaceShareBean bean = mGson.fromJson(param, SurfaceShareBean.class);
        return manager.abandonImageFrame(bean);
    }

    @Override
    public boolean isUsed() throws RemoteException {
        SurfaceShareManager manager = mCore.getSurfaceShareManager();
        return manager.isUsed();
    }
}
