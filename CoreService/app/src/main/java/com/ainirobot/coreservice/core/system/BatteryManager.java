/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.core.system;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.actionbean.BatteryBean;
import com.ainirobot.coreservice.client.actionbean.CommandBean;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.config.ConfigManager;
import com.ainirobot.coreservice.service.CoreService;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

import org.json.JSONException;
import org.json.JSONObject;

public class BatteryManager extends BroadcastReceiver {
    private static final String TAG = "BatteryServer";

    public enum BatteryStatus {
        NORMAL,
        DISABLE_OTA,
        ENABLE_OTA,
        START_CHARGING,
        CHARGING,
        FULL,
        LOW
    }

    private static final String ROBOT_LOW_POWER_LEVEL = "robot_settings_battery_low_level";
    private static final int FULL_LEVEL = 100;
    private static int LOW_LEVEL = ConfigManager.getBatteryConfig().getLowBatteryValue();
    private static final int OTA_LEVEL = ConfigManager.getBatteryConfig().getOtaLowBatteryValue();

    private BatteryStatus mBatteryStatus = BatteryStatus.NORMAL;
    private BatteryStatus mOTAStatus = BatteryStatus.ENABLE_OTA;
    private CoreService mCore;
    private boolean mIsCharging = false;
    private int mLevel;
    private int mBmsTemp;
    // 新增：用于检测充电状态变化的变量
    private boolean mFirstFrame = false; 
    private boolean mSecondFrame = false;

    public BatteryManager(CoreService coreService) {
        mCore = coreService;
        Context context = ApplicationWrapper.getContext();
        IntentFilter batteryFilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
        context.registerReceiver(this, batteryFilter);
        //RobotSettings进程中有修改低电量阈值的入口，此处要监听自定义修改
        initLowLevelListen();
    }

    private void initLowLevelListen() {
        // 从缓存的自定义的Global配置中读取,如果没有配置则默认使用config值
        reInitLowLevel();
        RobotSettings.registerGlobalContentObserver(ApplicationWrapper.getContext(), ROBOT_LOW_POWER_LEVEL,
                new ContentObserver(new Handler(Looper.getMainLooper())) {

                    @Override
                    public void onChange(boolean selfChange, Uri uri) {
                        super.onChange(selfChange, uri);
                        String lastPath = uri.getLastPathSegment();
                        Log.d(TAG, "onChange selfChange : "+ selfChange+ ", String uri : "+ lastPath);
                        if(!selfChange && ROBOT_LOW_POWER_LEVEL.equals(lastPath)){
                            reInitLowLevel();
                        }
                    }
                });
    }

    private void reInitLowLevel() {
        LOW_LEVEL = RobotSettings.getGlobalInt(mCore, ROBOT_LOW_POWER_LEVEL,
                ConfigManager.getBatteryConfig().getLowBatteryValue());
        Log.d(TAG, "reInitLowLevel : " + LOW_LEVEL);
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        int plugged = intent.getIntExtra(android.os.BatteryManager.EXTRA_PLUGGED, -1);
        boolean isCharging = plugged != 0;
        int level = intent.getIntExtra(android.os.BatteryManager.EXTRA_LEVEL, 0);
        int bmsTemp = intent.getIntExtra(android.os.BatteryManager.EXTRA_TEMPERATURE, 20);
        Log.d(TAG, "BatteryStatusReceiver level : " + level + ", charging : " + isCharging+ ", bmsTemp : " + bmsTemp);
        
        // 检测充电状态变化
        checkChargingStateChange(isCharging);
        
        batteryUpdate(level, bmsTemp, isCharging);
        BatteryBean batteryBean = new BatteryBean();
        batteryBean.setLevel(level);
        batteryBean.setBmsTemp(bmsTemp);
        batteryBean.setCharging(isCharging);
        batteryBean.setLow(level < LOW_LEVEL);
        Gson gson = new Gson();
        mCore.getStatusManager().handleStatus(RobotOS.SYSTEM_SERVICE, Definition.STATUS_BATTERY,
                gson.toJson(batteryBean));
        mCore.getRobotSettingManager().setRobotSetting(Definition.ROBOT_SETTINGS_BATTERY_INFO,
                String.valueOf(level));
    }

    private void batteryUpdate(int level, int bmsTemp, boolean isCharging) {
        this.mIsCharging = isCharging;
        mLevel = level;
        mBmsTemp = bmsTemp;

        Log.d(TAG, "onBatteryInfoUpdate level : " + level + ", charging : " + isCharging+ ", mBmsTemp : " + mBmsTemp);
        if (level < OTA_LEVEL) {
            updateOTAStatus(BatteryStatus.DISABLE_OTA, level, bmsTemp);
        } else {
            updateOTAStatus(BatteryStatus.ENABLE_OTA, level, bmsTemp);
        }

        if (isCharging) {
            updateChargingLevel(level);
            if (level == FULL_LEVEL) {
                updateStatus(BatteryStatus.FULL, level, bmsTemp);
            } else {
                updateStatus(BatteryStatus.START_CHARGING, level, bmsTemp);
            }
        } else {
            if (level <= LOW_LEVEL) {
                updateStatus(BatteryStatus.LOW, level, bmsTemp);
            } else {
                updateStatus(BatteryStatus.NORMAL, level, bmsTemp);
            }
        }
    }

    private synchronized void updateStatus(BatteryStatus batteryStatus, int level, int bmsTemp) {
        Log.d(TAG, "Update BatteryStatus : " + batteryStatus.name()
                + "  current BatteryStatus : " + mBatteryStatus.name() + ", level : " + level+ ", bmsTemp : " + bmsTemp);
        if (mBatteryStatus == batteryStatus) {
            return;
        }
        mBatteryStatus = batteryStatus;
        mCore.getSystemManager().updateBattery(mBatteryStatus, level, bmsTemp);
    }

    private synchronized void updateChargingLevel(int level) {
        mCore.getSystemManager().updateBattery(level == 100 ? BatteryStatus.FULL : BatteryStatus.CHARGING, level, mBmsTemp);
    }

    public BatteryStatus getBatteryStatus() {
        return mBatteryStatus;
    }

    public void setBatteryStatus(BatteryStatus status) {
        mBatteryStatus = status;
    }

    private synchronized void updateOTAStatus(BatteryStatus status, int level, int bmsTemp) {
        if (mOTAStatus == status) {
            return;
        }
        mOTAStatus = status;
        mCore.getSystemManager().updateBattery(mOTAStatus, level, bmsTemp);
        Log.d(TAG, "Update ota status : " + status.name() + " current : " + mOTAStatus.name());
    }

    public boolean isCharging() {
        return mIsCharging;
    }

    public int getLevel() {
        return mLevel;
    }

    private void checkChargingStateChange(boolean isCharging) {
        // 检查是否刚插上充电器且下一帧确认仍在充电
        if (mSecondFrame && isCharging) {
            JSONObject params = new JSONObject();
            try {
                Log.d(TAG, "checkChargingStateChange: start");
                params.put("enable", 1);
                CommandBean bean = new CommandBean(Definition.CMD_SET_POWER_MOTOR, params.toString(), false);
                mCore.getSystemServer().startAction(-1, Definition.ACTION_COMMON, new Gson().toJson(bean), null);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            mSecondFrame = false; // 重置状态，避免重复打印
        }
        
        // 检查充电状态是否发生变化
        if (mFirstFrame != isCharging) {
            Log.d(TAG, "充电状态变化: " + mFirstFrame + " -> " + isCharging);
            
            // 如果从未充电变为充电状态，标记为刚插上
            if (!mFirstFrame && isCharging) {
                mSecondFrame = true;
            } else {
                mSecondFrame = false;
            }
            
            mFirstFrame = isCharging;
        }
    }
}
