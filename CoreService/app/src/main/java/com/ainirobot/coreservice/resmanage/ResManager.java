/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.resmanage;

import android.os.Handler;
import android.os.Message;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.action.AbstractAction;
import com.ainirobot.coreservice.bean.Command;
import com.ainirobot.coreservice.core.CoreStateMachine;
import com.ainirobot.coreservice.core.InternalDef;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.service.CoreService;

import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class ResManager {
    private static final String TAG = ResManager.class.getSimpleName();

    private ConcurrentHashMap<Integer, AssignedRes> mAssignedMap = new ConcurrentHashMap<>();

    private CoreService mCoreService;
    private AbstractResProxy[] mCompetitiveAllRes = new AbstractResProxy[InternalDef.RES_MAX_ID];
    private AbstractResProxy[] mAssignedCompetitiveRes = new AbstractResProxy[InternalDef.RES_MAX_ID];
    private AbstractResProxy mShareRes;

    public ResManager(CoreService coreService) {
        mCoreService = coreService;
        init();
    }

    CoreService getCoreService() {
        return mCoreService;
    }

    private int getResIdFromName(String name) {
        int id = InternalDef.RES_INVALID_ID;
        for (int i = 0; i < InternalDef.RES_NAMES.length; i++) {
            if (InternalDef.RES_NAMES[i].equals(name)) {
                id = i;
                break;
            }
        }
        return id;
    }

    private void addCmd(int resId, String cmd) {
        if (resId == InternalDef.RES_INVALID_ID) {
            mShareRes.addSupportCmd(cmd);
        } else {
            mCompetitiveAllRes[resId].addSupportCmd(cmd);
        }
    }

    private void init() {
        for (int i = 0; i < InternalDef.RES_MAX_ID; i++) {
            mCompetitiveAllRes[i] = createResProxy(i);
        }

        mShareRes = new ShareRes(this);

        Iterator<Command> iterator = mCoreService.getCommandManager().getCmdIterator();
        Command command;
        while (iterator.hasNext()) {
            command = iterator.next();
            addCmd(InternalDef.RES_INVALID_ID, command.getType());
//            if (command.resources == null || command.resources.length == 0) {
//                addCmd(InternalDef.RES_INVALID_ID, command.type);
//            } else {
//                addCmd(getResIdFromName(command.resources[0]), command.type);
//            }
        }
    }

    public AssignedRes requestRes(AbstractAction action, String[] resource) {
        int length = resource == null ? 0 : resource.length;
        int[] resIds = new int[length];
        for (int i = 0; i < length; i++) {
            resIds[i] = getResIdFromName(resource[i]);
        }
        return requestRes(action, resIds);
    }

    public AssignedRes requestRes(AbstractAction action, int[] resIds) {
        AssignedRes result = null;
        if (action != null && resIds != null) {
            result = mAssignedMap.get(action.getActionId());
            if (result == null) {
                AbstractResProxy res;
                result = new AssignedRes(this, action);
                boolean assignFailed = false;
                for (int resId : resIds) {
                    if (resId > InternalDef.RES_INVALID_ID && resId < InternalDef.RES_MAX_ID) {
                        synchronized (this) {
                            res = mAssignedCompetitiveRes[resId];
                            if (res == null) {
                                res = mAssignedCompetitiveRes[resId] = mCompetitiveAllRes[resId];
                            } else {
                                assignFailed = true;
                                break;
                            }
                        }
                        result.addRes(res);
                    }
                }

                if (assignFailed) {
                    result.release();
                    result = null;
                } else {
                    result.addRes(mShareRes);
                    mAssignedMap.put(action.getActionId(), result);
                }
            }
        }
        return result;
    }

    private AbstractResProxy createResProxy(int resId) {
        AbstractResProxy res = null;
        switch (resId) {
            case InternalDef.RES_HEAD_ID:
                res = new Head(this, resId);
                break;
            case InternalDef.RES_HEAD_CAMERA_ID:
                res = new HeadCamera(this, resId);
                break;
            case InternalDef.RES_CHASSIS_ID:
                res = new Chassis(this, resId);
                break;
            case InternalDef.RES_RADAR_ID:
                res = new Radar(this, resId);
                break;
            case InternalDef.RES_ELEVATOR_ID:
                res = new Elevator(this, resId);
            default:
                try {
                    throw new RemoteException("Don't support Res : " + resId);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
                break;
        }
        return res;
    }

    public List<String> getUnavailableRes(String[] resource) {
        HashMap<Integer, String> resMap = new HashMap<>();
        List<String> unavailableRes = new ArrayList<>();
        if (resource == null) {
            return null;
        }
        for (String name : resource) {
            resMap.put(getResIdFromName(name), name);
        }
        for (Integer resId : resMap.keySet()) {
            AbstractResProxy res = mCompetitiveAllRes[resId];
            if (!res.isAvailable()) {
                unavailableRes.add(resMap.get(resId));
                Log.d(TAG, "res[" + resMap.get(resId) + "] is not available");
            }
        }
        return unavailableRes;
    }

    void finish(AbstractResProxy res) {
        if (res != null && res.getResId() != InternalDef.RES_INVALID_ID) {
            mAssignedCompetitiveRes[res.getResId()] = null;
        }
    }

    void recycleAssignedRes(AssignedRes res) {
        mAssignedMap.remove(res.getAssignedId());
    }

    int sendCommand(int reqId, String command, String params, String language, boolean isContinues,
                    LocalApi.OnCmdResponse response) {
//        RequestManager.ReqAction req = mCoreService.getRequestManager().getReqAction(reqId);
//        if (req == null) {
//            if (reqId == Definition.DEBUG_REQ_ID) {
//                req = mCoreService.getRequestManager().newReqAction();
//                req.reqId = Definition.DEBUG_REQ_ID;
//                req.reqModule = mCoreService.getModuleManager()
//                        .getDefaultModule(InternalDef.MODULE_PRIORITY_HIGH);
//                mCoreService.getRequestManager().addReqAction(req);
//            } else {
//                return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
//            }
//        }

        CoreStateMachine coreStateMachine = mCoreService.getCoreStateMachine();
        int cmdId = coreStateMachine.addCommand(reqId, command, params, language, isContinues, response);
//        RobotLog.d("Receive command from ModuleApp : cmdId="
//                + cmdId + "  cmdType=" + command + " cmdParam=" + params);
        if (cmdId < 0) {
            return cmdId;
        }

        Handler coreHandler = mCoreService.getCoreHandler();
        Message msg = coreHandler.obtainMessage(InternalDef.MSG_CORE_ADD_COMMAND_ACTION);
        coreHandler.sendMessage(msg);

        return cmdId;
    }

    boolean cancelCommand(int cmdId, boolean isForceStop) {
        CoreStateMachine coreStateMachine = mCoreService.getCoreStateMachine();
        boolean result = coreStateMachine.cancelCommand(cmdId, isForceStop);

        Handler coreHandler = mCoreService.getCoreHandler();
        Message msg = coreHandler.obtainMessage(InternalDef.MSG_CORE_ADD_COMMAND_ACTION);
        coreHandler.sendMessage(msg);
        return result;
    }

    public void dump(String prefix, PrintWriter writer, String args) {
        writer.println(TAG + " dump " + args);
        switch (args) {
            case "all":
                for (AbstractResProxy res : mCompetitiveAllRes) {
                    if (res == null) {
                        writer.println(TAG + " res null");
                        continue;
                    }
                    if (res.getResId() < 0) {
                        writer.println(TAG + " shared res");
                        continue;
                    }
                    writer.println(TAG + " res[" + InternalDef.RES_NAMES[res.getResId()] + "] isAvailable:" + res.isAvailable());
                }
                break;
            case "assigned":
                for (AbstractResProxy res : mAssignedCompetitiveRes) {
                    if (res == null) {
                        writer.println(TAG + " res null");
                        continue;
                    }
                    if (res.getResId() < 0) {
                        writer.println(TAG + " shared res");
                        continue;
                    }
                    writer.println(TAG + " res[" + InternalDef.RES_NAMES[res.getResId()] + "] isAvailable:" + res.isAvailable());
                }
                break;
            case "map":
                for (ConcurrentHashMap.Entry<Integer, AssignedRes> entry : mAssignedMap.entrySet()) {
                    writer.println(TAG + " actionId = " + entry.getKey());
                    entry.getValue().dump(TAG, writer, args);
                }
                break;
        }
    }
}
