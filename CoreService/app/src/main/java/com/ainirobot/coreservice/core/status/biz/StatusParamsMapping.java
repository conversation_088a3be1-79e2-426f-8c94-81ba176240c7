package com.ainirobot.coreservice.core.status.biz;

import com.ainirobot.coreservice.client.Definition;

import org.json.JSONException;
import org.json.JSONObject;

public class StatusParamsMapping {

    public static String getCameraParams() {
        JSONObject param = new JSONObject();
        try {
            param.put(Definition.JSON_HEAD_ID, "");
            param.put(Definition.JSON_HEAD_COUNT, "");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return param.toString();
    }
}
