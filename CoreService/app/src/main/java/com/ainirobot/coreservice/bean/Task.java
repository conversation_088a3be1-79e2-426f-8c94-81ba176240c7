package com.ainirobot.coreservice.bean;

import android.os.Parcel;
import android.os.Parcelable;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Arrays;
import java.util.Objects;

public class Task implements Cloneable, Parcelable {
    public static final int TASK_STATUS_START = 1;
    public static final int TASK_STATUS_CANCEL = 2;
    public static final int TASK_STATUS_FAIL = 3;
    public static final int TASK_STATUS_FINISH = 4;
    public static final int TASK_MODE_BACKGROUND = 2;
    public static final int TASK_MODE_FOREGROUND = 1;

    private int status = TASK_STATUS_START;
    private int mode = TASK_MODE_FOREGROUND;
    private String type = "";
    private String name = "";
    private String taskId = "";
    private TaskEvent[] subTaskList = null;
    private TaskEvent taskEvent = null;

    public Task(){
        super();
    }

    protected Task(Parcel in) {
        status = in.readInt();
        mode = in.readInt();
        type = in.readString();
        name = in.readString();
        taskId = in.readString();
        subTaskList = in.createTypedArray(TaskEvent.CREATOR);
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(status);
        dest.writeInt(mode);
        dest.writeString(type);
        dest.writeString(name);
        dest.writeString(taskId);
        dest.writeTypedArray(subTaskList, 0);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public void readFromParcel(Parcel reply) {
        status = reply.readInt();
        mode = reply.readInt();
        type = reply.readString();
        name = reply.readString();
        taskId = reply.readString();
        subTaskList = reply.createTypedArray(TaskEvent.CREATOR);
    }

    public static final Creator<Task> CREATOR = new Creator<Task>() {
        @Override
        public Task createFromParcel(Parcel in) {
            return new Task(in);
        }

        @Override
        public Task[] newArray(int size) {
            return new Task[size];
        }
    };

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getMode() {
        return mode;
    }

    public void setMode(int mode) {
        this.mode = mode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public TaskEvent[] getSubTaskList() {
        return subTaskList;
    }

    public void setSubTaskList(TaskEvent[] subTaskList) {
        this.subTaskList = subTaskList;
    }

    public TaskEvent getTaskEvent() {
        return taskEvent;
    }

    public void setTaskEvent(TaskEvent taskEvent) {
        this.taskEvent = taskEvent;
    }

    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Task task = (Task) o;
        return status == task.status &&
                mode == task.mode &&
                Objects.equals(type, task.type) &&
                Objects.equals(name, task.name) &&
                Objects.equals(taskId, task.taskId) &&
                Arrays.equals(subTaskList, task.subTaskList);
    }

    public String toJson(){
        JSONObject object = new JSONObject();
        String task_status = "";
        switch (status){
            case TASK_STATUS_CANCEL:
                task_status = "task_status_cancel";
                break;

            case TASK_STATUS_FAIL:
                task_status = "task_status_fail";
                break;

            case TASK_STATUS_FINISH:
                task_status = "task_status_finish";
                break;

            case TASK_STATUS_START:
                task_status = "task_status_start";
                break;

            default:
                task_status = "task_status_start";
                break;
        }
        try {
            object.put("task_status", task_status);
            object.put("task_id", taskId);
            object.put("task_name", name);
            object.put("task_type", type);
            object.put("task_mode", mode == TASK_MODE_BACKGROUND ? "mode_background" : "mode_foreground");
            if (subTaskList != null) {
                JSONArray subTaskArray = new JSONArray();
                for (TaskEvent event : subTaskList) {
                    subTaskArray.put(event);
                }
                object.put("sub_task_list", subTaskArray);
            }
            object.put("event_info", taskEvent == null ? "" : taskEvent.toJson());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return object.toString();
    }

    @Override
    public String toString() {
        return toJson();
    }
}
