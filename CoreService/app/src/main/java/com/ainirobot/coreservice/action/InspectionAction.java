package com.ainirobot.coreservice.action;

import android.os.RemoteException;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.IHWService;
import com.ainirobot.coreservice.R;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.actionbean.InspectActionBean;
import com.ainirobot.coreservice.client.actionbean.InspectionResult;
import com.ainirobot.coreservice.client.actionbean.ServiceInspectResultBean;
import com.ainirobot.coreservice.client.techreport.CallChainReport;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.core.external.ExternalService;
import com.ainirobot.coreservice.core.external.ExternalServiceManager;
import com.ainirobot.coreservice.inspection.AbstractInspectCallback;
import com.ainirobot.coreservice.inspection.InspectionStatusManager;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import static com.ainirobot.coreservice.client.Definition.ACTION_INSPECTION;
import static com.ainirobot.coreservice.client.Definition.HW_STATUS_IDLE;

/**
 * inspection action
 *
 * @version V1.0.0
 * @date 2019/10/11 18:40
 */
public class InspectionAction extends AbstractAction<InspectActionBean> {

    private static final String TAG = "InspectionAction";
    private static final long DEFAULT_INSPECTION_TIMEOUT = 4 * 60 * 1000;
    private long inspectionTimeout = DEFAULT_INSPECTION_TIMEOUT;
    private static final int INSPECT_CODE_SUCCESS = 1;
    private static final int INSPECT_CODE_FAIL = -1;
    private static final int INSPECT_CODE_TIMEOUT = -2;
    private CountDownLatch latch;
    private InspectionResult result;
    private ExecutorService inspectionPool;
    private ScheduledExecutorService checkServiceStatusPool;
    private ConcurrentHashMap<String, Future> futureMap;
    private AtomicBoolean inspectResult;
    private AtomicBoolean otaInspectResult;
    private List<String> mServicesList = new ArrayList<>();

    private CallChainReport callChain = new CallChainReport(ApplicationWrapper.getContext(), this.getClass().getName(),
            "inspect");


    public InspectionAction(ActionManager manager, String[] resList) {
        super(manager, ACTION_INSPECTION, resList);
    }

    @Override
    protected boolean startAction(InspectActionBean parameter, LocalApi api) {
        Log.i(TAG, "startAction: InspectActionBean=" + parameter);
        long timeout = parameter.getTimeOut();
        inspectionTimeout = timeout > 0 ? timeout : inspectionTimeout;

        onStatusUpdate(Definition.INSPECTION_ACTION_START_STATUS, Definition.INSPECTION_ACTION_START_MSG);
        ExternalServiceManager manager = mManager.getServiceManager();
        final List<ExternalService> services = manager.getInspectService();
        Log.i(TAG, "startInspect: inspect service config:" + services);
        int inspectServiceNum = services.size();
        initConfig(inspectServiceNum);
        inspectionPool.execute(new Runnable() {
            @Override
            public void run() {
                startInspect(services);
            }
        });
        return true;
    }

    /**
     * start inspect
     * 1.check service status
     * 2.count down latch await service inspect result
     * 3.format service inspect result
     * 4.return inspect result
     * 5.release source(thread pool、latch and so on)
     *
     * @param services inspect services
     */
    private void startInspect(List<ExternalService> services) {
        StringBuilder sb = new StringBuilder();
        if (mServicesList.size() > 0) {
            mServicesList.clear();
        }
        for (final ExternalService service : services) {
            Log.d(TAG, "startInspect service: " + service.toString());
            sb.append(service.getName()).append("; ");
            if (inspectionPool == null || inspectionPool.isShutdown()){
                Log.i(TAG, "inspectionPool is gone, return!");
                return;
            }
            mServicesList.add(service.getPackageName());
            inspectionPool.execute(new Runnable() {
                @Override
                public void run() {
                    checkServiceState(service);
                }
            });
        }
        callChain.invokeNodeMethod("startInspect");
        callChain.invokeNodeInfo(sb.toString(), "start inspect all service, size: "+services.size());
        callChain.report();
        boolean isFinish = false;
        try {
            isFinish = latch.await(inspectionTimeout, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            Log.w(TAG, "startInspect: inspect await intercepted");
        } finally {
            formatResult(isFinish);
        }
    }

    /**
     * config inspect thread pool and countDownLatch
     *
     * @param inspectServiceNum inspect service num
     */
    private void initConfig(int inspectServiceNum) {
        AtomicInteger inspectThreadCtl = new AtomicInteger(0);
        AtomicInteger checkHwStatusThreadCtl = new AtomicInteger(0);
        inspectResult = new AtomicBoolean(true);
        otaInspectResult = new AtomicBoolean(true);
        futureMap = new ConcurrentHashMap<>();
        result = new InspectionResult();
        latch = new CountDownLatch(inspectServiceNum);
        inspectionPool = Executors.newFixedThreadPool(inspectServiceNum + 1
                , createThreadFactory("inspection_thread_", inspectThreadCtl));
        checkServiceStatusPool = Executors.newScheduledThreadPool(inspectServiceNum + 1
                , createThreadFactory("check_hw_status_thread_", checkHwStatusThreadCtl));
    }

    private ThreadFactory createThreadFactory(final String threadName, final AtomicInteger ctl) {
        return new ThreadFactory() {
            @Override
            public Thread newThread(@NonNull Runnable r) {
                return new Thread(r, threadName + ctl);
            }
        };
    }

    /**
     * check service state, according to service
     * whether nor register hw ware callback
     *
     * @param service inspect service
     */
    private void checkServiceState(ExternalService service) {
        if (!isServiceReady(service)) {
            Log.i(TAG, "serviceInspect: waiting service ready: " + service);
            checkHwStatus(service);
            return;
        }
        serviceInspect(service);
    }

    /**
     * service ready ,start invoke service inspect
     *
     * @param service inspect service
     */
    private void serviceInspect(ExternalService service) {
        Log.i(TAG, "serviceInspect: service ready start  inspect:" + service.getPackageName());
        final CallChainReport.NodeMethod method = callChain.invokeNodeMethod("serviceInspect");
        IHWService ihwCallback = service.getCallback();
        if (ihwCallback == null) {
            Log.e(TAG, "serviceInspect: ihwCallback is null");
            return;
        }

        synchronized (this) {
            method.setType(CallChainReport.NodeMethod.TYPE_FUNCTION);
            callChain.invokeNodeInfo(service.getPackageName(),service.getName()+" inspect start");
            callChain.report();
        }
        try {
            ihwCallback.startInspect(new AbstractInspectCallback(service.getPackageName()) {
                @Override
                protected void onFinish(String service, boolean inspectResult, String info) {
                    Log.i(TAG, "onFinish: service:" + service + " inspectResult:" + inspectResult + " info:" + info);
                    synchronized (this) {
                        method.setType(CallChainReport.NodeMethod.TYPE_API_CALLBACK);
                        callChain.invokeNodeInfo(service+" ++ result:"+inspectResult+" ++ info:"+info,
                                service+" inspect finish");
                        callChain.report();
                    }
                    InspectionAction.this.inspectResult.compareAndSet(true, inspectResult);
                    if (!TextUtils.isEmpty(info)) {
                        Gson gson = new Gson();
                        ServiceInspectResultBean resultBean = gson.fromJson(info, ServiceInspectResultBean.class);
                        otaInspectResult.compareAndSet(true, resultBean.isOtaResult());
                        int step = resultBean.getStep();
                        InspectionResult.StepBean stepBean = new InspectionResult.StepBean(service, step);
                        result.getStep().add(stepBean);
                        formatFailItem(service, resultBean.getFail());
                        formatPassItem(service, resultBean.getPass());
                    }
                    mServicesList.remove(service);
                    latch.countDown();
                }
            });
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * format service inspect result
     *
     * @param service inspect service name
     * @param pass    service inspect pass item list
     */
    private void formatPassItem(String service, List<ServiceInspectResultBean.PassBean> pass) {
        Log.i(TAG, "formatPassItem: service:" + service + " pass:" + pass);
        if (pass == null || pass.isEmpty()) {
            return;
        }
        for (ServiceInspectResultBean.PassBean item : pass) {
            InspectionResult.PassBean passBean = new InspectionResult.PassBean();
            passBean.setName(item.getName());
            passBean.setService(service);
            result.getPass().add(passBean);
        }

    }

    /**
     * format service inspect result
     *
     * @param service inspect service name
     * @param fail    service inspect fail item list
     */
    private void formatFailItem(String service, List<ServiceInspectResultBean.FailBean> fail) {
        Log.i(TAG, "formatFailItem: service:" + service + " fail:" + fail);
        if (fail == null || fail.isEmpty()) {
            return;
        }
        for (ServiceInspectResultBean.FailBean item : fail) {
            InspectionResult.FailBean failBean = new InspectionResult.FailBean();
            failBean.setName(item.getName());
            failBean.setService(service);
            failBean.setCode(item.getCode());
            failBean.setErrorMsg(item.getErrorMsg());
            failBean.setIgnore(item.isIgnore());
            failBean.setErrorMsg(item.getAllErrorMsg());
            result.getFail().add(failBean);
        }
    }


    /**
     * timer to check service status
     *
     * @param service inspect service config
     */
    private void checkHwStatus(final ExternalService service) {
        if (checkServiceStatusPool == null || checkServiceStatusPool.isShutdown()){
            Log.i(TAG, "checkServiceStatusPool is gone");
            return;
        }
        final Future future = checkServiceStatusPool.scheduleWithFixedDelay(new Runnable() {
            @Override
            public void run() {
                String serviceName = service.getPackageName();
                Future future = futureMap.get(serviceName);
                if (isServiceReady(service) && !future.isCancelled()) {
                    Log.i(TAG, "checkHwStatus: service ready: " + service.getPackageName());
                    serviceInspect(service);
                    future.cancel(true);
                }
            }
        }, 2, 2, TimeUnit.SECONDS);

        futureMap.put(service.getPackageName(), future);
    }


    /**
     * check service status whether nor ready
     *
     * @param service inspect service config
     * @return true service ready,otherwise false
     */
    private boolean isServiceReady(ExternalService service) {
        try {
            int status = service.getStatus();
            Log.i(TAG, "isServiceReady: service:" + service.getPackageName() + " status:" + status);
            return status == HW_STATUS_IDLE;
        } catch (RemoteException e) {
            Log.w(TAG, "isServiceReady: " + e.getMessage());
        }
        return false;
    }

    private void formatResult(boolean isFinish) {
        Log.i(TAG, "formatResult: is isFinish:" + isFinish);
        result.setResult(isFinish ? checkResult() : INSPECT_CODE_TIMEOUT);
        result.setOtaResult(otaInspectResult.get());
        Log.i(TAG, "formatResult: service inspect result:" + result);

        if (!isFinish && mServicesList.size() > 0) {
            List<InspectionResult.FailBean> failBeanList = new ArrayList<>();
            for (String serviceName: mServicesList) {
                InspectionResult.FailBean failBean = new InspectionResult.FailBean(serviceName, INSPECT_CODE_TIMEOUT,
                        serviceName, ApplicationWrapper.getContext().getString(R.string.inspect_time_out)
                        + serviceName + ApplicationWrapper.getContext().getString(R.string.inspect_no_response));
                failBeanList.add(failBean);
            }
            result.setFail(failBeanList);
            mServicesList.clear();
        }
        String inspectInfo = new Gson().toJson(result);
        Log.i(TAG, "formatResult: inspectInfo:" + inspectInfo);
        if (!isRunning()) {
            Log.w(TAG, "formatResult: inspect terminated");
            return;
        }
        onResult(Definition.RESULT_OK, inspectInfo);
        InspectionStatusManager.getmInspectionStatusManager().setInspectionResult(result);
        mManager.setRobotSetting(SettingsUtil.ROBOT_SETTING_INSPECTION_RESULT, inspectInfo);
        Log.i(TAG, "formatResult: inspect finish");
    }

    private int checkResult() {
        return inspectResult.get() ? INSPECT_CODE_SUCCESS : INSPECT_CODE_FAIL;
    }


    @Override
    protected boolean stopAction(boolean isResetHW) {
        cancelCheckPool();
        releaseLatch();
        showDownThreadPool(inspectionPool);
        showDownThreadPool(checkServiceStatusPool);
        return true;
    }

    private void releaseLatch() {
        long waitCount = latch.getCount();
        Log.i(TAG, "releaseLatch: waitCount=" + waitCount);
        for (int i = 0; i < waitCount; i++) {
            latch.countDown();
        }
    }

    private void showDownThreadPool(ExecutorService pool) {
        if (pool.isShutdown()) {
            return;
        }
        pool.shutdown();
    }

    private void cancelCheckPool() {
        for (Future future : futureMap.values()) {
            if (future == null || future.isCancelled()) {
                continue;
            }
            boolean cancelResult = future.cancel(true);
            Log.i(TAG, "cancelCheckPool: cancelResult=" + cancelResult);
        }

    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        return false;
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return null;
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return null;
    }

    @Override
    protected boolean verifyParam(InspectActionBean param) {
        Log.i(TAG, "verifyParam: param=" + param);
        return true;
    }

}
