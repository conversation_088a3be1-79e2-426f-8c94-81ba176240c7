package com.ainirobot.coreservice.action;

import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.CommandBean;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.listener.IActionListener;

import java.util.ArrayList;

public class HeadResetAction extends AbstractAction<CommandBean> {
    private static final String TAG = "HeadResetAction";

    protected HeadResetAction(ActionManager manager, int id, String[] resList) {
        super(manager, id, resList);
    }

    @Override
    protected boolean startAction(CommandBean parameter, LocalApi api) {
        Log.d(TAG, "startAction: " + parameter.toString());
        return mManager.getCoreService().getHeadMoveManager().resetHead(new ActionListenerProxy() {
            @Override
            public void onResult(int status, String responseString) {
                Log.d(TAG, "onResult: " + status + " " + responseString);
                HeadResetAction.this.onResult(status, responseString);
            }

            @Override
            public void onError(int errorCode, String errorString) {
                Log.d(TAG, "onError: " + errorCode + " " + errorString);
                HeadResetAction.this.onError(errorCode, errorString);
            }

            @Override
            public void onStatusUpdate(int status, String data) {
                Log.d(TAG, "onStatusUpdate: " + status + " " + data);
                HeadResetAction.this.onStatusUpdate(status, data);
            }
        }) == Definition.CMD_SEND_SUCCESS;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        return true;
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return null;
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return null;
    }

    @Override
    protected boolean verifyParam(CommandBean param) {
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        return true;
    }
}
