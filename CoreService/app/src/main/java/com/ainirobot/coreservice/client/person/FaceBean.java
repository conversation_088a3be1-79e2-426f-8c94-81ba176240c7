package com.ainirobot.coreservice.client.person;

import com.google.gson.Gson;
import com.google.gson.annotations.Expose;

import org.json.JSONObject;

public class FaceBean {

    private String faceId;
    private String name;
    @Expose(deserialize = false)
    private String feature;
    private String pictureUri;
    private String userId;
    private long registerTime;
    private boolean isOnlyLocal;
    private boolean isDelete;

    public FaceBean() {
    }

    public FaceBean(JSONObject jsonObject) {
        this.faceId = jsonObject.optString("user_outer_id");
        this.name = jsonObject.optString("user_name");
    }

    public FaceBean(String faceId, String feature) {
        this.faceId = faceId;
        this.feature = feature;
    }

    public FaceBean(String faceId, String name, String feature, String pictureUri,
                    boolean isOnlyLocal) {
        this.faceId = faceId;
        this.name = name;
        this.feature = feature;
        this.pictureUri = pictureUri;
        this.isOnlyLocal = isOnlyLocal;
    }

    public String getFaceId() {
        return faceId;
    }

    public void setFaceId(String faceId) {
        this.faceId = faceId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFeature() {
        return feature;
    }

    public void setFeature(String feature) {
        this.feature = feature;
    }

    public String getPictureUri() {
        return pictureUri;
    }

    public void setPictureUri(String pictureUri) {
        this.pictureUri = pictureUri;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public long getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(long registerTime) {
        this.registerTime = registerTime;
    }

    public boolean isOnlyLocal() {
        return isOnlyLocal;
    }

    public void setOnlyLocal(boolean onlyLocal) {
        isOnlyLocal = onlyLocal;
    }

    public boolean isDelete() {
        return isDelete;
    }

    public void setDelete(boolean delete) {
        isDelete = delete;
    }

    @Override
    public String toString() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }
}
