package com.ainirobot.coreservice.bi.report;

import com.google.gson.JsonObject;

public class ExternalServiceReporter extends SystemInformationReporter{
    public ExternalServiceReporter() {
        super("ExternalService", "ExternalService.java");
    }

    public void reportStart(final String packageName) {
        JsonObject params = new JsonObject();
        params.addProperty("packageName", packageName);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("start")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportAddCommand(final String commands) {
        JsonObject params = new JsonObject();
        params.addProperty("commands", commands);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("addCommand")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportSetConfig(final String serviceConfig) {
        JsonObject params = new JsonObject();
        params.addProperty("serviceConfig", serviceConfig);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("setConfig")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportStartStatusSocket(final String type, final int socketPort,
        final boolean hasStart) {
        JsonObject params = new JsonObject();
        params.addProperty("type", type);
        params.addProperty("socketPort", socketPort);

        JsonObject results = new JsonObject();
        params.addProperty("hasStart", hasStart);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("startStatusSocket")
            .addNodeParams(params.toString())
            .addNodeResult(results.toString())
            .reportV();
    }

    public void reportCloseStatusSocket(final String type, final int socketPort,
        final boolean hasClose) {
        JsonObject params = new JsonObject();
        params.addProperty("type", type);
        params.addProperty("socketPort", socketPort);

        JsonObject results = new JsonObject();
        params.addProperty("hasClose", hasClose);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("closeStatusSocket")
            .addNodeParams(params.toString())
            .addNodeResult(results.toString())
            .reportV();
    }

    public void reportExeSyncCommand(final String cmdType, final String param) {
        JsonObject params = new JsonObject();
        params.addProperty("cmdType", cmdType);
        params.addProperty("param", param);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("exeSyncCommand")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportExeAsyncCommand(final String cmdType, final String param) {
        JsonObject params = new JsonObject();
        params.addProperty("cmdType", cmdType);
        params.addProperty("param", param);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("exeAsyncCommand")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportReset() {
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("reset")
            .reportV();
    }
}
