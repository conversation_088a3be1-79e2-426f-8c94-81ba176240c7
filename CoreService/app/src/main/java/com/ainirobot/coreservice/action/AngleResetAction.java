/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.action;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.StopCommandList.IStopCommandChecker;
import com.ainirobot.coreservice.action.StopCommandList.StopCommand;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.actionbean.AngleResetBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.robotlog.RobotLog;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;

public class AngleResetAction extends AbstractAction<AngleResetBean> {
    private static final String TAG = "AngleResetAction";

    private enum ResetState {
        IDLE, PREPARE, GET_ESTIMATE_STATE, GET_HEAD_CURRENT_ANGLE, GET_CURRENT_POSITION, RESET_TURNLEFT_FIRST, RESET_TURNLEFT_SECOND, RESET_CHASSIS_THETA, STOPPED
    }

    private int mReqId;
    private double mLinearSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED;
    private double mAngularSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED;
    private static final int DEFAULT_HEAD_VERTICAL_ANGULAR_SPEED = 20;
    private static final int DEFAULT_HEAD_HORIZONTAL_ANGULAR_SPEED = 70;
    private Pose mDestPose;
    private String mDestination;
    private Gson mGson;
    private boolean mIsFlag = false;
    private String mPoseListener;
    private ResetState mStatus;
    private int mCurHeadAngle = 0;
    private Pose mTempPose = null;
    private double mTempTargetTheta = 0;

    protected AngleResetAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_ANGLE_RESET, resList);
        mGson = new Gson();
    }

    @Override
    protected boolean startAction(AngleResetBean parameter, LocalApi api) {
        if (verifyParam(parameter)) {
            prepareNavigation(parameter);
            return true;
        }
        return false;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        RobotLog.d("Stop navigation action");
        mStatus = ResetState.STOPPED;
        unregisterStatusListener(mPoseListener);
        mIsFlag = false;
        mDestination = null;
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        Log.d(TAG, " cmdResponse: command:" + command + " result:" + result + " mstatus:" + mStatus);
        switch (command) {
            case Definition.CMD_NAVI_GET_LOCATION:
                processGetLocation(result);
                return true;
            case Definition.CMD_NAVI_IS_ESTIMATE:
                processPoseEstimate(result);
                return true;
            case Definition.CMD_HEAD_GET_STATUS:
                processHeadCurrentAngle(result);
                return true;
            case Definition.CMD_NAVI_GET_POSITION:
                processGetCurrentPosition(result);
                return true;
            case Definition.CMD_NAVI_GO_POSITION:
                processNavigationResult(result, extraData);
                return true;
            case Definition.CMD_HEAD_MOVE_HEAD:
                return true;
            default:
                return false;
        }
    }

    private void processNavigationResult(String result, String extraData) {
        if (mStatus == ResetState.RESET_TURNLEFT_SECOND){
            processResetChassisTheta();
            return;
        }
        if (Definition.NAVIGATION_OK.equals(result)) {
            onResult(Definition.RESULT_OK, result);
            return;
        }
        if (Definition.NAVIGATION_CANCELED.equals(result)) {
            onResult(Definition.RESULT_STOP, result);
            return;
        }
        processError(Definition.ERROR_NAVIGATION_FAILED, "Navigation failed : " + result, extraData);
    }

    private void processError(int errorCode, String errorMessage, String extraData) {
        Log.d(TAG, "processError : " + errorCode + " " + errorMessage + " " + extraData);
        super.onError(errorCode, errorMessage, extraData);
    }

    @Override
    public boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_GO_POSITION:
                processNavigationStatus(status, extraData);
                return true;
            default:
                break;
        }
        return false;
    }

    private void prepareNavigation(AngleResetBean params) {
        Log.d(TAG, "Start navigation action");
        mStatus = ResetState.PREPARE;
        mIsFlag = false;
        mReqId = params.getReqId();
        if (!TextUtils.isEmpty(mDestination)){
            getDestinationPose();
        }else {
            checkPoseEstimate();
        }
    }

    private void getDestinationPose(){
        mApi.getLocation(mReqId, mDestination);
    }

    private void processGetLocation(String result){
        if (mStatus != ResetState.PREPARE){
            return;
        }
        try {
            JSONObject jsonObject = new JSONObject(result);
            boolean isExist = jsonObject.has("siteexist") ? jsonObject.getBoolean("siteexist") : false;
            float poseX = (float)jsonObject.optDouble("px", 0.0f);
            float poseY = (float)jsonObject.optDouble("py", 0.0f);
            float poseTheta = (float)jsonObject.optDouble("theta", 0.0f);
            if (isExist){
                mDestPose = new Pose();
                mDestPose.setX(poseX);
                mDestPose.setY(poseY);
                mDestPose.setTheta(poseTheta);
                checkPoseEstimate();
            }else {
                onError(Definition.ERROR_DESTINATION_NOT_EXIST, "get target pose fail");
            }
        } catch (JSONException e) {
            Log.e(TAG, Log.getStackTraceString(e));
            onError(Definition.ERROR_DESTINATION_NOT_EXIST, "get target pose error");
        }
    }
    
    private void checkPoseEstimate() {
        Log.d(TAG, "Check pose estimate");
        if (mStatus == ResetState.PREPARE) {
            mApi.isRobotEstimate(mReqId);
        }
    }

    private void getHeadCurrentAngle(){
        Log.d(TAG, "getHeadCurrentAngle");
        mStatus = ResetState.GET_HEAD_CURRENT_ANGLE;
        mApi.getHeadStatus(mReqId);
    }

    private void getCurrentPosition(){
        Log.d(TAG, "getCurrentPosition");
        mStatus = ResetState.GET_CURRENT_POSITION;
        mApi.getPosition(mReqId);
    }

    private void processPoseEstimate(String result) {
        if (mStatus != ResetState.PREPARE){
            return;
        }
        if (!"true".equals(result)) {
            onError(Definition.ERROR_NOT_ESTIMATE, "Not pose estimate");
            return;
        }
        getHeadCurrentAngle();
    }

    private void processHeadCurrentAngle(String result){
        if (mStatus != ResetState.GET_HEAD_CURRENT_ANGLE){
            return;
        }
        try {
            JSONObject jsonObject = new JSONObject(result);
            mCurHeadAngle = jsonObject.getInt("horizontal");
        } catch (JSONException e) {
            e.printStackTrace();
            mCurHeadAngle = 0;
        }
        getCurrentPosition();
    }

    private void processGetCurrentPosition(String result){
        if (mStatus != ResetState.GET_CURRENT_POSITION){
            return;
        }
        mTempPose = new Pose();
        try {
            JSONObject object = new JSONObject(result);
            mTempPose.setX((float) object.optDouble("px"));
            mTempPose.setY((float) object.optDouble("py"));
            mTempPose.setTheta((float) object.optDouble("theta"));
        } catch (JSONException e) {
            e.printStackTrace();
            mTempPose = null;
        }
        readyForResetChassis(mTempPose);
    }

    private void readyForResetChassis(Pose pose){
        Log.d(TAG, "readyForResetChassis pose:" + pose);
        if (pose == null){
            onError(Definition.ERROR_NAVIGATION_FAILED, "Get current pose fail");
        }else {
            mTempTargetTheta = caculateChassisTheta(pose.getTheta(), mCurHeadAngle);
            moveHeadToFront();
            if (mCurHeadAngle < -125){
                mStatus = ResetState.RESET_TURNLEFT_FIRST;
                registerPoseListener();
                double poseTheta = caculateChassisTheta(pose.getTheta(), -90);
                mApi.goPosition(mReqId, pose.getX(), pose.getY(), poseTheta);
            }else {
                mStatus = ResetState.RESET_TURNLEFT_SECOND;
                mApi.goPosition(mReqId, pose.getX(), pose.getY(), mTempTargetTheta);
            }

        }
    }

    private void processResetChassisTheta(){
        if (mStatus != ResetState.RESET_TURNLEFT_SECOND){
            return;
        }
        mStatus = ResetState.RESET_CHASSIS_THETA;
        mApi.goPosition(mReqId, mTempPose.getX(), mTempPose.getY(), mDestPose.getTheta());
    }

    private void registerPoseListener() {
        mPoseListener = registerStatusListener(Definition.STATUS_POSE, new StatusListener() {
            @Override
            public void onStatusUpdate(String data) {
                onPoseUpdate(data);
            }
        });
    }

    private synchronized void onPoseUpdate(String data) {
        if (TextUtils.isEmpty(data) || mStatus != ResetState.RESET_TURNLEFT_FIRST || mIsFlag == true) {
            return;
        }
        Pose pose = mGson.fromJson(data, Pose.class);

        double theta = mTempPose.getTheta() + Math.PI/6;
        if (theta < -Math.PI){
            theta = theta + 2 * Math.PI;
        }else if (theta > Math.PI){
            theta = theta - 2 * Math.PI;
        }else {

        }
        if (pose.getTheta() > theta) {
            Log.d(TAG, " go second set goal:" + data + " theta:" + theta);
            mStatus = ResetState.RESET_TURNLEFT_SECOND;
            mIsFlag = true;
            mApi.goPosition(mReqId, mTempPose.getX(), mTempPose.getY(), mTempTargetTheta, mLinearSpeed, mAngularSpeed);
        }
    }


    private void moveHeadToFront(){
        mApi.moveHead(mReqId, "absolute", "absolute",
                0, 70, DEFAULT_HEAD_HORIZONTAL_ANGULAR_SPEED, DEFAULT_HEAD_VERTICAL_ANGULAR_SPEED);
    }

    private double caculateChassisTheta(double poseTheta, int headAngle){
        double theta = poseTheta - Math.toRadians(headAngle);
        if (theta < -Math.PI){
            theta = theta + 2 * Math.PI;
        }else if (theta > Math.PI){
            theta = theta - 2 * Math.PI;
        }else {

        }
        return theta;
    }


    private Pose getTargetPose(String jsonString) {
        Log.d(TAG, "getTargetPose:" + jsonString);
        mDestPose = null;
        try {
            JSONObject json = new JSONObject(jsonString);

            float x = json.has("px") ? Float.valueOf(json.get("px").toString()) : Float.valueOf(json.get("x").toString());
            float y = json.has("py") ? Float.valueOf(json.get("py").toString()) : Float.valueOf(json.get("y").toString());
            float theta = Float.valueOf(json.get("theta").toString());
            mDestPose = new Pose();
            mDestPose.setX(x);
            mDestPose.setY(y);
            mDestPose.setTheta(theta);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        Log.d(TAG, "destination pose:" + mDestPose + " jsonString:" + jsonString);
        return mDestPose;
    }

    @Override
    public boolean verifyParam(AngleResetBean params) {
        if (params == null) {
            return false;
        }
        if (TextUtils.isEmpty(params.getDestination()) && TextUtils.isEmpty(params.getPoseJson())){
            return false;
        }

        if (!TextUtils.isEmpty(params.getPoseJson())){
            Pose pose = getTargetPose(params.getPoseJson());
            if (pose == null){
                return false;
            }else {
                return true;
            }
        }
        if (!TextUtils.isEmpty(params.getDestination())){
            mDestination = params.getDestination();
            return true;
        }

        return true;
    }

    @Override
    protected ArrayList<StopCommand> getStopCommands() {
        return new ArrayList<StopCommand>() {{
            add(new StopCommand(Definition.CMD_NAVI_STOP_NAVIGATION, null));
        }};
    }

    @Override
    protected IStopCommandChecker getStopCommandChecker() {
        return new IStopCommandChecker() {
            @Override
            public boolean executeSuccess(String command, String result) {
                RobotLog.d("Leading stop check : " + command);
                switch (command) {
                    case Definition.CMD_NAVI_STOP_NAVIGATION:
                        return true;
                default:
                    return false;
                }
            }
        };
    }

    private void processNavigationStatus(String status, String extraData) {
        switch (status) {
            case Definition.NAVIGATION_OCCLUDED:
                onStatusUpdate(Definition.STATUS_GOAL_OCCLUDED, "The target is occluded", extraData);
                break;

            case Definition.NAVIGATION_OCCLUDED_END:
                onStatusUpdate(Definition.STATUS_GOAL_OCCLUDED_END, "The occluded end", extraData);
                break;

            case Definition.NAVIGATION_AVOID:
                onStatusUpdate(Definition.STATUS_NAVI_AVOID, "The local path search failed", extraData);
                break;

            case Definition.NAVIGATION_AVOID_END:
                onStatusUpdate(Definition.STATUS_NAVI_AVOID_END, "The local path search succeed", extraData);
                break;

            case Definition.NAVIGATION_OUT_MAP:
                onStatusUpdate(Definition.STATUS_NAVI_OUT_MAP, "The goal is out of map or more dangerous", extraData);
                break;

            case Definition.NAVIGATION_GLOBAL_PATH_FAILED:
                onStatusUpdate(Definition.STATUS_NAVI_GLOBAL_PATH_FAILED, "The global is path search failed", extraData);
                break;

            case Definition.NAVIGATION_OBSTACLES_AVOID:
                onStatusUpdate(Definition.STATUS_NAVI_OBSTACLES_AVOID, "obstacles avoid", extraData);
                break;

            case Definition.NAVIGATION_STARTED:
                onStatusUpdate(Definition.STATUS_START_NAVIGATION, "Navigation has been started", extraData);
                break;

            default:
                Log.i(TAG, "Command status: " + status +  " doesn't be handled");
                break;
        }
    }
}
