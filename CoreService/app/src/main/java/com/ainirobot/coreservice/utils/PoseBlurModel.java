package com.ainirobot.coreservice.utils;

import android.util.Log;

import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.Person;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * Created by Orion on 2020/1/15.
 * Pose点模糊扩散模型
 */
public class PoseBlurModel {
    private static String TAG = "PoseBlurModel";

    private PoseBlurModel() {

    }

    private static final class SingletonHolder {
        private static PoseBlurModel instance = new PoseBlurModel();
    }

    public static PoseBlurModel getInstance() {
        return SingletonHolder.instance;
    }

    volatile float x;
    volatile float y;
    volatile float theta;
    volatile double distance;
    volatile int angle;
    volatile double radians;
    volatile float thetaD;
    private BlockingQueue<Pose> mSpreadQueue = new LinkedBlockingQueue<>();

    public void createBlurPoseList(Person person, Pose pose) {
        if (person == null || pose == null) {
            throw new NullPointerException("Params null");
        }
        Log.i(TAG, "createBlurPoseList, Current size : " + mSpreadQueue.size());
        mSpreadQueue.clear();
        x = pose.getX();
        y = pose.getY();
        theta = pose.getTheta();
        distance = person.getDistance();
        angle = person.getAngle();
        radians = Math.toRadians(angle);
        thetaD = (float) (theta - radians);

        //人体所在点位Pose
        Pose personPose = getCurrentPersonPose();
        //半径0.1
        Pose front01 = getFrontPose(0.1);
        Pose right01 = getRightPose(0.1);
        Pose left01 = getLeftPose(0.1);
        Pose right0145 = getRightSpecialAnglePose(0.1, 45);
        Pose left0145 = getLeftSpecialAnglePose(0.1, 45);
        //半径0.2
        Pose front02 = getFrontPose(0.2);
        Pose right02 = getRightPose(0.2);
        Pose left02 = getLeftPose(0.2);
        Pose right0245 = getRightSpecialAnglePose(0.2, 45);
        Pose left0245 = getLeftSpecialAnglePose(0.2, 45);
        //半径0.3
        Pose front03 = getFrontPose(0.3);
        Pose right03 = getRightPose(0.3);
        Pose left03 = getLeftPose(0.3);
        Pose right0345 = getRightSpecialAnglePose(0.3, 45);
        Pose left0345 = getLeftSpecialAnglePose(0.3, 45);
        //半径0.4
        Pose front04 = getFrontPose(0.4);
        Pose right04 = getRightPose(0.4);
        Pose left04 = getLeftPose(0.4);
        Pose right0445 = getRightSpecialAnglePose(0.4, 45);
        Pose left0445 = getLeftSpecialAnglePose(0.4, 45);

//        mSpreadQueue.add(personPose);

        mSpreadQueue.add(front01);
        mSpreadQueue.add(right0145);
        mSpreadQueue.add(left0145);
        mSpreadQueue.add(right01);
        mSpreadQueue.add(left01);

        mSpreadQueue.add(front02);
        mSpreadQueue.add(right0245);
        mSpreadQueue.add(left0245);
        mSpreadQueue.add(right02);
        mSpreadQueue.add(left02);

        mSpreadQueue.add(front03);
        mSpreadQueue.add(right0345);
        mSpreadQueue.add(left0345);
        mSpreadQueue.add(right03);
        mSpreadQueue.add(left03);

        mSpreadQueue.add(front04);
        mSpreadQueue.add(right0445);
        mSpreadQueue.add(left0445);
        mSpreadQueue.add(right04);
        mSpreadQueue.add(left04);
    }

    private Pose getRightSpecialAnglePose(double radius, int angle) {
        double sideDistance = Math.sqrt(radius * radius + distance * distance + 2 * radius * distance * Math.cos(Math.toRadians(angle)));
        double radianOffset = Math.acos((sideDistance * sideDistance + distance * distance - radius * radius) / (2 * sideDistance * distance));
        double rightRadians = radians - radianOffset;
        double rightThetaD = theta - rightRadians;
        Pose personRight = new Pose(); //右侧
//        personRight.setTheta(thetaD);//机器朝向人体方向在地图坐标中theta
        personRight.setTheta((float) (thetaD - Math.toRadians(angle)));//扩散点朝向原始人体位置方向在地图坐标中theta
        personRight.setX((float) (x + sideDistance * Math.cos(rightThetaD)));
        personRight.setY((float) (y + sideDistance * Math.sin(rightThetaD)));
        personRight.setName("Blur_Right" + angle);
        return personRight;
    }

    private Pose getLeftSpecialAnglePose(double radius, int angle) {
        double sideDistance = Math.sqrt(radius * radius + distance * distance + 2 * radius * distance * Math.cos(Math.toRadians(angle)));
        double radianOffset = Math.acos((sideDistance * sideDistance + distance * distance - radius * radius) / (2 * sideDistance * distance));
        double leftRadians = radians + radianOffset;
        double leftThetaD = theta - leftRadians;
        Pose personLeft = new Pose(); //左侧
//        personLeft.setTheta(thetaD);
        personLeft.setTheta((float) (thetaD + Math.toRadians(angle)));
        personLeft.setX((float) (x + sideDistance * Math.cos(leftThetaD)));
        personLeft.setY((float) (y + sideDistance * Math.sin(leftThetaD)));
        personLeft.setName("Blur_Left" + angle);
        return personLeft;
    }

    private Pose getRightPose(double radius) {
        double sideDistance = Math.sqrt(radius * radius + distance * distance);
        double radianOffset = Math.atan2(radius, distance);
        double rightRadians = radians - radianOffset;
        double rightThetaD = theta - rightRadians;
        Pose personRight = new Pose(); //右侧
//        personRight.setTheta(thetaD);
        personRight.setTheta((float) (thetaD - Math.toRadians(90)));
        personRight.setX((float) (x + sideDistance * Math.cos(rightThetaD)));
        personRight.setY((float) (y + sideDistance * Math.sin(rightThetaD)));
        personRight.setName("Blur_Right");
        return personRight;
    }

    private Pose getLeftPose(double radius) {
        double sideDistance = Math.sqrt(radius * radius + distance * distance);
        double radianOffset = Math.atan2(radius, distance);
        double leftRadians = radians + radianOffset;
        double leftThetaD = theta - leftRadians;
        Pose personLeft = new Pose(); //右侧
//        personLeft.setTheta(thetaD);
        personLeft.setTheta((float) (thetaD + Math.toRadians(90)));
        personLeft.setX((float) (x + sideDistance * Math.cos(leftThetaD)));
        personLeft.setY((float) (y + sideDistance * Math.sin(leftThetaD)));
        personLeft.setName("Blur_Left");
        return personLeft;
    }

    private Pose getFrontPose(double radius) {
        //人体正前方
        Pose personFront = new Pose();
        personFront.setTheta(thetaD);
        personFront.setX((float) (x + (distance - radius) * Math.cos(thetaD)));
        personFront.setY((float) (y + (distance - radius) * Math.sin(thetaD)));
        personFront.setName("Blur_Front");
        return personFront;
    }

    private Pose getCurrentPersonPose() {
        //人体所在点位Pose
        Pose personPose = new Pose();
        personPose.setTheta(thetaD);
        personPose.setX((float) (x + distance * Math.cos(thetaD)));
        personPose.setY((float) (y + distance * Math.sin(thetaD)));
        personPose.setName("Blur_Person");
        return personPose;
    }

    public Pose poll() {
        Log.i(TAG, "poll, Current size : " + mSpreadQueue.size());
        return mSpreadQueue.poll();
    }

    public void clear() {
        Log.i(TAG, "clear, Current size : " + mSpreadQueue.size());
        mSpreadQueue.clear();
    }

}
