/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.utils;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.util.Log;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * Package Manager Utils for enabling/disabling packages
 * 
 * This utility class provides methods to enable or disable Android packages
 * using various permission levels (normal, root, user-level).
 */
public class PackageManagerUtils {
    
    private static final String TAG = "PackageManagerUtils";
    
    /**
     * Check if a package is disabled
     * 
     * @param context The application context
     * @param packageName The package name to check
     * @return true if the package is disabled, false otherwise
     */
    public static boolean isPackageDisabled(Context context, String packageName) {
        try {
            PackageManager pm = context.getPackageManager();
            ApplicationInfo appInfo = pm.getApplicationInfo(packageName, 0);
            
            // Check if the package is disabled
            return !appInfo.enabled;
        } catch (PackageManager.NameNotFoundException e) {
            Log.w(TAG, "Package not found: " + packageName);
            // If package not found, consider it as disabled
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Error checking package state: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Enable a specific package
     * 
     * @param packageName The package name to enable
     * @return true if successfully enabled, false otherwise
     */
    public static boolean enablePackage(String packageName) {
        try {
            // Execute pm enable command to enable the package
            String command = "pm enable " + packageName;
            Process process = Runtime.getRuntime().exec(command);
            
            // Wait for the command to complete
            int exitCode = process.waitFor();
            
            if (exitCode == 0) {
                Log.i(TAG, "Successfully enabled " + packageName);
                return true;
            } else {
                Log.e(TAG, "Failed to enable " + packageName + ", exit code: " + exitCode);
                
                // Try with su if regular pm enable failed
                return tryEnableWithRoot(packageName);
            }
            
        } catch (IOException e) {
            Log.e(TAG, "IOException while enabling package " + packageName + ": " + e.getMessage());
            return tryEnableWithRoot(packageName);
        } catch (InterruptedException e) {
            Log.e(TAG, "InterruptedException while enabling package " + packageName + ": " + e.getMessage());
            Thread.currentThread().interrupt();
            return false;
        } finally {
            // Log output for debugging
            logProcessOutput(packageName, "enable");
        }
    }
    
    /**
     * Disable a specific package
     * 
     * @param packageName The package name to disable
     * @return true if successfully disabled, false otherwise
     */
    public static boolean disablePackage(String packageName) {
        try {
            // Execute pm disable command to disable the package
            String command = "pm disable " + packageName;
            Process process = Runtime.getRuntime().exec(command);
            
            // Wait for the command to complete
            int exitCode = process.waitFor();
            
            if (exitCode == 0) {
                Log.i(TAG, "Successfully disabled " + packageName);
                return true;
            } else {
                Log.e(TAG, "Failed to disable " + packageName + ", exit code: " + exitCode);
                
                // Try with su if regular pm disable failed
                return tryDisableWithRoot(packageName);
            }
            
        } catch (IOException e) {
            Log.e(TAG, "IOException while disabling package " + packageName + ": " + e.getMessage());
            return tryDisableWithRoot(packageName);
        } catch (InterruptedException e) {
            Log.e(TAG, "InterruptedException while disabling package " + packageName + ": " + e.getMessage());
            Thread.currentThread().interrupt();
            return false;
        } finally {
            // Log output for debugging
            logProcessOutput(packageName, "disable");
        }
    }
    
    /**
     * Try to enable package with root permissions
     */
    private static boolean tryEnableWithRoot(String packageName) {
        try {
            Log.d(TAG, "Trying to enable " + packageName + " with root permissions");
            
            // Try with su command
            Process suProcess = Runtime.getRuntime().exec("su");
            DataOutputStream os = new DataOutputStream(suProcess.getOutputStream());
            
            // Execute pm enable command as root
            String command = "pm enable " + packageName + "\n";
            os.writeBytes(command);
            os.writeBytes("exit\n");
            os.flush();
            os.close();
            
            int exitCode = suProcess.waitFor();
            if (exitCode == 0) {
                Log.i(TAG, "Successfully enabled " + packageName + " with root");
                return true;
            } else {
                Log.e(TAG, "Failed to enable " + packageName + 
                        " with root, exit code: " + exitCode);
                
                // As a last resort, try without root
                return tryEnableForUser(packageName);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Exception while trying root enable: " + e.getMessage());
            // Try user-level enable as fallback
            return tryEnableForUser(packageName);
        }
    }
    
    /**
     * Try to disable package with root permissions
     */
    private static boolean tryDisableWithRoot(String packageName) {
        try {
            Log.d(TAG, "Trying to disable " + packageName + " with root permissions");
            
            // Try with su command
            Process suProcess = Runtime.getRuntime().exec("su");
            DataOutputStream os = new DataOutputStream(suProcess.getOutputStream());
            
            // Execute pm disable command as root
            String command = "pm disable " + packageName + "\n";
            os.writeBytes(command);
            os.writeBytes("exit\n");
            os.flush();
            os.close();
            
            int exitCode = suProcess.waitFor();
            if (exitCode == 0) {
                Log.i(TAG, "Successfully disabled " + packageName + " with root");
                return true;
            } else {
                Log.e(TAG, "Failed to disable " + packageName + 
                        " with root, exit code: " + exitCode);
                
                // As a last resort, try pm disable-user command which might not need root
                return tryDisableForUser(packageName);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Exception while trying root disable: " + e.getMessage());
            // Try user-level disable as fallback
            return tryDisableForUser(packageName);
        }
    }
    
    /**
     * Try to enable package for current user
     */
    private static boolean tryEnableForUser(String packageName) {
        try {
            Log.d(TAG, "Trying to enable " + packageName + " for current user");
            
            // Try pm enable command without user flag (some systems don't have enable-user)
            String command = "pm enable " + packageName;
            Process process = Runtime.getRuntime().exec(command);
            
            int exitCode = process.waitFor();
            if (exitCode == 0) {
                Log.i(TAG, "Successfully enabled " + packageName + " for current user");
                return true;
            } else {
                Log.e(TAG, "Failed to enable " + packageName + 
                        " for current user, exit code: " + exitCode);
                return false;
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Exception while trying user-level enable: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Try to disable package for current user
     */
    private static boolean tryDisableForUser(String packageName) {
        try {
            Log.d(TAG, "Trying to disable " + packageName + " for current user");
            
            // Try pm disable-user command which disables for current user only
            String command = "pm disable-user " + packageName;
            Process process = Runtime.getRuntime().exec(command);
            
            int exitCode = process.waitFor();
            if (exitCode == 0) {
                Log.i(TAG, "Successfully disabled " + packageName + " for current user");
                return true;
            } else {
                Log.e(TAG, "Failed to disable " + packageName + 
                        " for current user, exit code: " + exitCode);
                return false;
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Exception while trying user-level disable: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Log process output for debugging
     */
    private static void logProcessOutput(String packageName, String operation) {
        try {
            String command = "pm list packages -d | grep " + packageName;
            Process process = Runtime.getRuntime().exec(command);
            
            BufferedReader reader = new BufferedReader(
                    new InputStreamReader(process.getInputStream()));
            String line;
            boolean found = false;
            while ((line = reader.readLine()) != null) {
                if (line.contains(packageName)) {
                    found = true;
                    Log.d(TAG, "Package " + packageName + " is currently disabled");
                }
            }
            if (!found && "disable".equals(operation)) {
                Log.d(TAG, "Package " + packageName + " is not in disabled list (might be enabled or not exist)");
            }
            reader.close();
            
        } catch (Exception e) {
            Log.e(TAG, "Error checking package status: " + e.getMessage());
        }
    }
} 