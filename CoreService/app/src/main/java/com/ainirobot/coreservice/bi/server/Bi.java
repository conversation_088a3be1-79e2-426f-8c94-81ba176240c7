package com.ainirobot.coreservice.bi.server;

import android.annotation.TargetApi;
import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.base.AiniRobotAnalytics;
import com.ainirobot.base.OrionBase;
import com.ainirobot.base.analytics.interfaces.IAnalyticsConfig;
import com.ainirobot.base.analytics.interfaces.OnReportResultListener;
import com.ainirobot.base.analytics.utils.PublicDataUtils;
import com.ainirobot.base.util.Logger;
import com.ainirobot.coreservice.bi.annotation.OsType;
import com.ainirobot.coreservice.bi.data.BiData;
import com.ainirobot.coreservice.bi.data.BiDataMonitor;
import com.ainirobot.coreservice.bi.data.BiPublicDataMonitor;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.exception.InvalidArgumentException;
import com.ainirobot.coreservice.client.exception.NoSuchKeyException;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.utils.SettingDataHelper;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import static android.content.pm.PackageManager.MATCH_SYSTEM_ONLY;
import static com.ainirobot.coreservice.client.Definition.MODULE_PACKAGE_NAME;

public class Bi {

    public static String CLIENT_ID = "orion.ovs.client.1514259512471";

    private static final String TAG = "BiReport";

    private static final int MIN_LENGTH = 5;

    private static final String WAKEUP_ID = "wakeup_id";
    private static final String TASK = "task";
    private static final String TASK_WAIT = "task_wait";
    private static final String NLP_APP_ID = "appid";
    private static final String NLP_APP_PATH = "path";
    private static final String NLP_APP_VERSION = "app_ver";

    private static final String GB_Y_QUERY = "gb_y_query";
    private static final String GB_ERROR = "gb_error";

    private static volatile int osType = OsType.OS_TYPE_OTHER;

    private static final String URI = "content://com.ainirobot.coreservice.robotsettingprovider/setting";
    private static final String KEY_ID = "id";
    private static final String KEY_VALUE = "value";

    private static List<String> taskWhiteList = new ArrayList<String>() {{
        add(GB_Y_QUERY);
        add(GB_ERROR);
    }};

    private static BiData biData = BiData.getInstance();

    private Bi() {
    }

    public static void init(Context context) {
        SettingDataHelper.getInstance().setContext(context);
        initPublicData(context);
        BiDataMonitor.start(context);
        BiPublicDataMonitor.start(context);

        initOrionBase(context);
        BiLevelStrategy.initLevel(context);
    }

    private static void initPublicData(Context context) {
        String defaultApp = getRobotString(context, Definition.BOOT_APP_PACKAGE_NAME);
        Log.i(TAG, "checkBiPublicParam: defaultApp=" + defaultApp);
        if (!TextUtils.isEmpty(defaultApp) && !MODULE_PACKAGE_NAME.equals(defaultApp)) {
            setOsType(OsType.OS_TYPE_DEVELOPER);
        } else {
            setOsType(calculateOsType(context));
        }
    }

    public static void report(String tableName, String data, boolean notForce) {
        report(Definition.BI_TYPE_NORMAL, Definition.BI_LEVEL_VERBOSE, tableName, data, notForce);
    }

    public static void report(String type, int level, String tableName, String data,
                              boolean notForce) {
        if (BiLevelStrategy.isAbortReport(type, level)) {
            Log.d(TAG, "Abort report type : " + type + "  level : " + level);
            return;
        }

        if (tableName == null || tableName.length() < MIN_LENGTH) {
            Log.d(TAG, "Invalid table name : " + tableName + "  and data : " + data);
            return;
        }

        if (data == null || data.length() < MIN_LENGTH) {
            Log.d(TAG, "Invalid data : " + data + "  and table name : " + tableName);
            return;
        }

        data = appendWakeUpId(data);
        data = appendNlpState(data);

        if (taskWhiteList.contains(tableName)) {
            data = appendCurrentModule(data);
        }

        Log.d(TAG, "Bi report : " + data);
        AiniRobotAnalytics.getInstance().logEvent(tableName, data, !notForce);
    }

    private static void initOrionBase(final Context context) {
        String clientId = null;
        try {
            clientId = RobotSettings.getRobotString(context, "client_id");
        } catch (NoSuchKeyException | InvalidArgumentException e) {
            e.printStackTrace();
        }
        final String obClientId = TextUtils.isEmpty(clientId) ? CLIENT_ID : clientId;
        String zone = SettingDataHelper.getInstance().getCloudServerZone();
        Log.d(TAG,"initOrionBase zone: " + zone);
        OrionBase.setDomainEnv(ProductInfo.isOverSea(), zone,RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_SYSTEM_ENV) == 1);
        AiniRobotAnalytics.init(context, 60 * 1000,
                new IAnalyticsConfig() {
                    @Override
                    public void addPublicData(JSONObject jsonObject) throws Exception {
                        jsonObject.put("client_id", obClientId);
                    }
                });

        AiniRobotAnalytics.getInstance().setOnReportResultListener(new OnReportResultListener() {
            @Override
            public void onReportSuccess(String requestData, JSONObject jsonObject) {
                if (!TextUtils.isEmpty(requestData) && !TextUtils.isEmpty(jsonObject.toString())) {
                    Log.i(TAG, "AiniRobotAnalytics onReportSuccess  = " + requestData + " ResponseJson = " + jsonObject.toString());
                }
            }

            @Override
            public void onReportFail(String requestData, JSONObject jsonObject) {
//                if (Logger.debuggable()) {
//                    throw new RuntimeException(jsonObject.toString());
//                }
                if (!TextUtils.isEmpty(requestData) && !TextUtils.isEmpty(jsonObject.toString())) {
                    Log.i(TAG, "AiniRobotAnalytics onReportFail  = " + requestData + " ResponseJson = " + jsonObject.toString());
                }
            }

            @Override
            public void onReportException(String requestData, String msg) {
                if (!TextUtils.isEmpty(requestData) && !TextUtils.isEmpty(msg)) {
                    Log.i(TAG, "AiniRobotAnalytics onReportException  = " + requestData + " msg = " + msg);
                }
            }
        });
    }

    private static int getOsType(Context context) {
        if (osType == OsType.OS_TYPE_OTHER) {
            synchronized (Bi.class) {
                if (osType == OsType.OS_TYPE_OTHER) {
                    osType = calculateOsType(context);
                }
            }
        }
        return osType;
    }

    @TargetApi(Build.VERSION_CODES.N)
    public static int calculateOsType(Context ctx) {
        PackageManager packageManager = ctx.getPackageManager();
        PackageInfo info;
        int versionCode = 0;
        try {
            info = packageManager.getPackageInfo(MODULE_PACKAGE_NAME, MATCH_SYSTEM_ONLY);
            versionCode = info.versionCode;
            String versionName = info.versionName;
            Log.i(TAG, "calculateOsType: versionCode:" + versionCode + " versionName:" + versionName);
        } catch (PackageManager.NameNotFoundException e) {
            Log.e(TAG, "calculateOsType: " + e.getMessage());
        }
        if (versionCode == 0) {
            Log.w(TAG, "calculateOsType: versionCode == 0");
            return OsType.OS_TYPE_OTHER;
        }
        return versionCode % 100 == 0 ? OsType.OS_TYPE_DEV : OsType.OS_TYPE_CUSTOM;
    }


    public static void setOsType(int osType) {
        Bi.osType = osType;
        PublicDataUtils.setOSType(String.valueOf(osType));
    }

    public static String getRobotString(Context context, String key) {
        Cursor cursor = context.getContentResolver().query(Uri.parse(URI), new String[]{KEY_VALUE},
                KEY_ID + "=?", new String[]{key}, null);
        if (cursor != null && cursor.moveToNext()) {
            String value = cursor.getString(cursor.getColumnIndex(KEY_VALUE));
            cursor.close();
            Log.d(TAG, "get robot string:key=" + key + ",value=" + value);
            return value;
        }

        return "";
    }

    /**
     * 业务埋点数据添加wakeupId
     *
     * @param data 业务埋点数据
     * @return 添加wakeupId后的业务埋点数据
     */
    private static String appendWakeUpId(String data) {
        if (TextUtils.isEmpty(data) || data.contains(WAKEUP_ID)) {
            return data;
        }
        data = appendField(data, WAKEUP_ID, biData.getWakeUpId());
        return data;
    }

    /**
     * 业务埋点数据添加app_id&app_path
     *
     * @param data 业务埋点数据
     * @return 添加app_id&app_path后的业务埋点数据
     */
    private static String appendNlpState(String data) {
        if (TextUtils.isEmpty(data)) {
            return data;
        }
        data = appendField(data, NLP_APP_ID, biData.getNlpAppId());
        data = appendField(data, NLP_APP_PATH, biData.getNlpAppPath());
        data = appendField(data, NLP_APP_VERSION, biData.getNlpAppVersion());
        return data;
    }

    /**
     * add current module and wait module
     *
     * @param data bi data
     * @return bi data
     */
    private static String appendCurrentModule(String data) {
        if (TextUtils.isEmpty(data)) {
            return data;
        }
        data = appendField(data, TASK, biData.getCurrentModule());
        data = appendField(data, TASK_WAIT, biData.getWaitModule());
        return data;
    }

    private static String appendField(String data, String key, String value) {
        return String.format(Locale.ENGLISH, "%s%s", data, "&" + key + "=" + value);
    }

}
