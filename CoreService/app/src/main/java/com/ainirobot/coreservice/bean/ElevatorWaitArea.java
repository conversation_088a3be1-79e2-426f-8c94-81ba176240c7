package com.ainirobot.coreservice.bean;

import java.util.List;

public class ElevatorWaitArea {
    public int areaId;
    private String areaAlias;
    private List<String> availableWaitPoints;
    private List<String> availableElevators;

    public ElevatorWaitArea() {
    }

    public int getAreaId() {
        return this.areaId;
    }

    public void setAreaId(int areaId) {
        this.areaId = areaId;
    }

    public List<String> getAvailableWaitPoints() {
        return this.availableWaitPoints;
    }

    public void setAvailableWaitPoints(List<String> availableWaitPoints) {
        this.availableWaitPoints = availableWaitPoints;
    }

    public List<String> getAvailableElevators() {
        return this.availableElevators;
    }

    public void setAvailableElevators(List<String> availableElevators) {
        this.availableElevators = availableElevators;
    }

    public String getAreaAlias() {
        return this.areaAlias;
    }

    public void setAreaAlias(String areaAlias) {
        this.areaAlias = areaAlias;
    }

}