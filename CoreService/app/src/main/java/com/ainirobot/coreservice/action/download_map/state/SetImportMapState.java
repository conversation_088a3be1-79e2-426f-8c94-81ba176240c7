package com.ainirobot.coreservice.action.download_map.state;

import android.content.ContentResolver;
import android.database.Cursor;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.action.download_map.DownloadMapStateEnum;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.DownloadMapBean;
import com.ainirobot.coreservice.data.RobotInfoManager;

import org.json.JSONObject;

public class SetImportMapState extends BaseDownloadMapPkgState {

    private static final String COLUMN_MAP_NAME = "map_name";
    private static final String COLUMN_SYNC_STATE = "sync_state";
    public static final int UPLOADED_MAP = 1;
    private DownloadMapBean downloadMapBean;

    @Override
    protected void onStart(String paramsStr, DownloadMapBean downloadMapBean) {
        Log.d(TAG, "SetImportMapState onStart: ");
        this.downloadMapBean = downloadMapBean;
        updateMapSyncState(downloadMapBean.getMapName());
    }

    @Override
    public boolean onCmdResponse(int cmdId, String command, String result, String extraData) {
        if (TextUtils.equals(command, Definition.CMD_NAVI_SET_MAP_SYNC_STATE)) {
            Log.d(TAG, "onCmdResponse: set map sync state complete");
            setMapUuid(downloadMapBean.getMapName(), "");
        } else if (TextUtils.equals(command, Definition.CMD_NAVI_SET_MAP_UUID)) {
            Log.d(TAG, "onCmdResponse: set map uuid complete");
            onStateRunSuccess();
        }
        return false;
    }

    @Override
    protected DownloadMapStateEnum getNextState() {
        return DownloadMapStateEnum.DOWNLOAD_SUCCEED;
    }

    private void setMapUuid(String mapName, String uuid) {
        try {
            JSONObject param = new JSONObject();
            param.put("mapName", mapName);
            param.put("mapUuid", uuid);
            sendCommand(0, Definition.CMD_NAVI_SET_MAP_UUID, param.toString());
        } catch (Exception e) {
            e.printStackTrace();
            Log.d(TAG, "setMapUuid: send map uuid failed");
        }
    }

    private void updateMapSyncState(String mapName) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_MAP_NAME, mapName);
            param.put(Definition.JSON_MAP_IS_MAP_STATE, true);
            param.put(Definition.JSON_MAP_NEED_RE_UPLOAD, true);
            sendCommand(0, Definition.CMD_NAVI_SET_MAP_SYNC_STATE, param.toString());
        } catch (Exception e) {
            e.printStackTrace();
            onStateRunFailed(0, "send map sync state failed");
        }
    }
}
