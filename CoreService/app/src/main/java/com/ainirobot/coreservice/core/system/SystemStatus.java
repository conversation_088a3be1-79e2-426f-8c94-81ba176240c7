/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.core.system;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.R;
import com.ainirobot.coreservice.bi.report.SystemStatusReporter;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.core.InternalDef;
import com.ainirobot.coreservice.core.system.BatteryManager.BatteryStatus;
import com.ainirobot.coreservice.core.system.OTAManager.OTAStartMode;
import com.ainirobot.coreservice.core.system.OTAManager.OTAStatus;
import com.ainirobot.coreservice.service.CoreService;
import com.ainirobot.coreservice.utils.SystemProperties;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

public enum SystemStatus {
    /**
     * 重启状态
     */
    SYSTEM_SHUTDOWN(5) {
        @Override
        public String getName() {
            return getString(R.string.shutdown);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.shutdown_tts);
        }

        @Override
        public String getLight() {
            return Definition.LIGHT_CLOSE;
        }
    },
    /**
     * 自检状态
     */
    INSPECTION(10) {
        @Override
        public String getName() {
            return getString(R.string.inspection);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.inspection_tts);
        }

        @Override
        public String getLight() {
            return (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus()) ? Definition.LIGHT_INSPECTION : null;
        }
    },
    /**
     * 降级 OTA状态
     */
    DOWNGRADE_OTA(13) {
        private String mOtaParams;
        private OTAStatus mStatus;
        private OTAStartMode mStartMode = OTAStartMode.SETTING;

        @Override
        public void update(Object status, Object... params) {
            Log.d(TAG, "update ota downgrade params");
            super.update(status, params);
            mStatus = (OTAStatus) status;
            if (params != null && params.length >= 1) {
                Log.d(TAG, "status: " + status + " params: " + new Gson().toJson(params));
                if (params.length == 1) {
                    this.mOtaParams = (String) params[0];
                }
                if (params.length == 2) {
                    this.mOtaParams = (String) params[0];
                    this.mStartMode = (OTAStartMode) params[1];
                }
            }
        }

        @Override
        public String getType() {
            if (mStatus == null) {
                return null;
            }
            switch (mStatus) {
                case AUTH:
                    return Definition.REQ_OTA_AUTH_DOWNGRADE;

                case SHOW_RESULT:
                    return Definition.REQ_OTA_DOWNGRADE_RESULT;

                case INSPECTION:
                    return Definition.REQ_OTA_DOWNGRADE_INSPECT;

                default:
                    return null;
            }
        }

        @Override
        public String getParams() {
            if (mStatus == null) {
                return null;
            }
            JSONObject json = new JSONObject();
            try {
                json.put("otaInfo", mOtaParams);
                json.put("startup", mStartMode.getValue());
            } catch (JSONException e) {
                e.printStackTrace();
            }
            return json.toString();
        }

        @Override
        public String getName() {
            return getString(R.string.downgrade_ota);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.downgrade_ota_tts);
        }

        @Override
        public boolean isEnabled() {
            return super.isEnabled();
        }
    },
    SYSTEM_RECOVERY(15) {
        private String mParam;

        @Override
        public void update(Object status, Object... params) {
            super.update(status, params);
            mParam = (String) status;
        }

        @Override
        public String getType() {
            return Definition.REQ_SYSTEM_RECOVERY;
        }

        @Override
        public String getName() {
            return getString(R.string.system_recovery);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.system_recovery_tts);
        }

        @Override
        public String getParams() {
            return mParam;
        }
    },

    AUTO_OTA(17) {
        private OTAStartMode mStartMode;

        @Override
        public void update(Object status, Object... params) {
            super.update(status, params);
            mStartMode = (OTAStartMode) status;
        }

        @Override
        public boolean isEnabled() {
            return super.isEnabled()
                    || (mStartMode != OTAStartMode.BOOT && mStartMode != OTAStartMode.NIGHTTIME);
        }
    },

    OTA(17) {
        private OTAStatus mStatus;
        private boolean isForce;
        private String mOtaParams;
        private OTAStartMode mStartMode;

        private long mUptime = 0;

        @Override
        public void update(Object status, Object... params) {
            super.update(status, params);
            OTAStatus otaStatus = (OTAStatus) status;
            long uptime = 0;
            if (otaStatus == OTAStatus.NONE && isForce) {
                Log.d("OTA", "Forced ota can not be canceled");
                return;
            }

            if (otaStatus == OTAStatus.AUTH || otaStatus == OTAStatus.SHOW_RESULT) {
                if (params.length > 1) {
                    uptime = (long) params[0];
                }
                Log.e(TAG, "state:" + otaStatus + ", uptime:" + uptime + ", mUptime:" + mUptime);
                if (uptime < mUptime) {
                    return;
                } else {
                    mUptime = uptime;
                }
            } else if (otaStatus == OTAStatus.NONE) {
                mUptime = 0;
            }
            this.mStatus = otaStatus;

            if (params.length > 2) {
                this.isForce = (boolean) params[1];
                this.mOtaParams = (String) params[2];
            }

            if (params.length > 3) {
                this.mStartMode = (OTAStartMode) params[3];
            }
        }

        @Override
        public String getType() {
            if (mStatus == null) {
                return null;
            }
            switch (mStatus) {
                case AUTH:
                    return Definition.REQ_OTA_AUTH;

                case SHOW_RESULT:
                    return Definition.REQ_OTA_RESULT;

                case INSPECTION:
                    return Definition.REQ_OTA_INSPECT;

                default:
                    return null;
            }
        }

        @Override
        public String getParams() {
            if (mStatus == null) {
                return null;
            }
            switch (mStatus) {
                case AUTH: {
                    JSONObject json = new JSONObject();
                    try {
                        json.put("isForce", isForce);
                        json.put("otaInfo", mOtaParams);
                        json.put("startup", mStartMode.getValue());
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    return json.toString();
                }

                case SHOW_RESULT:
                    return mOtaParams;

                default:
                    return null;
            }
        }

        @Override
        public boolean isSystemControl() {
            return mStatus != OTAStatus.AUTH;
        }

        @Override
        public boolean isEnabled() {
            Log.d("OTA", "Ota is enable : " + super.isEnabled() + "  status : " + mStatus.name());
            return super.isEnabled() || mStatus == OTAStatus.SHOW_RESULT ||
                    mStatus == OTAStatus.INSPECTION;
        }

        @Override
        public String getName() {
            return getString(R.string.ota);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.ota_tts);
        }
    },

    D430_CALIBRATION(18) {
        private String params;

        @Override
        public String getType() {
            return Definition.REQ_D430_CALIBRATION_START;
        }

        @Override
        public String getParams() {
            return params;
        }

        @Override
        public String getName() {
            return getString(R.string.d430_calibration);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.d430_calibration_tts);
        }

        @Override
        public boolean isEnabled() {
            return super.isEnabled() && !ProductInfo.isSaiphRgbdFm1();
        }
    },

    STANDBY(19) {
        private String par;

        @Override
        public void update(Object status, Object... params) {
            super.update(status, params);
            par = (String) params[0];
        }

        @Override
        public String getType() {
            if (null != par) {
                try {
                    JSONObject jsonObject = new JSONObject(par);
                    String standbyStatus = jsonObject.optString(Definition.JSON_STANDBY_STATUS);
                    if (TextUtils.equals(standbyStatus, Definition.START)) {
                        return Definition.REQ_STANDBY_START;
                    } else if (TextUtils.equals(standbyStatus, Definition.STOP)) {
                        return Definition.REQ_STANDBY_STOP;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            return super.getType();
        }

        @Override
        public String getParams() {
            return par;
        }

        @Override
        public String getName() {
            return getString(R.string.standby);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.standby_tts);
        }

        @Override
        public String getLight() {
            return Definition.LIGHT_EMPTY;
        }

        @Override
        public String getDebugKey() {
            return "standby";
        }
    },

    /**
     * 定时关机
     */
    SHUTDOWN_TIMER(20) {
        @Override
        public String getType() {
            return Definition.REQ_SYSTEM_SHUTDOWN_TIMER;
        }

        @Override
        public String getName() {
            return getString(R.string.shutdown_timer);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.shutdown_timer_tts);
        }
    },

    DORMANCY(21) {

        private String params;

        @Override
        public void update(Object status, Object... params) {
            super.update(status, params);
            if (status != null) {
                this.params = (String) status;
            } else {
                this.params = null;
            }
        }

        @Override
        public String getType() {
            return Definition.REQ_START_DORMANCY;
        }

        @Override
        public String getParams() {
            return params;
        }

        @Override
        public String getName() {
            return getString(R.string.dormancy);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.dormancy_tts);
        }
    },

    REMOTE_BIND(22) {
        private String errMessage;

        @Override
        public String getType() {
            return Definition.REQ_BIND_FAILED;
        }

        @Override
        public String getParams() {
            return errMessage;
        }

        @Override
        public String getName() {
            return getString(R.string.remote_bind);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.remote_bind_tts);
        }

        @Override
        public void update(Object status, Object... params) {
            super.update(status, params);
            errMessage = (String) params[0];
        }
    },

    FULL_LOCK(23) {
        String type = "system_remote_lock_full";

        private String paramsJsonStr;

        @Override
        public void update(Object status, Object... params) {
            switch ((String) status) {
                case Definition.JSON_TYPE:
                    type = (String) params[0];
                    break;
                case Definition.JSON_CONFIG:
                    paramsJsonStr = (String) params[0];
                    break;
                default:
                    break;
            }
        }

        @Override
        public String getType() {
            return type;
        }

        @Override
        public String getLight() {
            return Definition.LIGHT_EMERGENCY;
        }

        @Override
        public String getName() {
            return getString(R.string.lock);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.lock_tts);
        }

        @Override
        public String getParams() {
            return paramsJsonStr;
        }
    },

    BLE_STATUS(24) {
        private boolean isCloseToSignalSource = false;// 是否靠近信号源

        @Override
        public void update(Object status, Object... params) {
            if (status instanceof Boolean) {
                isCloseToSignalSource = (boolean) status;
            }
        }

        @Override
        public String getType() {
            return isCloseToSignalSource ? Definition.REQ_BLE_SIGNAL_NEAR : Definition.REQ_BLE_SIGNAL_FAR;
        }

        @Override
        public String getLight() {
            return Definition.LIGHT_EMERGENCY;
        }

        @Override
        public String getName() {
            return getString(R.string.ble_status);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.ble_status_tts);
        }

        @Override
        public String getParams() {
            return "";
        }
    },

    FUNCTION_KEY(25) {
        private boolean isPressed;

        @Override
        public void update(Object status, Object... params) {
            super.update(status, params);
            if (status instanceof Boolean) {
                isPressed = (boolean) status;
            }
        }

        @Override
        public String getType() {
            if (isPressed) return Definition.REQ_FUNCTION_KEY_PRESS;
            else return Definition.REQ_FUNCTION_KEY_RELEASE;
        }

        @Override
        public String getName() {
            return getString(R.string.standby);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.standby_tts);
        }

        @Override
        public String getLight() {
            return Definition.LIGHT_EMPTY;
        }
    },

    //todo
    REMOTE_PUSH_MAP_NEED_SWITCH(26) {
        String type = Definition.REMOTE_PUSH_MAP_NEED_SWITCH;

        private String reqParams;

        @Override
        public void update(Object status, Object... params) {
            switch ((String) status) {
                case Definition.JSON_TYPE:
                    type = (String) params[0];
                    break;
                case Definition.JSON_CONFIG:
                    reqParams = (String) params[0];
                    break;
                default:
                    break;
            }
        }

        @Override
        public String getType() {
            return type;
        }

        @Override
        public String getParams() {
            return reqParams;
        }

        @Override
        public boolean isSystemControl() {
            return true;
        }
    },

    ENABLE_TARGET_CUSTOM(27) {
        private String param;

        @Override
        public void update(Object status, Object... params) {
            super.update(status, params);
            param = (String)status;
        }

        @Override
        public String getType() {
            return Definition.REQ_ENABLE_TARGET_CUSTOM;
        }

        @Override
        public String getParams() {
            return param;
        }

        @Override
        public String getName() {
            return getString(R.string.system_setting);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.system_setting_tts);
        }
    },

    WECHAT_CHARGE_PILE(28) {
        @Override
        public String getType() {
            return Definition.WECHAT_SET_CHARGING_PILE;
        }

        @Override
        public boolean isPersistent() {
            return false;
        }


        @Override
        public String getName() {
            return getString(R.string.set_charge_pile);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.set_charge_pile_tts);
        }
    },

    REMOTE_IMPORT_MAP(29) {
        private final String type = Definition.REMOTE_IMPORT_MAP_BEGIN;
        private String reqParams;

        @Override
        public void update(Object status, Object... params) {
            switch ((String) status) {
                case Definition.REMOTE_IMPORT_MAP_BEGIN:
                    reqParams = (String) params[0];
                    break;
                default:
                    break;
            }
        }

        @Override
        public String getType() {
            return type;
        }

        @Override
        public String getParams() {
            return reqParams;
        }

        @Override
        public boolean isSystemControl() {
            return true;
        }

        @Override
        public String getName() {
            return getString(R.string.import_map);
        }
    },

    SET_CHARGE_PILE(30) {

        private boolean isPoseEstimate = false;
        private boolean isCharging = false;
        private int chargingEnvironment = -1;

        @Override
        public void update(Object status, Object... params) {
            super.update(status, params);
            switch ((String) status) {
                case InternalDef.CHARGING:
                    isCharging = (boolean) params[0];
                    break;
                case InternalDef.POSE_ESTIMATE:
                    isPoseEstimate = (boolean) params[0];
                    break;
                case Definition.SET_CHARGING_ENVIRONMENT:
                    chargingEnvironment = Integer.parseInt((String) params[0]);
                    break;
                default:
                    break;
            }
        }

        @Override
        public String getType() {
            return Definition.REQ_SET_CHARGE_PILE;
        }

        @Override
        public boolean isPersistent() {
            return false;
        }

        @Override
        public boolean isEnabled() {
            Log.d("SET_CHARGE_PILE", "set_charge_pile isPoseEstimate : " + isPoseEstimate
                    + " !RobotSetting.isSituServiceOpened() :" + !RobotSetting.isSituServiceOpened());
            return super.isEnabled() && isPoseEstimate && !RobotSetting.isSituServiceOpened();
        }

        @Override
        public String getFailReason() {
            String reason = null;
            if (RobotSetting.isSituServiceOpened()) {
                reason = getString(R.string.situ_service_opened);
            } else if (!isPoseEstimate) {
                reason = getString(R.string.need_estimate);
            }
            return reason;
        }

        @Override
        public String getParams() {
            JSONObject json = new JSONObject();
            try {
                json.put(Definition.SET_CHARGING_ENVIRONMENT, chargingEnvironment);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            return json.toString();
        }

        @Override
        public String getName() {
            return getString(R.string.set_charge_pile);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.set_charge_pile_tts);
        }
    },

    EMERGENCY(40) {
        private boolean isEmergency = false;

        @Override
        public void update(Object status, Object... params) {
            super.update(status, params);
            if (status instanceof Boolean) {
                isEmergency = (boolean) status;
            }
        }

        @Override
        public String getType() {
            return isEmergency ? Definition.REQ_EMERGENCY_PRESS : null;
        }

        @Override
        public String getLight() {
            return Definition.LIGHT_EMERGENCY;
        }

        @Override
        public String getExitType() {
            return Definition.REQ_EMERGENCY_RELEASE;
        }

        @Override
        public String getName() {
            return getString(R.string.emergency);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.emergency_tts);
        }
    },

    E70_STATUS(41){
        private List<String> mErrList = new CopyOnWriteArrayList<>();
        private boolean isChanged = false;

        @Override
        public void update(Object reason, Object... params) {
            super.update(reason, params);
            String name = (String) reason;
            boolean status = false;
            if (params.length > 0) {
                status = (boolean) params[0];
            }

            int size = mErrList.size();
            if (status) {
                if (!mErrList.contains(name)) {
                    mErrList.add(name);
                }
            } else {
                mErrList.remove(name);
            }
            isChanged = size != mErrList.size();
        }

        @Override
        public String getType() {
            if (mErrList.isEmpty()){
                return Definition.REQ_HW_E70_RECOVERY;
            }else {
                return Definition.REQ_HW_E70;
            }
        }

        @Override
        public String getParams() {
            if (mErrList.isEmpty()) {
                return Definition.REQ_HW_E70_RECOVERY;
            } else {
                return mErrList.toString();
            }
        }

        @Override
        public boolean isEnabled() {
            return super.isEnabled() && isChanged;
        }

        @Override
        public String getName() {
            return getString(R.string.e70_exception);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.e70_exception_tts);
        }
    },

    HARDWARE_MALFUNCTION(42) {
        private List<String> mErrList = new CopyOnWriteArrayList<>();
        private boolean isChanged = false;

        @Override
        public void update(Object reason, Object... params) {
            super.update(reason, params);
            String name = (String) reason;
            boolean status = false;
            if (params.length > 0) {
                status = (boolean) params[0];
            }

            int size = mErrList.size();
            if (status) {
                if (!mErrList.contains(name)) {
                    mErrList.add(name);
                }
            } else {
                mErrList.remove(name);
            }
            isChanged = size != mErrList.size();
        }

        @Override
        public String getType() {
            if (mErrList.isEmpty()) {
                return Definition.REQ_HW_RECOVERY;
            } else {
                return Definition.REQ_HW_MALFUNCTION;
            }
        }

        @Override
        public String getParams() {
            if (mErrList.isEmpty()) {
                return Definition.REQ_HW_RECOVERY;
            } else {
                return mErrList.toString();
            }
        }

        @Override
        public boolean isEnabled() {
            String disableLxcStr = SystemProperties.get(ApplicationWrapper.getContext(),
                    Definition.KEY_DISABLE_LXC, "0");
            boolean disableLxc = TextUtils.equals("1", disableLxcStr);
            return super.isEnabled() && isChanged && !disableLxc;
        }

        @Override
        public String getName() {
            return getString(R.string.hardware_malfunction);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.hardware_malfunction_tts);
        }
    },

    NAVI_SENSOR_STATE(43){

        private boolean isNaviSensorNormal = true;

        @Override
        public void update(Object status, Object... params) {
            super.update(status, params);
            if (status instanceof Boolean) {
                isNaviSensorNormal = (boolean) status;
            }
        }

        @Override
        public String getType() {
            return isNaviSensorNormal ? Definition.REQ_CHASSIS_SENSOR_NORMAL : Definition.REQ_CHASSIS_SENSOR_ERROR;
        }

        @Override
        public String getExitType() {
            return Definition.REQ_CHASSIS_SENSOR_NORMAL;
        }

        @Override
        public String getName() {
            return getString(R.string.navi_sensor_exception);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.navi_sensor_exception_tts);
        }

        @Override
        public String getParams() {
            return String.valueOf(isNaviSensorNormal);
        }
    },
    NAVI_LOAD_MAP(44) {
        @Override
        public String getType() {
            return Definition.REQ_LOAD_MAP;
        }

        @Override
        public String getName() {
            return getString(R.string.load_map);
        }
    },
    REMOTE_REPOSITION(45) {
        private boolean isPoseEstimate = false;
        private boolean isSetChargePile = true;
        private int reqId = 0;

        @Override
        public void update(Object reason, Object... params) {
            super.update(reason, params);
            if (params.length > 0) {
                switch ((String) reason) {
                    case InternalDef.SET_CHARGE_PILE:
                        isSetChargePile = (boolean) params[0];
                        break;
                    case InternalDef.POSE_ESTIMATE:
                        isPoseEstimate = (boolean) params[0];
                        break;
                    case InternalDef.SET_REMOTE_REQ_ID:
                        reqId = (int) params[0];
                        break;
                    default:
                        break;
                }
                Log.d("REMOTE_REPOSITION", "Update " + reason + " : " + params[0]);
            }
        }

        @Override
        public String getType() {
            return Definition.REQ_REMOTE_RELOCATE;
        }

        @Override
        public String getFailReason() {
            int strId = isSetChargePile ?
                    R.string.reposition_state_ok : R.string.reposition_noset_charge_position;
            return getString(strId);
        }

        @Override
        public boolean isEnabled() {
            Log.d("REMOTE_REPOSITION", "isPoseEstimate : " + isPoseEstimate
                    + " isSetChargePile : " + isSetChargePile);
            return super.isEnabled() && !isPoseEstimate && isSetChargePile;
        }

        @Override
        public String getParams() {
            JSONObject json = new JSONObject();
            try {
                json.put(Definition.REQ_SWITCH_OPEN_ID, reqId);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            return json.toString();
        }

        @Override
        public String getName() {
            return getString(R.string.remote_reposition);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.remote_reposition_tts);
        }
    },

    REMOTE_STOP_CHARGING(46) {
        private int reqId = 0;
        private String remoteCmd;
        private String param = null;

        @Override
        public void update(Object cmd, Object... params) {
            super.update(cmd, params);
            Log.d(TAG, "REMOTE_STOP_CHARGING cmd: " + cmd + " params : " + params);
            remoteCmd = (String) cmd;
            if (params.length > 0) {
                Log.d(TAG, "REMOTE_STOP_CHARGING cmd: " + cmd + " params0 : " + params[0]);
                reqId = (int) params[0];
            }
            if(params.length > 1){
                Log.d(TAG, "REMOTE_STOP_CHARGING cmd: " + cmd + " params1 : " + params[1]);
                if(params[1] != null){
                    param = params[1].toString();
                }else{
                    param = null;
                }
            }
        }

        @Override
        public String getParams() {
            Log.d(TAG, "REMOTE_STOP_CHARGING getParams reqId: " + reqId + " param : " + param);
            JSONObject json = new JSONObject();
            try {
                json.put("reqId", reqId);
                json.put("param", param);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            return json.toString();
        }

        @Override
        public String getType() {
            if (remoteCmd != null) {
                switch (remoteCmd) {
                    case Definition.REQ_REMOTE_STOP_CHARGING:
                        return Definition.REQ_SYSTEM_STOP_CHARGING;
                    case Definition.REQ_REMOTE_STOP_CHARGING_STATUS:
                        return Definition.REQ_SYSTEM_STOP_CHARGING_STATUS;
                }
            }
            return null;
        }

        @Override
        public String getName() {
            return getString(R.string.remote_stop_charging);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.remote_stop_charging_tts);
        }
    },

    LEAVE_PILE_GO_POINT(47) {
        @Override
        public void update(Object status, Object... params) {
            super.update(status, params);
        }

        @Override
        public String getType() {
            return Definition.REQ_LEAVE_PILE_GO_POINT;
        }

        @Override
        public String getName() {
            return getString(R.string.leave_pile_go_point);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.leave_pile_go_point_tts);
        }
    },

    REPOSITION(50) {

        private boolean isPoseEstimate = false;
        private boolean isSetChargePile = true;
        private boolean isVisionRelocate = false;
        private boolean haveCurNaviMap = true;
        private boolean isBootLocate = false;// 是否为开机后启动的重定位

        @Override
        public void update(Object reason, Object... params) {
            super.update(reason, params);
            isBootLocate = false; // 非开机启动时重新更新
            if (params.length > 0) {
                boolean value = (boolean) params[0];
                switch ((String) reason) {
                    case InternalDef.SET_CHARGE_PILE:
                        isSetChargePile = value;
                        break;

                    case InternalDef.POSE_ESTIMATE:
                        isPoseEstimate = value;
                        break;
                    case Definition.REPOSITION_VISION:
                        isVisionRelocate = value;
                        break;
                    case InternalDef.HAVE_CUR_NAVI_MAP:
                        haveCurNaviMap = value;
                        break;
                    case Definition.REPOSITION_BY_BOOT:
                        isBootLocate = value;
                        break;
                    default:
                        break;
                }
                Log.d("Reposition", "Update " + reason + " : " + params[0]);
            }
        }

        @Override
        public String getType() {
            return Definition.REQ_REPOSITION;
        }

        @Override
        public String getFailReason() {
            if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus()) {
                return null;
            }
            int strId = haveCurNaviMap ? (isSetChargePile ?
                    R.string.reposition_state_ok : R.string.reposition_noset_charge_position) :
                    R.string.reposition_please_switch_map;
            return getString(strId);
        }

        @Override
        public boolean isEnabled() {
            Log.d("Reposition", "Reposition:: isPoseEstimate=" + isPoseEstimate
                    + " isSetChargePile=" + isSetChargePile
                    + " haveCurNaviMap=" + haveCurNaviMap);
            return super.isEnabled() && !isPoseEstimate && haveCurNaviMap;
        }

        @Override
        public String getParams() {
            JSONObject json = new JSONObject();
            try {
                json.put(Definition.REPOSITION_VISION, isVisionRelocate);
                json.put(Definition.REPOSITION_BY_BOOT, isBootLocate);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            return json.toString();
        }

//        @Override
//        public String getLight() {
//            return Definition.LIGHT_REPOSITION;
//        }

        @Override
        public String getName() {
            return getString(R.string.reposition);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.reposition_tts);
        }
    },

     MAP_DRIFT(51) {
        private boolean isMapDrift = false;
        private boolean haveCurNaviMap = false;
         private boolean isPoseEstimate = false;

         @Override
         public void update(Object reason, Object... params) {
             super.update(reason, params);
             Log.d("MAP_DRIFT", "Update " + reason + " : " + params[0]);
             if (params.length > 0) {
                 boolean value = (boolean) params[0];
                 switch ((String) reason) {
                     case InternalDef.POSE_ESTIMATE:
                         isPoseEstimate = value;
                         if (isMapDrift && !isPoseEstimate) {
                             isMapDrift = false;
                         }
                         break;
                     case InternalDef.HAVE_CUR_NAVI_MAP:
                         haveCurNaviMap = value;
                         break;
                     case InternalDef.MAP_DRIFT:
                         isMapDrift = value;
                         break;
                     default:
                         break;
                 }
             }
         }

         @Override
         public String getType() {
             return Definition.REQ_MAP_DRIFT;
         }

         @Override
         public boolean isPersistent() {
             return isMapDrift & isPoseEstimate;
         }

         @Override
         public boolean isEnabled() {
             Log.d("MAP_DRIFT", "isMapDrift: " + isMapDrift + ", haveCurNaviMap: " + haveCurNaviMap + ", isPoseEstimate: " + isPoseEstimate);
             return isMapDrift & haveCurNaviMap & isPoseEstimate;
         }

         @Override
         public String getName() {
             return getString(R.string.map_drift);
         }

         @Override
         public String getI18nName() {
             return getString(R.string.map_drift_tts);
         }
     },

    OPEN_RADAR(54) {
        private boolean isAvailable = true;
        private String reason = "";

        @Override
        public void update(Object status, Object... params) {
            super.update(status, params);
            if (params.length > 0) {
                switch ((String) status) {
                    case InternalDef.UPDATE_RADAR_STATUS:
                        isAvailable = (boolean) params[0];
                        Log.d("Radar", "Update radar status:" + isAvailable);
                        break;
                    case InternalDef.UPDATE_OPEN_RADAR_REASON:
                        reason = (String) params[0];
                        Log.d("Radar", "Update open radar reason:" + reason);
                        break;
                    default:
                        break;
                }
            }
        }

        @Override
        public String getType() {
            return Definition.REQ_OPEN_RADAR;
        }

        @Override
        public String getExitType() {
            return Definition.REQ_OPEN_RADAR_SUCCEED;
        }

        @Override
        public boolean isEnabled() {
            Log.d("Radar", "Radar isEnabled : " + super.isEnabled()
                    + " isAvailable : " + isAvailable);
            return super.isEnabled() && !isAvailable;
        }

        @Override
        public String getParams() {
            JSONObject json = new JSONObject();
            try {
                json.put("status", isAvailable);
                json.put("reason", reason);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            return json.toString();
        }

        @Override
        public String getName() {
            return getString(R.string.open_radar);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.open_radar_tts);
        }
    },

    STOP_CHARGE(55) {

        private String reason = "";
        private String data;

        @Override
        public void update(Object status, Object... params) {
            super.update(status, params);
            if (status != null) {
                reason = String.valueOf(status);
            }

            if (params.length > 0) {
                data = (String) params[0];
            }
        }

        @Override
        public String getType() {
            if (Definition.CHARGE_SLOW.equals(reason)) {
                return Definition.REQ_STOP_CHARGING_BY_CHARGING_SLOW;
            }
            return null;
        }

        @Override
        public String getExitType() {
            return Definition.REQ_NORMAL;
        }

        @Override
        public String getParams() {
            JSONObject json = new JSONObject();
            try {
                json.put("reason", reason);
                json.put("data", data);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            return json.toString();
        }
    },

    WECHAT_START_CHARGE(58) {
        @Override
        public String getType() {
            return Definition.WECHAT_FIRST_RECHARGING;
        }

        @Override
        public boolean isPersistent() {
            return false;
        }

        @Override
        public String getParams() {
            JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty("reason", Definition.CHARGE_WECHAT);
            return jsonObject.toString();
        }

        @Override
        public String getName() {
            return getString(R.string.start_charge);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.start_charge_tts);
        }
    },

    BATTERY(60) {
        private BatteryStatus mStatus;
        private int level;
        private int bmsTemp;
        private boolean isUpgrade = false;

        @Override
        public void update(Object status, Object... params) {
            super.update(status, params);
            this.mStatus = (BatteryStatus) status;
            if (params.length > 1) {
                level = (int) params[0];
                bmsTemp = (int)params[1];
                isUpgrade = (boolean) params[2];
            }
        }

        @Override
        public String getType() {
            if (mStatus == null) {
                return null;
            }

            switch (mStatus) {
                case CHARGING:
                    return Definition.REQ_BATTERY_CHARGING;
                case FULL:
                    return Definition.REQ_BATTERY_FULL;

                default:
                    return null;
            }
        }

        @Override
        public String getParams() {
            JSONObject json = new JSONObject();
            try {
                json.put("level", level);
                json.put("bmsTemp", bmsTemp);
                json.put("isUpgrade", isUpgrade);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            return json.toString();
        }

        @Override
        public String getLight() {
            if (mStatus == null) {
                return null;
            }

            switch (mStatus) {
                case LOW:
                case CHARGING:
                    return Definition.LIGHT_CHARGING;
                case FULL:
                    return Definition.LIGHT_CHARGE_FULL;

                default:
                    return null;
            }
        }

        public boolean isEnabled() {
            return super.isEnabled()
                    && !RobotSetting.isEnableNavigationInCharging()
                    && CoreService.mCore.getBatteryManager().isCharging();
        }

        @Override
        public String getExitType() {
            return Definition.REQ_NORMAL;
        }

        @Override
        public String getName() {
            return getString(R.string.charging);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.charging_tts);
        }
    },

    WHEEL_OVER_STATUS(69) {

        private boolean isWheelOver_5 = false;// 是否过流5次

        @Override
        public void update(Object status, Object... params) {
            if (status instanceof Boolean) {
                isWheelOver_5 = (boolean) status;
            }
        }

        @Override
        public String getType() {
            return isWheelOver_5 ? Definition.REQ_WHEEL_OVER_DANGER : Definition.REQ_WHEEL_OVER_NORMAL;
        }

        @Override
        public String getLight() {
            return Definition.LIGHT_EMERGENCY;
        }

        @Override
        public String getName() {
            return getString(R.string.wheel_over_status);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.wheel_over_status_tts);
        }

        @Override
        public String getParams() {
            return "";
        }
    },


    START_CHARGE(70) {
        private String reason;
        private String data;
        private boolean isPoseEstimate = false;
        private boolean isSetChargePile = true;

        @Override
        public void update(Object reason, Object... params) {
            super.update(reason, params);
            Object param = null;

            if (params.length > 0) {
                param = params[0];
            }

            switch ((String) reason) {
                case InternalDef.SET_CHARGE_PILE:
                    if (param != null) {
                        isSetChargePile = (boolean) param;
                    }
                    break;

                case InternalDef.POSE_ESTIMATE:
                    if (param != null) {
                        isPoseEstimate = (boolean) param;
                    }
                    break;

                default:
                    this.reason = (String) reason;
                    data = (String) param;
                    break;
            }
        }

        @Override
        public String getType() {
            return Definition.REQ_START_CHARGE;
        }

        @Override
        public String getParams() {
            JSONObject json = new JSONObject();
            try {
                json.put("reason", reason);
                json.put("data", data);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            return json.toString();
        }

        @Override
        public boolean isEnabled() {
            if (!Definition.CHARGE_LOW.equals(reason) &&
                    RobotSetting.isSituServiceOpened()) {
                return false;
            }

            if (Definition.CHARGE_OTA.equals(reason)) {
                return true;
            }

            if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMeissa2()) {
                return super.isEnabled();
            }
            return super.isEnabled() && isSetChargePile;
        }

        @Override
        public String getFailReason() {
            if (RobotSetting.isSituServiceOpened()) {
                return getString(R.string.situ_service_opened);
            }

            if (!isSetChargePile) {
                return getString(R.string.need_charge_pile);
            }
            return super.getFailReason();
        }

        @Override
        public boolean isPersistent() {
            return Definition.CHARGE_LOW.equals(reason);// 如果低电量充电被打断后,依然还是低电量(即条件都是BatteryStatus.LOW状态),需要继续触发低电回充.
        }

        @Override
        public String getName() {
            return getString(R.string.start_charge);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.start_charge_tts);
        }
    },

    OUTSIDE_MAP(74) {
        private boolean isOutsideMap = false;
        private boolean isBackMap = false;
        @Override
        public void update(Object status, Object... params) {
            super.update(status, params);
            Log.d("OUTSIDE_MAP", "Update " + status + " : " + status);
            switch ((String)status) {
                case InternalDef.MAP_OUTSIDE_ENTER:
                    isBackMap = false;
                    isOutsideMap = true;
                    break;
                case InternalDef.MAP_OUTSIDE_LEAVE:
                    isBackMap = isOutsideMap || isBackMap;
                    isOutsideMap = false;
                    break;
            }
        }

        @Override
        public String getType() {
            return Definition.REQ_MAP_OUTSIDE;
        }

        @Override
        public boolean isEnabled() {
            Log.d("OUTSIDE_MAP", "isOutsideMap: " + isOutsideMap + " ,isBackMap: " + isBackMap + " ,super.isEnabled(): " + super.isEnabled());
            return super.isEnabled() && isOutsideMap;
        }

        @Override
        public boolean isPersistent() {
            return isOutsideMap;
        }

        @Override
        public String getName() {
            return getString(R.string.map_outside);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.map_outside_tts);
        }

        @Override
        public String getParams() {
            return super.getParams();
        }

        @Override
        public String getExitType() {
            return Definition.REQ_MAP_OUSIDE_RELEASE;
        }
    },

    REMOTE_CMD(75) {

        private String remoteCmd;
        private String remoteParams;

        @Override
        public void update(Object cmd, Object... params) {
            super.update(cmd, params);
            Object param = null;
            if (params.length > 0) {
                param = params[0];
            }

            remoteCmd = (String) cmd;
            if (param != null) {
                remoteParams = (String) param;
            }
        }

        @Override
        public String getParams() {
            return remoteParams;
        }

        @Override
        public String getType() {
            if (remoteCmd != null) {
                switch (remoteCmd) {
                    case Definition.REQ_REMOTE_GO_POSE:
                        return Definition.REQ_SYSTEM_GO_POSE;
                    case Definition.REQ_REMOTE_GO_POSITION:
                        return Definition.REQ_SYSTEM_GO_POSITION;
                    case Definition.REQ_REMOTE_STOP_NAVIGATION:
                        return Definition.REQ_SYSTEM_STOP_NAVIGATION;
                }
            }
            return null;
        }

        @Override
        public boolean isSystemControl() {
            return true;
        }

        @Override
        public String getName() {
            return getString(R.string.remote_task);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.remote_task_tts);
        }
    },

    BATTERY_LOW(80) {
        @Override
        public boolean isEnabled() {
            return super.isEnabled();
        }

        @Override
        public String getLight() {
            return Definition.LIGHT_BATTERY_LOW;
        }

        @Override
        public boolean isSystemControl() {
            return false;
        }
    },

    REMOTE_PUSH_MAP_NO_SWITCH(80) {
        private String type = Definition.REMOTE_PUSH_MAP_NO_SWITCH;
        private String reqParams;

        @Override
        public void update(Object status, Object... params) {
            switch ((String) status) {
                case Definition.JSON_TYPE:
                    type = (String) params[0];
                    break;
                case Definition.JSON_CONFIG:
                    reqParams = (String) params[0];
                    break;
                default:
                    break;
            }
        }

        @Override
        public String getType() {
            return type;
        }

        @Override
        public String getParams() {
            return reqParams;
        }

        @Override
        public boolean isSystemControl() {
            return false;
        }
    },

    LITE_LOCK(80) {

        String type = "system_remote_lock_dialog";

        private String paramsJsonStr;

        @Override
        public void update(Object status, Object... params) {
            switch ((String) status) {
                case Definition.JSON_TYPE:
                    type = (String) params[0];
                    break;
                case Definition.JSON_CONFIG:
                    paramsJsonStr = (String) params[0];
                    break;
                default:
                    break;
            }
        }

        @Override
        public String getType() {
            return type;
        }

        @Override
        public boolean isSystemControl() {
            return false;
        }

        @Override
        public String getParams() {
            return paramsJsonStr;
        }
    },

    BEING_PUSHED(85) {
        private boolean isBeingPushedInMap = false;

        @Override
        public void update(Object status, Object... params) {
            super.update(status, params);
            Log.d(TAG, "BEING_PUSHED update: status=" + status);
            switch ((String) status) {
                case InternalDef.BEING_PUSHED:
                    isBeingPushedInMap = true;
                    break;
                case InternalDef.BEING_PUSHED_RELEASE:
                    isBeingPushedInMap = false;
                    break;
            }
        }

        @Override
        public String getType() {
            return Definition.REQ_ROBOT_BEING_PUSHED;
        }

        @Override
        public boolean isEnabled() {
            Log.d(TAG, "BEING_PUSHED isEnabled:" +  super.isEnabled() + ", isBeingPushedInMap:" + isBeingPushedInMap);
            return super.isEnabled() && isBeingPushedInMap;
        }

        @Override
        public String getName() {
            return getString(R.string.being_pushed);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.being_pushed_tts);
        }


        @Override
        public String getExitType() {
            return Definition.REQ_ROBOT_BEING_PUSHED_RELEASE;
        }
    },

    MULTI_ROBOT_ERROR(87) {
        private int errorType = 0;

        @Override
        public void update(Object status, Object... params) {
            super.update(status, params);
            Log.d(TAG, "MULTI_ROBOT_ERROR update: status=" + status);
            switch ((String) status) {
                case InternalDef.MULTI_ERROR_TYPE:
                    errorType = (int) params[0];
                    break;
            }
            super.setEnabled(errorType != 0);
        }

        @Override
        public String getParams() {
            JSONObject json = new JSONObject();
            try {
                json.put("errorType", errorType);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            return json.toString();
        }

        @Override
        public String getType() {
            return Definition.REQ_MULTI_ROBOT_ERROR;
        }

        @Override
        public boolean isEnabled() {
            Log.d(TAG, "MULTI_ROBOT_ERROR isEnabled: isEnabled=" + super.isEnabled());
            return super.isEnabled();
        }

        @Override
        public String getName() {
            return getString(R.string.multi_robot_error);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.multi_robot_error_tts);
        }
    },

    TIME_WARNING(90) {

        private boolean notNotifyNow;
        private boolean isWarning;

        @Override
        public void update(Object status, Object... params) {
            super.update(status, params);
            if (status instanceof Boolean) {
                isWarning = (boolean) status;
            }
            if (params != null && params.length > 0) {
                notNotifyNow = (boolean) params[0];
            }
        }

        @Override
        public String getType() {
            if (!isWarning) {
                return Definition.REQ_SYSTEM_TIME_WARNING_STOP;
            }
            return Definition.REQ_SYSTEM_TIME_WARNING_START;
        }

        @Override
        public String getName() {
            return getString(R.string.time_warning);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.time_warning_tts);
        }

        @Override
        public boolean isEnabled() {
            return !notNotifyNow;
        }

        @Override
        public boolean isPersistent() {
            return false;
        }

        @Override
        public boolean isSystemControl() {
            return true;
        }
    },

    STOP_CHARGE_CONFIRM(95) {
        @Override
        public void update(Object status, Object... params) {
            super.update(status, params);
        }

        @Override
        public String getType() {
            return Definition.REQ_STOP_CHARGING_CONFIRM;
        }

        @Override
        public String getName() {
            return getString(R.string.stop_charge_confirm);
        }

        @Override
        public String getI18nName() {
            return getString(R.string.stop_charge_confirm_tts);
        }
    };

    private static final String TAG = SystemStatus.class.getSimpleName();
    private int priority;
    private boolean enabled = true;
    private boolean removed = false;

    private static SystemStatusReporter sReporter = new SystemStatusReporter();

    SystemStatus(int priority) {
        this.priority = priority;
    }

    public String getLight() {
        return null;
    }

    public boolean hasLight() {
        return !TextUtils.isEmpty(getLight());
    }

    public int getPriority() {
        return priority;
    }

    public String getType() {
        return null;
    }

    public String getExitType() {
        return null;
    }

    public String getParams() {
        return null;
    }

    public String getName() {
        return null;
    }

    public String getI18nName(){
        return "";
    }

    public String getDebugKey() {
        return "";
    }

    public void update(Object status, Object... params) {
        sReporter.reportStatusUpdate(toString(), String.valueOf(status), Arrays.toString(params));
    }

    public boolean isSystemControl() {
        return true;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isEnabled() {
        return enabled && !removed;
    }

    public boolean isRemoved() {
        return this.removed;
    }

    public boolean isPersistent() {
        return true;
    }

    public String getFailReason() {
        return null;
    }

    public void remove() {
        this.removed = true;
    }

    protected String getString(int strId) {
        return ApplicationWrapper.getContext().getString(strId);
    }

    public static SystemStatus get(String status) {
        switch (status) {
            case Definition.SYSTEM_AUTO_OTA:
                return SystemStatus.AUTO_OTA;

            case Definition.SYSTEM_EMERGENCY:
                return SystemStatus.EMERGENCY;

            case Definition.SYSTEM_BATTERY_LOW:
                return SystemStatus.BATTERY_LOW;

            case Definition.SYSTEM_BATTERY:
                return SystemStatus.BATTERY;

            case Definition.SYSTEM_DORMANCY:
                return SystemStatus.DORMANCY;

            case Definition.SYSTEM_RADAR:
                return SystemStatus.OPEN_RADAR;

            case Definition.SYSTEM_FUNCTION_KEY:
                return SystemStatus.FUNCTION_KEY;

            case Definition.SYSTEM_SHUTDOWN_TIMER:
                return SystemStatus.SHUTDOWN_TIMER;

            case Definition.SYSTEM_PUSH_WARNING:
                return SystemStatus.BEING_PUSHED;

            case Definition.SYSTEM_OUTSIDE_MAP:
                return SystemStatus.OUTSIDE_MAP;

            default:
                return null;
        }
    }

    @Override
    public String toString() {
        return "SystemStatus{"
                + "name:"
                + getName()
                + ", params:"
                + getParams()
                + ", light:"
                + getLight()
                + ", type:"
                + getType()
                + ", exitType:"
                + getExitType()
                + ", priority:"
                + priority
                + ", enabled:"
                + enabled
                + ", removed:"
                + removed
                + '}';
    }
}
