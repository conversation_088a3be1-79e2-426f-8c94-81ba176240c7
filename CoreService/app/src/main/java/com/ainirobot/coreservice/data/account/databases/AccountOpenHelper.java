package com.ainirobot.coreservice.data.account.databases;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;

import com.ainirobot.coreservice.data.account.bean.Tables;

public class AccountOpenHelper extends SQLiteOpenHelper {
    private static final String TAG = AccountOpenHelper.class.getSimpleName();
    private static final String DB_NAME = "account.db";
    private static final int DB_VERSION = 2;

    AccountOpenHelper(Context context) {
        super(context, DB_NAME, null, DB_VERSION);
        Log.i(TAG, "init");
        SQLiteDatabase db = getWritableDatabase();
        db.close();
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        Log.i(TAG, "create db");
        db.execSQL("CREATE TABLE " + Tables.TOKEN + " (" +
                Tables.TokenColumns.TOKEN_ID + " TEXT PRIMARY KEY," +
                Tables.TokenColumns.VALUE + " TEXT," +
                Tables.TokenColumns.EXPIRES_IN + " INTEGER DEFAULT -1," +
                Tables.TokenColumns.CREATE_TIME + " INTEGER DEFAULT -1" +
                ");");
        db.execSQL("CREATE TABLE " + Tables.USER + " (" +
                Tables.UserColumns.USER_ID + " TEXT PRIMARY KEY," +
                Tables.UserColumns.TOKEN_ID + " TEXT REFERENCES " + Tables.TOKEN +
                "(" + Tables.TokenColumns.TOKEN_ID + ")," +
                Tables.UserColumns.AVATAR_URL + " TEXT," +
                Tables.UserColumns.MOBILE + " TEXT," +
                Tables.UserColumns.NICK_NAME + " TEXT NOT NULL," +
                Tables.UserColumns.ROLE_ID + " INTEGER DEFAULT 0," +
                Tables.UserColumns.CREATE_TIME + " TEXT," +
                Tables.UserColumns.IS_RECENT + " INTEGER DEFAULT 0," +
                Tables.UserColumns.UPDATE_TIME + " INTEGER" +
                ");");
        db.execSQL("CREATE TABLE " + Tables.USER_DEVELOP + " (" +
                Tables.UserColumns.USER_ID + " TEXT PRIMARY KEY," +
                Tables.UserColumns.TOKEN_ID + " TEXT REFERENCES " + Tables.TOKEN +
                "(" + Tables.TokenColumns.TOKEN_ID + ")," +
                Tables.UserColumns.AVATAR_URL + " TEXT," +
                Tables.UserColumns.MOBILE + " TEXT," +
                Tables.UserColumns.NICK_NAME + " TEXT NOT NULL," +
                Tables.UserColumns.ROLE_ID + " INTEGER DEFAULT 0," +
                Tables.UserColumns.CREATE_TIME + " TEXT," +
                Tables.UserColumns.IS_RECENT + " INTEGER DEFAULT 0," +
                Tables.UserColumns.UPDATE_TIME + " INTEGER" +
                ");");
        db.execSQL("CREATE TABLE " + Tables.USER_PRE_RELEASE + " (" +
                Tables.UserColumns.USER_ID + " TEXT PRIMARY KEY," +
                Tables.UserColumns.TOKEN_ID + " TEXT REFERENCES " + Tables.TOKEN +
                "(" + Tables.TokenColumns.TOKEN_ID + ")," +
                Tables.UserColumns.AVATAR_URL + " TEXT," +
                Tables.UserColumns.MOBILE + " TEXT," +
                Tables.UserColumns.NICK_NAME + " TEXT NOT NULL," +
                Tables.UserColumns.ROLE_ID + " INTEGER DEFAULT 0," +
                Tables.UserColumns.CREATE_TIME + " TEXT," +
                Tables.UserColumns.IS_RECENT + " INTEGER DEFAULT 0," +
                Tables.UserColumns.UPDATE_TIME + " INTEGER" +
                ");");
        db.execSQL("CREATE TABLE " + Tables.FACE + " (" +
                Tables.FaceColumns.FACE_ID + " TEXT PRIMARY KEY," +
                Tables.FaceColumns.USER_ID + " TEXT REFERENCES " + Tables.USER +
                "(" + Tables.UserColumns.USER_ID + ")," +
                Tables.FaceColumns.FEATURE + " TEXT," +
                Tables.FaceColumns.NAME + " TEXT NOT NULL," +
                Tables.FaceColumns.PICTURE_URI + " TEXT NOT NULL," +
                Tables.FaceColumns.IS_ONLY_LOCAL + " INTEGER DEFAULT 0," +
                Tables.FaceColumns.IS_DELETE + " INTEGER DEFAULT 0," +
                Tables.FaceColumns.REGISTER_TIME + " INTEGER" +
                ");");
        db.execSQL("CREATE TABLE " + Tables.FACE_DEVELOP + " (" +
                Tables.FaceColumns.FACE_ID + " TEXT PRIMARY KEY," +
                Tables.FaceColumns.USER_ID + " TEXT REFERENCES " + Tables.USER +
                "(" + Tables.UserColumns.USER_ID + ")," +
                Tables.FaceColumns.FEATURE + " TEXT," +
                Tables.FaceColumns.NAME + " TEXT NOT NULL," +
                Tables.FaceColumns.PICTURE_URI + " TEXT NOT NULL," +
                Tables.FaceColumns.IS_ONLY_LOCAL + " INTEGER DEFAULT 0," +
                Tables.FaceColumns.IS_DELETE + " INTEGER DEFAULT 0," +
                Tables.FaceColumns.REGISTER_TIME + " INTEGER" +
                ");");
        db.execSQL("CREATE TABLE " + Tables.FACE_PRE_RELEASE + " (" +
                Tables.FaceColumns.FACE_ID + " TEXT PRIMARY KEY," +
                Tables.FaceColumns.USER_ID + " TEXT REFERENCES " + Tables.USER +
                "(" + Tables.UserColumns.USER_ID + ")," +
                Tables.FaceColumns.FEATURE + " TEXT," +
                Tables.FaceColumns.NAME + " TEXT NOT NULL," +
                Tables.FaceColumns.PICTURE_URI + " TEXT NOT NULL," +
                Tables.FaceColumns.IS_ONLY_LOCAL + " INTEGER DEFAULT 0," +
                Tables.FaceColumns.IS_DELETE + " INTEGER DEFAULT 0," +
                Tables.FaceColumns.REGISTER_TIME + " INTEGER" +
                ");");
        db.execSQL("CREATE TABLE " + Tables.META + " (" +
                Tables.MetaColumns.MODULE + " TEXT PRIMARY KEY," +
                Tables.MetaColumns.VALUE + " TEXT" +
                ");");
        db.execSQL("DROP TRIGGER IF EXISTS " + Tables.USER + "_deleted;");
        db.execSQL("CREATE TRIGGER " + Tables.USER + "_deleted " +
                "     BEFORE DELETE ON " + Tables.USER +
                "   BEGIN " +
                "     DELETE FROM " + Tables.FACE +
                "       WHERE " + Tables.FaceColumns.USER_ID +
                "         =OLD." + Tables.UserColumns.USER_ID +
                ";  END");
        db.execSQL("DROP TRIGGER IF EXISTS " + Tables.USER_DEVELOP + "_deleted;");
        db.execSQL("CREATE TRIGGER " + Tables.USER_DEVELOP + "_deleted " +
                "     BEFORE DELETE ON " + Tables.USER_DEVELOP +
                "   BEGIN " +
                "     DELETE FROM " + Tables.FACE_DEVELOP +
                "       WHERE " + Tables.FaceColumns.USER_ID +
                "         =OLD." + Tables.UserColumns.USER_ID +
                ";  END");
        db.execSQL("DROP TRIGGER IF EXISTS " + Tables.USER_PRE_RELEASE + "_deleted;");
        db.execSQL("CREATE TRIGGER " + Tables.USER_PRE_RELEASE + "_deleted " +
                "     BEFORE DELETE ON " + Tables.USER_PRE_RELEASE +
                "   BEGIN " +
                "     DELETE FROM " + Tables.FACE_PRE_RELEASE +
                "       WHERE " + Tables.FaceColumns.USER_ID +
                "         =OLD." + Tables.UserColumns.USER_ID +
                ";  END");
        db.execSQL("DROP TRIGGER IF EXISTS " + Tables.TOKEN + "_deleted;");
        db.execSQL("CREATE TRIGGER " + Tables.TOKEN + "_deleted " +
                "     BEFORE DELETE ON " + Tables.TOKEN +
                "   BEGIN " +
                "     UPDATE " + Tables.USER +
                "       SET " + Tables.UserColumns.TOKEN_ID + "=NULL" +
                "       WHERE " + Tables.UserColumns.TOKEN_ID +
                "         =OLD." + Tables.TokenColumns.TOKEN_ID +
                ";    UPDATE " + Tables.USER_DEVELOP +
                "       SET " + Tables.UserColumns.TOKEN_ID + "=NULL" +
                "       WHERE " + Tables.TokenColumns.TOKEN_ID +
                "         =OLD." + Tables.TokenColumns.TOKEN_ID +
                ";    UPDATE " + Tables.USER_PRE_RELEASE +
                "       SET " + Tables.UserColumns.TOKEN_ID + "=NULL" +
                "       WHERE " + Tables.UserColumns.TOKEN_ID +
                "         =OLD." + Tables.TokenColumns.TOKEN_ID +
                ";  END");
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        Log.i(TAG, "upgrade db oldVersion: " + oldVersion + ", newVersion: " + newVersion);
        if (oldVersion <= 1) {
            db.execSQL("DROP TABLE IF EXISTS " + Tables.USER + ";");
            db.execSQL("DROP TABLE IF EXISTS " + Tables.USER_DEVELOP + ";");
            db.execSQL("DROP TABLE IF EXISTS " + Tables.USER_PRE_RELEASE + ";");
            db.execSQL("DROP TABLE IF EXISTS " + Tables.FACE + ";");
            db.execSQL("DROP TABLE IF EXISTS " + Tables.FACE_DEVELOP + ";");
            db.execSQL("DROP TABLE IF EXISTS " + Tables.FACE_PRE_RELEASE + ";");
            db.execSQL("DROP TABLE IF EXISTS " + Tables.TOKEN + ";");
            db.execSQL("DROP TABLE IF EXISTS " + Tables.META + ";");
            onCreate(db);
        }
    }

    @Override
    public void onDowngrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        Log.i(TAG, "downgrade db oldVersion: " + oldVersion + ", newVersion: " + newVersion);
        db.execSQL("DROP TABLE IF EXISTS " + Tables.USER + ";");
        db.execSQL("DROP TABLE IF EXISTS " + Tables.USER_DEVELOP + ";");
        db.execSQL("DROP TABLE IF EXISTS " + Tables.USER_PRE_RELEASE + ";");
        db.execSQL("DROP TABLE IF EXISTS " + Tables.FACE + ";");
        db.execSQL("DROP TABLE IF EXISTS " + Tables.FACE_DEVELOP + ";");
        db.execSQL("DROP TABLE IF EXISTS " + Tables.FACE_PRE_RELEASE + ";");
        db.execSQL("DROP TABLE IF EXISTS " + Tables.TOKEN + ";");
        db.execSQL("DROP TABLE IF EXISTS " + Tables.META + ";");
        onCreate(db);
    }
}
