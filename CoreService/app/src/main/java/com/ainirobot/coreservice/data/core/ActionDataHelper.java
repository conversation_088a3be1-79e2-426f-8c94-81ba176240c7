/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.data.core;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import android.provider.BaseColumns;
import android.text.TextUtils;

import com.ainirobot.coreservice.action.ActionInfo;
import com.ainirobot.coreservice.data.provider.BaseDataHelper;
import com.ainirobot.coreservice.data.provider.Column.Constraint;
import com.ainirobot.coreservice.data.provider.Column.DataType;
import com.ainirobot.coreservice.data.provider.SQLiteTable;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ActionDataHelper extends BaseDataHelper {
    public static final SQLiteTable TABLE = new SQLiteTable(ActionField.TABLE_NAME,
            DataProvider.AUTHORITY, true) {
        @Override
        public void importData(Context context, SQLiteDatabase db) {
            DataImportUtils.importDataFromAsset(context, db, "Action.sql");
        }

        @Override
        public void update(Context context, SQLiteDatabase db, int oldVersion, int newVersion) {
            DataImportUtils.importDataFromAsset(context, db, "Action.sql");
        }
    };

    static {
        TABLE.addColumn(ActionField.ACTION_ID, Constraint.UNIQUE, DataType.INTEGER)
                .addColumn(ActionField.ACTION_NAME, DataType.TEXT)
                .addColumn(ActionField.IS_COMMON, DataType.INTEGER)
                .addColumn(ActionField.RESOURCE, DataType.TEXT)
                .addColumn(ActionField.PERMISSION, DataType.INTEGER);
    }


    public ActionDataHelper(Context context) {
        super(context);
    }

    @Override
    public Uri getContentUri() {
        return TABLE.getContentUri();
    }

    private ContentValues getContentValues(ActionInfo info) {
        ContentValues values = new ContentValues();
        values.put(ActionField.ACTION_NAME, info.getActionName());
        values.put(ActionField.ACTION_ID, info.getActionId());
        values.put(ActionField.IS_COMMON, info.isCommon() ? 1 : 0);
        values.put(ActionField.PERMISSION, info.isSystem() ? 1 : 0);
        if (info.getResource() != null) {
            values.put(ActionField.RESOURCE, Arrays.toString(info.getResource()));
        }
        return values;
    }

    public void bulkInsert(List<ActionInfo> infos) {
        ArrayList<ContentValues> contentValues = new ArrayList<>();
        for (ActionInfo data : infos) {
            ContentValues values = getContentValues(data);
            contentValues.add(values);
        }

        ContentValues[] valueArray = new ContentValues[contentValues.size()];
        bulkInsert(contentValues.toArray(valueArray));
    }

    public void insert(ActionInfo info) {
        ContentValues values = getContentValues(info);
        insert(values);
    }

    public List<ActionInfo> getAllAction() {
        List<ActionInfo> actionInfos = new ArrayList<>();
        Cursor cursor = query(null, null, null, null);
        if (cursor == null) {
            return actionInfos;
        }
        while (cursor.moveToNext()) {
            ActionInfo info = new ActionInfo();
            info.setActionId(cursor.getInt(cursor.getColumnIndex(ActionField.ACTION_ID)));
            info.setActionName(cursor.getString(cursor.getColumnIndex(ActionField.ACTION_NAME)));
            info.setCommon(cursor.getInt(cursor.getColumnIndex(ActionField.IS_COMMON)) == 1);
            info.setSystem(cursor.getInt(cursor.getColumnIndex(ActionField.PERMISSION)) == 1);
            String resources = cursor.getString(cursor.getColumnIndex(ActionField.RESOURCE));
            if (!TextUtils.isEmpty(resources)) {
                info.setResource(resources.substring(1, resources.length() - 1).split(","));
            }
            actionInfos.add(info);
        }
        cursor.close();
        return actionInfos;
    }

    public static final class ActionField implements BaseColumns {
        static final String TABLE_NAME = "actionInfo";

        static final String ACTION_NAME = "actionName";
        static final String ACTION_ID = "actionId";
        static final String IS_COMMON = "isCommon";
        static final String RESOURCE = "resource";
        static final String PERMISSION = "permission";
    }

}
