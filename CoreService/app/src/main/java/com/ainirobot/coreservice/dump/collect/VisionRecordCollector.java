package com.ainirobot.coreservice.dump.collect;

import android.os.Environment;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.dump.manager.DataDumpManager;
import com.ainirobot.coreservice.utils.DelayTask;

/**
 * Ten seconds of video.(.mp4)
 * Created by Orion on 2020/5/25.
 */
public class VisionRecordCollector extends AbstractCollector {
    private static final String TAG = VisionRecordCollector.class.getSimpleName();

    private static final Object TAG_RECORD = new Object();
    private static final long RECORD_INTERVAL = 10 * 1000;
    private static final String PATH = Environment.getExternalStorageDirectory()
            + "/Download/vision_record/";

    private String mPath = PATH + System.currentTimeMillis() + ".mp4";

    public VisionRecordCollector(DataDumpManager mManager) {
        super(mManager);
    }

    @Override
    protected int getPermission() {
        return RobotSettingApi.getInstance().getRobotInt(Definition.DATA_DUMP_RIGHTS_VISION_RECORD);
    }

    @Override
    protected boolean startCollect(String param) {
        Log.d(TAG, "startCollect : param=" + param);
        startVisionRecord();
        return true;
    }

    @Override
    protected boolean stopCollect() {
        Log.d(TAG, "stopCollect");
        DelayTask.cancel(TAG_RECORD);
        stopVisionRecord();
        return true;
    }

    private void startVisionRecord() {
        mPath = PATH + System.currentTimeMillis() + ".mp4";
        Log.d(TAG, "startVisionRecord : mPath=" + mPath);
        mSystemApi.startVisionRecord(mReq, mPath, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(TAG, "startVisionRecord onResult : " + result + ", " + message);
                startRecordTimer();
            }
        });
    }

    private void startRecordTimer() {
        DelayTask.submit(TAG_RECORD, new Runnable() {
            @Override
            public void run() {
                stopVisionRecord();
            }
        }, RECORD_INTERVAL);
    }

    private void stopVisionRecord() {
        Log.d(TAG, "stopVisionRecord");
        mSystemApi.stopVisionRecord(mReq, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(TAG, "stopVisionRecord onResult : " + result + ", " + message);
                if (isRunning()) {
                    startVisionRecord();
                }
            }
        });
    }

}
