/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.data.provider;

import android.content.ContentProvider;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.UriMatcher;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.database.sqlite.SQLiteQueryBuilder;
import android.net.Uri;
import android.provider.BaseColumns;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.util.Log;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public abstract class BaseProvider extends ContentProvider {
    private static final String TAG = BaseProvider.class.getSimpleName();

    private final UriMatcher sUriMatcher;
    private final Map<Integer, SQLiteTable> sTables;

    static final Object DBLock = new Object();
    protected SQLiteOpenHelper mDBHelper;

    public BaseProvider() {
        this.sUriMatcher = new UriMatcher(UriMatcher.NO_MATCH);
        this.sTables = new ConcurrentHashMap<>();

        List<SQLiteTable> tables = getTables();
        if (tables != null) {
            for (int index = 0; index < tables.size(); index++) {
                SQLiteTable table = tables.get(index);
                sUriMatcher.addURI(table.getAuthority(), table.getTableName(), index);
                sTables.put(index, table);
            }
        }
    }

    @Override
    public boolean onCreate() {
        this.mDBHelper = getDBHelper();
        return false;
    }

    public abstract List<SQLiteTable> getTables();

    public abstract SQLiteOpenHelper getDBHelper();

    @Override
    public String getType(@NonNull Uri uri) {
        int code = sUriMatcher.match(uri);
        Log.d(TAG, "getType code = " + code);
        SQLiteTable table = sTables.get(code);
        if (table != null) {
            return table.getContentType();
        }
        return null;
    }

    protected String matchTable(Uri uri) {
        int code = sUriMatcher.match(uri);
        SQLiteTable table = sTables.get(code);
        
        Log.d(TAG, "matchTable uri = " + uri.getAuthority() + " LastPathSegment=" + uri.getLastPathSegment());
        Log.d(TAG, "matchTable code = " + code + "table is Null = " + (table == null));
        
        Log.d(TAG, "=== sTables content ===");
        for (Map.Entry<Integer, SQLiteTable> entry : sTables.entrySet()) {
            Log.d(TAG, "Key: " + entry.getKey() + ", Value: " + entry.getValue().getTableName());
        }
        Log.d(TAG, "=== end sTables content ===");
        
        if (table != null) {
            return table.getTableName();
        }
        return null;
    }

    @Override
    public Cursor query(@NonNull Uri uri, String[] projection,
                        String selection, String[] selectionArgs, String sortOrder) {
        synchronized (DBLock) {
            SQLiteQueryBuilder queryBuilder = new SQLiteQueryBuilder();
            String table = matchTable(uri);
            Log.d(TAG, "query table = " + table);
            queryBuilder.setTables(table);

            SQLiteDatabase db = mDBHelper.getReadableDatabase();
            Log.d(TAG, "query db = " + db.getPath()  + ", projection = "
                    + Arrays.toString(projection) + " selection = " + selection + " selectionArgs = "
                    + Arrays.toString(selectionArgs) + " sortOrder = " + sortOrder);

            Cursor cursor = queryBuilder.query(db,
                    projection,
                    selection,
                    selectionArgs,
                    null,
                    null,
                    sortOrder
            );

            if (getContext() == null || cursor == null) {
                return null;
            }
            cursor.setNotificationUri(getContext().getContentResolver(), uri);
            return cursor;
        }
    }

    @Nullable
    @Override
    public Uri insert(@NonNull Uri uri, @Nullable ContentValues values) {
        return insert(uri, values, true);
    }

    public Uri insert(@NonNull Uri uri, ContentValues values, boolean isNotifyChange) throws SQLException {
        synchronized (DBLock) {
            String table = matchTable(uri);
            SQLiteDatabase db = mDBHelper.getWritableDatabase();
            long rowId = 0;
            db.beginTransaction();
            try {
                rowId = db.insertWithOnConflict(table, null, values, SQLiteDatabase.CONFLICT_REPLACE);
                db.setTransactionSuccessful();
            } catch (Exception e) {
                Log.e(TAG, e.getMessage());
            } finally {
                db.endTransaction();
            }
            if (rowId > 0) {
                Uri returnUri = ContentUris.withAppendedId(uri, rowId);
                if (getContext() == null) {
                    return null;
                }
                if (isNotifyChange) {
                    getContext().getContentResolver().notifyChange(uri, null);
                }
                return returnUri;
            }
            throw new SQLException("Failed to insert row into " + uri);
        }
    }

    @Override
    public int bulkInsert(@NonNull Uri uri, @NonNull ContentValues[] values) {
        return bulkInsert(uri, values, true);
    }

    public int bulkInsert(@NonNull Uri uri, @NonNull ContentValues[] values, boolean isNotifyChange) {
        synchronized (DBLock) {
            String table = matchTable(uri);
            SQLiteDatabase db = mDBHelper.getWritableDatabase();
            db.beginTransaction();
            try {
                for (ContentValues contentValues : values) {
                    db.insertWithOnConflict(table, BaseColumns._ID, contentValues,
                            SQLiteDatabase.CONFLICT_IGNORE);
                }
                db.setTransactionSuccessful();
                if (getContext() == null) {
                    return -1;
                }

                if (isNotifyChange) {
                    getContext().getContentResolver().notifyChange(uri, null);
                }
                return values.length;
            } catch (Exception e) {
                Log.e(TAG, e.getMessage());
            } finally {
                db.endTransaction();
            }
            throw new SQLException("Failed to insert row into " + uri);
        }
    }

    @Override
    public int delete(@NonNull Uri uri, @Nullable String selection, @Nullable String[] selectionArgs) {
        return delete(uri, selection, selectionArgs, true);
    }

    public int delete(@NonNull Uri uri, String selection, String[] selectionArgs, boolean isNotifyChange) {
        synchronized (DBLock) {
            SQLiteDatabase db = mDBHelper.getWritableDatabase();

            int count = 0;
            String table = matchTable(uri);
            db.beginTransaction();
            try {
                count = db.delete(table, selection, selectionArgs);
                db.setTransactionSuccessful();
            } finally {
                db.endTransaction();
            }

            if (getContext() == null) {
                return -1;
            }
            if (isNotifyChange) {
                getContext().getContentResolver().notifyChange(uri, null);
            }
            return count;
        }
    }


    @Override
    public int update(@NonNull Uri uri, ContentValues values,
                      String selection, String[] selectionArgs) {
        synchronized (DBLock) {
            SQLiteDatabase db = mDBHelper.getWritableDatabase();
            int count;
            String table = matchTable(uri);
            db.beginTransaction();
            try {
                count = db.update(table, values, selection, selectionArgs);
                db.setTransactionSuccessful();
            } finally {
                db.endTransaction();
            }

            if (getContext() == null) {
                return -1;
            }
            return count;
        }
    }


    public int update(@NonNull Uri uri, ContentValues values,
                      String selection, String[] selectionArgs, boolean isNotifyChange) {
        synchronized (DBLock) {
            SQLiteDatabase db = mDBHelper.getWritableDatabase();
            int count;
            String table = matchTable(uri);
            db.beginTransaction();
            try {
                count = db.update(table, values, selection, selectionArgs);
                db.setTransactionSuccessful();
            } finally {
                db.endTransaction();
            }

            if (getContext() == null) {
                return -1;
            }
            if (isNotifyChange) {
                getContext().getContentResolver().notifyChange(uri, null);
            }
            return count;
        }
    }
}
