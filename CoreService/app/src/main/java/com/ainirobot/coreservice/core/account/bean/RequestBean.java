package com.ainirobot.coreservice.core.account.bean;

import com.ainirobot.coreservice.listener.IActionListener;

public class RequestBean {

    private IActionListener listener;
    private String userId;
    private String userToken;

    public RequestBean(String userId, String userToken, IActionListener listener) {
        this.userId = userId;
        this.userToken = userToken;
        this.listener = listener;
    }

    public String getUserId() {
        return userId;
    }

    public String getUserToken() {
        return userToken;
    }

    public IActionListener getListener() {
        return listener;
    }
}
