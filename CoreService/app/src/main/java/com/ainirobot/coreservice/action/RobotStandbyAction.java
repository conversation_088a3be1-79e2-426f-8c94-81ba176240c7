package com.ainirobot.coreservice.action;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.media.AudioManager;
import android.os.Handler;
import android.os.Looper;
import android.os.PowerManager;
import android.os.SystemClock;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.bi.report.BiTopIRStatusReport;
import com.ainirobot.coreservice.bi.report.RadarReport;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.RobotStandbyBean;
import com.ainirobot.coreservice.config.ConfigManager;
import com.ainirobot.coreservice.config.CoreConfig;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.core.status.LocalSubscriber;
import com.ainirobot.coreservice.core.status.StatusManager;
import com.ainirobot.coreservice.utils.BrightUtils;
import com.ainirobot.coreservice.utils.SettingDataHelper;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;

public class RobotStandbyAction extends AbstractAction<RobotStandbyBean> {
    private static final String TAG = RobotStandbyAction.class.getSimpleName();
    /**
     * 地图是否有Target信息，-1初始化未知状态 0不存在  1存在
     */
    private static final int MAP_TARGET_UNKNOWN = -1;
    private static final int MAP_TARGET_NONEXISTENT = 0;
    private static final int MAP_TARGET_EXIST = 1;

    private static final int DEFAULT_BRIGHTNESS = 0;
    private int brightness;
    private volatile int mTargetState = MAP_TARGET_UNKNOWN;
    private CoreConfig.StandbyConfig mStandbyConfig;
    private String odoMeterId;
    private double mLastLeftAcc = 0;
    private double mTotalLeftAcc = 0;
    private double mLastRightAcc = 0;
    private double mTotalRightAcc = 0;
    private boolean mIsRadarEnableInStandby = false;

    protected RobotStandbyAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_ROBOT_STANDBY, resList);
    }

    @Override
    protected boolean startAction(RobotStandbyBean standbyBean, LocalApi api) {
        Log.d(TAG, "startAction enter " + standbyBean.toString());
        mStandbyConfig = ConfigManager.getStandbyConfig();
        mIsRadarEnableInStandby = SettingDataHelper.getInstance().isRadarEnableInStandby();
        Log.d(TAG, "startAction: " + mStandbyConfig);
        if (null == mStandbyConfig) {
            Log.e(TAG, "standby config is null ");
            return false;
        }
        doStandbyBean(true, mStandbyConfig);
        Log.d(TAG, "startAction exit");
        return true;
    }

    private void doStandbyBean(boolean standbyStart, CoreConfig.StandbyConfig standbyConfig) {
        Log.d(TAG, "doStandbyBean standbyStart:" + standbyStart + " config:" + standbyConfig);
        Log.d(TAG, "doStandbyBean radar enable in standby: " + mIsRadarEnableInStandby);
//        if (standbyConfig.isDisable_depth_ir_camera()) {
//            mApi.updateChassisCameraEnableState(mReqId, Definition.ChassisCameraType.TYPE_DEPTH, !standbyStart);
//        }
        if (standbyStart) {
            mApi.getMapInfo(mReqId, null);
        } else {
            disableHardware(false);
        }
        if (standbyConfig.isMute_music()) {
            muteMusic(standbyStart);
        }
        if (standbyConfig.isScreen_brightness()) {
            screenBrightness(standbyStart);
        }
        if (standbyConfig.isDisable_vision()) {
            disableVision(standbyStart);
        }
        if (standbyConfig.isDisable_speech()) {
            disableSpeech(standbyStart);
        }
        if (standbyConfig.isGoto_sleep()) {
            goToSleep(standbyStart);
        }
        if (standbyConfig.isPower_off()) {
            powerOff(standbyStart);
        }

    }

    private void disableHardware(boolean isTaskStart) {
        Log.d(TAG, "disableHardware isRunning:" + isRunning() + " isTaskStart:" + isTaskStart);
        HashMap<String, Object> hardwareMaps = new HashMap<>();
        if (mStandbyConfig.isModule_bit_rgb_led()) {
            hardwareMaps.put(Definition.JSON_STANDBY_BIT_RGB_LED,
                    Definition.JSON_STANDBY_BIT_RGB_LED);
        }
        /**
         * 逻辑调整为：如果当前地图无target数据只做关闭操作，不处理恢复，
         * 如果有Target数据或查询超时，正常控制TopIR的开关操作。
         */
        if (mStandbyConfig.isModule_bit_ir_led()) {
            if (mTargetState == MAP_TARGET_NONEXISTENT) {
                if (isTaskStart) {
                    hardwareMaps.put(Definition.JSON_STANDBY_BIT_IR_LED,
                            Definition.JSON_STANDBY_BIT_IR_LED);
                    sendTopIrStatusReport(BiTopIRStatusReport.STATUS_CLOSE, BiTopIRStatusReport.MAP_TYPE_TARGET);
                }
            } else {
                hardwareMaps.put(Definition.JSON_STANDBY_BIT_IR_LED,
                        Definition.JSON_STANDBY_BIT_IR_LED);
                if (isTaskStart) {
                    sendTopIrStatusReport(BiTopIRStatusReport.STATUS_CLOSE, BiTopIRStatusReport.MAP_TYPE_TARGET);
                } else {
                    sendTopIrStatusReport(BiTopIRStatusReport.STATUS_OPEN, BiTopIRStatusReport.MAP_TYPE_TARGET);
                }
            }
        }
        //TODO 所有雷达操作需要通过Navigation控制，不能通过底盘控制
//        if(!mIsRadarEnableInStandby){
//            if (mStandbyConfig.isModule_bit_radar()) {
//                updateRadar(!isTaskStart);
////            hardwareMaps.put(Definition.JSON_STANDBY_BIT_RADAR,
////                    Definition.JSON_STANDBY_BIT_RADAR);
//            }
//        }else{
//            Log.d(TAG,"radar enable in Standby, donot close radar");
//        }

        if (mStandbyConfig.isModule_bit_wheel()) {
            hardwareMaps.put(Definition.JSON_STANDBY_BIT_WHEEL,
                    Definition.JSON_STANDBY_BIT_WHEEL);
        }
        if (!hardwareMaps.isEmpty()) {
            hardwareMaps.put(Definition.JSON_STANDBY_START, isTaskStart);
            mApi.setPowerLpm(mReqId, mGson.toJson(hardwareMaps));
        }
    }

    private void updateRadar(boolean openRadar) {
        if (openRadar) {
            removeOdoMeterStatusListener();
            mApi.updateRadarStatus(mReqId, true);
            new RadarReport().addEvent(RadarReport.EVENT_OPEN).addType(RadarReport.TYPE_STANDBY)
                    .report();
        } else {
            mApi.updateRadarStatus(mReqId, false);
            setOdoMeterStatusListener();
            new RadarReport().addEvent(RadarReport.EVENT_CLOSE).addType(RadarReport.TYPE_STANDBY)
                    .report();
        }
    }

    private void screenBrightness(boolean standbyStart) {
        //RobotSetting已经增加mode判断，因此这里不加
        if (standbyStart) {
            try {
                //设置亮度手动调节
                BrightUtils.setScreenManualMode();
                brightness = Settings.System.getInt(ApplicationWrapper.getContext()
                        .getContentResolver(), Settings.System.SCREEN_BRIGHTNESS);
                Log.d(TAG,"current brightness: " + brightness);
                boolean result = Settings.System.putInt(ApplicationWrapper.getContext().getContentResolver(),
                        Settings.System.SCREEN_BRIGHTNESS, DEFAULT_BRIGHTNESS);
                Log.d(TAG,"update brightness result: " + result);
                Log.d(TAG,"after update brightness: " + Settings.System.getInt(ApplicationWrapper.getContext()
                        .getContentResolver(), Settings.System.SCREEN_BRIGHTNESS));
                mManager.getRobotSettingManager().setRobotSetting(Definition.ROBOT_SETTING_SCREEN_LIGHT_BEFOR_STANDBY, String.valueOf(brightness));
            } catch (Settings.SettingNotFoundException e) {
                e.printStackTrace();
            }
        } else {
            try {
                //设置亮度自动调节
                //BrightUtils.setScreenAutoMode();
                int current_brightness = Settings.System.getInt(ApplicationWrapper.getContext()
                        .getContentResolver(), Settings.System.SCREEN_BRIGHTNESS);
                //避免中途用户更改过屏幕亮度
                if (current_brightness == DEFAULT_BRIGHTNESS) {
                    Settings.System.putInt(ApplicationWrapper.getContext().getContentResolver(),
                            Settings.System.SCREEN_BRIGHTNESS, brightness);
                }
                mManager.getRobotSettingManager().setRobotSetting(Definition.ROBOT_SETTING_SCREEN_LIGHT_BEFOR_STANDBY, String.valueOf(-1));
            } catch (Settings.SettingNotFoundException e) {
                e.printStackTrace();
            }
        }

    }

    /**
     * 是否关机
     *
     * @param powerOff true
     */
    private void powerOff(boolean powerOff) {
        if (powerOff) {
            ApplicationWrapper.getContext()
                    .sendStickyBroadcast(new Intent("com.ainirobot.canservice.action.DISABLE_POWER_BUTTON"));
        } else {
            ApplicationWrapper.getContext()
                    .removeStickyBroadcast(new Intent("com.ainirobot.canservice.action.DISABLE_POWER_BUTTON"));
        }
    }

    /**
     * 是否禁用visionSDK
     *
     * @param disalbeVison true 禁用
     */
    private void disableVision(boolean disalbeVison) {
        if (disalbeVison) {
            mApi.stopVision(mReqId);
        } else {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException interruptedException) {
                Log.d(TAG, "sleep err:" + interruptedException.getMessage());
            }
            mApi.startVision(mReqId);
        }
    }

    /**
     * 是否静音音乐媒体
     *
     * @param muteMusic true 静音
     */
    private void muteMusic(boolean muteMusic) {
        AudioManager audioManager = (AudioManager) ApplicationWrapper.getContext()
                .getSystemService(Context.AUDIO_SERVICE);
        if (audioManager != null) {
            if (muteMusic) {
                audioManager.adjustStreamVolume(AudioManager.STREAM_MUSIC, AudioManager.ADJUST_MUTE, 0);
            } else {
                audioManager.adjustStreamVolume(AudioManager.STREAM_MUSIC, AudioManager.ADJUST_UNMUTE, 0);
            }
        }
    }

    /**
     * 是否关闭speech
     *
     * @param disalbeSpeech true 关闭
     */
    private void disableSpeech(boolean disalbeSpeech) {
        if (!disalbeSpeech) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException interruptedException) {
                Log.d(TAG, "sleep err:" + interruptedException.getMessage());
            }
        }
        enableAsr(!disalbeSpeech);
    }

    /**
     * 是否进入休眠模式
     *
     * @param goToSleep true 进入
     */
    private void goToSleep(boolean goToSleep) {
        PowerManager powerManager = (PowerManager) ApplicationWrapper.getContext()
                .getSystemService(Context.POWER_SERVICE);
        if (goToSleep) {
            ApplicationWrapper.getContext().registerReceiver(mBroadcastReceiver,
                    new IntentFilter(Intent.ACTION_SCREEN_ON), null, mHandler);
            ApplicationWrapper.getContext().registerReceiver(mBroadcastReceiver,
                    new IntentFilter(Intent.ACTION_SCREEN_OFF), null, mHandler);
            try {
                PowerManager.class.getMethod("goToSleep", long.class, int.class, int.class)
                        .invoke(powerManager, SystemClock.uptimeMillis(), 1, 1);
            } catch (ReflectiveOperationException e) {
                e.printStackTrace();
            }
        } else {
            mHandler.removeCallbacksAndMessages(null);
            ApplicationWrapper.getContext().unregisterReceiver(mBroadcastReceiver);
            try {
                PowerManager.class.getMethod("wakeUp", long.class).invoke(powerManager, SystemClock.uptimeMillis());
            } catch (ReflectiveOperationException e) {
                e.printStackTrace();
            }
        }
    }

    private void enableAsr(boolean isEnable) {
        Intent intent = new Intent();
        intent.setAction("action_switch_speech");
        intent.putExtra("isOpen", isEnable);
        ApplicationWrapper.getContext().sendBroadcast(intent, "com.ainirobot.permission.command");
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        Log.d(TAG, "stopAction");
        Log.d(TAG, "stopAction: " + mStandbyConfig);
        if (null == mStandbyConfig) {
            Log.e(TAG, "standby config is null ");
            return false;
        }
        doStandbyBean(false, mStandbyConfig);
        return true;
    }

    private final Handler mHandler = new Handler(Looper.getMainLooper());

    private final BroadcastReceiver mBroadcastReceiver = new BroadcastReceiver() {

        @Override
        public void onReceive(Context context, Intent intent) {
            Log.d(TAG, "onReceive:: intent=" + intent);
            if (intent != null) {
                if (Intent.ACTION_SCREEN_ON.equals(intent.getAction())) {
                    mHandler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            onResult(Definition.RESULT_STANDBY_END_SUCCESS, "");
                        }
                    }, 500);
                } else if (Intent.ACTION_SCREEN_OFF.equals(intent.getAction())) {
                    mHandler.removeCallbacksAndMessages(null);
                    onStatusUpdate(Definition.STATUS_STANDBY_SUCCESS, "");
                }
            }
        }
    };


    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return null;
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return null;
    }

    @Override
    protected boolean verifyParam(RobotStandbyBean param) {
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_GET_MAP_INFO:
                getCurrentMapInfoResult(result, extraData);
                return true;
            default:
                break;
        }
        return true;
    }

    private void getCurrentMapInfoResult(String result, String extraData) {
        try {
            Log.d(TAG, "getCurrentMapInfoResult :" + result + " extraData:" + extraData);
            JSONObject jsonObject = new JSONObject(result);
            mTargetState = jsonObject.optInt(Definition.JSON_NAVI_TARGET_DATA_STATE, 0);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        disableHardware(true);
    }

    private void sendTopIrStatusReport(int status, int mapType) {
        BiTopIRStatusReport statusReport = new BiTopIRStatusReport();
        statusReport.addStatus(status)
                .addMapType(mapType)
                .addSource(BiTopIRStatusReport.SOURCE_STANDBY)
                .report();
    }

    Runnable closeRadarRunnable = new Runnable() {
        @Override
        public void run() {
            updateRadar(false);
        }
    };

    private void setOdoMeterStatusListener() {
        mTotalLeftAcc = 0;
        mLastLeftAcc = 0;
        mTotalRightAcc = 0;
        mLastRightAcc = 0;
        StatusManager statusManager = mManager.getCoreService().getStatusManager();
        odoMeterId = statusManager.registerStatusListener(Definition.STATUS_ODOMETER_REPORT, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                if (TextUtils.isEmpty(data)) {
                    return;
                }
                try {
                    JSONObject jsonObject = new JSONObject(data);
                    double leftAcc = jsonObject.optDouble("leftAcc");
                    double rightAcc = jsonObject.optDouble("rightAcc");
                    if (mLastLeftAcc == 0 && mLastRightAcc == 0) {
                        mLastLeftAcc = Math.abs(leftAcc);
                        mLastRightAcc = Math.abs(rightAcc);
                    }
                    double abs = Math.abs(Math.abs(leftAcc) - Math.abs(mLastLeftAcc));
                    if (abs >= 0.01) {
                        mTotalLeftAcc += abs;
                    }
                    double abs1 = Math.abs(Math.abs(rightAcc) - Math.abs(mLastRightAcc));
                    if (abs1 >= 0.01) {
                        mTotalRightAcc += abs1;
                    }
                    mLastLeftAcc = Math.abs(leftAcc);
                    mLastRightAcc = Math.abs(rightAcc);
                    if (mTotalLeftAcc >= 0.5 || mTotalRightAcc >= 0.5) {
                        Log.d(TAG, "odometer has moved leftAcc =  "
                                + mTotalLeftAcc + " , rightAcc = " + mTotalRightAcc);
                        updateRadar(true);
                        mHandler.removeCallbacks(closeRadarRunnable);
                        mHandler.postDelayed(closeRadarRunnable, 5 * 60 * 1000);
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private void removeOdoMeterStatusListener() {
        mHandler.removeCallbacks(closeRadarRunnable);
        StatusManager statusManager = mManager.getCoreService().getStatusManager();
        statusManager.unregisterStatusListener(odoMeterId);
    }

}
