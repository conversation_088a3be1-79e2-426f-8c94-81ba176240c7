/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.config.core;

import android.annotation.TargetApi;
import android.os.Build;
import android.provider.Settings;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.exception.InvalidArgumentException;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import java.util.Map;

public class NavigationConfig extends BaseConfig {
    private static final String TAG = NavigationConfig.class.getSimpleName();

    private int chargePointType;
    private float naviRadius;//底盘半径
    private float spring2WallDistance;//底充弹片与墙的距离
    private float infrared2WallDistance;//底充红外板与墙的距离

    public NavigationConfig(JsonObject jsonObject) {
        super(jsonObject, true);
    }

    @TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR1)
    public void parseConfig(JsonObject config) {
        chargePointType = 0;
        naviRadius = 0.244f;
        spring2WallDistance = 0.0f;
        infrared2WallDistance = 0.0f;

        if (config == null) {
            Log.d(TAG, "parseConfig config null");
            return;
        }
        for (Map.Entry<String, JsonElement> entry : config.entrySet()) {
            String enKey = ConfigDictionary.parseNavigationConfig(entry.getKey());
            switch (enKey) {
                case "chargePointType":
                    chargePointType = entry.getValue().getAsInt();
                    break;
                case "naviRadius":
                    naviRadius = entry.getValue().getAsFloat();
                    Settings.Global.putFloat(ApplicationWrapper.getContext().getContentResolver(),
                            Definition.ROBOT_CHASSIS_RADIUS, naviRadius);
                    break;
                case "spring2WallDistance":
                    spring2WallDistance = entry.getValue().getAsFloat();
                    break;
                case "infrared2WallDistance":
                    infrared2WallDistance = entry.getValue().getAsFloat();
                    break;
                default:
                    break;
            }
        }
        Log.d(TAG, toString());
    }

    public int getChargePointType() {
        return chargePointType;
    }

    public float getNaviRadius() {
        return naviRadius;
    }

    public float getSpring2WallDistance() {
        return spring2WallDistance;
    }

    public float getInfrared2WallDistance() {
        return infrared2WallDistance;
    }

    @Override
    public String toString() {
        return "NaviConfig{"
                + "isEnable='" + isEnable + '\''
                + ", chargePointType='" + chargePointType + '\''
                + ", naviRadius=" + naviRadius + '\''
                + ", spring2WallDistance=" + spring2WallDistance + '\''
                + ", infrared2WallDistance=" + infrared2WallDistance + '\''
                + '}';
    }
}

