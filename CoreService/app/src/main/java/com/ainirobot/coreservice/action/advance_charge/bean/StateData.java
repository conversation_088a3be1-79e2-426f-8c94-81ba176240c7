
package com.ainirobot.coreservice.action.advance_charge.bean;

import android.util.Log;

import com.ainirobot.coreservice.bean.ChargeArea;
import com.ainirobot.coreservice.client.actionbean.AutoChargeBean;
import com.ainirobot.coreservice.client.actionbean.Pose;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class StateData {
    private AutoChargeBean autoChargeBean;
    private ChargeArea mCurUseChargeArea = null;
    private int mCurChargePriority = -1;
    private List<Pose> mWaitPointList = new ArrayList<>();
    private List<Pose> mChargePileList = new ArrayList<>();
    private List<Pose> mChargePointList = new ArrayList<>();
    private Map<Pose, ChargePileInfo> mChargePileInfoMap;    //充电桩到回充点的映射关系

    public StateData(AutoChargeBean autoChargeBean) {
        this.autoChargeBean = autoChargeBean;
    }

    public AutoChargeBean getAutoChargeBean() {
        return autoChargeBean;
    }
    public void setCurUseChargeArea(ChargeArea chargeArea) {
        this.mCurUseChargeArea = chargeArea;
    }

    public int getCurUseAreaId() {
        if (mCurUseChargeArea != null) {
            return mCurUseChargeArea.getAreaId();
        }
        return -1;
    }

    public void setCurChargePriority(int priority) {
        mCurChargePriority = priority;
    }

    public int getCurChargePriority() {
        return mCurChargePriority;
    }

    public void setWaitPointList(List<Pose> waitPointList) {
        this.mWaitPointList.clear();
        if (waitPointList != null && !waitPointList.isEmpty()) {
            this.mWaitPointList.addAll(waitPointList);
        }
    }

    public List<Pose> getWaitPointList() {
        return mWaitPointList;
    }

    public void setChargePileList(List<Pose> chargePileList) {
        this.mChargePileList.clear();
        if (chargePileList != null && !chargePileList.isEmpty()) {
            this.mChargePileList.addAll(chargePileList);
        }
    }

    public List<Pose> getChargePileList() {
        return mChargePileList;
    }

    public void setChargePointList(List<Pose> chargePointList) {
        this.mChargePointList.clear();
        if (chargePointList != null && !chargePointList.isEmpty()) {
            this.mChargePointList.addAll(chargePointList);
        }
        initChargePileToPointMapping();
    }

    private void initChargePileToPointMapping() {
        mChargePileInfoMap = new HashMap<>();

        // 确保两个列表长度相同(一个充电桩对应一个回充点)
        if (mChargePileList.size() != mChargePointList.size()) {
            Log.e("StateData", "充电桩列表和回充点列表长度不匹配");
            return;
        }

        // 建立映射关系
        for (int i = 0; i < mChargePileList.size(); i++) {
            mChargePileInfoMap.put(mChargePileList.get(i), new ChargePileInfo(mChargePileList.get(i), mChargePointList.get(i), i));
        }
    }

    public Pose getChargePointForPile(Pose chargePile) {
        ChargePileInfo info = mChargePileInfoMap.get(chargePile);
        if (info != null) {
            return info.getChargePointPose();
        } else {
            return null;
        }
    }

    public int getChargePileIndex(Pose chargePile) {
        ChargePileInfo info = mChargePileInfoMap.get(chargePile);
        if (info != null) {
            return info.getIndex();
        } else {
            return -1;
        }
    }


    public List<Pose> getChargePointList() {
        return mChargePointList;
    }

    @Override
    public String toString() {
        return "StateData{" +
                "destination=" +
                ", actionCode=" +
                '}';
    }
}
