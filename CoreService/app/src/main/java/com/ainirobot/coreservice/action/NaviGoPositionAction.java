package com.ainirobot.coreservice.action;

import static com.ainirobot.coreservice.client.Definition.CMD_NAVI_GO_POSITION;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.CommandBean;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.utils.NavigationStatusUtils;

import java.util.ArrayList;

public class NaviGoPositionAction extends AbstractAction<CommandBean> {
    private static final String TAG = "NaviGoPositionAction";
    private NavigationResetHeadUtils mNavigationResetHeadUtils;

    public NaviGoPositionAction(ActionManager actionManager, int id, String[] resList) {
        super(actionManager, id, resList);
        mNavigationResetHeadUtils = new NavigationResetHeadUtils(mApi, mManager.getCoreService().getHeadMoveManager());
    }

    @Override
    protected boolean startAction(CommandBean command, LocalApi api) {
        mNavigationResetHeadUtils.checkResetHeadState(actionListenerProxy, mParam.getReqId());
        return true;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        mNavigationResetHeadUtils.stop();
        return true;
    }

    private NavigationResetHeadUtils.ResetHeadListener actionListenerProxy = new NavigationResetHeadUtils.ResetHeadListener() {
        @Override
        public void onResetHeadSuccess() {
            mApi.sendCommand(mParam.getReqId(), Definition.CMD_NAVI_GO_POSITION, mParam.getParams());
            mNavigationResetHeadUtils.startHeadStatusListener(mReqId);
        }

        @Override
        public void onResetHeadFailed(int errorCode, String errorString) {
            onError(errorCode, errorString);
        }
    };

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case CMD_NAVI_GO_POSITION:
                onResult(Definition.RESULT_OK, result, extraData);
                return true;
        }
        return mNavigationResetHeadUtils.onCmdResponse(cmdId, command, result, extraData);
    }

    @Override
    public boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        switch (command) {
            case CMD_NAVI_GO_POSITION:
                NavigationStatusUtils.processNavigationStatus(this, TAG, status, extraData);
                return true;
            default:
                return mNavigationResetHeadUtils.cmdStatusUpdate(cmdId, command, status, extraData);
        }
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return new ArrayList<StopCommandList.StopCommand>() {{
            add(new StopCommandList.StopCommand(Definition.CMD_NAVI_GO_POSITION, null));
        }};
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return null;
    }

    @Override
    protected boolean verifyParam(CommandBean param) {
        return true;
    }
}
