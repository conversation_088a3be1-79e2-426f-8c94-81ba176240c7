package com.ainirobot.coreservice.client.subtype;

import android.util.Log;

import com.ainirobot.coreservice.utils.IOUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Map;
import java.util.Properties;
import java.util.function.Consumer;

/**
 * 设备子类型判断工具类
 *
 * <p> 判断逻辑：
 * 1.判断是否有新格式字段，有五个中的一个即认为是新格式字段，否则认为是旧格式字段
 * 2.是新格式：读取新格式字段，是新格式则不会再尝试读取旧格式字段和 RobotConfig 配置，
 * 3.是旧格式：读取旧格式字段，旧的 device.properties 和 RobotConfig 配置作为并列条件判断，即“||”
 *
 * <p> 新格式字段：
 * 注意：使用新结构的参数只有(TopCamera\FrontCamera\BackCamera\MajorDepth\DownDepth)
 * 上面五个有一个存在就认为这五个属性都是新结构参数表示，都不存在才认为是旧的参数表示。
 * 这五个都是和底盘相关的配置，其他参数还是和旧的保持一致的，比如esp32、SMARTCAM等这些。
 *
 * <p> 新旧格式字段配置规则：
 * 新版本格式：10.1版本之后新的子类型配置规则，即 DeviceInfo 类，使用枚举类型
 * 旧版本格式：使用一对一的key-value形式，value值是int类型或者String类型
 */
public class DeviceSubType {
    private static final String TAG = "DeviceSubType";

    private static DeviceSubType sInstance;

    private DeviceSubType() {
        sNewDeviceInfo = getNewDeviceInfo();
        sOldDeviceBean = getOldDeviceInfo();
    }

    public static DeviceSubType getInstance() {
        if (sInstance == null) {
            synchronized (DeviceSubType.class) {
                if (sInstance == null) {
                    sInstance = new DeviceSubType();
                }
            }
        }
        return sInstance;
    }

    private static final String DEVICE_PROPERTIES = "device.properties";
    private static final String DEVICE_CONFIG_DIR = "/persist/orionoem/";

    private static NewSubTypeBean sNewDeviceInfo; //10.1版本之后新的子类型配置规则
    private static OldSubTypeBean sOldDeviceBean; //10.1版本之前旧的子类型配置规则

    /**
     * 是否 招财豹Pro-Carry 机器人
     */
    public boolean isWaiterProCarry() {
        if (sNewDeviceInfo != null) {
            return sNewDeviceInfo.getStructure() == NewSubTypeBean.StructureType.WaiterPro_Carry;
        }
        return false;
    }

    /**
     * 是否新结构子类型参数
     */
    public boolean isNewDeviceInfo() {
        return sNewDeviceInfo != null && sNewDeviceInfo.isNewCameraInfo();
    }

    /**
     * 是否有 Mono
     */
    public boolean hasMono() {
        if (isNewDeviceInfo()) {
            return sNewDeviceInfo.hasMono();
        }
        if (sOldDeviceBean != null) {
            return sOldDeviceBean.hasMono();
        }
        return false;
    }

    /**
     * 是否双通版本，即是否有 topMono
     */
    public boolean hasTopMono() {
        if (isNewDeviceInfo()) {
            return sNewDeviceInfo.hasTopMono();
        }
        if (sOldDeviceBean != null) {
            return sOldDeviceBean.hasTopMono();
        }
        return false;
    }

    /**
     * 是否有 TopIr
     */
    public boolean hasTopIr() {
        if (isNewDeviceInfo()) {
            return sNewDeviceInfo.hasTopIr();
        }
        if (sOldDeviceBean != null) {
            return sOldDeviceBean.hasTopIr();
        }
        return false;
    }

    /**
     * 是否有 ChargeIR
     */
    public boolean hasChargeIr() {
        if (isNewDeviceInfo()) {
            return sNewDeviceInfo.hasChargeIr();
        }
        if (sOldDeviceBean != null) {
            return sOldDeviceBean.hasChargeIr();
        }
        return false;
    }

    /**
     * 是否有 回充双目摄像头
     */
    public boolean hasFrontChargeCamera() {
        if (isNewDeviceInfo()) {
            return sNewDeviceInfo.hasFrontChargeCamera();
        }
        return false;
    }

    /**
     * 是否不支持 D430
     * @return true 不支持
     */
    public boolean isUnSupportD430() {
        NewSubTypeBean.MajorDepthType majorDepth;
        if (isNewDeviceInfo() && (majorDepth = sNewDeviceInfo.getMajorDepth()) != null) {
            Log.d(TAG, "isUnSupportD430: majorDepth=" + majorDepth);
            return majorDepth == NewSubTypeBean.MajorDepthType.YXSK_WF
                    || majorDepth == NewSubTypeBean.MajorDepthType.ASJ_XB40
                    || majorDepth == NewSubTypeBean.MajorDepthType.ASJ_XB100
                    || majorDepth == NewSubTypeBean.MajorDepthType.INTEL_RS430;
        }
        if (sOldDeviceBean != null) {
            int rgbdD430 = sOldDeviceBean.getRgbdD430();
            int rgbdFm1 = sOldDeviceBean.getRgbdFm1();
            int rgbdWf = sOldDeviceBean.getRgbdWf();
            String depthType = sOldDeviceBean.getDepthType();
            boolean isProDepthType = OldSubTypeBean.DEPTH_TYPE_2XB40_X100.equalsIgnoreCase(depthType)
                    || OldSubTypeBean.DEPTH_TYPE_3X100.equalsIgnoreCase(depthType)
                    || OldSubTypeBean.DEPTH_TYPE_X100.equalsIgnoreCase(depthType);
            Log.d(TAG, "isUnSupportD430 rgbd_d430:" + rgbdD430 + ", rgbd_fm1:" + rgbdFm1 + ", rgbd_wf:" + rgbdWf + ", type:" + depthType);
            return rgbdD430 == 0 || rgbdFm1 == 1 || rgbdWf == 1 || isProDepthType;
        }
        return true;
    }


    public String readFile() {
        File file = new File(DEVICE_CONFIG_DIR, DEVICE_PROPERTIES);
        if (!file.exists()) {
            return "";
        }
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
            byte[] data = new byte[(int) file.length()];
            fis.read(data);
            return new String(data, "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            IOUtils.close(fis);
        }
        return "";
    }

    /**
     * 10.1版本之后，新的子类型配置规则，
     */
    private NewSubTypeBean getNewDeviceInfo() {
        Properties properties = getDeviceProperties();
        if (properties == null) {
            Log.d(TAG, "getNewDeviceInfo properties is null.");
            return null;
        }
        // 从 Properties 转换为 CameraInfo 对象
        NewSubTypeBean info = NewSubTypeBean.fromProperties(properties);
        Log.d(TAG, "getNewDeviceInfo:" + info);
        return info;
    }

    /**
     * 10.1版本之前，旧的子类型配置规则，
     */
    private OldSubTypeBean getOldDeviceInfo() {
        Properties properties = getDeviceProperties();
        if (properties == null) {
            Log.d(TAG, "getOldDeviceInfo properties is null.");
            return null;
        }

        OldSubTypeBean deviceBean = new OldSubTypeBean();
        deviceBean.setLora(Integer.parseInt(properties.getProperty("lora") == null ? "-1" : properties.getProperty("lora", "0")));
        deviceBean.setLightFilter(Integer.parseInt(properties.getProperty("light_filter", "0")));
        deviceBean.setDampener(Integer.parseInt(properties.getProperty("dampener", "0")));
        deviceBean.setLidarEai(Integer.parseInt(properties.getProperty("lidar_EAI", "0")));
        deviceBean.setCplFilter(Integer.parseInt(properties.getProperty("CPL_filter", "0")));
        deviceBean.setTopIr(Integer.parseInt(properties.getProperty("TOPIR_mini2", "0")));
        deviceBean.setRgbdD430(Integer.parseInt(properties.getProperty("RGBD_D430", "1")));
        deviceBean.setEsp32(Integer.parseInt(properties.getProperty("esp32") == null ? "-1" : properties.getProperty("esp32", "0")));
        deviceBean.setHeightBody(Integer.parseInt(properties.getProperty("ZCB_G10CM", "0")));
        deviceBean.setLidarFov(Integer.parseInt(properties.getProperty("LIDAR_FOV", "0")));
        deviceBean.setChargeIr(Integer.parseInt(properties.getProperty("CHGIR", "0")));
        deviceBean.setRgbdFm1(Integer.parseInt(properties.getProperty("RGBD_FM1", "0")));
        deviceBean.setRgbdWf(Integer.parseInt(properties.getProperty("RGBD_WF", "0")));
        deviceBean.setDepthType(properties.getProperty("DEPTH", ""));
        deviceBean.setAutoDoor(Integer.parseInt(properties.getProperty("autodoor_4", "0")));
        deviceBean.setSsMono(Integer.parseInt(properties.getProperty("SS_MONO", "0")));
        deviceBean.setTopMono(Integer.parseInt(properties.getProperty("TOP_MONO", "0")));
        Log.d(TAG, "getOldDeviceInfo end:" + deviceBean.toString());
        return deviceBean;
    }

    private Properties getDeviceProperties() {
        File file = new File(DEVICE_CONFIG_DIR, DEVICE_PROPERTIES);
        if (!file.exists()) {
            Log.d(TAG, "getDeviceProperties file is not exists.");
            return null;
        }
        Properties properties = new Properties();
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
            properties.load(fis);
        } catch (IOException e) {
            e.printStackTrace();
            return properties;
        } finally {
            IOUtils.close(fis);
        }
        Log.d(TAG, "getDeviceProperties: start --->>>");
        printProperty(properties);
        Log.d(TAG, "getDeviceProperties: end <<<---");
        return properties;
    }

    private void printProperty(Properties properties) {
        if (properties == null) {
            Log.d(TAG, "printProperty properties is null");
            return;
        }
        for (Map.Entry<Object, Object> objectObjectEntry : properties.entrySet()) {
            Object key = objectObjectEntry.getKey();
            Object value = objectObjectEntry.getValue();
            Log.d(TAG, "printProperty:key=" + key + " value=" + value);
        }
    }


}
