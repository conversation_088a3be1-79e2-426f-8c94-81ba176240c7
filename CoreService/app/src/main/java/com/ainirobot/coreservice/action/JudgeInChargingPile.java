package com.ainirobot.coreservice.action;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.JudgePileBean;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.service.CoreService;
import com.ainirobot.robotlog.RobotLog;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;

public class JudgeInChargingPile extends AbstractAction<JudgePileBean> {

    private static final String TAG = "JudgeInChargingPile";

    private CoreService mCoreService;
    private float mCoordinateDeviation;
    private String mChargingType = Definition.CHARGING_TYPE_PILE;

    protected JudgeInChargingPile(ActionManager manager, int id, String[] resList) {
        super(manager, id, resList);
        mCoreService = CoreService.mCore;
        mCoordinateDeviation = 0.1f;
    }

    @Override
    protected boolean startAction(JudgePileBean parameter, LocalApi api) {
        checkChargingType();
        mApi.isRobotEstimate(mReqId);
        return true;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        return true;
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return null;
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return null;
    }

    @Override
    protected boolean verifyParam(JudgePileBean param) {
        if (param == null) {
            return true;
        }
        mCoordinateDeviation = param.getCoordinateDeviation();
        Log.d("JudgeChargingPile", param.toString());
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        Log.d(TAG, "command=" + command + ",result=" + result);
        switch (command) {
            case Definition.CMD_NAVI_IS_ESTIMATE:
                if (Definition.RESULT_TRUE.equals(result)) {
//                    //有定位，再判断机器人是否在回充点一定范围内
//                    JSONObject obj = new JSONObject();
//                    try {
//                        obj.put(Definition.JSON_NAVI_TARGET_PLACE_NAME, Definition.START_CHARGE_PILE_POSE);
//                        obj.put(Definition.JSON_NAVI_COORDINATE_DEVIATION, mCoordinateDeviation + "");
//                        mApi.isRobotInlocations(mReqId, obj.toString());
//                    } catch (JSONException e) {
//                        e.printStackTrace();
//                    }
                    if (mCoreService.getSystemManager().isCharging()) {
                        if (mChargingType.equals(Definition.CHARGING_TYPE_WIRE)) {
                            onResult(Definition.RESULT_OK_ESTIMATE_WITHIN_RANGE_WIRE, "estimate, wire charging");
                        } else {
                            onResult(Definition.RESULT_OK_ESTIMATE_WITHIN_RANGE, "estimate, charging");
                        }
                    } else {
                        onResult(Definition.RESULT_ESTIMATE_WITHOUT_RANGE, "estimate,no charging");
                    }
                } else {
                    //没有定位，判断是否处于充电状态
                    if (mCoreService.getSystemManager().isCharging()) {
                        if (mChargingType.equals(Definition.CHARGING_TYPE_WIRE)) {
                            onResult(Definition.RESULT_OK_NO_ESTIMATE_CHARGING_WIRE, result);
                        } else {
                            onResult(Definition.RESULT_OK_NO_ESTIMATE_CHARGING, result);
                        }
                    } else {
                        onResult(Definition.RESULT_NO_ESTIMATE_NO_CHARGING, "no estimate,no charging");
                    }
                }
                break;
            case Definition.CMD_NAVI_IS_IN_LOCATION:
//                try {
//                    JSONObject json = new JSONObject(result);
//                    boolean isInLocation = json.optBoolean(Definition.JSON_NAVI_IS_IN_LOCATION, false);
//                    if (isInLocation) {
//                        //在回充点
//                        onResult(Definition.RESULT_OK_ESTIMATE_WITHIN_RANGE, result);
//                    } else {
//                        //不在充电桩
//                        onResult(Definition.RESULT_ESTIMATE_WITHOUT_RANGE, result);
//                    }
//                } catch (JSONException e) {
//                    e.printStackTrace();
//                }
                break;
        }
        return true;
    }

    private void checkChargingType() {
        String chargingType = mManager.getRobotSettingManager().
                getRobotSetting(Definition.ROBOT_SETTINGS_CHARGING_TYPE);
        RobotLog.d(TAG, "checkChargingType: chargingType=" + chargingType);
        if (!TextUtils.isEmpty(chargingType)) {
            this.mChargingType = chargingType;
        }
    }

}
