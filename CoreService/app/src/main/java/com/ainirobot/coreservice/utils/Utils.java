/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.utils;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.Signature;
import android.os.Build;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.security.MessageDigest;
import java.util.List;
import java.util.Properties;

public class Utils {
    public static void writeIpProp(Context context) {
        Properties prop = new Properties();
        prop.put("NaviIp", "************");
        saveConfig(context, "/mnt/sdcard/config.properties", prop);
    }

    public static boolean saveConfig(Context context, String file,
                                     Properties properties) {
        try {
            File fil = new File(file);
            if (!fil.exists()) {
                fil.createNewFile();
            }
            FileOutputStream s = new FileOutputStream(fil);
            properties.store(s, "");
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    public static String getIpProp(Context context) {
        Properties prop = loadConfig(context, "/mnt/sdcard/config.properties");
        return (String) prop.getProperty("NaviIp");
    }

    public static Properties loadConfig(Context context, String file) {
        Properties properties = new Properties();
        try {
            FileInputStream s = new FileInputStream(file);
            properties.load(s);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return properties;
    }

    public static String loadConfig(Context context, String testFile, int rawFile) {
        InputStream is = null;
        try {
            File file = new File(testFile);
            if (file.exists()) {
                is = new FileInputStream(file);
            } else {
                is = context.getResources().openRawResource(rawFile);
            }
            int total = is.available();
            byte[] bytes = new byte[total];
            int len = is.read(bytes);
            if (total == len) {
                return new String(bytes);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            IOUtils.close(is);
        }
        return null;
    }

    /**
     * remove the file in sdcard according to filePath
     * @param filePath
     * @return
     */
    public static boolean removeFileFromSDCard(String filePath) {
        File file = new File(filePath);
        if (file.exists()) {
            try {
                file.delete();
                return true;
            } catch (Exception e) {
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * 从SD卡中读取文件内容
     * @param filePath 文件路径
     * @return
     */
    public static String getFileContent(String filePath){
        File file = new File(filePath);
        String str = "";
        StringBuffer content = new StringBuffer();
        InputStream is = null;
        InputStreamReader input = null;
        BufferedReader reader = null;
        try {
            is = new FileInputStream(file);
            input = new InputStreamReader(is, "UTF-8");
            reader = new BufferedReader(input);
            while ((str = reader.readLine()) != null) {
                content.append(str);
            }

        } catch (FileNotFoundException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } finally {
            IOUtils.close(is);
            IOUtils.close(input);
            IOUtils.close(reader);
            return content.toString();
        }

    }

    public static String getPackageSign(Context context, String pkgName) {
        String signStr = "-1";

        //获取包管理器
        PackageManager packageManager = context.getPackageManager();
        PackageInfo packageInfo;
        //获取当前要获取 SHA1 值的包名，也可以用其他的包名，但需要注意，
        //在用其他包名的前提是，此方法传递的参数 Context 应该是对应包的上下文。
        //签名信息
        Signature[] signatures = null;
        try {
            packageInfo = packageManager.getPackageInfo(pkgName, PackageManager.GET_SIGNATURES);
            signatures = packageInfo.signatures;
        } catch (Exception e) {
            e.printStackTrace();

        }
        if (null != signatures && signatures.length > 0) {
            Signature sign = signatures[0];
            signStr = encryptionMD5(sign.toByteArray()).toUpperCase();
        }

        return signStr;
    }

    private static String encryptionMD5(byte[] byteStr) {
        MessageDigest messageDigest;
        StringBuilder md5StrBuff = new StringBuilder();
        try {
            messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.reset();
            messageDigest.update(byteStr);
            byte[] byteArray = messageDigest.digest();
            for (byte aByteArray : byteArray) {
                if (Integer.toHexString(0xFF & aByteArray).length() == 1) {
                    md5StrBuff.append("0").append(Integer.toHexString(0xFF & aByteArray));
                } else {
                    md5StrBuff.append(Integer.toHexString(0xFF & aByteArray));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return md5StrBuff.toString();
    }

}