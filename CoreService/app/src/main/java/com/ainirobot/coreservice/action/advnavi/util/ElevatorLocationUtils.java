package com.ainirobot.coreservice.action.advnavi.util;

import android.util.Log;

import com.ainirobot.coreservice.bean.ElevatorRangeInfo;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.config.ConfigManager;

import java.math.BigDecimal;

/**
 * 根据机器人当前点位坐标，判断是否在某电梯的轿厢范围内
 * 根据进出电梯状态，区分电梯范围
 * Created by wanglijing on 2022/8/24
 */
public class ElevatorLocationUtils {

    private final String TAG = getClass().getSimpleName();
    /**
     * 底盘半径
     */
    private final float ROVER_RADIUS = ConfigManager.getNavigationConfig().getNaviRadius();

    //0: 电梯外   1:电梯内  2:获得的点位信息超时
    public enum LocationState {
        OUTSIDE_ELEVATOR,   //电梯外
        INSIDE_ELEVATOR,    //电梯内
        TIMEOUT,            //点位超时，一段时间内未更新点位信息
        CONFIG_ERROR,       //配置信息错误
        NOT_ESTIMATE,       //未定位
    }

    /**
     * 获得相对于电梯的位置信息
     *
     * @param isEnterElevator 是否为进梯过程
     * @param currentPose     当前点位信息
     * @param centerPose      电梯中心点位信息
     * @param rangeInfo       当前电梯范围bean
     * @return 状态 0: 电梯外   1:电梯内  2:获得的点位信息超时  3:点位不存在
     */
    public synchronized LocationState relativeElevatorPosition(boolean isEnterElevator,
                                                               Pose currentPose,
                                                               Pose centerPose,
                                                               ElevatorRangeInfo rangeInfo) {
        try {
            Log.d(TAG, " currentPose: " + currentPose
                    + " , centerPose: " + centerPose
                    + " , rangeInfo: " + rangeInfo);

            if (centerPose == null
                    || rangeInfo == null) {
                Log.e(TAG, " config error ");
                return LocationState.CONFIG_ERROR;
            }

            //当前点位信息为空，有可能未定位
            if (currentPose == null) {
                Log.e(TAG, " current pose is null，not estimate ");
                return LocationState.NOT_ESTIMATE;
            }

            //点位信息超过三秒没有更新
            long takeTime = Math.abs(System.currentTimeMillis() - currentPose.getTime());
            if (takeTime > 3000) {
                Log.e(TAG, " get pose time out, take time: " + takeTime);
                return LocationState.TIMEOUT;
            }

            return isInElevatorRange(isEnterElevator, currentPose, centerPose, rangeInfo)
                    ? LocationState.INSIDE_ELEVATOR
                    : LocationState.OUTSIDE_ELEVATOR;
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "!!! RelativeElevatorPosition Error:  " + e);
            return LocationState.CONFIG_ERROR;
        }

    }

    /**
     * 先根据与电梯中心点前后左右的距离，获得电梯轿厢范围的四个顶点
     * 再判断当前点是否在轿厢范围内
     *
     * @return true:在轿厢范围内
     */
    private boolean isInElevatorRange(boolean isEnterElevator,
                                      Pose currentPose,
                                      Pose centerPose,
                                      ElevatorRangeInfo rangeInfo) {
        double toFront = getToFrontDistance(isEnterElevator, rangeInfo.getToFront());    //距电梯前方距离
        double toBack = rangeInfo.getToBack();     //距电梯后方距离
        double toLeft = rangeInfo.getToLeft();     //距电梯左侧距离
        double toRight = rangeInfo.getToRight();   //距电梯右侧距离

        //这里的判断太冗余，后面可以优化
        //求在直线上，且距电梯中心点一段距离的坐标时，会得到正负两个x值
        //这里根据距离的正负选择x值

        //当机器人角度在 0˚ ~ 90˚ ，即电梯中心点在第一象限
        //向前、向左距离为正，向后、向右距离为负
        if (rangeInDefined(centerPose.getTheta(), 0, Math.PI / 2)) {
            toBack = 0 - toBack;
            toRight = 0 - toRight;
        }

        //当机器人角度在 90˚ ~ 180˚ ，即电梯中心点在第二象限
        //向后、向右距离为正，向前、向左距离为负
        if (rangeInDefined(centerPose.getTheta(), Math.PI / 2, Math.PI)) {
            toFront = 0 - toFront;
            toLeft = 0 - toLeft;
        }

        //当机器人角度在 -180˚ ~ -90˚ ，即电梯中心点在第三象限
        //向后、向左距离为正，向前、向右距离为负
        if (rangeInDefined(centerPose.getTheta(), -Math.PI, -Math.PI / 2)) {
            toFront = 0 - toFront;
            toRight = 0 - toRight;
        }

        //当机器人角度在 -90˚ ~ 0˚ ，即电梯中心点在第四象限
        //向前、向右距离为正，向后、向左距离为负
        if (rangeInDefined(centerPose.getTheta(), -Math.PI/2, 0)) {
            toBack = 0 - toBack;
            toLeft = 0 - toLeft;
        }

        //获得轿厢范围顶点坐标
        Pose topLeftPose = calculateApexPose(centerPose, toFront, toLeft);    //左前方顶点坐标
        Pose topRightPose = calculateApexPose(centerPose, toFront, toRight);  //右前方顶点坐标
        Pose bottomLeftPose = calculateApexPose(centerPose, toBack, toLeft);  //左后方顶点坐标
        Pose bottomRightPose = calculateApexPose(centerPose, toBack, toRight);//右后方顶点坐标

        //当前坐标是否在顶点围成的矩形范围内
        boolean inRange = isInRange(
                currentPose,
                topLeftPose,
                topRightPose,
                bottomLeftPose,
                bottomRightPose);

        Log.i(TAG, " leftTopPose: " + topLeftPose
                + "\n rightTopPose: " + topRightPose
                + "\n leftBottomPose: " + bottomLeftPose
                + "\n rightBottomPose: " + bottomRightPose
                + "\n isInRange: " + inRange
                + "\n angle: " + Math.toDegrees(centerPose.getTheta())
        );
        return inRange;
    }

    /**
     * 判断角度是否在区间内
     *
     * @param currentTheta 当前机器人角度
     * @param min          最小角度
     * @param max          最大角度
     * @return true：在区间内
     */
    public boolean rangeInDefined(double currentTheta, double min, double max) {
        return Math.toDegrees(min) <= Math.toDegrees(currentTheta)
                && Math.toDegrees(currentTheta) < Math.toDegrees(max);
    }

    /**
     * 根据电梯中心点，获得电梯轿厢范围矩形的顶点坐标
     *
     * @param pose 电梯中心点
     * @param d1   前后方向用正负表示；前方：d1>0 , 后方 d1 < 0
     * @param d2   左右方向用正负表示；左侧：d1<0 , 右侧 d1 > 0
     * @return 顶点坐标
     */
    public Pose calculateApexPose(Pose pose, double d1, double d2) {
        //  L2 |
        //     |
        //    ——        * C
        //     |
        //  d2 |
        //     |P
        //  ---|--------|----- （与机器人朝向相同且过电梯中心点的直线L1）
        //         d1        L1

        // L1: 过电梯中心点,且与电梯中心点坐标朝向相同的直线
        // 由于和电梯中心点的朝向相同，电梯中心点在建图时又是朝向电梯门的，
        // 故L1上的点也可以认为是前后距离上的点

        // L2: 过电梯中心点,且与电梯中心点坐标朝向相同的直线的垂线
        // 由于是前后直线L1的垂线，L2的点可认为是左右距离上的点

        //1. L1上，距电梯中心点d1距离的，点位坐标
        // 在L1上，即是求前后的点，当d1>0,求前方点坐标；当d1<0,求后方点坐标
        Pose poseInLine = getPoseInLineByDistance(pose, d1, false);

        //2. L2上，距电梯中心点d2距离的，点位坐标
        // 在L2上，即是求左右的点，当d1<0,求左侧点坐标；当d1>0,求右侧点坐标
        Pose poseInVerticalLine = getPoseInLineByDistance(pose, d2, true);

        //3. 直角矩形，根据3个点坐标，求出矩形第四个点坐标，即求出顶点坐标
        return getPose(pose, poseInLine, poseInVerticalLine);
    }

    /**
     * 求得距centerPoint点distance距离，且在直线上的点坐标
     * 电梯中心点包含机器人朝向，以机器人朝向为正方向
     *
     * @param centerPoint 中心点坐标
     * @param distance    距中心点距离
     * @param isVertical  是否为垂线
     *                    如果是垂线上距中心点的距离，需要反转k值，也影响判断x值的选择
     * @return 在线段上的点坐标
     */
    public Pose getPoseInLineByDistance(Pose centerPoint, double distance, boolean isVertical) {
        // 第一步：求得直线方程相关参数y=kx+b
        //电梯中心点位的角度已知，以该角度为直线斜率
        double k = Math.tan(centerPoint.getTheta());// 坐标直线斜率k
        k = isVertical ? -1 / k : k;    //如果是垂线，斜率需要转换为-1/k
        k = rounding(k);
        double b = centerPoint.getY() - k * centerPoint.getX();// 坐标直线b
        b = rounding(b);

        // 第二步：求得在直线y=kx+b上，距离当前坐标距离为distance的某点
        //根据直线方程和两点间距离公式的方程组
        //解得一个一元二次方程，方程的abc可由下面表示
        // A=k^2+1;
        double A = Math.pow(k, 2) + 1;
        // B=2[(b-y0)k-x0];
        double B = 2 * ((b - centerPoint.getY()) * k - centerPoint.getX());
        // C=(b-y0)^2+x0^2-L^2
        double C = Math.pow(b - centerPoint.getY(), 2) + Math.pow(centerPoint.getX(), 2)
                - Math.pow(distance, 2);
        A = rounding(A);
        B = rounding(B);
        C = rounding(C);

        // 根据一元二次方程求根公式，获得两根x值
        // 两根x1,x2= [-B±√(B^2-4AC)]/2A
        double t = Math.pow(B, 2) - 4 * A * C;
        if (t < 0) {
            t = 0;
        }
        double x1 = (-B + Math.sqrt(t)) / (2 * A); //左侧X值
        double x2 = (-B - Math.sqrt(t)) / (2 * A); //右侧X值

        double x = switchXPoint(x1, x2, isVertical, distance);// 最后确定是在已知两点之间的某点
        double y = k * x + b;
        Log.d("getPoseInLineByDistance", "x1:" + x1 + ",x2:" + x2);
        return new Pose(doubleToFloat(x), doubleToFloat(y), 0);
    }

    /**
     * 根据是否为垂线、距离的正负值选择x值
     *
     * @param x1         x1
     * @param x2         x2
     * @param isVertical 是否为垂线
     * @param distance   距离
     * @return x值
     */
    private double switchXPoint(double x1, double x2, boolean isVertical, double distance) {
        if (isVertical) {
            //如果是垂线
            //如果距离为负值，则取左侧的x值
            return distance < 0 ? x1 : x2;
        } else {
            //不是垂线
            //如果距离为正值，则取左侧的x值
            return distance > 0 ? x1 : x2;
        }
    }

    /**
     * 根据叉乘的方向性，来判断夹角是否超过了180度
     * 没超过180说明在两条平行线中间（在矩形范围内）
     * <p>
     * 参数ABCD需按顺时针方向设置
     */
    public boolean isInRange(Pose pose, Pose poseTL, Pose poseTR, Pose poseBL, Pose poseBR) {
        //叉乘有方向，所以点位顺序需一致
        // A |-----------------|B
        //   |                 |
        //   |       *E        |
        //   |                 |
        // D |-----------------|C
        // (AB * AE ) * (CD * CE) >= 0 说明E在AB和CD两条线中间
        // (DA * DE ) * (BC * BE) >= 0 说明E在DA和BC两条线中间
        // 最后判断是否为且关系,即是否在四条边中间
        //(AB * AE ) * (CD * CE)  >= 0 && (DA * DE ) * (BC * BE) >= 0

        //顶点需按设置，如顺时针：TL:A  TR:B  BR:C  BL:D
        return getCross(pose, poseTR, poseTL) * getCross(pose, poseBL, poseBR) >= 0
                && getCross(pose, poseTL, poseBL) * getCross(pose, poseBR, poseTR) >= 0;
    }


    /**
     * 根据三点坐标，获得向量叉乘结果
     * P*Q = BA * BE

     * @param currentPose 当前点坐标
     * @param anglePose   两向量相交点坐标
     * @param linePose    直线上另一坐标
     * @return >0，Q在P逆时针方向
     *         <0，Q在P顺时针方向
     *         =0，Q和P共线
     */
    private double getCross(Pose currentPose, Pose anglePose, Pose linePose) {
        //  A |-----------------|B(angelPose)
        //(linePose)          /
        //                  /
        //                * E(currentPose)
        // E: currentPose
        // B: anglePose
        // A: linePose

        // 向量BA用坐标表示：BA = (A.x - B.x , A.y - B.y) = P
        // 向量BE用坐标表示：BE = (E.x - B.x , E.y - B.y) = Q
        // 二维向量叉乘定义：P * Q = P.x * Q.y - Q.x * P.y
        // 带入可得：BA * BE = (A.x - B.x)(E.y - B.y) - (E.x - B.x)(A.y - B.y)
        return (linePose.getX() - anglePose.getX()) * (currentPose.getY() - anglePose.getY())
                - (currentPose.getX() - anglePose.getX()) * (linePose.getY() - anglePose.getY());
    }


    /**
     * 直角矩形根据三个点获得第四个点坐标
     *
     * @param pose1 直角点坐标
     * @param pose2 坐标2
     * @param pose3 坐标3
     * @return 第四个顶点坐标
     */
    private Pose getPose(Pose pose1, Pose pose2, Pose pose3) {
        Pose pose = new Pose();
        double poseX = rounding(pose2.getX() + pose3.getX() - pose1.getX());
        double poseY = rounding(pose2.getY() + pose3.getY() - pose1.getY());
        pose.setX(doubleToFloat(poseX));
        pose.setY(doubleToFloat(poseY));
        return pose;
    }

    //四舍五入，保留三位小数
    private double rounding(double num) {
        BigDecimal b = new BigDecimal(num);
        return b.setScale(3, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * double to float
     *
     * @param doubleValue double
     * @return float
     */
    private float doubleToFloat(double doubleValue) {
        BigDecimal bigDecimal1 = new BigDecimal(Double.toString(doubleValue));
        return bigDecimal1.floatValue();
    }

    /**
     * 根据不同的进电梯模式，获得前方的距离
     * <p>
     * 由于是根据当前坐标判断是否在范围内，
     * 为避免机器人没进/出完全，还有一部分在轿厢外/内
     * 需要对前方的距离做调整
     * <p>
     * 进梯模式：toFont - ROVER_RADIUS;减机器人半径
     * 出梯模式：toFont + ROVER_RADIUS;加机器人半径
     */
    public double getToFrontDistance(boolean isEnterElevator, double toFront) {
        return isEnterElevator
                ? toFront - ROVER_RADIUS
                : toFront + ROVER_RADIUS;
    }
}
