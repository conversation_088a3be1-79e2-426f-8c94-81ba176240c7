package com.ainirobot.coreservice.config.core;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * Created by Orion on 2021/3/25.
 */

public class LanguageConfig {

    @SerializedName("语言版本号")
    private String version;
    @SerializedName("语言信息")
    private List<LangInfo> langInfos;

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public List<LangInfo> getLangInfos() {
        return langInfos;
    }

    public void setLangInfos(List<LangInfo> langInfos) {
        this.langInfos = langInfos;
    }

    @Override
    public String toString() {
        return "LanguageConfig{" +
                "version='" + version + '\'' +
                ", langInfos=" + langInfos +
                '}';
    }

    public static class LangInfo {
        @SerializedName("类型")
        private String langCode;
        @SerializedName("昵称")
        private String langName;

        public String getLangCode() {
            return langCode;
        }

        public void setLangCode(String langCode) {
            this.langCode = langCode;
        }

        public String getLangName() {
            return langName;
        }

        public void setLangName(String langName) {
            this.langName = langName;
        }

        @Override
        public String toString() {
            return "LanguageConfig{" +
                    "langCode='" + langCode + '\'' +
                    ", langName='" + langName + '\'' +
                    '}';
        }
    }
}
