/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.utils;

import com.ainirobot.coreservice.client.Invoker;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 定时任务管理
 */
public class DelayTask {

    private static DelayTask mInstance = new DelayTask();

    private ScheduledExecutorService mExecutor;
    private HashMap<Object, List<Future>> mTasks;

    private DelayTask() {
        mExecutor = Executors.newScheduledThreadPool(5);
        mTasks = new HashMap<>();
    }

    private Future submit(Object tag, Runnable runnable, long delay, TimeUnit unit) {
        Future future = mExecutor.schedule(runnable, delay, unit);
        addTask(tag, future);
        return future;
    }

    private Future submit(Object tag, Runnable runnable, long delay, long period, TimeUnit unit) {
        Future future = mExecutor.scheduleAtFixedRate(runnable, delay, period, unit);
        addTask(tag, future);
        return future;
    }

    private void addTask(Object tag, Future future) {
        if (tag == null) {
            return;
        }

        if (mTasks.containsKey(tag)) {
            mTasks.get(tag).add(future);
        } else {
            List<Future> futures = new ArrayList<>();
            futures.add(future);
            mTasks.put(tag, futures);
        }
    }

    public void cancelTask(Object tag) {
        List<Future> futures = mTasks.get(tag);
        if (futures == null) {
            return;
        }
        for (Future future : futures) {
            if (future == null || future.isCancelled() || future.isDone()) {
                continue;
            }
            future.cancel(true);
        }
        mTasks.remove(tag);
    }

    public void cancelTask(Object tag, Future future) {
        List<Future> futures = mTasks.get(tag);
        if (futures == null) {
            return;
        }
        future.cancel(true);
        futures.remove(future);
        if (futures.isEmpty()) {
            mTasks.remove(tag);
        }
    }

    /**
     * 非延迟任务
     *
     * <p class="note"><strong>Note: </strong>调用此接口并不表示任务会立即执行，具体
     * 执行情况由线程池的调度决定
     *
     * @param runnable 任务
     * @return
     */
    public static Future submit(Runnable runnable) {
        return mInstance.submit(null, runnable, 0, TimeUnit.MILLISECONDS);
    }

    public static Future submit(Object tag, Runnable runnable) {
        DelayRunnable delayRunnable = new DelayRunnable(tag, runnable);
        Future future = mInstance.submit(tag, delayRunnable, 0, TimeUnit.MILLISECONDS);
        delayRunnable.setFuture(future);
        return future;
    }

    /**
     * 延时任务
     *
     * @param runnable 任务
     * @param delay    延时时间
     * @return future
     */
    public static Future submit(Runnable runnable, long delay) {
        return mInstance.submit(null, runnable, delay, TimeUnit.MILLISECONDS);
    }

    public static Future submit(Object tag, Runnable runnable, long delay) {
        DelayRunnable delayRunnable = new DelayRunnable(tag, runnable);
        Future future = mInstance.submit(tag, delayRunnable, delay, TimeUnit.MILLISECONDS);
        delayRunnable.setFuture(future);
        return future;
    }

    /**
     * 周期性定时任务
     *
     * @param runnable 任务
     * @param delay    延时时间
     * @param period   间隔时间
     * @return future
     */
    public static Future submit(Runnable runnable, long delay, long period) {
        return mInstance.submit(null, runnable, delay, period, TimeUnit.MILLISECONDS);
    }

    public static Future submit(Object tag, Runnable runnable, long delay, long period) {
        DelayRunnable delayRunnable = new DelayRunnable(tag, runnable);
        Future future = mInstance.submit(tag, delayRunnable, delay, period, TimeUnit.MILLISECONDS);
//        delayRunnable.setFuture(future);
        return future;
    }

    /**
     * 取消定时任务
     *
     * @param tag 任务tag
     */
    public static synchronized void cancel(Object tag) {
        mInstance.cancelTask(tag);
    }

    private static synchronized void cancel(Object tag, Future future) {
        if (future == null) {
            return;
        }
        mInstance.cancelTask(tag, future);
    }

    private static class DelayRunnable implements Runnable {
        private final Object tag;
        private final Runnable runnable;
        private Future future;

        private DelayRunnable(Object tag, Runnable runnable) {
            this.tag = tag;
            this.runnable = runnable;
        }

        @Override
        public void run() {
            if (runnable == null) {
                return;
            }
            if (tag instanceof Invoker) {
                Invoker invoker = (Invoker) tag;
                synchronized (tag) {
                    if (invoker.isAlive()) {
                        runnable.run();
                    }
                }
            } else {
                runnable.run();
            }
            cancel(tag, future);
        }

        void setFuture(Future future) {
            this.future = future;
        }
    }
}
