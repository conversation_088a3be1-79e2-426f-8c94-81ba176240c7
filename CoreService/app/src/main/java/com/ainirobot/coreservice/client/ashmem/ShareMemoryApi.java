package com.ainirobot.coreservice.client.ashmem;

import android.content.Context;
import android.os.MemoryFile;
import android.os.ParcelFileDescriptor;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.IRobotBinderPool;
import com.ainirobot.coreservice.IShareMemoryApi;
import com.ainirobot.coreservice.IShareMemoryCallback;
import com.ainirobot.coreservice.client.BaseSubApi;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.coreservice.utils.ResUtils;

import java.io.FileDescriptor;
import java.io.IOException;
import java.lang.reflect.Method;

/**
 * 共享内存Api : 用于1M以上的数据,跨进程传输
 * 大数据传输不能频繁调用，如果频繁调用，而系统资源释放不是及时的，很容易引起系统低内存，会触发GC．
 * 对于大块大数据的传输业务上来说也不应该过于频繁．
 */
public class ShareMemoryApi extends BaseSubApi {

    private static final String TAG = ShareMemoryApi.class.getSimpleName();

    private static final String MEMORY_FILE_NAME = "Robot_Share_Memory";

    private static ShareMemoryApi instance;
    private volatile IShareMemoryApi iShareMemoryInterface;
    private Context mContext = null;

    public ShareMemoryApi() {
        Log.i(TAG, "ShareMemoryApi create");
        startNewThread(TAG);
    }

    public static synchronized ShareMemoryApi getInstance() {
        if (instance == null) {
            instance = new ShareMemoryApi();
        }
        return instance;
    }

    @Override
    public void onConnect(IRobotBinderPool robotBinderPool, Context context) {
        mContext = context;
        Log.d(TAG, "on connect");
        try {
            iShareMemoryInterface = IShareMemoryApi.Stub.asInterface(
                    robotBinderPool.queryBinder(Definition.BIND_SHARE_MEMORY, null));
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onDisconnect() {
        super.onDisconnect();
        Log.d(TAG, "on dis connect");
        iShareMemoryInterface = null;
    }

    public boolean writeToMemoryFile(final byte[] bytes) {

        long availableMemory = ResUtils.getAvailableMemory(mContext);
        if (bytes == null || bytes.length > availableMemory / 10) { // 申请的空间最多是 1/10 的的剩余可用内存,避免申请异常，导致系统其他应用没有可用的内存使用.
            Log.e(TAG, "writeToMemoryFile failed or Memory is not enough, " +
                    "availableMemory : " + (availableMemory / 1024) + " KB");
            return false;
        }

        if (iShareMemoryInterface == null) {
            Log.d(TAG, "writeToMemoryFile failed , iShareMemoryInterface is null");
            return false;
        }

        DelayTask.submit(new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "start writeBytes length :" + (bytes.length / 1024) + "KB");
                writeBytes(bytes);
            }
        });

        return true;
    }

    private boolean writeBytes(byte[] bytes) {
        synchronized (TAG) {
            try {
                long l1 = System.currentTimeMillis();
                MemoryFile memoryFile = new MemoryFile(MEMORY_FILE_NAME, bytes.length);
                memoryFile.allowPurging(true);
                memoryFile.writeBytes(bytes, 0, 0, bytes.length);
                Method method = MemoryFile.class.getDeclaredMethod("getFileDescriptor");
                //通过反射获得文件句柄
                FileDescriptor fd = (FileDescriptor) method.invoke(memoryFile);
                ParcelFileDescriptor pfd = ParcelFileDescriptor.dup(fd);
                if (iShareMemoryInterface != null) {
                    iShareMemoryInterface.broadcastPfd(pfd);
                }
                //每次更新最新的数据后，当前进程回收历史MemoryFile对象
                closeMemoryFile(memoryFile, pfd);
                Log.d(TAG, "writeBytes cost time:" + (System.currentTimeMillis() - l1));
                return true;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return false;
    }

    private void closeMemoryFile(MemoryFile memoryFile, ParcelFileDescriptor pfd) {
        if (pfd != null) {
            try {
                pfd.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        if (memoryFile != null) {
            memoryFile.close();
        }
    }

    public void registerPfdListener(IShareMemoryCallback callback) {
        try {
            if (iShareMemoryInterface != null) {
                iShareMemoryInterface.registerCallback(callback);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void unregisterPfdListener(IShareMemoryCallback callback) {
        try {
            if (iShareMemoryInterface != null) {
                iShareMemoryInterface.unregisterCallback(callback);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void startShare() {
        try {
            iShareMemoryInterface.startShare();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void stopShare() {
        try {
            iShareMemoryInterface.stopShare();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //-------------------------------------------------------------------------- map.pgm data -start

    ParcelFileDescriptor mGetMapPgmPfd = null;

    /**
     * 获取 map.pgm 文件描述符
     * 在 NavigationService 进程创建共享内存，把文件描述符返回给应用层
     */
    public ParcelFileDescriptor getMapPgmPFD(String mapName) {
        Log.d(TAG, "getMapPgmPFD: mapName=" + mapName);
        mGetMapPgmPfd = getParcelFileDescriptor(Definition.TYPE_PFD_MAP_PGM_GET, mapName);
        return mGetMapPgmPfd;
    }

    /**
     * 修改 map.pgm 文件
     * 在应用层进程创建共享内存，把文件描述符传递给 NavigationService
     */
    public boolean setMapPgmPFD(String mapName, byte[] bytes) {
        Log.d(TAG, "setMapPgmPFD: mapName=" + mapName + " bytes length=" + bytes.length);
        ParcelFileDescriptor pfdFromMemoryFile = ShareMemoryFactory.
                createPfdFromMemoryFile(Definition.TYPE_PFD_MAP_PGM_SET, bytes);
        if (pfdFromMemoryFile == null) {
            Log.d(TAG, "setMapPgmPFD: Create pfd failed!");
            return false;
        }
        return setParcelFileDescriptor(Definition.TYPE_PFD_MAP_PGM_SET, pfdFromMemoryFile, mapName);
    }

    /**
     * 文件描述符{ParcelFileDescriptor}在途径的各个进程都有本地内存映射，需要各个进程单独释放资源。
     * 并且，必须等到应用层使用完之后再主动关闭，否则应用层会读取失败。
     */
    public void releaseGetMapPgmPFD() {
        Log.d(TAG, "releaseGetMapPgmPFD: ");
        if (mGetMapPgmPfd != null) {
            try {
                mGetMapPgmPfd.close();
                mGetMapPgmPfd = null;
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        releaseParcelFileDescriptor(Definition.TYPE_PFD_MAP_PGM_GET);
    }

    /**
     * 通过 binder 接口获取远程进程共享内存的文件描述符
     */
    private ParcelFileDescriptor getParcelFileDescriptor(String type, String params) {
        Log.d(TAG, "getParcelFileDescriptor: type=" + type + " params=" + params);
        try {
            if (iShareMemoryInterface != null) {
                ParcelFileDescriptor pfd = iShareMemoryInterface.getParcelFileDescriptor(type, params);
                return pfd;
            }
        } catch (Exception e) {
            Log.d(TAG, "getParcelFileDescriptor:Exception: " + e.getMessage());
            e.printStackTrace();
        } finally {
            Log.d(TAG, "getParcelFileDescriptor:finally: ");
        }
        Log.d(TAG, "getParcelFileDescriptor: Failed!");
        return null;
    }

    /**
     * 通过 binder 接口把共享内存的文件描述符发送给远程进程
     */
    private boolean setParcelFileDescriptor(String type, ParcelFileDescriptor pfd, String params) {
        Log.d(TAG, "setParcelFileDescriptor: type=" + type + " pfd=" + pfd + " params=" + params);
        try {
            if (iShareMemoryInterface != null) {
                boolean result = iShareMemoryInterface.setParcelFileDescriptor(type, pfd, params);
                Log.d(TAG, "setParcelFileDescriptor: result=" + result);
                return result;
            }
        } catch (Exception e) {
            Log.d(TAG, "setParcelFileDescriptor:Exception: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (pfd != null) {
                Log.d(TAG, "setParcelFileDescriptor:finally: Close pfd!");
                try {
                    pfd.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        Log.d(TAG, "setParcelFileDescriptor: Failed!");
        return false;
    }

    private void releaseParcelFileDescriptor(String type) {
        Log.d(TAG, "releaseParcelFileDescriptor: type=" + type);
        try {
            if (iShareMemoryInterface != null) {
                iShareMemoryInterface.releaseParcelFileDescriptor(type);
            }
        } catch (Exception e) {
            Log.d(TAG, "releaseParcelFileDescriptor:Exception: " + e.getMessage());
            e.printStackTrace();
        }
    }


}
