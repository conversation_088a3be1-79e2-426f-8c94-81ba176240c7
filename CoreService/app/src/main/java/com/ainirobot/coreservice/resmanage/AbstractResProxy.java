/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.resmanage;


import android.text.TextUtils;

import com.ainirobot.coreservice.core.InternalDef;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.service.CoreService;

import java.util.HashSet;
import java.util.Iterator;

abstract class AbstractResProxy {
    private ResManager mManager;
    private HashSet<String> mSupportCmds = new HashSet<>();
    private int mId = InternalDef.RES_INVALID_ID;
    protected CoreService mCoreService;
    protected boolean mIsAvailable = true;

    protected boolean mHasStopFlag = false;

    AbstractResProxy(ResManager manager, int resId) {
        mManager = manager;
        mId = resId;
        mCoreService = mManager.getCoreService();
    }

    void addSupportCmd(String cmd) {
        if (!TextUtils.isEmpty(cmd)) {
            mSupportCmds.add(cmd);
        }
    }

    public boolean release() {
        if (stop()) {
            return releaseRes();
        }
        return false;
    }

    public final boolean stop() {
        if (!mHasStopFlag) {
            mHasStopFlag = stopOperate();
        }
        return mHasStopFlag;
    }

    int sendModuleCmd(int reqId, String command, String params, String language, boolean isContinues,
                      LocalApi.OnCmdResponse response) {
        return mManager.sendCommand(reqId, command, params, language, isContinues, response);
    }

    public boolean isAvailable() {
        return mIsAvailable;
    }

    boolean cancelCommand(int cmdId, boolean isForceStop) {
        return mManager.cancelCommand(cmdId, isForceStop);
    }

    Iterator<String> getSupportCmds() {
        return mSupportCmds.iterator();
    }

    int getResId() {
        return mId;
    }

    abstract boolean stopOperate();

    abstract boolean releaseRes();
}
