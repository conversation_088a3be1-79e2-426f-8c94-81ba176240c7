/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.data.robotsetting;

import android.content.ContentValues;
import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.net.Uri;
import android.support.annotation.NonNull;
import android.util.Log;

import com.ainirobot.coreservice.data.provider.BaseProvider;
import com.ainirobot.coreservice.data.provider.SQLiteTable;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;


public class RobotSettingProvider extends BaseProvider {

    public static final String AUTHORITY = "com.ainirobot.coreservice.robotsettingprovider";

    private static final String TAG = RobotSettingProvider.class.getSimpleName();

    private static final List<SQLiteTable> TABLES = new ArrayList<SQLiteTable>() {{
        add(SettingDataHelper.TABLE);
    }};

    @Override
    public List<SQLiteTable> getTables() {
        return TABLES;
    }

    @Override
    public SQLiteOpenHelper getDBHelper() {
        return new RobotSettingOpenHelper(getContext());
    }

    @Override
    public String getType(@NonNull Uri uri) {
        return "vnd.android.cursor.item/" + uri.getLastPathSegment();
    }

    @Override
    public String matchTable(Uri uri) {
        return uri.getLastPathSegment();
    }

    @Override
    public boolean onCreate() {
        Log.i(TAG, "on create");
        mDBHelper = new RobotSettingOpenHelper(getContext());
        return true;
    }

    @Override
    public int update(@NonNull Uri uri, ContentValues values, String selection, String[] selectionArgs) {
        int count = super.update(uri, values, selection, selectionArgs);
        getContext().getContentResolver().notifyChange(
                Uri.withAppendedPath(uri, selectionArgs[0]), null);
        return count;
    }

    static class RobotSettingOpenHelper extends SQLiteOpenHelper {
        private static final String TAG = RobotSettingOpenHelper.class.getSimpleName();
        private static final String DB_NAME = "setting.db";
        private static final int DB_VERSION = 4;

        private List<BaseInitDataHelper> sInitDataHelper = new CopyOnWriteArrayList<>();

        public RobotSettingOpenHelper(Context context) {
            super(context, DB_NAME, null, DB_VERSION);
            Log.i(TAG, "init");
            sInitDataHelper.add(new SettingInitDataHelper(context));
            SQLiteDatabase db = getWritableDatabase();
            for (BaseInitDataHelper table : sInitDataHelper) {
                table.init(db);
            }
            db.close();
        }

        @Override
        public void onCreate(SQLiteDatabase db) {
            Log.i(TAG, "create db");
            for (BaseInitDataHelper table : sInitDataHelper) {
                table.onCreate(db);
            }
        }

        @Override
        public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            Log.i(TAG, "upgrade db");
            for (BaseInitDataHelper table : sInitDataHelper) {
                table.onUpgrade(db);
            }
        }

        @Override
        public void onDowngrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            Log.i(TAG, "onDowngrade  oldVersion=" + oldVersion + "  newVersion=" + newVersion);
            for (BaseInitDataHelper table : sInitDataHelper) {
                table.onDowngrade(db);
            }
        }
    }
}