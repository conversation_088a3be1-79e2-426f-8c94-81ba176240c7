package com.ainirobot.coreservice.action.download_map.state;

import android.text.TextUtils;

import com.ainirobot.coreservice.action.download_map.DownloadMapStateEnum;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.DownloadMapBean;
import com.ainirobot.coreservice.utils.ZipUtils;
import com.google.gson.Gson;

public class UpdatePlaceListToNaviState extends BaseDownloadMapPkgState {

    private Object data;

    @Override
    protected void onStart(String paramsStr, DownloadMapBean downloadMapBean) {
        Gson gson = new Gson();
        sendCommand(0, Definition.CMD_NAVI_UPDATE_PLACE_LIST,
                ZipUtils.zipMapData(Definition.CMD_NAVI_UPDATE_PLACE_LIST, gson, gson.toJson(data)));
    }

    @Override
    public boolean onCmdResponse(int cmdId, String command, String result, String extraData) {
        if (TextUtils.equals(command, Definition.CMD_NAVI_UPDATE_PLACE_LIST)) {
            onStateRunSuccess();
            return true;
        }
        return false;
    }

    @Override
    protected DownloadMapStateEnum getNextState() {
        return DownloadMapStateEnum.UPDATE_PLACE_LIST_TO_NET;
    }

    @Override
    public Object getData() {
        return data;
    }

    @Override
    public void setData(Object data) {
        this.data = data;
    }
}
