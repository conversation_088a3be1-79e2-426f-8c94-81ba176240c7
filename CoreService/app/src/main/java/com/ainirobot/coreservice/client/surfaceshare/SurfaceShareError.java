package com.ainirobot.coreservice.client.surfaceshare;

public class SurfaceShareError {

    public static final int ERROR_CONNECT_SERVER_TIMEOUT = -11;
    public static final int ERROR_SERVER_EXIT_ABNORMALLY = -12;
    public static final int ERROR_SERVER_EXIT = -13;
    public static final int ERROR_SURFACE_SHARE_USED = -14;
    public static final int ERROR_SET_STREAM_SURFACE_FAILED = -15;
    public static final int ERROR_SURFACE_SHARE_PREEMPTED = -16;
}
