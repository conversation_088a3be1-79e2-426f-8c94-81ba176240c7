package com.ainirobot.coreservice.core.status;

public interface IStatus {

    void registerHWCallback(String serviceName);

    void unregisterHWCallback(String serviceName);

    void sendStatusReport(String serviceName, String statusType, String data);

    void getRobotStatus(String statusType, StatusSubscriber listener);

    void registerStatusListener(String statusType, StatusSubscriber listener);

    boolean unregisterStatusListener(String statusType, int hashCode);
}
