package com.ainirobot.coreservice.client;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.utils.FileUtils;
import com.ainirobot.coreservice.utils.IOUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Map;
import java.util.Properties;

public class ProductInfo {
    private static final String TAG = "ProductInfo";

    public static final String SWIPE_THERMAL_PRODUCT_ID = "产品ID";
    public static final String HEXIN_ID = "1";
    public static final String JUGE_ID = "2";
    private static final String DEVICE_PROPERTIES = "device.properties";
    private static final String DEVICE_CONFIG_DIR = "/persist/orionoem/";

    public enum ProductModel {
        CM_XIAOMI_BASE("CM-GB01N"),
        CM_XIAOMI_TW("CM-GB01NTW"),
        CM_XIAOMI_SWIPE_CARD("CM-GB02N"),
        CM_XIAOMI_MEIDI("CM-GB01M"),
        CM_XIAOMI_COSTDOWN("CM-GB02C"),
        CM_XIAOMI_HEXIN("CM-GB01R"),
        CM_XIAOMI_JUGE("CM-GB02R"),
        CM_XIAOMI_1p1("CM-GB01C"),
        CM_XIAOMI_MINI("CM-GB01T"),
        CM_XIAOMI_1P5("OS-R-SG02S"),
        /**
         * 小秘Plus----845
         */
        CM_XIAOMI_PLUS("OS-R-DP01S"),
        /**
         * 小秘2.0----845
         */
        CM_XIAOMI_2("OS-R-SG04B"),

        CM_VASE_BASE("CM-GB01L"),
        CM_VASE_845("CM-GB02L"),
        CM_VASE_COSTDOWN("OS-R-SA03S"), //商场COSTDOWN版本

        CM_BIGSCREEN_821("CM-GB01D"),
        CM_BIGSCREEN_845("CM-GB02D"),
        CM_BIGSCREEN_COSTDOWN("CM-GB03D"),

        CM_KTV_BASE("CM-GB01K"),
        CM_POSTER_BIGSCREEN("CM-GB02K"),
        /**
         * KTV机器人----845
         */
        CM_KTV_845("OS-R-DG02S"),
        CM_CONVEYING_BASE("CM-GB01J"),
        CM_CONVEYING_HUAZHU("CM-GB02J"),

        CM_DATOU_BASE("CM-GB01H"),

        /**
         * 送餐机器人----845
         */
        CM_DELIVERY_BASE("OS-R-DR01S"),
        CM_DELIVERY_BASE_LANDSCAPE("OS-R-DR02S"), //招财豹Pro
        CM_DELIVERY_XD("OS-R-XD01"),
        CM_DELIVERY_BIG_SCREEN("OS-R-AD01S"),
        CM_DELIVERY_MALL("OS-R-DS01S"), //招财豹商超版
        CM_DELIVERY_DOOR("OS-R-DH02A"), //豹小递Pro 新华医疗
        CM_DELIVERY_DOOR_HOTEL("OS-R-DH02H"), //豹小递Pro Hotel Huazhu
        CM_DELIVERY_DOOR_COMMON_HOTEL("OS-R-DH02S"), //酒店通用递送版本
        CM_DELIVERY_DOOR_DEVELOPMENT("OS-R-DH02D"), //豹小递Pro 二开版

        CM_DELIVERY_SLIM_DOOR("OS-R-DH03S"), //豹小递SLIM
        CM_DELIVERY_CARRY("OS-R-DR02G"), //豹厂通 Carry
        CM_DELIVERY_SLIM("OS-R-DR03S"), //SLIM

        /**
         * 海外版本model
         */
        CM_DELIVERY_OVERSEAS("OS-R-DR01"),
        CM_DELIVERY_OVERSEAS_LANDSCAPE("OS-R-DR02"),
        CM_MINI_TOB_OVERSEAS("OS-R-SD03"),
        CM_XIAOMI_PLUS_OVERSEAS("OS-R-DP01"),

        CM_XIAOMI_2_OVERSEAS("OS-R-SG04"),

        CM_DELIVERY_CARRY_OVERSEAS("OS-R-DR02C"),

        CM_DELIVERY_SLIM_OVERSEAS("OS-R-DR03"), //SLIM

        CM_DELIVERY_SLIM_PRO_OVERSEAS("OS-R-DR03P"), //SLIM PRO（slim机型中唯一带有1/2层托盘摄像头的型号）

        /**
         * Mini机器人----845
         */
        CM_MINI_TOB("OS-R-SD01B"),
        CM_MINI_TOC("OS-R-SD01S"),
        CM_MINI_TOC_OLD("OS-DB01D");

        private static String TAG = ProductModel.class.getSimpleName();
        public final String model;

        ProductModel(String model) {
            this.model = model;
        }
    }

    private static final String PRODUCT_MODEL = RobotSettings.getProductModel();

    // 判断是否未mini产品
    public static boolean isMiniProduct() {
        return TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_MINI_TOB.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_MINI_TOB_OVERSEAS.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_MINI_TOC.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_MINI_TOC_OLD.model);
    }

    // 判断是否是招财豹产品
    public static boolean isDeliveryProduct() {
        return TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_BASE.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_BASE_LANDSCAPE.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_OVERSEAS.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_OVERSEAS_LANDSCAPE.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_XD.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_BIG_SCREEN.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_MALL.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_DOOR.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_DOOR_HOTEL.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_CARRY.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_CARRY_OVERSEAS.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_DOOR_COMMON_HOTEL.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_DOOR_DEVELOPMENT.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_SLIM.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_SLIM_DOOR.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_SLIM_OVERSEAS.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_SLIM_PRO_OVERSEAS.model);
    }

    // 判断是否是海外产品(zcb,mini etc.定义为所有产品线的海外环境)
    public static boolean isOverSea() {
        return TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_OVERSEAS.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_OVERSEAS_LANDSCAPE.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_MINI_TOB_OVERSEAS.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_XIAOMI_PLUS_OVERSEAS.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_XIAOMI_2_OVERSEAS.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_CARRY_OVERSEAS.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_SLIM_OVERSEAS.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_SLIM_PRO_OVERSEAS.model);
    }

    // 判断招财豹产品的海外
    public static boolean isDeliveryOverSea() {
        return TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_OVERSEAS.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_OVERSEAS_LANDSCAPE.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_CARRY_OVERSEAS.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_SLIM_OVERSEAS.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_SLIM_PRO_OVERSEAS.model);
    }

    // 判断招财豹产品的海外基础版
    public static boolean isBaseDeliveryOverSea() {
        return TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_OVERSEAS.model);
    }

    // 判断是否是大屏845CD外售版
    public static boolean isLaraForSale() {
        return TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_BIGSCREEN_COSTDOWN.model);
    }

    // 判断是否是豹小秘1.X
    public static boolean isMeissa() {
        return TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_XIAOMI_BASE.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_XIAOMI_1p1.model);
    }

    // 判断是否是豹小秘1.5
    public static boolean isMeissa1P5() {
        return TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_XIAOMI_1P5.model);
    }

    public static boolean isMeissaPlus() {
        return TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_XIAOMI_PLUS.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_XIAOMI_PLUS_OVERSEAS.model);
    }

    // 判断是否是豹小秘2.0
    public static boolean isMeissa2() {
        return TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_XIAOMI_2.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_XIAOMI_2_OVERSEAS.model);
    }

    // 是否为招财消毒机器人
    public static boolean isSaiphXD(){
        return TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_XD.model);
    }

    // 是否为招财大屏机器人
    public static boolean isSaiphBigScreen(){
        return TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_BIG_SCREEN.model);
    }

    public static boolean isSaiphXdOrBigScreen(){
        return isSaiphXD() || isSaiphBigScreen();
    }

    // 是否为Mini海外版本
    public static boolean isMiniOverSea() {
        return TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_MINI_TOB_OVERSEAS.model);
    }

    // 是否为视觉回充招财豹（标品）
    public static boolean isSaiphChargeIr() {
        return (TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_BASE.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_OVERSEAS.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_MALL.model))
                && FileUtils.hasChargeIR();
    }

    // 肇观RGBD招财豹
    public static boolean isSaiphRgbdFm1() {
        return (TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_BASE.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_OVERSEAS.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_OVERSEAS_LANDSCAPE.model))
                && FileUtils.hasRgbdFm1();
    }

    // 是否是标准招财
    public static boolean isSaiph() {
        return TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_BASE.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_OVERSEAS.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_MALL.model);
    }

    // 是否是招财商超版
    public static boolean isSaiphMall() {
        return TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_MALL.model);
    }

    // 是否是招财豹Pro(视觉回充)
    public static boolean isSaiphPro() {
        return TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_BASE_LANDSCAPE.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_OVERSEAS_LANDSCAPE.model);
    }

    // 是否是豹小递Pro(视觉回充)
    public static boolean isAlnilamPro() {
        return TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_DOOR.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_DOOR_HOTEL.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_DOOR_COMMON_HOTEL.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_DOOR_DEVELOPMENT.model);
    }

    public static boolean isChargeIrProduct() {
        return isSaiphChargeIr() || isSaiphPro() || isAlnilamPro() || isCarryProduct() || isSlimProduct();
    }

    // 是否为带梯控的项目
    public static boolean isElevatorCtrlProduct(){
        return TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_XD.model)
                || FileUtils.hasElevator();
    }
    public static boolean isMiniProductSupportMultiRobot() {
        return (TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_MINI_TOB.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_MINI_TOB_OVERSEAS.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_MINI_TOC.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_MINI_TOC_OLD.model))
                && FileUtils.hasEsp32();
    }

    public static boolean isCarryProduct(){
        return TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_CARRY.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_CARRY_OVERSEAS.model);
    }

    public static boolean isSlimProduct(){
        return TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_SLIM.model)
                ||TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_SLIM_DOOR.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_SLIM_OVERSEAS.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_SLIM_PRO_OVERSEAS.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_DOOR_HOTEL.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_DOOR_COMMON_HOTEL.model);
    }

    public static boolean isSlimDoor(){
        return TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_SLIM_DOOR.model)
                || TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_DOOR_HOTEL.model);
    }

    public static boolean isSlimProOversea(){
        return TextUtils.equals(PRODUCT_MODEL, ProductModel.CM_DELIVERY_SLIM_PRO_OVERSEAS.model);
    }

    public static boolean hasElectricDoor() {
        return FileUtils.hasElectricDoor();
    }

    public static Properties getDeviceProperties() {
        File file = new File(DEVICE_CONFIG_DIR, DEVICE_PROPERTIES);
        if (!file.exists()) {
            Log.d(TAG, "getDeviceProperties file is not exists.");
            return null;
        }
        Properties properties = new Properties();
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
            properties.load(fis);
        } catch (IOException e) {
            e.printStackTrace();
            return properties;
        } finally {
            IOUtils.close(fis);
        }
        Log.d(TAG, "getDeviceProperties: start --->>>");
        printProperty(properties);
        Log.d(TAG, "getDeviceProperties: end <<<---");
        return properties;
    }

    private static void printProperty(Properties properties) {
        if (properties == null) {
            Log.d(TAG, "printProperty properties is null");
            return;
        }
        for (Map.Entry<Object, Object> objectObjectEntry : properties.entrySet()) {
            Object key = objectObjectEntry.getKey();
            Object value = objectObjectEntry.getValue();
            Log.d(TAG, "key=" + key + " value=" + value);
        }
    }
}
