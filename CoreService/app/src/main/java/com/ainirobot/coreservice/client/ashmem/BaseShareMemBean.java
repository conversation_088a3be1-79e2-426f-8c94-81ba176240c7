package com.ainirobot.coreservice.client.ashmem;

public abstract class BaseShareMemBean {

    public enum ShareDataType {
        DEPTH_IMG {
            @Override
            public int getType() {
                return 1;
            }
        },
        TOPIR_IMG {
            @Override
            public int getType() {
                return 2;
            }
        };

        public abstract int getType();
    }

    protected int type;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}
