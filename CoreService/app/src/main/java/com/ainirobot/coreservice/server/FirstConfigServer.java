/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.server;

import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;
import android.os.RemoteException;
import android.text.TextUtils;

import com.ainirobot.coreservice.IFirstConfigRegistry;
import com.ainirobot.coreservice.IStatusListener;
import com.ainirobot.coreservice.action.ActionManager;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.BaseBean;
import com.ainirobot.coreservice.client.speech.SkillApi;
import com.ainirobot.coreservice.core.InternalDef;
import com.ainirobot.coreservice.core.status.RemoteSubscriber;
import com.ainirobot.coreservice.core.status.StatusManager;
import com.ainirobot.coreservice.listener.IActionListener;
import com.ainirobot.coreservice.service.CoreService;
import com.ainirobot.robotlog.RobotLog;
import com.google.gson.Gson;

import org.json.JSONObject;

public class FirstConfigServer extends IFirstConfigRegistry.Stub {

    private CoreService mCoreService;

    public FirstConfigServer(CoreService coreService){
        this.mCoreService = coreService;
    }

    @Override
    public int sendFirstConfigRequest(String type, String params) throws RemoteException {
        if (TextUtils.isEmpty(type))
            return -1;
        return 0;
    }

    @Override
    public int sendFirstConfigCommand(String cmdAction, String cmdParam) throws RemoteException {
        return 0;
    }

    @Override
    public int startAction(int reqId, int actionId, String params, IActionListener listener) throws RemoteException {
        RobotLog.d("startAction");
        ActionManager actionManager = mCoreService.getActionManager();
        actionManager.exCmd(actionId, params, listener);
        return 0;
    }

    @Override
    public int stopAction(int reqId, int actionId, boolean isResetHW) throws RemoteException {
        ActionManager actionManager = mCoreService.getActionManager();
        actionManager.exStopCmd(actionId, isResetHW);
        return 0;
    }


    @Override
    public String registerStatusListener(String type, IStatusListener listener) throws RemoteException {
        StatusManager statusManager = mCoreService.getStatusManager();
        RemoteSubscriber subscriber = new RemoteSubscriber(listener);
        return statusManager.registerStatusListener(type, subscriber);
    }

    @Override
    public boolean unregisterStatusListener(String id) throws RemoteException {
        StatusManager statusManager = mCoreService.getStatusManager();
        return statusManager.unregisterStatusListener(id);
    }

    @Override
    public void startInspection(int reqId, IActionListener listener) throws RemoteException {
        BaseBean bean = new BaseBean();
        bean.setReqId(reqId);
        String jsonStr = new Gson().toJson(bean);
        ActionManager actionManager = new ActionManager(mCoreService);
        actionManager.exCmd(Definition.ACTION_INSPECTION,jsonStr,listener);
    }



//    @Override
//    public void textToSpeech(String text) throws RemoteException {
//        RobotLog.d("CoreService textToSpeech text = " + text);
//        Handler coreHandler = mCoreService.getCoreHandler();
//        Message msg = coreHandler.obtainMessage(InternalDef.MSG_CORE_NEW_TEXT_SPEECH);
//        Bundle bundle = new Bundle();
//        bundle.putString(InternalDef.BUNDLE_TEXT, text);
//        msg.setData(bundle);
//        coreHandler.sendMessage(msg);
//        SkillApi.getInstance().playText(text,null);
//    }

//    @Override
//    public void toneToSpeech(String type) throws RemoteException {
//        RobotLog.d("CoreService toneToSpeech type = " + type);
//        Handler coreHandler = mCoreService.getCoreHandler();
//        Message msg = coreHandler.obtainMessage(InternalDef.MSG_CORE_NEW_TONE_SPEECH);
//        Bundle bundle = new Bundle();
//        bundle.putString(InternalDef.BUNDLE_TEXT, type);
//        msg.setData(bundle);
//        coreHandler.sendMessage(msg);
//        SkillApi.getInstance().playTone(type,null);
//
//    }
}
