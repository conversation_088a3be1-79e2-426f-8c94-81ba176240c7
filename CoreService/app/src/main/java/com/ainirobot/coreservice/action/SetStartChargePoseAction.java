/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.action;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.AutoChargeBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.config.ConfigManager;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.robotlog.RobotLog;
import com.google.gson.Gson;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

public class SetStartChargePoseAction extends AbstractAction<AutoChargeBean> {

    private static final String Tag = "ChargeActionSet";
    private static final float ROVER_RADIUS = ConfigManager.getNavigationConfig().getNaviRadius();
    private AutoChargeBean autoChargeBean = null;
    private long timeout = 30 * Definition.SECOND;  // 30s default
    private Timer timer = null;
    private volatile State mCurState = State.IDLE;
    private Pose mChargingPile;
    private int mChargeMode;
    private String mLanguage;
    private String mChargingType = Definition.CHARGING_TYPE_PILE;
    private int mTypeId; // 特殊点类型
    private int mPriority; // 特殊点优先级
    private String mPlaceName; // 地点名字

    private IChargePointPolicy mCurrentPolicy;
    private IChargePointPolicy[] mPolicy = new IChargePointPolicy[Definition.CHARGEPOINT_MAX_POLICY];

    private static final int CHARGE_ENVIRONMENT_NORMAL = 0;
    private static final int CHARGE_ENVIRONMENT_NARROW = 1;
    private static final int CHARGE_ENVIRONMENT_VISION = 2;
    private List<Pose> poseList = new ArrayList<>();
    private boolean mIsReplacePose = false;

    private enum State {
        IDLE,
        STARTING,
        CALCULATING, //计算回充点
        SET_CHARGE_POSE, //设置回充点
        SET_CHARGE_PILE, //设置充电桩
        SET_LOCATE_POSE //设置定位点
    }

    protected SetStartChargePoseAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_SET_START_CHARGE_POSE, resList);

        mPolicy[Definition.BACKTO_CHARGEPOINT_POLICY] = new BackToPolicy();//回充时，背对充电桩，默认策略（最早期豹小秘红外回充）
        mPolicy[Definition.FACETO_CHARGEPOINT_POLICY] = new FaceToPolicy();//回充时，面对充电桩，已废弃，目前没有此方案机型
        mPolicy[Definition.VISION_CHARGEPOINT_POLICY] = new VisionPolicy();//视觉底充，现在的硬件都是视觉侧充，mini和小秘早期版本使用底充
        mPolicy[Definition.WIRE_CHARGING_POLICY] = new WireChargingPolicy();//线充，充电桩、回充点、定位点“三点合一”设置
        mPolicy[Definition.VISION_CHARGEPOINT_SIDE_POLICY] = new VisionSideCharging();//视觉侧充，新机型使用

        /**
         * 注意：
         * VisionSideCharging 新的视觉侧充只有新华医疗在用，回充点在充电桩前方固定距离，不需要处理机器人半径。
         * 修改原因：以后的充电桩都会统一为侧充，不再需要处理机器人半径。
         */
    }

    @Override
    protected boolean startAction(AutoChargeBean parameter, LocalApi api) {
        RobotLog.d(Tag, "SetStartChargePose action start , mCurState = " + mCurState
                + ", ROVER_RADIUS=" + ROVER_RADIUS + ", parameter = " + parameter);
        if (mCurState != State.IDLE) {
            return false;
        }
        checkChargingType(parameter);
        mChargeMode = parameter.getChargeMode();
        mLanguage = parameter.getMapLanguage();
        mCurState = State.STARTING;
        mPlaceName = parameter.getPlaceName();
        mTypeId = parameter.getTypeId();
        mPriority = parameter.getPriority();
        mIsReplacePose = parameter.isReplacePose();
        if (mTypeId == Definition.NORMAL_POINT_TYPE) {
            deleteSetPoint();
            mPlaceName = Definition.START_CHARGE_PILE_POSE;
        }
        if (mIsReplacePose) {
            deletePoint(mPlaceName);
        }
        getCurrentPolicy(parameter);
        Log.d(Tag, "really start language: " + mLanguage + " mChargeMode=" + mChargeMode);
        Log.d(Tag, "mPlaceName: " + mPlaceName + " mTypeId: " + mTypeId + " mPriority: " + mPriority);
        registerPoseStatusListener();
        mApi.resetHead(mReqId);
        startChargePoseTimer();
        return true;
    }

    private void checkChargingType(AutoChargeBean parameter) {
        this.mChargingType = parameter.getSetStartChargePoseType();
        if (!TextUtils.isEmpty(mChargingType)) {
            return;
        }
        Log.d(Tag, "checkChargingType: chargingType=" + this.mChargingType);
        String chargingType = mManager.getRobotSettingManager().
                getRobotSetting(Definition.ROBOT_SETTINGS_CHARGING_TYPE);
        RobotLog.d(Tag, "checkChargingType: chargingType=" + chargingType);
        if (!TextUtils.isEmpty(chargingType)) {
            this.mChargingType = chargingType;
        }
    }

    private void getCurrentPolicy(AutoChargeBean parameter) {
        if (mChargingType.equals(Definition.CHARGING_TYPE_WIRE)) {
            mCurrentPolicy = mPolicy[Definition.WIRE_CHARGING_POLICY];
            return;
        }
        //mini初期充电桩硬件选型适配：底充、侧充等，已废弃
        int type = ConfigManager.getNavigationConfig().getChargePointType();
        if (type < 0 || type >= Definition.CHARGEPOINT_MAX_POLICY) {
            type = 0;
        }
        Log.d(Tag, "getCurrentPolicy: charging type=" + type);
        mCurrentPolicy = mPolicy[type];
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_SET_POSE_LOCATION:
                RobotLog.d(Tag, "setPoseLocation result = " + result);
                cancelChargePoseTimer();
                if ("succeed".equals(result)) {
                    switch (mCurState) {
                        case SET_CHARGE_POSE://设置回充点
                            mCurState = State.SET_CHARGE_PILE; //开始设置充电桩
                            if (mChargeMode == AutoChargeBean.CHARGE_MODE_NORMAL) {
                                mChargingPile.setTypeId(Definition.CHARGING_POLE_TYPE);
                                mChargingPile.setPriority(mPriority);
                                setStartChargeLocation(mChargingPile, mPlaceName);
                            } else {
                                mChargingPile.setTypeId(Definition.CHARGING_POLE_TYPE);
                                mChargingPile.setPriority(mPriority);
                                setStartChargeAndPoint(mChargingPile, mPlaceName);
                            }
                            break;
                        case SET_CHARGE_PILE://设置充电桩
                            handleChargePileSetSuccess();
                            break;
                        case SET_LOCATE_POSE://设置定位点
                            handleLocatePileSetSuccess();
                            break;
                        default:
                            break;
                    }
                } else {
                    onResultFail("StartChargePointAction set failed");
                }
                break;
            case Definition.CMD_NAVI_DELETE_MAPPING_POSE:
                RobotLog.d(Tag, "delete_mapping_pose result = " + result);
                break;
            case Definition.CMD_NAVI_ADD_MAPPING_POSE:
                RobotLog.d(Tag, "setPointLocation result = " + result);
                cancelChargePoseTimer();
                switch (mCurState) {
                    case SET_CHARGE_POSE://设置回充点
                        mCurState = State.SET_CHARGE_PILE;
                        mChargingPile.setTypeId(Definition.CHARGING_POLE_TYPE);
                        mChargingPile.setPriority(mPriority);
                        setStartChargeAndPoint(mChargingPile, mPlaceName);
                        break;
                    case SET_CHARGE_PILE://设置充电桩
                        handleChargePileSetSuccess();
                        break;
                    case SET_LOCATE_POSE://设置定位点
                        handleLocatePileSetSuccess();
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }
        return false;
    }

    private void handleChargePileSetSuccess() {
        handleChangeSetSuccess(Definition.STATUS_SET_CHARGE_PILE);
    }

    private void handleLocatePileSetSuccess() {
        handleChangeSetSuccess(Definition.STATUS_SET_LOCATE_PILE);
    }

    private void handleChangeSetSuccess(String status) {
        Log.d(Tag, "handleChargePileSetSuccess: mCurState=" + mCurState + " status=" + status);
        mCurState = State.IDLE;
        sendStatus(status, "true");
        unRegisterPoseStatusListener();// 设置完充电桩和回充点后,取消注册
        onResult(Definition.RESULT_OK, "StartChargePointAction set success ===");
    }

    /**
     * get curPose when charging success
     */
    private void registerPoseStatusListener() {
        registerStatusListener(Definition.STATUS_POSE, mPoseListener);
    }

    private StatusListener mPoseListener = new StatusListener() {
        @Override
        public void onStatusUpdate(String data) {
            if (mCurState != State.STARTING)
                return;
            if (TextUtils.isEmpty(data)) {
                onResultFail("listen PoseStatusListener result is empty ");
                return;
            }
            Gson gson = new Gson();
            Pose pose = gson.fromJson(data, Pose.class);
            if (null == pose) {
                onResultFail("can't get current Pose position");
                return;
            }
            RobotLog.d(Tag, "calculate StartChargePoint pose " + pose.toJson());
            mChargingPile = pose;
            mCurState = State.CALCULATING;
            setStartChargePoint(pose);
        }
    };

    private float getStartPointDistance() {
        float startPointDistance;
        int chargingEnvironment;
        try {
            chargingEnvironment = Integer.parseInt(mManager.getRobotSettingManager().
                    getRobotSetting(Definition.ROBOT_SETTINGS_CHARGING_ENVIRONMENT));
        } catch (Exception e) {
            chargingEnvironment = -1;
        }

        switch (chargingEnvironment) {
            case CHARGE_ENVIRONMENT_VISION:
                startPointDistance = 0.7f;
                break;
            case CHARGE_ENVIRONMENT_NARROW:
                startPointDistance = 0.5f;
                break;
            default:
                startPointDistance = 0.9f;
                break;
        }

        RobotLog.d(Tag, "getStartPointDistance   startPointDistance: " + startPointDistance + ", chargingEnvironment: " + chargingEnvironment);
        return startPointDistance;
    }

    /**
     * calculate StartChargePoint pose when charging success .
     *
     * @param pose
     */
    private void setStartChargePoint(Pose pose) {
        Pose startChargePose;
        String placeName;
        if (TextUtils.equals(mChargingType, Definition.CHARGING_TYPE_WIRE)) {
            mCurState = State.SET_LOCATE_POSE; //设置定位点
            startChargePose = pose;
            placeName = checkPlaceName(mPlaceName);
            mTypeId = Definition.POSITIONING_POINT_TYPE;
        } else {
            mCurState = State.SET_CHARGE_POSE; //设置回充点
            startChargePose = mCurrentPolicy.calculate(pose);
            placeName = checkPlaceName(mPlaceName);
            mTypeId = Definition.CHARGING_POINT_TYPE;
        }
        Log.d(Tag, "setStartChargePoint curState：" + mCurState + " pose：" + startChargePose.toString() + " placeName：" + placeName);
        startChargePose.setName(placeName);
        startChargePose.setTypeId(mTypeId);
        startChargePose.setPriority(mPriority);
        Log.d(Tag, "mChargeMode: " + mChargeMode);
        if (mChargeMode == AutoChargeBean.CHARGE_MODE_NORMAL) {
            setStartChargeLocation(startChargePose, placeName);// 这里需要先进行定位,否则setLocation 失败。
        } else if (mChargeMode == AutoChargeBean.CHARGE_MODE_NORMAL_SET_POINT) {
            setPointNormalModelByEdit(startChargePose);
        } else {
            setStartChargeAndPoint(startChargePose, placeName);
        }
    }

    private interface IChargePointPolicy {
        Pose calculate(Pose pose);
    }

    /**
     * 视觉侧充，回充点在充电桩点位正前方固定距离处，距离为 0.7m
     */
    private class VisionSideCharging implements IChargePointPolicy {
        @Override
        public Pose calculate(Pose pose) {
            float x = pose.getX();
            float y = pose.getY();
            float theta = pose.getTheta();
            float startPointDistance = getStartPointDistance();

            Pose startChargePose = new Pose();
            startChargePose.setX((float) (x + startPointDistance * Math.cos(theta)));
            startChargePose.setY((float) (y + startPointDistance * Math.sin(theta)));
            startChargePose.setTheta(pose.getTheta());
            Log.d(Tag, "VisionSideCharging： startChargePose:" + startChargePose.toString());

            return startChargePose;
        }
    }

    /**
     * 线充方式策略：充电桩、回充点、定位点“三点合一”设置
     */
    private class WireChargingPolicy implements IChargePointPolicy {
        @Override
        public Pose calculate(Pose pose) {
            Log.d(Tag, "WireChargingPolicy calculate: pose" + pose.toString());
            return pose;
        }
    }

    private class FaceToPolicy implements IChargePointPolicy {
        @Override
        public Pose calculate(Pose pose) {
            float x = pose.getX();
            float y = pose.getY();
            float theta = pose.getTheta() - (float) Math.PI;
            float startPointDistance = getStartPointDistance();

            Pose startChargePose = new Pose();
            startChargePose.setX((float) (x + (startPointDistance - ROVER_RADIUS) * Math.cos(theta)));
            startChargePose.setY((float) (y + (startPointDistance - ROVER_RADIUS) * Math.sin(theta)));
            startChargePose.setTheta(pose.getTheta());
            Log.d(Tag, "FaceTo startChargePose:" + startChargePose.toString());

            return startChargePose;
        }
    }

    private class BackToPolicy implements IChargePointPolicy {
        @Override
        public Pose calculate(Pose pose) {
            float x = pose.getX();
            float y = pose.getY();
            float theta = pose.getTheta();
            float startPointDistance = getStartPointDistance();

            Pose startChargePose = new Pose();
            startChargePose.setX((float) (x + (startPointDistance - ROVER_RADIUS) * Math.cos(theta)));
            startChargePose.setY((float) (y + (startPointDistance - ROVER_RADIUS) * Math.sin(theta)));
            startChargePose.setTheta(pose.getTheta());
            Log.d(Tag, "BackTo startChargePose:" + startChargePose.toString());

            return startChargePose;
        }
    }

    private class VisionPolicy implements IChargePointPolicy {
        @Override
        public Pose calculate(Pose pose) {
            float x = pose.getX();
            float y = pose.getY();
            float theta = pose.getTheta();
            float startPointDistance = getStartPointDistance() + ROVER_RADIUS
                    - ConfigManager.getNavigationConfig().getSpring2WallDistance()
                    + ConfigManager.getNavigationConfig().getInfrared2WallDistance();

            Pose startChargePose = new Pose();
            startChargePose.setX((float) (x + startPointDistance * Math.cos(theta)));
            startChargePose.setY((float) (y + startPointDistance * Math.sin(theta)));
            startChargePose.setTheta(pose.getTheta());
            Log.d(Tag, "Vision startChargePose:" + startChargePose.toString()
                    + ", startPointDistance:" + startPointDistance);

            return startChargePose;
        }
    }

    private void setStartChargeAndPoint(Pose pose, String placeName) {
        if (pose == null) {
            RobotLog.d(Tag, "setStartChargeLocation pose is Null !!!");
            onResultFail("Try to set null pose location");
            return;
        }

        try {
            RobotLog.d(Tag, "setStartChargeAndPoint: placeName" + placeName
                    + " pose=" + pose.toString());
            pose.setName(placeName);
            mApi.setPointLocation(0, mGson.toJson(pose));
        } catch (Exception e) {
            e.printStackTrace();
            onResultFail("setStartChargeLocation json error :" + e.getLocalizedMessage());
            RobotLog.d(Tag, "setStartChargeLocation json error : " + e.getLocalizedMessage());
        }
    }

    private void setStartChargeLocation(Pose pose, String placeName) {
        if (pose == null) {
            RobotLog.d(Tag, "setStartChargeLocation pose is Null !!!");
            onResultFail("Try to set null pose location");
            return;
        }
        JSONObject json = new JSONObject();
        try {
            json.put("x", pose.getX());
            json.put("y", pose.getY());
            json.put("theta", pose.getTheta());
            json.put("name", placeName);
            json.put("typeId", pose.getTypeId());
            json.put("priority", pose.getPriority());
            mApi.setPoseLocation(0, json.toString());
        } catch (JSONException e) {
            e.printStackTrace();
            onResultFail("setStartChargeLocation json error :" + e.getLocalizedMessage());
            RobotLog.d(Tag, "setStartChargeLocation json error : " + e.getLocalizedMessage());
        }
    }

    // 编辑地图创建点位时，直接将计算好的回充点和充电桩点位 或者 定位点数据返回
    private void setPointNormalModelByEdit(Pose pose) {
        poseList.clear();
        if (TextUtils.equals(mChargingType, Definition.CHARGING_TYPE_WIRE)) {
            // 定位点
            poseList.add(pose);
        } else {
            // 回充点
            poseList.add(pose);
            // 充电桩
            mChargingPile.setName(mPlaceName);
            mChargingPile.setTypeId(Definition.CHARGING_POLE_TYPE);
            mChargingPile.setPriority(mPriority);
            poseList.add(mChargingPile);
        }
        String jsonString = new Gson().toJson(poseList);
        if (jsonString != null) {
            mCurState = State.IDLE;
            unRegisterPoseStatusListener();
            onResult(Definition.RESULT_OK, jsonString);
        } else {
            onResultFail("CHARGE_MODE_NORMAL_SET_POINT ERROR");
        }
    }

    public void unRegisterPoseStatusListener() {
        RobotLog.d(Tag, "unRegisterPoseStatusListener mPoseStatusListener");
        unregisterStatusListener(Definition.STATUS_POSE);
    }


    private void startChargePoseTimer() {
        timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                onResultFail("setStartChargeLocation timeout " + timeout);
            }
        }, timeout);
    }

    private void onResultFail(String message) {
        Log.d(Tag, "onResultFail : " + message);
        mCurState = State.IDLE;
        deleteSetPoint();
        unRegisterPoseStatusListener();// 设置完充电桩和回充点后,取消注册
        onResult(Definition.RESULT_FAILURE, message);
        cancelChargePoseTimer();
    }

    /**
     * 10.3以后允许建图的时候设置多个特殊点位,是否需要删除该方法？？？
     * 删除是以名字为标识的，现在设置多个特殊点名字不一样，暂时不保留
     */
    private void deleteSetPoint() {
        if (mChargeMode == AutoChargeBean.CHARGE_MODE_SET_POINT) {
            mApi.removePointLocation(0, Definition.START_BACK_CHARGE_POSE);
            mApi.removePointLocation(0, Definition.START_CHARGE_PILE_POSE);
            if (mChargingType.equals(Definition.CHARGING_TYPE_WIRE)) {
                mApi.removePointLocation(0, Definition.LOCATE_POSITION_POSE);
            }
        }
    }

    /**
     * 替换点位前, 需要删除旧的同名点位
     */
    private void deletePoint(String placeName) {
        if (mChargeMode == AutoChargeBean.CHARGE_MODE_SET_POINT && mIsReplacePose) {
            mApi.removePointLocation(0, placeName);
            mApi.removePointLocation(0, checkPlaceName(placeName));
            if (mChargingType.equals(Definition.CHARGING_TYPE_WIRE)) {
                mApi.removePointLocation(0, placeName);
            }
        }
    }

    private void cancelChargePoseTimer() {
        if (timer != null) {
            timer.cancel();
            timer = null;
        }
    }

    private String checkPlaceName(String targetName) {
        boolean containsDigit = targetName.chars().anyMatch(Character::isDigit);
       if (mPlaceName == null || mPlaceName.isEmpty()) { // 兜底处理
           mPlaceName = Definition.START_CHARGE_PILE_POSE + mPriority;
           return Definition.START_BACK_CHARGE_POSE + mPlaceName;
       } else if(mTypeId == Definition.NORMAL_POINT_TYPE
           || (!containsDigit && mTypeId == Definition.CHARGING_POLE_TYPE)) {
           return Definition.START_BACK_CHARGE_POSE;
        } else if (mTypeId == Definition.CHARGING_POLE_TYPE){
            return Definition.START_BACK_CHARGE_POSE + "-" + targetName;
        } else {
            return targetName;
        }
    }

    @Override
    protected boolean verifyParam(AutoChargeBean param) {
        autoChargeBean = param;
        mReqId = autoChargeBean.getReqId();
        timeout = autoChargeBean.getTimeout();
        if (timeout < 30 * Definition.SECOND) {
            timeout = 30 * Definition.SECOND;
        }
        return true;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        Log.d(Tag, "stopAction ==================");
        cancelChargePoseTimer();
        mCurState = State.IDLE;
        return true;
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return null;
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return null;
    }
}