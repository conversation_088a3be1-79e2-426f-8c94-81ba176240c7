package com.ainirobot.coreservice.bean;

/**
 * Data: 2022/11/30 15:59
 * Author: wanglijing
 * Description: BreakSpeedThresholdBean
 * 各刹车档位最大速度bean：
 *  -1：为白名单，不限制速度
 *  ""：使用默认值
 */
public class BreakSpeedThresholdBean {

    public static String DEFAULT_BREAK_LEVEL_1_BACK_SPEED_MAX = "0.5";
    public static String DEFAULT_BREAK_LEVEL_2_BACK_SPEED_MAX = "0.8";
    public static String DEFAULT_BREAK_LEVEL_3_BACK_SPEED_MAX = "1.0";

    private String break_level_1_meal_back_speed_max = "";
    private String break_level_2_meal_back_speed_max = "";
    private String break_level_3_meal_back_speed_max = "";
    private String break_level_1_meal_deliver_speed_max = "";
    private String break_level_2_meal_deliver_speed_max = "";
    private String break_level_3_meal_deliver_speed_max = "";


    public String getBreak_level_1_meal_back_speed_max() {
        return break_level_1_meal_back_speed_max;
    }

    public void setBreak_level_1_meal_back_speed_max(String break_level_1_meal_back_speed_max) {
        this.break_level_1_meal_back_speed_max = break_level_1_meal_back_speed_max;
    }

    public String getBreak_level_2_meal_back_speed_max() {
        return break_level_2_meal_back_speed_max;
    }

    public void setBreak_level_2_meal_back_speed_max(String break_level_2_meal_back_speed_max) {
        this.break_level_2_meal_back_speed_max = break_level_2_meal_back_speed_max;
    }

    public String getBreak_level_3_meal_back_speed_max() {
        return break_level_3_meal_back_speed_max;
    }

    public void setBreak_level_3_meal_back_speed_max(String break_level_3_meal_back_speed_max) {
        this.break_level_3_meal_back_speed_max = break_level_3_meal_back_speed_max;
    }

    public String getBreak_level_1_meal_deliver_speed_max() {
        return break_level_1_meal_deliver_speed_max;
    }

    public void setBreak_level_1_meal_deliver_speed_max(String break_level_1_meal_deliver_speed_max) {
        this.break_level_1_meal_deliver_speed_max = break_level_1_meal_deliver_speed_max;
    }

    public String getBreak_level_2_meal_deliver_speed_max() {
        return break_level_2_meal_deliver_speed_max;
    }

    public void setBreak_level_2_meal_deliver_speed_max(String break_level_2_meal_deliver_speed_max) {
        this.break_level_2_meal_deliver_speed_max = break_level_2_meal_deliver_speed_max;
    }

    public String getBreak_level_3_meal_deliver_speed_max() {
        return break_level_3_meal_deliver_speed_max;
    }

    public void setBreak_level_3_meal_deliver_speed_max(String break_level_3_meal_deliver_speed_max) {
        this.break_level_3_meal_deliver_speed_max = break_level_3_meal_deliver_speed_max;
    }

    @Override
    public String toString() {
        return "{" +
                "break_level_1_meal_back_speed_max='" + break_level_1_meal_back_speed_max + '\'' +
                ", break_level_2_meal_back_speed_max='" + break_level_2_meal_back_speed_max + '\'' +
                ", break_level_3_meal_back_speed_max='" + break_level_3_meal_back_speed_max + '\'' +
                ", break_level_1_meal_deliver_speed_max='" + break_level_1_meal_deliver_speed_max + '\'' +
                ", break_level_2_meal_deliver_speed_max='" + break_level_2_meal_deliver_speed_max + '\'' +
                ", break_level_3_meal_deliver_speed_max='" + break_level_3_meal_deliver_speed_max + '\'' +
                '}';
    }
}
