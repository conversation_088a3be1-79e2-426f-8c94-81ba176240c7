/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.client.messagedispatcher;

import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.client.listener.ActionListener;

class ActionMessage implements IMsgHandle, IRecyclable {
    private static final String TAG = ActionMessage.class.getSimpleName();
    private static RecyclablePool<ActionMessage> sPool = new RecyclablePool<>();

    @Override
    public void handleMessage() {
        try {
            Log.d(TAG, "msgType : " + msgType);
            switch (msgType) {
                case result:
                    msgListener.onResult(msgCode, msgStr, extraData);
                    msgListener.onResult(msgCode, msgStr);
                    break;
                case error:
                    msgListener.onError(msgCode, msgStr, extraData);
                    msgListener.onError(msgCode, msgStr);
                    break;
                case status:
                    msgListener.onStatusUpdate(msgCode, msgStr, extraData);
                    msgListener.onStatusUpdate(msgCode, msgStr);
                    break;
            }
        } catch (RemoteException e) {

        }
        sPool.recycle(this);
    }

    @Override
    public void recycle() {
        Log.d(TAG, "recycle : " + msgStr+",");
        msgStr = null;
        extraData = null;
        msgListener = null;
    }

    enum MsgType {
        result, error, status
    }

    MsgType msgType;
    String msgStr;
    String extraData;
    ActionListener msgListener;
    int msgCode;

    private ActionMessage(ActionListener msgListener, MsgType msgType, String msgStr, int msgCode,
                          String extraData) {
        this.msgCode = msgCode;
        this.msgStr = msgStr;
        this.extraData = extraData;
        this.msgType = msgType;
        this.msgListener = msgListener;
    }

    static ActionMessage obtain(ActionListener msgListener, MsgType msgType, String msgStr,
                                int msgCode, String extraData) {
        ActionMessage actionMessage = sPool.obtain();

        if (actionMessage == null) {
            actionMessage = new ActionMessage(msgListener, msgType, msgStr, msgCode, extraData);
        } else {
            actionMessage.msgType = msgType;
            actionMessage.msgStr = msgStr;
            actionMessage.msgCode = msgCode;
            actionMessage.extraData = extraData;
            actionMessage.msgListener = msgListener;
        }
        return actionMessage;
    }
}
