package com.ainirobot.coreservice.action.download_map.state;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.download_map.DownloadMapStateEnum;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.DownloadMapBean;
import com.ainirobot.coreservice.config.ConfigManager;

import org.json.JSONObject;

import java.io.File;

public class DownloadMapExtraState extends BaseDownloadMapPkgState {

    private boolean hasLocalMap;

    private static final int VISION_TYPE = 0x02;
    private static final int VISION_TARGET_TYPE = 0x03;

    @Override
    protected void onStart(String paramsStr, DownloadMapBean downloadMapBean) {
        hasLocalMap = downloadMapBean.hasLocalMap();
        if(!hasLocalMap){ //下载地图
            String mapSupportType = downloadMapBean.getMapSupportType();
            try {
                int supportType = Integer.parseInt(mapSupportType.substring(2), 16);
                if ((supportType == VISION_TYPE || supportType == VISION_TARGET_TYPE)
                        && ConfigManager.isRobotSupportVision()) {
                    downloadExtra(paramsStr, downloadMapBean);
                } else {
                    Log.d(TAG, "not support vision");
                    onStateRunSuccess();
                }
            } catch (Exception e) {
                e.printStackTrace();
                onStateRunFailed(0, "get support type error");
            }
        }else { //导入地图
            onStateRunSuccess();
        }
    }

    private void downloadExtra(String paramsStr, DownloadMapBean downloadMapBean) {
        if (paramsIsInvalid(downloadMapBean)) {
            onStateRunFailed(0, "downloadExtra params is error");
        } else {
            sendCommand(0, Definition.CMD_REMOTE_DOWNLOAD_EXTRA_FILE_PKG, paramsStr);
        }
    }

    private boolean paramsIsInvalid(DownloadMapBean downloadMapBean) {
        boolean urlEmpty, md5Empty = false, idEmpty = false;
        if ((urlEmpty = TextUtils.isEmpty(downloadMapBean.getExtraUrl())) ||
                (md5Empty = TextUtils.isEmpty(downloadMapBean.getExtraMd5())) ||
                (idEmpty = TextUtils.isEmpty(downloadMapBean.getExtraId()))) {
            Log.e(TAG, "downloadExtra  urlEmpty: " + urlEmpty + " md5Empty: " + md5Empty + " idEmpty: " + idEmpty);
            return true;
        }
        return false;
    }

    @Override
    public boolean onCmdResponse(int cmdId, String command, String result, String extraData) {
        if (TextUtils.equals(command, Definition.CMD_REMOTE_DOWNLOAD_EXTRA_FILE_PKG)) {
            if (TextUtils.equals(Definition.SUCCEED, result)) {
                sendCommand(0, Definition.CMD_NAVI_SET_EXTRA_FILE_DATA, extraData);
                deleteExtraZip(extraData);
                onStateRunSuccess();
            } else {
                onStateRunFailed(0, "download extra is failed");
            }
            return true;
        }
        return false;
    }

    private void deleteExtraZip(String extraData) {
        JSONObject jsonObject;
        try {
            jsonObject = new JSONObject(extraData);
            String extraPath = jsonObject.optString(Definition.JSON_MAP_EXTRA_LOCAL_PATH);
            File mapZipFile = new File(extraPath);
            if (mapZipFile.exists()) {
                mapZipFile.delete();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected DownloadMapStateEnum getNextState() {
        return !hasLocalMap ? DownloadMapStateEnum.DOWNLOAD_SUCCEED : DownloadMapStateEnum.SET_IMPORT_MAP_SATE;
    }
}
