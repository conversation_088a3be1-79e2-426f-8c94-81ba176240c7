package com.ainirobot.coreservice.action.advnavi.state;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.BasePoseBean;
import com.ainirobot.coreservice.client.actionbean.MultiRobotConfigBean;
import com.ainirobot.coreservice.client.actionbean.MultiRobotStatus;
import com.ainirobot.coreservice.client.actionbean.NavigationAdvancedBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.utils.DelayTask;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;
import java.util.Vector;

/**
 * Data: 2022/9/21 17:37
 * Author: wanglijing
 * 多机导航base类
 * preAction:初始化准备状态中获得的多机信息
 */
public abstract class BaseMultiRobotNaviState extends BaseNavigationState {
    /**
     * 多机配置信息
     */
    protected MultiRobotConfigBean mMultiRobotConfig;

    /**
     * 参数传过来的、地图中存在的有效备用点列表
     * **可能为空**
     */
    protected List<Pose> mValidStandbyList;

    /**
     * 已经尝试执行过的备用点列表，主目标点如果失败会直接结束任务
     */
    protected Vector<String> mExecutedPoseVector;

    /**
     * 是否立即上报避障状态
     */
    private boolean mIsNeedAvoidNotifyImmediately;


    /**
     * 多机情况下机器人目标点0.5m范围内进行聚合
     */
    protected static final double DEFAULT_DESTINATION_AVAILABLE_RANGE = 0.5;
    protected static final double DEFAULT_DESTINATION_CHANGE_GOAL_OFFSET = 2;

    /**
     * 多机判断是否有机器占用目的地的范围
     */
    protected double mInDestinationRange = DEFAULT_DESTINATION_AVAILABLE_RANGE;

    protected static final String DELAY_TAG = "delay_conform";

    BaseMultiRobotNaviState(String name) {
        super(name);
    }

    @Override
    protected boolean isEnableMultiRobotStatus() {
        return true;
    }

    @Override
    protected void preAction(StateData data, IStateListener listener) {
        super.preAction(data, listener);
        mExecutedPoseVector = new Vector<String>();
        mValidStandbyList = mStateData.getValidStandbyList();
        mMultiRobotConfig = mStateData.getMultiRobotConfigBean();
        mIsNeedAvoidNotifyImmediately = mNavigationBean.isNeedAvoidNotifyImmediately();
        mInDestinationRange = mNavigationBean.getInDestinationRange();
    }

    @Override
    protected void doAction() {
        super.doAction();
        checkDestinationExist();
        if (isInMainDestination()) {
            Log.i(TAG, "doAction is already in destination");
            arrivedMainDestination();
        }
        registerMultiRobotStatusListener();
    }

    @Override
    protected void exit() {
        DelayTask.cancel(DELAY_TAG);
        super.exit();
    }

    @Override
    protected void startNavigation(NavigationAdvancedBean navigationBean) {
        //super前新增导航点位pose对象
        super.startNavigation(navigationBean);
    }

    protected void startNavigation(String destination, boolean onlyOnePose) {
        try {
            NavigationAdvancedBean bean = cloneNaviBean();
            bean.setDestination(destination);
            bean.setMaxAvoidCount(onlyOnePose
                    ? mNavigationBean.getLastAvoidMaxCnt()
                    : mNavigationBean.getMaxAvoidCount());

            startNavigation(bean);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onNavigationStatusUpdate(int result, String message, String extraData) {
        switch (result) {
            case Definition.STATUS_START_NAVIGATION:
                processStatusWithDestination(mHasStartNavigation
                        ? Definition.STATUS_NAVI_REPLACE_DESTINATION
                        : Definition.STATUS_START_NAVIGATION, extraData);

                break;
            //ComponentStatus.STATUS_OBSTACLES_AVOID
            case Definition.STATUS_NAVI_OBSTACLES_AVOID:
                //ComponentStatus.STATUS_NAVIGATION_AVOID
            case Definition.STATUS_NAVI_AVOID:
                if (mIsNeedAvoidNotifyImmediately) {
                    //ComponentStatus.STATUS_NAVIGATION_AVOID_IMMEDIATELY
                    onStatusUpdate(Definition.STATUS_NAVI_AVOID_IMMEDIATELY, extraData);
                    break;
                }
                //ComponentStatus.STATUS_NAVIGATION_AVOID_END
            case Definition.STATUS_NAVI_AVOID_END:
                //ComponentStatus.STATUS_ESTIMATE_LOST
            case Definition.STATUS_ESTIMATE_LOST:
                //ComponentStatus.STATUS_DISTANCE_WITH_DESTINATION
            case Definition.STATUS_DISTANCE_WITH_DESTINATION:
            default:
                processStatusWithDestination(result, extraData);
                break;
        }
    }

    @Override
    protected void onNavigationError(int result, String message, String extraData) {
        processResultWithDestination(result, extraData);
    }

    @Override
    protected void onNavigationResult(int result, String message, String extraData) {
        switch (result) {
            case Definition.ERROR_IN_DESTINATION:
            case Definition.RESULT_OK:
                //ComponentResult.RESULT_NAVIGATION_TRANSFER_ARRIVED,
                arrivedMainDestination();
                break;
        }
    }

    @Override
    protected void handleMultipleRobotStatus(List<MultiRobotStatus> robotStatusList) {

    }

    /**
     * 判断某个点位当前是不是可用
     */
    protected boolean isPoseInUseCurrently(Pose pose, List<MultiRobotStatus> robotStatusList) {
        if (pose == null) {
            return false;
        }
        for (int i = 0; i < robotStatusList.size(); i++) {
            MultiRobotStatus robotStatus = robotStatusList.get(i);
            if (robotStatus.isCurRobot()) {
                Log.d(TAG, "isPoseInUseCurrently current robot");
                continue;
            }
            //status 为1表示无定位 2表示未在导航 3表示在导航中
            BasePoseBean poseBean = null;
            switch (robotStatus.getStatus()) {
                case 2:
                    //不在导航中，如果机器人当前点位在目标点，则目标点也属于不可达
                    poseBean = robotStatus.getPose();
                    break;
                case 3:
                    //处于导航模式，如果目标点被占用，属于不可达
                    poseBean = robotStatus.getGoal();
                    break;
                default:
                    break;
            }
            if (poseBean == null) {
                continue;
            }
            boolean inUse = calculateTargetPoseInUse(pose, poseBean);
            if (inUse) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断距离是否小于参数设置的，多机点位膨胀范围内
     *
     * @return true：在范围内
     */
    protected boolean isInPoseRange(double distance) {
        boolean inRange = distance < mInDestinationRange;
        Log.d(TAG, "isInPoseRange: " + inRange);
        return inRange;
    }


    /**
     * 是否已经在目的地了
     */
    protected boolean isInMainDestination() {
        Pose curPose = getCurrentPose();
        if (curPose == null || mDestinationPose == null) {
            return false;
        }
        return isInPoseRange(curPose.getDistance(mDestinationPose));
    }


    /**
     * 判断是否到达某点位范围内
     *
     * @param pose     机器人想要去的目标点
     * @param goalPose 另外一台机器当前导航的目标点
     * @return true 目标点不能到达 false  目标点可以去
     */
    protected boolean calculateTargetPoseInUse(Pose pose, BasePoseBean goalPose) {
        if (pose == null && goalPose == null) {
            return false;
        }
        double distance = Math.sqrt(Math.pow((pose.getX() - goalPose.getX()), 2)
                + Math.pow((pose.getY() - goalPose.getY()), 2));
        boolean inUse = isInPoseRange(distance);
        Log.d(TAG, "Target Pose " + pose.getName() + "distance: " + distance
                + ", inRange: " + inUse
                + ", pose:" + pose.toString()
                + ", goal:" + goalPose.toString());
        return inUse;
    }

    /**
     * 判断是否当前只有一个可用备用点位
     */
    protected boolean isOneAvailableStandbyPoseCurrently() {
        if (mRobotStatusList == null || mRobotStatusList.size() <= 0) {
            return true;
        }
        int availableCnt = 0;
        for (int i = 0; i < mValidStandbyList.size(); i++) {
            Pose pose = mValidStandbyList.get(i);
            if (mExecutedPoseVector != null && mExecutedPoseVector.contains(pose.getName())) {
                continue;
            }
            boolean isInUse = isPoseInUseCurrently(pose, mRobotStatusList);
            if (!isInUse) {
                availableCnt++;
            }
        }
        return availableCnt <= 1;
    }


    /**
     * 计算两个点位之间的间距
     */
    protected double calculatePoseDistance(Pose pose, BasePoseBean goalPose) {
        if (pose == null || goalPose == null) {
            return Double.MAX_VALUE;
        }
        double distance = Math.sqrt(Math.pow((pose.getX() - goalPose.getX()), 2)
                + Math.pow((pose.getY() - goalPose.getY()), 2));
        Log.d(TAG, "calculatePoseDistance: " + distance);
        return distance;
    }

    /**
     * 计算两个点位之间的间距
     */
    protected double calculatePoseDistance(Pose pose, Pose goalPose) {
        if (pose == null || goalPose == null) {
            return Double.MAX_VALUE;
        }
        double distance = Math.sqrt(Math.pow((pose.getX() - goalPose.getX()), 2)
                + Math.pow((pose.getY() - goalPose.getY()), 2));
        Log.d(TAG, "calculatePoseDistance: " + distance);
        return distance;
    }


    /**
     * 检查目的地是否存在
     */
    private void checkDestinationExist() {
        Log.d(TAG, "checkDestinationExist : " + mDestinationPose);
        if (mDestinationPose == null) {
            //ERROR_DESTINATION_NOT_EXIST
            onResult(Definition.ERROR_DESTINATION_NOT_EXIST);
        }
    }

    /**
     * 获得当前坐标点
     */
    protected Pose getCurrentPose() {
        try {
            String poseInfo = getRobotInfo(Definition.SYNC_ACTION_GET_CURRENT_LOCATION);
            return mGson.fromJson(poseInfo, Pose.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 获得当前所在位置名称，如果配置信息中没有，从备用列表中获得
     *
     * @return 点位名称
     */
    protected String getCurrentPlaceName() {
        String currentPlace = mNavigationBean.getCurrentPlace();

        if (TextUtils.isEmpty(currentPlace)
                || !isPlaceExit(currentPlace)) {

            Pose pose = getCurrentPose();   //当前点位信息
            for (int i = 0; i < mValidStandbyList.size(); i++) {
                Pose poseInfo = mValidStandbyList.get(i);
                Log.e(TAG, "poseInfo:" + poseInfo.toString());
                if (isInPoseRange(poseInfo.getDistance(pose))) { //是否在点位附近
                    currentPlace = poseInfo.getName();
                    break;
                }
            }
            Log.e(TAG, "mCurrentPlace is null, find in list is:"
                    + currentPlace + ", pose:" + (pose == null ? "null" : pose.toString()));
        } else {
            Log.e(TAG, "has mCurrentPlace:" + currentPlace);
        }
        return currentPlace;
    }

    /**
     * 判断目标点是否可以到达，如果目标点被占用，则返回失败
     */
    protected DestinationState judgeDestinationIsAvailable(List<MultiRobotStatus> robotStatusList) {
        if (mDestinationPose == null) {
            return DestinationState.DESTINATION_INVALID;
        }
        if (robotStatusList == null || robotStatusList.size() <= 0) {
            return DestinationState.AVAILABLE;
        }
        for (int j = 0; j < robotStatusList.size(); j++) {
            MultiRobotStatus robotStatus = robotStatusList.get(j);
            Log.d(TAG, "judgeDestinationIsAvailable" +
                    "  robot:" + robotStatus.getId() +
                    " ,status: " + robotStatus.getStatus());
            //status 为1表示无定位 2表示未在导航 3表示在导航中
            BasePoseBean poseBean = null;
            switch (robotStatus.getStatus()) {
                case 2:
                    //不在导航中，如果机器人当前点位在目标点，则目标点也属于不可达
                    poseBean = robotStatus.getPose();
                    break;
                case 3:
                    //处于导航模式，如果目标点被占用，属于不可达
                    poseBean = robotStatus.getGoal();
                    break;
                default:
                    break;
            }
            if (poseBean == null) {
                Log.e(TAG, "judgeDestinationIsAvailable"
                        + " current robot:" + robotStatus.getId()
                        + ", state:" + robotStatus.getStatus()
                        + " poseBean is null");
                continue;
            }
            boolean inUse = calculateTargetPoseInUse(mDestinationPose, poseBean);
            Log.d(TAG, "judgeDestinationIsAvailable inUse: " + inUse + " poseBean: " + poseBean);
            if (inUse) {
                //点位附近，判断是否为当前机器人
                if (robotStatus.isCurRobot()) {
                    //当前机器人在导航中
                    if (robotStatus.getStatus() == 3) {
                        return DestinationState.NAVI_IN_RANGE;
                    } else {
                        return DestinationState.NERA_DESTINATION;
                    }
                    //不是当前机器人，返回被占用
                } else {
                    return DestinationState.OCCUPIED;
                }
            }
        }
        return DestinationState.AVAILABLE;
    }

    /**
     * 在补位需要将出餐点添加到补位列表中
     */
    protected void addDestinationToStandbyList() {
        Pose destinationPose = getPoseByName(mDestination);
        if (destinationPose != null) {
            mValidStandbyList.add(0, destinationPose);
        }
    }

    protected boolean isPlaceExit(String placeName) {
        return TextUtils.equals(getRobotInfo(Definition.SYNC_ACTION_IS_PLACE_EXISTS, placeName),
                Definition.RESULT_TRUE);
    }

    /**
     * 目标点是否可到达的状态
     *
     * AVAILABLE 可到达的，当前没有任何机器占用该点位
     * NERA_DESTINATION 机器人当前已经在该目标点范围内
     * OCCUPIED 有机器在该目标点，或者有机器的导航点为该地
     * DESTINATION_INVALID 目标点无效，加的保护不会遇到
     * NAVI_IN_RANGE 当前机器已经有去目标点附近的导航任务
     */
    protected enum DestinationState {
        /**
         * 可到达的，当前没有任何机器占用该点位
         */
        AVAILABLE,
        /**
         * 有机器在该目标点，或者有机器的导航点为该地
         */
        OCCUPIED,
        /**
         * 机器人当前已经在该目标点范围内
         */
        NERA_DESTINATION,
        /**
         * 目标点无效，加的保护不会遇到
         */
        DESTINATION_INVALID,
        /**
         * 当前机器已经有去目标点附近的导航任务
         */
        NAVI_IN_RANGE,
        /**
         * 退出监听
         */
        EXIT,
    }
}
