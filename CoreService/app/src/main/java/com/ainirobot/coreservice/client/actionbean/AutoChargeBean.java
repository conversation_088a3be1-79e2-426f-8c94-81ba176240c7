/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.client.actionbean;

import com.ainirobot.coreservice.client.Definition;

public class AutoChargeBean extends BaseBean{

    public static final int CHARGE_MODE_NORMAL = 0;//建图完成设点
    public static final int CHARGE_MODE_SET_POINT = 1;//边建图边设点
    public static final int CHARGE_MODE_NORMAL_SET_POINT = 2;//编辑地图设点(支持多点位)

    private long timeout = 30 * Definition.SECOND; // timeout from starting autocharge to ending action
    private long avoidTimeout = 20 * Definition.SECOND;     // 20 s超时,是否导航0.1m .
    private double avoidDistance = 0.1;         // unit meter
    private double coordinateDeviation = 0.1f;  // unit meter
    private int chargeType = Definition.CHARGE_VISION_POLICY;
    private int chargePointType = Definition.BACKTO_CHARGEPOINT_POLICY;
    private int chargeMode = CHARGE_MODE_NORMAL;
    private String mapLanguage;
    private long mMultipleWaitTime = 300*Definition.SECOND;
    private String setStartChargePoseType;
    private int typeId; // 特殊点类型
    private int priority; // 特殊点优先级
    private String placeName; // 特殊点同类型支持多个后，不在固定点位名称
    private boolean isReplacePose = false; // 替换点位标记

    public double getCoordinateDeviation() {
        return coordinateDeviation;
    }

    public void setCoordinateDeviation(double coordinateDeviation) {
        this.coordinateDeviation = coordinateDeviation;
    }

    public long getAvoidTimeout() {
        return avoidTimeout;
    }

    public void setAvoidTimeout(long avoidTimeout) {
        this.avoidTimeout = avoidTimeout;
    }

    public double getAvoidDistance() {
        return avoidDistance;
    }

    public void setAvoidDistance(double avoidDistance) {
        this.avoidDistance = avoidDistance;
    }

    public long getTimeout() {
        return timeout;
    }

    public void setTimeout(long timeout) {
        this.timeout = timeout;
    }

    public int getChargeMode() {
        return chargeMode;
    }

    public void setChargeMode(int chargeMode) {
        this.chargeMode = chargeMode;
    }

    public int getChargeType() {
        return chargeType;
    }

    public void setChargeType(int chargeType) {
        this.chargeType = chargeType;
    }

    public int getChargePointType() {
        return chargePointType;
    }

    public void setChargePintType(int chargeType) {
        this.chargePointType = chargeType;
    }

    public String getMapLanguage() {
        return mapLanguage;
    }

    public void setMapLanguage(String mapLanguage) {
        this.mapLanguage = mapLanguage;
    }

    public long getMultipleWaitTime() {
        return mMultipleWaitTime;
    }

    public void setMultipleWaitTime(long multipleWaitTime) {
        mMultipleWaitTime = multipleWaitTime;
    }

    public String getSetStartChargePoseType() {
        return setStartChargePoseType;
    }

    public void setSetStartChargePoseType(String setStartChargePoseType) {
        this.setStartChargePoseType = setStartChargePoseType;
    }

    public void setTypeId(int typeId) {
        this.typeId = typeId;
    }

    public int getTypeId() {
        return typeId;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public int getPriority() {
        return priority;
    }

    public void setPlaceName(String placeName) {
        this.placeName = placeName;
    }

    public String getPlaceName() {
        return placeName;
    }

    public boolean isReplacePose() {
        return isReplacePose;
    }

    public void setReplacePose(boolean replacePose) {
        isReplacePose = replacePose;
    }

    @Override
    public String toString() {
        return "AutoChargeBean{" +
                "timeout=" + timeout +
                ", avoidTimeout=" + avoidTimeout +
                ", avoidDistance=" + avoidDistance +
                ", coordinateDeviation=" + coordinateDeviation +
                ", chargeType=" + chargeType +
                ", chargePointType=" + chargePointType +
                ", chargeMode=" + chargeMode +
                ", mapLanguage='" + mapLanguage + '\'' +
                ", mMultipleWaitTime=" + mMultipleWaitTime +
                ", setStartChargePoseType='" + setStartChargePoseType + '\'' +
                ", typeId=" + typeId +
                ", priority=" + priority +
                ", placeName=" + placeName +
                ", isReplacePose=" + isReplacePose +
                '}';
    }
}