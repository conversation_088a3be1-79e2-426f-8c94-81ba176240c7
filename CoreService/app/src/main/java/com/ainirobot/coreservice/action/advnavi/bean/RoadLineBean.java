package com.ainirobot.coreservice.action.advnavi.bean;

import com.ainirobot.coreservice.client.actionbean.Pose;

/**
 * Data: 2023/1/31 14:15
 * Author: wanglijing
 * Description: RoadLineBean
 */
public class RoadLineBean {

    private int id;
    /**
     * 路径宽度，范围（0.65 到 5），未设置默认 1.4，单位（m）
     */
    private double lineWidth = 1.4;
    private RoadGraphNode endPose;
    private RoadGraphNode startPose;
    /**
     * 路径方向，未设置默认 0 由导航决定，1 为前行，2 为后退，3 为多通(即双向)
     */
    private int rule;

    /**
     * 只有 (rule == 1 || rule == 2) 时才可设置“绝对单向”
     * 如果勾选【绝对单向行驶】，则此字段保存为：-1
     * 如果未勾选【绝对单向行驶】，或者选择【双向】，则此字段保存为：5
     */
    private double retrograde_cost = 5;


    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public RoadGraphNode getStartPose() {
        return startPose;
    }

    public void setStartPose(RoadGraphNode startPose) {
        this.startPose = startPose;
    }

    public void buildStartPose(Pose pose) {
        RoadGraphNode graphNode = new RoadGraphNode();
        graphNode.setPosition(new Vector2d(pose.getX(), pose.getY()));
        setStartPose(graphNode);
    }

    public RoadGraphNode getEndPose() {
        return endPose;
    }

    public void setEndPose(RoadGraphNode endPose) {
        this.endPose = endPose;
    }

    public void buildEndPose(Pose pose) {
        RoadGraphNode graphNode = new RoadGraphNode();
        graphNode.setPosition(new Vector2d(pose.getX(), pose.getY()));
        setEndPose(graphNode);
    }

    public double getLineWidth() {
        return lineWidth;
    }

    public void setLineWidth(double lineWidth) {
        this.lineWidth = lineWidth;
    }

    public int getRule() {
        return rule;
    }

    public void setRule(int rule) {
        this.rule = rule;
    }

    public double getRetrograde_cost() {
        return retrograde_cost;
    }

    public void setRetrograde_cost(double retrograde_cost) {
        this.retrograde_cost = retrograde_cost;
    }

    @Override
    public String toString() {
        return "{" +
                "id=" + id +
                ", lineWidth=" + lineWidth +
                ", endPose=" + endPose +
                ", startPose=" + startPose +
                ", rule=" + rule +
                ", retrograde_cost=" + retrograde_cost +
                '}';
    }
}
