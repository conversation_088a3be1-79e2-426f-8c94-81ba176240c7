/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.client.actionbean;

/**
 * Created by Orion on 2021/3/15.
 */
public class LanguageBean {

    private String langCode;
    private String langName;
    private int isDefault = UseState.NOT_DEFAULT;
    private int support = SupportState.NOT_SUPPORT;

    public static class UseState {
        public static final int NOT_DEFAULT = 0; //非默认语言
        public static final int DEFAULT = 1; //默认语言
    }

    public static class SupportState {
        public static final int NOT_SUPPORT = 0; //不支持的系统语言
        public static final int LOCAL = 1 << 0; //本地支持-1
        public static final int SERVER = 1 << 1; //服务端支持-2
        public static final int LOCAL_AND_SERVER = LOCAL | SERVER; //本地和服务端都支持-3
    }

    public String getLangCode() {
        return langCode;
    }

    public void setLangCode(String langCode) {
        this.langCode = langCode;
    }

    public String getLangName() {
        return langName;
    }

    public void setLangName(String langName) {
        this.langName = langName;
    }

    public int getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(int isDefault) {
        this.isDefault = isDefault;
    }

    public boolean isDefaultLanguage() {
        return this.isDefault == UseState.DEFAULT;
    }

    public int getSupport() {
        return support;
    }

    public void setSupport(int support) {
        this.support = support;
    }

    /**
     * 本地支持
     *
     * @return
     */
    public boolean isLocalSupport() {
        return ((this.support & SupportState.LOCAL) != 0);
    }

    /**
     * 服务端支持
     *
     * @return
     */
    public boolean isServerSupport() {
        return ((this.support & SupportState.SERVER) != 0);
    }

    /**
     * 本地和服务端都支持的系统语言(交集)
     *
     * @return
     */
    public boolean isSupportLocalAndServer() {
        return this.support == SupportState.LOCAL_AND_SERVER;
    }

    @Override
    public String toString() {
        return "LanguageBean{" +
                "langCode='" + langCode + '\'' +
                ", langName='" + langName + '\'' +
                ", isDefault=" + isDefault +
                ", support=" + support +
                '}';
    }
}
