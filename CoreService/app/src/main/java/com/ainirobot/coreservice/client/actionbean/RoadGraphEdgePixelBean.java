package com.ainirobot.coreservice.client.actionbean;

import com.ainirobot.coreservice.client.Definition;

public class RoadGraphEdgePixelBean {

    /**
     * 线段 id，从 0 递增，唯一不可重复
     */
    private int id;

    /**
     * 最高限速，范围（0.1 到 1.2），未设置默认 0 由导航决定，单位（m/s）
     */
    private double linear_speed_limit = 0;

    /**
     * 路径宽度，范围（0.65 到 5），未设置默认 1.4，单位（m）
     */
    private double line_width = 1.4;

    /**
     * 路径方向，未设置默认 0 由导航决定，1 为前行，2 为后退，3 为多通(即双向)
     */
    private int rule;

    /**
     * 只有 (rule == 1 || rule == 2) 时才可设置“绝对单向”
     * 如果勾选【绝对单向行驶】，则此字段保存为：-1
     * 如果未勾选【绝对单向行驶】，或者选择【双向】，则此字段保存为：5
     */
    private double retrograde_cost = 5;

    /**
     * 巡线调度设置，未设置默认 0 由导航决定，勾选后值为 -1 即禁止调度停靠
     */
    private double parking_cost = 0;

    /**
     * 起始端点
     */
    private RoadGraphNodeBean startNode;

    /**
     * 结束端点
     */
    private RoadGraphNodeBean endNode;

    /**
     * 特殊区域
     */
    private int scene_type = Definition.SCENE_NORMAL;

    public RoadGraphEdgePixelBean() {

    }

    public RoadGraphEdgePixelBean(int id, int rule, double linear_speed_limit,
                                  double line_width, double retrograde_cost, double parking_cost) {
        this.id = id;
        this.rule = rule;
        this.linear_speed_limit = linear_speed_limit;
        this.line_width = line_width;
        this.retrograde_cost = retrograde_cost;
        this.parking_cost = parking_cost;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getRule() {
        return rule;
    }

    public void setRule(int rule) {
        this.rule = rule;
    }

    public double getLinear_speed_limit() {
        return linear_speed_limit;
    }

    public void setLinear_speed_limit(double linear_speed_limit) {
        this.linear_speed_limit = linear_speed_limit;
    }

    public double getLine_width() {
        return line_width;
    }

    public void setLine_width(double line_width) {
        this.line_width = line_width;
    }

    public double getRetrograde_cost() {
        return retrograde_cost;
    }

    public void setRetrograde_cost(double retrograde_cost) {
        this.retrograde_cost = retrograde_cost;
    }

    public double getParking_cost() {
        return parking_cost;
    }

    public void setParking_cost(double parking_cost) {
        this.parking_cost = parking_cost;
    }

    public RoadGraphNodeBean getStartNode() {
        return startNode;
    }

    public void setStartNode(RoadGraphNodeBean startNode) {
        this.startNode = startNode;
    }

    public RoadGraphNodeBean getEndNode() {
        return endNode;
    }

    public void setEndNode(RoadGraphNodeBean endNode) {
        this.endNode = endNode;
    }

    /**
     * 不是SceneType.kSceneNormal，就认为是特殊区域
     * @return true: 是特殊区域
     */
    public boolean isSceneType() {
        return scene_type != Definition.SCENE_NORMAL;
    }

    public void setSceneType(int scene_type) {
        this.scene_type = scene_type;
    }

    public int getSceneType(){
        return scene_type;
    }

    public RoadGraphEdgePixelBean deepCopy() {
        RoadGraphEdgePixelBean newEdgePixel = new RoadGraphEdgePixelBean(this.getId(),
                this.getRule(),
                this.getLinear_speed_limit(),
                this.getLine_width(),
                this.getRetrograde_cost(),
                this.getParking_cost());
        newEdgePixel.setStartNode(new RoadGraphNodeBean(this.getStartNode().getId(),
                new Vector2dBean(this.getStartNode().getPosition().x,
                        this.getStartNode().getPosition().y)));
        newEdgePixel.setEndNode(new RoadGraphNodeBean(this.getEndNode().getId(),
                new Vector2dBean(this.getEndNode().getPosition().x,
                        this.getEndNode().getPosition().y)));
        newEdgePixel.setSceneType(this.scene_type);
        return newEdgePixel;
    }



    @Override
    public String toString() {
        return "RoadGraphEdge{" +
                "id=" + id +
                ", rule=" + rule +
                ", startNode=" + startNode +
                ", endNode=" + endNode +
                ", linear_speed_limit=" + linear_speed_limit +
                ", line_width=" + line_width +
                ", retrograde_cost=" + retrograde_cost +
                ", parking_cost=" + parking_cost +
                ", scene_type=" + scene_type +
                '}';
    }
}