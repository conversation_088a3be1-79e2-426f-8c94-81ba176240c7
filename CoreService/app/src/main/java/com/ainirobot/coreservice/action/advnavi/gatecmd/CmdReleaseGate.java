package com.ainirobot.coreservice.action.advnavi.gatecmd;

import com.ainirobot.coreservice.client.Definition;

/**
 * Date: 2024/2/22
 * Author: dengting
 * <p>
 * 释放闸机命令
 * * 成功/失败 都上报RESULT_OK
 * * 超时 重试释放
 */
public class CmdReleaseGate extends GateCommand {


    CmdReleaseGate(String name, CmdType intentType, GateCommandInterface commandInterface, GateCommandListener listener) {
        super(name, intentType, commandInterface, listener);
        initCmdType(CmdType.RELEASE_GATE);
    }

    @Override
    public void start() {
        super.start();
        releaseGateControl();
    }

    @Override
    public void onSuccess() {
        super.onSuccess();
        onResult(Definition.RESULT_OK, "release gate suc");
    }

    @Override
    public void onFail() {
        super.onFail();
        onResult(Definition.RESULT_OK, "release gate suc");
    }

    @Override
    protected void onRetry() {
        releaseGateControl();
    }
}
