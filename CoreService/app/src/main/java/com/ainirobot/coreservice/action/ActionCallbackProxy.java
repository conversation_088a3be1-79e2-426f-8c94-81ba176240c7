/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.action;

import android.os.IBinder;
import android.os.IBinder.DeathRecipient;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.listener.IActionListener;
import com.ainirobot.coreservice.client.log.RLog;

import java.util.concurrent.ExecutorService;

class ActionCallbackProxy {
    private static final String TAG = "ActionCallbackProxy";
    private IActionListener mCallback;
    private ExecutorService mCallbackExecutor;
    private DeathRecipient mDeathRecipient;
    private AbstractAction mAction;

    private boolean isAlive = true;

    static class Response {
        int responseCode = Definition.ACTION_RESPONSE_SUCCESS;
        int statusCode = Definition.RESPONSE_INVALID_STATUS;
        String responseString;
        String extraData;

        void reset() {
            responseCode = Definition.ACTION_RESPONSE_SUCCESS;
            statusCode = Definition.RESPONSE_INVALID_STATUS;
            responseString = null;
            extraData = null;
        }

        @Override
        public String toString() {
            return "Response{" +
                    "responseCode=" + responseCode +
                    ", statusCode=" + statusCode +
                    ", responseString='" + responseString +
                    ", extraData='"+ '\'' +
                    '}';
        }
    }

    ActionCallbackProxy(ExecutorService responseHandler, IActionListener callback) {
        try {
            if (callback != null) {
                mDeathRecipient = new IBinder.DeathRecipient() {
                    @Override
                    public void binderDied() {
                        //mCallback = null;
                        isAlive = false;
                        Log.d(TAG, "Action callback is died ###");
                        if (mAction != null) {
                            mAction.stop(true);
                        }
                        //todo restart ModuleApp
                    }
                };
                callback.asBinder().linkToDeath(mDeathRecipient, 0);
            } else {
                Log.d(TAG, "Callback is null");
            }

            mCallbackExecutor = responseHandler;
            mCallback = callback;
            if (Definition.DEBUG_ALL_DISPATHER) Log.v(Definition.DISPATCHER_TAG,
                    TAG + " callback:" + (callback == null ? "null" : callback.hashCode()));
        } catch (RemoteException e) {
            e.printStackTrace();
            isAlive = false;
        }
    }

    public void bind(AbstractAction action) {
        this.mAction = action;
    }

    public boolean isAlive() {
        return isAlive;
    }

    void response(final Response response) {

        RLog.v(TAG, "response: " + response);

        if (response != null && mCallback != null && isAlive) {
            if (Definition.DEBUG_ALL_DISPATHER) Log.v(Definition.DISPATCHER_TAG,
                    TAG + " response:" + mCallback.hashCode());

            if (mCallbackExecutor.isShutdown()) {
                Log.d(TAG, "CallbackExecutor is shutdown");
            }
            mCallbackExecutor.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        if (response.statusCode != Definition.RESPONSE_INVALID_STATUS) {
                            mCallback.onStatusUpdateWithExtraData(response.statusCode, response.responseString, response.extraData);
                            mCallback.onStatusUpdate(response.statusCode, response.responseString);
                        } else if (response.responseCode < Definition.ACTION_RESPONSE_SUCCESS) {
                            Log.d(TAG, "Action callback Error : "
                                    + response.responseCode + "  " + response.responseString);
                            mCallback.onErrorWithExtraData(response.responseCode, response.responseString, response.extraData);
                            mCallback.onError(response.responseCode, response.responseString);
                            mCallback.asBinder().unlinkToDeath(mDeathRecipient, 0);
                        } else {
                            Log.d(TAG, "Action callback result : "
                                    + response.responseCode + "  " + response.responseString);
                            mCallback.onResultWithExtraData(response.responseCode, response.responseString, response.extraData);
                            mCallback.onResult(response.responseCode, response.responseString);
                            mCallback.asBinder().unlinkToDeath(mDeathRecipient, 0);
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "response error: " + e.getMessage());
                        e.printStackTrace();
                        isAlive = false;
                    }
                }
            });
        } else {
            Log.i(TAG, "Response failed : " + response + "  callback : " + mCallback + " isAlive : " + isAlive);
        }
    }


}
