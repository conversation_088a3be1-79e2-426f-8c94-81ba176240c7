package com.ainirobot.coreservice.client.actionbean;

import com.ainirobot.coreservice.client.SettingsUtil;

public class GoPositionByTypeBean extends BaseBean {
    private int typeId;
    private int priority;
    private long time;
    private double coordinateDeviation;
    private boolean isAdjustAngle;
    private double linear_speed = SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED;
    private double angular_speed = SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED;
    /**
     * 最大避障距离，传0表示默认值，navi中默认值为0.75
     */
    private double mObsDistance = 0.0D;
    private int wheelOverCurrentRetryCount;
    private long mMultipleWaitTime;
    private int taskPriority;
    private double linearAcceleration;
    private double angularAcceleration;
    private int startModeLevel;
    private int brakeModeLevel;
    private int mRunStopCmdParam = 0; // 默认值0
    private double destinationRange = 0.0D;

    public int getTypeId() {
        return typeId;
    }

    public void setTypeId(int typeId) {
        this.typeId = typeId;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }
    public double getCoordinateDeviation() {
        return coordinateDeviation;
    }

    public void setCoordinateDeviation(double coordinateDeviation) {
        this.coordinateDeviation = coordinateDeviation;
    }

    public boolean getIsAdjustAngle() {
        return isAdjustAngle;
    }

    public void setIsAdjustAngle(boolean isAdjustAngle) {
        this.isAdjustAngle = isAdjustAngle;
    }

    public double getLinearSpeed(){
        return linear_speed;
    }

    public void setLinearSpeed(double linearSpeed){
        this.linear_speed = linearSpeed;
    }

    public double getAngularSpeed(){
        return angular_speed;
    }

    public void setAngularSpeed(double angularSpeed){
        this.angular_speed = angularSpeed;
    }

    public double getObsDistance() {
        return mObsDistance;
    }

    public void setObsDistance(double obsDistance) {
        mObsDistance = obsDistance;
    }

    public int getWheelOverCurrentRetryCount() {
        return wheelOverCurrentRetryCount;
    }

    public void setWheelOverCurrentRetryCount(int wheelOverCurrentRetryCount) {
        this.wheelOverCurrentRetryCount = wheelOverCurrentRetryCount;
    }

    public long getMultipleWaitTime() {
        return mMultipleWaitTime;
    }

    public void setMultipleWaitTime(long multipleWaitTime) {
        mMultipleWaitTime = multipleWaitTime;
    }

    public int getTaskPriority() {
        return taskPriority;
    }

    public void setTaskPriority(int taskPriority) {
        this.taskPriority = taskPriority;
    }

    public boolean isAdjustAngle() {
        return isAdjustAngle;
    }

    public void setAdjustAngle(boolean adjustAngle) {
        isAdjustAngle = adjustAngle;
    }

    public double getLinear_speed() {
        return linear_speed;
    }

    public void setLinear_speed(double linear_speed) {
        this.linear_speed = linear_speed;
    }

    public double getAngular_speed() {
        return angular_speed;
    }

    public void setAngular_speed(double angular_speed) {
        this.angular_speed = angular_speed;
    }

    public double getLinearAcceleration() {
        return linearAcceleration;
    }

    public void setLinearAcceleration(double linearAcceleration) {
        this.linearAcceleration = linearAcceleration;
    }

    public double getAngularAcceleration() {
        return angularAcceleration;
    }

    public void setAngularAcceleration(double angularAcceleration) {
        this.angularAcceleration = angularAcceleration;
    }

    public int getStartModeLevel() {
        return startModeLevel;
    }

    public void setStartModeLevel(int startModeLevel) {
        this.startModeLevel = startModeLevel;
    }

    public int getBrakeModeLevel() {
        return brakeModeLevel;
    }

    public void setBrakeModeLevel(int brakeModeLevel) {
        this.brakeModeLevel = brakeModeLevel;
    }

    public int getRunStopCmdParam() {
        return mRunStopCmdParam;
    }

    public void setRunStopCmdParam(int mRunStopCmdParam) {
        this.mRunStopCmdParam = mRunStopCmdParam;
    }

    public double getDestinationRange() {
        return destinationRange;
    }

    public void setDestinationRange(double destinationRange) {
        this.destinationRange = destinationRange;
    }

    @Override
    public String toString() {
        return "GoPositionByTypeBean{" +
                "typeId=" + typeId +
                ", priority=" + priority +
                ", time=" + time +
                ", coordinateDeviation=" + coordinateDeviation +
                ", isAdjustAngle=" + isAdjustAngle +
                ", linear_speed=" + linear_speed +
                ", angular_speed=" + angular_speed +
                ", mObsDistance=" + mObsDistance +
                ", wheelOverCurrentRetryCount=" + wheelOverCurrentRetryCount +
                ", mMultipleWaitTime=" + mMultipleWaitTime +
                ", taskPriority=" + taskPriority +
                ", linearAcceleration=" + linearAcceleration +
                ", angularAcceleration=" + angularAcceleration +
                ", startModeLevel=" + startModeLevel +
                ", brakeModeLevel=" + brakeModeLevel +
                ", mRunStopCmdParam=" + mRunStopCmdParam +
                ", destinationRange=" + destinationRange +
                '}';
    }
}
