package com.ainirobot.coreservice.action.advnavi.gatecmd;

import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.utils.DelayTask;

import java.util.concurrent.TimeUnit;

/**
 * Data: 2024/2/22
 * Author: dengting
 * <p>
 * 开闸机门 先控制闸机，后开门
 * * 成功 开门
 * * 失败 尝试控制闸机 -> 开门
 * * 超时 重试
 */
public class CmdOpenGateDoor extends GateCommand {
    //10秒重试一次开门
    private final long OPEN_DOOR_DELAY = TimeUnit.SECONDS.toMillis(3);

    private CmdCallGate cmdCallGate;

    CmdOpenGateDoor(String name, CmdType intentType, GateCommandInterface commandInterface, GateCommandListener listener) {
        super(name, intentType, commandInterface, listener);
        initCmdType(CmdType.OPEN_DOOR);
    }


    @Override
    public void start() {
        super.start();
        callGate();
    }

    @Override
    public void cmdResponse(String command, String result, String extraData) {
        super.cmdResponse(command, result, extraData);
        Log.d(TAG, "CmdOpenGateDoor cmdResponse" + result + extraData);
        if (null != cmdCallGate) {
            cmdCallGate.cmdResponse(command, result, extraData);
        }
    }

    @Override
    public void onSuccess() {
        super.onSuccess();
        Log.d(TAG, "onSuccess cmdopengatedoor");
        onResult(Definition.RESULT_OK, "open suc");
        delayOpenDoor();
    }

    @Override
    public void onFail() {
        super.onFail();
        DelayTask.cancel(OPEN_DOOR_DELAY);  //onFail
        callGate();
        Log.d(TAG, "open door fail, try call gate");
    }

    @Override
    protected void onRetry() {
        openGateDoor();
    }

    @Override
    public void stop() {
        super.stop();
        DelayTask.cancel(OPEN_DOOR_DELAY);  //onStop
        if (null != cmdCallGate && cmdCallGate.isAlive) {
            cmdCallGate.stop();
        }
    }

    private final GateCommandListener callCmdListener = new GateCommandListener() {
        @Override
        public void onStatusUpdate(int status, String data, String extraData) {
            onCmdStatusUpdate(status, data, extraData);
        }

        @Override
        public void onFinish(int status, String data) {
            if (status == Definition.RESULT_OK) {
                Log.d(TAG, "call gate suc, open door");
                openGateDoor();
                return;
            }
            onResult(status, data);
        }
    };

    private void delayOpenDoor() {
        DelayTask.cancel(OPEN_DOOR_DELAY);
        DelayTask.submit(OPEN_DOOR_DELAY, new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "delay open door");
                start();
            }
        }, OPEN_DOOR_DELAY);
    }

    private void callGate() {
        if (null == cmdCallGate) {
            cmdCallGate = new CmdCallGate(TAG, intentType, commandInterface, callCmdListener);
        }
        cmdCallGate.start();
    }
}
