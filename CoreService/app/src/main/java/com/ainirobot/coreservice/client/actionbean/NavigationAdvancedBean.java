package com.ainirobot.coreservice.client.actionbean;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SettingsUtil;

import org.json.JSONObject;

import java.util.List;

/**
 * Data: 2022/8/10 11:38
 * Author: wanglijing
 * Description: NavigationAdvancedBean
 */
public class NavigationAdvancedBean extends NavigationBean implements Cloneable {

    // ----  definition  ----
    /**
     * 目标点默认偏移量0.5
     */
    private static final double DEFAULT_DESTINATION_COORDINATE_DEVIATION = 0.5;
    /**
     * 导航去目标房间默认无位移超时时间10min
     */
    private static final long DEFAULT_GO_DESTINATION_MOVING_TIMEOUT_TIME = 10 * 60 * 1000;
    /**
     * 多机避障等待默认超时时间，默认300s
     */
    private static final long DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT = 300 * 1000;


    /**
     * 导航方式：按目的地导航；按点导航
     */
    public static final int TYPE_DESTINATION = 1;
    public static final int TYPE_POSE = 2;


    /**
     * 轮子过流后尝试恢复次数
     */
    private static final int WHEEL_OVER_CURRENT_RETRY_DEFAULT_COUNT = 0;

    private static final double DEFAULT_COORDINATE_DEVIATION = 0.5;

    private static final long DEFAULT_AVOID_INTERVAL_TIME = 5 * 1000;

    private static final int DEFAULT_MAX_AVOID_COUNT = 5;

    private static final long DEFAULT_MOVING_TIMEOUT_TIME = 20 * 1000;

    /**
     * 获取距离默认频率 1s/次
     */
    private static final long GET_DISTANCE_INTERVAL_TIME = 1000;

    /**
     * 获取距离默认最小频率 200ms/次
     */
    private static final long GET_DISTANCE_MIN_INTERVAL_TIME = 200;

    /**
     * 导航任务默认优先级 值越大优先级越高，多机场景下使用
     */
    private static final int DEFAULT_NAVIGAITON_TASK_PRIORITY = 0;

    /**
     * 导航如果无法到达目的地中心点，默认到达目的地的范围
     */
    public static final double NAVIGATION_DEFAULT_DESTINATION_RANGE = 0.5D;

    // -----  param  -----

    /**
     * 目的地楼层
     * targetFloor != 0 时，需要乘梯导航
     */
    private int targetFloor = 0;

    /**
     * 导航调度策略
     */
    private Definition.AdvNaviStrategy advNaviStrategy = Definition.AdvNaviStrategy.DEFAULT;
    /**
     * 仅在导航时补位: false 机器推动离开位置也补位（默认）, true 机器导航离开才补位
     */
    private boolean fillingStrategy = false;

    /**
     * 导航组件需要透传的参数信息
     */
    private String navigationParams;

    /**
     * 备用目的地列表
     */
    private List<String> standbyDesList;

    /**
     * 长时间未移动超时时间
     */
    private long movieTimeout = DEFAULT_MOVING_TIMEOUT_TIME;

    private long avoidIntervalTime = DEFAULT_AVOID_INTERVAL_TIME;

    private int maxAvoidCount = DEFAULT_MAX_AVOID_COUNT;

    private long distanceIntervalTime = GET_DISTANCE_INTERVAL_TIME;

    /**
     * 多机判断 是否在某点的范围
     * *与导航使用的到达范围不同
     */
    private double inDestinationRange = DEFAULT_DESTINATION_COORDINATE_DEVIATION;

    /**
     * 是否需要避障通知
     */
    private boolean isNeedAvoidNotifyImmediately = true;

    /**
     * 闸机第一个口
     */
    private Pose gateFirstPose;
    /**
     * 闸机第二个口
     */
    private Pose gateSecondPose;

    /**
     * 目的地点位
     */
    private Pose naviPose;

    /**
     * 导航类型
     */
    private int naviType = TYPE_DESTINATION;

    private String currentPlace;
    /**
     * 去最后一个点时，避障的次数
     */
    private int lastAvoidMaxCnt = 0;
    /**
     * 闸机点位组
     */
    private List<GatePairPose> gatePairPoses;

    public NavigationAdvancedBean() {
        setCoordinateDeviation(DEFAULT_DESTINATION_COORDINATE_DEVIATION);
        setMovieTimeout(DEFAULT_GO_DESTINATION_MOVING_TIMEOUT_TIME);
        setMultipleWaitTime(DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT);
    }

    /**
     * 乘梯导航时 目的地楼层
     */
    public int getTargetFloor() {
        return targetFloor;
    }

    /**
     * 乘梯导航时 目的地楼层
     */
    public void setTargetFloor(int targetFloor) {
        this.targetFloor = targetFloor;
    }

    public Definition.AdvNaviStrategy getNaviStrategy() {
        return advNaviStrategy;
    }

    public void setNaviStrategy(Definition.AdvNaviStrategy advNaviStrategy) {
        this.advNaviStrategy = advNaviStrategy;
    }

    /**
     * 仅在导航时补位: false 机器推动离开位置也补位（默认）, true 机器导航离开才补位, 目前仅对火车生效
     */
    public boolean isFillingStrategy() {
        return fillingStrategy;
    }

    public void setFillingStrategy(boolean fillingStrategy) {
        this.fillingStrategy = fillingStrategy;
    }

    public int getLastAvoidMaxCnt() {
        return lastAvoidMaxCnt;
    }

    public void setLastAvoidMaxCnt(int lastAvoidMaxCnt) {
        this.lastAvoidMaxCnt = lastAvoidMaxCnt;
    }

    public Pose getGateFirstPose() {
        return gateFirstPose;
    }

    public void setGateFirstPose(Pose gateFirstPose) {
        this.gateFirstPose = gateFirstPose;
    }

    public Pose getGateSecondPose() {
        return gateSecondPose;
    }

    public List<GatePairPose> getGatePairPoses() {
        return gatePairPoses;
    }

    public void setGatePairPoses(List<GatePairPose> gatePairPoses) {
        this.gatePairPoses = gatePairPoses;
    }

    public void setGateSecondPose(Pose gateSecondPose) {
        this.gateSecondPose = gateSecondPose;
    }

    public Pose getNaviPose() {
        return naviPose;
    }

    /**
     * 设置导航目的地
     * ** pose需要目的地名称 **
     *
     * @param naviPose  目的地点对象
     */
    public void setNaviPose(Pose naviPose) {
        this.naviPose = naviPose;
        if (null != naviPose && !TextUtils.isEmpty(naviPose.getName())) {
            setDestination(naviPose.getName());
        }
    }

    public String getNavigationParams() {
        return navigationParams;
    }

    /**
     * 初始化导航参数
     */
    public void initNavigationParams() {
        if (null != navigationParams) {
            try {
                JSONObject params = new JSONObject(navigationParams);
                Log.d("NaviAdvancedAction", "setNavigationParams params " + params);

                //最后一次导航时，避障最大次数
                setLastAvoidMaxCnt(params.optInt(Navigation.PARAM_LAST_NAVIGATION_AVOID_MAX_COUNT, 0));

                //naviType
                setNaviType(params.optInt(Navigation.PARAM_NAVIGATION_TYPE,
                        TYPE_DESTINATION));

                //填充策略，是否被推动了也补位
                setFillingStrategy(params.optBoolean(Navigation.PARAM_FILLING_WHEN_DELIVERY, false));

                //当前点位名称
                //上层可能传这种参数：{\"code\":30010001,\"message\":\"\"}
                setCurrentPlace(params.optString(Navigation.PARAM_CURRENT_PLACE, ""));

                //坐标偏差
                setCoordinateDeviation(params.optDouble(Navigation.PARAM_COORDINATE_DEVIATION,
                        DEFAULT_COORDINATE_DEVIATION));

                //长时间未移动退出时间
                long movieTime = params.optLong(Navigation.PARAM_MOVING_TIMEOUT_TIME,
                        DEFAULT_MOVING_TIMEOUT_TIME);
                setMovieTimeout(movieTime);

                //避障最大次数
                setMaxAvoidCount(params.optInt(Navigation.PARAM_MAX_AVOID_COUNT,
                        DEFAULT_MAX_AVOID_COUNT));

                //避障间隔时间
                setAvoidIntervalTime(params.optLong(Navigation.PARAM_AVOID_INTERVAL_TIME,
                        DEFAULT_AVOID_INTERVAL_TIME));

                //获得距目的地距离时间间隔
                setDistanceIntervalTime(params.optLong(Navigation.PARAM_GET_DISTANCE_INTERVAL_TIME,
                        GET_DISTANCE_INTERVAL_TIME));

                //是否使用结束时机器人朝向（不归正朝向）
                setAdjustAngle(params.optBoolean(Navigation.PARAM_IS_ADJUST_ANGLE,
                        false));

                //到达目的地的范围，任务导航任务成功
                setDestinationRange(params.optDouble(Navigation.PARAM_DESTINATION_RANGE,
                        NAVIGATION_DEFAULT_DESTINATION_RANGE));

                //多机判断某点的膨胀范围
                setInDestinationRange(params.optDouble(Navigation.PARAM_IN_DESTINATION_RANGE,
                        DEFAULT_DESTINATION_COORDINATE_DEVIATION));

                //多机避障等待时间
                long waitTimeout = params.optLong(Navigation.PARAM_MULTI_ROBOT_WAITING_TIMEOUT_TIME,
                        DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT);
                setMultipleWaitTime((waitTimeout <= 0) ? DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT : waitTimeout);

                //导航任务等级
                setPriority(params.optInt(Navigation.PARAM_PRIORITY,
                        DEFAULT_NAVIGAITON_TASK_PRIORITY));

                //是否需要避障立刻通知
                setNeedAvoidNotifyImmediately(params.optBoolean(Navigation.PARAM_IS_NEED_AVOID_NOTIFY_IMMEDIATELY,
                        true));

                //线性加速度
                setLinearAcceleration(params.optDouble(Navigation.PARAM_LINEAR_ACCELERATION,
                        0));

                //角度加速度
                setAngularAcceleration(params.optDouble(Navigation.PARAM_ANGULAR_ACCELERATION,
                        0));

                //启动档位
                setStartModeLevel(params.optInt(Navigation.PARAM_START_MODE_LEVEL,
                        0));

                //刹车档位
                setBrakeModeLevel(params.optInt(Navigation.PARAM_BRAKE_MODE_LEVEL,
                        0));

                //设置车轮过电流重试计数
                int wheelOverCurrentRetryCount = params.optInt(Navigation.PARAM_WHEEL_OVER_CURRENT_RETRY_COUNT,
                        WHEEL_OVER_CURRENT_RETRY_DEFAULT_COUNT);
                if (wheelOverCurrentRetryCount < WHEEL_OVER_CURRENT_RETRY_DEFAULT_COUNT) {
                    wheelOverCurrentRetryCount = WHEEL_OVER_CURRENT_RETRY_DEFAULT_COUNT;
                }
                setWheelOverCurrentRetryCount(wheelOverCurrentRetryCount);

                //设置线速度
                float lineSpeed = (float) params.optDouble(Navigation.PARAM_LINEAR_SPEED
                        , SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED);
                lineSpeed = rectifyLinearSpeed(lineSpeed);
                setLinearSpeed(lineSpeed);

                //设置角速度
                float angularSpeed = (float) params.optDouble(Navigation.PARAM_ANGULAR_SPEED
                        , SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED);
                angularSpeed = rectifyAngularSpeed(angularSpeed);
                setAngularSpeed(angularSpeed);


                //导航到某点
                if (naviType == TYPE_POSE) {
                    Pose pose = new Pose();
                    pose.setX(Float.parseFloat(params.optString(Navigation.PARAM_POSE_X, "0f")));
                    pose.setY(Float.parseFloat(params.optString(Navigation.PARAM_POSE_Y, "0f")));
                    pose.setTheta(Float.parseFloat(params.optString(Navigation.PARAM_POSE_THETA, "0f")));
                    setNaviPose(pose);
                }

            } catch (Exception e) {
                e.printStackTrace();
                this.navigationParams = null;
                Log.e("NaviAdvancedAction", "setNavigationParams error: " + e.getMessage());
            }
        }
    }

    /**
     * set后默认初始化一遍参数
     */
    public void setNavigationParams(String navigationParams) {
        this.navigationParams = navigationParams;
        initNavigationParams();
    }

    public List<String> getStandbyDesList() {
        return standbyDesList;
    }

    public void setStandbyDesList(List<String> mStandbyDesList) {
        this.standbyDesList = mStandbyDesList;
    }

    public long getMovieTimeout() {
        return movieTimeout;
    }

    public void setMovieTimeout(long movieTimeout) {
        this.movieTimeout = (movieTimeout <= 0) ? Long.MAX_VALUE : movieTimeout;
    }


    public long getAvoidIntervalTime() {
        return avoidIntervalTime;
    }

    public void setAvoidIntervalTime(long avoidIntervalTime) {
        this.avoidIntervalTime = avoidIntervalTime;
    }

    public int getMaxAvoidCount() {
        return maxAvoidCount;
    }

    public void setMaxAvoidCount(int maxAvoidCount) {
        this.maxAvoidCount = maxAvoidCount;
    }

    public long getDistanceIntervalTime() {
        return distanceIntervalTime;
    }

    /**
     * 设置获得距离时间，>=200ms
     */
    public void setDistanceIntervalTime(long distanceIntervalTime) {
        if (distanceIntervalTime < GET_DISTANCE_MIN_INTERVAL_TIME) {
            distanceIntervalTime = GET_DISTANCE_INTERVAL_TIME;
        }
        this.distanceIntervalTime = distanceIntervalTime;
    }

    public boolean isNeedAvoidNotifyImmediately() {
        return isNeedAvoidNotifyImmediately;
    }

    public void setNeedAvoidNotifyImmediately(boolean isNeedAvoidNotifyImmediately) {
        this.isNeedAvoidNotifyImmediately = isNeedAvoidNotifyImmediately;
    }

    public int getNaviType() {
        return naviType;
    }

    public void setNaviType(int naviType) {
        this.naviType = naviType;
    }

    public String getCurrentPlace() {
        return currentPlace;
    }

    public void setCurrentPlace(String currentPlace) {
        this.currentPlace = currentPlace;
    }

    public double getInDestinationRange() {
        return inDestinationRange;
    }

    public void setInDestinationRange(double inDestinationRange) {
        this.inDestinationRange = inDestinationRange;
    }

    /**
     * 底盘限速区间值
     */
    public static final float LINEAR_SPEED_MIN = 0.1f;
    public static final float LINEAR_SPEED_MAX = 1.2f;
    public static final float ANGULAR_SPEED_MIN = 0.2f;
    public static final float ANGULAR_SPEED_MAX = 1.8f;

    /**
     * 纠偏线速度
     */
    private float rectifyLinearSpeed(float speed) {
        if (speed < LINEAR_SPEED_MIN) {
            speed = LINEAR_SPEED_MIN;
        } else if (speed > LINEAR_SPEED_MAX) {
            speed = LINEAR_SPEED_MAX;
        }
        return speed;
    }

    /**
     * 纠偏角速度
     */
    private float rectifyAngularSpeed(float speed) {
        if (speed < ANGULAR_SPEED_MIN) {
            speed = ANGULAR_SPEED_MIN;
        } else if (speed > ANGULAR_SPEED_MAX) {
            speed = ANGULAR_SPEED_MAX;
        }
        return speed;
    }

    public static class Navigation {
        public static final String PARAM_NAVIGATION_TYPE = "navigation_type";
        public static final String PARAM_COORDINATE_DEVIATION = "coordinate_deviation";
        public static final String PARAM_MOVING_TIMEOUT_TIME = "moving_timeout_time";
        public static final String PARAM_AVOID_INTERVAL_TIME = "avoid_interval_time";
        public static final String PARAM_GET_DISTANCE_INTERVAL_TIME = "get_distance_interval_time";
        public static final String PARAM_MAX_AVOID_COUNT = "max_avoid_count";
        public static final String PARAM_LINEAR_SPEED = "param_linear_speed";
        public static final String PARAM_ANGULAR_SPEED = "param_angular_speed";
        public static final String PARAM_IS_ADJUST_ANGLE = "param_is_adjust_angle";
        public static final String PARAM_MULTI_ROBOT_WAITING_TIMEOUT_TIME = "param_multi_waiting_timeout_time";
        public static final String PARAM_IS_NEED_AVOID_NOTIFY_IMMEDIATELY = "param_is_need_avoid_notify_immediately";
        public static final String PARAM_DESTINATION_RANGE = "param_destination_range";
        public static final String PARAM_WHEEL_OVER_CURRENT_RETRY_COUNT = "param_wheel_over_current_retry_count";
        public static final String PARAM_PRIORITY = "param_priority";
        public static final String PARAM_POSE_X = "pose_x";
        public static final String PARAM_POSE_Y = "pose_y";
        public static final String PARAM_POSE_THETA = "pose_theta";
        public static final String PARAM_LINEAR_ACCELERATION = "linear_acceleration";
        public static final String PARAM_ANGULAR_ACCELERATION = "angular_acceleration";
        public static final String PARAM_START_MODE_LEVEL = "start_mode_level";
        public static final String PARAM_BRAKE_MODE_LEVEL = "brake_mode_level";
        public static final String PARAM_IN_DESTINATION_RANGE = "in_destination_range";
        public static final String PARAM_LAST_NAVIGATION_AVOID_MAX_COUNT = "last_navigation_avoid_max_count";
        public static final String PARAM_FILLING_WHEN_DELIVERY = "fillingWhenDelivery";
        public static final String PARAM_CURRENT_PLACE = "current_place";


    }

    @Override
    public NavigationAdvancedBean clone() {
        NavigationAdvancedBean bean = null;
        try {
            bean = (NavigationAdvancedBean) super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return bean;
    }

    @Override
    public String toString() {
        return "NavigationAdvancedBean{" +
                "targetFloor=" + targetFloor +
                ", advNaviStrategy=" + advNaviStrategy +
                ", fillingStrategy=" + fillingStrategy +
                ", navigationParams='" + navigationParams + '\'' +
                ", standbyDesList=" + standbyDesList +
                ", movieTimeout=" + movieTimeout +
                ", avoidIntervalTime=" + avoidIntervalTime +
                ", maxAvoidCount=" + maxAvoidCount +
                ", distanceIntervalTime=" + distanceIntervalTime +
                ", inDestinationRange=" + inDestinationRange +
                ", isNeedAvoidNotifyImmediately=" + isNeedAvoidNotifyImmediately +
                ", gateFirstPose=" + gateFirstPose +
                ", gateSecondPose=" + gateSecondPose +
                ", naviPose=" + naviPose +
                ", naviType=" + naviType +
                ", currentPlace='" + currentPlace + '\'' +
                ", lastAvoidMaxCnt=" + lastAvoidMaxCnt +
                ", gatePairPoses=" + gatePairPoses +
                '}';
    }
}
