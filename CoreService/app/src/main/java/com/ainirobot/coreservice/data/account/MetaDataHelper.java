package com.ainirobot.coreservice.data.account;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.data.account.bean.Tables;
import com.ainirobot.coreservice.data.provider.BaseDataHelper;

import static com.ainirobot.coreservice.data.account.bean.Tables.META;
import static com.ainirobot.coreservice.data.account.bean.Tables.AUTHORITY_URI;

class MetaDataHelper extends BaseDataHelper {

    private static final String TAG = MetaDataHelper.class.getSimpleName();
    private static final Uri CONTENT_URI = Uri.withAppendedPath(AUTHORITY_URI, META);

    MetaDataHelper(Context context) {
        super(context);
    }

    @Override
    public Uri getContentUri() {
        return CONTENT_URI;
    }

    String getMetaValue(String module) {
        Log.d(TAG, "getMetaValue module: " + module);
        Cursor cursor = query(Tables.MetaColumns.MODULE + "=?",
                new String[]{
                        module
                });
        if (cursor != null && cursor.moveToNext()) {
            String value = cursor.getString(cursor
                    .getColumnIndex(Tables.MetaColumns.VALUE));
            cursor.close();
            return value;
        }
        return null;
    }

    void updateMetaValue(String module, String value) {
        Log.d(TAG, "updateMetaValue module: " + module
                + ", value: " + value);
        if (!TextUtils.isEmpty(getMetaValue(module))) {
            ContentValues contentValues = new ContentValues();
            contentValues.put(Tables.MetaColumns.VALUE, value);
            update(contentValues, Tables.MetaColumns.MODULE + "=?",
                    new String[]{
                            module
                    });
        } else {
            ContentValues contentValues = new ContentValues();
            contentValues.put(Tables.MetaColumns.MODULE, module);
            contentValues.put(Tables.MetaColumns.VALUE, value);
            insert(contentValues);
        }
    }
}
