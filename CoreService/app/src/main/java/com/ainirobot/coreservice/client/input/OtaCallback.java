/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.client.input;

import android.os.Bundle;
import android.os.RemoteException;

import com.ainirobot.coreservice.IOtaCallback;

public class OtaCallback extends IOtaCallback.Stub {

    @Override
    public void onCmdResponse(int cmdId, String cmdType, String cmdResponse)
            throws RemoteException {

    }

    @Override
    public boolean onOtaUpgradeStart(boolean needDownload) throws RemoteException {
        return false;
    }

    @Override
    public boolean onOtaRollbackStart() throws RemoteException {
        return false;
    }

    @Override
    public String onOtaGetDescription() throws RemoteException {
        return null;
    }

    @Override
    public boolean onOtaCancelDownload() throws RemoteException {
        return false;
    }

    @Override
    public void installPatch(Bundle bundle) throws RemoteException {
    }

    @Override
    public void onOtaInterrupted(String reason) throws RemoteException {
    }
}
