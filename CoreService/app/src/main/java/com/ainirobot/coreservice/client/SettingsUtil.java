/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.client;

import android.content.Context;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Handler;
import android.provider.Settings;
import android.support.annotation.StringDef;
import android.util.Log;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.Arrays;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

/**
 * <p>
 * we use system setting Global table to store key/value. if you want write key/value, you should
 * use android:sharedUserId="android.uid.system" and <uses-permission android:name="android.permission
 * .WRITE_SETTINGS"/>, with system signature level. Every user can read and register value change
 * listener. default value init from framework setting provider.
 * </p>
 *
 * <AUTHOR> Liu
 * @date 2018-11-06
 */

public class SettingsUtil {

    private static final String TAG = SettingsUtil.class.getSimpleName();

    private static final String AUTHORITY = "settings";
    private static final String TABLE = "global";
    private static final Uri CONTENT_URI = Uri.parse("content://" + AUTHORITY + "/" + TABLE);

    private static final String CORE_SERVICE = "com.ainirobot.coreservice";
    private static final String HOME = "com.ainirobot.home";
    private static final String FIRST_CONFIG = "com.ainirobot.firstconfig";
    private static final String MODULE_APP = "com.ainirobot.moduleapp";
    private static final String MAP_TOOL = "com.ainirobot.maptool";
    private static final List<String> ACCEPT_PACKAGE_LIST = Collections.unmodifiableList(
            Arrays.asList(CORE_SERVICE, HOME, FIRST_CONFIG, MODULE_APP, MAP_TOOL));
    private static MyContentObserver myContentObserver;


    @Retention(RetentionPolicy.SOURCE)
    @StringDef({
            ROBOT_SETTING_AUTO_CHARGE_INT,
            ROBOT_SETTING_OTA_UPDATE_INT,
            ROBOT_SETTING_WAKE_ANSWER_INT,
            ROBOT_SETTING_SOUND_ANGEL_CENTER_FLOAT,
            ROBOT_SETTING_SOUND_ANGEL_RANGE_FLOAT,
            ROBOT_SETTING_SPOKE_MAN_INT,
            ROBOT_SETTING_SPEECH_SPEED_INT,
            ROBOT_SETTING_SPEECH_VOLUME_INT,
            ROBOT_SETTING_IS_AUTO_LANG,
            ROBOT_SETTING_LANGS_RANGE,
            ROBOT_SETTING_ASR_LANG,
            ROBOT_SETTING_BAD_NETWORK_INT,
            ROBOT_SETTING_MULTI_MODEL_INT,
            ROBOT_SETTING_VAD_END_INT,
            ROBOT_SHIPPING_MODE,
            ROBOT_SMALL_ACTION,
            ROBOT_OBSTACLES_AVOID,
            ROBOT_CHECK_CLIFF,
            ROBOT_SOUND_TYPE,
            ROBOT_SETTING_ORION_FACTORY_MMI,
            ROBOT_SETTING_RUNNING_TEST,
            ROBOT_SETTING_CRUISE_LINEAR_SPEED,
            ROBOT_SETTING_NAV_LINEAR_SPEED,
            ROBOT_SETTING_LEAD_LINEAR_SPEED,
            ROBOT_SETTING_GUIDE_LINEAR_SPEED,
            ROBOT_SETTING_GREET_LINEAR_SPEED,
            ROBOT_SETTING_CRUISE_ANGULAR_SPEED,
            ROBOT_SETTING_NAV_ANGULAR_SPEED,
            ROBOT_SETTING_LEAD_ANGULAR_SPEED,
            ROBOT_SETTING_GUIDE_ANGULAR_SPEED,
            ROBOT_SETTING_GREET_ANGULAR_SPEED,
            ROBOT_SETTING_CHARGING_ENVIRONMENT,
            ROBOT_SETTING_WAITING_TIME,
            ROBOT_SETTING_AUTO_BACK_RECEPTION,
            ROBOT_SETTING_SITU_SERVICE_STATUS,
            ROBOT_SETTING_CHARGING_CHAT,
            ROBOT_SETTING_SITU_SERVICE_STATUS,
            ROBOT_SETTING_CURRENT_CHARACTER,
            ROBOT_SETTING_OPK_INFO,
            ROBOT_SETTING_OPK_INSTALL_LIST,
            ROBOT_SETTING_SYSTEM_ENV,
            ROBOT_SETTING_CHASSIS_MULTI_ID,
            ROBOT_SETTING_INSPECTION_RESULT,
            ROBOT_SETTING_SHUTDOWN_SWITCH,
            ROBOT_SETTING_SHUTDOWN_TIMER,
            ROBOT_SETTING_POWERON_SWITCH,
            ROBOT_SETTING_POWERON_TIMER,
            ROBOT_SETTING_SHUTDOWN_TIMER_DEF,
            ROBOT_SETTING_INSPECT_FAIL_AUTO_REBOOT,
            ROBOT_SETTINGS_HCUSB_ENABLE,
            ROBOT_FOCUS_FOLLOW_ANGLE
    })
    @interface RobotSettingKey {
    }

    public static final String ROBOT_SETTING_POWERON_SWITCH = "robot_setting_poweron_switch";
    public static final String ROBOT_SETTING_POWERON_TIMER = "robot_setting_poweron_time";
    public static final String ROBOT_SETTING_SHUTDOWN_SWITCH = "robot_setting_shutdown_switch";
    public static final String ROBOT_SETTING_SHUTDOWN_TIMER = "robot_setting_shutdown_time";
    public static final String ROBOT_SETTING_SHUTDOWN_TIMER_DEF = "robot_setting_shutdown_time_def";
    public static final String ROBOT_SETTINGS_HCUSB_ENABLE = "robot_settings_hcusb_enable";
    public static final String ROBOT_FOCUS_FOLLOW_ANGLE = "robot_focus_follow_angle";

    /**
     * auto charge function, 0 denote disable; 1 denote enable, default value is 1.
     */
    public static final String ROBOT_SETTING_AUTO_CHARGE_INT = "robot_setting_auto_charge";

    /**
     * ota update function, 0 denote disable; 1 denote enable, default value is 1.
     */
    public static final String ROBOT_SETTING_OTA_UPDATE_INT = "robot_setting_ota_update";

    public static final String ROBOT_SETTING_CHARGING_CHAT = "robot_usable_when_charging";

    /**
     * SpeechAsrService robot is or not answer when user speak XiaoBao XiaoBao, 0 denote disable;
     * 1 denote enable, default value is 1.
     */
    public static final String ROBOT_SETTING_WAKE_ANSWER_INT = "robot_setting_wake_answer";

    /**
     * SpeechAsrService type for sound center angel and range angel, must be set together, angel
     * center is [0-360], default value is 0, angel range is (0-120], default value is 120.
     */
    public static final String ROBOT_SETTING_SOUND_ANGEL_CENTER_FLOAT = "robot_setting_sound_angel_center";
    public static final String ROBOT_SETTING_SOUND_ANGEL_RANGE_FLOAT = "robot_setting_sound_angel_range";
    public static final String ROBOT_SETTING_HOME_LAUNCHER_COMPONENT = "robot_setting_home_launcher_component";

    /**
     * SpeechAsrService type for speech volume, value is from 0 to 30, default value is 30.
     */
    public static final String ROBOT_SETTING_SPEECH_VOLUME_INT = "robot_setting_speech_volume";
    /**
     * SpeechAsrService type for spoke man, value is 0 or 20, default value is 0.
     */
    public static final String ROBOT_SETTING_SPOKE_MAN_INT = "robot_setting_spoke_man";
    /**
     * SpeechAsrService type for speech rate, value is from 0 to 5, default value is 5.
     */
    public static final String ROBOT_SETTING_SPEECH_RATE_INT = "robot_setting_speech_rate";

    /**
     * SpeechAsrService set speech speed, value range is 0-9, default value is 5.
     */
    public static final String ROBOT_SETTING_SPEECH_SPEED_INT = "robot_setting_speech_speed";
    /**
     * 是否支持多语言自动识别
     */
    public static final String ROBOT_SETTING_VOICE_SUPPORT_MULTILINGUAL = "robot_setting_voice_support_multilingual";
    /**
     * SpeechAsrService type for speech pit, value is from 0 to 9, default value is 5.
     */
    public static final String ROBOT_SETTING_SPEECH_PIT_INT = "robot_setting_speech_pit";
    /**
     * SpeechAsrService type for automatic or nonautomatic language, value is 0 or 1
     */
    public static final String ROBOT_SETTING_IS_AUTO_LANG = "robot_setting_is_auto_lang";
    /**
     * SpeechAsrService type for languages range in automatic , value like this '1,2'
     * {@link com.ainirobot.coreservice.client.speech.entity.LangParamsEnum}
     */
    public static final String ROBOT_SETTING_LANGS_RANGE = "robot_setting_langs_range";
    /**
     * SpeechAsrService type for language of asr in nonautomatic , value like this 1
     * {@link com.ainirobot.coreservice.client.speech.entity.LangParamsEnum}
     */
    public static final String ROBOT_SETTING_ASR_LANG = "robot_setting_asr_lang";
    /**
     * SpeechAsrService type for language of tts , value like this "zh_CN"
     * {@link com.ainirobot.coreservice.client.speech.entity.LangParamsEnum}
     */
    public static final String ROBOT_SETTING_TTS_LANG = "robot_setting_tts_lang";

    /**
     * SpeechAsrService type for bad network, 0 denote disable else 1 denote enable,
     * default value is 1.
     */
    public static final String ROBOT_SETTING_BAD_NETWORK_INT = "robot_setting_bad_network";

    /**
     * SpeechAsrService type for speech multi model, 0 denote disable; 1 denote enable,
     * default value is 0.
     */
    public static final String ROBOT_SETTING_MULTI_MODEL_INT = "robot_setting_multi_model";

    /**
     * SpeechAsrService type for speech vad end, value range is 0-3000, default value is 400.
     */
    public static final String ROBOT_SETTING_VAD_END_INT = "robot_setting_vad_end";

    public static final String ROBOT_SHIPPING_MODE = "robot_setting_shipping_mode";
    /**
     * if open small action, 0 denote disable else 1 denote enable,
     * default value is 1.
     */
    public static final String ROBOT_SMALL_ACTION = "robot_samll_action";
    /**
     * obstacles avoid switcher, 0 denote disable else 1 denote enable,
     * default 1.
     */
    public static final String ROBOT_OBSTACLES_AVOID = "robot_obstacles_avoid";
    /** 1 enable check cliff */
    public static final String ROBOT_CHECK_CLIFF = "robot_check_cliff";
    /**
     * 1 sound size    0 sound speed
     */
    public static final String ROBOT_SOUND_TYPE = "robot_sound_type";

    public static final String ROBOT_SETTING_ORION_FACTORY_MMI = "com.android.orionfactorymmi";

    public static final String ROBOT_SETTING_RUNNING_TEST = "com.android.runningtest";

    public static final String ROBOT_SETTING_CRUISE_LINEAR_SPEED = "robot_setting_cruise_linear_speed";

    public static final String ROBOT_SETTING_NAV_LINEAR_SPEED = "robot_setting_nav_linear_speed";

    public static final String ROBOT_SETTING_LEAD_LINEAR_SPEED = "robot_setting_lead_linear_speed";

    public static final String ROBOT_SETTING_GUIDE_LINEAR_SPEED = "robot_setting_guide_linear_speed";

    public static final String ROBOT_SETTING_GREET_LINEAR_SPEED = "robot_setting_greet_linear_speed";

    public static final String ROBOT_SETTING_CRUISE_ANGULAR_SPEED = "robot_setting_cruise_angular_speed";

    public static final String ROBOT_SETTING_NAV_ANGULAR_SPEED = "robot_setting_nav_angular_speed";

    public static final String ROBOT_SETTING_LEAD_ANGULAR_SPEED = "robot_setting_lead_angular_speed";

    public static final String ROBOT_SETTING_GUIDE_ANGULAR_SPEED = "robot_setting_guide_angular_speed";

    public static final String ROBOT_SETTING_GREET_ANGULAR_SPEED = "robot_setting_greet_angular_speed";

    public static final float ROBOT_SETTING_DEFAULT_LINEAR_SPEED = 0.7f;

    public static final float ROBOT_SETTING_DEFAULT_ANGULAR_SPEED = 1.2f;

    public static final float ROBOT_SETTING_DEFAULT_GREET_LINEAR_SPEED = 0.3f;

    public static final double ROBOT_SETTING_FOLLOW_DISTANCE_TO_PERSON = 1.0;

    public static final double ROBOT_SETTING_FOLLOW_DISTANCE_TO_PERSON_MIN = 0.6;

    public static final float ROBOT_SETTING_DEFAULT_FOLLOW_LINEAR_SPEED = 1.0f;

    public static final float ROBOT_SETTING_DEFAULT_FOLLOW_ANGULAR_SPEED = 1.2f;

    public static final String ROBOT_SETTING_CHARGING_ENVIRONMENT = "robot_settings_charging_environment";

    public static final String ROBOT_SETTING_WAITING_TIME = "robot_setting_waiting_time";

    public static final String ROBOT_SETTING_AUTO_BACK_RECEPTION = "robot_auto_back_reception";

    public static final String ROBOT_SETTING_SITU_SERVICE_STATUS = "robot_setting_situ_service_status";

    public static final String ROBOT_SETTING_CURRENT_CHARACTER = "robot_setting_current_character";

    public static final String ROBOT_SETTING_OPK_INFO = "robot_setting_opk_info";

    public static final String ROBOT_SETTING_OPK_INSTALL_LIST = "robot_setting_opk_install_list";

    public static final String ROBOT_SETTING_SYSTEM_ENV = "robot_setting_system_environment";

    /**
     * OTA正在升级模块类型:HOST,CAN,NAVI,HEAD
     */
    public static final String ROBOT_SETTINGS_OTA_INSTALLING_OS = "ota_installing_os";

    /**
     * robot current platform name
     */
    public static final String ROBOT_SETTING_PLATFORM_NAME = "robot_setting_platform_name";

    public static final String ROBOT_PREVENT_COLLISION = "robot_prevent_collision";

    public static final String ROBOT_SETTING_INSPECTION_RESULT = "robot_setting_inspection_result";

    /**
     * Chassis multi robot work id, KTV id > 1,000,000,000 ; Meissa id < 1,000,000,000
     * default value is 0.
     */
    public static final String ROBOT_SETTING_CHASSIS_MULTI_ID = "robot_setting_chassis_multi_id";

    /**
     * 首次配置或开机自检结束后如果失败，自动执行一次重启，重启仍失败的话展示失败页面。默认0可以执行重启，1表示已重启过。自检成功后要重置该状态
     */
    public static final String ROBOT_SETTING_INSPECT_FAIL_AUTO_REBOOT = "robot_setting_inspect_fail_auto_reboot";

    public static Uri getUriFor(@RobotSettingKey String name) {
        return getUriFor(CONTENT_URI, name);
    }

    private static Uri getUriFor(Uri contentUri, String name) {
        return Uri.withAppendedPath(contentUri, name);
    }

    /**
     * Convenience function for updating a single settings value as an
     * integer. This will either create a new entry in the table if the
     * given name does not exist, or modify the value of the existing row
     * with that name.  Note that internally setting values are always
     * stored as strings, so this function converts the given value to a
     * string before storing it.
     *
     * @param context The Context to access.
     * @param name    The name of the setting to modify.
     * @param value   The new value for the setting.
     * @return true if the value was set, false on database errors or permission deny
     */
    public static boolean putInt(Context context, @RobotSettingKey String name, int value) {
        boolean result = false;
        if (!checkPermission(context)) {
            Log.e(TAG, "Permission deny");
            return result;
        }

        try {
            result = Settings.Global.putInt(context.getContentResolver(), name, value);
        } catch (Exception e) {
            e.printStackTrace();
            result = false;
        }
        return result;
    }

    public static int getInt(Context context, @RobotSettingKey String name)
            throws Settings.SettingNotFoundException {
        return Settings.Global.getInt(context.getContentResolver(), name);
    }

    public static int getInt(Context context, @RobotSettingKey String name, int def) {
        return Settings.Global.getInt(context.getContentResolver(), name, def);
    }

    public static boolean putLong(Context context, @RobotSettingKey String name, long value) {
        boolean result = false;
        if (!checkPermission(context)) {
            Log.e(TAG, "Permission deny");
            return result;
        }

        try {
            result = Settings.Global.putLong(context.getContentResolver(), name, value);
        } catch (Exception e) {
            e.printStackTrace();
            result = false;
        }
        return result;
    }

    public static long getLong(Context context, @RobotSettingKey String name)
            throws Settings.SettingNotFoundException {
        return Settings.Global.getLong(context.getContentResolver(), name);
    }

    public static long getLong(Context context, @RobotSettingKey String name, long def) {
        return Settings.Global.getLong(context.getContentResolver(), name, def);
    }


    public static boolean putFloat(Context context, @RobotSettingKey String name, float value) {
        boolean result = false;
        if (!checkPermission(context)) {
            Log.e(TAG, "Permission deny");
            return result;
        }
        try {
            result = Settings.Global.putFloat(context.getContentResolver(), name, value);
        } catch (Exception e) {
            e.printStackTrace();
            result = false;
        }
        return result;
    }

    public static float getFloat(Context context, @RobotSettingKey String name)
            throws Settings.SettingNotFoundException {
        return Settings.Global.getFloat(context.getContentResolver(), name);
    }

    public static float getFloat(Context context, @RobotSettingKey String name, float def) {
        return Settings.Global.getFloat(context.getContentResolver(), name, def);
    }

    public static boolean putString(Context context, @RobotSettingKey String name, String value) {
        boolean result = false;
        if (!checkPermission(context)) {
            Log.e(TAG, "Permission deny");
            return result;
        }
        try {
            result = Settings.Global.putString(context.getContentResolver(), name, value);
        } catch (Exception e) {
            e.printStackTrace();
            result = false;
        }
        return result;
    }

    public static String getString(Context context, @RobotSettingKey String name) {
        return Settings.Global.getString(context.getContentResolver(), name);
    }

    private static boolean checkPermission(Context context) {
        return ACCEPT_PACKAGE_LIST.contains(context.getPackageName());
    }

    public static void registerSettingsListener(Context context, List<String> nameList,
                                                final SettingsListener settingsListener) {
        Iterator<String> iterator = nameList.iterator();
        while (iterator.hasNext()) {
            registerSettingsListener(context, iterator.next(), settingsListener);
        }
    }

    public static void registerSettingsListener(Context context, @RobotSettingKey String name,
                                                final SettingsListener settingsListener) {
        myContentObserver = new MyContentObserver(settingsListener);
        context.getContentResolver().registerContentObserver(SettingsUtil.getUriFor(name),
                true, myContentObserver);
    }

    public static void unregisterSettingsListener(Context context) {
        if (myContentObserver != null) {
            context.getContentResolver().unregisterContentObserver(myContentObserver);
        }
    }

    public interface SettingsListener {
        void onChange(boolean selfChange, String name);
    }

    private static class MyContentObserver extends ContentObserver {
        private final SettingsListener settingsListener;

        public MyContentObserver(SettingsListener settingsListener) {
            super(new Handler());
            this.settingsListener = settingsListener;
        }

        @Override
        public void onChange(boolean selfChange, Uri uri) {
            String name = uri.getLastPathSegment();
            settingsListener.onChange(selfChange, name);
            Log.d(TAG, "CoreService SettingUtil onChange:" + name + " changed");
        }

    }
}
