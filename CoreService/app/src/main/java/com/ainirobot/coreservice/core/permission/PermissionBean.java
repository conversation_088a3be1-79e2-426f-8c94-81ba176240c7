package com.ainirobot.coreservice.core.permission;

import android.content.Intent;

public class PermissionBean {
    private Intent intent;
    private String pkg;
    private int pid;
    private int uid;
    private String processName;
    private boolean foregroundActivities;
    private String msg;
    private String msg2;
    private long timeMillis;
    private String stackTrace;

    PermissionBean(String processName, int pid, String shortMsg, String longMsg,
                   long timeMillis, String stackTrace) {
        this.processName = processName;
        this.pid = pid;
        this.msg = shortMsg;
        this.msg2 = longMsg;
        this.timeMillis = timeMillis;
        this.stackTrace = stackTrace;
    }

    PermissionBean(int pid, int uid, boolean foregroundActivities) {
        this.pid = pid;
        this.uid = uid;
        this.foregroundActivities = foregroundActivities;
    }

    PermissionBean(String processName, int pid, String msg) {
        this.processName = processName;
        this.pid = pid;
        this.msg = msg;
    }

    PermissionBean(Intent intent, String pkg) {
        this.intent = intent;
        this.pkg = pkg;
    }

    PermissionBean(String pkg, String msg) {
        this.pkg = pkg;
        this.msg = msg;
    }

    PermissionBean(String msg) {
        this.msg = msg;
    }

    PermissionBean(int pid, int uid) {
        this.pid = pid;
        this.uid = uid;
    }

    public Intent getIntent() {
        return intent;
    }

    public String getPkg() {
        return pkg;
    }

    public int getPid() {
        return pid;
    }

    public int getUid() {
        return uid;
    }

    public String getProcessName() {
        return processName;
    }

    public boolean isForegroundActivities() {
        return foregroundActivities;
    }

    public String getMsg() {
        return msg;
    }

    public String getMsg2() {
        return msg2;
    }

    public long getTimeMillis() {
        return timeMillis;
    }

    public String getStackTrace() {
        return stackTrace;
    }
}
