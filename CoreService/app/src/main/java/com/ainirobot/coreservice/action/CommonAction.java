/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.action;

import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.CommandBean;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.robotlog.RobotLog;

import java.util.ArrayList;

public class CommonAction extends AbstractAction<CommandBean> {
    private static final String TAG = "CommonAction";
    private static final int NAN = -1;
    int mCmdId = NAN;
    private String commandType;

    protected CommonAction(ActionManager manager, int id, String[] resList) {
        super(manager, id, resList);
    }

    @Override
    protected boolean startAction(CommandBean command, LocalApi api) {
        commandType = command.getCmdType();
        mCmdId = api.sendCommand(command.getReqId(), command.getCmdType(),
                command.getParams(), command.isContinue());
        return mCmdId >= 0;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        Log.d(TAG, "Action stop : " + getActionId());
        if (mCmdId != NAN) {
            mApi.cancelCommand(mCmdId, mParam.getCmdType());
        }
        return true;
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return null;
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return null;
    }

    @Override
    protected boolean verifyParam(CommandBean param) {
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        if (commandType != null && !commandType.equals(command)) {
            Log.d(TAG, "Command not match， receive : " + command + " , command : " + commandType + "  extraData=" + extraData);
            return false;
        }
        if (!mParam.isContinue()) {
            RobotLog.d("CommonAction : command=" + command + "  result=" + result + "  extraData=" + extraData);
            mCmdId = NAN;
            onResult(Definition.RESULT_OK, result, extraData);
        } else {
            if (!onStatusUpdate(Definition.STATUS_INFO_UPDATE, result, true, extraData)) {
                Log.e(TAG, "CommonAction : callback invalid " + getActionId());
                onError(Definition.ERROR_LISTENER_INVALID, "Callback invalid");
            }
        }
        return true;
    }

    @Override
    public boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        RobotLog.d("CommonAction : command=" + command + "  status =" + status);
        if (!onStatusUpdate(Definition.STATUS_INFO_UPDATE, status, true, extraData)) {
            Log.e(TAG, "CommonAction : callback invalid " + getActionId());
            onError(Definition.ERROR_LISTENER_INVALID, "Callback invalid");
        }
        return true;
    }
}
