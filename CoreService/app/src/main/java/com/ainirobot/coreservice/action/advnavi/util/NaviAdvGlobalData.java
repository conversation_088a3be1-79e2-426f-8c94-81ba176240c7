package com.ainirobot.coreservice.action.advnavi.util;

import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;

/**
 * Data: 2023/4/11 18:23
 * Author: wanglijing
 * Description: NaviAdvGlobalData
 */
public class NaviAdvGlobalData {

    private static NaviAdvGlobalData mInstance;
    private int elevatorCmdRetryCount = -1;

    public static NaviAdvGlobalData getInstance() {
        if (mInstance == null) {
            synchronized (NaviAdvGlobalData.class) {
                if (mInstance == null) {
                    mInstance = new NaviAdvGlobalData();
                }
            }
        }
        return mInstance;
    }

    /**
     * 控制电梯命令 最大连续失败次数
     */
    public int elevatorCmdRetryCount() {
        if (elevatorCmdRetryCount < 0) {
            //默认五次重试机会
            elevatorCmdRetryCount = RobotSettingApi.getInstance()
                    .getRobotInt("robot_setting_elevator_control_retry_count") <= 0
                    ? 5
                    : elevatorCmdRetryCount;
        }
        return elevatorCmdRetryCount;
    }

}
