package com.ainirobot.coreservice.bi.report;


import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

/**
 * inspection bi report
 *
 * @version V1.0.0
 * @date 2019/4/9 17:05
 */
public class ModuleAppCrashReport extends BaseBiReport {

    private static final String TABLE_NAME = "gb_moduleapp_crash";

    private static final String CTIME = "ctime";

    public static final String CRASH_ID = "crash_id";

    public static final String TYPE = "type";

    private static final String STATE = "state";

    public static final String TYPE_RESTART = "Restart ModuleApp";

    public static final int STATE_ABNORMAL = 0;

    public ModuleAppCrashReport() {
        super(TABLE_NAME);
    }

    public ModuleAppCrashReport addState(int state) {
        addData(STATE, state);
        return this;
    }

    public ModuleAppCrashReport addCrashId(String crashId) {
        addData(CRASH_ID, crashId);
        return this;
    }

    public ModuleAppCrashReport addType(String type) {
        addData(TYPE, type);
        return this;
    }

    @Override
    public void report() {
        addData(CTIME, System.currentTimeMillis());
        super.report();
    }
}
