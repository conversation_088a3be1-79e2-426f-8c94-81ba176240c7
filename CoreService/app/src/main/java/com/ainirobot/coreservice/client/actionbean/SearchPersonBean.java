package com.ainirobot.coreservice.client.actionbean;

public class SearchPersonBean extends BaseBean {
    private int cameraType;
    private int personId;
    private String personName;
    private long searchTimeout;

    public int getCameraType() {
        return cameraType;
    }

    public String getPersonName() {
        return personName;
    }

    public void setCameraType(int cameraType) {
        this.cameraType = cameraType;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public int getPersonId() {
        return personId;
    }

    public void setPersonId(int personId) {
        this.personId = personId;
    }

    public long getSearchTimeout() {
        return searchTimeout;
    }

    public void setSearchTimeout(long searchTimeout) {
        this.searchTimeout = searchTimeout;
    }
}
