package com.ainirobot.coreservice.core.apiproxy.result;

import com.google.gson.Gson;

public class BaseApiResult {

    public static final int RESULT_SUCCESS = 1;
    public static final int RESULT_SKIP = 2;
    public static final int RESULT_LOCAL_SYNC_UPGRADED = 10;
    public static final int RESULT_FAILED = -1;
    public static final int RESULT_RECOGNIZE_ONLY_FACE = -10;

    private Type type;
    private int result;
    private Object data;

    public enum Type {
        UNKNOWN, VISION_STATE, PERSON_DATA, LOCAL_SYNC_FACE, REGISTER, RECOGNIZE, RE_REGISTER,
        REMOTE_LOGIN_STATE, REMOTE_SYNC_MEMBER, REGISTER_FACE_LIST, CHECK_VISION_CONNECT,
        SYSTEM_TOKEN_CHANGED, TOKEN_TABLE_CHANGED, FACE_TABLE_CHANGED, NETWORK_STATE,
        DELETE_FACE_LIST, REMOTE_UNBIND
    }

    public BaseApiResult(Type type) {
        this.type = type;
    }

    public BaseApiResult(Type type, int result) {
        this.type = type;
        this.result = result;
    }

    public BaseApiResult(Type type, int result, Object data) {
        this.type = type;
        this.result = result;
        this.data = data;
    }

    public Type getType() {
        return type;
    }

    public int getResult() {
        return result;
    }

    public void setResult(int result) {
        this.result = result;
    }

    public Object getData() {
        return data;
    }

    @Override
    public String toString() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }
}
