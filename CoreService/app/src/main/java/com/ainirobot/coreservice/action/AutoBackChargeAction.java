/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.action;


import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.AutoChargeBean;
import com.ainirobot.coreservice.client.actionbean.BatteryBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.config.ConfigManager;
import com.ainirobot.coreservice.core.LocalApi;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Timer;
import java.util.TimerTask;

public class AutoBackChargeAction extends AbstractAction<AutoChargeBean> {

    private static final String TAG = "ChargeAction";
    private static final String SUCCEED = "succeed";
    private final String FAILED = "failed";
    private static final int CHARGE_TYPE_PILE = 0;
    private static final int CHARGE_TYPE_WIRE = 1;
    private static final int CHARGE_TYPE_WIRE_CUSTOM = 2;

    private AutoChargeBean autoChargeBean = null;
    private static final long AUTO_CHARGE_TIMEOUT = 40 * Definition.SECOND; // timeout from starting autocharge to ending action
    private long NAVI_TIMEOUT = 4 * Definition.MINUTE;
    private static final long DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT = 300 * 1000;
    private State mState = State.IDLE;
    private Timer naviToChargeTimer;
    private long timeout = NAVI_TIMEOUT;
    private volatile boolean finalStatus = false;
    private Pose mFlagPose;
    private double mAvoidDistance;
    private long mAvoidTimeout;
    private String mPoseListener;
    private int mInt = 0;
    private volatile boolean isNavigating = false;
    private double coordinateDeviation = 0.1f;
    private static final int DELAY_TIME = 2;
    private IChargePolicy mCurrentPolicy;
    private IChargePolicy[] mPolicy = new IChargePolicy[Definition.CHARGE_MAX_POLICY];
    /**
     * 多机模式等待超时时长，默认300s
     **/
    private long mWaitTimeout;
    /**
     * 是否是在多机避障状态，如果是则停止处理机器N秒不动退出导航的逻辑
     **/
    private volatile boolean isMultipleWaitStatus = false;
    private Timer mWaitTimer;
    private int mConfigChargingType;
    private String mCustomLocation;
    private NavigationResetHeadUtils mNavigationResetHeadUtils;

    private enum State {
        IDLE, GO_LOCATION, GO_CHARGE, CHARGING
    }

    private interface IChargePolicy {
        boolean start();

        boolean stop();

        boolean onCmdResponse(int cmdId, String cmdType, String params, LocalApi api);

        void onCmdStatusUpdate(int cmdId, String command, String status);
    }

    /**
     * 线充方式回充
     */
    private class WireChargingPolicy implements IChargePolicy{

        @Override
        public boolean start() {
            Log.d(TAG, "WireChargingPolicy start: mState=" + mState);
            isNavigating = false;
            registerStatusListener(Definition.STATUS_BATTERY, new StatusListener() {
                @Override
                public void onStatusUpdate(String data) {
                    Log.d(TAG, "STATUS_BATTERY onStatusUpdate: data=" + data);
                    Gson gson = new Gson();
                    BatteryBean batteryBean = gson.fromJson(data, BatteryBean.class);
                    if (batteryBean != null && batteryBean.isCharging()) {
                        finishAction(Definition.RESULT_OK, "wire charging success");
                    }
                }
            });
            return false;
        }

        @Override
        public boolean stop() {
            Log.d(TAG, "WireChargingPolicy stop: mState=" + mState);
            unregisterStatusListener(Definition.STATUS_BATTERY);
            return false;
        }

        @Override
        public boolean onCmdResponse(int cmdId, String cmdType, String params, LocalApi api) {
            return false;
        }

        @Override
        public void onCmdStatusUpdate(int cmdId, String command, String status) {

        }
    }

    /**
     * 底盘通过视觉拍照定位，然后底盘直接控制轮子 由起始回充点 移动到 充电桩.
     */
    private class VisionPolicy implements IChargePolicy {

        @Override
        public boolean start() {
            boolean isFrontCharge = ConfigManager.isFrontCharge();
            Log.d(TAG, "send cmd_navi_vision_charge_start cmd  isFrontCharge=" + isFrontCharge);
            mApi.startVisionCharge(mReqId, isFrontCharge);
            return true;
        }

        @Override
        public boolean stop() {
            Log.d(TAG, "stop mState :" + mState);
//            if (mState == State.GO_CHARGE) {
            mApi.stopVisionCharge(mReqId);
//            } else if (mState == State.GO_LOCATION) {
//                mApi.stopNavigation(mReqId);
//            }
            return true;
        }

        @Override
        public boolean onCmdResponse(int cmdId, String cmdType, String params, LocalApi api) {
            Log.d(TAG, "VisionPolicy onCmdResponse cmdType=" + cmdType + " params=" + params);
            switch (cmdType) {
                case Definition.CMD_NAVI_VISION_CHARGE_START:
                    if ("timeout".equals(params)) {
                        finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.CHARGE_FAIL_VISION_CHARGE_TIMEOUT));
                        return false;
                    }

                    Gson gson = new Gson();
                    ChargeResult chargeResult = gson.fromJson(params, ChargeResult.class);
                    int code = chargeResult.getCode();
                    if (code == 25) {
                        chargeResult.setCode(Definition.RESULT_OK);
                        finishAction(Definition.RESULT_OK, gson.toJson(chargeResult));
                    } else if (code == 26) {
                        chargeResult.setCode(Definition.RESULT_FAILED);
                        //finishAction(Definition.RESULT_FAILED, gson.toJson(chargeResult));
                        finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.CHARGE_FAIL_WHEN_VISION_CHARGE_START));
                    }
                    break;

                case Definition.CMD_NAVI_VISION_CHARGE_STOP:
                    gson = new Gson();
                    chargeResult = gson.fromJson(params, ChargeResult.class);
                    code = chargeResult.getCode();
                    if (code == 0) {
                        chargeResult.setCode(Definition.RESULT_OK);
                        finishAction(Definition.RESULT_OK, gson.toJson(chargeResult));
                    } else {
                        chargeResult.setCode(Definition.RESULT_FAILED);
                        //finishAction(Definition.RESULT_FAILED, gson.toJson(chargeResult));
                        finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.CHARGE_FAIL_WHEN_VISION_CHARGE_STOP));
                    }
                    break;

                default:
                    break;

            }
            return true;
        }

        @Override
        public void onCmdStatusUpdate(int cmdId, String cmdType, String status) {
            switch (cmdType) {
                case Definition.CMD_NAVI_VISION_CHARGE_START:
                    Log.d(TAG, "VisionPolicy onCmdStatusUpdate : cmdType=" + cmdType
                            + " status=" + status);
                    Gson gson = new Gson();
                    ChargeResult chargeResult = gson.fromJson(status, ChargeResult.class);
                    int code = chargeResult.getCode();

                    if (code == 0) {
                        onStatusUpdate(Definition.STATUS_VISION_CHARGE_START_GOTO_CHARGING_PILE,
                                "Start go to charging pile");
                    } else {
                        onStatusUpdate(code, status);
                    }
                    break;
            }
        }

        class ChargeResult {
            private int code;
            private String message;

            public ChargeResult() {
            }

            public ChargeResult(int code, String message) {
                this.code = code;
                this.message = message;
            }

            public int getCode() {
                return code;
            }

            public String getMessage() {
                return message;
            }

            public void setCode(int code) {
                this.code = code;
            }

            public void setMessage(String message) {
                this.message = message;
            }
        }

    }

    protected AutoBackChargeAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_AUTO_NAVI_CHARGE, resList);
        mPolicy[Definition.CHARGE_VISION_POLICY] = new VisionPolicy();
        mPolicy[Definition.CHARGE_WIRE_POLICY] = new WireChargingPolicy();
        mNavigationResetHeadUtils = new NavigationResetHeadUtils(mApi, mManager.getCoreService().getHeadMoveManager());
    }

    @Override
    protected boolean startAction(AutoChargeBean parameter, LocalApi api) {
        Log.d(TAG, "AutoBackChargeAction start , curState = " + mState);
        if (mState == State.GO_LOCATION || mState == State.GO_CHARGE) {
            return false;
        }
        updateParams(parameter);
        mApi.isRobotEstimate(mReqId);
        return true;
    }

    private void updateParams(AutoChargeBean parameter) {
        autoChargeBean = parameter;
        mReqId = autoChargeBean.getReqId();
        timeout = autoChargeBean.getTimeout();
        mAvoidDistance = autoChargeBean.getAvoidDistance();
        mAvoidTimeout = autoChargeBean.getAvoidTimeout();
        coordinateDeviation = autoChargeBean.getCoordinateDeviation();
        long waitTimeout = autoChargeBean.getMultipleWaitTime();
        mWaitTimeout = (waitTimeout <= 0) ? DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT : waitTimeout;
        isMultipleWaitStatus = false;
        if (timeout < NAVI_TIMEOUT) {
            timeout = NAVI_TIMEOUT;
        }
        Log.d(TAG, "verifyParam timeout:" + timeout + ", mAvoidTimeout:" + mAvoidTimeout
                + ", mAvoidDistance:" + mAvoidDistance);
    }

    private void startCheckLocation() {
        // parse parameter to gain chargePoint , then　goPosition (px py angle) 导航到该点,　then startAutoCharge
        mFlagPose = null;
        mInt = 0;
        mState = State.GO_LOCATION;
        Log.d(TAG, "start charge isRobotInLocation");
        registerPoseListener();
        isNavigating = true;
        if (mConfigChargingType == CHARGE_TYPE_WIRE_CUSTOM) {
            isRobotInLocation(mCustomLocation, coordinateDeviation);
        } else {
            isRobotInLocation(getChargingFinalPoseTypeId(mConfigChargingType), coordinateDeviation);
        }
//        mApi.resetHead(mReqId);
        startNaviToChargeTimer();
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_IS_ESTIMATE:
                Log.d(TAG, "CMD_NAVI_IS_ESTIMATE  result : " + result);
                if ("true".equals(result)) {
                    checkChargePlace();
                } else {
                    finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.CHARGE_FAIL_WHEN_NOT_ESTIMATE));
                }
                return true;
            case Definition.CMD_NAVI_GO_LOCATION:
            case Definition.CMD_NAVI_GO_POSITION_BY_TYPE:
                if (mState == State.GO_CHARGE) {
                    return false;
                }
                Log.d(TAG, "cmdType: " + command + ", result = " + result + ", mState : " + mState);
                cancelNaviToChargeTimer();
                if (Definition.NAVIGATION_OK.equals(result)) {
                    mState = State.GO_CHARGE;
                    isNavigating = false;
                    cmdStatusUpdate(mReqId, Definition.CMD_NAVI_IS_IN_LOCATION, Definition.CMD_NAVI_IS_IN_LOCATION, extraData);//通知上层已经在回充点位置
                    startChargePolicy();
                } else {
                    finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.CHARGE_FAIL_WHEN_NAVIGATION));
                }
                return true;
            case Definition.CMD_NAVI_IS_IN_LOCATION:
                Log.d(TAG, "handle CMD_NAVI_IS_IN_LOCATION result : " + result);
                if (mState == State.GO_CHARGE) {
                    return false;
                }
                handleIsInLocation(result, extraData);
                return true;
            case Definition.CMD_NAVI_RESUME_SPECIAL_PLACE_THETA:
                Log.d(TAG, "handle CMD_NAVI_RESUME_SPECIAL_PLACE_THETA resutl : " + result);
                if (SUCCEED.equals(result) || Definition.SPECIAL_THETA_ALREADY_SUCCESS_NO_NEED_TO_RESUME.equals(result)) {
                    mState = State.GO_CHARGE;
                    isNavigating = false;
                    cancelNaviToChargeTimer();
                    startChargePolicy();
                } else {
                    isNavigating = true;
                    Log.d(TAG, "handle CMD_NAVI_RESUME_SPECIAL_PLACE_THETA FAIL");
                    if (mConfigChargingType != CHARGE_TYPE_WIRE_CUSTOM) {
                        mApi.goPositionByType(mReqId, getChargingPoseTypeId(mConfigChargingType), Definition.SPECIAL_PLACE_HIGH_PRIORITY);
                    } else {
                        mApi.goPlace(mReqId, mCustomLocation);
                    }
                }
                return true;
            case Definition.CMD_NAVI_GET_LOCATION:
                processGetLocation(result, extraData);
                return true;

            default:
                if (!mNavigationResetHeadUtils.onCmdResponse(cmdId, command, result, extraData)){
                    onChargePolicyCmdResponse(cmdId, command, result);
                }
                return false;
        }

    }

    @Override
    public boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_GO_LOCATION:
            case Definition.CMD_NAVI_GO_POSITION_BY_TYPE:
                handleNavigationStatus(status);
                return true;
            case Definition.CMD_NAVI_IS_IN_LOCATION:
                onStatusUpdate(Definition.STATUS_ALREADY_AT_START_CHARGE_POINT, "already at start charge point and begin to psb charge");
                return true;
            default:
                if (!mNavigationResetHeadUtils.cmdStatusUpdate(cmdId, command, status, extraData)){
                    onChargePolicyCmdStatusUpdate(cmdId, command, status);
                }
                return true;
        }
    }

    private void processGetLocation(String result, String extraData) {
        try {
            JSONObject json = new JSONObject(result);
            boolean isExist = json.getBoolean(Definition.JSON_NAVI_SITE_EXIST);
            String posName = json.getString(Definition.JSON_REMOTE_POS_NAME);
            if (isExist) {
                updatePolicy();
                startCheckLocation();
            } else {
                if (mConfigChargingType == CHARGE_TYPE_WIRE_CUSTOM) {
                    mConfigChargingType = CHARGE_TYPE_WIRE;
                    mApi.getLocation(mReqId, getChargingFinalPoseTypeId(mConfigChargingType));
                } else {
                    finishAction(isChargePile() ?
                            Definition.CHARGE_FAIL_NOT_EXIST_CHARGING_PILE_POSE :
                            Definition.CHARGE_FAIL_NOT_EXIST_CHARGING_WIRE_POSE, getChargingFinalPoseName(mConfigChargingType));
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
            finishAction(isChargePile() ?
                    Definition.CHARGE_FAIL_NOT_EXIST_CHARGING_PILE_POSE :
                    Definition.CHARGE_FAIL_NOT_EXIST_CHARGING_WIRE_POSE, getChargingFinalPoseName(mConfigChargingType));
        }
    }

    private boolean isChargePile() {
        return mConfigChargingType == CHARGE_TYPE_PILE;
    }

    private void isRobotInLocation(String placeName, double coordinateDeviation) {
        JSONObject obj = new JSONObject();
        try {
            obj.put(Definition.JSON_NAVI_TARGET_PLACE_NAME, placeName);
            obj.put(Definition.JSON_NAVI_COORDINATE_DEVIATION, coordinateDeviation);
            mApi.isRobotInlocations(mReqId, obj.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void isRobotInLocation(int typeId, double coordinateDeviation) {
        mApi.isRobotInLocations(mReqId, typeId, Definition.SPECIAL_PLACE_HIGH_PRIORITY, coordinateDeviation);
    }

    private void resumeSpecialPlaceTheta(String placeName) {
        JSONObject obj = new JSONObject();
        try {
            obj.put(Definition.JSON_NAVI_TARGET_PLACE_NAME, placeName);
            mApi.resumeSpecialPlaceTheta(mReqId, obj.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void resumeSpecialPlaceTheta(int typeId) {
        JSONObject obj = new JSONObject();
        try {
            obj.put(Definition.JSON_NAVI_TYPE_ID, typeId);
            obj.put(Definition.JSON_NAVI_PRIORITY, Definition.SPECIAL_PLACE_HIGH_PRIORITY);
            mApi.isRobotInlocations(mReqId, obj.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void handleIsInLocation(String result, String extraData) {
        try {
            JSONObject json = new JSONObject(result);
            boolean isInLocation = json.optBoolean(Definition.JSON_NAVI_IS_IN_LOCATION, false);
            if (isInLocation) {
                if (mConfigChargingType != CHARGE_TYPE_PILE) {
                    Log.d(TAG, "Already in location");
                    cmdStatusUpdate(mReqId, Definition.CMD_NAVI_IS_IN_LOCATION, Definition.CMD_NAVI_IS_IN_LOCATION, extraData);
                    startChargePolicy();
                } else {
                    Log.d(TAG, "resume place theta");
                    cmdStatusUpdate(mReqId, Definition.CMD_NAVI_IS_IN_LOCATION, Definition.CMD_NAVI_IS_IN_LOCATION, extraData);//通知上层已经在回充点位置
                    if (mConfigChargingType != CHARGE_TYPE_WIRE_CUSTOM) {
                        resumeSpecialPlaceTheta(getChargingPoseTypeId(mConfigChargingType));
                    } else {
                        resumeSpecialPlaceTheta(mCustomLocation);
                    }
                }
            } else {
                Log.d(TAG, "start go place , checkResetHeadState ============  ");
                mNavigationResetHeadUtils.checkResetHeadState(new NavigationResetHeadUtils.ResetHeadListener() {
                    @Override
                    public void onResetHeadSuccess() {
                        Log.d(TAG, "start go place , go ============  ");
                        if (mConfigChargingType != CHARGE_TYPE_WIRE_CUSTOM) {
                            if (mConfigChargingType == CHARGE_TYPE_PILE) {
                                if (ConfigManager.isFrontCharge()) {
                                    mApi.goPositionByType(mReqId, Definition.CHARGING_POINT_TYPE, Definition.SPECIAL_PLACE_HIGH_PRIORITY, true);
                                } else {
                                    mApi.goPositionByType(mReqId, Definition.CHARGING_POINT_TYPE, Definition.SPECIAL_PLACE_HIGH_PRIORITY);
                                }

                            } else {
                                mApi.goPositionByType(mReqId, Definition.POSITIONING_POINT_TYPE, Definition.SPECIAL_PLACE_HIGH_PRIORITY);
                            }
                        } else {
                            mApi.goPlace(mReqId, mCustomLocation);
                        }
                        mNavigationResetHeadUtils.startHeadStatusListener(mReqId);
                    }

                    @Override
                    public void onResetHeadFailed(int errorCode, String errorString) {
                        onError(errorCode, errorString);
                    }
                }, mReqId);

            }
        } catch (Throwable e) {
            e.printStackTrace();
            Log.d(TAG, "handleIsInLocation error : " + e.getLocalizedMessage());
            finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.CHARGE_FAIL_WHEN_PARSE_IN_LOCATION));
        }
    }

    private String buildFailJson(int failStatus) {
        JSONObject json = new JSONObject();
        try {
            json.put(Definition.CHARGE_FAIL_STATUS, failStatus);
            String reason = getFailReasonMsg(failStatus);
            json.put(Definition.CHARGE_FAIL_REASON, reason);
            return json.toString();
        } catch (JSONException e) {
            e.printStackTrace();
            return "Unknown fail status";
        }
    }

    private String getFailReasonMsg(int failStatus) {
        switch (failStatus) {
            case Definition.CHARGE_FAIL_WHEN_NOT_ESTIMATE:
                return "Need estimate first before navigation !!!";

            case Definition.CHARGE_FAIL_WHEN_NAVIGATION:
                return "Navigate to statChargePoint error !!!";

            case Definition.CHARGE_FAIL_WHEN_PARSE_IN_LOCATION:
                return "Parse IsInLocation result error !!!";

            case Definition.CHARGE_FAIL_WHEN_NOT_MOVE_FOR_20S:
                return "Not MOVE any Distance for 20s !!!";

            case Definition.CHARGE_FAIL_WHEN_PSB_CHARGE:
                return "PSB autoCharge failure !!!";

            case Definition.CHARGE_FAIL_WHEN_LARGE_MAP_NAVI_TIMEOUT:
                return "Map is too large to navigate timeout !!!";

            case Definition.CHARGE_FAIL_WHEN_VISION_CHARGE_START:
                return "Vision charge start failed !!!";

            case Definition.CHARGE_FAIL_WHEN_VISION_CHARGE_STOP:
                return "Vision charge stop failed !!!";

            case Definition.CHARGE_FAIL_VISION_CHARGE_TIMEOUT:
                return "Vision charge timeout !!!";

            case Definition.STATUS_NAVI_MULTI_MAP_NOT_MATCH:
                return "navigation multi robot map not match!";

            case Definition.STATUS_NAVI_MULTI_LORA_DISCONNECT:
                return "navigation multi robot lora disconnect!";

            case Definition.STATUS_NAVI_MULTI_LORA_CONFIG_FAIL:
                return "navigation multi robot lora config fail!";

            case Definition.STATUS_NAVI_MULTI_VERSION_NOT_MATCH:
                return "navigation multi robot version not match!";

            default:
                return "Unkown fail reason ";
        }
    }

    private void handleNavigationStatus(String status) {
        Log.d(TAG, "handleNavigationStatus status : " + status);
        switch (status) {
            case Definition.NAVIGATION_OCCLUDED:
                onStatusUpdate(Definition.STATUS_GOAL_OCCLUDED, "The target is occluded");
                break;

            case Definition.NAVIGATION_OCCLUDED_END:
                onStatusUpdate(Definition.STATUS_GOAL_OCCLUDED_END, "The occluded end");
                break;

            case Definition.NAVIGATION_AVOID:
                onStatusUpdate(Definition.STATUS_NAVI_AVOID, "The local path search failed");
                break;

            case Definition.NAVIGATION_AVOID_END:
                onStatusUpdate(Definition.STATUS_NAVI_AVOID_END, "The local path search succeed");
                break;

            case Definition.NAVIGATION_OUT_MAP:
                onStatusUpdate(Definition.STATUS_NAVI_OUT_MAP, "The goal is out of map or more dangerous");
                break;

            case Definition.NAVIGATION_GLOBAL_PATH_FAILED:
                onStatusUpdate(Definition.STATUS_NAVI_GLOBAL_PATH_FAILED, "The global is path search failed");
                break;

            case Definition.NAVIGATION_OBSTACLES_AVOID:
                onStatusUpdate(Definition.STATUS_NAVI_OBSTACLES_AVOID, "obstacles avoid");
                break;

            case Definition.NAVIGATION_STARTED:
                onStatusUpdate(Definition.STATUS_START_NAVIGATION, "navigation started");
                break;
            case Definition.NAVIGATION_MULTI_ROBOT_WAITING:
                updateMultipleRobotWaitingStatus(true);
                startWaitingTimer();
                onStatusUpdate(Definition.STATUS_NAVI_MULTI_ROBOT_WAITING, "Multiple robots are waiting to avoid obstacles");
                break;

            case Definition.NAVIGATION_MULTI_ROBOT_WAITING_END:
                updateMultipleRobotWaitingStatus(false);
                cancelWaitTimer();
                startNaviToChargeTimer();
                onStatusUpdate(Definition.STATUS_NAVI_MULTI_ROBOT_WAITING_END, "Multiple robots passageways returned to normal");
                break;
            case Definition.NAVIGATION_MULTI_MAP_NOT_MATCH:
                finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.STATUS_NAVI_MULTI_MAP_NOT_MATCH));
                break;

            case Definition.NAVIGATION_MULTI_LORA_DISCONNECT:
                finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.STATUS_NAVI_MULTI_LORA_DISCONNECT));
                break;

            case Definition.NAVIGATION_MULTI_LORA_CONFIG_FAIL:
                finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.STATUS_NAVI_MULTI_LORA_CONFIG_FAIL));
                break;

            case Definition.NAVIGATION_MULTI_VERSION_NOT_MATCH:
                finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.STATUS_NAVI_MULTI_VERSION_NOT_MATCH));
                break;
            default:
                break;
        }
    }

    /**
     * 更新多机避障状态，如果是多机避障状态，则机器人超时等待策略需忽略
     * @param isWaiting 是否正在多机避障等待状态
     */
    private void updateMultipleRobotWaitingStatus(boolean isWaiting){
        isMultipleWaitStatus = isWaiting;
    }

    private void startWaitingTimer() {
        cancelWaitTimer();
        mWaitTimer = new Timer();
        mWaitTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (mState != State.GO_LOCATION) {
                    return;
                }
                finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.ERROR_MULTI_ROBOT_WAITING_TIMEOUT));
            }
        }, mWaitTimeout);
    }

    private void cancelWaitTimer() {
        if (mWaitTimer != null) {
            mWaitTimer.cancel();
            mWaitTimer = null;
        }
    }

    private boolean isAborted(Pose pose) {
        double distance = pose.getDistance(mFlagPose);
        if (!isMultipleWaitStatus && Double.compare(distance, mAvoidDistance) < 0) {
            long movingTime = pose.getTime() - mFlagPose.getTime();
            if (movingTime > mAvoidTimeout) {
                return true;
            }
        } else {
            mFlagPose = pose;
        }
        return false;
    }

    private void registerPoseListener() {
        mPoseListener = registerStatusListener(Definition.STATUS_POSE, new StatusListener() {
            @Override
            public void onStatusUpdate(String data) {
                if (mState == State.GO_CHARGE) {
                    return;
                }
                Pose pose = mGson.fromJson(data, Pose.class);
                boolean aborted = isAborted(pose);
                if (mInt % 2 == 0) {
//                    Log.d(TAG, "is Aborted(pose) : " + aborted);
                } else {
//                    Log.d(TAG, "isAborted(pose) : " + aborted);
                }
                mInt++;
                if (isNavigating && aborted) {
                    finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.CHARGE_FAIL_WHEN_NOT_MOVE_FOR_20S));
                }
            }
        });
    }

    private void startNaviToChargeTimer() {
        Log.d(TAG, "startNaviToChargeTimer mstate:" + mState + " multiWaitStatus:" + isMultipleWaitStatus);
        if (mState != State.GO_LOCATION){
            return;
        }
        cancelNaviToChargeTimer();
        naviToChargeTimer = new Timer();
        naviToChargeTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (mState == State.GO_CHARGE || isMultipleWaitStatus) {
                    return;
                }
//                if (mState == State.GO_LOCATION) { // 3 minute timeout , stopNavigation called on getStopCommands
//                    mApi.stopNavigation(mReqId);
//                }
                finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.CHARGE_FAIL_WHEN_LARGE_MAP_NAVI_TIMEOUT));
            }
        }, timeout);

    }

    private void cancelNaviToChargeTimer() {
        if (naviToChargeTimer != null) {
            naviToChargeTimer.cancel();
            naviToChargeTimer = null;
        }
    }

    private void finishAction(int status, String msg) {
        Log.d(TAG, "AutoBackChargeAction finishAction : " + status + " " + msg);
        mState = State.IDLE;
        finalStatus = false;
        onResult(status, msg);
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        Log.d(TAG, "AutoBackChargeAction stopAction ------------  ");
        updateMultipleRobotWaitingStatus(false);
        cancelWaitTimer();
        unregisterStatusListener(mPoseListener);
//        mApi.stopNavigation(mReqId);
        cancelNaviToChargeTimer();
        stopChargePolicy();
        mState = State.IDLE;
        finalStatus = false;
        isNavigating = false;
        mConfigChargingType = 0;
        mInt = 0;
        mNavigationResetHeadUtils.stop();
        return true;
    }

    @Override
    protected boolean verifyParam(AutoChargeBean p) {
        return true;
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        Log.d(TAG, "getStopCommands call ");
        return new ArrayList<StopCommandList.StopCommand>() {{
            add(new StopCommandList.StopCommand(Definition.CMD_NAVI_STOP_NAVIGATION, null));
        }};
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        Log.d(TAG, "getStopCommandChecker call ");
        return new StopCommandList.IStopCommandChecker() {
            @Override
            public boolean executeSuccess(String command, String result) {
                Log.d(TAG, "AutoChargeAction stop check : " + command);
                switch (command) {
                    case Definition.CMD_NAVI_STOP_NAVIGATION:
                        return true;
                    default:
                        break;
                }
                return false;
            }
        };
    }

    private void checkChargePlace() {
        boolean hasChargeIR = ConfigManager.isHasChargeIR();
        if (hasChargeIR && isChargingTypePile()) {//有TopIR且选择桩充
            mConfigChargingType = CHARGE_TYPE_PILE;
        } else {
            mCustomLocation = mManager.getRobotSettingManager().
                    getRobotSetting(Definition.ROBOT_SETTING_LOW_BATTERY_NAVI_LOCATION);
            Log.e(TAG, "mCustomLocation: " + mCustomLocation);
            mConfigChargingType = TextUtils.isEmpty(mCustomLocation) ? CHARGE_TYPE_WIRE : CHARGE_TYPE_WIRE_CUSTOM;
        }
        if (mConfigChargingType != CHARGE_TYPE_WIRE_CUSTOM) {
            mApi.getLocation(mReqId, getChargingFinalPoseTypeId(mConfigChargingType));
        } else {
            // CHARGE_TYPE_WIRE_CUSTOM 类型 业务层设置的其他点位作为回充点
            mApi.getLocation(mReqId, mCustomLocation);
        }
    }

    private boolean isChargingTypePile() {
        String chargingType = mManager.getRobotSettingManager().
                getRobotSetting(Definition.ROBOT_SETTINGS_CHARGING_TYPE);
        return Definition.CHARGING_TYPE_PILE.equals(chargingType);
    }

    private void updatePolicy() {
        if (mConfigChargingType == CHARGE_TYPE_PILE) {
            mCurrentPolicy = mPolicy[Definition.CHARGE_VISION_POLICY];
        } else {
            mCurrentPolicy = mPolicy[Definition.CHARGE_WIRE_POLICY];
        }
    }

    private String getChargingFinalPoseName(int chargingType) {
        switch (chargingType) {
            case CHARGE_TYPE_PILE:
                return Definition.START_CHARGE_PILE_POSE;
            case CHARGE_TYPE_WIRE:
                return Definition.LOCATE_POSITION_POSE;
            case CHARGE_TYPE_WIRE_CUSTOM:
                return mCustomLocation;
        }
        return Definition.LOCATE_POSITION_POSE;
    }

    private int getChargingFinalPoseTypeId(int chargingType) {
        switch (chargingType) {
            case CHARGE_TYPE_PILE:
                return Definition.CHARGING_POLE_TYPE;
            case CHARGE_TYPE_WIRE:
                return Definition.POSITIONING_POINT_TYPE;
        }
        return Definition.POSITIONING_POINT_TYPE;
    }

    private String getChargingPoseName(int chargingType) {
        switch (chargingType) {
            case CHARGE_TYPE_PILE:
                return Definition.START_BACK_CHARGE_POSE;
            case CHARGE_TYPE_WIRE:
                return Definition.LOCATE_POSITION_POSE;
            case CHARGE_TYPE_WIRE_CUSTOM:
                return mCustomLocation;
        }
        return Definition.LOCATE_POSITION_POSE;
    }

    private int getChargingPoseTypeId(int chargingType) {
        switch (chargingType) {
            case CHARGE_TYPE_PILE:
                return Definition.CHARGING_POINT_TYPE;
            case CHARGE_TYPE_WIRE:
                return Definition.POSITIONING_POINT_TYPE;
        }
        return Definition.POSITIONING_POINT_TYPE;
    }

    private void startChargePolicy() {
        if (null == mCurrentPolicy) {
            Log.e(TAG, "startChargePolicy policy is null ");
            return;
        }
        mCurrentPolicy.start();
    }

    private void stopChargePolicy() {
        if (null == mCurrentPolicy) {
            return;
        }
        mCurrentPolicy.stop();
        mCurrentPolicy = null;
    }

    private void onChargePolicyCmdStatusUpdate(int cmdId, String cmdType, String params) {
        if (null == mCurrentPolicy) {
            return;
        }
        mCurrentPolicy.onCmdStatusUpdate(cmdId, cmdType, params);
    }

    private void onChargePolicyCmdResponse(int cmdId, String cmdType, String params) {
        if (null == mCurrentPolicy) {
            return;
        }
        mCurrentPolicy.onCmdResponse(cmdId, cmdType, params, mApi);
    }
}
