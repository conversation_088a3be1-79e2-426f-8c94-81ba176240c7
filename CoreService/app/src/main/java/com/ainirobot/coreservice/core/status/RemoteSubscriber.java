package com.ainirobot.coreservice.core.status;

import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.IStatusListener;
import com.ainirobot.coreservice.client.log.RLog;

public class RemoteSubscriber implements StatusSubscriber {
    private IStatusListener listener;
    private boolean isAlive = true;

    public RemoteSubscriber(IStatusListener listener) {
        Log.d("RemoteSubscriber", "create subscriber, listener:"
                + (listener == null ? "" : listener.hashCode()));
        this.listener = listener;
    }

    @Override
    public void onStatusUpdate(String type, String data) {
        if ("status_obstacle_info".equals(type)
                || "navi_pose".equals(type)
                || "status_battery".equals(type)
                || "status_multiple_robot_working".equals(type)
                || "status_avoid_stopping".equals(type)) {
            RLog.pruneLog("RemoteSubscriber_" + type, "onStatusUpdate type:" + type + " data:" + data, 5000);
        } else {
            Log.d("RemoteSubscriber", "listener:"
                    + (listener == null ? "" : listener.hashCode()
                    + ", onStatusUpdate type:" + type + " data:" + data));
        }
        if (listener == null) {
            Log.d("RemoteSubscriber", "Listener is null : " + type);
            isAlive = false;
            return;
        }

        try {
            listener.onStatusUpdate(type, data);
        } catch (RemoteException e) {
            Log.d("RemoteSubscriber", "Listener exception : " + type);
            e.printStackTrace();
            isAlive = false;
        } catch (Exception e) {
            // 捕获所有其他可能的异常
            Log.e("RemoteSubscriber", "Unexpected exception in onStatusUpdate for type: " + type, e);
            e.printStackTrace();
            isAlive = false;
        }
    }

    @Override
    public boolean isRemote() {
        return true;
    }

    @Override
    public boolean isAlive() {
        return isAlive;
    }
}
