/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


package com.ainirobot.coreservice.action;


import android.os.Handler;
import android.os.HandlerThread;
import android.util.Log;
import android.util.SparseArray;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.HorizontalTurnHeadBean;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.utils.MessageParser;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.atomic.AtomicInteger;

public class HorizontalTurnHeadAction extends AbstractAction<HorizontalTurnHeadBean> {

    private static final String TAG = HorizontalTurnHeadAction.class.getSimpleName();

    private static final int HEAD_ANGLE_RANGE = 5;
    private static final long mMoveHeadTimeout = 18 * 1000;

    private final Handler mBgHandler;

    private Integer mMoveHeadAngle = 0;
    private long mMoveHeadTime;
    private volatile AtomicInteger mRotationCircleNumber = new AtomicInteger(0);

    private volatile boolean mIsTurning = false;
    private volatile boolean mIsRestart = false;

    private LinkedList<Integer> mCurrentTurningList = new LinkedList<>();
    private static SparseArray<int[]> mSparseArray = new SparseArray<>();


    protected HorizontalTurnHeadAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_HORIZONTAL_TURN_HEAD, resList);

        Log.d(TAG, "new HorizontalTurnHeadAction()");

        HandlerThread handlerThread = new HandlerThread(TAG);
        handlerThread.start();
        mBgHandler = new Handler(handlerThread.getLooper());
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_HEAD_MOVE_HEAD:
                handleMoveHeadResult(result);
                return true;
            case Definition.CMD_HEAD_GET_STATUS:
                handleHeadStatus(result);
                return true;
            default:
                break;
        }
        return false;
    }

    @Override
    protected boolean startAction(HorizontalTurnHeadBean parameter, LocalApi api) {
        Log.i(TAG, "startAction... isRestart:" + parameter.isRestart());
        if (mIsTurning) {
            Log.i(TAG, "the head is turning, mIsTurning = " + mIsTurning);
            return false;
        }
        mIsTurning = true;
        mRotationCircleNumber.set(0);
        initData(parameter);
        mIsRestart = parameter.isRestart();
        turningHead(mIsRestart ? mCurrentTurningList.poll() : mMoveHeadAngle);
        return true;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        if (mIsTurning) {
            mApi.moveHead(mReqId, Definition.JSON_HEAD_RELATIVE, Definition.JSON_HEAD_RELATIVE, 0, 0);
            mIsTurning = false;
            mRotationCircleNumber.set(0);
            Log.i(TAG, "stopAction... ,mIsTurning:" + mIsTurning);
        } else {
            Log.d(TAG, "the head is stop, mIsTurning = " + mIsTurning);
        }
        return true;
    }

    private void handleMoveHeadResult(String params) {
        Log.d(TAG, "Move head result : " + params);
        boolean isSucceed = MessageParser.parseResult(params);
        if (isSucceed) {
            getHeadStatus();
        } else {
            Log.e(TAG, "move head failed");
            onError(Definition.ERROR_MOVE_HEAD_FAILED, "move head failed");
        }
    }

    private void getHeadStatus() {
        mBgHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                mApi.getHeadStatus(mReqId);
            }
        }, 100);
    }

    private void handleHeadStatus(String params) {
//        Log.v(TAG, "Get head status :" + params + "      " + mMoveHeadAngle);
        if (System.currentTimeMillis() - mMoveHeadTime > mMoveHeadTimeout) {
            Log.w(TAG, "time is out, take next turning");
            onResponse();
            return;
        }

        int horizontal;
        try {
            JSONObject jsonObject = new JSONObject(params);
            horizontal = jsonObject.optInt("horizontal", 0);
            int status = jsonObject.optInt("status", -1);

            if (status == 0 && Math.abs(mMoveHeadAngle - horizontal) <= HEAD_ANGLE_RANGE) {
//                Log.v(TAG, "this cost time is " + (System.currentTimeMillis() - mMoveHeadTime));
                onResponse();
            } else {
                getHeadStatus();
            }
        } catch (Exception e) {
            e.printStackTrace();
            Log.w(TAG, "Get head status result error");
//            onError(Definition.ERROR_RESULT_PARSE, "Get head status result error");
            getHeadStatus();
        }
    }

    private void onResponse() {
        Log.d(TAG, "take next turn action mRotationCircleNumber: " + mRotationCircleNumber.get());

        final Integer tempMoveHeadAngle = mCurrentTurningList.poll();
        if (tempMoveHeadAngle == null && mRotationCircleNumber.get() >= 2) {
            Log.w(TAG, "temporary stop");
            mRotationCircleNumber.set(0);
            onResult(HorizontalTurnHeadBean.RESULT_TEMPORARY_STOP, HorizontalTurnHeadBean.MESSAGE_TEMPORARY_STOP);
            return;
        }

        //  1. 正常一个周期后需要延迟 10S, 再进行下一个周期
        //  2. 如果是预唤醒进入的, 刚好到一个周期结束, 那么就还是延迟 2S 执行
        mBgHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (tempMoveHeadAngle != null) {
                    mMoveHeadAngle = tempMoveHeadAngle;
                }
                turningHead(tempMoveHeadAngle);
            }
        }, tempMoveHeadAngle == null ? (mIsRestart ? 10 * 1000 : 2000) : 2000);
    }


    private void turningHead(Integer angle) {
        if (!mIsTurning) {
            Log.i(TAG, "the turn action is stop...");
            return;
        }

        if (angle == null) {
            takeNextHorizontalTurningList();
            angle = mCurrentTurningList.poll();
        }

        mMoveHeadAngle = angle;
        mApi.moveHead(mReqId, Definition.JSON_HEAD_ABSOLUTE,
                Definition.JSON_HEAD_ABSOLUTE, angle, Definition.DEFAULT_VERTICAL_ANGLE, 35, 0);
        mMoveHeadTime = System.currentTimeMillis();

        Log.d(TAG, "turningHead: " + angle + ", mMoveHeadAngle:" + mMoveHeadAngle + ", mMoveHeadTimeout:" + mMoveHeadTimeout);
    }

    private void initData(HorizontalTurnHeadBean parameter) {
        mSparseArray.clear();
        if (parameter.isRestart()) {
            mCurrentTurningList.clear();
        }
        List<int[]> turningAngleList = parameter.getTurningAngleList();
        for (int i = 0; i < turningAngleList.size(); i++) {
            mSparseArray.put(mSparseArray.size(), turningAngleList.get(i));
        }
    }

    private void takeNextHorizontalTurningList() {
        int[] ints = mSparseArray.get(new Random().nextInt(mSparseArray.size()));
        mCurrentTurningList.clear();
        for (int i = 0; i < ints.length; i++) {
            mCurrentTurningList.add(ints[i]);
        }
        mRotationCircleNumber.incrementAndGet();
        Log.i(TAG, "next turn action:: " + mCurrentTurningList.toString());
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return null;
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return null;
    }

    @Override
    protected boolean verifyParam(HorizontalTurnHeadBean param) {
        return true;
    }
}
