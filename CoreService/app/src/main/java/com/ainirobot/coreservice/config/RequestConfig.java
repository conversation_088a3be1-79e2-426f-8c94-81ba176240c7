/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.config;

import com.ainirobot.coreservice.core.module.ModuleManager.Permission;
import com.google.gson.JsonObject;
import com.google.gson.annotations.SerializedName;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

public class RequestConfig {

    @SerializedName("项目名称")
    private String name;

    @SerializedName("版本")
    private String version;

    @SerializedName("请求配置")
    private JsonObject request;

    private Map<String, Permission> requestMap = new ConcurrentHashMap<>();

    public int getVersion() {
        return Integer.parseInt(version);
    }

    public Map<String, Permission> parseRequest() {
        if (request != null) {
            Set<String> keys = request.keySet();
            for (String key : keys) {
                int permissionValue = request.get(key).getAsInt();
                Permission permission = Permission.fromValue(permissionValue);
                if (permission == null) {
                    continue;
                }
                requestMap.put(key, permission);
            }
        }
        return requestMap;
    }

    public Set<String> getRequestKeys() {
        return requestMap.keySet();
    }

    public Permission getPermission(String key) {
        return requestMap.get(key);
    }
}
