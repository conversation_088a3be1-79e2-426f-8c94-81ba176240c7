/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.client;

import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Bundle;
import android.os.IBinder;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.IRobotBinderPool;
import com.ainirobot.coreservice.ISystemApi;
import com.ainirobot.coreservice.bean.LoadMapBean;
import com.ainirobot.coreservice.bean.Task;
import com.ainirobot.coreservice.client.account.AccountApi;
import com.ainirobot.coreservice.client.actionbean.AutoChargeBean;
import com.ainirobot.coreservice.client.actionbean.CommandBean;
import com.ainirobot.coreservice.client.actionbean.ControlElectricDoorBean;
import com.ainirobot.coreservice.client.actionbean.FocusFollowBean;
import com.ainirobot.coreservice.client.actionbean.GoPositionBean;
import com.ainirobot.coreservice.client.actionbean.GoPositionByTypeBean;
import com.ainirobot.coreservice.client.actionbean.InspectActionBean;
import com.ainirobot.coreservice.client.actionbean.LeadingParams;
import com.ainirobot.coreservice.client.actionbean.NaviCmdTimeOutBean;
import com.ainirobot.coreservice.client.actionbean.NavigationAdvancedBean;
import com.ainirobot.coreservice.client.actionbean.NavigationBean;
import com.ainirobot.coreservice.client.actionbean.PlaceBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.actionbean.RadarAlignBean;
import com.ainirobot.coreservice.client.actionbean.ResetEstimateParams;
import com.ainirobot.coreservice.client.actionbean.RobotStandbyBean;
import com.ainirobot.coreservice.client.ashmem.ShareMemoryApi;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.listener.Person;
import com.ainirobot.coreservice.client.listener.PersonInfoListener;
import com.ainirobot.coreservice.client.log.RLog;
import com.ainirobot.coreservice.client.messagedispatcher.StatusDispatcher;
import com.ainirobot.coreservice.client.module.ModuleCallbackApi;
import com.ainirobot.coreservice.client.permission.PermissionApi;
import com.ainirobot.coreservice.client.person.PersonApi;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.client.surfaceshare.SurfaceShareApi;
import com.ainirobot.coreservice.core.InternalDef;
import com.ainirobot.coreservice.listener.IActionListener;
import com.ainirobot.coreservice.utils.FileUtils;
import com.ainirobot.coreservice.utils.ZipUtils;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.reflect.TypeToken;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SystemApi extends BaseApi {

    private static final String TAG = "SystemApi";
    public static final int ERROR_REMOTE_VISITOR = -101;

    private static SystemApi mInstance = new SystemApi();
    private ISystemApi mApi;
    private ModuleCallbackApi mCallback;
    private Gson mGson;
    private String mPackageName;
    private final ArrayList<PersonInfoListener> mAllPersonInfoListener = new ArrayList<>();
    private ActionListener mPersonListener;

    public static SystemApi getInstance() {
        return mInstance;
    }

    private SystemApi() {
        mGson = new Gson();
        mSubApiList.add(RobotSettingApi.getInstance());
        mSubApiList.add(PersonApi.getInstance());
        mSubApiList.add(AccountApi.getInstance());
        mSubApiList.add(PermissionApi.getInstance());
        mSubApiList.add(SurfaceShareApi.getInstance());
        mSubApiList.add(ShareMemoryApi.getInstance());
    }

    public void connect(Context ctx, ApiListener listener) {
        mPackageName = ctx.getPackageName();
        this.ctx = ctx;
        removeAllApiEventListeners();
        addApiEventListener(listener);
        Intent intent = IntentUtil.createExplicitIntent(Definition.CORE_SERVICE_NAME,
                IRobotBinderPool.class.getName());
        ctx.bindService(intent, apiConnection, Service.BIND_AUTO_CREATE);
        RLog.initRLog(ctx);
    }

    public void setContext(Context context) {
        this.ctx = context;
        mPackageName = context.getPackageName();
    }

    public void onServiceConnected(ComponentName className, IBinder service) {
        apiConnection.onServiceConnected(className, service);
    }

    public void onServiceDisconnected(ComponentName className) {
        apiConnection.onServiceDisconnected(className);
    }

    public void setCallback(ModuleCallbackApi callback) {
        mCallback = callback;
        if (mApi == null) {
            return;
        }

        try {
            mApi.setCallback(mPackageName, callback);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    protected ServiceConnection apiConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName className, IBinder service) {
            mRobotBinderPool = IRobotBinderPool.Stub.asInterface(service);
            try {
                mApi = ISystemApi.Stub.asInterface(
                        mRobotBinderPool.queryBinder(Definition.BIND_SYSTEM, null));
                notifyEventApiConnected();

                if (mCallback != null) {
                    setCallback(mCallback);
                }
            } catch (RemoteException | NullPointerException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName className) {
            mIsServiceConnected = false;
            notifyEventApiDisconnected();
        }
    };

    public void disconnect(Context context) {
        try {
            removeAllApiEventListeners();
            context.unbindService(apiConnection);
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
        }
    }

    public boolean finishModuleParser(int reqId, boolean ifParsedSucc) {
        return finishModuleParser(reqId, ifParsedSucc, null);
    }

    public boolean finishModuleParser(int reqId, boolean ifParsedSucc, String response) {
        try {
            return mApi.finishModuleWithResponse(reqId, ifParsedSucc, response);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return false;
    }

    public String registerStatusListener(String type, StatusListener listener) {
        try {
            if (listener == null) {
                return null;
            }
            StatusDispatcher statusListener = mMessageDispatcher.obtainStatusDispatcher(listener);

            String id = mApi.registerStatusListener(type, statusListener);
            statusListener.register(id);
            return id;
        } catch (RemoteException e) {
            e.printStackTrace();
            return null;
        }
    }

    public boolean unregisterStatusListener(StatusListener listener) {
        try {
            if (listener == null) {
                return false;
            }
            boolean result = mApi.unregisterStatusListener(listener.getId());
            mMessageDispatcher.unregisterStatusDispatcher(listener.getId());
            return result;
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }

    public boolean startStatusSocket(String type, int socketPort) {
        try {
            return mApi.startStatusSocket(type, socketPort);
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }

    public boolean closeStatusSocket(String type, int socketPort) {
        try {
            return mApi.closeStatusSocket(type, socketPort);
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }

    public void sendStatusReport(String type, String data) {
        try {
            mApi.sendStatusReport(type, data);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * start focus follow
     *
     * @param reqId
     * @param personId    focus follow person id
     * @param lostTimer   How long do you think it was lost
     * @param maxDistance How far away is no longer tracking
     * @return
     */
    public int startFocusFollow(int reqId, int personId, long lostTimer,
                                float maxDistance, ActionListener listener) {
        FocusFollowBean bean = new FocusFollowBean();
        bean.setReqId(reqId);
        bean.setPersonId(personId);
        bean.setLostTimer(lostTimer);
        bean.setMaxDistance(maxDistance);
        String jsonStr = mGson.toJson(bean);
        return startAction(reqId, Definition.ACTION_FOCUS_FOLLOW, jsonStr, listener);
    }

    /**
     * stop focus follow
     *
     * @param reqId
     * @return
     */
    public int stopFocusFollow(int reqId) {
        try {
            return mApi.stopAction(reqId, Definition.ACTION_FOCUS_FOLLOW, false);
        } catch (RemoteException e) {
            e.printStackTrace();
            return ERROR_REMOTE_VISITOR;
        }
    }

    public int stopFocusFollow(int reqId, boolean isResetHW) {
        try {
            return mApi.stopAction(reqId, Definition.ACTION_FOCUS_FOLLOW, isResetHW);
        } catch (RemoteException e) {
            e.printStackTrace();
            return ERROR_REMOTE_VISITOR;
        }
    }


    @Deprecated
    public int startInspection(int reqId, long time, boolean isReInspection, ActionListener listener) {
        String result = getInspectResult();
        if (!isReInspection && result != null) {
            try {
                listener.onResult(Definition.ACTION_RESPONSE_SUCCESS, result);
                listener.onResult(Definition.ACTION_RESPONSE_SUCCESS, result, "");
            } catch (RemoteException e) {
                e.printStackTrace();
            }
            return 0;
        }

        return startInspection(reqId, time, listener);
    }

    public int startInspection(int reqId, long time, ActionListener listener) {
        InspectActionBean bean = new InspectActionBean();
        bean.setReqId(reqId);
        bean.setTimeOut(time);
        String jsonStr = mGson.toJson(bean);
        return startAction(reqId, Definition.ACTION_INSPECTION, jsonStr, listener);
    }


    public int stopInspection(int reqId, boolean isResetHW) {
        return stopAction(reqId, Definition.ACTION_INSPECTION, isResetHW);
    }

    @Deprecated
    public int setLambColor(int reqId, int target, int color) {
        JSONObject params = new JSONObject();
        try {
            params.put(Definition.JSON_LAMP_TARGET, target);
            params.put(Definition.JSON_LAMP_COLOR_RGB_VALUE, color);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        CommandBean bean = new CommandBean(Definition.CMD_CAN_LAMP_COLOR, params.toString(), false);
        return startAction(reqId, Definition.ACTION_LAMP_COLOR, mGson.toJson(bean), null);
    }

    @Deprecated
    public int setLambAnimation(int reqId, int target, int start, int end, int startTime, int
            endTime, int repeat, int onTime, int freeze) {
        JSONObject params = new JSONObject();
        try {
            params.put(Definition.JSON_LAMP_TARGET, target);
            params.put(Definition.JSON_LAMP_RGB_START, start);
            params.put(Definition.JSON_LAMP_RGB_END, end);
            params.put(Definition.JSON_LAMP_START_TIME, startTime);
            params.put(Definition.JSON_LAMP_END_TIME, endTime);
            params.put(Definition.JSON_LAMP_REPEAT, repeat);
            params.put(Definition.JSON_LAMP_ON_TIME, onTime);
            params.put(Definition.JSON_LAMP_RGB_FREEZE, freeze);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        CommandBean bean = new CommandBean(Definition.CMD_CAN_LAMP_ANIM, params.toString(), false);
        return startAction(reqId, Definition.ACTION_LAMP_ANIMATION, mGson.toJson(bean), null);
    }

    /**
     * @deprecated
     */
    @Deprecated
    public int setLight(int reqId, int type, int target, int start, int end, int startTime, int
            endTime, int repeat, int onTime, int freeze) {
        JSONObject params = new JSONObject();
        try {
            params.put(Definition.JSON_LAMP_TYPE, type);
            params.put(Definition.JSON_LAMP_TARGET, target);
            params.put(Definition.JSON_LAMP_RGB_START, start);
            params.put(Definition.JSON_LAMP_RGB_END, end);
            params.put(Definition.JSON_LAMP_START_TIME, startTime);
            params.put(Definition.JSON_LAMP_END_TIME, endTime);
            params.put(Definition.JSON_LAMP_REPEAT, repeat);
            params.put(Definition.JSON_LAMP_ON_TIME, onTime);
            params.put(Definition.JSON_LAMP_RGB_FREEZE, freeze);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        CommandBean bean = new CommandBean(Definition.CMD_CAN_LAMP_ANIM, params.toString(), false);
        return startAction(reqId, Definition.ACTION_LAMP_ANIMATION, mGson.toJson(bean), null);
    }

    public int getChargeStatus(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_CHARGE_STATUS, null, false);
        return startAction(reqId, Definition.ACTION_AUTO_CHARGE_STATUS, mGson.toJson(bean), listener);
    }

    public int startAutoChargeAction(int reqId, long timeout, ActionListener listener) {
        return this.startAutoChargeAction(reqId, timeout, 0L, listener);
    }

    public int startAutoChargeAction(int reqId, long timeout, long multiWaitTimeout, ActionListener listener) {        AutoChargeBean bean = new AutoChargeBean();
        bean.setReqId(reqId);
        bean.setTimeout(timeout);
        bean.setMultipleWaitTime(multiWaitTimeout);
        String toJson = mGson.toJson(bean);
        return startAction(reqId, Definition.ACTION_AUTO_NAVI_CHARGE, toJson, listener);
    }

    public int stopAutoChargeAction(int reqId, boolean isResetHW) {
        return stopAction(reqId, Definition.ACTION_AUTO_NAVI_CHARGE, isResetHW);
    }

    public int setStartChargePoseAction(int reqId, long timeout, ActionListener listener) {
        AutoChargeBean bean = new AutoChargeBean();
        bean.setReqId(reqId);
        bean.setTimeout(timeout);
        String toJson = mGson.toJson(bean);
        return startAction(reqId, Definition.ACTION_SET_START_CHARGE_POSE, toJson, listener);
    }

    /**
     * set startChargePose
     *
     * @param reqId
     * @param listener
     * @param chargeMode 判断回充类型
     * @param timeout    timeout of this action
     * @return
     */
    public int setStartChargePoseAction(int reqId, long timeout, int chargeMode, String setStartChargePoseType, String language, ActionListener listener) {
        AutoChargeBean bean = new AutoChargeBean();
        bean.setReqId(reqId);
        bean.setTimeout(timeout);
        bean.setChargeMode(chargeMode);
        bean.setMapLanguage(language);
        bean.setSetStartChargePoseType(setStartChargePoseType);
        String toJson = mGson.toJson(bean);
        return startAction(reqId, Definition.ACTION_SET_START_CHARGE_POSE, toJson, listener);
    }

    /**
     * set startChargePose
     * @param reqId 请求码
     * @param timeout    timeout of this action
     * @param chargeMode 判断回充类型
     * @param placeName 地点名称
     * @param typeId 地点类型
     * @param priority 地点优先级
     * @param listener 返回结果监听
     * @return int
     */
    public int setStartChargePoseAction(int reqId, long timeout, int chargeMode, String setStartChargePoseType, String placeName, int typeId, int priority, String language, ActionListener listener) {
        AutoChargeBean bean = new AutoChargeBean();
        bean.setReqId(reqId);
        bean.setTimeout(timeout);
        bean.setChargeMode(chargeMode);
        bean.setMapLanguage(language);
        bean.setSetStartChargePoseType(setStartChargePoseType);
        bean.setPlaceName(placeName);
        bean.setTypeId(typeId);
        bean.setPriority(priority);
        String toJson = mGson.toJson(bean);
        return startAction(reqId, Definition.ACTION_SET_START_CHARGE_POSE, toJson, listener);
    }

    public int getHeadVersion(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_GET_VERSION, null, false);
        return startAction(reqId, Definition.ACTION_HEAD_GET_VERSION, mGson.toJson(bean),
                listener);
    }

    public int getNavigationVersion(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_VERSION, null, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_VERSION,
                mGson.toJson(bean), listener);
    }

    public int getNavigationUpdateParams(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_UPDATE_PARAMS, null, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_UPDATE_PARAMS, mGson.toJson(bean),
                listener);
    }

    public int getNavigationSerialNumber(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_SERIAL_NUMBER, null, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_SERIAL_NUMBER, mGson.toJson(bean),
                listener);
    }

    public int setNavigationPatrolList(int reqId, String params, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_PATROL_LIST, params, false);
        return startAction(reqId, Definition.ACTION_NAVI_SET_PATROL_LIST, mGson.toJson(bean),
                listener);
    }

    public int getNavigationPatrolList(int reqId, String params, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_PATROL_LIST, params, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_PATROL_LIST, mGson.toJson(bean),
                listener);
    }

    public int getHeadUpdateParams(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_GET_UPDATE_PARAMS, null, false);
        return startAction(reqId, Definition.ACTION_HEAD_GET_UPDATE_PARAMS, mGson.toJson(bean),
                listener);
    }

    public int getHeadSerialNumber(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_GET_SERIAL_NUMBER, null, false);
        return startAction(reqId, Definition.ACTION_HEAD_GET_SERIAL_NUMBER, mGson.toJson(bean),
                listener);
    }

    public int getCanMotorHorizontalVersion(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_MOTOR_H_VERSION, null, false);
        return startAction(reqId, Definition.ACTION_CAN_GET_MOTOR_H_VERSION, mGson.toJson(bean),
                listener);
    }

    public int getCanMotorVerticalVersion(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_MOTOR_V_VERSION, null, false);
        return startAction(reqId, Definition.ACTION_CAN_GET_MOTOR_V_VERSION, mGson.toJson(bean),
                listener);
    }

    public int getCanPsbVersion(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_PSB_VERSION, null, false);
        return startAction(reqId, Definition.ACTION_CAN_GET_PSB_VERSION, mGson.toJson(bean), listener);
    }

    public int getCanAutoChargeVersion(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_AUTO_CHARGE_VERSION, null, false);
        return startAction(reqId, Definition.ACTION_CAN_GET_AUTO_CHARGE_VERSION,
                mGson.toJson(bean), listener);
    }

    public int getCanBatteryVersion(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_BATTERY_VERSION, null, false);
        return startAction(reqId, Definition.ACTION_CAN_GET_BATTERY_VERSION, mGson.toJson(bean),
                listener);
    }

    /**
     * Start get infrared info from can service. Receive info in onStatusUpdate callback method.
     */
    public int getCanInfraredInfo(int reqId, ActionListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_INFRARED_INFO, null, false);
        return startAction(reqId, Definition.ACTION_CAN_GET_INFRARED_INFO, mGson.toJson(bean),
                listener);
    }

    /**
     * Stop get infrared info from can service.
     */
    public int stopGetCanInfraredInfo(int reqId) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_STOP_SEND_INFRARED_INFO, null, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), null);
    }

    /**
     * Start location charge for remote location function.
     *
     * @param reqId
     * @param mode     0-close 1-open
     * @param listener
     * @return
     */
    public int startLocationCharge(int reqId, int mode, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_REMOTE_ENABLE, mode);

            CommandBean bean = new CommandBean(Definition.CMD_CAN_START_CHARGE_WITHOUT_ESTIMATE, param.toString(), false);
            return startAction(reqId, Definition.ACTION_CAN_START_CHARGE_WITHOUT_ESTIMATE, mGson.toJson(bean),
                    listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    /**
     * CMD_SET_POWER_MOTOR
     *
     * @param reqId
     * @param mode     0-close 1-open
     * @param listener
     * @return
     */
    public int setPowerMotor(int reqId, int mode, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_REMOTE_ENABLE, mode);

            CommandBean bean = new CommandBean(Definition.CMD_SET_POWER_MOTOR, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                    listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * Open or close head vertical max limit angle.
     * Enable head vertical angle down to 87 degrees.
     * Normally the max head angle is 80 degrees.
     *
     * @param reqId
     * @param mode     0-close 1-open
     * @param listener
     * @return
     */
    public int setVerticalMaxLimitAngle(int reqId, int mode, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put("motor", 5); //5-vertical 4-horizontal
            param.put("enable", mode);

            CommandBean bean = new CommandBean(Definition.CMD_CAN_SET_VERTICAL_MAX_LIMIT_ANGLE, param.toString(), false);
            return startAction(reqId, Definition.ACTION_CAN_SET_VERTICAL_MAX_LIMIT_ANGLE, mGson.toJson(bean),
                    listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int canRobotReboot(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_ROBOT_REBOOT, null, false);
        return startAction(reqId, Definition.ACTION_CAN_ROBOT_REBOOT, mGson.toJson(bean), listener);
    }

    public int stopSetChargePileAction(int reqId) {
        return stopAction(reqId, Definition.ACTION_SET_START_CHARGE_POSE, true);
    }

    public int resetHead(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_RESET_HEAD, "", false);
        return startAction(reqId, Definition.ACTION_HEAD_RESET_HEAD, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int moveHead(int reqId, String hmode, String vmode, int hangle, int vangle,
                        CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_HMODE, hmode);
            param.put(Definition.JSON_HEAD_VMODE, vmode);
            param.put(Definition.JSON_HEAD_HORIZONTAL, hangle);
            param.put(Definition.JSON_HEAD_VERTICAL, vangle);

            CommandBean bean = new CommandBean(Definition.CMD_HEAD_MOVE_HEAD, param.toString(),
                    false);
            return startAction(reqId, Definition.ACTION_HEAD_MOVE_HEAD, mGson.toJson(bean),
                    listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int moveHead(int reqId, String hmode, String vmode,
                        int hangle, int vangle, int hMaxSpeed, int vMaxSpeed, CommandListener
                                listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_HSPEED, hMaxSpeed);
            param.put(Definition.JSON_HEAD_VSPEED, vMaxSpeed);
            param.put(Definition.JSON_HEAD_HMODE, hmode);
            param.put(Definition.JSON_HEAD_VMODE, vmode);
            param.put(Definition.JSON_HEAD_HORIZONTAL, hangle);
            param.put(Definition.JSON_HEAD_VERTICAL, vangle);

            CommandBean bean = new CommandBean(Definition.CMD_HEAD_MOVE_HEAD, param.toString(),
                    false);
            return startAction(reqId, Definition.ACTION_HEAD_MOVE_HEAD, mGson.toJson(bean),
                    parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int isRobotEstimate(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_IS_ESTIMATE, "", false);
        return startAction(reqId, Definition.ACTION_NAVI_IS_ESTIMATE, mGson.toJson(bean), listener);
    }

    public int isRobotHasVision(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_IS_HAS_VISIION, "", false);
        return startAction(reqId, Definition.ACTION_NAVI_IS_HAS_VISION, mGson.toJson(bean), listener);
    }

    public int setNavigationConfig(int reqId, String param, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_CONFIG, param, false);
        return startAction(reqId, Definition.ACTION_NAVI_SET_CONFIG, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int getNavigationConfig(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_CONFIG, "", false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_CONFIG, mGson.toJson(bean), listener);
    }

    public int getPlace(int reqId, String param, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_PLACE_NAME, param, false);
        return startAction(reqId,
                Definition.ACTION_NAVI_GET_PLACE_NAME, mGson.toJson(bean), listener);
    }

    public int getInternationalPlaceList(int reqId, CommandListener listener) {
        return getInternationalPlaceList(reqId, null, listener);
    }

    public int getInternationalPlaceList(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_INTERNATIONAL_PLACE_LIST, mapName, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_INTERNATIONAL_PLACE_LIST, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int getInternationalPlaceListForReport(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_INTERNATIONAL_PLACE_LIST_FOR_REPORT, mapName, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_INTERNATIONAL_PLACE_LIST_FOR_REPORT, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int getPlacesByType(int reqId, int typeId, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_TYPE_ID, typeId);

            CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_PLACELIST_BY_TYPE, param.toString(),
                    false);
            return startAction(reqId, Definition.ACTION_NAVI_GET_PLACELIST_BY_TYPE, mGson.toJson(bean),
                    parseCommand(listener));
        } catch (JSONException e) {
            Log.d(TAG, "getPlacesByType: " + e.getMessage());
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int updatePlaceList(int reqId, List<PlaceBean> placeList, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_UPDATE_PLACE_LIST,
                ZipUtils.zipMapData(Definition.CMD_NAVI_UPDATE_PLACE_LIST,
                        mGson, mGson.toJson(placeList)), false);
        return startAction(reqId, Definition.ACTION_NAVI_UPDATE_PLACE_LIST, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int getLocation(int reqId, String param, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_LOCATION_HIGH_FREQUENCY, param, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_LOCATION, mGson.toJson(bean), listener);
    }

    public int getLocation(int reqId, int typeId, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_TYPE_ID, typeId);
            param.put(Definition.JSON_NAVI_PRIORITY, Definition.SPECIAL_PLACE_HIGH_PRIORITY);

            CommandBean bean = new CommandBean();
            bean.setCmdType(Definition.CMD_NAVI_GET_LOCATION_HIGH_FREQUENCY);
            bean.setParams(param.toString());
            bean.setContinue(false);

            return startAction(reqId, Definition.ACTION_NAVI_GET_LOCATION, mGson.toJson(bean), listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    public int goForward(int reqId, float speed, CommandListener listener) {
        return goForward(reqId, speed, Float.MAX_VALUE, listener);
    }

    public int goForward(int reqId, float speed, float distance, CommandListener listener) {
        return goForward(reqId, speed, distance, false, listener);
    }

    public int goForward(int reqId, float speed, float distance, boolean avoid,
                         CommandListener listener) {
        return motionLine(reqId, Definition.CMD_NAVI_MOVE_SUB_FORWARD, speed, distance, avoid,
                listener);
    }

    public int motionLine(int reqId, String direction, float speed, float distance,
                          final CommandListener listener) {
        return motionLine(reqId, direction, speed, distance, false, listener);
    }

    public int motionLine(int reqId, String direction, float speed, float distance,
                          boolean forwardAvoid,
                          final CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_DERICTION, direction);
            param.put(Definition.JSON_NAVI_DISTANCE, distance);
            param.put(Definition.JSON_NAVI_LINEAR_SPEED, speed);
            param.put(Definition.JSON_NAVI_FORWARD_AVOID, forwardAvoid);

            CommandBean bean = new CommandBean();
            bean.setContinue(false);
            bean.setParams(param.toString());
            bean.setCmdType(Definition.CMD_NAVI_MOVE_DIRECTION);

            String params = mGson.toJson(bean);

            return startAction(reqId, Definition.ACTION_NAVI_MOVE_DIRECTION, params, listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    public int stopMove(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_STOP_MOVE, "", false);
        return startAction(reqId, Definition.ACTION_NAVI_STOP_MOVE, mGson.toJson(bean), listener);
    }

    public int getEmergencyStatus(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_EMERGENCY_STATUS, null, false);
        return startAction(reqId, Definition.ACTION_EMERGENCY_STATUS, mGson.toJson(bean), listener);
    }

    public int startCreatingMap(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_START_CREATING_MAP, "", false);
        return startAction(reqId, Definition.ACTION_NAVI_START_CREATING_MAP, mGson.toJson(bean),
                listener);
    }

    public int stopCreatingMap(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_STOP_CREATING_MAP, mapName, false);
        return startAction(reqId, Definition.ACTION_NAVI_STOP_CREATING_MAP, mGson.toJson(bean),
                listener);
    }

    /**
     * 切换地图
     *
     * @param reqId
     * @param mapName  地图名称
     * @param listener 回调函数
     * @return
     */
    public int switchMap(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SWITCH_MAP, mapName, false);
        return startAction(reqId, Definition.ACTION_NAVI_SWITCH_MAP, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 重新加载地图
     */
    public int loadCurrentMap(int reqId, CommandListener listener) {
        return loadCurrentMap(reqId, false, false, listener);
    }

    /**
     * 重新加载地图
     *
     * @param keepPose 重新load后，是否信任之前点
     */
    public int loadCurrentMap(int reqId, boolean keepPose, CommandListener listener) {
        return loadCurrentMap(reqId, true, keepPose, listener);
    }

    private int loadCurrentMap(int reqId, boolean useCustomKeepPose, boolean keepPose, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_LOAD_CURRENT_MAP,
                mGson.toJson(new LoadMapBean(useCustomKeepPose, keepPose)), false);
        return startAction(reqId, Definition.ACTION_NAVI_LOAD_CURRENT_MAP,
                mGson.toJson(bean), listener);
    }

    public int getMapName(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_MAP_NAME, null, false);
        return startAction(reqId, Definition.ACTION_COMMON,
                mGson.toJson(bean), listener);
    }

    public int getPlaceListByMapNameMini(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_PLACELIST_BY_MAPNAME_MINI, mapName, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int setMapInfo(int reqId, String param, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_MAP_INFO, param, false);
        return startAction(reqId, Definition.ACTION_NAVI_SET_MAP_INFO, mGson.toJson(bean),
                listener);
    }

    public int locateVision(int reqId, int retryCount, CommandListener listener) {
        ResetEstimateParams params = new ResetEstimateParams();
        params.setReqId(reqId);
        params.setRetryCount(retryCount);
        return startAction(reqId, Definition.ACTION_LOCATE_VISION,
                mGson.toJson(params), listener);
    }

    public int robotStandby(int reqId, CommandListener listener) {
        RobotStandbyBean bean = new RobotStandbyBean();
        return robotStandby(reqId, bean, listener);
    }

    public int robotStandby(int reqId, RobotStandbyBean bean, CommandListener listener) {
        bean.setReqId(reqId);
        return startAction(reqId, Definition.ACTION_ROBOT_STANDBY, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int robotStandbyEnd(int reqId) {
        return stopAction(reqId, Definition.ACTION_ROBOT_STANDBY, false);
    }

    public int getErrorLog(int reqId, String filePath, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_ERROR_LOG, filePath, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_ERROR_LOG,
                mGson.toJson(bean), parseCommand(listener));
    }

    public int packLogFile(int reqId, String param, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_PACK_LOG_FILE, param, false);
        return startAction(reqId, Definition.ACTION_COMMON,
                mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * 针对TK1底盘获取日志方式
     */
    public int getLogFile(int reqId, String savePath, CommandListener listener) {
        return getChassisFile(reqId, -1, -1, "", savePath, listener);
    }

    /**
     * 针对845系统底盘日志获取系统，无需将打包和下载分开处理。
     *
     * @param reqId
     * @param startTime 获取日志的起始时间
     * @param endTime   获取日志的终止时间
     * @param type      获取日志的类型
     * @param savePath  文件保存路径
     * @param listener
     * @return
     */
    public int getChassisFile(int reqId, long startTime, long endTime, String type, String savePath, CommandListener listener) {
        JSONObject obj = new JSONObject();
        try {
            obj.put(Definition.JSON_START_TIME, startTime);
            obj.put(Definition.JSON_END_TIME, endTime);
            obj.put(Definition.JSON_CMD_TYPE, type);
            obj.put(Definition.JSON_SAVE_PATH, savePath);

            CommandBean bean = new CommandBean();
            bean.setContinue(false);
            bean.setParams(obj.toString());
            bean.setCmdType(Definition.CMD_NAVI_GET_LOG_FILE);

            String params = mGson.toJson(bean);
            return startAction(reqId, Definition.ACTION_COMMON,
                    params, parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    public int setPoseEstimate(int reqId, String param, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_POSE_ESTIMATE, param, false);
        return startAction(reqId, Definition.ACTION_NAVI_SET_POSE_ESTIMATE, mGson.toJson(bean),
                listener);
    }

    public int setFixedEstimate(int reqId, String param, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_FIXED_ESTIMATE, param, false);
        return startAction(reqId, Definition.ACTION_NAVI_SET_FIXED_ESTIMATE, mGson.toJson(bean),
                listener);
    }

    public int setForceEstimate(int reqId, String param, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_FORCE_ESTIMATE, param, false);
        return startAction(reqId, Definition.ACTION_NAVI_SET_FORCE_ESTIMATE, mGson.toJson(bean),
                listener);
    }

    public int getBatteryTimeRemaining(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_BATTERY_TIME_REMAIN, null, false);
        return startAction(reqId, Definition.ACTION_CAN_BATTERY_TIME_REMAIN,
                mGson.toJson(bean), listener);
    }

    public int getChargeTimeRemaining(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_CHARGE_TIME_REMAIN, null, false);
        return startAction(reqId, Definition.ACTION_CAN_CHARGE_TIME_REMAIN,
                mGson.toJson(bean), listener);
    }

    /* Removing when optimizing remote control */

    public int remoteRequestQrcode(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_QRCODE, null, false);
        return startAction(reqId, Definition.ACTION_REMOTE_QRCODE,
                mGson.toJson(bean), listener);
    }

    public int getFullCheckStatus(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_FULL_CHECK_STATUS, "", false);
        return startAction(reqId, Definition.ACTION_GET_FULL_CHECK_STATUS, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int postSetPlaceToServer(int reqId, String params) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_POST_SET_PLACE, params, false);
        return startAction(reqId, Definition.ACTION_REMOTE_POST_SET_PLACE,
                mGson.toJson(bean), null);
    }

    public int remotePostPrepared(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_POST_PREPARED, null, false);
        return startAction(reqId, Definition.ACTION_REMOTE_POST_PREPARED,
                mGson.toJson(bean), null);
    }

    public int remotePostEmergencyMsg(int reqId, String status) {
        JsonObject obj = new JsonObject();
        obj.addProperty("emergency", status);
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_POST_EMERGENCY_MSG, obj.toString
                (), false);
        return startAction(reqId, Definition.ACTION_REMOTE_POST_REMOTE_MESSAGE,
                mGson.toJson(bean), null);
    }

    public int uploadSwitchInfo(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_SWITCH_MAP_INFO
                , mapName, false);
        return startAction(reqId, Definition.ACTION_REMOTE_SWITCH_MAP_INFO, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int remoteFirstCharge(int reqId,
                                 String status,
                                 boolean result,
                                 CommandListener listener) {
        Log.d("FC_core", "remote first charge status : " + status + " , result : " + result);
        try {
            JsonObject value = new JsonObject();
            value.addProperty("status", status);

            JsonObject obj = new JsonObject();
            obj.addProperty("type", Definition.REQ_FIRST_CHARGING);
            obj.add("value", value);
            obj.addProperty("result", result);

            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_FIRST_CHARGING,
                    mGson.toJson(obj), false);
            Log.d("FC_core", "FCApi remoteFirstCharge　reqId = " + reqId);
            return startAction(reqId, Definition.ACTION_REMOTE_FIRST_CHARGING,
                    mGson.toJson(bean), listener);
        } catch (Throwable e) {
            Log.e("FC_core", "remoteFirstCharge e : " + e.getLocalizedMessage());
            return 0;
        }
    }

    public int remoteBindStatus(int reqId, CommandListener listener) {
        JsonObject obj = new JsonObject();
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_BIND_STATUS, obj.toString(),
                false);
        return startAction(reqId, Definition.ACTION_REMOTE_BIND_STATUS,
                mGson.toJson(bean), listener);
    }

    public int remoteChargePile(int reqId,
                                String status,
                                String msg,
                                boolean result,
                                CommandListener listener) {
        Log.d("FC_core", "remote setChargePile status : "
                + status + ", msg : " + msg + " , result : " + result);
        try {
            JsonObject value = new JsonObject();
            value.addProperty("status", status);
            value.addProperty("name", Definition.START_BACK_CHARGE_POSE);
            if (!TextUtils.isEmpty(msg)) {
                JsonElement msgJson = new JsonParser().parse(msg);
                if (msgJson != null && msgJson.isJsonObject()) {
                    value.add("position", msgJson);
                } else {
                    value.addProperty("msg", msg);
                }
            }

            JsonObject obj = new JsonObject();
            obj.addProperty("type", Definition.REQ_FIRST_SET_CHARGING_PILE);
            obj.add("value", value);
            obj.addProperty("result", result);
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_CHARGE_PILE,
                    mGson.toJson(obj), false);
            Log.d("FC_core", "FCApi remoteChargePile　reqId = " + reqId);
            return startAction(reqId, Definition.ACTION_REMOTE_CHARGE_PILE,
                    mGson.toJson(bean), listener);
        } catch (Throwable e) {
            Log.e("FC_core", "FCApi remoteChargePile e : " + e.getLocalizedMessage());
            return 0;
        }
    }

    public int remoteGetMapId(int reqId, CommandListener listener) {
        JsonObject obj = new JsonObject();
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_GET_MAP_ID, obj.toString(),
                false);
        Log.d("FC_", "RobotApi remoteGetMapId ");
        return startAction(reqId, Definition.ACTION_REMOTE_GET_MAP_ID,
                mGson.toJson(bean), listener);
    }

    public int parseDownloadMapPlaceListToNaviProp(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_PARSE_PLACE_LIST, mapName, false);
        return startAction(reqId, Definition.ACTION_NAVI_PARSE_PLACE_LIST, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int startCharge(int reqId) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_AUTO_CHARGE_START, null, false);
        return startAction(reqId, Definition.ACTION_AUTO_CHARGE_START, mGson.toJson(bean), null);
    }

    public int stopCharge(int reqId) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_AUTO_CHARGE_END, null, false);
        return startAction(reqId, Definition.ACTION_AUTO_CHARGE_END, mGson.toJson(bean), null);
    }

    public int switchChargeMode(int reqId) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SWITCH_AUTO_CHARGE_MODE, null,
                false);
        return startAction(reqId, Definition.ACTION_SWITCH_AUTO_CHARGE_MODE, mGson.toJson(bean),
                null);
    }

    public int switchManualMode(int reqId) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SWITCH_MANUAL_MODE, null,
                false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                null);
    }

    public int switchCamera(int reqId, String mode, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_LOCATION, mode);

            CommandBean bean = new CommandBean(Definition.CMD_HEAD_SWITCH_CAMERA, param.toString
                    (), false);
            return startAction(reqId, Definition.ACTION_HEAD_SWITCH_CAMERA, mGson.toJson(bean),
                    parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    private ActionListener parseCommand(final CommandListener listener) {
        if (listener == null) {
            return null;
        }
        ActionListener al = new ActionListener() {
            @Override
            public void onResult(int status, String responseString) throws RemoteException {
                listener.onResult(status, responseString);
            }

            @Override
            public void onResult(int status, String responseString, String extraData) throws RemoteException {
                listener.onResult(status, responseString, extraData);
            }

            @Override
            public void onStatusUpdate(int status, String data) throws RemoteException {
                listener.onStatusUpdate(status, data);
            }

            @Override
            public void onStatusUpdate(int status, String data, String extraData) throws RemoteException {
                listener.onStatusUpdate(status, data, extraData);
            }
        };
        return al;
    }

    /**
     * 拍照.
     * <p>使用fov摄像头拍照
     *
     * @param reqId
     * @param params
     * @param listener
     * @return
     */
    public int takePicture(int reqId, String params, CommandListener listener){
        return getFovCameraStatus(reqId,-1, 0 ,listener);
    }

    public int getFovCameraStatus(int reqId, int id, int count, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_ID, id);
            param.put(Definition.JSON_HEAD_COUNT, count);

            CommandBean bean = new CommandBean();
            bean.setCmdType(Definition.CMD_HEAD_GET_FOV_STATUS);
            bean.setParams(param.toString());
            bean.setContinue(false);

            return startAction(reqId, Definition.ACTION_HEAD_GET_FOV_STATUS, mGson.toJson(bean), parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    public int getDepthCameraStatus(int reqId, int id, int count, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_ID, id);
            param.put(Definition.JSON_HEAD_COUNT, count);

            CommandBean bean = new CommandBean();
            bean.setCmdType(Definition.CMD_HEAD_GET_DEPTH_STATUS);
            bean.setParams(param.toString());
            bean.setContinue(false);

            return startAction(reqId, Definition.ACTION_HEAD_GET_DEPTH_STATUS, mGson.toJson(bean), parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    public int turnLeft(int reqId, float speed, CommandListener listener) {
        return turnLeft(reqId, speed, Float.MAX_VALUE, listener);
    }

    public int turnLeft(int reqId, float speed, float angle, CommandListener listener) {
        return motionAngle(reqId, Definition.CMD_NAVI_MOVE_SUB_TURN_LEFT, speed, angle, listener);
    }

    public int turnRight(int reqId, float speed, CommandListener listener) {
        return turnRight(reqId, speed, Float.MAX_VALUE, listener);
    }

    public int turnRight(int reqId, float speed, float angle, CommandListener listener) {
        return motionAngle(reqId, Definition.CMD_NAVI_MOVE_SUB_TURN_RIGHT, speed, angle, listener);
    }

    public int goBackward(int reqId, float speed, CommandListener listener) {
        return goBackward(reqId, speed, Float.MAX_VALUE, listener);
    }

    public int goBackward(int reqId, float speed, float distance, CommandListener listener) {
        return motionLine(reqId, Definition.CMD_NAVI_MOVE_SUB_BACKWARD, speed, distance, listener);
    }

    protected int motionAngle(int reqId, String direction, float speed, float angle,
                              CommandListener listener) {
        float distance;
        float radianspeed = (float) Math.toRadians(speed);
        if (angle == Float.MAX_VALUE) {
            distance = Float.MAX_VALUE;
        } else {
            distance = (float) Math.toRadians(angle);
        }
        return motionLine(reqId, direction, radianspeed, distance, listener);
    }

    private int stopAction(int reqId, int actionId, boolean isResetHW) {
        try {
            return mApi.stopAction(reqId, actionId, isResetHW);
        } catch (RemoteException e) {
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    /**
     * start lead
     *
     * @param reqId
     * @param params   lead person name
     * @param listener
     * @return
     */
    public int startLead(int reqId, LeadingParams params, ActionListener listener) {
        if (params == null) {
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
        params.setReqId(reqId);
        String jsonStr = mGson.toJson(params);
        return startAction(reqId, Definition.ACTION_LEAD, jsonStr, listener);
    }

    /**
     * stop lead
     *
     * @param reqId
     * @return
     */
    public int stopLead(int reqId, boolean isResetHW) {
        try {
            return mApi.stopAction(reqId, Definition.ACTION_LEAD, isResetHW);
        } catch (RemoteException e) {
            e.printStackTrace();
            return ERROR_REMOTE_VISITOR;
        }
    }

    /**
     * 监听当前人的信息
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int startGetAllPersonInfo(final int reqId, PersonInfoListener listener) {
        return startGetAllPersonInfo(reqId, listener, false);
    }

    /**
     * 监听当前人的信息
     *
     * @param reqId
     * @param listener
     * @param isNeedInvalidPerson
     * @return
     */
    public int startGetAllPersonInfo(final int reqId, PersonInfoListener listener,
                                     boolean isNeedInvalidPerson) {
        synchronized (mAllPersonInfoListener) {
            if (!mAllPersonInfoListener.contains(listener)) {
                mAllPersonInfoListener.add(listener);
            }
        }
        String params;
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_LOOPER, Definition.JSON_HEAD_CONTINUOUS);
            param.put(Definition.JSON_HEAD_IS_NEED_INVALID_PERSON, isNeedInvalidPerson ?
                    Definition.JSON_HEAD_NEED_INVALID_PERSON :
                    Definition.JSON_HEAD_NOT_NEED_INVALID_PERSON);
            params = param.toString();
        } catch (JSONException e) {
            e.printStackTrace();
            return ERROR_REMOTE_VISITOR;
        }
        CommandBean bean = new CommandBean();
        bean.setCmdType(Definition.CMD_HEAD_GET_ALL_PERSON_INFOS);
        bean.setReqId(reqId);
        bean.setParams(params);
        bean.setContinue(true);
        final String jsonParams = mGson.toJson(bean);
        try {
            final Type type = new TypeToken<List<Person>>() {
            }.getType();

            if (mPersonListener != null) {
                IActionListener actionListener = mMessageDispatcher.obtainActionDispatcher(mPersonListener);
                return mApi.startAction(reqId,
                        Definition.ACTION_HEAD_GET_ALL_PERSON_INFOS, jsonParams, actionListener);
            }

            mPersonListener = new ActionListener() {
                @Override
                public void onResult(int status, String responseString) throws RemoteException {
                    if (status == Definition.ACTION_RESPONSE_ALREADY_RUN) {
                        return;
                    }
                    synchronized (mAllPersonInfoListener) {
                        for (PersonInfoListener p : mAllPersonInfoListener) {
                            p.onResult(status, responseString);
                        }
                    }
                    super.onResult(status, responseString);
                }

                @Override
                public void onError(int errorCode, String errorString) throws RemoteException {
                    if (errorCode == Definition.ERROR_LISTENER_INVALID
                            && mAllPersonInfoListener.size() > 0) {
                        Log.d(TAG, "Get all person restart");
                        IActionListener actionListener = mMessageDispatcher.obtainActionDispatcher(mPersonListener);
                        mApi.startAction(reqId,
                                Definition.ACTION_HEAD_GET_ALL_PERSON_INFOS, jsonParams,
                                actionListener);
                    }
                }

                @Override
                public void onStatusUpdate(int status, String data) throws RemoteException {
                    //Log.d("RobotApi", "Get all person info : " + data);
                    List<Person> result;
                    try {
                        result = mGson.fromJson(data, type);
                    } catch (Exception e) {
                        result = new ArrayList<>();
                    }
                    synchronized (mAllPersonInfoListener) {
                        for (PersonInfoListener p : mAllPersonInfoListener) {
                            p.onData(status, result);
                        }
                    }
                    super.onStatusUpdate(status, data);
                }
            };
            IActionListener actionListener = mMessageDispatcher.obtainActionDispatcher(mPersonListener);
            return mApi.startAction(reqId, Definition.ACTION_HEAD_GET_ALL_PERSON_INFOS,
                    jsonParams, actionListener);
        } catch (RemoteException e) {
            e.printStackTrace();
            return ERROR_REMOTE_VISITOR;
        }
    }

    /**
     * 移除监听当前人信息
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int stopGetAllPersonInfo(int reqId, PersonInfoListener listener) {
        Log.d("RobotApi", "Stop all person info : " + mAllPersonInfoListener.size());
        synchronized (mAllPersonInfoListener) {
            if (mAllPersonInfoListener.contains(listener)) {
                mAllPersonInfoListener.remove(listener);
            }

            if (mAllPersonInfoListener.size() > 0) {
                return mAllPersonInfoListener.size();
            }
        }

        try {
            Log.d("RobotApi", "Stop all person info ###");
            return mApi.stopAction(reqId,
                    Definition.ACTION_HEAD_GET_ALL_PERSON_INFOS, true);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return ERROR_REMOTE_VISITOR;
    }

    private int startAction(int reqId, int actionId, String params, ActionListener listener) {
        try {
            IActionListener actionListener = null;
            if (listener != null) {
                actionListener = mMessageDispatcher.obtainActionDispatcher(listener);
            }
            return mApi.startAction(reqId, actionId, params, actionListener);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public void disableEmergency() {
        try {
            mApi.disableEmergency();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void disableBattery() {
        try {
            mApi.disableBattery();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void disablePushWarning() {
        try {
            mApi.disablePushWarning();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void enablePushWarning() {
        try {
            mApi.enablePushWarning();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }
    public void resetSystemStatus() {
        try {
            mApi.resetSystemStatus();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public boolean startOtaUpgrade(boolean needDownload, boolean isRollback) {
        try {
            return mApi.startOtaUpgrade(needDownload, isRollback);
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }

    public String getOtaUpgradeDescription() {
        try {
            return mApi.getOtaUpgradeDescription();
        } catch (RemoteException e) {
            e.printStackTrace();
            return null;
        }
    }

    public boolean cancelOtaUpgradeDownload() {
        try {
            return mApi.cancelOtaUpgradeDownload();
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }

    public boolean installPatch(Bundle bundle) {
        try {
            return mApi.installPatch(bundle);
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }

    public void onSetChargePileFinished() {
        try {
            mApi.updateSystemStatus(Definition.SYSTEM_SET_CHARGE_PILE, Definition.FINISHED);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void onWeChatSetChargePileFinished() {
        try {
            mApi.updateSystemStatus(Definition.WECHAT_SET_CHARGING_PILE, Definition.FINISHED);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 取消定时关机
     */
    public void onShutdownTimerCanceled() {
        try {
            mApi.updateSystemStatus(Definition.SYSTEM_SHUTDOWN_TIMER, Definition.FINISHED);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void onWeChatFirstRechargingFinished() {
        try {
            mApi.updateSystemStatus(Definition.WECHAT_FIRST_RECHARGING, Definition.FINISHED);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void onStartChargeFinished(String result) {
        try {
            mApi.updateSystemStatus(Definition.SYSTEM_START_CHARGE, result);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void onOtaSuccess() {
        try {
            mApi.updateSystemStatus(Definition.SYSTEM_OTA, Definition.SUCCESS);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void onOtaFinished() {
        try {
            mApi.updateSystemStatus(Definition.SYSTEM_OTA, Definition.FINISHED);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void onInspectionStart() {
        try {
            mApi.updateSystemStatus(Definition.SYSTEM_INSPECTION, Definition.START);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void onInspectionFinished(String inspectionType) {
        try {
            mApi.updateSystemStatus(Definition.SYSTEM_INSPECTION, inspectionType);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void updateStandby(String standbyStatus, String jsonData) {
        try {
            Map<String, Object> robotHashMap = mGson.fromJson(jsonData, new TypeToken<Map<String, Object>>() {
            }.getType());
            if (null == robotHashMap) {
                robotHashMap = new HashMap<>();
            }
            robotHashMap.put(Definition.JSON_STANDBY_STATUS, standbyStatus);
            mApi.updateSystemStatus(Definition.SYSTEM_STANDBY, mGson.toJson(robotHashMap));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void updateTimeWarning(boolean noNotifyAlways) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(Definition.TIME_WARNING_PARAM_NO_NOTIFY_ALWAYS, noNotifyAlways);
            mApi.updateSystemStatus(Definition.SYSTEM_TIME_WARNING, jsonObject.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onRepositionFinished() {
        try {
            mApi.updateSystemStatus(Definition.SYSTEM_REPOSITION, Definition.FINISHED);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void onCalibrationFinished() {
        try {
            mApi.updateSystemStatus(Definition.SYSTEM_CALIBRATION, Definition.FINISHED);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void onPushMapNoSwitchModuleFinish() {
        try {
            mApi.updateSystemStatus(Definition.REMOTE_PUSH_MAP_NO_SWITCH, Definition.FINISHED);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void onPushMapNeedSwitchModuleFinish() {
        try {
            mApi.updateSystemStatus(Definition.REMOTE_PUSH_MAP_NEED_SWITCH, Definition.FINISHED);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void onDormancyFinished() {
        try {
            mApi.updateSystemStatus(Definition.SYSTEM_DORMANCY, Definition.FINISHED);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * Finish reposition state when in remote location estimate.
     */
    public void onRemoteRepositionFinished() {
        try {
            mApi.updateSystemStatus(Definition.SYSTEM_REMOTE_REPOSITION, Definition.FINISHED);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * Finish remote stop charging
     */
    public void onRemoteStopChargingFinished() {
        try {
            mApi.updateSystemStatus(Definition.REQ_REMOTE_STOP_CHARGING, Definition.FINISHED);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void onRemoteBindFinished() {
        try {
            mApi.updateSystemStatus(Definition.SYSTEM_REMOTE_BIND, Definition.FINISHED);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void onRemoteControlFinished() {
        try {
            mApi.updateSystemStatus(Definition.SYSTEM_REMOTE_CONTROL, Definition.FINISHED);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void onBleSystemControlFinished(){
        try {
            mApi.updateSystemStatus(Definition.SYSTEM_BLE_MANUAL_EXIT, Definition.FINISHED);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void onWheelOverDangerFinish(){
        try {
            mApi.updateSystemStatus(Definition.SYSTEM_WHEEL_OVER_DANGER_EXIT, Definition.FINISHED);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void startAppControl(String packageName) {
        try {
            mApi.startAppControl(packageName);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void stopAppControl() {
        try {
            mApi.stopAppControl();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public String getActiveApp() {
        try {
            return mApi.getActiveApp();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return null;
    }

    public boolean updateCurrentStatus() {
        try {
            return mApi.updateCurrentStatus();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 临时方案
     *
     * @param reqId
     * @param listener
     * @return
     */
    @Deprecated
    public int startBackupVision(int reqId, CommandListener listener) {
        Log.d(TAG, "startBackupVision");
        CommandBean bean = new CommandBean(Definition.CMD_BACKUP_START_VISION,
                null, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 临时方案
     *
     * @param reqId
     * @param listener
     * @return
     */
    @Deprecated
    public int stopBackupVision(int reqId, CommandListener listener) {
        Log.d(TAG, "stopBackupVision");
        CommandBean bean = new CommandBean(Definition.CMD_BACKUP_STOP_VISION,
                null, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int requestFirstConfigRobotInfo(int reqId, CommandListener listener) {
        Log.d("FC_", "SystemApi requestFirstConfigRobotInfo ---");
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_FIRST_CONFIG_ROBOT_INFO, Definition.REQ_FIRST_TYPE_ROBOT_INFO, false);
        return startAction(reqId, Definition.ACTION_REMOTE_FIRST_CONFIG_ROBOT_INFO,
                mGson.toJson(bean), listener);
    }

    public int getPosition(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_POSITION, null, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_POSITION,
                mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * Report navigation error to NavigationService for package.
     *
     * @param reqId
     * @param timestamp
     * @param cacheId   SN + "-" + timestamp
     * @param type
     * @param params
     * @param listener
     * @return
     */
    public int naviTimeOutCmdReport(int reqId, long timestamp, String cacheId, String type,
                                    String params, CommandListener listener) {
        Log.i(TAG, Definition.TAG_NAVI_LOG_REPORT + " type : " + type);
        NaviCmdTimeOutBean naviCmd = new NaviCmdTimeOutBean(timestamp, cacheId, reqId, type, params);
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_TIME_OUT_REPORT,
                new Gson().toJson(naviCmd), false);
        return startAction(reqId, Definition.ACTION_NAVI_TIME_OUT_REPORT,
                mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * Delete msg id cached in navigation when upload log success.
     *
     * @param reqId
     * @param cacheId  MSG unique identifier.(SN + timestamp)
     * @param listener
     * @return
     */
    public int naviTimeOutMsgDelete(int reqId, String cacheId, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_TIME_OUT_MSG_ID, cacheId);

            CommandBean bean = new CommandBean(Definition.CMD_NAVI_TIME_OUT_MSG_DELETE, param.toString(), false);
            return startAction(reqId, Definition.ACTION_NAVI_TIME_OUT_MSG_DELETE, mGson.toJson(bean),
                    listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int getShotLog(int reqId, String path, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_SHOT_LOG, path, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_SHOT_LOG, mGson.toJson(bean), listener);
    }

    public int getLogById(int reqId, String id, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_LOG_BY_ID, id, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_LOG_BY_ID, mGson.toJson(bean), listener);
    }

    public int updateLogStatusById(int reqId, String cacheId, int logStatus, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_LOG_CACHE_ID, cacheId);
            param.put(Definition.JSON_NAVI_LOG_CACHE_STATUS, logStatus);

            CommandBean bean = new CommandBean(Definition.CMD_NAVI_UPDATE_LOG_STATUS_BY_ID, param.toString(), false);
            return startAction(reqId, Definition.ACTION_NAVI_UPDATE_LOG_STATUS_BY_ID, mGson.toJson(bean), listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public void onHardwareRecovery() {
        try {
            mApi.updateSystemStatus(Definition.REQ_HW_RECOVERY, Definition.FINISHED);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void onHardwareE70Recovery(){
        try {
            mApi.updateSystemStatus(Definition.REQ_HW_E70_RECOVERY, Definition.FINISHED);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public int updateRadarStatus(int reqId, boolean openRadar, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_OPEN_RADAR, openRadar);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_RADAR_STATUS, param.toString(), false);
            return startAction(reqId, Definition.ACTION_NAVI_SET_RADAR_STATUS, mGson.toJson(bean), listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int queryRadarStatus(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_QUERY_RADAR_STATUS, "", false);
            return startAction(reqId, Definition.ACTION_NAVI_QUERY_RADAR_STATUS, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * Set lighting effects
     *
     * @param reqId    request ID
     * @param params,  including:
     *                 type - Lighting effect type
     *                 start - Start color
     *                 end - End color
     *                 startTime - Start time
     *                 endTime - End time
     *                 repeat - Loop times
     *                 onTime - Duration
     *                 freese - end lighting color
     * @param listener
     * @return the status of set action.
     */
    public int setLight(int reqId, String params, ActionListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_LAMP_ANIM, params.toString(), false);
        return startAction(reqId, Definition.ACTION_LAMP_ANIMATION, mGson.toJson(bean), listener);
    }

    public int remoteNextEvent() {
        JsonObject obj = new JsonObject();
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_NEXT_STATE, obj.toString(),
                false);
        return startAction(0, Definition.ACTION_REMOTE_NEXT_STATE,
                mGson.toJson(bean), null);
    }

    public int remoteRefreshQrcode() {
        JsonObject obj = new JsonObject();
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_REFRESH_QRCODE, obj.toString(),
                false);
        return startAction(0, Definition.ACTION_REMOTE_REFRESH_QRCODE,
                mGson.toJson(bean), null);
    }

    /**
     * get charge state
     *
     * @return String result is string "true" or "false"
     */
    public boolean getChargeStatus() {
        boolean isCharging = false;
        try {
            String result = mApi.getRobotInfo(Definition.SYNC_ACTION_IS_CHARGING, null);
            isCharging = Boolean.parseBoolean(result);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return isCharging;
    }

    /**
     * get battery level
     *
     * @return result is string "0"...."100"
     */
    public int getBatteryLevel() {
        String batteryLevel = "";
        try {
            batteryLevel = mApi.getRobotInfo(Definition.SYNC_ACTION_GET_BATTERY_LEVEL, null);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        if (!TextUtils.isEmpty(batteryLevel)) {
            return Integer.parseInt(batteryLevel);
        } else {
            return 0;
        }
    }

    /**
     * called for stop charging on by 3th app .
     * If it is charging on and level more than 10 , go forward 0.1m and leave ChargePile so as to stop charging.
     *
     * @return int 0 note sent cmd success  ; else fail .
     */
    public int stopChargingByApp() {
        if (ctx != null) {
            ctx.sendBroadcast(new Intent(Definition.ACTION_STOP_CHARGING_BY_APP));
            return 0;
        }
        return ERROR_REMOTE_VISITOR;
    }

    public String getSystemStatus() {
        try {
            return mApi.getSystemStatus();
        } catch (RemoteException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * start to recovery navigation, it will delete log and map data.
     *
     * @param reqId
     * @return
     */
    public int startNavigationRecovery(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_RECOVERY, null, false);
        return startAction(reqId, Definition.ACTION_NAVI_RECOVERY, mGson.toJson(bean), listener);
    }

    public int shutdown(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_ROBOT_SHUTDOWN, null, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
    }

    /**
     * start to recovery head, it will delete log and map data.
     *
     * @param reqId
     * @return
     */
    public int startHeadRecovery(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_RECOVERY, null, false);
        return startAction(reqId, Definition.ACTION_HEAD_RECOVERY, mGson.toJson(bean), listener);
    }

    /**
     * start to unbind robot from server.
     *
     * @param reqId
     * @param userName admin user name
     * @param password admin password
     * @param listener 0 成功，500:用户验证错误，403:用户非管理员， 402:用户验证返回错误，406:密码错误，404:机器人不存在，405:用户不存在 408:解绑失败
     * @return
     */
    public int unBindRobot(int reqId, String userName, String password, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_REMOTE_USER_NAME, userName);
            param.put(Definition.JSON_REMOTE_PASSWORD, password);

            CommandBean bean = new CommandBean();
            bean.setContinue(false);
            bean.setParams(param.toString());
            bean.setCmdType(Definition.CMD_REMOTE_UNBIND_ROBOT);

            String params = mGson.toJson(bean);

            return startAction(reqId, Definition.ACTION_REMOTE_UNBIND_ROBOT, params, listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    public int dormancyStart(int reqId, int dormancyTimeMinute) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_DORMANCY_TIME, dormancyTimeMinute);
            CommandBean bean = new CommandBean(Definition.CMD_CAN_DORMANCY_START, param.toString(), false);
            return startAction(reqId, Definition.ACTION_CAN_DORMANCY_START, mGson.toJson(bean), null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }


    /**
     * get inspect result
     *
     * @return inspection result
     */
    public String getInspectResult() {
        String inspectionResult = null;
        try {
            inspectionResult = mApi.getRobotInfo(Definition.SYNC_ACTION_GET_INSPECTION_RESULT, null);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return inspectionResult;
    }

    public String getBindInfo() {
        String bindInfo = null;
        try {
            bindInfo = mApi.getRobotInfo(Definition.SYNC_ACTION_GET_BIND_INFO, null);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return bindInfo;
    }

    public boolean isNavigationServiceEnable() throws RemoteException {
        return mApi.isExternalServiceEnable(RobotOS.NAVIGATION_SERVICE);
    }

    public boolean isHeadServiceEnable() throws RemoteException {
        return mApi.isExternalServiceEnable(RobotOS.HEAD_SERVICE);
    }

    public String getPipeInfo() {
        String pipeInfo = null;
        try {
            pipeInfo = mApi.getRobotInfo(Definition.SYNC_ACTION_GET_CHANNEL_INFO, null);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return pipeInfo;
    }

    public String getMqttInfo() {
        String pipeInfo = null;
        try {
            pipeInfo = mApi.getRobotInfo(Definition.SYNC_ACTION_GET_MQTT_INFO, null);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return pipeInfo;
    }

    public int remoteRequestQrcodeMini(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_QRCODE_MINI, null, false);
        return startAction(reqId, Definition.ACTION_COMMON,
                mGson.toJson(bean), parseCommand(listener));
    }

    public int remoteBindStatusMini(int reqId, String sid, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_CHECK_BIND_STATUS_MINI, sid,
                false);
        return startAction(reqId, Definition.ACTION_COMMON,
                mGson.toJson(bean), parseCommand(listener));
    }

    public int remoteReportOSType(int reqId, String osType, CommandListener listener) {
        JsonObject obj = new JsonObject();
        obj.addProperty("os_type", osType);
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_REPORT_OS_TYPE, obj.toString(),
                false);
        return startAction(reqId, Definition.ACTION_COMMON,
                mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * 指定名称 Pose 点
     */
    public Pose getSpecialPose(String placeName) {
        Pose pose = null;
        try {
            String result = mApi.getRobotInfo(Definition.SYNC_ACTION_GET_SPECIAL_LOCATION, placeName);
            pose = mGson.fromJson(result, Pose.class);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return pose;
    }

    /**
     * start navigation action with pose
     *
     * @param reqId               request id
     * @param pose                pose object
     * @param coordinateDeviation Coordinate
     * @param time                time out ms
     * @param linearSpeed         speed
     * @param angularSpeed        speed
     * @param listener            listener for result
     * @return
     */
    public int startPoseNavigation(int reqId, Pose pose, double coordinateDeviation,
                                   long time, double linearSpeed, double angularSpeed,
                                   ActionListener listener) {

        return startPoseNavigation(reqId, pose, coordinateDeviation, time, linearSpeed, angularSpeed, 0, listener);
    }

    /**
     * start navigation action with pose
     *
     * @param reqId               request id
     * @param pose                pose object
     * @param coordinateDeviation Coordinate
     * @param time                time out ms
     * @param linearSpeed         speed
     * @param angularSpeed        speed
     * @param listener            listener for result
     * @return
     */
    public int startPoseNavigation(int reqId, Pose pose, double coordinateDeviation,
                                   long time, double linearSpeed, double angularSpeed,
                                   int wheelOverCurrentRetryCount, ActionListener listener) {

        return startPoseNavigation(reqId, pose, coordinateDeviation, time, linearSpeed, angularSpeed, wheelOverCurrentRetryCount, 0, listener);
    }


    /**
     * start navigation action with pose
     *
     * @param reqId               request id
     * @param pose                pose object
     * @param coordinateDeviation Coordinate
     * @param time                time out ms
     * @param linearSpeed         speed
     * @param angularSpeed        speed
     * @param priority            priority
     * @param listener            listener for result
     * @return
     */
    public int startPoseNavigation(int reqId, Pose pose, double coordinateDeviation,
                                   long time, double linearSpeed, double angularSpeed,
                                   int wheelOverCurrentRetryCount, int priority, ActionListener listener) {

        GoPositionBean bean = new GoPositionBean();
        bean.setReqId(reqId);
        bean.setPose(pose);
        bean.setTime(time);
        bean.setCoordinateDeviation(coordinateDeviation);
        bean.setIsAdjustAngle(false);
        bean.setLinearSpeed(linearSpeed);
        bean.setAngularSpeed(angularSpeed);
        bean.setWheelOverCurrentRetryCount(wheelOverCurrentRetryCount);
        bean.setPriority(priority);
        return startAction(reqId, Definition.ACTION_GO_POSITION, mGson.toJson(bean), listener);
    }

    public int stopPoseNavigation(int reqId) {
        return stopAction(reqId, Definition.ACTION_GO_POSITION, true);
    }

    /**
     * 特殊点位开始导航
     *
     * @param typeId              特殊点位类型
     * @param priority            特殊点位优先级
     * @param coordinateDeviation 开始导航前， 距离目标点多远可以认为是在目标点
     * @param destinationRange    目的地为中心，多大范围内可认为已到达
     * @param time                导航中，机器人人被遮挡不动多长时间即自动退出巡逻
     * @param listener            回调函数
     */
    public int startNavigationByType(int reqId, int typeId, int priority, double coordinateDeviation, double destinationRange, long time, ActionListener listener) {
        return startNavigationByType(reqId, typeId, priority, coordinateDeviation, destinationRange, false, time,
                SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED,
                SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED, 0, listener);
    }

    public int startNavigationByType(int reqId, int typeId, int priority, double coordinateDeviation, double destinationRange, double obsDistance, long time, ActionListener listener) {
        return startNavigationByType(reqId, typeId, priority, coordinateDeviation, destinationRange, obsDistance, false, time,
                SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED,
                SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED, 0, 0, 0, 0, 0, 0, 0, listener);
    }

    public int startNavigationByType(int reqId, int typeId, int priority, double coordinateDeviation, double destinationRange,
                                     long time, double linearSpeed, double angularSpeed, ActionListener listener) {
        return startNavigationByType(reqId, typeId, priority, coordinateDeviation, destinationRange, false, time,
                linearSpeed, angularSpeed, listener);
    }

    public int startNavigationByType(int reqId, int typeId, int priority, double coordinateDeviation, double destinationRange, boolean isAdjustAngle,
                                     long time, double linearSpeed, double angularSpeed, ActionListener listener) {

        return startNavigationByType(reqId, typeId, priority, coordinateDeviation, destinationRange, isAdjustAngle, time, linearSpeed,
                angularSpeed, 0, listener);
    }

    public int startNavigationByType(int reqId, int typeId, int priority, double coordinateDeviation, double destinationRange, boolean isAdjustAngle,
                                     long time, double linearSpeed, double angularSpeed,
                                     int wheelOverCurrentRetryCount, ActionListener listener) {
        return startNavigationByType(reqId, typeId, priority, coordinateDeviation, destinationRange, isAdjustAngle, time, linearSpeed,
                angularSpeed, wheelOverCurrentRetryCount, 0, listener);
    }

    public int startNavigationByType(int reqId, int typeId, int priority, double coordinateDeviation, double destinationRange, boolean isAdjustAngle,
                                     long time, double linearSpeed, double angularSpeed,
                                     int wheelOverCurrentRetryCount, int taskPriority, ActionListener listener) {
        return startNavigationByType(reqId, typeId, priority, coordinateDeviation, destinationRange, isAdjustAngle, time, linearSpeed,
                angularSpeed, wheelOverCurrentRetryCount, taskPriority, 0, 0, 0, 0, 0, listener);
    }

    public int startNavigationByType(int reqId, int typeId, int priority, double coordinateDeviation, double destinationRange, boolean isAdjustAngle,
                                     long time, double linearSpeed, double angularSpeed,
                                     int wheelOverCurrentRetryCount, int taskPriority,
                                     double linearAcceleration, double angularAcceleration,
                                     int runStopCmdParam, int startModeLevel, int brakeModeLevel, ActionListener listener) {
        return startNavigationByType(reqId, typeId, priority, coordinateDeviation, destinationRange, 0.0D, isAdjustAngle, time, linearSpeed, angularSpeed,
                wheelOverCurrentRetryCount, taskPriority, linearAcceleration, angularAcceleration, runStopCmdParam, startModeLevel, brakeModeLevel, listener);
    }

    /**
     * 特殊点位开始导航
     *
     * @param typeId                     特殊点位类型
     * @param priority                   特殊点位优先级
     * @param coordinateDeviation        开始导航前， 距离目标点多远可以认为是在目标点
     * @param destinationRange           目的地为中心，多大范围内可认为已到达
     * @param obsDistance                最大避障距离，距离目标的障碍物小于该值时，机器人停止
     * @param time                       机器人人被遮挡不动多长时间即自动退出巡逻
     * @param linearSpeed                机器人运动线速度
     * @param angularSpeed               机器人运动角速度
     * @param isAdjustAngle              是否适应导航结束时朝向的角度。如传false，则归正到点位设置时的角度
     * @param wheelOverCurrentRetryCount 导航过程中轮子堵转尝试次数
     * @param runStopCmdParam            导航过程中如遇多机等待，默认发送停止单次导航命令
     * @param listener                   回调函数
     */
    public int startNavigationByType(int reqId, int typeId, int priority, double coordinateDeviation, double destinationRange, double obsDistance,
                                     boolean isAdjustAngle, long time, double linearSpeed, double angularSpeed,
                                     int wheelOverCurrentRetryCount, int taskPriority,
                                     double linearAcceleration, double angularAcceleration,
                                     int runStopCmdParam, int startModeLevel, int brakeModeLevel, ActionListener listener) {
        GoPositionByTypeBean bean = new GoPositionByTypeBean();
        bean.setReqId(reqId);
        bean.setTypeId(typeId);
        bean.setPriority(priority);
        bean.setTime(time);
        bean.setCoordinateDeviation(coordinateDeviation);
        bean.setDestinationRange(destinationRange);
        bean.setIsAdjustAngle(isAdjustAngle);
        bean.setLinearSpeed(linearSpeed);
        bean.setAngularSpeed(angularSpeed);
        bean.setWheelOverCurrentRetryCount(wheelOverCurrentRetryCount);
        bean.setTaskPriority(taskPriority);
        bean.setLinearAcceleration(linearAcceleration);
        bean.setAngularAcceleration(angularAcceleration);
        bean.setRunStopCmdParam(runStopCmdParam);
        bean.setStartModeLevel(startModeLevel);
        bean.setBrakeModeLevel(brakeModeLevel);
        bean.setObsDistance(obsDistance);
        return startAction(reqId, Definition.ACTION_NAVI_GO_POSITION_BY_TYPE, mGson.toJson(bean), listener);
    }

    /**
     * start navigation
     *
     * @param reqId
     * @param destination navigation destination
     * @param time        How long does it take to fail to think
     * @return
     */
    public int startNavigation(int reqId, String destination, double coordinateDeviation,
                               long time, ActionListener listener) {
        return startNavigation(reqId, destination, coordinateDeviation, time, SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED, SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED, listener);
    }

    /**
     * start navigation
     *
     * @param reqId
     * @param destination navigation destination
     * @param time        How long does it take to fail to think
     * @return
     */
    public int startNavigation(int reqId, String destination, double coordinateDeviation,
                               long time, double linearSpeed, double angularSpeed, ActionListener listener) {
        return startNavigation(reqId, destination, coordinateDeviation, time, linearSpeed, angularSpeed, false, listener);
    }

    /**
     * 导航
     *
     * @param isAdjustAngle 是否适应导航结束时朝向的角度，默认 false:即归正目标点设定角度
     * @return
     */
    public int startNavigation(int reqId, String destination, double coordinateDeviation,
                               long time, double linearSpeed, double angularSpeed,
                               boolean isAdjustAngle, ActionListener listener) {
        return startNavigation(reqId, destination, coordinateDeviation, time,
                linearSpeed, angularSpeed, isAdjustAngle, 0.0D, listener);
    }

    /**
     * 导航
     *
     * @param isAdjustAngle 是否适应导航结束时朝向的角度，默认 false:即归正目标点设定角度
     * @return
     */
    public int startNavigation(int reqId, String destination, double coordinateDeviation,
                               long time, double linearSpeed, double angularSpeed,
                               boolean isAdjustAngle, double destinationRange, ActionListener listener) {
        return startNavigation(reqId, destination, coordinateDeviation, time,
                linearSpeed, angularSpeed, isAdjustAngle, destinationRange, 0, listener);
    }

    /**
     * 导航
     *
     * @param isAdjustAngle 是否适应导航结束时朝向的角度，默认 false:即归正目标点设定角度
     * @return
     */
    public int startNavigation(int reqId, String destination, double coordinateDeviation,
                               long time, double linearSpeed, double angularSpeed,
                               boolean isAdjustAngle, double destinationRange,
                               int wheelOverCurrentRetryCount, ActionListener listener) {
        return startNavigation(reqId, destination, coordinateDeviation, time,
                linearSpeed, angularSpeed, isAdjustAngle, destinationRange,
                wheelOverCurrentRetryCount, 0, listener);
    }

    /**
     * 导航
     *
     * @param isAdjustAngle 是否适应导航结束时朝向的角度，默认 false:即归正目标点设定角度
     * @return
     */
    public int startNavigation(int reqId, String destination, double coordinateDeviation,
                               long time, double linearSpeed, double angularSpeed,
                               boolean isAdjustAngle, double destinationRange, int wheelOverCurrentRetryCount,
                               int priority, ActionListener listener) {
        NavigationBean bean = new NavigationBean();
        bean.setReqId(reqId);
        bean.setDestination(destination);
        bean.setTime(time);
        bean.setCoordinateDeviation(coordinateDeviation);
        bean.setLinearSpeed(linearSpeed);
        bean.setAngularSpeed(angularSpeed);
        bean.setAdjustAngle(isAdjustAngle);
        bean.setDestinationRange(destinationRange);
        bean.setWheelOverCurrentRetryCount(wheelOverCurrentRetryCount);
        bean.setPriority(priority);
        String jsonStr = mGson.toJson(bean);

        return startAction(reqId, Definition.ACTION_NAVIGATION, jsonStr, listener);
    }

    /**
     * stop navigation
     *
     * @param reqId
     * @return
     */
    public int stopNavigation(int reqId) {
        return stopAction(reqId, Definition.ACTION_NAVIGATION, true);
    }

    public Pose getCurrentPose() {
        Pose pose = null;
        try {
            String result = mApi.getRobotInfo(Definition.SYNC_ACTION_GET_CURRENT_LOCATION, null);
            pose = mGson.fromJson(result, Pose.class);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return pose;
    }

    public int getFovCameraInfo(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_GET_FOV_CAMERA_INFO, null, false);
        return startAction(reqId, Definition.ACTION_HEAD_GET_FOV_CAMERA_INFO, mGson.toJson(bean), listener);
    }

    public int savePlaceListToPlaceFile(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SAVE_PLACE_LIST, mapName, false);
        return startAction(reqId, Definition.ACTION_NAVI_SAVE_PLACE_LIST, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int detectFace(int reqId, String img, CommandListener listener) {
        JSONObject params = new JSONObject();
        try {
            params.put(Definition.JSON_IMAGE, img);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_DETECT_FACE, params.toString(), false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
    }

    public int startVision(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_START_VISION,
                null, false);
        return startAction(reqId, Definition.ACTION_HEAD_START_VISION, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int stopVision(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_STOP_VISION,
                null, false);
        return startAction(reqId, Definition.ACTION_HEAD_STOP_VISION, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 获取人脸抠图
     */
    public int getPictureById(int reqId, int id, int count, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_ID, id);
            param.put(Definition.JSON_HEAD_COUNT, count);

            CommandBean bean = new CommandBean(Definition.CMD_HEAD_GET_PICTURE_BY_ID, param
                    .toString(), false);
            return startAction(reqId, Definition.ACTION_HEAD_GET_PICTURE_BY_ID, mGson.toJson
                    (bean), parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 当前地图下所有 Pose 点
     */
    public List<Pose> getPlaceList() {
        List<Pose> list = null;
        try {
            String result = mApi.getRobotInfo(Definition.SYNC_ACTION_GET_ALL_LOCATION, null);
            list = mGson.fromJson(result, new TypeToken<List<Pose>>() {
            }.getType());
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return list;
    }

    /**
     * 开始录视频
     *
     * @param reqId
     * @param path  全路径：文件夹路径+文件名.mp4
     * @return
     */
    public int startVisionRecord(int reqId, String path, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_VISION_RECORD_PATH, path);
            CommandBean bean = new CommandBean(Definition.CMD_VISION_START_VISION_RECORD, param
                    .toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson
                    (bean), parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int stopVisionRecord(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_VISION_STOP_VISION_RECORD,
                null, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 埋点分级，从OrionBase获取当前配置的日志级别
     */
    public int checkMiniTrackLevel(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CHECK_TRACK_LEVEL,
                null, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson
                (bean), parseCommand(listener));
    }

    /**
     * 埋点分级，向OrionBase上报当前机器的埋点级别
     */
    public int reportMiniTrackLevel(int reqId, int callChainTrackLevel, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_EVENT_CALLCHAIN_TRACK_LEVEL, callChainTrackLevel);
            CommandBean bean = new CommandBean(Definition.CMD_REPORT_TRACK_LEVEL, param
                    .toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson
                    (bean), parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int getMapStatus(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_MAP_STATUS, null, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_MAP_STATUS, mGson.toJson(bean), listener);
    }

    public int startRecordFaceData() {
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_RECORD_FACE_START, "", false);
        return startAction(0, Definition.ACTION_HEAD_RECORD_FACE_DATA, mGson.toJson(bean), null);
    }

    public int stopRecordFaceData() {
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_RECORD_FACE_STOP, "", false);
        return startAction(0, Definition.ACTION_HEAD_RECORD_FACE_DATA, mGson.toJson(bean), null);
    }

    public int startExtendMap(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_START_EXTEND_MAP, "", false);
        return startAction(reqId, Definition.ACTION_NAVI_START_EXTEND_MAP, mGson.toJson(bean), parseCommand(listener));
    }

    public int cancelCreateMap(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_CANCEL_CREATE_MAP, "", false);
        return startAction(reqId, Definition.ACTION_NAVI_CANCEL_CREATE_MAP, mGson.toJson(bean), parseCommand(listener));
    }

    public int setChassisRelocation(int reqId, int mode, Pose pose, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            if (pose != null) {
                param.put(Definition.JSON_NAVI_RELOCATION_POSE, pose.toJsonObject());
            }
            param.put(Definition.JSON_NAVI_RELOCATION_TYPE, mode);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_RELOCATION, param
                    .toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson
                    (bean), parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int uploadMapPlaceList(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_UPLOAD_PLACE_LIST
                , mapName, false);
        return startAction(reqId, Definition.ACTION_REMOTE_UPLOAD_PLACE_LIST, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int enableBlueFovLight(int reqId, ActionListener listener) {
        return startAction(reqId, Definition.ACTION_BLUE_FOV_LIGHT, "{}", listener);
    }

    public int enableRedFovLight(int reqId, ActionListener listener) {
        return startAction(reqId, Definition.ACTION_RED_FOV_LIGHT, "{}", listener);
    }

    public int disableBlueFovLight(int reqId) {
        return stopAction(reqId, Definition.ACTION_BLUE_FOV_LIGHT, false);
    }

    public int disableRedFovLight(int reqId) {
        return stopAction(reqId, Definition.ACTION_RED_FOV_LIGHT, false);
    }


    /**
     * @param speed    默认为0.7
     * @param distance 默认为0.2m
     * @return
     */
    public int leaveChargingPile(int reqId, float speed, float distance, CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.JSON_NAVI_DISTANCE, distance + "");
            jsonObject.put("speed", speed + "");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return startAction(reqId, Definition.ACTION_LEAVE_CHARGING_PILE, jsonObject.toString(), listener);
    }

    public int judgeInChargingPile(int reqId, float coordinateDeviation, CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.KEY_COORDINATE_DEVIATION, String.valueOf(coordinateDeviation));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return startAction(reqId, Definition.ACTION_JUDGE_IN_CHARGING_PILE, jsonObject.toString(), listener);
    }

    /**
     * 查询Robot状态
     */
    public void getRobotStatus(String type, StatusListener listener) {
        try {
            mApi.getRobotStatus(type, listener);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public int getMultiFunctionSwitchState(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_MULTI_FUNC_SWITCH_STATE, "", false);
        return startAction(reqId, Definition.ACTION_COMMON,
                mGson.toJson(bean), parseCommand(listener));
    }

    public int removeMap(int reqId, String param, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_REMOVE_MAP, param, false);
        return startAction(reqId, Definition.ACTION_NAVI_REMOVE_MAP, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int getCloudMapPlaceList(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_GET_PLACE_LIST, mapName, false);
        return startAction(reqId, Definition.ACTION_REMOTE_GET_PLACE_LIST, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 获取地图信息，返回结果是个JSONString，目前返回是否有二维码数据
     *
     * @param reqId
     * @param mapName 地图名称
     * @param listener
     * @return
     */
    public int getMapInfo(int reqId, String mapName, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_MAP_INFO, mapName, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_MAP_INFO, mGson.toJson(bean), listener);
    }

    /**
     * 设置地图 uuid
     *
     * @param reqId
     * @param mapName  地图名称
     * @param uuid     云端生成的全网唯一标识
     * @param listener
     * @return
     */
    public int setMapUuid(int reqId, String mapName, String uuid, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_MAP_NAME, mapName);
            param.put(Definition.JSON_MAP_UUID, uuid);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_MAP_UUID, param.toString(), false);
            return startAction(reqId, Definition.ACTION_NAVI_SET_MAP_UUID, mGson.toJson(bean), listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 设置地图上传云端状态
     *
     * @param reqId
     * @param mapName      地图名称
     * @param isMapState   是否是Map状态。否则为Place状态
     * @param needReUpload 是否需要重置状态
     * @param listener
     * @return
     */
    public int setMapSyncState(int reqId, String mapName, boolean isMapState, boolean needReUpload, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_MAP_NAME, mapName);
            param.put(Definition.JSON_MAP_IS_MAP_STATE, isMapState);
            param.put(Definition.JSON_MAP_NEED_RE_UPLOAD, needReUpload);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_MAP_SYNC_STATE, param.toString(), false);
            return startAction(reqId, Definition.ACTION_NAVI_SET_MAP_SYNC_STATE, mGson.toJson(bean), listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public String reportTask(TaskProxy task){
        if(task == null){
            return null;
        }
        try {
            return mApi.reportTask(task.generateTask());
        } catch (RemoteException e) {
            e.printStackTrace();
            return null;
        }
    }

    public void reportTaskEvent(TaskEventProxy taskEvent){
        if(taskEvent == null){
            return;
        }
        try {
            mApi.reportTaskEvent(taskEvent.generateTaskEvent());
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public List<Task> getCurrentTasks(){
        try {
            return mApi.getCurrentTask();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return new ArrayList<>();
    }

    public int setMapUpdateTime(int reqId, String mapName, long updateTime, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put("mapName", mapName);
            param.put("updateTime", updateTime);
            CommandBean bean = new CommandBean("cmd_navi_set_map_update_time", param.toString(), false);
            return this.startAction(reqId, 369, this.mGson.toJson(bean), listener);
        } catch (JSONException var8) {
            var8.printStackTrace();
            return -1;
        }
    }

    /**
     * 设置地图完成状态
     *
     * @param reqId
     * @param mapName     地图名称
     * @param finishState 0-没有地图 1-开始建图成功 2-充电桩设置成功 4-接待点设置成功 7-完成
     * @param isReset 是否需要重置，重置的话，直接覆盖原值，否则需要与原值进行“或（|）”操作
     * @param listener
     * @return
     */
    public int setMapFinishState(int reqId, String mapName, int finishState, boolean isReset, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_MAP_NAME, mapName);
            param.put(Definition.JSON_MAP_FINISH_STATE, finishState);
            param.put(Definition.JSON_MAP_FINISH_STATE_IS_RESET, isReset);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_MAP_FINISH_STATE, param.toString(), false);
            return startAction(reqId, Definition.ACTION_NAVI_SET_MAP_FINISH_STATE, mGson.toJson(bean), listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int startNaviToAutoChargeAction(int reqId, long timeout, ActionListener listener) {
        AutoChargeBean bean = new AutoChargeBean();
        bean.setReqId(reqId);
        bean.setTimeout(timeout);
        bean.setChargeType(1);
        String toJson = this.mGson.toJson(bean);
        return this.startAction(reqId, 21, toJson, listener);
    }

    public int addMapInfo(int reqId, String mapName, String mapMd5, String mapUuid,
                          String updateTime, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_MAP_NAME, mapName);
            param.put(Definition.JSON_MAP_MD5, mapMd5);
            param.put(Definition.JSON_MAP_UUID, mapUuid);
            param.put(Definition.JSON_MAP_UPDATE_TIME, updateTime);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_ADD_MAP_INFO, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int updateChassisCameraEnableState(int reqId, Definition.ChassisCameraType cameraType, boolean enable,
                                              CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_CAMERA_TYPE, cameraType.getValue());
            param.put(Definition.JSON_NAVI_ENABLE_STATE, enable);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_CAMERA_STATE, param
                    .toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int setPowerLpm(int reqId, String params) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_CAN_SET_POWER_LPM, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int setLockEnable(int reqId, int type, int bord, boolean enable) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_DOOR_TYPE, type);
            param.put(Definition.JSON_CAN_BOARD, bord);
            param.put(Definition.JSON_CAN_LOCK_ENABLE, enable);
            CommandBean bean = new CommandBean(Definition.CMD_CAN_SET_LOCK_ENABLE, param.toString(), false);
            return startAction(reqId, Definition.ACTION_CAN_SET_LOCK_ENABLE,
                    mGson.toJson(bean), null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    public int getDoorStatus(int reqId, int type, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_DOOR_TYPE, type);
            CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_DOOR_STATUS, param.toString(), false);
            return startAction(reqId, Definition.ACTION_CAN_GET_DOOR_STATUS,
                    mGson.toJson(bean), parseCommand(listener));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    public int getCanPsbMotorVersion(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_PSB_S_VERSION, null, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int taskCommandReport(int reqId, String cmdId, String cmdType, String execResult,
                                 String execData, CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.JSON_CMD_ID, cmdId);
            jsonObject.put(Definition.JSON_CMD_TYPE, cmdType);
            jsonObject.put(Definition.JSON_TASK_EXEC_RESULT, execResult);
            jsonObject.put(Definition.JSON_TASK_EXEC_DATA, execData);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_COMMAND_EXEC_REPORT,
                jsonObject.toString(), false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), parseCommand(listener));
    }

    /**
     * 获取本地和服务端都支持的系统语言
     *
     * @return
     */
    public String getLocalAndServerSupportLanguageList() {
        String languageList = null;
        try {
            languageList = mApi.getRobotInfo(
                    Definition.SYNC_ACTION_GET_LOCAL_AND_SERVER_LANGUAGE, null);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return languageList;
    }

    /**
     * 获取本地支持的系统语言列表
     *
     * @return
     */
    public String getLocalSupportLanguageList () {
        String languageList = null;
        try {
            languageList = mApi.getRobotInfo(
                    Definition.SYNC_ACTION_GET_LOCAL_SUPPORT_LANGUAGE, null);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return languageList;
    }

    /**
     * 设置服务端支持的系统语言列表
     *
     * @param params
     * @return
     */
    public boolean setServerSupportLanguageList(String params) {
        try {
            return mApi.setServerSupportLanguageList(params);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * RobotInfoManager 同步服务端的主语言
     * @return
     */
    public String checkRemoteLanguage() {
        String remoteLanguageList = null;
        try {
            remoteLanguageList = mApi.getRobotInfo(Definition.SYNC_ACTION_GET_REMOTE_LANGUAGE, null);
            return remoteLanguageList;
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 发送多机消息，默认下发消息为非测试消息
     * test_msg为true表示是测试数据，test_msg为false代表实际发送的lora数据（该情况用于工厂版本使用）
     * 默认情况下lora的数据完全由底盘收发，上层不做处理
     */
    public int sendMultiRobotMessage(int reqId, String params, CommandListener listener){
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_SEND_MULTI_ROBOT_MESSAGE, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        }catch (Exception e){
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 发送Lora消息，默认下发消息为非测试消息,该接口仅针对招财豹工厂测试使用
     * @platform 招财豹工厂多机Lora版本
     */
    public int setMultiRobotTestEnable(int reqId, boolean enable, CommandListener listener){
        try {
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put(Definition.JSON_NAVI_LORA_TEST_ENABLE, enable);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_MULTI_ROBOT_TEST_ENABLE, jsonObject.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        }catch (Exception e){
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 重置LoraConfig状态为默认状态
     * @platform  招财豹Lora工厂分支
     */
    public int resetMultiRobotConfig(int reqId, CommandListener listener){
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_RESET_MULTI_ROBOT_CONFIG, null, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        }catch (Exception e){
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获取多机参数
     */
    public int getMultiRobotSettingConfig(int reqId, CommandListener listener){
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_MULTI_ROBOT_CONFIG, null, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        }catch (Exception e){
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 配置多机参数
     * @param reqId
     * @param params 参数信息参照LoraConfigBean
     * @param listener
     * @return
     */
    public int setMultiRobotSettingConfig(int reqId, String params, CommandListener listener){
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_MULTI_ROBOT_CONFIG, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        }catch (Exception e){
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 解析底盘地图数据
     * 第一个版本是解析road.json为road_graph.data数据
     * dataType如果是 roadData表明解析的是road.json
     */
    public int parseChassisMapData(int reqId, String mapName, String dataType, CommandListener listener){
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(Definition.JSON_MAP_NAME, mapName);
            jsonObject.put(Definition.JSON_DATA_TYPE, dataType);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_PARSE_MAP_DATA, jsonObject.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 修改机器人激活时的名称,目前需求不对RobotApi开放
     *
     * @param robotNewName 机器人的新名称
     * @return
     */
    public int modifyRobotName(int reqId, String robotNewName, CommandListener listener) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(Definition.JSON_REMOTE_ROBOT_NAME, robotNewName);
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_MODIFY_ROBOT_NAME, jsonObject.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int startD430Calibration(int reqId, CommandListener listener) {
        try {
            JSONObject jsonObject = new JSONObject();
            CommandBean bean = new CommandBean(Definition.CMD_D430_CALIBRATION_START, jsonObject.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int stopD430Calibration(int reqId) {
        try {
            JSONObject jsonObject = new JSONObject();
            CommandBean bean = new CommandBean(Definition.CMD_D430_CALIBRATION_STOP, jsonObject.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public void onEnableTargetCustomFinish(String result) {
        try {
            mApi.updateSystemStatus(Definition.SYSTEM_ENABLE_TARGET_CUSTOM, result);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public int getAdditionalDevices(int reqId, CommandListener listener){
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_ADDITIONAL_DEVICES, "", false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 控制消毒机器人模块的供电控制接口
     * @enabled true will charged, false will discharged.
     */
    public int setXDPowerEnable(int reqId, boolean enabled){
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_XD_POWER, enabled);
            CommandBean bean = new CommandBean(Definition.CMD_CAN_SET_XD_POWER, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 消毒豹项目控制消毒模块的风扇
     * @param enable true will open ; false will close
     */
    public int setXDFanEnable(int reqId, boolean enable){
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_XD_FAN, enable);
            CommandBean bean = new CommandBean(Definition.CMD_CAN_SET_XD_FAN_ENABLE, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    /**
     * 消毒豹项目控制消毒模块的雾化强度
     * @param rank rank will be JSON_CAN_XD_RANK_CLOSE or JSON_CAN_XD_RANK_OPEN_SMALL or JSON_CAN_XD_RANK_OPEN_LARGE
     */
    public int setXDRank(int reqId, int rank) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_XD_RANK, rank);
            CommandBean bean = new CommandBean(Definition.CMD_CAN_SET_XD_RANK, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 系统接管触发回充, 功能和RobotSettings 去充电一样.
     */
    public int goCharging(int reqId) {
        //走系统接管充电
        this.ctx.sendBroadcast(new Intent(InternalDef.ACTION_START_CHARGE));
        return Definition.RESULT_OK;
    }

    /**
     * 判断电梯功能是否启用
     */
    public boolean isElevatorControlEnable() {
        int enableValue = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_ELEVATOR_CONTROL_ENABLED);
        Log.d(TAG, "isElevatorControlEnabled : " + enableValue);
        return enableValue == 1;
    }

    /**
     * 获得多层配置信息
     *
     * @return 全部配置信息
     */
    public int queryMultiFloorConfig(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_QUERY_MULTI_FLOOR_CONFIG, null, false);
            return startAction(reqId, Definition.ACTION_NAVI_QUERY_MULTI_FLOOR_CONFIG, mGson.toJson(bean),
                    parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;

    }

    /**
     * 获得多层配置和对应地图里的所有点位
     */
    public int getMultiFloorConfigAndPose(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_MULTI_FLOOR_CONFIG_AND_POSE, null, false);
            return startAction(reqId, Definition.ACTION_NAVI_GET_MULTI_FLOOR_CONFIG_AND_POSE, mGson.toJson(bean), parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获得多层配置和对应地图里的点位
     * （不包含特殊点位）
     */
    public int getMultiFloorConfigAndCommonPose(int reqId, CommandListener listener){
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_MULTI_FLOOR_CONFIG_AND_COMMON_POSE, null, false);
            return startAction(reqId, Definition.ACTION_NAVI_GET_MULTI_FLOOR_CONFIG_AND_COMMON_POSE, mGson.toJson(bean), parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 新增多楼层配置信息
     *
     * @param param List<MultiFloorInfo> 形式，MultiFloorInfo对象可参考NavigationService结构
     *              ** 必须有floorId、mapId
     */
    public int insertMultiFloorConfig(int reqId, String param, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_INSERT_MULTI_FLOOR_CONFIG, param, false);
            return startAction(reqId, Definition.ACTION_NAVI_INSERT_MULTI_FLOOR_CONFIG, mGson.toJson(bean),
                    parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    /**
     * 更新多层配置中，某楼层地图状态类型
     * @param floorId       floorId
     * @param floorState    要修改的状态类型
     */
    public int updateMultiFloorConfigFloorState(int reqId, int floorId, String mapName, int floorState, CommandListener listener) {
        try {
            JSONObject valueObj = new JSONObject();
            valueObj.put("floorId",floorId);
            valueObj.put("mapName",mapName);
            valueObj.put("floorState",floorState);

            JSONObject param = new JSONObject();
            param.put(Definition.JSON_TYPE, Definition.JSON_NAVI_MULTI_FLOOR_UPDATE_FLOOR_STATE);
            param.put(Definition.JSON_VALUE, valueObj.toString());
            return updateMultiFloorConfig(reqId, param.toString(), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    /**
     * 更新多层配置中，楼层映射id，该id用于lora梯控
     *
     * @param floorId    floorId
     * @param mapName
     * @param floorIndex 楼层映射id
     */
    public int updateMultiFloorConfigFloorIndex(int reqId, int floorId, String mapName, int floorIndex, CommandListener listener) {
        try {
            JSONObject valueObj = new JSONObject();
            valueObj.put("floorId", floorId);
            valueObj.put("mapName", mapName);
            valueObj.put("floorIndex", floorIndex);

            JSONObject param = new JSONObject();
            param.put(Definition.JSON_TYPE, Definition.JSON_NAVI_MULTI_FLOOR_UPDATE_FLOOR_INDEX);
            param.put(Definition.JSON_VALUE, valueObj.toString());
            return updateMultiFloorConfig(reqId, param.toString(), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    /**
     * 更新多层配置中，楼层别名
     *
     * @param floorId    floorId
     * @param mapName
     * @param floorAlias 楼层别名
     */
    public int updateMultiFloorConfigFloorAlias(int reqId, int floorId, String mapName, String floorAlias, CommandListener listener) {
        try {
            JSONObject valueObj = new JSONObject();
            JSONObject param = new JSONObject();
            valueObj.put("floorId", floorId);
            valueObj.put("mapName", mapName);
            valueObj.put("floorAlias", floorAlias);

            param.put(Definition.JSON_TYPE, Definition.JSON_NAVI_MULTI_FLOOR_UPDATE_FLOOR_ALIAS);
            param.put(Definition.JSON_VALUE, valueObj.toString());
            return updateMultiFloorConfig(reqId, param.toString(), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    /**
     * 更新多层配置中，该层可用的电梯
     *
     * @param floorId    floorId
     * @param mapName
     * @param availableElevators 可用的电梯
     */
    public int updateMultiFloorConfigElevators(int reqId, int floorId, String mapName, List<String> availableElevators, CommandListener listener) {
        try {
            JSONObject valueObj = new JSONObject();
            JSONObject param = new JSONObject();
            valueObj.put("floorId", floorId);
            valueObj.put("mapName", mapName);
            valueObj.put("availableElevators", availableElevators);

            param.put(Definition.JSON_TYPE, Definition.JSON_NAVI_MULTI_FLOOR_UPDATE_ELEVATORS);
            param.put(Definition.JSON_VALUE, valueObj.toString());
            return updateMultiFloorConfig(reqId, param.toString(), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    /**
     * 更新多层配置中，某楼层地图状态类型
     * @param param       包含更新的type，及数据value
     *                    数据中必须包含floorId、mapId用于查询，其他要更新的字段名称参考NavigationService的MultiFloorInfo
     */
    public int updateMultiFloorConfig(int reqId, String param, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_UPDATE_MULTI_FLOOR_CONFIG, param, false);
            return startAction(reqId, Definition.ACTION_NAVI_UPDATE_MULTI_FLOOR_CONFIG, mGson.toJson(bean), parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    /**
     * 根据id删除某层配置
     *
     * @param floorId    floorId
     * @param mapName    地图名
     */
    public int removeMultiFloorConfigById(int reqId, int floorId, String mapName, CommandListener listener) {
        try {
            JSONObject valueObj = new JSONObject();
            valueObj.put("floorId", floorId);
            valueObj.put("mapName", mapName);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_REMOVE_MULTI_FLOOR_CONFIG, valueObj.toString(), false);
            return startAction(reqId, Definition.ACTION_NAVI_REMOVE_MULTI_FLOOR_CONFIG, mGson.toJson(bean), parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 打开电梯门
     */
    public int openElevatorDoor(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_CONTROL_OPEN_ELEVATOR_DOOR,
                    null, false);
            return startAction(reqId, Definition.ACTION_CONTROL_OPEN_ELEVATOR_DOOR,
                    mGson.toJson(bean), parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 关闭电梯门
     */
    public int closeElevatorDoor(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_CONTROL_CLOSE_ELEVATOR_DOOR,
                    null, false);
            return startAction(reqId, Definition.ACTION_CONTROL_CLOSE_ELEVATOR_DOOR,
                    mGson.toJson(bean), parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 释放电梯
     */
    public int releaseElevator(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_CONTROL_RELEASE_ELEVATOR,
                    null, false);
            return startAction(reqId, Definition.ACTION_CONTROL_RELEASE_ELEVATOR,
                    mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 呼叫电梯到指定楼层
     */
    public int callElevatorToTargetFloor(int reqId, int currentFloor, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CONTROL_TARGET_FLOOR, currentFloor);
            CommandBean bean = new CommandBean(Definition.CMD_CONTROL_CALL_ELEVATOR, param.toString(), false);
            return startAction(reqId, Definition.ACTION_CONTROL_CALL_ELEVATOR,
                    mGson.toJson(bean), parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获取电梯状态
     */
    public int getElevatorStatus(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_CONTROL_GET_ELEVATOR_STATUS,
                    null, false);
            return startAction(reqId, Definition.ACTION_CONTROL_GET_ELEVATOR_STATUS,
                    mGson.toJson(bean), parseCommand(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }
    /**
     * 乘梯导航
     */
    public int startElevatorNavigation(int reqId, String destination, int floor,
                                       ActionListener listener) {
        NavigationAdvancedBean bean = new NavigationAdvancedBean();
        bean.setReqId(reqId);
        bean.setDestination(destination);
        bean.setTargetFloor(floor);
        bean.setNaviStrategy(Definition.AdvNaviStrategy.ELEVATOR);
        String jsonStr = mGson.toJson(bean);

        return startAction(reqId, Definition.ACTION_ADVANCED_NAVIGATION, jsonStr, listener);
    }

    public int startElevatorNavigation(int reqId, String destination, int floor,
                                       boolean isAdjustAngle, ActionListener listener) {
        NavigationAdvancedBean bean = new NavigationAdvancedBean();
        bean.setReqId(reqId);
        bean.setDestination(destination);
        bean.setTargetFloor(floor);
        bean.setAdjustAngle(isAdjustAngle);
        bean.setNaviStrategy(Definition.AdvNaviStrategy.ELEVATOR);
        String jsonStr = mGson.toJson(bean);

        return startAction(reqId, Definition.ACTION_ADVANCED_NAVIGATION, jsonStr, listener);
    }

    /**
     * 多机导航策略
     *
     * @param destination      目的地
     * @param advNaviStrategy     导航策略,NULL: 不使用多机导航
     * @param standbyDesList   备用目的地列表
     * @param navigationParams 导航参数
     * @param listener         listener
     */
    public int startMultiRobotNavigation(int reqId,
                                         String destination,
                                         Definition.AdvNaviStrategy advNaviStrategy,
                                         List<String> standbyDesList,
                                         String navigationParams,
                                         ActionListener listener) {

        NavigationAdvancedBean bean = new NavigationAdvancedBean();
        bean.setReqId(reqId);
        bean.setDestination(destination);
        bean.setNaviStrategy(advNaviStrategy);
        bean.setStandbyDesList(standbyDesList);
        bean.setNavigationParams(navigationParams);
        String jsonStr = mGson.toJson(bean);

        return startAction(reqId, Definition.ACTION_ADVANCED_NAVIGATION, jsonStr, listener);
    }

    /**
     * 乘梯导航
     */
    public int startElevatorNavigation(int reqId, String destination, int floor, double coordinateDeviation,
                                       long time, double linearSpeed, double angularSpeed,
                                       boolean isAdjustAngle, double destinationRange,
                                       int wheelOverCurrentRetryCount, long multipleWaitTime,
                                       int priority, double linearAcceleration, double angularAcceleration,
                                       ActionListener listener) {
        NavigationAdvancedBean bean = new NavigationAdvancedBean();
        bean.setReqId(reqId);
        bean.setDestination(destination);
        bean.setTargetFloor(floor);
        bean.setTime(time);
        bean.setCoordinateDeviation(coordinateDeviation);
        bean.setLinearSpeed(linearSpeed);
        bean.setAngularSpeed(angularSpeed);
        bean.setAdjustAngle(isAdjustAngle);
        bean.setDestinationRange(destinationRange);
        bean.setWheelOverCurrentRetryCount(wheelOverCurrentRetryCount);
        bean.setMultipleWaitTime(multipleWaitTime);
        bean.setPriority(priority);
        bean.setLinearAcceleration(linearAcceleration);
        bean.setAngularAcceleration(angularAcceleration);
        String jsonStr = mGson.toJson(bean);
        bean.setNaviStrategy(Definition.AdvNaviStrategy.ELEVATOR);

        return startAction(reqId, Definition.ACTION_ADVANCED_NAVIGATION, jsonStr, listener);
    }

    public int stopAdvanceNavigation(int reqId) {
        return stopAction(reqId, Definition.ACTION_ADVANCED_NAVIGATION, true);
    }

    /**
     * 是否存在TopIR
     * @return
     */
    public boolean hasTopIR() {
        try {
            return mApi.hasTopIR();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 控制UVC托盘摄像头模块的识别, 单次识别接口
     */
    public int uvcSingleClassify(int reqId, CommandListener listener){
        try {
            CommandBean bean = new CommandBean(Definition.CMD_UVC_CAMERA_SINGLE_CLASSIFY, "", false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 线激光托盘摄像头，使用制具自检
     */
    public int trayLaserInspection(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_TRAY_LASER_INSPECTION, "", false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 控制UVC托盘摄像头模块的识别, 持续识别接口
     */
    public int uvcContinueClassify(int reqId, CommandListener listener){
        try {
            CommandBean bean = new CommandBean(Definition.CMD_UVC_CAMERA_CONTINUE_CLASSIFY, "", false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 控制UVC托盘摄像头模块的识别, 不识别接口
     */
    public int uvcSingleyNoClassify(int reqId, CommandListener listener){
        try {
            CommandBean bean = new CommandBean(Definition.CMD_UVC_CAMERA_SINGLE_NO_CLASSIFY, "", false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 控制UVC托盘摄像头模块的识别, 停止持续识别接口
     */
    public int uvcStopContinueClassify(int reqId, CommandListener listener){
        try {
            CommandBean bean = new CommandBean(Definition.CMD_UVC_CAMERA_CONTINUE_CLASSIFY_STOP, "", false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * UVC托盘摄像头的设备的连接状态查询
     */
    public int queryUvcCameraConnectedStatus(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_QUERY_UVC_CAMERA_CONNECTED_STATUS, "", false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 是否存在回充IR
     * @return
     */
    public boolean hasChargeIR() {
        try {
            return mApi.hasChargeIR();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public int requestSettingsScenarioList(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_SETTINGS_SCENARIO_LIST, null, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int uploadSettingsScenario(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_UPLOAD_SETTINGS_SCENARIO, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int requestSettingsScenarioInfo(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_SETTINGS_SCENARIO_INFO, null, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 是否使用自动效果灯带接口
     * @return
     */
    public boolean isUseAutoEffectLed() {
        try {
            return mApi.isUseAutoEffectLed();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 自动效果底盘灯带
     */
    public int setBottomLedEffect(int reqId, int effect, CommandListener listener) {
        if (!isUseAutoEffectLed()) {
            if (listener != null){
                listener.onResult(Definition.CMD_SEND_ERROR_RES_NOT_HOLD, Definition.FAILED, "");
            }
            return Definition.CMD_SEND_ERROR_RES_NOT_HOLD;
        }
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_LED_EFFECT, effect);
            CommandBean bean = new CommandBean(Definition.CMD_CAN_BOTTOM_LED_EFFECT, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 是否有锁骨灯
     * @return
     */
    public boolean isHasClavicleLed() {
        try {
            return mApi.isHasClavicleLed();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 是否有胸口灯
     * @return
     */
    public boolean isHasChestLed() {
        try {
            return mApi.isHasChestLed();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 控制IobLed灯，即Saiph Pro的锁骨灯
     *"Iob"应该是拼写错误，应该是Lobby，表示胸口部位的灯带。
     * 对招财Pro，豹小递送是锁骨灯；小秘2是胸口灯
     * Slim不支持该接口，靠业务区分
     */
    public int setClavicleLedEffect(int reqId, int effect, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_LED_EFFECT, effect);
            CommandBean bean = new CommandBean(Definition.CMD_CAN_CLAVICLE_LED_EFFECT, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 是否有托盘指示灯
     * @return
     */
    public boolean isHasTrayLight() {
        try {
            return mApi.isHasTrayLight();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * Pro托盘三层指示灯的控制
     *
     * @param effect must be defined in Definition.
     */
    public int setTrayLightEffect(int reqId, int effect, CommandListener listener) {
        if (!isHasTrayLight()) {
            if (listener != null){
                listener.onResult(Definition.CMD_SEND_ERROR_RES_NOT_HOLD, Definition.FAILED, "");
            }
            return Definition.CMD_SEND_ERROR_RES_NOT_HOLD;
        }
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_TRAY_LIGHT_EFFECT, effect);
            CommandBean bean = new CommandBean(Definition.CMD_CAN_TRAY_LIGHT_EFFECT, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * Slim托盘灯控制（设备接口不一样），靠业务调用区分
     */
    public int setTrayLedEffect(int reqId, int effect, CommandListener listener) {
        if (!isHasTrayLight()) {
            //不想增加新判断了，靠相同接口，判断的还是是否存在托盘Led
            if (listener != null){
                listener.onResult(Definition.CMD_SEND_ERROR_RES_NOT_HOLD, Definition.FAILED, "");
            }
            return Definition.CMD_SEND_ERROR_RES_NOT_HOLD;
        }
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_TRAY_AUTO_LED_EFFECT, effect);
            CommandBean bean = new CommandBean(Definition.CMD_CAN_TRAY_LED_EFFECT, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 是否使用ProZcbLed灯带接口
     * @return
     */
    @Deprecated
    public boolean isUseProZcbLed() {
        try {
            return mApi.isUseProZcbLed();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 控制ZcbLed灯，即Saiph Pro的底盘灯带
     * Zcb:招财豹
     */
    @Deprecated
    public int setProZcbLedEffect(int reqId, int proZcbEffect , CommandListener listener){
        if (!isUseProZcbLed()) {
            if (listener != null){
                listener.onResult(Definition.CMD_SEND_ERROR_RES_NOT_HOLD, Definition.FAILED, "");
            }
            return Definition.CMD_SEND_ERROR_RES_NOT_HOLD;
        }
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_PRO_ZCB_EFFECT, proZcbEffect);
            CommandBean bean = new CommandBean(Definition.CMD_ZCB_LED_EFFECT, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 是否有锁骨灯
     * @return
     */
    @Deprecated
    public boolean isHasClavicleLight() {
        try {
            return mApi.isHasClavicleLight();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 是否有胸口灯
     * @return
     */
    @Deprecated
    public boolean isHasChestLight() {
        try {
            return mApi.isHasChestLight();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 控制IobLed灯，即Saiph Pro的锁骨灯
     * "Iob"应该是拼写错误，应该是Lobby，表示胸口部位的灯带。
     */
    @Deprecated
    public int setProIobLedEffect(int reqId, int proIobLedEffect, CommandListener listener) {

        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_PRO_IOB_EFFECT, proIobLedEffect);
            CommandBean bean = new CommandBean(Definition.CMD_IOB_LED_EFFECT, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 是否有托盘指示灯
     * @return
     */
    @Deprecated
    public boolean isHasProTrayLED() {
        try {
            return mApi.isHasProTrayLED();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * Pro托盘三层指示灯的控制
     * @param trayLedEffect must be defined in Definition.
     * like TRAYLEDS_TRAY01ON , TRAYLEDS_TRAY01OFF , TRAYLEDS_TRAY02ON , TRAYLEDS_TRAY02OFF ,
     *  TRAYLEDS_TRAY03ON , TRAYLEDS_TRAY03OFF , TRAYLEDS_ALLON , TRAYLEDS_ALLOFF
     */
    @Deprecated
    public int setProTrayLedEffect(int reqId, int trayLedEffect , CommandListener listener){
        if (!isHasProTrayLED()) {
            if (listener != null){
                listener.onResult(Definition.CMD_SEND_ERROR_RES_NOT_HOLD, Definition.FAILED, "");
            }
            return Definition.CMD_SEND_ERROR_RES_NOT_HOLD;
        }
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_TRAY_LED_EFFECT, trayLedEffect);
            CommandBean bean = new CommandBean(Definition.CMD_TRAY_LED_EFFECT, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * get mFirstConfigFinishedNotReboot state
     *
     * @return String result is string "true" or "false"
     */
    public boolean isFirstConfigFinishedNotReboot() {
        boolean result = false;
        try {
            String b = mApi.getRobotInfo(Definition.SYNC_ACTION_FIRST_CONFIG_ACTIVATION_STATE, null);
            result = Boolean.parseBoolean(b);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return result;
    }

    public void onChassisSensorFinished() {
        try {
            mApi.updateSystemStatus(Definition.SYSTEM_NAVI_SENSOR_RECOVERY, "");
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 当前机器人是否支持视觉
     * 和当前地图是否是视觉图
     *
     * @param reqId
     * @param listener
     * @return
     */
    public int isCurrentMapHasVision(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_MAP_HAS_VISION, null, false);
            return startAction(reqId, Definition.ACTION_NAVI_MAP_HAS_VISION, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 地图类型是否包含视觉
     *
     * @param reqId
     * @param mapName  地图名称
     * @param listener
     * @return
     */
    public int isMapHasVision(int reqId, String mapName, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_MAP_NAME, mapName);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_MAP_HAS_VISION, param.toString(), false);
            return startAction(reqId, Definition.ACTION_NAVI_MAP_HAS_VISION, mGson.toJson(bean), listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 上传文件
     */
    public int uploadFile(int reqId, String filePath, String fileType, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_UPLOAD_FILE_PATH, filePath);
            param.put(Definition.JSON_UPLOAD_FILE_TYPE, fileType);
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_UPLOAD_FILE, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 通用POST请求
     */
    public int requestPost(int reqId, String api, Map<String, String> params,
                           String contentType, CommandListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_REQUEST_API, api);
            param.put(Definition.JSON_REQUEST_CONTENT_TYPE, contentType);
            param.put(Definition.JSON_REQUEST_PARAMS, new JSONObject(params));
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_REQUEST_POST, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 上传额外文件
     */
    public int uploadExtraFilePkg(int reqId, String mapName, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_UPLOAD_EXTRA_FILE_PKG, mapName, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 下载额外文件
     */
    public int downloadExtraFilePkg(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_DOWNLOAD_EXTRA_FILE_PKG, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 保存额外信息
     */
    public int setExtraFileData(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_EXTRA_FILE_DATA, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 检查是否存在导航额外文件
     */
    public int hasNavigationExtraData(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_HAS_EXTRA_FILE, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 压缩导航额外文件
     */
    public int zipNavigationExtraData(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_ZIP_EXTRA_FILE, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 解压导航额外文件
     */
    public int unzipNavigationExtraData(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_UNZIP_EXTRA_FILE, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 压缩地图文件
     */
    public int zipMapFile(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_ZIP_MAP_FILE, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 解压地图文件
     */
    public int unzipMapFile(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_UNZIP_MAP_FILE, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 下载地图包的所有流程
     */
    public int downLoadWholeMapPkg(int reqId, String params, CommandListener listener) {
        try {
            return startAction(reqId, Definition.ACTION_DOWNLOAD_WHOLE_MAP_PKG, params, listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 终止下载地图包的所有流程
     */
    public int stopDownLoadWholeMapPkg(int reqId) {
        try {
            return stopAction(reqId, Definition.ACTION_DOWNLOAD_WHOLE_MAP_PKG, true);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public void onImportMapModuleFinish() {
        try {
            mApi.updateSystemStatus(Definition.REMOTE_IMPORT_MAP_END, Definition.FINISHED);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取本地地图信息列表，从数据库获取
     */
    public int getLocalMapInfoList(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_LOCAL_MAP_INFO_LIST, "", false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_LOCAL_MAP_INFO_LIST, mGson.toJson(bean), listener);
    }

    /**
     * 当前机型是否有Mono
     */
    public boolean hasMono() {
        try {
            return mApi.hasMono();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public boolean hasTopMono() {
        try {
            return mApi.hasTopMono();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public boolean isSupportElevator() {
        try {
            return mApi.isSupportElevator();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 是否开启底盘雷达数据上报的开关
     *
     * @param reqId
     * @param enabled
     * @return
     */
    public int setNavigationLineData(int reqId, boolean enabled) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_ENALBE_LINE_DATA, enabled);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_ENABLE_REPORT_LINE_DATA, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 是否开启RGBD深度图像上报的开关
     *
     * @param reqId
     * @param enabled
     * @return
     */
    public int setNavigationDepthImage(int reqId, boolean enabled) {
        return setNavigationDepthImage(reqId, enabled, Definition.DEPTH_DEVICE.DEPTH_1);
    }

    /**
     * 是否开启深度图像上报的开关
     *
     * @param reqId
     * @param device
     * @param enabled
     * @return
     */
    public int setNavigationDepthImage(int reqId, boolean enabled, Definition.DEPTH_DEVICE device) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_ENALBE_DEPTH_IMAGE, enabled);
            param.put(Definition.JSON_NAVI_DEPTH_DEVICE, device.getId());
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_ENABLE_DEPTH_IMAGE, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 是否开启Top IR图像上报的开关
     * @param reqId
     * @param enabled
     * @return
     */
    public int setNavigationIRImage(int reqId, boolean enabled) {
        return setNavigationIRImage(reqId, enabled, Definition.IR_DEVICE.TOP_IR);
    }

    /**
     * 是否开启 IR图像上报的开关
     * @param reqId
     * @param device
     * @param enabled
     * @return
     */
    public int setNavigationIRImage(int reqId, boolean enabled, Definition.IR_DEVICE device) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_ENALBE_IR_IMAGE, enabled);
            param.put(Definition.JSON_NAVI_IR_DEVICE, device.getId());
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_ENABLE_IR_IMAGE, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    public void onNavigationLoadMapFinished() {
        try {
            mApi.updateSystemStatus(Definition.SYSTEM_NAVI_LOAD_MAP, Definition.FINISHED);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void onStopChargeConfirmFinished() {
        try {
            mApi.updateSystemStatus(Definition.SYSTEM_STOP_CHARGE_CONFIRM, Definition.FINISHED);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void onLeavePileToPointFinished() {
        try {
            mApi.updateSystemStatus(Definition.SYSTEM_LEAVE_PILE_TO_POINT_END, Definition.FINISHED);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * Pro含有额外的电动门外设时，控制电动门接口
     * @param doorCmd must be defined in Definition , only support one of below commands :
     * CAN_DOOR_DOOR1_DOOR2_OPEN; CAN_DOOR_DOOR1_DOOR2_CLOSE ;
     * CAN_DOOR_DOOR3_DOOR4_OPEN; CAN_DOOR_DOOR3_DOOR4_CLOSE;
     * CAN_DOOR_ALL_OPEN; CAN_DOOR_ALL_CLOSE;
     * 发送指令前，先通过mApi.registerStatusListener(STATUS_CAN_ELECTRIC_DOOR_CTRL,listener)
     * 实时获取控制指令的结果状态值
     */
    public int setElectricDoorCtrl(int reqId, int doorCmd, CommandListener listener) {
        if (!FileUtils.hasElectricDoor()) {
            if (listener != null){
                listener.onResult(Definition.CMD_SEND_ERROR_RES_NOT_HOLD, Definition.FAILED, "");
            }
            return Definition.CMD_SEND_ERROR_RES_NOT_HOLD;
        }
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_ELECTRIC_DOOR_CTRL, doorCmd);
            CommandBean bean = new CommandBean(Definition.CMD_CAN_ELECTRIC_DOOR_CTRL, param.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * Pro电动门外设时，查询电动门状态接口,各个门对应的查询结果状态值，defined in Definition:
     * CAN_DOOR_STATUS_OPEN = 51;
     * CAN_DOOR_STATUS_CLOSE = 68;
     * CAN_DOOR_STATUS_TIMEOUT = 85;
     * CAN_DOOR_STATUS_FG2LOW = 238;
     * CAN_DOOR_STATUS_RUNNING = 102;
     * @param listener
     */
    public int getElectricDoorStatus(int reqId, CommandListener listener) {
        if (!FileUtils.hasElectricDoor()) {
            if (listener != null){
                listener.onResult(Definition.CMD_SEND_ERROR_RES_NOT_HOLD, Definition.FAILED, "");
            }
            return Definition.CMD_SEND_ERROR_RES_NOT_HOLD;
        }
        try {
            CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_ELECTRIC_DOOR_STATUS, "", false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 主动问题上报
     * @param occurrenceTime
     * @param issueType
     * @param packageName
     * @param pageName
     * @param description
     */
    public int proactiveProblemReport(long occurrenceTime, String issueType, String packageName, String pageName, String description) {

        Intent intent = new Intent(Definition.INTENT_PROBLEM_REPORT);

        intent.putExtra(Definition.KEY_PROBLEM_OCCURRENCE_TIME, occurrenceTime);
        intent.putExtra(Definition.KEY_PROBLEM_ISSUE_TYPE, issueType);
        intent.putExtra(Definition.KEY_PROBLEM_PACKAGE_NAME, packageName);
        intent.putExtra(Definition.KEY_PROBLEM_PAGE_NAME, pageName);
        intent.putExtra(Definition.KEY_PROBLEM_DESCRIPTION, description);

        this.ctx.sendBroadcast(intent);
        return Definition.RESULT_OK;
    }

    /**
     * 获取点图分离标志位
     */
    public int getMapPosSeparate(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_GET_MAP_POS_SEPARATE, "", false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
    }

    /**
     * 通过本地内存卡目录下地图文件夹的地图名，得到对应地图信息(List<MapInfo>)
     */
    public int getMapInfoBySdMapNames(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_MAP_INFO_BY_SD_MAP_NAMES, null, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获取当前使用地图名
     */
    public int getCurrentMapName(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_CURRENT_MAP_NAME, null, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获取当前使用地图名
     */
    public int getNavigationExtraData(int reqId, String mapName, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_EXTRA_FILE_DATA, mapName, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获取Lxc导航版本
     */
    public String getLxcVersion() {
        return FileUtils.getLxcVersion();
    }

    /**
     * 上传多楼层信息给接待后台
     * @param reqId
     * @param multiFloorInfo 多楼层信息json串
     * @param listener
     * @return
     */
    public int uploadMultiFloorInfo(int reqId, String multiFloorInfo, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_UPDATE_MULTI_FLOOR_INFO
                , multiFloorInfo, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 机器人任务信息同步接口
     * @param reqId
     * @param paramJson
     * @param listener
     */
    public int deliveryRobotTask(int reqId, String paramJson, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_DELIVERY_ROBOTTASK
                , paramJson, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 机器人上下线通知接口
     * @param reqId
     * @param paramJson
     * @param listener
     */
    public int deliveryRobotStatusNotify(int reqId, String paramJson, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_DELIVERY_ROBOTSTATUSNOTIFY
                , paramJson, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                parseCommand(listener));
    }

    /**
     * 机器人异常上报
     * @param reqId
     * @param paramJson
     * @param listener
     */
    public int deliveryMonitorDetail(int reqId, String paramJson, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_DELIVERY_MONITORDETAIL
                , paramJson, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                parseCommand(listener));
    }
    /**
     * 机器人点评上报
     * @param reqId
     * @param paramJson
     * @param listener
     */
    public int deliveryRobotComment(int reqId, String paramJson, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_DELIVERY_ROBOTCOMMENT
                , paramJson, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                parseCommand(listener));
    }

    public int startRadarAlign(int reqId, String poseName, CommandListener listener) {
        try {
            RadarAlignBean bean = new RadarAlignBean();
            bean.setDestination(poseName);
            return startAction(reqId, Definition.ACTION_RADAR_ALIGN, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int startRadarAlign(int reqId, String poseName, long startAlignTimeout,
                               long retryDelayTime, long navigationTimeout, CommandListener listener) {
        try {
            RadarAlignBean bean = new RadarAlignBean();
            bean.setDestination(poseName);
            bean.setStartAlignTimeout(startAlignTimeout);
            bean.setRetryDelayTime(retryDelayTime);
            bean.setNavigationTimeout(navigationTimeout);
            return startAction(reqId, Definition.ACTION_RADAR_ALIGN, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int stopRadarAlign(int reqId) {
        return stopAction(reqId, Definition.ACTION_RADAR_ALIGN, true);
    }

    public int cabinetPutGoodsStatus(int reqId, String taskId, String stationType, String stationId, String stationOrderId, CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.JSON_REMOTE_TASK_ID, taskId);
            jsonObject.put(Definition.JSON_REMOTE_STATION_TYPE, stationType);
            jsonObject.put(Definition.JSON_REMOTE_STATION_ID, stationId);
            jsonObject.put(Definition.JSON_REMOTE_STATION_ORDER_ID, stationOrderId);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        try {
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_CABINET_PUT_GOOD_STATUS,
                    jsonObject.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int cabinetAutoTest(int reqId, String roomNumber, String floorNumber, String testId, CommandListener listener) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.JSON_REMOTE_ROOM_NUMBER, roomNumber);
            jsonObject.put(Definition.JSON_REMOTE_FLOOR_NUMBER, floorNumber);
            jsonObject.put(Definition.JSON_REMOTE_TEST_ID, testId);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        try {
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_CABINET_AUTO_TEST,
                    jsonObject.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int startControlElectricDoor(int reqId, int doorCmd, ActionListener listener) {
        try {
            ControlElectricDoorBean bean = new ControlElectricDoorBean(doorCmd);
            return startAction(reqId, Definition.ACTION_CONTROL_ELECTRIC_DOOR, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 是否屏蔽二开能力
     */
    public boolean isSecondaryDevelopmentProhibited() {
        int enableValue = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_IS_SECONDARY_DEVELOPMENT_PROHIBITED);
        Log.d(TAG, "isSecondaryDevelopmentProhibited : " + enableValue);
        return enableValue == 1;
    }

    public int startNavigationOtaDowngrade(int reqId, String downgradeTargetVersion, boolean isDeleteLocalData, String fullVersion, CommandListener listener) {
        try {
            JSONObject params = new JSONObject();
            params.put(Definition.JSON_NAVI_DOWNGRADE_TARGET_VERSION, downgradeTargetVersion);
            params.put(Definition.JSON_NAVI_IS_DELETE_LOCAL_MAP, isDeleteLocalData);
            params.put(Definition.JSON_NAVI_DOWNGRADE_TARGET_FULL_VERSION, fullVersion);
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_OTA_DOWNGRADE, params.toString(), false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int notifyOtaExecutionEvents(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_NOTIFY_OTA_EXECUTION_EVENT, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获取支持的创建地图类型
     */
    public String getCreateMapType() {
        try {
            return mApi.getCreateMapType();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public String isAlreadyInElevator() {
        try {
            return mApi.isAlreadyInElevator();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     *批量更新闸机关系信息
     * @param reqId
     * @param params
     * @param listener
     * @return
     */
    public int batchInsertOrUpdateGate(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_BATCH_INSERT_OR_UPDATE_GATE, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     *根据闸机主键批量删除指定关系信息
     * @param reqId
     * @param params
     * @param listener
     * @return
     */
    public int deleteByGateIds(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_DELETE_BY_GATE_IDS, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    /**
     *根据闸机线主键批量删除指定关系信息
     * @param reqId
     * @param params
     * @param listener
     * @return
     */
    public int deleteByLineIds(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_DELETE_BY_LINE_IDS, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }


    /**
     *根据闸机ID获取闸机关系信息列表
     * @param reqId
     * @param params
     * @param listener
     * @return
     */
    public int findByGateIds(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_FIND_BY_GATE_IDS, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }
    /**
     *根据闸机线ID获取闸机关系信息列表
     * @param reqId
     * @param params
     * @param listener
     * @return
     */
    public int findByLineIds(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_FIND_BY_LINE_IDS, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获取所有闸机关系信息
     * @param reqId
     * @param listener
     * @return
     */
    public int getAllGateRelationData(int reqId, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_FIND_GATE_RELATION, null, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获取闸机服务中的所有本地闸机设备
     *
     */
    public int getAllGateDevices(int reqId, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_GATE_GET_ALL_GATE_DEVICES, "", false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
    }

    /**
     * 更新闸机服务中闸机设备中，某个设备的绑定状态
     * @param params 闸机设备参数json串, 包含两个字段：gateIdentifier和isBindGateLine
     */
    public int updateBindGateLineState(int reqId, String params, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_GATE_UPDATE_BIND_LINE_STATE, params, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
    }

    /**
     * 批量更新闸机信息
     * @param params 闸机设备信息参数json
     */
    public int updateGateDevicesInfo(int reqId, String params, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_GATE_UPDATE_GATE_DEVICES_INFO, params, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
    }

    /**
     *删除传入闸机线主键以外的数据并返回删除的闸机关系集合
     * @param reqId
     * @param params
     * @param listener
     * @return
     */
    public int deleteExceptLineIds(int reqId, String params, CommandListener listener) {
        try {
            CommandBean bean = new CommandBean(Definition.CMD_NAVI_DELETE_EXCEPT_LINE_IDS, params, false);
            return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean), listener);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public String getLineSpeed() {
        String line = "";
        try {
            line = mApi.getRobotInfo(Definition.SYNC_ACTION_GET_LINE_SPEED, null);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return line;
    }

    /**
     * 设置导航最大速度
     *
     * @param reqId
     * @param lineSpeed 线速度
     * @param listener 回调函数
     * @return
     */
    public int setLineSpeed(int reqId, String lineSpeed, CommandListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_SET_LINE_SPEED, lineSpeed, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                parseCommand(listener));
    }

}
