/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.client.module;

import android.os.RemoteException;

import com.ainirobot.coreservice.IModuleCallback;

public class ModuleCallbackApi extends IModuleCallback.Stub {
    @Override
    public boolean onSendRequest(int reqId, String reqType, String reqText, String reqParam)
            throws RemoteException {
        return false;
    }

    @Override
    public void onHWReport(int hwFunction, String cmdType, String hwReport)
            throws RemoteException {

    }

    @Override
    public void onSuspend() throws RemoteException {

    }

    @Override
    public void onRecovery() throws RemoteException {

    }

}
