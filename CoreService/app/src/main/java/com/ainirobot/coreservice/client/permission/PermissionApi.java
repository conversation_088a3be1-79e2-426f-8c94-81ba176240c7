/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.client.permission;

import android.content.Context;
import android.content.Intent;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.IPermissionApi;
import com.ainirobot.coreservice.IRobotBinderPool;
import com.ainirobot.coreservice.client.BaseSubApi;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.messagedispatcher.PermissionDispatcher;

import java.util.ArrayList;

public class PermissionApi extends BaseSubApi {

    private static final String TAG = PermissionApi.class.getSimpleName();
    private static PermissionApi instance;
    private IPermissionApi mApi;
    private final ArrayList<PermissionListener> mPermissionListenerList = new ArrayList<>();
    private PermissionListener mPermissionListener;

    private PermissionApi() {
        Log.i(TAG, "PermissionApi create");
        startNewThread(TAG);
    }

    public static synchronized PermissionApi getInstance() {
        if (null == instance) {
            instance = new PermissionApi();
        }
        return instance;
    }

    @Override
    public void onConnect(IRobotBinderPool robotBinderPool, Context context) {
        Log.i(TAG, "PermissionApi start connect");
        try {
            mApi = IPermissionApi.Stub.asInterface(
                    robotBinderPool.queryBinder(Definition.BIND_PERMISSION, null));
            Log.i(TAG, "PermissionApi connect success");
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void registerPermissionListener(PermissionListener listener) {
        if (listener == null) {
            return;
        }

        if (mPermissionListener == null) {
            mPermissionListener = new PermissionListener() {
                public void activityStarting(Intent intent, String pkg) {
                    for (PermissionListener listenerItem : mPermissionListenerList) {
                        listenerItem.activityStarting(intent, pkg);
                    }
                }

                public void activityResuming(String pkg) {
                    for (PermissionListener listenerItem : mPermissionListenerList) {
                        listenerItem.activityResuming(pkg);
                    }
                }

                public void appCrashed(String processName, int pid,
                                       String shortMsg, String longMsg,
                                       long timeMillis, String stackTrace) {
                    for (PermissionListener listenerItem : mPermissionListenerList) {
                        listenerItem.appCrashed(processName, pid, shortMsg, longMsg, timeMillis,
                                stackTrace);
                    }
                }

                public void appEarlyNotResponding(String processName, int pid, String annotation) {
                    for (PermissionListener listenerItem : mPermissionListenerList) {
                        listenerItem.appEarlyNotResponding(processName, pid, annotation);
                    }
                }

                public void appNotResponding(String processName, int pid, String processStats) {
                    for (PermissionListener listenerItem : mPermissionListenerList) {
                        listenerItem.appNotResponding(processName, pid, processStats);
                    }
                }

                public void systemNotResponding(String msg) {
                    for (PermissionListener listenerItem : mPermissionListenerList) {
                        listenerItem.systemNotResponding(msg);
                    }
                }

                /**
                 * onForegroundActivitiesChanged only support during android sdk 28 and above
                 * @param pid
                 * @param uid
                 * @param foregroundActivities
                 */
                public void onForegroundActivitiesChanged(int pid, int uid,
                                                          boolean foregroundActivities) {
                    for (PermissionListener listenerItem : mPermissionListenerList) {
                        listenerItem.onForegroundActivitiesChanged(pid, uid, foregroundActivities);
                    }
                }

                /**
                 * onProcessDied only support during android sdk 28 and above
                 * @param pid
                 * @param uid
                 */
                public void onProcessDied(int pid, int uid) {
                    for (PermissionListener listenerItem : mPermissionListenerList) {
                        listenerItem.onProcessDied(pid, uid);
                    }
                }
            };
        }

        synchronized (mPermissionListenerList) {
            if (mPermissionListenerList.contains(listener)) {
                return;
            } else {
                mPermissionListenerList.add(listener);
                try {
                    Log.d(TAG, "register permission listener");
                    PermissionDispatcher permissionDispatcher = mMessageDispatcher.obtainPermissionDispatcher(
                            mHandlerThread.getLooper(), mPermissionListener);
                    mApi.registerPermissionListener(permissionDispatcher);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public void unregisterPermissionListener(PermissionListener listener) {
        if (listener == null) {
            return;
        }

        if (mPermissionListener == null) {
            return;
        }

        synchronized (mPermissionListenerList) {
            if (mPermissionListenerList.remove(listener) && mPermissionListenerList.size() == 0) {
                try {
                    Log.d(TAG, "unregister permission listener");
                    PermissionDispatcher permissionDispatcher = mMessageDispatcher.obtainPermissionDispatcher(
                            mHandlerThread.getLooper(), mPermissionListener);
                    mApi.unregisterPermissionListener(permissionDispatcher);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
