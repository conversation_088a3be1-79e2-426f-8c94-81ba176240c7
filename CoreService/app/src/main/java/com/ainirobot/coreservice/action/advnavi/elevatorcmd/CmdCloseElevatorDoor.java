package com.ainirobot.coreservice.action.advnavi.elevatorcmd;

import android.util.Log;

import com.ainirobot.coreservice.client.Definition;

/**
 * Data: 2023/3/29 16:22
 * Author: wanglijing
 * <p>
 * 关闭电梯门
 * * 成功 上报RESULT_OK
 * * 失败 尝试调呼梯重新走呼梯 -> 关门
 * * 超时 重试
 */
public class CmdCloseElevatorDoor extends ElevatorCommand {

    private CmdCallElevator cmdCallElevator;

    CmdCloseElevatorDoor(String name, CmdType intentType, ElevatorCommandInterface commandInterface, ElevatorCommandListener listener) {
        super(name, intentType, commandInterface, listener);
        initCmdType(CmdType.CLOSE_DOOR);

    }


    @Override
    public void start() {
        super.start();
        closeElevatorDoor();
    }

    @Override
    public void stop() {
        super.stop();
        if (null != cmdCallElevator && cmdCallElevator.isAlive) {
            cmdCallElevator.stop();
        }
    }

    @Override
    public void cmdResponse(String command, String result, String extraData) {
        super.cmdResponse(command, result, extraData);
        if (null != cmdCallElevator) {
            cmdCallElevator.cmdResponse(command, result, extraData);
        }
    }

    @Override
    public void onSuccess() {
        super.onSuccess();
        onResult(Definition.RESULT_OK, "close door suc");
    }

    @Override
    public void onFail() {
        if (null == cmdCallElevator) {
            cmdCallElevator = new CmdCallElevator(TAG, intentType, commandInterface, callListener);
        }
        Log.d(TAG, "close door fail, call elevator");
        cmdCallElevator.start();
    }

    private final ElevatorCommandListener callListener = new ElevatorCommandListener() {
        @Override
        public void onStatusUpdate(int status, String data, String extraData) {
            onCmdStatusUpdate(status, data, extraData);
        }

        @Override
        public void onFinish(int status, String data) {
            if (status == Definition.RESULT_OK) {
                Log.d(TAG, "call elevator suc, close door");
                closeElevatorDoor();
                return;
            }
            onResult(status, data);
        }
    };

    @Override
    protected void onRetry() {
        closeElevatorDoor();
    }

}
