/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.client.hardware;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.RemoteException;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.IRobotBinderPool;
import com.ainirobot.coreservice.IRobotCore;
import com.ainirobot.coreservice.client.BaseSubApi;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.IntentUtil;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.account.AccountApi;
import com.ainirobot.coreservice.client.ashmem.ShareMemoryApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.listener.InitListener;
import com.ainirobot.coreservice.client.log.RLog;
import com.ainirobot.coreservice.client.permission.PermissionApi;
import com.ainirobot.coreservice.client.person.PersonApi;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.listener.IActionListener;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Timer;
import java.util.TimerTask;

public class RobotCore {

    private static final String TAG = "RobotCore";

    private static final Map<String, HWService> sHWServices = new HashMap<>();
    private static Context sContext;
    private static IRobotCore sRobotCore;
    private static InitListener sInitListener;
    private static ArrayList<BaseSubApi> mSubApiList = new ArrayList<>();
    private static IRobotBinderPool mRobotBinderPool = null;

    private static boolean isReconnect = false;
    private static Timer reconnectTimer;

    static class RobotConnection implements ServiceConnection {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.d(TAG, "Core server is connected : " + sContext.getPackageName());
            mRobotBinderPool = IRobotBinderPool.Stub.asInterface(service);
            try {
                sRobotCore = IRobotCore.Stub.asInterface(mRobotBinderPool.queryBinder(
                        Definition.BIND_HW, sContext.getPackageName()));
                for (BaseSubApi subApi : mSubApiList) {
                    subApi.onConnect(mRobotBinderPool, sContext);
                }
                if (!isReconnect && sInitListener != null) {
                    sInitListener.onFinish();
                }
                registerHWServices();
                cancelReconnectTimer();
                isReconnect = false;
            } catch (RemoteException | NullPointerException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            for (BaseSubApi subApi : mSubApiList) {
                subApi.onDisconnect();
            }
            reconnect();
        }
    }

    private static final RobotConnection CONNECTION = new RobotConnection();

    private RobotCore() {
    }

    public static void init(@NonNull Context context) {
        init(context, null);
    }

    public static void init(@NonNull Context context, InitListener listener) {
        sContext = context.getApplicationContext();
        sInitListener = listener;
        isReconnect = false;
        RLog.initRLog(sContext);
        mSubApiList.add(RobotSettingApi.getInstance());
        mSubApiList.add(PermissionApi.getInstance());
        mSubApiList.add(AccountApi.getInstance());
        mSubApiList.add(PersonApi.getInstance());
        mSubApiList.add(ShareMemoryApi.getInstance());
        connect();
    }

    private static void connect() {
        Intent intent = IntentUtil.createExplicitIntent(Definition.CORE_SERVICE_NAME,
                IRobotBinderPool.class.getName());
        sContext.bindService(intent, CONNECTION, 0);
    }

    private static void reconnect() {
        Log.d(TAG, "Reconnect to core server : " + sContext.getPackageName() + " isReconnect : " + isReconnect);
        if (isReconnect) {
            return;
        }
        isReconnect = true;
        cancelReconnectTimer();
        reconnectTimer = new Timer();
        reconnectTimer.schedule(new TimerTask() {

            @Override
            public void run() {
                connect();
            }
        }, 1000, 5000);
    }

    private static void cancelReconnectTimer() {
        if (reconnectTimer != null) {
            reconnectTimer.cancel();
            reconnectTimer = null;
        }
    }

    private static void registerHWServices() {
        try {
            for (Entry<String, HWService> entry : sHWServices.entrySet()) {
                sRobotCore.registerHWCallback(entry.getKey(), entry.getValue());
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public static void registerHWService(String serviceName, HWService service) {
        sHWServices.put(serviceName, service);
        try {
            if (isConnected()) {
                sRobotCore.registerHWCallback(serviceName, service);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public static boolean isConnected() {
        return sRobotCore != null && sRobotCore.asBinder().pingBinder();
    }

    public static int sendRequest(String reqType, String requestText, String reqParam) {
        if (TextUtils.isEmpty(reqType)) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        try {
            return sRobotCore.sendRequest(reqType, requestText, reqParam);
        } catch (RemoteException e) {
            Log.d(TAG, "sendRequest failed reqType: " + reqType
                    + ", requestText: " + requestText + ", reqParam: " + reqParam);
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
    }

    public static int sendCommand(String cmdType, String params, boolean isContinue,
                                  final CommandListener listener) {
        if (TextUtils.isEmpty(cmdType)) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        try {
            return sRobotCore.sendCommand(cmdType, params, isContinue, new IActionListener.Stub() {
                @Override
                public void onResult(int result, String responseString) {
                    if (listener != null) {
                        listener.onResult(result, responseString);
                    }
                }

                @Override
                public void onStatusUpdate(int status, String data) {
                    if (listener != null) {
                        listener.onStatusUpdate(status, data);
                    }
                }

                @Override
                public void onError(int errorCode, String errorString) {
                    if (listener != null) {
                        try {
                            listener.onError(errorCode, errorString);
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                    }
                }

                @Override
                public void onResultWithExtraData(int status, String responseString, String extraData) throws RemoteException {
                    if (listener != null) {
                        listener.onResult(status, responseString, extraData);
                    }
                }

                @Override
                public void onErrorWithExtraData(int errorCode, String errorString, String extraData) throws RemoteException {
                    if (listener != null) {
                        try {
                            listener.onError(errorCode, errorString, extraData);
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                    }
                }

                @Override
                public void onStatusUpdateWithExtraData(int status, String data, String extraData) throws RemoteException {
                    if (listener != null) {
                        listener.onStatusUpdate(status, data, extraData);
                    }
                }
            });
        } catch (RemoteException e) {
            Log.d(TAG, "sendCommand failed cmdType: " + cmdType + ", params: " + params
                    + ", isContinue: " + isContinue);
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
    }

    public static void sendAsyncResponse(String cmdType, int result, String message) {
        try {
            sRobotCore.sendAsyncResponse(cmdType, result, message);
        } catch (RemoteException e) {
            Log.d(TAG, "Send async response failed : " + cmdType + "  " + message);
            e.printStackTrace();
        }
    }

    public static void sendAsyncResponse(String cmdType, int result, String message, String extraData) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(Definition.JSON_COMMAND_EXTRA_DATA, extraData);
            jsonObject.put(Definition.JSON_COMMAND_RESULT, message);
            sRobotCore.sendAsyncResponse(cmdType, result, jsonObject.toString());
        } catch (RemoteException e) {
            Log.d(TAG, "Send async response with extra data failed : " + cmdType + "  " + message);
            e.printStackTrace();
        } catch (JSONException ex) {
            ex.printStackTrace();
        }
    }

    public static void sendAsyncStatus(String cmdType, String status) {
        try {
            sRobotCore.sendAsyncStatus(cmdType, status);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public static void sendAsyncStatus(String cmdType, String status, String extraData) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(Definition.JSON_COMMAND_EXTRA_DATA, extraData);
            jsonObject.put(Definition.JSON_COMMAND_STATUS, status);
            sRobotCore.sendAsyncStatus(cmdType, jsonObject.toString());
        } catch (RemoteException e) {
            Log.d(TAG, "Send async status with extra data failed : " + cmdType);
            e.printStackTrace();
        } catch (JSONException ex) {
            ex.printStackTrace();
        }
    }

    public static void sendStatusReport(String serviceName, String type, String params) {
        try {
            sRobotCore.sendStatusReport(serviceName, type, params);
        } catch (RemoteException e) {
            Log.d(TAG, "Send status report failed : " + type + "  " + params);
            e.printStackTrace();
        }
    }

    public static void sendExceptionReport(String serviceName, String type, String params) {
        try {
            sRobotCore.sendExceptionReport(serviceName, type, params);
        } catch (RemoteException e) {
            Log.d(TAG, "Send exception report failed : " + type + "  " + params);
            e.printStackTrace();
        }
    }

    public static String registerStatusListener(String type, StatusListener listener) {
        try {
            return sRobotCore.registerStatusListener(type, listener);
        } catch (RemoteException e) {
            Log.d(TAG, "registerStatusListener failed type: " + type);
            e.printStackTrace();
            return null;
        }
    }

    public static boolean unregisterStatusListener(String id) {
        try {
            return sRobotCore.unregisterStatusListener(id);
        } catch (RemoteException e) {
            Log.d(TAG, "unregisterStatusListener failed id: " + id);
            e.printStackTrace();
            return false;
        }
    }

    public static boolean startStatusSocket(String type, int socketPort) {
        try {
            return sRobotCore.startStatusSocket(type, socketPort);
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }

    public static boolean closeStatusSocket(String type, int socketPort) {
        try {
            return sRobotCore.closeStatusSocket(type, socketPort);
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }
}
