/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.client.upload.bi;


import android.os.Handler;

import com.ainirobot.coreservice.client.Definition;

import java.util.UUID;

public class BiReport {

    private static final int DELAY_TIME = 5000;

    private static BiSocketClient client;

    public static void init() {
        client = new BiSocketClient();
        //delay 5s to connect bi socket server
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                client.connectServer();
            }
        }, DELAY_TIME);
    }

    public static String createId() {
        return UUID.randomUUID().toString();
    }

    /**
     * 普通业务埋点
     */
    public static void report(String tableName, String data) {
        report(Definition.BI_TYPE_NORMAL, Definition.BI_LEVEL_VERBOSE, tableName, data);
    }

    /**
     * 埋点上报
     *
     * @param type      埋点类型
     * @param level     级别
     * @param tableName 表名
     * @param data      数据
     */
    public static void report(String type, int level, String tableName, String data) {
        if (client != null) {
            client.biReport(type, level, tableName, data);
        }
    }

    /**
     * 埋点上报
     *
     * @param type      埋点类型
     * @param level     级别
     * @param tableName 表名
     * @param data      数据
     * @param notForce 是否非强制上报（默认强制上报）
     */
    public static void report(String type, int level, String tableName, String data,
                              boolean notForce) {
        if (client != null) {
            client.biReport(type, level, tableName, data, notForce);
        }
    }
}
