package com.ainirobot.coreservice.client.actionbean;

public class Vector2dBean {

    public double x;
    public double y;

    public Vector2dBean() {}

    public Vector2dBean(double x, double y){
        this.x = x;
        this.y = y;
    }

    public double getX() {
        return x;
    }

    public void setX(double x) {
        this.x = x;
    }

    public double getY() {
        return y;
    }

    public void setY(double y) {
        this.y = y;
    }

    @Override
    public String toString() {
        return "Vector2d{" +
                "x=" + x +
                ", y=" + y +
                '}';
    }
}
