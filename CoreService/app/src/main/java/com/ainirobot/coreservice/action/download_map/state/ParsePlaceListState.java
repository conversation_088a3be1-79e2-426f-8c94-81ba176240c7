package com.ainirobot.coreservice.action.download_map.state;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.download_map.DownloadMapStateEnum;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.DownloadMapBean;

import org.json.JSONException;
import org.json.JSONObject;

public class ParsePlaceListState extends BaseDownloadMapPkgState {

    private String mNapName = "";

    @Override
    protected void onStart(String paramsStr, DownloadMapBean downloadMapBean) {
        this.mNapName = downloadMapBean.getMapName();
        sendCommand(0, Definition.CMD_NAVI_PARSE_PLACE_LIST, paramsStr);
    }

    @Override
    public boolean onCmdResponse(int cmdId, String command, String result, String extraData) {
        Log.d(TAG, "onCmdResponse: command=" + command + " result=" + result +
                " extraData=" + extraData);
        if (TextUtils.equals(command, Definition.CMD_NAVI_PARSE_PLACE_LIST)) {
            if (TextUtils.equals(Definition.SUCCEED, result)) {
                String finishStateParams = getSetMapFinishStateParams();
                Log.d(TAG, "onCmdResponse: finishStateParams=" + finishStateParams);
                sendCommand(0, Definition.CMD_NAVI_SET_MAP_FINISH_STATE, finishStateParams);
            } else {
                onStateRunFailed(0, "parse place is failed");
            }
            return true;
        } else if (TextUtils.equals(command, Definition.CMD_NAVI_SET_MAP_FINISH_STATE)) {
            if (TextUtils.equals(Definition.SUCCEED, result)) {
                onStateRunSuccess();
            } else {
                onStateRunFailed(0, "set map finish state failed");
            }
        }
        return false;
    }

    @Override
    protected DownloadMapStateEnum getNextState() {
        return DownloadMapStateEnum.GET_MAP_POS_SEPARATE;
    }

    private String getSetMapFinishStateParams() {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_MAP_NAME, mNapName);
            param.put(Definition.JSON_MAP_FINISH_STATE, Definition.MAPFINISHSTATE.STATE_COMPLETE_MAP);
            param.put(Definition.JSON_MAP_FINISH_STATE_IS_RESET, true);
            return param.toString();
        } catch (JSONException e) {
            e.printStackTrace();
            return "";
        }
    }
}
