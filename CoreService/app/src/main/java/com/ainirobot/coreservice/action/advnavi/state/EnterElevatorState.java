package com.ainirobot.coreservice.action.advnavi.state;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.StopCommandList;
import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.action.advnavi.elevatorcmd.ElevatorControlUtilListener;
import com.ainirobot.coreservice.action.advnavi.elevatorcmd.ElevatorControlUtil;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.MultiRobotStatus;
import com.ainirobot.coreservice.client.actionbean.NavigationAdvancedBean;
import com.ainirobot.coreservice.utils.DelayTask;

import java.util.ArrayList;
import java.util.List;

/**
 * Data: 2022/8/9 11:05
 * Author: wanglijing
 * Description: EnterElevatorState
 * <p>
 * 进电梯状态
 * doAction:
 * 判断是什么事件触发的进电梯状态
 * already_inside_elevator: 第一次启动发现机器人已经在电梯里了
 * continue_to_center：回电梯口状态失败，梯控出现异常
 *   这两种事件下，梯控可能出现问题了，只能强制去电梯中心，直到导航成功。
 * elevator_arrived：电梯到达，该事件可按正常流程处理。
 *
 * 1. 开启定时开门，并监听电梯是否出现异常（命令是否回复fail）
 * 当梯控出现异常，判断是否在电梯内
 * ** 在电梯内，更新状态为continue_to_center；
 *      由于梯控出现问题，可能无法控制电梯了，而且还在电梯内，只能强制去电梯中心点。这里忽略重试次数，一直重试到到达。
 * ** 在电梯外，更新状态为back_to_gate;
 *      机器人还未进电梯，可以直接让他回电梯口点重试。
 * <p>
 * 2. 导航去电梯中心点
 * 当导航失败时，判断当前状态
 * ** 状态是 continue_to_center
 *      这个状态除doAction外，是监听电梯状态更新的，是该状态说明梯控出问题，只能一直尝试导航到电梯中心，忽略重试次数，直到到达。
 * ** 状态不是 continue_to_center
 *      未更新到该状态，说明此刻梯控还是正常的，回电梯口。
 * <p>
 * 3. 到达电梯中心点后回正机器人朝向
 * <p>
 * 机器人状态上报给梯控：
 * 进入电梯成功-1，在下一个状态中上报，即切图状态，作为必须的第一步操作
 * 取消进入电梯-3，可以不报此状态，要退出时直接 release
 */
public class EnterElevatorState extends BaseNavigationState {

    private static final String DELAY_CHECK = "delay_check";
    /**
     * 导航去电梯中心超时时间: 设置为30秒: 电梯口距离电梯中心很近，如果能进去会很快，
     * 如果进不去，就尽快放弃，回电梯口重试 
     */
    private static final long ENTER_FAIL_TIME = 20 * 1000;

    private Status mStatus;
    //导航失败次数
    private int naviFailCount = 0;
    //导航失败最大重试次数
    private final int NAVI_FAIL_MAX_COUNT = 2;
    //回正失败次数
    private int resumeFailCount = 0;
    //回正失败最大重试次数
    private final int RESUME_FAIL_MAX_COUNT = 5;
    private ElevatorControlUtil controlUtil;
    private String elevatorCenterName;

    private enum Status {
        IDLE,
        //正在导航去电梯中心
        GO_CENTER,
        //到达电梯中心，还未回正方向
        ROTATE,
        //持续回正方向
        CONTINUE_ROTATE,
        //成功到达电梯中心并回正方向
        ARRIVED,
        //回电梯口
        BACK_TO_GATE,
        //机器人已经在电梯内，电梯状态异常，无法保证开门状态。
        //只能一直尝试去电梯中心，直到成功
        CONTINUE_GO_CENTER,
    }

    EnterElevatorState(String name) {
        super(name);
    }

    @Override
    protected boolean isEnableMultiRobotStatus() {
        return true;
    }

    @Override
    protected void preAction(StateData data, IStateListener listener) {
        super.preAction(data, listener);
        naviFailCount = 0;
        controlUtil = new ElevatorControlUtil(this,
                mStateData.getCurrentFloorIndex(),
                true,
                elevatorControlUtilListener);
        elevatorCenterName = mStateData.getElevatorCenterPoseName();
        updateCurrentStatus(Status.IDLE);
    }

    @Override
    protected void doAction() {
        super.doAction();
        onStatusUpdate(Definition.STATUS_START_GO_ELEVATOR_CENTER, "navi to elevator center");

        //如果是 持续去电梯中心事件 或 已经在电梯中时间 触发的该状态，不需要控制电梯
        //释放电梯、更新本状态的子状态为 强制去电梯中心
        if (isContinueToCenter()) {
            Log.d(TAG, "doAction continue to center");
            cancelOpenElevatorDoor();  //关电梯门控制
            updateCurrentStatus(Status.CONTINUE_GO_CENTER);
        } else {
            Log.d(TAG, "doAction navi to center");
            continueOpenDoor(); //控制电梯开门
        }
        naviToElevatorCenter(); //首次导航去电梯中心
    }

    @Override
    protected BaseState getNextState() {
        switch (actionState) {
            case ACTION_NAVI_SUC:
                return StateEnum.SWITCH_MAP.getState();
            case ACTION_NAVI_FAIL:
                return StateEnum.GO_ELEVATOR_GATE.getState();//去电梯中心失败，回电梯口
        }
        return super.getNextState();
    }


    @Override
    protected boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        if (null != controlUtil
                && controlUtil.cmdResponse(command, result, extraData)) {
            //透传给elevatorControlUtil
            return true;
        }
        switch (command) {
            case Definition.CMD_NAVI_RESUME_SPECIAL_PLACE_THETA:
                parseResumeRobotTheta(result);
                return true;
        }
        return super.cmdResponse(cmdId, command, result, extraData);
    }

    @Override
    protected boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        if (null != controlUtil
                && controlUtil.cmdResponse(command, status, extraData)) {
            //透传给elevatorControlUtil
            return true;
        }
        return super.cmdStatusUpdate(cmdId, command, status, extraData);
    }

    //  ----------------  导航  ----------------  //

    @Override
    protected void onNavigationStatusUpdate(int result, String message, String extraData) {
        switch (result){
            case Definition.STATUS_START_NAVIGATION:
                Log.d(TAG, "start enter elevator");
                updateCurrentStatus(Status.GO_CENTER);
                startEnterElevatorTimer();
                break;
        }
        onStatusUpdate(result, message, extraData);
    }

    @Override
    protected void onNavigationError(int result, String message, String extraData) {
        switch (result) {
            case Definition.ERROR_DESTINATION_CAN_NOT_ARRAIVE:
            case Definition.ERROR_MULTI_ROBOT_WAITING_TIMEOUT:
            case Definition.ERROR_NAVIGATION_AVOID_TIMEOUT:
                //naviRetry();    //重试
                backToElevatorGate();
                return;
        }
        onResult(result, message, extraData);
    }

    @Override
    protected void onNavigationResult(int result, String message, String extraData) {
        switch (result) {
            case Definition.ERROR_IN_DESTINATION:
            case Definition.RESULT_OK:
                naviSuccess();
                break;
        }
    }

    @Override
    protected void handleMultipleRobotStatus(List<MultiRobotStatus> robotStatusList) {

    }

    @Override
    protected void exit() {
        DelayTask.cancel(DELAY_CHECK);
        cancelOpenElevatorDoor();
        super.exit();
    }

    @Override
    protected List<StopCommandList.StopCommand> getStopCommands() {
        return new ArrayList<StopCommandList.StopCommand>() {{
            add(new StopCommandList.StopCommand(Definition.CMD_CONTROL_RELEASE_ELEVATOR, null));
        }};
    }

    public void startEnterElevatorTimer() {
        DelayTask.cancel(DELAY_CHECK);
        DelayTask.submit(DELAY_CHECK, new Runnable() {
            @Override
            public void run() {
                if (isAlive() && null != controlUtil) {
                    Log.d(TAG, "enter elevator timer out !!");
                    openDoorFail(controlUtil.inElevatorRange());
                }
            }
        }, ENTER_FAIL_TIME);
    }

    /**
     * 判断是否是 持续去电梯中心事件 或 已经在电梯中时间 触发的该状态
     */
    private boolean isContinueToCenter() {
        return mStateData.getActionCode() == Definition.ElevatorAction.ACTION_CONTINUE_TO_CENTER
                || mStateData.getActionCode() == Definition.ElevatorAction.ACTION_ALREADY_INSIDE_ELEVATOR;
    }

    /**
     * 导航去电梯中心点
     */
    private void naviToElevatorCenter() {
        NavigationAdvancedBean bean = cloneNaviBean();
        bean.setDestination(elevatorCenterName);
        /**
         * 这里不需要回正到建图角度，原因是避免机器在电梯中心附近时执行导航不会回正，不回正会导致定位失败或定位不准
         * 这里导航之后，会主动回正，不需要再导航回正
         */
        bean.setAdjustAngle(true);  //到目的地后，不回正到建图角度
//        double distanceToFrontEnter = controlUtil.getToFrontDistanceEnter();
        double distanceToFrontEnter = 0.3;
        Log.d(TAG, "naviToElevatorCenter distanceToFrontEnter: " + distanceToFrontEnter);
        //是否已在当前点位判断的半径值，在梯箱内即认为已经在中心点位，导航前判断是否已经在中心点位使用
        bean.setCoordinateDeviation(distanceToFrontEnter);
        //导航到达范围，到达电梯箱内即可，不必挤到真正的中心位置
        bean.setDestinationRange(distanceToFrontEnter);
        bean.setLinearSpeed(ELEVATOR_NAVI_LINEAR_SPEED);
        bean.setAngularSpeed(ELEVATOR_NAVI_ANGULAR_SPEED);
        bean.setObsDistance(3.0);//进电梯时，避障距离设置大一点，避免遇到障碍物停不下来
        startNavigation(bean);
    }

    /**
     * 重试导航去电梯中心点
     *
     */
    private void naviRetry() {
        naviFailCount++;
        //重试导航次数用尽，且状态不是一直导航去电梯中心，即机器人还在电梯外
        if (naviFailCount >= NAVI_FAIL_MAX_COUNT
                && mStatus != Status.CONTINUE_GO_CENTER) {
            //则需要返回电梯口
            //这里不用判断电梯状态，不是一直导航去电梯中心就说明机器人还在电梯外
            //controlUtilListener会收到电梯状态
            Log.d(TAG, "navi fail backToElevatorGate , state: " + mStatus);
            backToElevatorGate(); //naviRetry，导航失败且在电梯外，回到电梯口
            return;
        }
        //已经是回电梯口状态，不需要再导航
        //这里可能是controlUtilListener收到了异常，更新的回电梯口状态
        if (mStatus == Status.BACK_TO_GATE) {
            Log.d(TAG, "already in BACK_TO_GATE state");
            return;
        }
        //重试去电梯中心
        Log.d(TAG, "retry to elevator center,retryCount: " + naviFailCount + "  state: " + mStatus);
        naviToElevatorCenter();//naviRetry，重试导航去电梯中心，导航失败且在电梯内
    }

    /**
     * 导航成功到达电梯中心
     * 需要回正方向
     */
    private void naviSuccess() {
        Log.d(TAG, "naviSuccess");

        //关门
        cancelOpenElevatorDoor(); //arrived pose
        updateCurrentStatus(Status.ROTATE);
        onStatusUpdate(Definition.STATUS_ARRIVED_ELEVATOR_CENTER, "Arrived elevator center");
        resumeRobotTheta(); //归正机器人朝向与建图时方向相同
    }

    /**
     * 发送归正方向命令
     * 归正机器人朝向与建图时方向相同
     */
    private void resumeRobotTheta() {
        mApi.resumeSpecialPlaceTheta(mReqId, elevatorCenterName);
    }

    /**
     * 处理归正机器人朝向
     *
     * @param result true：进梯成功；
     *               false：重新归正
     */
    private void parseResumeRobotTheta(String result) {
        if (TextUtils.equals(result, Definition.RESULT_TRUE)
                || TextUtils.equals(result, Definition.SPECIAL_THETA_ALREADY_SUCCESS_NO_NEED_TO_RESUME)) {
            Log.d(TAG, "resume orientation suc");
            enterElevatorSuc();
            //记录当前pose和电梯中心偏移量，用于切图后强制重定位
            saveDeltaPoseEnter();
        } else {
            Log.d(TAG, "resume orientation retry");
            resumeRetry();
        }
    }

    private void resumeRetry() {
        resumeFailCount++;
        if (resumeFailCount > RESUME_FAIL_MAX_COUNT
                && mStatus == Status.ROTATE) {
            updateCurrentStatus(Status.CONTINUE_ROTATE);
            //通知用户：失败回正方向失败n次
            onStatusUpdate(Definition.STATUS_UPDATE_RETRY_ERROR, Definition.STATUS_RESUME_TIME_OUT);
        }
        resumeRobotTheta();//重试回正
    }

    private void updateCurrentStatus(Status status) {
        if (mStatus == status) {
            Log.d(TAG, "already in " + status);
            return;
        }

        Log.d(TAG, "updateCurrentStatus " + this.mStatus + " --> " + status);
        this.mStatus = status;
        //当状态更新为到达电梯中心、正在回正朝向或持续去电梯中心时
        //不需要再控制电梯门开
        if (mStatus == Status.ARRIVED
                || mStatus == Status.ROTATE
                || mStatus == Status.CONTINUE_ROTATE
                || mStatus == Status.CONTINUE_GO_CENTER) {
            cancelOpenElevatorDoor();//停止控制开门
        }

        //上报强制导航去电梯中心事件
        if (mStatus == Status.CONTINUE_GO_CENTER) {
            onStatusUpdate(Definition.STATUS_UPDATE_RETRY_ERROR, Definition.STATUS_CONTINUE_TO_CENTER);
        }
    }


    //  ----------------  梯控  ----------------  //

    /**
     * 关电梯门
     */
    private void cancelOpenElevatorDoor() {
        if (null != controlUtil) {
//            controlUtil.releaseElevatorControl();//这里只是要关门，并不是要是释放电梯
            controlUtil.cmdCloseElevatorDoor();
            controlUtil.stopControlElevator();
        }
    }

    /**
     * 开门失败次数过多
     * 需要判断是否要回到电梯口点
     */
    private void openDoorFail(boolean inElevatorRange) {
        Log.d(TAG, "openDoorFail inElevatorRange: " + inElevatorRange + " , status: " + mStatus);
        //如果已经到了电梯中心
        if (mStatus == Status.ROTATE
                || mStatus == Status.CONTINUE_ROTATE
                || mStatus == Status.ARRIVED) {
            cancelOpenElevatorDoor();    //关门不释放电梯
            Log.d(TAG, "openDoorFail arrived elevator center,release elevator.currentStatus: " + mStatus);
            return;
        }
        if (inElevatorRange) {
            //在电梯范围，且控制开门失败次数过多
            //由于不知道电梯现在状态是否异常了，不再尝试出梯，
            //直接让机器人一直尝试去中心
            Log.d(TAG, "openDoor fail go center");
            updateCurrentStatus(Status.CONTINUE_GO_CENTER);
            naviToElevatorCenter(); //控梯失败，且还在电梯里，导航去电梯中心
            return;
        }
        Log.d(TAG, "openDoor fail backToElevatorGate , state: " + mStatus);
        backToElevatorGate();//openDoorFail，不在电梯范围，回到电梯口
    }

    /**
     * 进梯失败，回到电梯口等待重试
     */
    private void backToElevatorGate() {
        updateCurrentStatus(Status.BACK_TO_GATE);
        updateAction(Definition.ElevatorAction.ACTION_NAVI_FAIL);
        onStatusUpdate(Definition.STATUS_START_BACK_TO_ELEVATOR_GATE, "back to elevator gate");
        onSuccess();
    }

    /**
     * 进梯成功，更新下一状态
     */
    private void enterElevatorSuc() {
        updateCurrentStatus(Status.ARRIVED);
        updateAction(Definition.ElevatorAction.ACTION_NAVI_SUC);
        onStatusUpdate(Definition.STATUS_ARRIVED_ELEVATOR_CENTER_CORRECT_ORIENTATION, "correct orientation");
        onSuccess();
    }

    /**
     * 保持开门
     */
    private void continueOpenDoor() {
        if (null != controlUtil) {
            controlUtil.startOpenDoor();
        }
    }

    /**
     * 控制电梯开门时状态监听
     */
    private final ElevatorControlUtilListener elevatorControlUtilListener = new ElevatorControlUtilListener() {
        @Override
        public void onElevatorStatusUpdate(int id, String param, String extraData) {

        }

        @Override
        public void onElevatorResult(int id, String param) {
            onElevatorCmdResult(id, param);
        }

        @Override
        public void onSuccess() {

        }
    };

    private void onElevatorCmdResult(int id, String param) {
        Log.d(TAG, "onElevatorCmdResult status:" + id + " , data:" + param);
        switch (id) {
            case Definition.ERROR_ELEVATOR_CONTROL:
            case Definition.ERROR_CALL_ELEVATOR_FAILED:
            case Definition.ERROR_CLOSE_ELEVATOR_DOOR_FAILED:
            case Definition.ERROR_OPEN_ELEVATOR_DOOR_FAILED:
                if (!isContinueToCenter()) {
                    stopNavigation();   //不是要一直导航去电梯中心 停止导航
                }
                openDoorFail(controlUtil.inElevatorRange());
                break;

            default:
                onResult(id, param);
                break;
        }
    }

}
