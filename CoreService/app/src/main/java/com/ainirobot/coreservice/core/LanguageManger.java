/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.core;

import android.database.ContentObserver;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.actionbean.LanguageBean;
import com.ainirobot.coreservice.core.status.LocalSubscriber;
import com.ainirobot.coreservice.data.language.LanguageDataHelper;
import com.ainirobot.coreservice.data.language.LanguageVersionHelper;
import com.ainirobot.coreservice.service.CoreService;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Orion on 2021/3/16.
 */
public class LanguageManger {
    private static final String TAG = LanguageManger.class.getSimpleName();
    private CoreService mCore;
    private Gson mGson;
    private LanguageDataHelper mLanguageDataHelper;
    private LanguageVersionHelper mLanguageVersionHelper;

    public LanguageManger(CoreService context) {
        this.mCore = context;
        this.mGson = new Gson();
        this.mLanguageDataHelper = new LanguageDataHelper(context);
        this.mLanguageVersionHelper = new LanguageVersionHelper(context);
        Log.i(TAG, "LanguageManger: Language config version=" +
                mLanguageVersionHelper.getVersion());
        initDbObserver();
        initStatusListener();
    }

    private void initStatusListener() {
        mCore.getStatusManager().registerStatusListener(
                Definition.STATUS_GET_REMOTE_LANGUAGE_SUCCESS, new LocalSubscriber() {
                    @Override
                    public void onStatusUpdate(String type, String data) {
                        Log.i(TAG, "onStatusUpdate: language data=" + data);
                        setServerLanguageList(data);
                    }
                });
    }

    private void initDbObserver() {
        mLanguageDataHelper.registerListener(new ContentObserver(null) {
            @Override
            public void onChange(boolean selfChange, Uri uri) {
                Log.i(TAG, "Language-onChange: selfChange=" + selfChange + " uri=" + uri);
                List<LanguageBean> languageBeans = getLocalAndServerSupportLanguageList();
                if (languageBeans != null && languageBeans.size() > 0) {
                    notifySupportLanguageChange(languageBeans);
                }
            }
        });
    }

    private void notifySupportLanguageChange(List<LanguageBean> beans) {
        mCore.getStatusManager().handleStatus(RobotOS.SYSTEM_SERVICE,
                Definition.STATUS_SUPPORT_LANGUAGE_CHANGE,
                mGson.toJson(beans));
    }

    /**
     * 本地支持的系统语言全集
     *
     * @return
     */
    public List<LanguageBean> getLocalSupportLanguageList() {
        List<LanguageBean> allLanguageList = getAllLanguageList();
        if (allLanguageList == null || allLanguageList.size() <= 0) {
            Log.d(TAG, "getLocalSupportLanguageList: Has no any language!");
            return null;
        }
        Log.d(TAG, "getLocalSupportLanguageList: allLanguageList=" + allLanguageList.toString());
        List<LanguageBean> languageLocal = new ArrayList<>();
        for (LanguageBean bean : allLanguageList) {
            if (bean.isLocalSupport()) {
                languageLocal.add(bean);
            }
        }
        Log.d(TAG, "getLocalSupportLanguageList: languageLocal=" + languageLocal.toString());
        return languageLocal;
    }

    /**
     * 服务都按支持的系统语言全集
     *
     * @return
     */
    public List<LanguageBean> getServerSupportLanguageList() {
        List<LanguageBean> languageBeans = getAllLanguageList();
        if (languageBeans == null || languageBeans.size() <= 0) {
            return null;
        }
        List<LanguageBean> languageServer = new ArrayList<>();
        for (LanguageBean bean : languageBeans) {
            if (bean.isServerSupport()) {
                languageServer.add(bean);
            }
        }
        return languageServer;
    }

    /**
     * 本地和服务端支持列表的交集
     *
     * @return
     */
    public List<LanguageBean> getLocalAndServerSupportLanguageList() {
        List<LanguageBean> languageBeans = getAllLanguageList();
        if (languageBeans == null || languageBeans.size() <= 0) {
            return null;
        }
        List<LanguageBean> languageLocalAndServer = new ArrayList<>();
        for (LanguageBean bean : languageBeans) {
            if (bean.isSupportLocalAndServer()) {
                languageLocalAndServer.add(bean);
            }
        }
        return languageLocalAndServer;
    }

    /**
     * 获取默认主语言
     *
     * @return
     */
    public LanguageBean getSupportDefaultLanguage() {
        List<LanguageBean> languageBeans = getLocalAndServerSupportLanguageList();
        if (languageBeans == null || languageBeans.size() <= 0) {
            return null;
        }
        for (LanguageBean bean : languageBeans) {
            if (bean.isDefaultLanguage()) {
                return bean;
            }
        }
        return null;
    }

    public List<LanguageBean> getAllLanguageList() {
        return mLanguageDataHelper.getAllLanguageList();
    }

    public boolean setServerLanguageList(String params) {
        if (TextUtils.isEmpty(params)) {
            Log.e(TAG, "setServerLanguageList: Params null error!");
            return false;
        }
        TypeToken<List<LanguageBean>> list = new TypeToken<List<LanguageBean>>() {
        };
        List<LanguageBean> languageBeans = mGson.fromJson(params, list.getType());
        return setServerLanguageList(languageBeans);
    }

    /**
     * 更新服务端支持系统语言列表信息
     *
     * @param serverList
     * @return
     */
    private boolean setServerLanguageList(List<LanguageBean> serverList) {
        if (serverList == null || serverList.size() <= 0) {
            Log.e(TAG, "setServerLanguageList: Server list null error!");
            return false;
        }
        Log.d(TAG, "setServerLanguageList: serverList=" + serverList.toString());
        List<LanguageBean> localList = getLocalSupportLanguageList();
        if (localList == null || localList.size() <= 0) {
            Log.e(TAG, "setServerLanguageList: Local list null error!");
            return false;
        }
        //delete all
        mLanguageDataHelper.deleteAll();
        //insert local support language
        for (LanguageBean localBean : localList) {
            localBean.setIsDefault(LanguageBean.UseState.NOT_DEFAULT);
            localBean.setSupport(LanguageBean.SupportState.LOCAL);
            mLanguageDataHelper.insert(localBean);
        }
        //insert server support language
        String defaultLanguageCode = "";
        for (LanguageBean serverBean : serverList) {
            if (serverBean.isDefaultLanguage()) {
                defaultLanguageCode = serverBean.getLangCode();
            }
            boolean isInLocal = false;
            for (LanguageBean localBean : localList) {
                if (localBean.getLangCode().equals(serverBean.getLangCode())) {
                    isInLocal = true;
                    continue;
                }
            }
            Log.d(TAG, "setServerLanguageList:  isInLocal=" + isInLocal
                    + (isInLocal ? " Update" : " Insert") + " serverBean=" + serverBean.toString());
            if (isInLocal) {
                mLanguageDataHelper.updateSupportState(serverBean.getLangCode(),
                        LanguageBean.SupportState.LOCAL_AND_SERVER);
            } else {
                serverBean.setSupport(LanguageBean.SupportState.SERVER);
                mLanguageDataHelper.insert(serverBean);
            }
        }
        Log.d(TAG, "setServerLanguageList: defaultLanguageCode=" + defaultLanguageCode);
        //set default language
        mLanguageDataHelper.setAllLanguageNotDefault();
        if (!TextUtils.isEmpty(defaultLanguageCode)) {
            mLanguageDataHelper.setSpecialLanguageDefault(defaultLanguageCode);
        }
        printAllLanguageData();
        return true;
    }

    private void printAllLanguageData() {
        List<LanguageBean> all = mLanguageDataHelper.getAllLanguageList();
        if (all == null || all.size() <= 0) {
            Log.i(TAG, "printAllLanguageData: NULL");
        } else {
            Log.i(TAG, "printAllLanguageData: ---start");
            for (LanguageBean bean : all) {
                Log.d(TAG, "printAllLanguageData: " + bean.toString());
            }
            Log.i(TAG, "printAllLanguageData: ---end");
        }
    }

}
