package com.ainirobot.coreservice.config.core;

import android.util.Log;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import java.util.Map;

public class HeadConfig extends BaseConfig {

    private static final String TAG = HeadConfig.class.getSimpleName();

    private boolean isSupportHorizontal270;
    private boolean isSupportHorizontal;
    private boolean isSupportVertical;
    private int defaultHorizontalAngle;
    private int defaultVerticalAngle;
    private int maxVerticalAngle;
    private int minVerticalAngle;
    private int maxHorizontalAngle;
    private int minHorizontalAngle;
    private int min270HorizontalAngle;
    private int maxSpeed;
    private int defaultSpeed;
    private int verticalAngleOffset;

    public HeadConfig(JsonObject jsonObject) {
        super(jsonObject, true);
    }

    public void parseConfig(JsonObject config) {
        isSupportHorizontal270 = true;
        isSupportHorizontal = true;
        isSupportVertical = true;
        defaultHorizontalAngle = 0;
        defaultVerticalAngle = 70;
        maxVerticalAngle = 83;
        minVerticalAngle = 50;
        maxHorizontalAngle = 80;
        minHorizontalAngle = -80;
        min270HorizontalAngle = -190;
        maxSpeed = 90;
        defaultSpeed = 30;
        verticalAngleOffset = 45;
        if (config == null) {
            Log.d(TAG, "parseConfig config null");
            return;
        }
        for (Map.Entry<String, JsonElement> entry : config.entrySet()) {
            String enKey = ConfigDictionary.parseHeadConfig(entry.getKey());
            switch (enKey) {
                case "isSupportHorizontal270":
                    isSupportHorizontal270 = entry.getValue().getAsBoolean();
                    break;
                case "isSupportHorizontal":
                    isSupportHorizontal = entry.getValue().getAsBoolean();
                    break;
                case "isSupportVertical":
                    isSupportVertical = entry.getValue().getAsBoolean();
                    break;
                case "defaultHorizontalAngle":
                    defaultHorizontalAngle = entry.getValue().getAsInt();
                    break;
                case "defaultVerticalAngle":
                    defaultVerticalAngle = entry.getValue().getAsInt();
                    break;
                case "maxVerticalAngle":
                    maxVerticalAngle = entry.getValue().getAsInt();
                    break;
                case "minVerticalAngle":
                    minVerticalAngle = entry.getValue().getAsInt();
                    break;
                case "maxHorizontalAngle":
                    maxHorizontalAngle = entry.getValue().getAsInt();
                    break;
                case "minHorizontalAngle":
                    minHorizontalAngle = entry.getValue().getAsInt();
                    break;
                case "min270HorizontalAngle":
                    min270HorizontalAngle = entry.getValue().getAsInt();
                    break;
                case "maxSpeed":
                    maxSpeed = entry.getValue().getAsInt();
                    break;
                case "defaultSpeed":
                    defaultSpeed = entry.getValue().getAsInt();
                    break;
                case "verticalAngleOffset":
                    verticalAngleOffset = entry.getValue().getAsInt();
                    break;
                default:
                    break;
            }
        }
        Log.d(TAG, toString());
    }

    public boolean isSupportHorizontal270() {
        return isSupportHorizontal270;
    }

    public boolean isSupportHorizontal() {
        return isSupportHorizontal;
    }

    public boolean isSupportVertical() {
        return isSupportVertical;
    }

    public int getDefaultHorizontalAngle() {
        return defaultHorizontalAngle;
    }

    public int getDefaultSpeed() {
        return defaultSpeed;
    }

    public int getVerticalAngleOffset() {
        return verticalAngleOffset;
    }

    public int getDefaultVerticalAngle() {
        return defaultVerticalAngle;
    }

    public int getMaxHorizontalAngle() {
        return maxHorizontalAngle;
    }

    public int getMaxSpeed() {
        return maxSpeed;
    }

    public int getMaxVerticalAngle() {
        return maxVerticalAngle;
    }

    public int getMin270HorizontalAngle() {
        return min270HorizontalAngle;
    }

    public int getMinHorizontalAngle() {
        return minHorizontalAngle;
    }

    public int getMinVerticalAngle() {
        return minVerticalAngle;
    }

    @Override
    public String toString() {
        return "HeadConfig{"
                + "isEnable='" + isEnable + '\''
                + ", isSupportHorizontal270=" + isSupportHorizontal270 + '\''
                + ", isSupportHorizontal=" + isSupportHorizontal + '\''
                + ", isSupportVertical=" + isSupportVertical + '\''
                + ", defaultHorizontalAngle=" + defaultHorizontalAngle + '\''
                + ", defaultVerticalAngle=" + defaultVerticalAngle + '\''
                + ", maxVerticalAngle=" + maxVerticalAngle + '\''
                + ", minVerticalAngle=" + minVerticalAngle + '\''
                + ", maxHorizontalAngle=" + maxHorizontalAngle + '\''
                + ", minHorizontalAngle=" + minHorizontalAngle + '\''
                + ", min270HorizontalAngle=" + min270HorizontalAngle + '\''
                + ", maxSpeed=" + maxSpeed + '\''
                + ", defaultSpeed=" + defaultSpeed + '\''
                + ", verticalAngleOffset=" + verticalAngleOffset + '\''
                + '}';
    }
}
