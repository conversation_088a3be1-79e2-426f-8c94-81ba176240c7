package com.ainirobot.coreservice.action.advnavi.elevatorcmd;

import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.action.advnavi.state.BaseState;
import com.ainirobot.coreservice.action.advnavi.util.ElevatorLocationUtils;
import com.ainirobot.coreservice.bean.ElevatorRangeInfo;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.google.gson.Gson;

import org.json.JSONObject;

/**
 * Data: 2022/8/19 14:15
 * Author: wanglijing
 * Description: ElevatorControlUtil
 * <p>
 * 流程为：
 * 开门/关门 -fail-> 呼梯 -fail-> 释放电梯
 * 开门/关门 <-suc- 呼梯 <-suc- 释放电梯
 * <p>
 * ERROR_OPEN_ELEVATOR_DOOR_FAILED：控制开门失败
 * ERROR_NOT_FOUND_ELEVATOR_PATH：电梯范围信息错误
 * ERROR_NOT_ESTIMATE：机器人未定位
 */
public class ElevatorControlUtil implements ElevatorCommandInterface {
    private String TAG = ElevatorCommand.class.getSimpleName();
    private final StateData mStateData;
    private final boolean isEnterElevator;  //是否为进电梯状态
    private final ElevatorControlUtilListener mListener;
    private final Gson mGson = new Gson();
    /**
     * 呼叫电梯时的楼层映射
     */
    private final int floorIndex;
    private final ElevatorLocationUtils locationUtils;
    private ElevatorRangeInfo rangeInfo;
    private ElevatorCommand elevatorCommand;

    private volatile boolean isAlive = false;
    private final BaseState mBaseState;
    /**
     * 用户用了那个方法
     */
    private ElevatorCommand.CmdType intentType;

    /**
     * 控制电梯开门
     * 需设置stateData的callElevatorFloorIndex，即呼梯时的楼层映射
     * <p>
     *
     * @param floorIndex      楼层映射；
     *                        用于重试呼梯及判断电梯是否离开
     * @param isEnterElevator 是否为进电梯过程
     *                        影响判断是否在电梯内方法；
     * @param listener        listener
     */
    public ElevatorControlUtil(@NonNull BaseState state,
                               int floorIndex,
                               boolean isEnterElevator,
                               ElevatorControlUtilListener listener) {
        this.mBaseState = state;
        this.mListener = listener;
        this.floorIndex = floorIndex;
        this.isEnterElevator = isEnterElevator;
        this.mStateData = mBaseState.mStateData;

        if (mStateData == null) {
            Log.e(TAG, "ElevatorControlUtil stateData is null");
            if (null != mListener) {
                onResult(Definition.ERROR_PARAMS_JSON_PARSER_ERROR, "ElevatorControlUtil stateData is null");
            }
            locationUtils = null;
            return;
        }

        Log.d(TAG, "\n" + mBaseState.TAG + "\n\n");
        TAG = mBaseState.TAG + "-" + TAG;
        locationUtils = new ElevatorLocationUtils();
    }

    /**
     * 控制持续呼梯
     * <p>
     * listener result
     * * RESULT_OK：呼梯流程完成,电梯到达并开门
     * * other：其他错误，呼梯重试n次后还是失败上报的错误
     * <p>
     * 流程：
     * 呼梯 -fail-> 释放电梯
     * 呼梯 <-suc- 释放电梯
     */
    public void startCallElevator() {

        if (elevatorCommand != null && elevatorCommand instanceof CmdCallElevator) {
            if (elevatorCommand.isAlive) {
                Log.d(TAG, "startCallElevator cmd already start !");
                return;
            }
        }

        stopControlElevator();
        commandStart(ElevatorCommand.CmdType.CALL_FLOOR);
        elevatorCommand = new CmdCallElevator(
                TAG,
                intentType,
                this,
                cmdListener);
        elevatorCommand.start();
    }

    /**
     * 控制持续开门
     * <p>
     * listener result
     * * other：其他错误
     * <p>
     * 流程：
     * 开门 -fail-> 呼梯 -fail-> 释放电梯
     * 开门 <-suc- 呼梯 <-suc- 释放电梯
     */
    public void startOpenDoor() {
        if (elevatorCommand != null && elevatorCommand instanceof CmdOpenElevatorDoor) {
            if (elevatorCommand.isAlive) {
                Log.d(TAG, "startOpenDoor cmd already start !");
                return;
            }
        }
        stopControlElevator();
        commandStart(ElevatorCommand.CmdType.OPEN_DOOR);
        elevatorCommand = new CmdOpenElevatorDoor(
                TAG,
                intentType,
                this,
                cmdListener);
        elevatorCommand.start();
    }

    /**
     * 控制关门
     * <p>
     * listener result
     * * other：其他错误
     * <p>
     * 流程：
     * 关门 -fail-> 呼梯 -fail-> 释放电梯
     * 关门 <-suc- 呼梯 <-suc- 释放电梯
     * <p>
     * 实测效果并不好，现改为直接释放电梯
     */
    public void startCloseDoor() {
        if (elevatorCommand != null && elevatorCommand instanceof CmdCloseElevatorDoor) {
            if (elevatorCommand.isAlive) {
                Log.d(TAG, "startCloseDoor cmd already start !");
                return;
            }
        }
        stopControlElevator();
        commandStart(ElevatorCommand.CmdType.CLOSE_DOOR);
        elevatorCommand = new CmdCloseElevatorDoor(
                TAG,
                intentType,
                this,
                cmdListener);
        elevatorCommand.start();
    }

    /**
     * 释放电梯控制
     * 停止正在进行的命令，并释放电梯
     */
    public void releaseElevatorControl() {
        if (null != elevatorCommand) {
            Log.d(TAG, "releaseElevator elevatorCommand.isAlive=" + elevatorCommand.isAlive);
            elevatorCommand.stop();
        }
        cmdReleaseElevator();
    }

    /**
     * 停止持续控制
     * 一般和释放电梯一起调用
     */
    public void stopControlElevator() {
        isAlive = false;
        if (null != elevatorCommand && elevatorCommand.isAlive) {
            Log.d(TAG, "stop control elevator cmd");
            elevatorCommand.stop();
        }
    }


    private void commandStart(ElevatorCommand.CmdType cmdType) {
        isAlive = true;
        rangeInfo = getElevatorRange(mStateData.getElevatorName());
        updateIntent(cmdType);
        Log.d(TAG, "command " + cmdType.toString() + " start");
    }

    private void updateIntent(ElevatorCommand.CmdType intentType) {
        this.intentType = intentType;
    }


    private boolean isAlive() {
        return isAlive;
    }

    /**
     * 需要透传 CMD_CONTROL_OPEN_ELEVATOR_DOOR 和 CMD_CONTROL_CALL_ELEVATOR 的响应信息
     */
    public boolean cmdResponse(String command, String result, String extraData) {
        if (null != elevatorCommand) {
            switch (command) {
                case Definition.CMD_CONTROL_CALL_ELEVATOR:
                case Definition.CMD_CONTROL_OPEN_ELEVATOR_DOOR:
                case Definition.CMD_CONTROL_CLOSE_ELEVATOR_DOOR:
                case Definition.CMD_CONTROL_RELEASE_ELEVATOR:
                    if (!isAlive()) {
                        Log.i(TAG, "elevator command not alive");
                        return false;
                    }
                    elevatorCommand.cmdResponse(command, result, extraData);
                    return true;
            }
        }
        return false;
    }

    /**
     * 判断是否在电梯范围内
     * 执行结果回调onResult
     *
     * @return true:在该电梯轿厢范围内
     */
    public boolean inElevatorRange() {
        return inElevatorRange(true);
    }

    /**
     * 判断是否在电梯范围内
     * 不执行结果回调onResult
     */
    public boolean inElevatorRangeNoResult() {
        return inElevatorRange(false);
    }

    /**
     * 判断是否在电梯范围内
     *
     * @param invokeResult 是否执行结果回调onResult
     * @return true:在该电梯轿厢范围内
     */
    private boolean inElevatorRange(boolean invokeResult) {
        if (null == locationUtils) {
            if (invokeResult)
                onResult(Definition.ERROR_NOT_FOUND_ELEVATOR_PATH, "locationUtils is null");
            return false;
        }

        Pose currentPose = getCurrentPose();
        if (null == rangeInfo) {
            rangeInfo = getElevatorRange(mStateData.getElevatorName());
        }
        if(currentPose == null || rangeInfo == null){
            if (invokeResult)
                onResult(Definition.ERROR_PARAMETER, ((currentPose == null) ? "current pose " : "rangeInfo ") + "is null");
            return false;
        }
        ElevatorLocationUtils.LocationState state = locationUtils.relativeElevatorPosition(
                isEnterElevator,
                currentPose,
                mStateData.getElevatorCenterPose(),
                rangeInfo);
        switch (state) {
            case NOT_ESTIMATE:
            case TIMEOUT:
                if (!isEstimate()) {
                    if (invokeResult)
                        onResult(Definition.ERROR_NOT_ESTIMATE, "");
                }
                return false;
            case INSIDE_ELEVATOR:
                return true;
            case OUTSIDE_ELEVATOR:
                return false;
        }
        return false;
    }

    private boolean isEstimate() {
        return TextUtils.equals(getRobotInfo(Definition.SYNC_ACTION_IS_ROBOT_ESTIMATE), Definition.RESULT_TRUE);
    }


    private void parseCommandstatus(int status, String data, String extraData) {
        onStatusUpdate(status, data, extraData);
    }

    private void parseCommandFinish(int status, String data) {
        if (isCmdSuc(ElevatorCommand.CmdType.RELEASE_ELEVATOR, status, data)) {
            Log.d(TAG, "release elevator suc");
            return;
        }
        if (isCmdSuc(intentType, status, data)) {
            Log.d(TAG, intentType.toString() + " success");
            onSuccess();
            return;
        }
        switch (status) {
            case Definition.ERROR_OPEN_ELEVATOR_DOOR_FAILED:
                boolean isInElevator = inElevatorRange();
                Log.i(TAG, "Result Open Door Fail, inElevator: " + isInElevator);
                //上报开门失败错误，并判断是否在电梯内
                onResult(Definition.ERROR_OPEN_ELEVATOR_DOOR_FAILED, "Open Door Fail");
                break;
            default:
                onResult(status, data);
                break;
        }
    }

    private final ElevatorCommandListener cmdListener = new ElevatorCommandListener() {
        @Override
        public void onStatusUpdate(int status, String data, String extraData) {
            parseCommandstatus(status, data, extraData);
        }

        @Override
        public void onFinish(int status, String data) {
            parseCommandFinish(status, data);
        }
    };

    //当前点位信息
    private Pose getCurrentPose() {
        try {
            String poseInfo = getRobotInfo(Definition.SYNC_ACTION_GET_CURRENT_LOCATION);
            return mGson.fromJson(poseInfo, Pose.class);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    private ElevatorRangeInfo getElevatorRange(String elevatorName) {
        String rangeInfo = getRobotInfo(Definition.SYNC_ACTION_GET_ELEVATOR_RANGE, elevatorName);
        try {
            return mGson.fromJson(rangeInfo, ElevatorRangeInfo.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private String getRobotInfo(int id) {
        return getRobotInfo(id, "");
    }

    private String getRobotInfo(int id, String param) {
        if (null != mBaseState) {
            return mBaseState.getRobotInfo(id, param);
        }
        return null;
    }

    private void onStatusUpdate(int status, String data, String extraData) {
        if (null != mListener) {
            mListener.onElevatorStatusUpdate(status, data, extraData);
        }
    }

    private void onSuccess() {
        if (null != mListener) {
            mListener.onSuccess();
        }
    }

    private void onResult(int id, String param) {
        if (null != mListener) {
            mListener.onElevatorResult(id, param);
        }
    }

    public boolean isCmdSuc(ElevatorCommand.CmdType cmdType, int status, String extraData) {
        try {
            if (status == Definition.RESULT_OK) {
                JSONObject object = new JSONObject(extraData);
                String type = object.getString(Definition.JSON_CMD_TYPE);
                return TextUtils.equals(type, cmdType.toString());
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 电梯中心点位，目标点范围改为整个电梯箱，range参数设置为电梯中心到电梯口的距离减去机器半径
     * @return
     */
    public double getToFrontDistanceEnter() {
        if (null == rangeInfo) {
            rangeInfo = getElevatorRange(mStateData.getElevatorName());
        }
        if (null == rangeInfo) {
            return 0;
        }
        return locationUtils.getToFrontDistance(true, rangeInfo.getToFront());
    }

    @Override
    public void cmdCallElevator() {
        if (null != mBaseState && isAlive()) {
            boolean isInElevator = inElevatorRange();
            int originFloor = mStateData.getCurrentFloorIndex();
            int targetFloor = mStateData.getTargetFloorIndex();
            Log.d(TAG, "cmdCallElevator: originFloor = " + originFloor
                    + ", targetFloor = " + targetFloor + ", isInElevator = " + isInElevator);
            mBaseState.callElevator(isInElevator, originFloor, targetFloor);
//            mBaseState.callElevator(floorIndex);
        }
    }

    @Override
    public void cmdOpenElevatorDoor() {
        if (null != mBaseState && isAlive()) {
            mBaseState.openElevatorDoor();
        }
    }

    @Override
    public void cmdCloseElevatorDoor() {
        if (null != mBaseState && isAlive()) {
            mBaseState.closeElevatorDoor();
        }
    }

    @Override
    public void cmdReleaseElevator() {
        Log.d(TAG, "cmdReleaseElevator mBaseState =" + mBaseState);
        if (null != mBaseState) {
            mBaseState.releaseElevator();
        }
    }

    @Override
    public int getTargetFloorIndex() {
//        return floorIndex;
        boolean isInElevator = inElevatorRange();
        int originFloor = mStateData.getCurrentFloorIndex();
        int targetFloor = mStateData.getTargetFloorIndex();
        Log.d(TAG, "cmdCallElevator: originFloor = " + originFloor
                + ", targetFloor = " + targetFloor + ", isInElevator = " + isInElevator);
        return isInElevator? targetFloor : originFloor;
    }
}
