package com.ainirobot.coreservice.core.account;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.util.Log;

import com.ainirobot.coreservice.core.apiproxy.impl.token.TokenApiProxy;
import com.ainirobot.coreservice.core.apiproxy.result.BaseApiResult;
import com.ainirobot.coreservice.data.account.MemberDataHelper;
import com.ainirobot.coreservice.service.CoreService;

import static com.ainirobot.coreservice.core.apiproxy.BaseApiProxy.MSG_API_RESULT;

class TokenManager {

    private static final String TAG = TokenManager.class.getSimpleName();

    private MemberDataHelper mDataHelper;
    private TokenApiProxy mApiProxy;

    TokenManager(CoreService core) {
        HandlerThread thread = new HandlerThread("TokenManager");
        thread.start();
        TokenHandler handler = new TokenHandler(this, thread.getLooper());
        mDataHelper = MemberDataHelper.getInstance(core);
        mApiProxy = new TokenApiProxy(core, handler);
        mApiProxy.registerSystemTokenChangeListener();
        mDataHelper.updateLocalSystemToken(mApiProxy.getSystemToken());
    }

    public static class TokenHandler extends Handler {

        private TokenManager mManager;

        private TokenHandler(TokenManager manager, Looper looper) {
            super(looper);
            mManager = manager;
        }

        @Override
        public void handleMessage(Message msg) {
            Log.d(TAG, "handleMessage what: " + msg.what);
            switch (msg.what) {
                case MSG_API_RESULT:
                    mManager.handleApiResult((BaseApiResult) msg.obj);
                    break;
                default:
                    break;
            }
        }
    }

    private void handleApiResult(BaseApiResult apiResult) {
        Log.d(TAG, "handleApiResult apiResult: " + apiResult);
        BaseApiResult.Type type = apiResult.getType();
        switch (type) {
            case SYSTEM_TOKEN_CHANGED:
                mDataHelper.updateLocalSystemToken(mApiProxy.getSystemToken());
                break;
            default:
                break;
        }
    }
}
