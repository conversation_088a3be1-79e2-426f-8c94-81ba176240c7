package com.ainirobot.coreservice.action.advnavi.util;

import android.graphics.PointF;
import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.advnavi.bean.RoadGraph;
import com.ainirobot.coreservice.action.advnavi.bean.RoadGraphEdge;
import com.ainirobot.coreservice.action.advnavi.bean.RoadGraphNode;
import com.ainirobot.coreservice.action.advnavi.bean.RoadLineBean;
import com.ainirobot.coreservice.action.advnavi.bean.Vector2d;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.utils.Utils;
import com.google.gson.Gson;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;

/**
 * Data: 2023/1/30 17:17
 * Author: wanglijing
 * 巡线处理
 */
public class RoadLineUtils {
    private static final String TAG = "NaviAdvancedAction-" + RoadLineUtils.class.getSimpleName();
    private final static String MAP_DIR = "/robot/map";
    private final static String ROBOT_MAP_DIR = Environment.getExternalStorageDirectory() + MAP_DIR;
    private final static String FILE_ROAD_JSON = "road.json";
    private final Gson mGson = new Gson();
    private final List<RoadLineBean> mAllRoadLines = new ArrayList<>();

    /**
     * 加载巡线
     *
     * @param mapName 地图名
     */
    public synchronized List<RoadLineBean> loadRoadLineInfo(String mapName) {
        mAllRoadLines.clear();
        //巡线所有端点哈希表，便于根据 id 查找
        HashMap<Integer, RoadGraphNode> nodesMap = new HashMap<>();

        RoadGraph roadGraph = loadRoadGraphInfo(mapName);
        if (roadGraph == null) {
            return null;
        }

        try {
            List<RoadGraphNode> nodes = roadGraph.getNodes();
            //解析节点坐标
            for (RoadGraphNode node : nodes) {
                nodesMap.put(node.getId(), node);
            }

            List<RoadGraphEdge> edges = roadGraph.getEdges();
            //解析巡线线段信息
            for (RoadGraphEdge edge : edges) {
                RoadLineBean roadLineBean = new RoadLineBean();
                roadLineBean.setId(edge.getId());
                roadLineBean.setLineWidth(edge.getLine_width());
                roadLineBean.setStartPose(nodesMap.get(edge.getNode_start_id()));
                roadLineBean.setEndPose(nodesMap.get(edge.getNode_end_id()));

                mAllRoadLines.add(roadLineBean);
            }

            return mAllRoadLines;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<RoadLineBean> getAllRoadLines() {
        return mAllRoadLines;
    }

    /**
     * 在所有巡线中找到包含该点的巡线
     */
    public List<RoadLineBean> findRoadContentPose(Pose pose) {
        return findRoadContentPose(pose, mAllRoadLines);
    }

    /**
     * 找到包含该点的巡线
     *
     * @param pose          目标点
     * @param roadLineBeans 所有巡线线段
     * @return 包含该点的巡线数组，可能为null或size=0
     */
    public synchronized List<RoadLineBean> findRoadContentPose(Pose pose,
                                                               List<RoadLineBean> roadLineBeans) {
        if (pose == null || roadLineBeans == null || roadLineBeans.size() <= 0) {
            Log.i(TAG, "findRoadContentPose mRoadGraph is null");
            return null;
        }
        Log.d(TAG, "findRoadContentPose currentPose: " + pose);
        List<RoadLineBean> contentPoseMap = new ArrayList<>();
        for (RoadLineBean bean : roadLineBeans) {
            if (isPoseInRoad(pose, bean)) {
                contentPoseMap.add(bean);
            }
        }

        return new ArrayList<>(new HashSet<>(contentPoseMap));
    }

    /**
     * 判断点位是否在巡线内
     *
     * @param pose     坐标点
     * @param roadLine 线段
     */
    public boolean isPoseInRoad(Pose pose, RoadLineBean roadLine) {
        //edge to rect
        Vector2d[] vector2ds = getSegmentRectF(roadLine, roadLine.getLineWidth(), 0);
        PointF pointLT = new PointF((float) vector2ds[0].getX(), (float) vector2ds[0].getY());
        PointF pointRT = new PointF((float) vector2ds[1].getX(), (float) vector2ds[1].getY());
        PointF pointLB = new PointF((float) vector2ds[2].getX(), (float) vector2ds[2].getY());
        PointF pointRB = new PointF((float) vector2ds[3].getX(), (float) vector2ds[3].getY());
        PointF pointPose = new PointF((float) pose.getX(), (float) pose.getY());
        //judge
        boolean in = pointInRect(pointPose, pointLT, pointRT, pointLB, pointRB);
        Log.d(TAG, "isPoseInRoad in=" + in + " , roadId=" + roadLine.getId());
        return in;
    }

    /**
     * 根据线段，获取包裹该线段的矩形
     * 由于线段是有斜度的，直接用 RectF 无法绘制和获取区域范围，
     * 所以，这里使用该矩形的四个直角来代表示矩形区域
     */
    private Vector2d[] getSegmentRectF(RoadLineBean roadEdge, double innerLineWidth, double outerLineWidth) {
        RoadGraphNode startNode = roadEdge.getStartPose();
        double startX = startNode.getPosition().getX();
        double startY = startNode.getPosition().getY();
        RoadGraphNode endNode = roadEdge.getEndPose();
        double endX = endNode.getPosition().getX();
        double endY = endNode.getPosition().getY();
        return calculateRectangleAngles(startX, startY, endX, endY,
                innerLineWidth, outerLineWidth);
    }

    /**
     * 根据路径的端点(起点和终点)和宽度算出矩形 4 个直角的坐标
     *
     * @param x0             起点 x
     * @param y0             起点 y
     * @param x1             结束点 x
     * @param y1             结束点 y
     * @param innerLineWidth 被包裹的线宽度
     * @param outerLineWidth 外部矩形线宽度
     * @return
     */
    private Vector2d[] calculateRectangleAngles(double x0, double y0, double x1, double y1, double innerLineWidth, double outerLineWidth) {
        Vector2d[] angles = new Vector2d[4];
        angles[0] = new Vector2d();
        angles[1] = new Vector2d();
        angles[2] = new Vector2d();
        angles[3] = new Vector2d();

        double rectWidth = innerLineWidth + outerLineWidth;
        double len = Math.sqrt(Math.pow(x1 - x0, 2) + Math.pow(y1 - y0, 2));
        double a = 0;

        if (x0 == x1) {
            a = Math.PI / 2;
            if (y0 > y1) {
                a += Math.PI;
            }
        } else {
            a = Math.atan((y1 - y0) / (x1 - x0));
            if (x0 > x1) {
                a += Math.PI;
            }
        }

        double sx = x0 + (Math.sin(a) * rectWidth) / 2;
        double ex = sx + len;
        double sy = y0 - (Math.cos(a) * rectWidth) / 2;
        double ey = sy;

        double endx = x1 + (Math.sin(a) * rectWidth) / 2;
        double endy = y1 - (Math.cos(a) * rectWidth) / 2;

        double sux = x0 - (Math.sin(a) * rectWidth) / 2;
        double suy = y0 + (Math.cos(a) * rectWidth) / 2;

        double eux = x1 - (Math.sin(a) * rectWidth) / 2;
        double euy = y1 + (Math.cos(a) * rectWidth) / 2;

        angles[0].setX(sx);
        angles[0].setY(sy);
        angles[1].setX(endx);
        angles[1].setY(endy);
        angles[2].setX(sux);
        angles[2].setY(suy);
        angles[3].setX(eux);
        angles[3].setY(euy);

//        Log.d(TAG, "calculateRectangleAngles: sx=" + sx + " sy=" + sy
//                + " endx=" + endx + " endy=" + endy
//                + " sux=" + sux + " suy=" + suy
//                + " eux=" + eux + " euy=" + euy);
        return angles;
    }

    /**
     * 点是否落在四边形内
     *
     * @param curPoint 点击位置
     * @param pointLT  左上顶点
     * @param pointRT  右上顶点
     * @param pointLB  左下顶点
     * @param pointRB  右下顶点
     * @return true 在四边形内；false 不在四边形内
     */
    private boolean pointInRect(PointF curPoint, PointF pointLT, PointF pointRT, PointF pointLB, PointF pointRB) {
        int nCount = 4;
        PointF[] rectPoints = new PointF[]{pointLT, pointLB, pointRB, pointRT};
        int nCross = 0;
        for (int i = 0; i < nCount; i++) {
            //依次取相邻的两个点
            PointF pStart = rectPoints[i];
            PointF pEnd = rectPoints[(i + 1) % nCount];

            //相邻的两个点是平行于x轴的，肯定不相交，忽略
            if (pStart.y == pEnd.y)
                continue;

            //交点在pStart,pEnd的延长线上，忽略
            if (curPoint.y < Math.min(pStart.y, pEnd.y) || curPoint.y > Math.max(pStart.y, pEnd.y))
                continue;

            //求当前点和x轴的平行线与pStart,pEnd直线的交点的x坐标
            double x = (double) (curPoint.y - pStart.y) * (double) (pEnd.x - pStart.x) / (double) (pEnd.y - pStart.y) + pStart.x;

            //若x坐标大于当前点的坐标，则有交点
            if (x > curPoint.x)
                nCross++;
        }

        // 单边交点为偶数，点在多边形之外
        return (nCross % 2 == 1);
    }

    private RoadGraph loadRoadGraphInfo(String mapName) {
        String roadDataString = Utils.getFileContent(getMapRoadFilePath(mapName));
        Log.d(TAG, "loadRoadGraph roadDataString: " + roadDataString);
        return TextUtils.isEmpty(roadDataString) ? null : mGson.fromJson(roadDataString, RoadGraph.class);
    }

    private String getMapRoadFilePath(String name) {
        String roadFilePath = ROBOT_MAP_DIR + File.separator + name + File.separator + FILE_ROAD_JSON;
        Log.d(TAG, "getMapRoadFilePath: roadFilePath=" + roadFilePath);
        return roadFilePath;
    }

}
