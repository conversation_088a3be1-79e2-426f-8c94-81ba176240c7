/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.client.upload.bi;

import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedWriter;
import java.io.Closeable;
import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.net.InetAddress;
import java.net.Socket;
import java.util.LinkedList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

public class BiSocketClient {

    private static final String TAG = "BiSocketClient";
    private static final int CORE_THREAD_POOL_SIZE = 3;
    private static final int MAX_THREAD_POOL_SIZE = 10;
    private static final int MAX_BUF_SIZE = 20;
    private static final int PORT = 9191;
    private static final int DELAY_SECONEDS = 10;
    private static final int MAX_RETRY = 3;

    private ExecutorService executorService;
    private ScheduledExecutorService timerExecutor;
    private ScheduledFuture timerFuture;
    private AtomicInteger retryCount;

    private Socket socket;
    private OutputStream outputStream;
    private BufferedWriter bufferedWriter;
    private LimitQueue<String> bufferMsg;
    private boolean isConnecting = false;

    public BiSocketClient() {
        final AtomicInteger threadNum = new AtomicInteger(0);
        executorService = new ThreadPoolExecutor(CORE_THREAD_POOL_SIZE
                , MAX_THREAD_POOL_SIZE
                , 0L
                , TimeUnit.SECONDS
                , new LinkedBlockingQueue<Runnable>()
                , new ThreadFactory() {
            @Override
            public Thread newThread(@NonNull Runnable r) {
                return new Thread(r, "BiSocketClient Thread-" + threadNum.incrementAndGet());
            }
        });
        timerExecutor = Executors.newSingleThreadScheduledExecutor(new ThreadFactory() {
            @Override
            public Thread newThread(@NonNull Runnable r) {
                return new Thread(r, "BiSocketClient Timer");
            }
        });
        retryCount = new AtomicInteger(0);
        bufferMsg = new LimitQueue<>(MAX_BUF_SIZE);
    }

    public void connectServer() {
        Log.i(TAG, "connectServer");
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                synchronized (BiSocketClient.this) {
                    if (isConnecting) {
                        resetRetryConnect();
                        return;
                    }
                    try {
                        InetAddress serverAddr = InetAddress.getByName(null);
                        socket = new Socket(serverAddr, PORT);
                        if (socket.isConnected()) {
                            Log.i(TAG, "socket connectServer success!");
                            isConnecting = true;
                            outputStream = socket.getOutputStream();
                            bufferedWriter = new BufferedWriter(new OutputStreamWriter(outputStream));
                            resetRetryConnect();
                            checkConnectStatus(socket);
                            sendBufferMsg();
                        }
                    } catch (IOException e) {
                        Log.i(TAG, "socket connectServer failed:" + e.toString());
                        retryConnectServer();
                    }
                }
            }
        });
    }

    public void biReport(String tableName, String data) {
        biReport(Definition.BI_TYPE_NORMAL, Definition.BI_LEVEL_VERBOSE, tableName, data);
    }

    public void biReport(String type, int level, String tableName, String data) {
        if (!TextUtils.isEmpty(tableName) && !TextUtils.isEmpty(data)) {
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put(Definition.BI_PARAM_TABLE_NAME, tableName);
                jsonObject.put(Definition.BI_PARAM_DATA, data);
                jsonObject.put(Definition.BI_PARAM_TYPE, type);
                jsonObject.put(Definition.BI_PARAM_LEVEL, level);
                jsonObject.put(Definition.BI_PARAM_NOT_FORCE, true);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            String msg = jsonObject.toString();
            sendSocketMsg(msg);
        }
    }

    public void biReport(String type, int level, String tableName, String data, boolean notForce) {
        if (!TextUtils.isEmpty(tableName) && !TextUtils.isEmpty(data)) {
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put(Definition.BI_PARAM_TABLE_NAME, tableName);
                jsonObject.put(Definition.BI_PARAM_DATA, data);
                jsonObject.put(Definition.BI_PARAM_TYPE, type);
                jsonObject.put(Definition.BI_PARAM_LEVEL, level);
                jsonObject.put(Definition.BI_PARAM_NOT_FORCE, notForce);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            String msg = jsonObject.toString();
            sendSocketMsg(msg);
        }
    }

    private void retryConnectServer() {
        if (retryCount.get() >= MAX_RETRY) {
            resetRetryConnect();
            Log.i(TAG, "stop retry connectServer");
            return;
        }
        timerFuture = timerExecutor.schedule(new Runnable() {
            @Override
            public void run() {
                retryCount.incrementAndGet();
                Log.i(TAG, "retry connectServer-" + retryCount.get());
                connectServer();
            }
        }, DELAY_SECONEDS, TimeUnit.SECONDS);
    }

    private void resetRetryConnect() {
        if (timerFuture != null) {
            timerFuture.cancel(true);
        }
        retryCount.set(0);
    }

    private void sendSocketMsg(final String msg) {
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                synchronized (BiSocketClient.this) {
                    if (isConnecting && bufferedWriter != null) {
                        try {
                            bufferedWriter.write(msg);
                            bufferedWriter.newLine();
                            bufferedWriter.flush();

                            Log.v(TAG, "send bi socket msg：" + msg);
                        } catch (IOException e) {
                            Log.e(TAG, "sendSocketMsg failed:" + e.toString());

                            handleSocketBroken(msg);
                        }
                    } else {
                        handleSocketBroken(msg);
                    }
                }
            }
        });
    }

    private void checkConnectStatus(final Socket socket) {
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    if (socket.getInputStream().read() == -1) {
                        Log.i(TAG, "the socket connection is interrupted!");
                        isConnecting = false;
                        resetRetryConnect();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        });
    }


    private void handleSocketBroken(String msg) {
        Log.i(TAG, "handleSocketBroken");
        isConnecting = false;
        bufferMsg.offer(msg);
        closeResource(bufferedWriter);
        closeResource(outputStream);
        closeResource(socket);

        connectServer();
    }

    private void sendBufferMsg() {
        for (String msg : bufferMsg) {
            sendSocketMsg(msg);
        }
        bufferMsg.clear();
    }

    private void closeResource(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
                closeable = null;
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private class LimitQueue<E> extends LinkedList<E> {

        private int limit;

        public LimitQueue(int limit) {
            this.limit = limit;
        }

        @Override
        public boolean offer(E e) {
            if (size() >= limit) {
                poll();
            }
            return super.offer(e);
        }
    }
}
