package com.ainirobot.coreservice.action;


import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.actionbean.RadarAlignBean;
import com.ainirobot.coreservice.config.ConfigManager;
import com.ainirobot.coreservice.core.LocalApi;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Timer;
import java.util.TimerTask;

public class RadarAlignAction extends AbstractAction<RadarAlignBean> {
    public static final long START_ALIGN_TIMEOUT = 60 * Definition.SECOND;
    public static final long RETRY_DELAY_TIME = 1 * Definition.SECOND;
    public static final long NAVIGATION_TIMEOUT = 30 * Definition.SECOND;
    private static final float ROVER_RADIUS = ConfigManager.getNavigationConfig().getNaviRadius();
    private static final float ALIGN_DISTANCE = 1f;
    private static final String TAG = "RadarAlignAction";

    private RadarAlignBean radarAlignBean = null;
    private long startAlignTimeout = START_ALIGN_TIMEOUT;
    private long retryDelayTime = RETRY_DELAY_TIME;
    private long navigationTimeout = NAVIGATION_TIMEOUT;
    private Timer mRetryDelayTimer;
    private Timer mRadarAlignTimer;
    private Timer mNaviToPoseTimer;
    private Pose mAlignPose;
    private State mState = State.IDLE;

    private enum State {
        IDLE, GO_LOCATION, GET_LOCATION, START_ALIGN, STOPPED
    }

    class ChargeResult {
        private int code;
        private String message;

        public ChargeResult() {
        }

        public ChargeResult(int code, String message) {
            this.code = code;
            this.message = message;
        }

        public int getCode() {
            return code;
        }

        public String getMessage() {
            return message;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }

    protected RadarAlignAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_RADAR_ALIGN, resList);
    }

    @Override
    protected boolean startAction(RadarAlignBean parameter, LocalApi api) {
        Log.d(TAG, "RadarAlignAction start , parameter = " + parameter.toString());
        updateParams(parameter);
        naviToPose();
        return true;
    }

    private void updateParams(RadarAlignBean parameter) {
        radarAlignBean = parameter;
        mReqId = radarAlignBean.getReqId();
        startAlignTimeout = radarAlignBean.getStartAlignTimeout();
        retryDelayTime = radarAlignBean.getRetryDelayTime();
        navigationTimeout = radarAlignBean.getNavigationTimeout();
        Log.d(TAG, "updateParams params:" + radarAlignBean.toString());
    }

    private Pose calculate(Pose pose) {
        float x = pose.getX();
        float y = pose.getY();
        float theta = pose.getTheta();
        float startPointDistance = ALIGN_DISTANCE;

        Pose alignPose = new Pose();
        alignPose.setX((float) (x + (startPointDistance - ROVER_RADIUS) * Math.cos(theta)));
        alignPose.setY((float) (y + (startPointDistance - ROVER_RADIUS) * Math.sin(theta)));
        alignPose.setTheta(pose.getTheta());
        Log.d(TAG, " calculatePose:" + alignPose.toString());

        return alignPose;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        Log.d(TAG, "cmdResponse cmdType=" + command + " result=" + result);
        switch (command) {
            case Definition.CMD_NAVI_GO_LOCATION:
                Log.d(TAG, "go location result = " + result);
                if (Definition.NAVIGATION_OK.equals(result)) {
                    cancelNaviToPoseTimer();
                    mState = State.GET_LOCATION;
                    mApi.getLocation(mReqId, radarAlignBean.getDestination());
                } else {
                    Log.d(TAG, "go location failed");
                    mApi.goPlace(mReqId, radarAlignBean.getDestination());
                }
                return true;
            case Definition.CMD_NAVI_GET_LOCATION:
                processGetLocation(result, extraData);
                break;
            case Definition.CMD_NAVI_ALIGN_START:
                if ("timeout".equals(result)) {
                    finishAction(Definition.RESULT_FAILURE, "navi radar align timeout");
                    return false;
                }

                Gson gson = new Gson();
                ChargeResult chargeResult = gson.fromJson(result, ChargeResult.class);
                int code = chargeResult.getCode();
                if (code == 58) {
                    chargeResult.setCode(Definition.RESULT_OK);
                    finishAction(Definition.RESULT_OK, gson.toJson(chargeResult));
                } else {
                    if (radarAlignBean.getIsNeedRetry()) {
                        onStatusUpdate(Definition.STATUS_RADAR_ALIGN_RETRY,
                                "Retry radar align");
                        mApi.stopAlign(mReqId, "");
                        this.startRetryTimer();
                    } else {
                        chargeResult.setCode(Definition.RESULT_FAILED);
                        finishAction(Definition.RESULT_FAILURE, result);
                    }
                }
                break;

            case Definition.CMD_NAVI_ALIGN_CANCEL:
                gson = new Gson();
                chargeResult = gson.fromJson(result, ChargeResult.class);
                code = chargeResult.getCode();
//                if (code == 0) {
//                    chargeResult.setCode(Definition.RESULT_OK);
//                    finishAction(Definition.RESULT_OK, gson.toJson(chargeResult));
//                } else {
//                    chargeResult.setCode(Definition.RESULT_FAILED);
//                    finishAction(Definition.RESULT_FAILURE, " radar align cancel failed");
//                }
                break;

            default:
                break;

        }
        return true;
    }

    private void naviToPose() {
        mState = State.GO_LOCATION;
        startNaviToPoseTimer();
        mApi.goPlace(mReqId, radarAlignBean.getDestination());
    }
    private void processGetLocation(String result, String extraData) {
        try {
            mState = State.START_ALIGN;
            this.startAlignTimer();
            JSONObject json = new JSONObject(result);
            boolean isExist = json.getBoolean(Definition.JSON_NAVI_SITE_EXIST);
            if (isExist) {
                Pose pose = mGson.fromJson(result, Pose.class);
                mAlignPose = this.calculate(pose);
                mApi.startAlign(mReqId, mGson.toJson(mAlignPose));
            } else {
                finishAction(Definition.ERROR_DESTINATION_NOT_EXIST, "Destination not exist");
            }
        } catch (JSONException e) {
            e.printStackTrace();
            finishAction(Definition.ERROR_DESTINATION_NOT_EXIST, "Get " + radarAlignBean.getDestination() + " failed");
        }
    }


    @Override
    public boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_ALIGN_START:
                Log.d(TAG, "cmdStatusUpdate : cmdType=" + command
                        + " status=" + status);
                Gson gson = new Gson();
                ChargeResult chargeResult = gson.fromJson(status, ChargeResult.class);
                int code = chargeResult.getCode();

                if (code == 0) {
                    onStatusUpdate(Definition.STATUS_START_GOTO_RADAR_ALIGN,
                            "Start navi radar align");
                } else {
                    onStatusUpdate(code, status);
                }
                break;
        }
        return true;
    }

    private void startRetryTimer() {
        this.cancelRetryTimer();
        mRetryDelayTimer = new Timer();
        mRetryDelayTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                Log.d(TAG, "startRetry");
                naviToPose();
            }
        }, retryDelayTime);
    }

    private void cancelRetryTimer() {
        if (mRetryDelayTimer != null) {
            mRetryDelayTimer.cancel();
            mRetryDelayTimer = null;
        }
    }

    private void startAlignTimer() {
        cancelAlignTimer();
        mRadarAlignTimer = new Timer();
        mRadarAlignTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (mState == State.START_ALIGN) {
                    finishAction(Definition.RESULT_FAILURE, "radar align failed timeout");
                }
            }
        }, startAlignTimeout);
    }

    private void cancelAlignTimer() {
        if (mRadarAlignTimer != null) {
            mRadarAlignTimer.cancel();
            mRadarAlignTimer = null;
        }
    }

    private void startNaviToPoseTimer() {
        Log.d(TAG, "startNaviToPoseTimer mstate:" + mState );
        if (mState != State.GO_LOCATION){
            return;
        }
        cancelNaviToPoseTimer();
        mNaviToPoseTimer = new Timer();
        mNaviToPoseTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (mState == State.GO_LOCATION) {
                    finishAction(Definition.RESULT_FAILURE, "navi failed timeout");
                }
            }
        }, navigationTimeout);

    }

    private void cancelNaviToPoseTimer() {
        if (mNaviToPoseTimer != null) {
            mNaviToPoseTimer.cancel();
            mNaviToPoseTimer = null;
        }
    }

    private void finishAction(int status, String msg) {
        Log.d(TAG, "RadarAlignAction finishAction : " + status + " " + msg);
        mApi.stopAlign(mReqId, "");
        this.cancelRetryTimer();
        this.cancelAlignTimer();
        this.cancelNaviToPoseTimer();
        mState = State.IDLE;
        onResult(status, msg);
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        Log.d(TAG, "RadarAlignAction stopAction ------------  ");
        mApi.stopAlign(mReqId, "");
        this.cancelRetryTimer();
        this.cancelAlignTimer();
        this.cancelNaviToPoseTimer();
        mState = State.IDLE;
        return true;
    }

    @Override
    protected boolean verifyParam(RadarAlignBean param) {
        return true;
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        Log.d(TAG, "getStopCommands call ");
        return new ArrayList<StopCommandList.StopCommand>() {{
            add(new StopCommandList.StopCommand(Definition.CMD_NAVI_STOP_NAVIGATION, null));
        }};
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        Log.d(TAG, "getStopCommandChecker call ");
        return new StopCommandList.IStopCommandChecker() {
            @Override
            public boolean executeSuccess(String command, String result) {
                Log.d(TAG, "RadarAlignAction stop check : " + command);
                switch (command) {
                    case Definition.CMD_NAVI_STOP_NAVIGATION:
                        return true;
                    default:
                        break;
                }
                return false;
            }
        };
    }

}
