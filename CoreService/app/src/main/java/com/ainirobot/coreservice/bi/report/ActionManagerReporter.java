package com.ainirobot.coreservice.bi.report;

import com.google.gson.JsonObject;

public class ActionManagerReporter extends SystemInformationReporter {
    public ActionManagerReporter() {
        super("ActionManager", "ActionManager");
    }

    public void reportGetCache(final int actionId, final String actionInfo) {
        JsonObject params = new JsonObject();
        params.addProperty("actionId", actionId);

        JsonObject result = new JsonObject();
        result.addProperty("actionInfo", actionInfo);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("getCache")
            .addNodeParams(params.toString())
            .addNodeResult(result.toString())
            .reportV();
    }

    public void reportExCmd(final int actionId, final String parameter, final String actionListener,
        final boolean isSystem, final String actionInfo) {
        JsonObject params = new JsonObject();
        params.addProperty("actionId", actionId);
        params.addProperty("parameter", parameter);
        params.addProperty("actionListener", actionListener);
        params.addProperty("isSystem", isSystem);
        params.addProperty("actionInfo", actionInfo);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("exCmd")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportExStopCmd(final int actionId, final boolean isResetHW, final boolean resultValue) {
        JsonObject params = new JsonObject();
        params.addProperty("actionId", actionId);
        params.addProperty("isResetHW", isResetHW);

        JsonObject result = new JsonObject();
        result.addProperty("result", resultValue);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("exStopCmd")
            .addNodeParams(params.toString())
            .addNodeResult(result.toString())
            .reportV();
    }

    public void reportStopAllAction() {
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("stopAllAction")
            .reportV();
    }

    public void reportStopRunningAction(final String runningActions) {
        JsonObject params = new JsonObject();
        params.addProperty("runningActions", runningActions);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("stopRunningAction")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportCreateAction(final int id, final String resList, final String action) {
        JsonObject params = new JsonObject();
        params.addProperty("id", id);
        params.addProperty("resList", resList);

        JsonObject result = new JsonObject();
        result.addProperty("action", action);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("createAction")
            .addNodeParams(params.toString())
            .addNodeResult(result.toString())
            .reportV();
    }

    public void reportOnFinish(final String action) {
        JsonObject params = new JsonObject();
        params.addProperty("action", action);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("onFinish")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportHandlerStatus(final String type, final String data) {
        JsonObject params = new JsonObject();
        params.addProperty("type", type);
        params.addProperty("data", data);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("handleStatus")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportSetRobotSetting(final String type, final String data) {
        JsonObject params = new JsonObject();
        params.addProperty("type", type);
        params.addProperty("data", data);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("setRobotSetting")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportCommonGetAction(final int actionId, final String resList,
        final String action) {
        JsonObject params = new JsonObject();
        params.addProperty("actionId", actionId);
        params.addProperty("resList", resList);

        JsonObject result = new JsonObject();
        result.addProperty("action", action);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("getAction")
            .addNodeNote("from CommonActionCache")
            .addNodeParams(params.toString())
            .addNodeResult(result.toString())
            .reportV();
    }

    public void reportCommonRemoveRunAction(final int actionId, final String action) {
        JsonObject params = new JsonObject();
        params.addProperty("actionId", actionId);
        params.addProperty("action", action);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("removeRunAction")
            .addNodeNote("from CommonActionCache")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportCommonFinaRunAction(final int actionId, final String action) {
        JsonObject params = new JsonObject();
        params.addProperty("actionId", actionId);

        JsonObject result = new JsonObject();
        result.addProperty("action", action);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("finaRunAction")
            .addNodeNote("from CommonActionCache")
            .addNodeParams(params.toString())
            .addNodeResult(result.toString())
            .reportV();
    }

    public void reportCommonGetRunningActions(final String runningActions) {
        JsonObject result = new JsonObject();
        result.addProperty("runningActions", runningActions);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("getRunningActions")
            .addNodeNote("from CommonActionCache")
            .addNodeResult(result.toString())
            .reportV();
    }

    public void reportPersistentGetAction(final int actionId, final String resList,
        final String action) {
        JsonObject params = new JsonObject();
        params.addProperty("actionId", actionId);
        params.addProperty("resList", resList);

        JsonObject result = new JsonObject();
        result.addProperty("action", action);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("getAction")
            .addNodeNote("from PersistentActionCache")
            .addNodeParams(params.toString())
            .addNodeResult(result.toString())
            .reportV();
    }

    public void reportPersistentRemoveRunAction(final int actionId, final String action) {
        JsonObject params = new JsonObject();
        params.addProperty("actionId", actionId);
        params.addProperty("action", action);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("removeRunAction")
            .addNodeNote("from PersistentActionCache")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportPersistentFinaRunAction(final int actionId, final String action) {
        JsonObject params = new JsonObject();
        params.addProperty("actionId", actionId);

        JsonObject result = new JsonObject();
        result.addProperty("action", action);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("finaRunAction")
            .addNodeNote("from PersistentActionCache")
            .addNodeParams(params.toString())
            .addNodeResult(result.toString())
            .reportV();
    }

    public void reportPersistentGetRunningActions(final String runningActions) {
        JsonObject result = new JsonObject();
        result.addProperty("runningActions", runningActions);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("getRunningActions")
            .addNodeNote("from PersistentActionCache")
            .addNodeResult(result.toString())
            .reportV();
    }
}
