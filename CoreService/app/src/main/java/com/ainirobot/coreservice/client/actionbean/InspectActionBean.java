/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.client.actionbean;

public class InspectActionBean extends BaseBean {
    private long timeOut;
    private boolean isReInspection = true;

    public long getTimeOut() {
        return timeOut;
    }

    public void setTimeOut(long timeOut) {
        this.timeOut = timeOut;
    }

    public boolean getIsReInspection() {
        return isReInspection;
    }

    public void setIsReInspection(boolean isReInspection) {
        this.isReInspection = isReInspection;
    }

    @Override
    public String toString() {
        return "InspectActionBean{" +
                "timeOut=" + timeOut +
                ", isReInspection=" + isReInspection +
                '}';
    }
}
