package com.ainirobot.coreservice.data.authorization;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;

import com.ainirobot.coreservice.data.provider.BaseProvider;
import com.ainirobot.coreservice.data.provider.SQLiteTable;

import java.util.ArrayList;
import java.util.List;

/**
 * 系统语言&发言人
 * 维护本地和服务端支持的系统语言(取交集)及每种语言支持的发言人，
 * 只存储整体列表信息不维护当前系统语言和发言人.
 *
 * <p>
 * 当前系统语言：{@link com.ainirobot.coreservice.client.Definition#ROBOT_LANGUAGE}
 * 当前发言人：{@link com.ainirobot.coreservice.client.Definition#ROBOT_SETTING_SPEAKER_ROLE}</p>
 */
public class AuthProvider extends BaseProvider {
    private static final String TAG = "AuthProvider";

    public static final String AUTHORITY = "com.ainirobot.coreservice.AuthProvider";

    private static final List<SQLiteTable> TABLES = new ArrayList<SQLiteTable>() {{
        add(AuthDataHelper.TABLE);
    }};

    @Override
    public List<SQLiteTable> getTables() {
        return TABLES;
    }

    @Override
    public SQLiteOpenHelper getDBHelper() {
        return new DBHelper(getContext());
    }

    @Override
    public boolean onCreate() {
        Log.i(TAG, "on create");
        mDBHelper = new DBHelper(getContext());
        return true;
    }

    static class DBHelper extends SQLiteOpenHelper {
        private static final String DB_NAME = "auth.db";
        private static final int VERSION = 1;

        private Context mContext;

        private DBHelper(Context context) {
            super(context, DB_NAME, null, VERSION);
            Log.d(TAG, "DBHelper version: " + VERSION);
            mContext = context;
        }

        @Override
        public void onCreate(SQLiteDatabase db) {
            Log.d(TAG, "DBHelper onCreate version: " + VERSION);
            for (SQLiteTable table : TABLES) {
                table.create(db);
                table.importData(mContext, db);
            }
        }

        @Override
        public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            Log.d(TAG, "DBHelper onUpgrade oldVersion: " + oldVersion + ", newVersion: " + newVersion);
            for (SQLiteTable table : TABLES) {
                table.delete(db);
                table.create(db);
                table.importData(mContext, db);
            }
        }

        @Override
        public void onDowngrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            Log.d(TAG, "DBHelper onDowngrade oldVersion: " + oldVersion + ", newVersion: " + newVersion);
            for (SQLiteTable table : TABLES) {
                table.delete(db);
                table.create(db);
                table.importData(mContext, db);
            }
        }

    }
}
