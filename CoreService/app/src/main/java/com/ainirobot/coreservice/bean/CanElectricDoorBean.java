package com.ainirobot.coreservice.bean;

import com.google.gson.annotations.SerializedName;
public class CanElectricDoorBean {

    @SerializedName("bootReason")
    private int bootReason;
    @SerializedName("door1")
    private int door1;
    @SerializedName("door2")
    private int door2;
    @SerializedName("door3")
    private int door3;
    @SerializedName("door4")
    private int door4;
    @SerializedName("doorstatus")
    private int doorStatus;
    @SerializedName("down_status")
    private int downStatus;
    @SerializedName("propType")
    private int propType;
    @SerializedName("runMode")
    private int runMode;
    @SerializedName("sensitivity")
    private int sensitivity;
    @SerializedName("speed")
    private int speed;
    @SerializedName("state")
    private int state;
    @SerializedName("up_status")
    private int upStatus;

    public int getBootReason() {
        return bootReason;
    }

    public void setBootReason(int bootReason) {
        this.bootReason = bootReason;
    }

    public int getDoor1() {
        return door1;
    }

    public void setDoor1(int door1) {
        this.door1 = door1;
    }

    public int getDoor2() {
        return door2;
    }

    public void setDoor2(int door2) {
        this.door2 = door2;
    }

    public int getDoor3() {
        return door3;
    }

    public void setDoor3(int door3) {
        this.door3 = door3;
    }

    public int getDoor4() {
        return door4;
    }

    public void setDoor4(int door4) {
        this.door4 = door4;
    }

    public int getDoorStatus() {
        return doorStatus;
    }

    public void setDoorStatus(int doorStatus) {
        this.doorStatus = doorStatus;
    }

    public int getDownStatus() {
        return downStatus;
    }

    public void setDownStatus(int downStatus) {
        this.downStatus = downStatus;
    }

    public int getPropType() {
        return propType;
    }

    public void setPropType(int propType) {
        this.propType = propType;
    }

    public int getRunMode() {
        return runMode;
    }

    public void setRunMode(int runMode) {
        this.runMode = runMode;
    }

    public int getSensitivity() {
        return sensitivity;
    }

    public void setSensitivity(int sensitivity) {
        this.sensitivity = sensitivity;
    }

    public int getSpeed() {
        return speed;
    }

    public void setSpeed(int speed) {
        this.speed = speed;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getUpStatus() {
        return upStatus;
    }

    public void setUpStatus(int upStatus) {
        this.upStatus = upStatus;
    }
}
