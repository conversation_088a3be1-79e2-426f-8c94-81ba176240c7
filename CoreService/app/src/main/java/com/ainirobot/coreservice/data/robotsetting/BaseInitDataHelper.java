/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.data.robotsetting;

import android.database.sqlite.SQLiteDatabase;

import com.ainirobot.coreservice.data.provider.SQLiteTable;

public abstract class BaseInitDataHelper {
    final SQLiteTable mTableField;

    BaseInitDataHelper(SQLiteTable tableField) {
        mTableField = tableField;
    }

    /**
     * 每次启动
     * @param db - 可读写数据库
     */
    abstract void init(SQLiteDatabase db);

    /**
     * 数据库创建
     * @param db - 可读写数据库
     */
    abstract void onCreate(SQLiteDatabase db);

    /**
     * 数据库更新
     * @param db - 可读写数据库
     */
    abstract void onUpgrade(SQLiteDatabase db);

    /**
     * 数据库降级
     * @param db - 可读写数据库
     */
    abstract void onDowngrade(SQLiteDatabase db);
}
