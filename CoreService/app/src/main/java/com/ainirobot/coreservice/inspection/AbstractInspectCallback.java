package com.ainirobot.coreservice.inspection;

import android.os.RemoteException;

import com.ainirobot.coreservice.IInspectCallBack;

/**
 * @version V1.0.0
 * @date 2019/10/16 19:12
 */
public abstract class AbstractInspectCallback extends IInspectCallBack.Stub {

    private String inspectService;

    protected AbstractInspectCallback(String inspectService) {
        this.inspectService = inspectService;
    }

    @Override
    public final void onInspectFinish(boolean result, String info) throws RemoteException {
        onFinish(inspectService, result, info);
    }

    /**
     * service inspect finish
     *
     * @param service       inspect service
     * @param inspectResult service inspect result
     * @param info          inspect detail info
     */
    protected abstract void onFinish(String service, boolean inspectResult, String info);

    @Override
    public String toString() {
        return "InspectResultCallback{" +
                "inspectService='" + inspectService + '\'' +
                '}';
    }
}
