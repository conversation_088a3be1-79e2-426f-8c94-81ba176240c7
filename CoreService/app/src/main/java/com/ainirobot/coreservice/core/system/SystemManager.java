/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.core.system;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.ContentObserver;
import android.hardware.display.DisplayManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.util.SparseArray;
import android.widget.Toast;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.IStatusListener;
import com.ainirobot.coreservice.R;
import com.ainirobot.coreservice.bean.ElectricDoorStatus;
import com.ainirobot.coreservice.bi.report.SystemManagerReporter;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.actionbean.CommandBean;
import com.ainirobot.coreservice.client.actionbean.InspectionBean;
import com.ainirobot.coreservice.client.exception.InvalidArgumentException;
import com.ainirobot.coreservice.client.exception.NoSuchKeyException;
import com.ainirobot.coreservice.client.exception.ValueFormatException;
import com.ainirobot.coreservice.client.module.ModuleCallbackApi;
import com.ainirobot.coreservice.client.subtype.DeviceSubType;
import com.ainirobot.coreservice.client.techreport.CallChainReport;
import com.ainirobot.coreservice.config.ConfigManager;
import com.ainirobot.coreservice.core.CoreStateMachine;
import com.ainirobot.coreservice.core.InternalDef;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.core.RequestManager.ReqAction;
import com.ainirobot.coreservice.core.RobotSettingManager;
import com.ainirobot.coreservice.core.external.ExternalService;
import com.ainirobot.coreservice.core.external.ExternalServiceManager;
import com.ainirobot.coreservice.core.external.OtaService;
import com.ainirobot.coreservice.core.module.Module;
import com.ainirobot.coreservice.core.receiver.SystemBroadcastReceiver;
import com.ainirobot.coreservice.core.status.LocalSubscriber;
import com.ainirobot.coreservice.core.status.StatusManager;
import com.ainirobot.coreservice.core.system.BatteryManager.BatteryStatus;
import com.ainirobot.coreservice.core.system.OTAManager.OTAStartMode;
import com.ainirobot.coreservice.core.system.OTAManager.OTAStatus;
import com.ainirobot.coreservice.core.system.RobotSetting.SettingListener;
import com.ainirobot.coreservice.data.RobotInfoManager;
import com.ainirobot.coreservice.inspection.InspectionStatusManager;
import com.ainirobot.coreservice.service.CoreService;
import com.ainirobot.coreservice.utils.ArgsIterator;
import com.ainirobot.coreservice.utils.FileUtils;
import com.ainirobot.coreservice.utils.SettingDataHelper;
import com.ainirobot.coreservice.utils.SystemProperties;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.PrintWriter;
import java.lang.reflect.InvocationTargetException;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class SystemManager extends Module implements LocalApi.OnCmdResponse {
    private static final String TAG = "SystemManager";
    private static final int NOT_CHARGING = 0;
    private final int FIRST = 0;

    private static final String PROP_ORIONSEC_ENHANCED = "sys.orionsec.enhanced";
    private static final String PROP_WIFI_ADB = "debug.tp.wifi_adb";
    private static final String ROBOT_SETTINGS_D430_OTA_UNFINISHED = "robot_settings_d430_ota_unfinished";

    private static final int STATUS_LOCK_LITE = 1;
    private static final int STATUS_LOCK_FULL = 255;
    private static final int STATUS_LOCK_SPEECH = 2;
    private static final int STATUS_LOCK_NONE = 0;
    private static final String HW_NO_REGISTERED = "HWService not registered";
    private static final String HW_UN_SUPPORT = "unsupported";
    private static final String HW_NOT_SUPPORT = "not supported";
    private static final String HW_RES_TIMEOUT = "timeout";

    private CoreService mCore;
    public OTAManager mOta;
    public RemoteBindManager mRemoteBind;
    private Context mContext = ApplicationWrapper.getContext();
    private boolean mIsStartAlarm = false;

    private SparseArray<SystemStatus> mSystemStatus = new SparseArray<>();
    private SparseArray<SystemStatus> mLightStatus = new SparseArray<>();

    private CallChainReport mCallChain;
    private SystemManagerReporter mReporter;
    private Gson mGson;

    public SystemManager(CoreService core) {
        super(core, "Root");
        mCallChain = new CallChainReport(mContext, this.getClass().getSimpleName(), "system_status");
        mCore = core;
        mGson = new Gson();
        RobotSetting.init(mCore.getRobotSettingManager());
        mReporter = new SystemManagerReporter();
        setRequestCallback();
        setSettingListener();
        setHardwareMalfunctionListener();
        setBroadcastListener();

        mOta = new OTAManager(mCore.getApplicationContext());
        updateStatus(SystemStatus.INSPECTION);
        mRemoteBind = new RemoteBindManager(mCore.getApplicationContext(), this);
        ExternalServiceManager mServices = mCore.getExternalServiceManager();
        ExternalService naviService = mServices.getExternalService(RobotOS.NAVIGATION_SERVICE);
        Log.d(TAG, "SystemManager naviService: " + naviService
                + ", batteryManager: " + mCore.getBatteryManager());
        if (naviService != null && naviService.isEnable()) {
            setChargePileListener();
            setPoseEstimateListener();
            setEmergencyListener();
            setNaviStatusOkListener();
            setSwitchMapListener();
            setChargingTypeListener();
            setRadarStatusListener();
            //TODO：其它线充是否需要也取消监听
            if (!ProductInfo.isDeliveryProduct() && !ProductInfo.isMeissaPlus()) {
                setBmsWarningStatusListener();
                setMapDriftWarningListener();
            }
            setMapOutsideListener();
            setBeingPushedStatusListener();
//            setMultiRobotErrorListener();
            checkCurNaviMap();
        }
        setFunctionKeyListener();
        setRemoteConfigListener();
        initSystemStatus();
        checkAdbStatus();
        checkWifiAdbStatus();
        setBleSignalListener();
        if (ProductInfo.isMiniProduct() || ProductInfo.isMeissa2()) {
            setWheelOverListener();
        }
        setNaviSensorExceptionListener();
        setNaviLoadMapListener();
    }

    //监听NavigationService中底盘初始化完成或者底盘重启后，加载地图
    private void setNaviLoadMapListener() {
        StatusManager statusManager = mCore.getStatusManager();
        statusManager.registerStatusListener(Definition.STATUS_NAVI_LOAD_MAP, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Log.d(TAG, "setNaviLoadMapListener: " + data);
                updateStatus(SystemStatus.NAVI_LOAD_MAP);
            }
        });
    }

    private void setRemoteConfigListener() {
        final RobotSettingManager settingManager = mCore.getRobotSettingManager();
        settingManager.registerListener(Definition.ROBOT_SETTING_ENABLE_TARGET_CUSTOM,
                new ContentObserver(new Handler()) {
                    @Override
                    public void onChange(boolean selfChange) {
                        String value = settingManager.getRobotSetting(
                                Definition.ROBOT_SETTING_ENABLE_TARGET_CUSTOM, true);
                        SystemStatus.ENABLE_TARGET_CUSTOM.update(value);
                        updateStatus(SystemStatus.ENABLE_TARGET_CUSTOM);
                    }
                });
    }

    private void initSystemStatus() {
        ExternalServiceManager mServices = mCore.getExternalServiceManager();
        ExternalService naviService = mServices.getExternalService(RobotOS.NAVIGATION_SERVICE);

        //如果导航服务被禁用，则以下系统状态不可用
        if (naviService == null || !naviService.isEnable()) {
            Log.d(TAG, "Navigation service disabled");
            SystemStatus.OPEN_RADAR.remove();
            SystemStatus.REPOSITION.remove();
            SystemStatus.REMOTE_REPOSITION.remove();
            SystemStatus.START_CHARGE.remove();
            SystemStatus.BATTERY.remove();
            SystemStatus.BATTERY_LOW.remove();
            SystemStatus.MAP_DRIFT.remove();
            SystemStatus.OUTSIDE_MAP.remove();
            SystemStatus.BEING_PUSHED.remove();
            SystemStatus.MULTI_ROBOT_ERROR.remove();
        }
    }

    private void setRequestCallback() {
        setCallback(new ModuleCallbackApi() {
            @Override
            public boolean onSendRequest(int reqId, String reqType, String reqText, String reqParam) {
                return onNewRequest(reqId, reqType, reqParam);
            }
        });
    }

    private void checkAdbStatus() {
            boolean isAdbAlwaysOpen = false;
            try {
                isAdbAlwaysOpen = RobotSettings
                        .getRobotInt(mCore, Definition.ROBOT_SETTING_SYSTEM_ADB_ALWAYS_OPEN) == 1;
            } catch (InvalidArgumentException | NoSuchKeyException | ValueFormatException e) {
                e.printStackTrace();
            }
            boolean isAdbEnable = "0".equals(SystemProperties.get(ApplicationWrapper.getContext(),
                    PROP_ORIONSEC_ENHANCED, "0"));
            Log.d(TAG, "checkAdbStatus isAdbAlwaysOpen: " + isAdbAlwaysOpen
                    + ", isAdbOpen: " + isAdbEnable);
            if (isAdbAlwaysOpen && !isAdbEnable) {
                SystemProperties.setProperty(ApplicationWrapper.getContext(), PROP_ORIONSEC_ENHANCED, "0");
                Settings.Global.putInt(ApplicationWrapper.getContext().getContentResolver(),
                        Settings.Global.ADB_ENABLED, 1);
            }
    }

    private void checkWifiAdbStatus(){
        boolean isWifiAdbAlwaysOpen = false;
        try {
            isWifiAdbAlwaysOpen = RobotSettings.getRobotInt(mCore, Definition.PROP_SETTING_SYSTEM_WIFI_ADB_ALWAYS_OPEN) == 1 ;
        }catch (InvalidArgumentException | NoSuchKeyException | ValueFormatException e){
            e.printStackTrace();
        }
        boolean isWifiAdbEnable = "1".equals(SystemProperties.get(ApplicationWrapper.getContext(), PROP_WIFI_ADB, "0"));
        Log.d(TAG, " checkWifiAdbStatus isWifiAdbAlwaysOpen：" + isWifiAdbAlwaysOpen + " isWifiAdbEnable：" + isWifiAdbEnable);
        if (isWifiAdbAlwaysOpen && !isWifiAdbEnable){
            SystemProperties.setProperty(ApplicationWrapper.getContext(), PROP_WIFI_ADB, "1");
        }
    }

    private boolean onNewRequest(int reqId, String reqType, String params) {
        Log.d(TAG, "System new request : " + reqType + "  " + params);
        boolean result = false;
        switch (reqType) {
            case InternalDef.REQ_OTA_REMOTE:
                mOta.startService(OTAStartMode.REMOTE, InternalDef.START_COMMAND_CHECK_NEW_VERSION);
                result = true;
                break;

            case InternalDef.REQ_OTA:
                requestOtaAuth(false, params);
                result = true;
                break;

            case InternalDef.REQ_OTA_FORCE:
                requestOtaAuth(true, params);
                result = true;
                break;

            case InternalDef.REQ_OTA_FINISH:
                if (RobotSetting.isOtaDowngrade()) {
                    showOTADowngradeResult(params);
                } else {
                    showOTAResult(params);
                }
                result = true;
                break;

            case InternalDef.REQ_OTA_DOWNGRADE:
                requestOtaDowngrade(params);
                result = true;
                break;
            //When in creating map mode, we temporary forward this two command from WeChat to Home;
            case InternalDef.WECHAT_SET_CHARGING_PILE:
                updateStatus(SystemStatus.WECHAT_CHARGE_PILE, false);
                result = true;
                break;
            case InternalDef.WECHAT_FIRST_RECHARGING:
                updateStatus(SystemStatus.WECHAT_START_CHARGE, false);
                result = true;
                break;

            case InternalDef.RELOCATE_SWITCH_OPEN:
                Log.d(TAG, "System new request : " + reqType + "  " + params);
                SystemStatus.REMOTE_REPOSITION.update(InternalDef.SET_REMOTE_REQ_ID, reqId);
                updateStatus(SystemStatus.REMOTE_REPOSITION, false);
                result = true;
                break;

            case Definition.REQ_REMOTE_BIND_FAILED:
            case Definition.REQ_LOGIN_FAIL:
                removeSystemStatus(SystemStatus.INSPECTION);
                SystemStatus.REMOTE_BIND.update(false, params);
                updateStatus(SystemStatus.REMOTE_BIND, true);
                result = true;
                break;

            case Definition.REQ_REMOTE_BIND_SUCCESSFUL:
                SystemStatus.REMOTE_BIND.update(true, params);
                removeSystemStatus(SystemStatus.REMOTE_BIND);
                result = true;
                break;

            case Definition.REQ_REMOTE_GO_POSE:
            case Definition.REQ_REMOTE_GO_POSITION:
            case Definition.REQ_REMOTE_STOP_NAVIGATION:
                handleRemoteCommand(reqType, params);
                result = true;
                break;

            case "req_remote_lock":
                handleRemoteLock(params);
                result = true;
                break;

            case Definition.REQUEST_REMOTE_PUSH_MAP:
                handleRemotePushMap(reqType, params);
                result = true;
                break;

            case Definition.REQ_FUNCTION_KEY_PRESS: {
                Log.d(TAG,"function key press, start standby mode");
                Map<String, Object> pressHashMap = new HashMap<>();
                pressHashMap.put(Definition.JSON_STANDBY_STATUS, Definition.START);
                SystemStatus.STANDBY.update(Definition.SYSTEM_STANDBY, mGson.toJson(pressHashMap));
                updateStatus(SystemStatus.STANDBY);
                result = true;
            }
            break;
            case Definition.REQ_FUNCTION_KEY_RELEASE: {
                Log.d(TAG,"function key release, stop standby mode");
                Map<String, Object> releaseHashMap = new HashMap<>();
                releaseHashMap.put(Definition.JSON_STANDBY_STATUS, Definition.STOP);
                SystemStatus.STANDBY.update(Definition.SYSTEM_STANDBY, mGson.toJson(releaseHashMap));
                updateStatus(SystemStatus.STANDBY);
                result = true;
            }
            break;
            case Definition.REQ_REMOTE_STANDBY_START: {
                if(SettingDataHelper.getInstance().isStandByInOpkMode()){
                    //OPK休眠模式，不处理
                    Log.d(TAG,"donot process remote standby start in OPK mode");
                    break;
                }
                SystemStatus currentStatus = getFirst(mSystemStatus);
                if (currentStatus != SystemStatus.STANDBY) {
                    SystemStatus.STANDBY.update(Definition.SYSTEM_STANDBY, params);
                    result = updateStatus(SystemStatus.STANDBY, false);
                    if (!result) {
                        sendResponseToServer(params, Definition.CAN_NOT_ENTER_STANDBY);
                    }
                } else {
                    result = false;
                    sendResponseToServer(params, Definition.ALREADY_IN_STANDBY);
                }
            }

            break;
            case Definition.REQ_REMOTE_STANDBY_STOP: {
                if(SettingDataHelper.getInstance().isStandByInOpkMode()){
                    //OPK休眠模式，不处理
                    Log.d(TAG,"donot process remote standby stop in OPK mode");
                    break;
                }
                SystemStatus currentStatus = getFirst(mSystemStatus);
                if (currentStatus == SystemStatus.STANDBY) {
                    if (TextUtils.equals(SystemStatus.FUNCTION_KEY.getType(),
                            Definition.REQ_FUNCTION_KEY_RELEASE)) {
                        SystemStatus.STANDBY.update(Definition.SYSTEM_STANDBY, params);
                        result = updateStatus(SystemStatus.STANDBY, false);
                        if (!result) {
                            sendResponseToServer(params, Definition.CAN_NOT_EXIT_STANDBY);
                        }
                    } else {
                        result = false;
                        sendResponseToServer(params, Definition.FUNCTION_STANDBY);
                    }
                } else {
                    result = false;
                    sendResponseToServer(params, Definition.NOT_IN_STANDBY);
                }
            }
            break;

            case Definition.REQ_REMOTE_STOP_CHARGING:
                Log.d(TAG, " remote stop charging : " + reqType + "  " + params);
                SystemStatus.REMOTE_STOP_CHARGING.update(Definition.REQ_REMOTE_STOP_CHARGING, reqId,params);
                updateStatus(SystemStatus.REMOTE_STOP_CHARGING, false);
                result = true;
                break;
            case Definition.REQ_REMOTE_STOP_CHARGING_STATUS:
                Log.d(TAG, " remote stop charging status : " + reqType + "  " + params);
                SystemStatus.REMOTE_STOP_CHARGING.update(Definition.REQ_REMOTE_STOP_CHARGING_STATUS, reqId,params);
                updateStatus(SystemStatus.REMOTE_STOP_CHARGING, false);
                result = true;
                break;
            case Definition.REQ_SYSTEM_GO_CHARGING:
                Log.d(TAG, " remote system start charging : " + reqType + "  " + params);
                updateStatus(SystemStatus.START_CHARGE, false);
                break;
            default:
                break;
        }
        finishRequest(reqId, result);
        return result;
    }

    private String sendResponseToServer(String params, String result) {
        try {
            JSONObject jsonObject = new JSONObject(params);
            if (jsonObject.has(Definition.JSON_STANDBY_FROM)) {
                String from = jsonObject.getString(Definition.JSON_STANDBY_FROM);
                if (from.startsWith(Definition.JSON_CMD_FROM)) {
                    String id = jsonObject.getString(Definition.JSON_CMD_ID);
                    String type = jsonObject.getString(Definition.JSON_CMD_TYPE);
                    jsonObject = new JSONObject();
                    jsonObject.put(Definition.JSON_CMD_ID, id);
                    jsonObject.put(Definition.JSON_CMD_TYPE, type);
                    jsonObject.put(Definition.JSON_TASK_EXEC_RESULT, result);
                    jsonObject.put(Definition.JSON_TASK_EXEC_DATA, "");
                    CommandBean bean = new CommandBean(Definition.CMD_REMOTE_COMMAND_EXEC_REPORT,
                            jsonObject.toString(), false);
                    mCore.getSystemServer().startAction(-1, Definition.ACTION_COMMON,
                            new Gson().toJson(bean), null);
                }
                return from;
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return null;
    }

    private void handleRemotePushMap(String reqType, String params) {
//        {
//            "map_url": "https://xxxxx",
//                "map_name": "here-0526142727",
//                "map_uuid": "2deafe198d224434b14f4c086aa0fec3",
//                "task_id": "40b7cd1438204f36b679e8d5bce78e07",
//                "version": 1590475050
//        }
        String mapName = "";
        try {
            JSONObject object = new JSONObject(params);
            mapName = object.optString("map_name");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        String curNaviMap = mCore.getRobotInfoManager()
                .getRobotInfo(Definition.SYNC_ACTION_GET_MAP_NAME, "");
        Log.d(TAG, "handleRemotePushMap: reqType = " + reqType + "， params = " + params + ", mapName = " + mapName + ", curNaviMap = " + curNaviMap);
        if (mapName.equals(curNaviMap)) {
            SystemStatus.REMOTE_PUSH_MAP_NEED_SWITCH.update(Definition.JSON_CONFIG, params);
            updateStatus(SystemStatus.REMOTE_PUSH_MAP_NEED_SWITCH, false);
        } else {
            SystemStatus.REMOTE_PUSH_MAP_NO_SWITCH.update(Definition.JSON_CONFIG, params);
            updateStatus(SystemStatus.REMOTE_PUSH_MAP_NO_SWITCH, false);
        }
    }

    private void handleRemoteLock(String params) {
        Log.i(TAG, "handle remote lock status: " + params);
        int lockStatus = STATUS_LOCK_NONE;
        try {
            JSONObject json = new JSONObject(params);
            lockStatus = json.getInt(Definition.JSON_REMOTE_LOCK_STATUS);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        switch (lockStatus) {
            case STATUS_LOCK_FULL:
                if (isStatusExist(SystemStatus.LITE_LOCK)) {
                    sendRequest("system_remote_lock_lite_exit", "");
                    removeStatus(SystemStatus.LITE_LOCK);
                }
                SystemStatus.FULL_LOCK.update(Definition.JSON_CONFIG, params);
                updateStatus(SystemStatus.FULL_LOCK, true);
                break;

            case STATUS_LOCK_LITE:
                if (isStatusExist(SystemStatus.FULL_LOCK)) {
                    sendRequest("system_remote_lock_full_exit", "");
                    removeStatus(SystemStatus.FULL_LOCK);
                }

                SystemStatus.LITE_LOCK.update(Definition.JSON_TYPE, "system_remote_lock_dialog");
                SystemStatus.LITE_LOCK.update(Definition.JSON_CONFIG, params);
                updateStatus(SystemStatus.LITE_LOCK, true);
                break;

            case STATUS_LOCK_SPEECH:
                if (isStatusExist(SystemStatus.FULL_LOCK)) {
                    sendRequest("system_remote_lock_full_exit", "");
                    removeStatus(SystemStatus.FULL_LOCK);
                }

                SystemStatus.LITE_LOCK.update(Definition.JSON_TYPE, "system_remote_lock_speech");
                SystemStatus.LITE_LOCK.update(Definition.JSON_CONFIG, params);
                updateStatus(SystemStatus.LITE_LOCK, true);
                break;

            case STATUS_LOCK_NONE:
                if (isStatusExist(SystemStatus.FULL_LOCK)) {
                    sendRequest("system_remote_lock_full_exit", "");
                    removeStatus(SystemStatus.FULL_LOCK);
                }

                if (isStatusExist(SystemStatus.LITE_LOCK)) {
                    sendRequest("system_remote_lock_lite_exit", "");
                    removeStatus(SystemStatus.LITE_LOCK);
                }
                break;

            default:
                break;
        }
    }

    private void handleRemoteCommand(String reqType, String params) {
        SystemStatus.REMOTE_CMD.update(reqType, params);
        boolean result = updateStatus(SystemStatus.REMOTE_CMD, false);
        if (!result) {
            JSONObject object = new JSONObject();
            try {
                object.put("type", reqType);
                object.put("result", -1);
                object.put("errmsg", "There are more urgent tasks at present");
            } catch (JSONException e) {
                e.printStackTrace();
            }
            sendStatusReport(Definition.STATUS_REMOTE_NAVI, object.toString());
        }
    }

    private void setRadarStatusListener() {
        StatusManager statusManager = mCore.getStatusManager();
        statusManager.registerStatusListener(Definition.STATUS_RADAR, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                boolean isOpened = false;
                try {
                    JSONObject object = new JSONObject(data);
                    isOpened = object.optBoolean(Definition.JSON_NAVI_OPEN_RADAR, false);
                } catch (JSONException e) {
                    e.printStackTrace();
                }

                Log.d(TAG, "On radar status change:" + data
                        + "isOpen:" + isOpened
                        + "isEnabled:" + SystemStatus.OPEN_RADAR.isEnabled());
                SystemStatus.OPEN_RADAR.update(InternalDef.UPDATE_RADAR_STATUS, isOpened);
                if (isOpened && mSystemStatus.indexOfValue(SystemStatus.OPEN_RADAR) != -1) {
                    String params = SystemStatus.OPEN_RADAR.getParams();
                    try {
                        JSONObject json = new JSONObject(params);
                        String reason = json.getString("reason");
                        if (Definition.OPEN_BY_ALARM_CHARGING.equals(reason)) {
                            updateStatus(SystemStatus.INSPECTION);
                        }
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    removeStatus(SystemStatus.OPEN_RADAR);
                }
            }
        });
    }

    private void setBmsWarningStatusListener() {
        StatusManager statusManager = mCore.getStatusManager();
        statusManager.registerStatusListener(Definition.STATUS_BMS_WARNING, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                try {
                    JSONObject object = new JSONObject(data);
                    int status = object.getInt("status");

                    Log.d(TAG, "bms warning status: " + status);

                    switch (status) {
                        case Definition.BMS_WARNING_STATUS_CAN_CHARGING_NORMAL:
                            if (mSystemStatus.indexOfValue(SystemStatus.STOP_CHARGE) > 0) {
                                removeStatus(SystemStatus.STOP_CHARGE);
                                removeStatus(SystemStatus.START_CHARGE);
                            }
                            break;

                        case Definition.BMS_WARNING_STATUS_CAN_CHARGING_SLOW_RECHARGE:
                            Log.d(TAG, "charge_slow_recharge stop_charge_is_enable: " + SystemStatus.STOP_CHARGE.isEnabled()
                                    + ", start_charge_is_enalbe: " + SystemStatus.START_CHARGE.isEnabled()
                                    + ", open_radar_is_enalbe: " + SystemStatus.OPEN_RADAR.isEnabled());

                            if (!mCore.getBatteryManager().isCharging()) {
                                Log.d(TAG, "charge_slow_recharge, no charging and no need recharge");
                                break;
                            }

                            SystemStatus.STOP_CHARGE.update(Definition.CHARGE_SLOW, data);
                            SystemStatus.START_CHARGE.update(Definition.CHARGE_SLOW, data);
                            updateStatus(SystemStatus.STOP_CHARGE);
                            updateStatus(SystemStatus.START_CHARGE);
                            break;

                        default:
                            break;
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private void setBeingPushedStatusListener() {
        StatusManager statusManager = mCore.getStatusManager();
        statusManager.registerStatusListener(Definition.STATUS_ROBOT_BEING_PUSHED, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Log.d(TAG, "Being pushed state changed: " + data);
                try {
                    JSONObject object = new JSONObject(data);
                    int status = object.optInt("status");
                    switch (status) {
                        case Definition.BEING_PUSHED:
                            SystemStatus.BEING_PUSHED.update(InternalDef.BEING_PUSHED);
                            updateStatus(SystemStatus.BEING_PUSHED);
                            break;
                        case Definition.NOT_BEING_PUSHED:
                            SystemStatus.BEING_PUSHED.update(InternalDef.BEING_PUSHED_RELEASE);
                            removeStatus(SystemStatus.BEING_PUSHED);
                            break;
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private void setMultiRobotErrorListener() {
        StatusManager statusManager = mCore.getStatusManager();
        statusManager.registerStatusListener(Definition.STATUS_MULTI_ROBOT_ERROR, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Log.d(TAG, "Multi robot error : " + data);
                try {
                    JSONObject object = new JSONObject(data);
                    int multiErrorType = object.optInt("multiErrorType");
                    SystemStatus.MULTI_ROBOT_ERROR.update(InternalDef.MULTI_ERROR_TYPE, multiErrorType);
                    updateStatus(SystemStatus.MULTI_ROBOT_ERROR);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private void setMapOutsideListener() {
        StatusManager statusManager = mCore.getStatusManager();
        statusManager.registerStatusListener(Definition.STATUS_MAP_OUTSIDE, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Log.d(TAG, "map outside data: " + data);
                try {
                    JSONObject object = new JSONObject(data);
                    String status = object.optString("status");
                    switch (status) {
                        case Definition.MAP_INSIDE:
                            SystemStatus.OUTSIDE_MAP.update(InternalDef.MAP_OUTSIDE_LEAVE);
                            removeStatus(SystemStatus.OUTSIDE_MAP);
                            break;
                        case Definition.MAP_OUTSIDE:
                            SystemStatus.OUTSIDE_MAP.update(InternalDef.MAP_OUTSIDE_ENTER);
                            updateStatus(SystemStatus.OUTSIDE_MAP);
                            break;
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private void setMapDriftWarningListener() {
        StatusManager statusManager = mCore.getStatusManager();
        statusManager.registerStatusListener(Definition.STATUS_MAP_DRIFT_WARNING, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                try {
                    JSONObject object = new JSONObject(data);
                    int status = object.getInt("status");

                    Log.d(TAG, "map drift warning status: " + status);

                    switch (status) {
                        case Definition.MAP_DRIFT_DANGEROUS:
                            // 定位漂移，切换系统接管，引导用户推回定位点定位
                            if (ProductInfo.isMiniProduct() || ProductInfo.isMeissa2()) {
                                SystemStatus.MAP_DRIFT.update(InternalDef.MAP_DRIFT, true);
                                updateStatus(SystemStatus.MAP_DRIFT);
                            }
                            break;
                        default:
                            break;
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }

            }
        });
    }

    private void setSettingListener() {
        setSystemStatusEnabled(Definition.SYSTEM_BATTERY_LOW, RobotSetting.isAllowAutoCharge());
        setSystemStatusEnabled(Definition.SYSTEM_AUTO_OTA, RobotSetting.isAllowAutoOta());
        setSystemStatusEnabled(Definition.SYSTEM_BATTERY, !RobotSetting.isAllowChargingChat());
        RobotSetting.setListener(new SettingListener() {
            @Override
            public void onNewSetting(String type, String params) {
                mReporter.reportSettingUpdate(type, params);
                String chargingType = mCore.getRobotSettingManager().
                        getRobotSetting(Definition.ROBOT_SETTINGS_CHARGING_TYPE);

                switch (type) {
                    case InternalDef.SETTING_SET_CHARGE_PILE:
                        SystemStatus.SET_CHARGE_PILE.update(Definition.SET_CHARGING_ENVIRONMENT, params);
                        updateStatus(SystemStatus.SET_CHARGE_PILE, false);
                        break;

                    case InternalDef.SETTING_START_CHARGE:      // settings 点击"去充电"触发
                        if (FileUtils.hasElectricDoor()) {
                            mCore.getRobotInfoManager().getElectricDoorStatus(new RobotInfoManager.ElectricDoorStatusListener() {
                                @Override
                                public void onResult(String message) {
                                    Log.d(TAG, "getElectricDoorStatus:message=" + message);
                                    if (!TextUtils.isEmpty(message)) {
                                        ElectricDoorStatus status;
                                        try {
                                            status = new Gson().fromJson(message, ElectricDoorStatus.class);
                                            if (status != null) {
                                                if (status.isAllDoorClosed()) {
                                                    //监听状态，已全部关闭
                                                    if (mSystemStatus.indexOfValue(SystemStatus.START_CHARGE) < 0) {
                                                        SystemStatus.START_CHARGE.update(Definition.CHARGE_SETTING);
                                                        updateStatus(SystemStatus.OPEN_RADAR);
                                                        updateStatus(SystemStatus.START_CHARGE, false);
                                                    }
                                                } else {
                                                    Toast.makeText(mContext, mContext.getString(R.string.close_electric_door_please), Toast.LENGTH_SHORT).show();
                                                }
                                            }
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    }
                                }
                            });
                        } else {
                            if (mSystemStatus.indexOfValue(SystemStatus.START_CHARGE) < 0) {
                                SystemStatus.START_CHARGE.update(Definition.CHARGE_SETTING);
                                updateStatus(SystemStatus.OPEN_RADAR);
                                updateStatus(SystemStatus.START_CHARGE, false);
                            }
                        }
                        break;

                    case InternalDef.SETTING_START_CHARGE_TIMING:      //定时充电开始触发
                        //线充模式不处理定时充电
                        Log.d(TAG, " chargingType=" + chargingType);
                        if (Definition.CHARGING_TYPE_WIRE.equals(chargingType)) {
                            break;
                        }

                        if (mSystemStatus.indexOfValue(SystemStatus.START_CHARGE) < 0) {
                            SystemStatus.START_CHARGE.update(Definition.CHARGE_SETTING_TIMING);
                            updateStatus(SystemStatus.OPEN_RADAR);
                            updateStatus(SystemStatus.START_CHARGE);
                            finishStandByStatus();
                        }
                        break;

                    case InternalDef.SETTING_AUTO_CHARGE:       // settings  低电量自动回充的开关.
                        setSystemStatusEnabled(Definition.SYSTEM_BATTERY_LOW,
                                Boolean.parseBoolean(params));
                        if (SystemStatus.BATTERY_LOW.isEnabled()) {
                            if (mLightStatus.indexOfValue(SystemStatus.BATTERY_LOW) >= 0) {
                                SystemStatus.START_CHARGE.update(Definition.CHARGE_LOW);
                                updateStatus(SystemStatus.OPEN_RADAR);
                                updateStatus(SystemStatus.START_CHARGE);
                            }
                        } else {
                            SystemStatus.START_CHARGE.update("");
                            removeSystemStatus(SystemStatus.START_CHARGE);
                        }
                        break;

                    case InternalDef.SETTING_AUTO_OTA:
                        setSystemStatusEnabled(Definition.SYSTEM_AUTO_OTA,
                                Boolean.parseBoolean(params));
                        break;

                    case InternalDef.SETTING_START_REPOSITION:
                        SystemStatus.REPOSITION.update(Definition.REPOSITION_VISION, Boolean.parseBoolean(params));
                        updateStatus(SystemStatus.REPOSITION, false);
                        break;
                    case InternalDef.SETTING_E70_RECOVERY_START_REPOSITION:
                        SystemStatus.REPOSITION.update(InternalDef.POSE_ESTIMATE, false);
                        updateStatus(SystemStatus.REPOSITION, true);
                        break;
                    case InternalDef.SETTING_STOP_CHARGE:           //settings 定时充电结束触发
                        //线充模式不处理定时充电结束(启动雷达-自检-离桩)
                        Log.d(TAG, " chargingType=" + chargingType);
                        if (Definition.CHARGING_TYPE_WIRE.equals(chargingType)) {
                            break;
                        }
                        //如果当时正在执行急停等高优先级任务，松开急停后不再执行定时充电结束的逻辑
                        if (mSystemStatus.size() > 0 && getFirst(mSystemStatus).getPriority()
                                < SystemStatus.OPEN_RADAR.getPriority()){
                            SystemStatus.OPEN_RADAR.update(InternalDef.UPDATE_OPEN_RADAR_REASON,
                                    Definition.OPEN_BY_STOP_CHARGING);
                        } else {
                            SystemStatus.OPEN_RADAR.update(InternalDef.UPDATE_OPEN_RADAR_REASON,
                                    Definition.OPEN_BY_ALARM_CHARGING);
                        }
                        if (!updateStatus(SystemStatus.OPEN_RADAR)
                                && isAllowStopCharging()) {
                            //雷达可用且为充电状态，发送退出充电的请求到home
                            sendRequest(Definition.REQ_STOP_CHARGING, null);
                        }
                        break;

                    case InternalDef.SETTING_STOP_CHARGE_BY_APP:
                        SystemStatus.OPEN_RADAR.update(InternalDef.UPDATE_OPEN_RADAR_REASON, Definition.OPEN_BY_APP_STOP_CHARGING);
                        if (!updateStatus(SystemStatus.OPEN_RADAR)
                                && isAllowStopCharging()) {
                            //雷达可用且为充电状态，发送退出充电的请求到home
                            sendRequest(Definition.REQ_STOP_CHARGING_BY_APP, null);
                        }
                        break;

                    case InternalDef.SETTING_START_DORMANCY:
                        Log.d(TAG, "start dormancy");
                        SystemStatus.DORMANCY.update(params);
                        if (!updateStatus(SystemStatus.DORMANCY)) {
                            //TODO: 执行休眠失败，埋点
                        }
                        break;

                    case InternalDef.SETTING_RECOVERY:
                        Log.d(TAG, "do recovery");
                        SystemStatus.SYSTEM_RECOVERY.update(params);
                        updateStatus(SystemStatus.SYSTEM_RECOVERY);
                        break;

                    case InternalDef.SETTING_SHUTDOWN_TIMER:
                        SystemStatus currentStatus = getFirst(mSystemStatus);
                        if (currentStatus == null
                                || currentStatus.getPriority() > SystemStatus.SHUTDOWN_TIMER.getPriority()) {
                            updateStatus(SystemStatus.SHUTDOWN_TIMER, false);
                        } else {
                            //休眠比较特殊，休眠退出时需要恢复状态，耗时较长，定时关机触发时需要先通知休眠退出，然后再触发
                            if (currentStatus == SystemStatus.STANDBY) {
                                if (updateStatus(SystemStatus.SHUTDOWN_TIMER, true)) {
                                    sendRequest(Definition.REQ_STANDBY_STOP, null);
                                }
                            } else {
                                Log.d(TAG, "Shutdown timer failed : " + currentStatus.getName());
                            }
                        }
                        break;

                    case InternalDef.SETTING_CHARGING_USABLE:   // 充电可用控制
                        setSystemStatusEnabled(Definition.SYSTEM_BATTERY, !Boolean.parseBoolean(params));
                        break;

                    default:
                        break;
                }
            }
        });
    }

    private void finishStandByStatus() {
        SystemStatus currentStatus = getFirst(mSystemStatus);
        if (currentStatus == SystemStatus.STANDBY || currentStatus == SystemStatus.FUNCTION_KEY) {
            sendRequest(Definition.REQ_STANDBY_FINISH, null);
        }
    }

    /**
     * 当前是否允许，执行退出充电指令
     */
    private boolean isAllowStopCharging() {
        if (getFirst(mSystemStatus) == SystemStatus.BATTERY) {
            return true;
        }

        //充电状态被禁的情况，需要特殊处理
        return mCore.getBatteryManager().isCharging() && (mSystemStatus.size() == 0 ||
                mSystemStatus.valueAt(FIRST).getPriority() > SystemStatus.BATTERY.getPriority());
    }

    private void setEmergencyListener() {
        StatusManager statusManager = mCore.getStatusManager();
        statusManager.registerStatusListener(Definition.STATUS_EMERGENCY, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Log.d(TAG, "Emergency status update : " + type+", "+data);
                mReporter.reportEmergencyUpdate(type, data);
                switch (data) {
                    case InternalDef.EMERGENCY_PRESS:
                        SystemStatus.EMERGENCY.update(true);
                        updateStatus(SystemStatus.EMERGENCY);
                        break;

                    case InternalDef.EMERGENCY_RELEASE:
                        SystemStatus.EMERGENCY.update(false);
                        removeStatus(SystemStatus.EMERGENCY);
                        break;

                    default:
                        break;
                }

            }
        });
    }

    /**
     * 是否靠近危险区域,系统状态监听
     */
    private void setBleSignalListener() {
        StatusManager statusManager = mCore.getStatusManager();
        statusManager.registerStatusListener(Definition.STATUS_BLE_SIGNAL, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Log.d(TAG, "bleSignal status update : " + type+", "+data);
                try {
                    int bleSignal = Integer.valueOf(data);
                    Log.d(TAG, "bleSignal: "+bleSignal);
                    switch (bleSignal) {
                        case Definition.HARDWARE_BLE_NEAR:
                            SystemStatus.BLE_STATUS.update(true);
                            updateStatus(SystemStatus.BLE_STATUS);
                            break;

                        case Definition.HARDWARE_BLE_FAR:
                            if (getFirst(mSystemStatus) != SystemStatus.BLE_STATUS) {
                                Log.d(TAG, "first Status is not BLE_STATUS :" + getFirst(mSystemStatus));
                                return;
                            }
                            SystemStatus.BLE_STATUS.update(false);
                            updateStatus(SystemStatus.BLE_STATUS);
                            break;

                        default:
                            break;
                    }
                }catch (Throwable e){
                    Log.d(TAG, "onStatusUpdate e :"+e.getLocalizedMessage());
                }

            }
        });
    }

    /**
     * 是否轮子过流锁死的监听
     */
    private void setWheelOverListener() {
        StatusManager statusManager = mCore.getStatusManager();
        statusManager.registerStatusListener(Definition.STATUS_WHEEL_OVER_CURRENT, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Log.d(TAG, "wheel_over status update : " + type+", "+data);
                try {
                    int value = Integer.parseInt(data);
                    Log.d(TAG, "value: "+value);
                    switch (value) {
                        case Definition.STATUS_WHEEL_ABNORMAL:
                            SystemStatus.WHEEL_OVER_STATUS.update(true);
                            updateStatus(SystemStatus.WHEEL_OVER_STATUS);
                            break;

                        case Definition.STATUS_WHEEL_NORMAL:
                            SystemStatus.WHEEL_OVER_STATUS.update(false);
                            removeStatus(SystemStatus.WHEEL_OVER_STATUS);
                            break;

                        default:
                            break;
                    }
                }catch (Throwable e){
                    Log.d(TAG, "onStatusUpdate e :"+e.getLocalizedMessage());
                }

            }
        });
    }

    /**
     * 监听导航传感器是否异常
     */
    private void setNaviSensorExceptionListener() {
        StatusManager statusManager = mCore.getStatusManager();
        statusManager.registerStatusListener(Definition.STATUS_NAVI_SENSOR_EXCEPTION, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Log.d(TAG, "setNaviSensor status update : " + type+", "+data);
                if (!Definition.STATUS_NAVI_SENSOR_EXCEPTION.equals(type)){
                    Log.d(TAG, " type is not support");
                    return;
                }
                try {
                    if (Definition.NAVI_SENSOR_STATE_NORMAL.equals(data)){
                        if (Boolean.parseBoolean(SystemStatus.NAVI_SENSOR_STATE.getParams())){
                            Log.d(TAG,"Current navigation is normal.");
                            return;
                        }
                        SystemStatus.NAVI_SENSOR_STATE.update(true);
                        removeStatus(SystemStatus.NAVI_SENSOR_STATE);
                    }else if (Definition.NAVI_SENSOR_STATE_ERROR.equals(data)){
                        SystemStatus.NAVI_SENSOR_STATE.update(false);
                        updateStatus(SystemStatus.NAVI_SENSOR_STATE);
                    }
                }catch (Throwable e){
                    Log.d(TAG, "setNaviSensorException onStatusUpdate e :"+e.getLocalizedMessage());
                }

            }
        });
    }

    private void setFunctionKeyListener() {
        StatusManager statusManager = mCore.getStatusManager();
        statusManager.registerStatusListener(Definition.STATUS_MULTI_FUNCTION_SWITCH, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Log.d(TAG, "function key status update : " + data);
                Log.d(TAG, "function key is enable: " + SystemStatus.FUNCTION_KEY.isEnabled());
                Log.d(TAG, "function key standby in opk mode: " + SettingDataHelper.getInstance().isStandByInOpkMode());
                if (TextUtils.isEmpty(data)) {
                    return;
                }
                if(SettingDataHelper.getInstance().isStandByInOpkMode()){
                    //OPK休眠模式，不进行休眠系统接管
                    return;
                }
                try {
                    JSONObject jsonObject = new JSONObject(data);
                    int state = jsonObject.optInt(Definition.JSON_CAN_MULTI_FUNC_SWITCH_STATE);
                    switch (state) {
                        case InternalDef.FUNCTION_KEY_PRESS:
                            SystemStatus.FUNCTION_KEY.update(true);
                            if (updateStatus(SystemStatus.FUNCTION_KEY)) {
                                setSystemStatusEnabled(Definition.SYSTEM_SHUTDOWN_TIMER, false);
                            }
                            break;
                        default:
                            SystemStatus.FUNCTION_KEY.update(false);
                            removeStatus(SystemStatus.FUNCTION_KEY);
                            setSystemStatusEnabled(Definition.SYSTEM_SHUTDOWN_TIMER, true);
                            break;
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private void setPoseEstimateListener() {
        StatusManager statusManager = mCore.getStatusManager();
        statusManager.registerStatusListener(Definition.STATUS_POSE_ESTIMATE, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                mReporter.reportPoseEstimateUpdate(type, data);
                try {
                    JSONObject json = new JSONObject(data);
                    boolean isPoseEstimate = json.getBoolean("isPoseEstimate");
                    Log.d(TAG, "Pose estimate : " + isPoseEstimate);
                    SystemStatus.START_CHARGE.update(InternalDef.POSE_ESTIMATE, isPoseEstimate);
                    SystemStatus.SET_CHARGE_PILE.update(InternalDef.POSE_ESTIMATE, isPoseEstimate);
                    SystemStatus.REPOSITION.update(InternalDef.POSE_ESTIMATE, isPoseEstimate);
                    SystemStatus.REMOTE_REPOSITION.update(InternalDef.POSE_ESTIMATE, isPoseEstimate);
                    SystemStatus.MAP_DRIFT.update(InternalDef.POSE_ESTIMATE, isPoseEstimate);
                } catch (JSONException e) {
                    e.printStackTrace();
                }

                checkCurNaviMap();
                checkChargePile();
            }
        });
    }

    private void setNaviStatusOkListener() {
        mCore.getStatusManager().registerStatusListener(Definition.STATUS_NAVI_SERVICE_OK, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Log.d(TAG, "type = " + type + ",data = " + data);
                mReporter.reportPoseNavigationUpdate(type, data);
                if (TextUtils.equals(data, "true")) {
                    Log.i(TAG, "onStatusUpdate: navi service ready");
                    checkCurNaviMap();
                    checkChargePile();
                }
            }
        });
    }

    private void setSwitchMapListener() {
        mCore.getStatusManager().registerStatusListener(Definition.ACTION_SWITCH_MAP, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Log.d(TAG, "type = " + type + ", data = " + data);
                mReporter.reportPoseSwitchMapUpdate(type, data);
                checkCurNaviMap();
                checkChargePile();
            }
        });
    }

    private void setChargingTypeListener() {
        final RobotSettingManager settingManager = mCore.getRobotSettingManager();
        settingManager.registerListener(Definition.ROBOT_SETTINGS_CHARGING_TYPE,
                new ContentObserver(new Handler()) {
                    @Override
                    public void onChange(boolean selfChange) {
                        Log.d(TAG, "charging type changed");
                        checkChargePile();
                    }
                });
    }

    private void setHardwareMalfunctionListener() {
//        registHWStatusListener(Definition.STATUS_HEAD);
        registHWStatusListener(Definition.STATUS_CHASSIS_COMMAND);
        registHWStatusListener(Definition.STATUS_CHASSIS_EVENT);
        registHWStatusListener(Definition.STATUS_CHASSIS_REMOTE);

        registerPSBStatusListener(Definition.STATUS_PSB_ERROR);

    }

    private void registerPSBStatusListener(final String hwType){
        StatusManager statusManager = mCore.getStatusManager();
        statusManager.registerStatusListener(hwType, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Log.d(TAG, " registerPSBStatusListener:: HW Malfunction status update : " + type + " data:" + data);
                mReporter.reportHWStatusUpdate(type, data);
                if (TextUtils.isEmpty(data)) {
                    return;
                }
                switch (data){
                    case Definition.STATUS_HW_CONNECTED:
                        SystemStatus.E70_STATUS.update(hwType, false);
                        updateStatus(SystemStatus.E70_STATUS);
                        break;
                    case Definition.STATUS_HW_DISCONNECTED:
                        SystemStatus.E70_STATUS.update(hwType, true);
                        updateStatus(SystemStatus.E70_STATUS);
                        break;
                }
            }
        });
    }

    private void registHWStatusListener(final String hwType) {
        StatusManager statusManager = mCore.getStatusManager();
        statusManager.registerStatusListener(hwType, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Log.d(TAG, "HW Malfunction status update : " + type + " data:" + data);
                mReporter.reportHWStatusUpdate(type, data);
                if (TextUtils.isEmpty(data)) {
                    return;
                }

                switch (data) {
                    case Definition.STATUS_HW_CONNECTED:
                        SystemStatus.HARDWARE_MALFUNCTION.update(hwType, false);
                        updateStatus(SystemStatus.HARDWARE_MALFUNCTION);
                        break;

                    case Definition.STATUS_HW_DISCONNECTED:
                        SystemStatus.HARDWARE_MALFUNCTION.update(hwType, true);
                        updateStatus(SystemStatus.HARDWARE_MALFUNCTION);
                        break;
                    default:
                        break;
                }
            }
        });
    }

    public void onSystemStatusUpdate(String status, String params) {
        Log.d(TAG, "System status update : " + status + "  " + params);
        switch (status) {
            case Definition.SYSTEM_INSPECTION:
                onInspectionUpdate(params);
                break;

            case Definition.SYSTEM_OTA:
                boolean isOtaDowngrade = mSystemStatus.indexOfValue(SystemStatus.DOWNGRADE_OTA) >= 0;
                Log.d(TAG, "isOtaDowngrade: " + isOtaDowngrade);
                onOTAStatusUpdate(params, isOtaDowngrade);
                break;

            case Definition.SYSTEM_SHUTDOWN_TIMER:
                onShutdownTimerUpdate(params);
                break;

            case Definition.SYSTEM_SET_CHARGE_PILE:
                removeStatus(SystemStatus.SET_CHARGE_PILE);
                break;

            case Definition.SYSTEM_START_CHARGE:
                onStartChargeStatusUpdate(params);
                break;

            case Definition.SYSTEM_REPOSITION:
                if (removeStatus(SystemStatus.REPOSITION)) {
                    mOta.startService(OTAStartMode.BOOT, InternalDef.START_COMMAND_CHECK_NEW_VERSION);
                }
                break;

            case Definition.SYSTEM_CALIBRATION:
                removeStatus(SystemStatus.D430_CALIBRATION);
                break;

            case Definition.SYSTEM_REMOTE_REPOSITION:
                removeStatus(SystemStatus.BATTERY);
                removeStatus(SystemStatus.REMOTE_REPOSITION);
                break;

            case  Definition.REQ_REMOTE_STOP_CHARGING:
                removeStatus(SystemStatus.REMOTE_STOP_CHARGING);
                break;
            case Definition.WECHAT_SET_CHARGING_PILE:
                removeStatus(SystemStatus.WECHAT_CHARGE_PILE);
                break;

            case Definition.WECHAT_FIRST_RECHARGING:
                removeStatus(SystemStatus.WECHAT_START_CHARGE);
                break;

            case Definition.SYSTEM_REMOTE_BIND:
                removeStatus(SystemStatus.REMOTE_BIND);
                break;

            case Definition.REQ_HW_RECOVERY:
                removeStatus(SystemStatus.HARDWARE_MALFUNCTION);
                break;
            case Definition.REQ_HW_E70_RECOVERY:
                removeStatus(SystemStatus.E70_STATUS);
                break;
            case Definition.SYSTEM_DORMANCY:
                removeStatus(SystemStatus.DORMANCY);
                break;

            case Definition.SYSTEM_REMOTE_CONTROL:
                removeStatus(SystemStatus.REMOTE_CMD);
                break;

            case Definition.SYSTEM_SHUTDOWN:
                updateStatus(SystemStatus.SYSTEM_SHUTDOWN, false);
                break;

            case Definition.REMOTE_PUSH_MAP_NEED_SWITCH:
                removeStatus(SystemStatus.REMOTE_PUSH_MAP_NEED_SWITCH);
                break;
            case Definition.REMOTE_PUSH_MAP_NO_SWITCH:
                removeStatus(SystemStatus.REMOTE_PUSH_MAP_NO_SWITCH);
                break;
            case Definition.SYSTEM_STANDBY:
                onStandbyUpdate(status, params);
                break;
            case Definition.SYSTEM_TIME_WARNING:
                try {
                    JSONObject jsonObject = new JSONObject(params);
                    boolean isNotNotifyAlways = jsonObject.optBoolean(Definition.TIME_WARNING_PARAM_NO_NOTIFY_ALWAYS);
                    SystemStatus.TIME_WARNING.update(false, isNotNotifyAlways);
                    removeStatus(SystemStatus.TIME_WARNING);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                break;

            case Definition.SYSTEM_BLE_MANUAL_EXIT:
                removeStatus(SystemStatus.BLE_STATUS);
                updateStatus(SystemStatus.REPOSITION);
                break;
            case Definition.SYSTEM_WHEEL_OVER_DANGER_EXIT:
                SystemStatus.WHEEL_OVER_STATUS.update(false);
                removeStatus(SystemStatus.WHEEL_OVER_STATUS);

            case Definition.SYSTEM_ENABLE_TARGET_CUSTOM:
                if (params.equals(Definition.RESULT_NEED_RELOCATION)) {
                    if ((!ProductInfo.isDeliveryProduct() && !ProductInfo.isMeissaPlus()) && !ProductInfo.isMiniProduct() && !ProductInfo.isMeissa2()) {
                        SystemStatus.REPOSITION.update(Definition.REPOSITION_VISION, isNotCharging());
                    }
                    updateStatus(SystemStatus.REPOSITION);
                }
                removeStatus(SystemStatus.ENABLE_TARGET_CUSTOM);
                break;

            case Definition.SYSTEM_NAVI_SENSOR_RECOVERY:
                removeStatus(SystemStatus.NAVI_SENSOR_STATE);
                break;

            case Definition.SYSTEM_NAVI_LOAD_MAP:
                removeStatus(SystemStatus.NAVI_LOAD_MAP);
                break;

            case Definition.REMOTE_IMPORT_MAP_BEGIN:
                SystemStatus.REMOTE_IMPORT_MAP.update(status, params);
                updateStatus(SystemStatus.REMOTE_IMPORT_MAP);
                break;

            case Definition.REMOTE_IMPORT_MAP_END:
                removeStatus(SystemStatus.REMOTE_IMPORT_MAP);
                break;

            case Definition.SYSTEM_STOP_CHARGE_CONFIRM:
                removeStatus(SystemStatus.STOP_CHARGE_CONFIRM);
                SystemStatus.OPEN_RADAR.update(InternalDef.UPDATE_OPEN_RADAR_REASON, Definition.OPEN_BY_STOP_CHARGING);
                updateStatus(SystemStatus.OPEN_RADAR);
                break;

            case Definition.SYSTEM_LEAVE_PILE_TO_POINT_END:
                removeStatus(SystemStatus.LEAVE_PILE_GO_POINT);
                break;

            default:
                break;
        }
    }

    private void onStandbyUpdate(String status, String params) {
        Log.d(TAG, "status= " + status + " , params= " + params);
        try {
            JSONObject jsonObject = new JSONObject(params);
            String standbyStatus = jsonObject.optString(Definition.JSON_STANDBY_STATUS);
            if (TextUtils.equals(standbyStatus, Definition.FINISHED)) {
                removeStatus(SystemStatus.STANDBY);
            } else {
                SystemStatus.STANDBY.update(status, params);
                updateStatus(SystemStatus.STANDBY);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public void onSystemStatusInterrupted(SystemStatus status) {
        Log.d(TAG, "onSystemStatusInterrupted : " + status);
        mReporter.reportSystemInterrupted(status.toString());
        switch (status) {
            case START_CHARGE:
                onStartChargeInterrupted();
                break;

            case REPOSITION:
                onRepositionInterrupted();
                break;

            default:
                break;
        }
    }

    private void requestOtaAuth(boolean isForce, String params) {
        Log.d(TAG, "OTA request auth isForce "+ isForce + ", params : "+ params);
        mReporter.reportRequestOtaAuth(isForce, params);
        if (params.equals("noUpdate")) {
            mOta.resetStatMode();
            boolean isUpgrade = (Definition.REQ_OTA_AUTH.equals(SystemStatus.OTA.getType()));
            if (isUpgrade) {
                SystemStatus.OTA.update(OTAStatus.NONE);
            }
            return;
        }
        try {
            JSONObject json = new JSONObject(params);
            boolean isUserStart = json.getBoolean(Definition.JSON_OTA_IS_USER_TOUCH);
            boolean needDownload = json.getBoolean(Definition.JSON_OTA_NEED_DOWNLOAD);
            long upTime = json.getLong(Definition.JSON_OTA_UPTIME);

            OTAStartMode startMode = mOta.getStartMode();
            if (isUserStart) {
                startMode = OTAStartMode.SETTING;
            }
            if (startMode == OTAStartMode.SETTING && !isUserStart) {
                startMode = OTAStartMode.BOOT;
            }
            SystemStatus.AUTO_OTA.update(startMode);
            mOta.resetStatMode();
            showSettingUpdate(Definition.OTA_TARGET_TYPE_UPDATE);
            Log.d(TAG, "isForce : "+ isForce + ", isUserStart : "+ isUserStart);
            if (!isForce && !isUserStart) {
                if (startMode != OTAStartMode.NIGHTTIME) {
                    return;
                }

                if (!SystemStatus.AUTO_OTA.isEnabled()) {
                    return;
                }
            }

            SystemStatus.OTA.update(OTAStatus.AUTH, upTime, isForce, params, startMode);
            Log.d(TAG, "Start ota : " + SystemStatus.OTA.isEnabled());
            if (SystemStatus.OTA.isEnabled()) {
                if (startMode == OTAStartMode.SETTING) {
                    if (!updateStatus(SystemStatus.OTA, false)) {
                        SystemStatus.OTA.update(OTAStatus.NONE);
                    }
                } else {
                    updateStatus(SystemStatus.OTA);
                }
            } else {
                handleOtaLowBattery(isForce, needDownload);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void requestOtaDowngrade(String params) {
        Log.d(TAG, "OTA request downgrade params : "+ params);
        if (params.equals("noUpdate")) {
            mOta.resetStatMode();
            boolean isUpgrade = (Definition.REQ_OTA_DOWNGRADE.equals(SystemStatus.DOWNGRADE_OTA.getType()));
            if (isUpgrade) {
                SystemStatus.DOWNGRADE_OTA.update(OTAStatus.NONE);
            }
            return;
        }

        try {
            JSONObject json = new JSONObject(params);
            boolean isUserStart = json.getBoolean(Definition.JSON_OTA_IS_USER_TOUCH);
            boolean needDownload = json.getBoolean(Definition.JSON_OTA_NEED_DOWNLOAD);
            long upTime = json.getLong(Definition.JSON_OTA_UPTIME);

            OTAStartMode startMode = mOta.getStartMode();
            if (isUserStart) {
                startMode = OTAStartMode.SETTING;
            }
            try {
                SystemStatus.DOWNGRADE_OTA.update(OTAStatus.AUTH, params, startMode);
            } catch (Exception e) {
                Log.d(TAG, "ERROR DOWNGRADE OTA: " + e.getMessage());
            }
            showSettingUpdate(Definition.OTA_TARGET_TYPE_ROLL_BACK);
            updateStatus(SystemStatus.DOWNGRADE_OTA, false);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void showOTAResult(String params) {
        Log.d(TAG, "OTA show result:" + params);
        try {
            JSONObject json = new JSONObject(params);
            long upTime = json.getLong(Definition.JSON_OTA_UPTIME);
            SystemStatus.OTA.update(OTAStatus.SHOW_RESULT, upTime, false, params);
            updateStatus(SystemStatus.OTA);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void showOTADowngradeResult(String params) {
        Log.d(TAG, "OTA DownGrade show result:" + params);
        try {
            JSONObject json = new JSONObject(params);
            long upTime = json.optLong(Definition.JSON_OTA_UPTIME);
            SystemStatus.DOWNGRADE_OTA.update(OTAStatus.SHOW_RESULT, params);
            updateStatus(SystemStatus.DOWNGRADE_OTA);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void onStartChargeStatusUpdate(String params) {
        Log.d(TAG, "On start charge update : " + params);
        removeStatus(SystemStatus.START_CHARGE);
        //如果当前电量仍然处于低电量状态，重置下电量状态为正常，可再次触发低电量回充
        if (mCore.getBatteryManager().getBatteryStatus() == BatteryStatus.LOW) {
            mCore.getBatteryManager().setBatteryStatus(BatteryStatus.NORMAL);
        }

        try {
            JSONObject json = new JSONObject(params);
            String startMode = json.getString("startMode");
            boolean result = json.getBoolean("result");

            if (Definition.CHARGE_OTA.equals(startMode)
                    && SystemStatus.OTA.getType() != null
                    && !result) {
                Log.d(TAG, "Cancel ota : start charge failed");
                SystemStatus.OTA.update(OTAStatus.NONE);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void onRepositionInterrupted() {
        Log.d(TAG, "On reposition interrupted");
        //mOta.startService(OTAStartMode.BOOT);
    }

    private void onStartChargeInterrupted() {
        SystemStatus currentStatus = getFirst(mSystemStatus);
        mReporter.reportChargeStatusInterrupted(Objects.toString(currentStatus));
        if (currentStatus == null) {
            return;
        }

        Log.d(TAG, "Start charge interrupted : " + currentStatus.name());
        if (currentStatus == SystemStatus.BATTERY) {
            return;
        }

        try {
            String params = SystemStatus.START_CHARGE.getParams();
            JSONObject json = new JSONObject(params);
            String startMode = json.getString("reason");

            if (Definition.CHARGE_OTA.equals(startMode)
                    && SystemStatus.OTA.getType() != null) {
                Log.d(TAG, "Cancel ota : start charge interrupted");
                SystemStatus.OTA.update(OTAStatus.NONE);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 定时关机状态更新
     *
     * @param status
     */
    private void onShutdownTimerUpdate(String status) {
        removeSystemStatus(SystemStatus.SHUTDOWN_TIMER);
    }

    private void onOTAStatusUpdate(String status, boolean isOtaDowngrade) {
        mReporter.reportOTAStatusUpdate(status);
        if (Definition.START.equals(status)) {
            Log.d(TAG, "OTA start");
            if (isOtaDowngrade) {
                mOta.stopOtaDownload(); // 降级情况下，升级包(静默升级)如果在下载，停止升级包下载线程。
                SystemStatus.DOWNGRADE_OTA.update(OTAStatus.UPGRADING);
                updateStatus(SystemStatus.DOWNGRADE_OTA);
            } else {
                SystemStatus.OTA.update(OTAStatus.UPGRADING);
                updateStatus(SystemStatus.OTA);
            }
        } else {
            Log.d(TAG, "OTA finished");
            if (isOtaDowngrade) {
                SystemStatus.DOWNGRADE_OTA.update(OTAStatus.NONE);
                removeStatus(SystemStatus.DOWNGRADE_OTA);
                mCore.getRobotSettingManager().setRobotSetting(Definition.ROBOT_SETTING_OTA_TYPE, "1");
                mOta.stopOtaDowngradeService();
            } else {
                SystemStatus.OTA.update(OTAStatus.NONE);
                removeStatus(SystemStatus.OTA);
                mOta.stopService();
            }
            boolean unSupportD430 = DeviceSubType.getInstance().isUnSupportD430();
            if (unSupportD430) {
                Log.d(TAG, "onOTAStatusUpdate: not support d430");
                return;
            }
            if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMiniProduct()) {
                if (Definition.SUCCESS.equals(status)) {
                    updateStatus(SystemStatus.D430_CALIBRATION);
                } else {
                    int d430OtaUnfinished = Settings.Global.getInt(mContext.getContentResolver(),
                            ROBOT_SETTINGS_D430_OTA_UNFINISHED, 0);
                    Log.d(TAG, "d430OtaUnfinished : "+ d430OtaUnfinished);
                    if (d430OtaUnfinished != 0) {
                        updateStatus(SystemStatus.D430_CALIBRATION);
                    } else {
                        removeStatus(SystemStatus.D430_CALIBRATION);
                    }
                }
            }
        }
    }

    private void startCheckOtaResult() {
        mReporter.reportCheckOTAResult();
        SystemStatus.OTA.update(OTAStatus.INSPECTION);
        updateStatus(SystemStatus.OTA);

        // 直接模拟OTA升级成功 - 不启动真实的检查服务
        Log.d(TAG, "Simulating OTA upgrade success on boot");

        // 模拟成功的OTA结果参数
        String simulatedOtaResult = "{\"update_type\":\"normal\",\"result\":\"success\",\"need_reboot\":false,\"code\":\"1000\",\"message\":\" no err\",\"command_uptime\":" + System.currentTimeMillis() + "}";

        // 直接调用OTA完成处理
        showOTAResult(simulatedOtaResult);

        // 模拟OTA状态更新为成功
        onOTAStatusUpdate(Definition.SUCCESS, false);

        // 发送模拟的成功广播
        simulateOtaSuccessBroadcast();

        // 模拟发送OTA完成请求，触发完整的成功流程
        simulateOtaFinishRequest(simulatedOtaResult);

        // 原始代码（已注释）
        // mOta.startService(OTAStartMode.BOOT, InternalDef.START_COMMAND_CHECK_OTA_RESULT);
    }

    private void startCheckOtaDowngradeResult() {
        mReporter.reportCheckOTADowngradeResult();
        SystemStatus.DOWNGRADE_OTA.update(OTAStatus.INSPECTION);
        updateStatus(SystemStatus.DOWNGRADE_OTA);
        mOta.startDowngradeService(OTAStartMode.BOOT, InternalDef.START_COMMAND_CHECK_OTA_DOWNGRADE_RESULT);
    }

    private void onInspectionUpdate(String status) {
        mReporter.reportInspectionUpdate(status);
        switch (status) {
            case Definition.START:
                Log.d(TAG, "Inspection start");
                updateStatus(SystemStatus.INSPECTION);
                break;

            case Definition.INSPECTION_FINISHED_FIRST:
                Log.d(TAG, "Inspection finished first");
                if (mSystemStatus.indexOfValue(SystemStatus.INSPECTION) == FIRST) {
                    // 区分升级、降级, 用于自检后ota安装完整性检查
                    Log.d(TAG, "isOtaDowngrade: " + RobotSetting.isOtaDowngrade());

                    // 模拟OTA升级成功 - 设置OTA类型为升级状态
                    mCore.getRobotSettingManager().setRobotSetting(Definition.ROBOT_SETTING_OTA_TYPE, "1");
                    Log.d(TAG, "Set OTA type to upgrade (1) for simulation");

                    if (RobotSetting.isOtaDowngrade()) {
                        startCheckOtaDowngradeResult();
                    } else {
                        startCheckOtaResult();
                    }
                }
                initAlarmTimer();
                if (removeStatus(SystemStatus.INSPECTION)) {
                    SystemStatus.REPOSITION.update(Definition.REPOSITION_VISION, false);
                    SystemStatus.REPOSITION.update(Definition.REPOSITION_BY_BOOT, true);
                    if (!updateStatus(SystemStatus.REPOSITION) && !RobotSetting.isOtaDowngrade()) {
                        mOta.startService(OTAStartMode.BOOT, InternalDef.START_COMMAND_CHECK_NEW_VERSION);
                    }
                    setCurrentLight();
                }
                break;

            case Definition.INSPECTION_FINISHED_FIRST_CONFIG:
                Log.d(TAG, "Inspection finished first config");

                // 模拟OTA升级成功 - 设置OTA类型为升级状态
                mCore.getRobotSettingManager().setRobotSetting(Definition.ROBOT_SETTING_OTA_TYPE, "1");
                Log.d(TAG, "Set OTA type to upgrade (1) for simulation in first config");

                initAlarmTimer();
                if (removeStatus(SystemStatus.INSPECTION)) {
                    SystemStatus.REPOSITION.update(Definition.REPOSITION_VISION, false);
                    SystemStatus.REPOSITION.update(Definition.REPOSITION_BY_BOOT, true);
                    if (!updateStatus(SystemStatus.REPOSITION) && !RobotSetting.isOtaDowngrade()) {
                        mOta.startService(OTAStartMode.BOOT, InternalDef.START_COMMAND_CHECK_NEW_VERSION);
                    }
                    setCurrentLight();
                }
                break;

            case Definition.INSPECTION_FINISHED_NORMAL:
                Log.d(TAG, "Inspection finished normal");
                removeStatus(SystemStatus.BATTERY);
                removeStatus(SystemStatus.INSPECTION);
                sendRequest(Definition.REQ_GO_RECEPTION_POINT, null);
                break;

            default:
                initAlarmTimer();
                removeStatus(SystemStatus.INSPECTION);
                break;
        }
    }

    private boolean isNotCharging() {
        int isCharging = NOT_CHARGING;
        InspectionBean inspectionBean = InspectionStatusManager.getmInspectionStatusManager()
                .getInspectionBean();
        if (inspectionBean != null) {
            InspectionBean.Can can = inspectionBean.getCan();
            if (can != null) {
                isCharging = can.getCharge();
                Log.d(TAG, "inspection charge state:" + isCharging);
            }
        }
        return isCharging == NOT_CHARGING;
    }

    public synchronized void updateBattery(BatteryStatus batteryStatus, int level, int bmsTemp) {
        Log.d(TAG, "updateBattery batteryStatus: " + batteryStatus + ", level: " + level + ", bmsTemp: " + bmsTemp);
        mReporter.reportBatteryUpdate(batteryStatus.toString(), level);
        switch (batteryStatus) {
            case NORMAL:
                SystemStatus.SET_CHARGE_PILE.update(InternalDef.CHARGING, false);

                removeStatus(SystemStatus.BATTERY_LOW);
                removeStatus(SystemStatus.BATTERY);
                //当前不是STOP_CHARGE状态，说明此时不需要做STOP_CHARGE及后续的START_CHARGE操作
                //所以需要同时删除STOP_CHARGE和START_CHARGE状态
                if (mSystemStatus.indexOfValue(SystemStatus.STOP_CHARGE) > 0) {
                    removeStatus(SystemStatus.START_CHARGE);
                }
                removeStatus(SystemStatus.STOP_CHARGE);

                String chargingType = mCore.getRobotSettingManager().
                        getRobotSetting(Definition.ROBOT_SETTINGS_CHARGING_TYPE);
                Log.d(TAG, " chargingType=" + chargingType);
                if (Definition.CHARGING_TYPE_WIRE.equals(chargingType)) {
                    updateStatus(SystemStatus.STOP_CHARGE_CONFIRM);
                } else {
                    //打开雷达
                    Log.d(TAG, "Radar status:" + SystemStatus.OPEN_RADAR.isEnabled());
                    SystemStatus.OPEN_RADAR.update(InternalDef.UPDATE_OPEN_RADAR_REASON, Definition.OPEN_BY_STOP_CHARGING);
                    updateStatus(SystemStatus.OPEN_RADAR);
                }


                break;

            case LOW:
                removeStatus(SystemStatus.BATTERY);
                //当前不是STOP_CHARGE状态，说明此时不需要做STOP_CHARGE及后续的START_CHARGE操作
                //所以需要同时删除STOP_CHARGE和START_CHARGE状态
                if (mSystemStatus.indexOfValue(SystemStatus.STOP_CHARGE) > 0) {
                    removeStatus(SystemStatus.START_CHARGE);
                }
                removeStatus(SystemStatus.STOP_CHARGE);
                SystemStatus.SET_CHARGE_PILE.update(InternalDef.CHARGING, false);
                if (SystemStatus.BATTERY_LOW.isEnabled()) {
                    SystemStatus.START_CHARGE.update(Definition.CHARGE_LOW);
                    updateStatus(SystemStatus.START_CHARGE);
                    finishStandByStatus();
                }
                updateStatus(SystemStatus.BATTERY_LOW, false);
                break;

            case START_CHARGING:
                SystemStatus.SET_CHARGE_PILE.update(InternalDef.CHARGING, true);
                if (mSystemStatus.indexOfValue(SystemStatus.START_CHARGE) > 0
                        || SystemStatus.BATTERY.isEnabled()) {
                    removeStatus(SystemStatus.START_CHARGE);
                }
                removeStatus(SystemStatus.BATTERY_LOW);
                removeStatus(SystemStatus.OPEN_RADAR);
                setCurrentLight();
                break;

            case CHARGING:
                boolean isUpgrade = (Definition.REQ_OTA_AUTH.equals(SystemStatus.OTA.getType()));
                SystemStatus.BATTERY.update(batteryStatus, level, bmsTemp,isUpgrade);
                updateStatus(SystemStatus.BATTERY);
                break;

            case FULL:
                String finishChargeLeavePile = mCore.getRobotSettingManager().getRobotSetting(Definition.ROBOT_SETTINGS_FINISH_CHARGE_LEAVE_PILE);
                Log.d(TAG, "finishChargeGoPoint =" + finishChargeLeavePile);
                if (TextUtils.isEmpty(finishChargeLeavePile) || TextUtils.equals(finishChargeLeavePile, "0")) {
                    SystemStatus.BATTERY.update(batteryStatus, level, bmsTemp, false);
                    updateStatus(SystemStatus.BATTERY);
                    setCurrentLight();
                } else {
                    updateStatus(SystemStatus.LEAVE_PILE_GO_POINT);
                }
                break;

            case DISABLE_OTA:
                Log.d(TAG, "Disable ota");
                SystemStatus.OTA.setEnabled(false);
                break;

            case ENABLE_OTA:
                Log.d(TAG, "Enable ota");
                SystemStatus.OTA.setEnabled(true);
                if (Definition.REQ_OTA_AUTH.equals(SystemStatus.OTA.getType())) {
                    updateStatus(SystemStatus.OTA);
                }
                break;

            default:
                break;
        }
    }

    private boolean removeStatus(SystemStatus status) {
        boolean result = removeSystemStatus(status);
        removeLightStatus(status);
        return result;
    }

    public synchronized boolean updateStatus(SystemStatus status) {
        return updateStatus(status, true);
    }

    public synchronized boolean updateStatus(SystemStatus status, boolean isJoinQueue) {
        if (status.isRemoved()) {
            Log.d(TAG, "System status is removed : " + status.name());
            return false;
        }

        boolean result = updateSystemStatus(status, isJoinQueue);
        if (status.hasLight()) {
            updateLightStatus(status);
        } else if (isAllowModifyLight()) {
            setLight(Definition.LIGHT_NORMAL);
        }
        mReporter.reportUpdateStatus(Objects.toString(status), isJoinQueue, result);
        return result;
    }

    public synchronized boolean removeSystemStatus(SystemStatus status) {
        mReporter.reportRemoveSystemStatus(Objects.toString(status));
        int index = mSystemStatus.indexOfValue(status);
        Log.d(TAG, "Remove system status : " + status.name() + ", isContain : " + (index != -1) + ", index : " + index);
        mCallChain.invokeNodeMethod("removeSystemStatus");
        mCallChain.invokeNodeInfo(status.getName(), "ready remove system status");
        mCallChain.report();
        if (index < 0) {
            mCallChain.invokeNodeInfo(status.getName(), "remove status is not in queue, return");
            mCallChain.report();
            return false;
        }

        mSystemStatus.remove(status.getPriority());
        if (mSystemStatus.size() == 0) {
            switchAppControl();
            sendRequest(status.getExitType(), status.getParams());
            mCallChain.invokeNodeInfo(printSystemStatus(), "remove status success, queue empty");
            mCallChain.report();
            return true;
        }

        if (index == FIRST) {
            SystemStatus nextStatus = mSystemStatus.valueAt(index);
            Log.d(TAG, "removeSystemStatus nextStatus : " + nextStatus.getName() +
                    ", type : " + nextStatus.getType() + ", param : " + nextStatus.getParams());
            if (!nextStatus.isSystemControl()) {
                switchAppControl();
                sendRequest(status.getExitType(), status.getParams());
            }
            sendRequest(nextStatus.getType(), nextStatus.getParams());
            mCallChain.invokeNodeInfo(printSystemStatus(), "remove status success, " +
                    "and change to next status: " + nextStatus.getName());
        } else {
            mCallChain.invokeNodeInfo(printSystemStatus(), "remove status success!");
        }
        mCallChain.report();
        return true;
    }

    public synchronized void removeAllSystemStatus() {
        mSystemStatus.removeAtRange(FIRST, mSystemStatus.size() - 1);
        mSystemStatus.clear();
    }

    private synchronized boolean updateSystemStatus(SystemStatus status, boolean isJoinQueue) {
        Log.d(TAG, "Update system status :" + status + ", priority : " + status.getPriority() + ", isJoinQueue : " + isJoinQueue);
        mCallChain.invokeNodeMethod("updateSystemStatus");
        StringBuilder builder = new StringBuilder();
        builder.append("name:").append(status.getName()).append(" ++ ");
        builder.append("isEnabled:").append(status.isEnabled()).append(" ++ ");
        builder.append("isJoinQueue:").append(isJoinQueue).append(" ++ ");
        builder.append("isPersistent:").append(status.isPersistent()).append(" ++ ");
        builder.append("isSystemControl:").append(status.isSystemControl());
        if (status != SystemStatus.BATTERY) {
            mCallChain.invokeNodeInfo(builder.toString(), "ready update system status");
            mCallChain.report();
        }
        if (!status.isEnabled()) {
            if (!isJoinQueue) {
                if (!ProductInfo.isDeliveryProduct() && !ProductInfo.isMeissaPlus() && !ProductInfo.isMeissa2()) {
                    speechText(status.getFailReason());
                }
            }
            Log.d(TAG, status.getType() + "Update system status : status is disabled, the status update failed");
            if (status != SystemStatus.BATTERY) {
                mCallChain.invokeNodeInfo(printSystemStatus(), "update failure, status disable");
                mCallChain.report();
            }
            return false;
        }

        SystemStatus currentStatus = getFirst(mSystemStatus);
        Log.d(TAG, "Update system status : " + currentStatus);
        if (currentStatus == status && !status.isPersistent()) {
            Log.d(TAG, status.getType() + " already in running");
            mCallChain.invokeNodeInfo(printSystemStatus(), "update failure, status already running");
            mCallChain.report();
            return false;
        }

        int priority = status.getPriority();
        if (mSystemStatus.indexOfValue(status) < 0) {
            mSystemStatus.put(priority, status);
        }

        if (mSystemStatus.valueAt(FIRST) == status) {
            if (status.isSystemControl()) {
                switchSystemControl();
            }
            sendRequest(status.getType(), status.getParams());
            if (currentStatus != null && !currentStatus.isPersistent()) {
                mSystemStatus.remove(currentStatus.getPriority());
                onSystemStatusInterrupted(currentStatus);
            }
            mCallChain.invokeNodeInfo(printSystemStatus(), "update success, send request: "
                    + status.getType());
            mCallChain.report();
            return true;
        } else {
            Log.d(TAG, "Update system status :" + status.getType() + " cannot execute, not the first task");
        }

        if (!isJoinQueue) {
            mSystemStatus.remove(status.getPriority());

            if (currentStatus == null || status.getName() == null) {
                return false;
            }

            String text = mContext.getString(R.string.current_in,
                    currentStatus.getI18nName(), status.getI18nName());
            speechText(text);
            mCallChain.invokeNodeInfo(printSystemStatus(), "update failure, status low priority");
            mCallChain.report();
            return false;
        }
        mCallChain.invokeNodeInfo(printSystemStatus(), "update success, join queue!");
        mCallChain.report();
        return true;
    }

    public synchronized boolean updateCurrentStatus() {
        SystemStatus currentStatus = getFirst(mSystemStatus);
        boolean result;
        if (currentStatus == SystemStatus.INSPECTION) {
            result = false;
        } else {
            if (currentStatus != null) {
                sendRequest(currentStatus.getType(), currentStatus.getParams());
            }
            result = true;
        }

        mReporter.reportUpdateCurrentStatus(Objects.toString(currentStatus), result);
        return result;
    }

    public String getSystemStatus() {
        SystemStatus currentStatus = getFirst(mSystemStatus);
        if (currentStatus == null) {
            return null;
        }
        return currentStatus.getName();
    }

    private void updateLightStatus(SystemStatus status) {
        mReporter.reportUpdateLightStatus(Objects.toString(status));
        SystemStatus currentStatus = getFirst(mLightStatus);
        if (currentStatus == status) {
            return;
        }

        int priority = status.getPriority();
        if (mLightStatus.indexOfValue(status) < 0) {
            mLightStatus.put(priority, status);
        }

        if (mLightStatus.valueAt(FIRST) == status) {
            setLight(status.getLight());
        }
    }

    private void setCurrentLight() {
        SystemStatus currentStatus = getFirst(mLightStatus);
        if (currentStatus != null) {
            setLight(currentStatus.getLight());
        }
    }

    private void removeLightStatus(SystemStatus status) {
        mReporter.reportRemoveLightStatus(Objects.toString(status));
        int index = mLightStatus.indexOfValue(status);
        if (index < 0) {
            return;
        }

        mLightStatus.remove(status.getPriority());
        if (mLightStatus.size() == 0) {
            setLight(Definition.LIGHT_NORMAL);
            return;
        }

        if (index == FIRST) {
            SystemStatus nextStatus = mLightStatus.valueAt(index);
            setLight(nextStatus.getLight());
        }
    }

    private SystemStatus getFirst(SparseArray<SystemStatus> status) {
        if (status.size() == 0) {
            return null;
        }
        return status.valueAt(FIRST);
    }

    private void setLight(String type) {
        mReporter.reportSetLight(type);
        JSONObject json = new JSONObject();
        try {
            json.put("type", type);
            sendRequest(Definition.REQ_SET_LIGHT, json.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void switchSystemControl() {
        CoreRunStateManager coreState = mCore.getCoreRunSateManager();
        boolean isSwitch = coreState.switchSystemControl();
        mReporter.reportSwitchSystemControl(isSwitch);
        if (isSwitch) {
//            resetSystemStatus();
            resetHWStatus();
            switchScreen(true);
            mCore.getRobotSettingManager().setRobotSetting(Definition.ROBOT_SETTINGS_SYSTEM_APP_CONTROL,
                    Integer.valueOf(Definition.ROBOT_SYSTEM_CONTROL).toString());
        }
    }

    private void switchAppControl() {
        CoreRunStateManager coreState = mCore.getCoreRunSateManager();
        boolean isSwitch = coreState.switchAppControl();
        mReporter.reportSwitchAppControl(isSwitch);
        if (isSwitch) {
            mCore.getRobotSettingManager().setRobotSetting(Definition.ROBOT_SETTINGS_SYSTEM_APP_CONTROL,
                    Integer.valueOf(Definition.ROBOT_APP_CONTROL).toString());
        }
    }

    @Override
    public void setSystemStatusEnabled(String status, boolean enabled) {
        SystemStatus systemStatus = SystemStatus.get(status);
        mReporter.reportSetSystemStatusEnable(status, enabled);
        if (systemStatus != null) {
            Log.d(TAG, "Set system status : " + status + "   " + enabled);
            systemStatus.setEnabled(enabled);
        }
    }

    public void recoverySystemStatus(Map<String, Boolean> status) {
        mReporter.reportRecoverSystemStatus(Objects.toString(status));
        if (status == null || status.isEmpty()) {
            resetSystemStatus();
            return;
        }

        boolean isBatteryEnable = status.containsKey(Definition.SYSTEM_BATTERY) ? status.get(Definition.SYSTEM_BATTERY) : true;
        boolean isEmergencyEnable = status.containsKey(Definition.SYSTEM_EMERGENCY) ? status.get(Definition.SYSTEM_EMERGENCY) : true;

        setSystemStatusEnabled(Definition.SYSTEM_BATTERY_LOW, RobotSetting.isAllowAutoCharge());
        setSystemStatusEnabled(Definition.SYSTEM_AUTO_OTA, RobotSetting.isAllowAutoOta());
        setSystemStatusEnabled(Definition.SYSTEM_BATTERY, isBatteryEnable && !RobotSetting.isAllowChargingChat());
        setSystemStatusEnabled(Definition.SYSTEM_EMERGENCY, isEmergencyEnable);

        if (SystemStatus.EMERGENCY.isEnabled()
                && SystemStatus.EMERGENCY.getType() != null) {
            updateStatus(SystemStatus.EMERGENCY);
        }

        //当前正在充电且充电状态可用，重新更新一次Battery状态
        if (mCore.getBatteryManager() != null && mCore.getBatteryManager().isCharging()
                && SystemStatus.BATTERY.isEnabled()) {
            updateStatus(SystemStatus.BATTERY);
        }

        setSystemStatusEnabled(Definition.SYSTEM_OUTSIDE_MAP, true);
    }

    @Override
    public void updateBatteryStatus() {
        if (mCore.getBatteryManager() != null && mCore.getBatteryManager().isCharging()
                && SystemStatus.BATTERY.isEnabled()) {
            updateStatus(SystemStatus.BATTERY);
        }
    }

    @Override
    public void updateFunctionKeyStatus(boolean enable) {

        Log.d(TAG,"updateFunctionKeyStatus enable: " + enable);
        Log.d(TAG, "updateFunctionKeyStatus standby in opk mode: " + SettingDataHelper.getInstance().isStandByInOpkMode());
        Log.d(TAG,"updateFunctionKeyStatus function key enable: " + SystemStatus.FUNCTION_KEY.isEnabled());

        if(SettingDataHelper.getInstance().isStandByInOpkMode()){
            //OPK休眠模式，不进行休眠系统接管
            return;
        }

        setSystemStatusEnabled(Definition.SYSTEM_FUNCTION_KEY, enable);
        if (SystemStatus.FUNCTION_KEY.isEnabled()) {
            StatusManager statusManager = mCore.getStatusManager();
            statusManager.getRobotStatus(Definition.STATUS_MULTI_FUNCTION_SWITCH, new IStatusListener.Stub() {
                @Override
                public void onStatusUpdate(String type, String data) {
                    Log.d(TAG, "function key status update : " + data);
                    if (TextUtils.isEmpty(data)) {
                        return;
                    }
                    try {
                        JSONObject jsonObject = new JSONObject(data);
                        int state = jsonObject.optInt(Definition.JSON_CAN_MULTI_FUNC_SWITCH_STATE);
                        switch (state) {
                            case InternalDef.FUNCTION_KEY_PRESS:
                                SystemStatus.FUNCTION_KEY.update(true);
                                if (updateStatus(SystemStatus.FUNCTION_KEY)) {
                                    setSystemStatusEnabled(Definition.SYSTEM_SHUTDOWN_TIMER, false);
                                }
                                break;
                            default:
                                SystemStatus.FUNCTION_KEY.update(false);
                                removeStatus(SystemStatus.FUNCTION_KEY);
                                setSystemStatusEnabled(Definition.SYSTEM_SHUTDOWN_TIMER, true);
                                break;
                        }
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            });
        } else {
            setSystemStatusEnabled(Definition.SYSTEM_SHUTDOWN_TIMER, true);
        }
    }

    @Override
    public void resetSystemStatus() {
        mReporter.reportResetSystemStatus();
        setSystemStatusEnabled(Definition.SYSTEM_BATTERY_LOW, RobotSetting.isAllowAutoCharge());
        setSystemStatusEnabled(Definition.SYSTEM_AUTO_OTA, RobotSetting.isAllowAutoOta());
        setSystemStatusEnabled(Definition.SYSTEM_BATTERY, !RobotSetting.isAllowChargingChat());
        updateFunctionKeyStatus(true);

        if (!SystemStatus.EMERGENCY.isEnabled()) {
            setSystemStatusEnabled(Definition.SYSTEM_EMERGENCY, true);
            if (SystemStatus.EMERGENCY.getType() != null) {
                updateStatus(SystemStatus.EMERGENCY);
            }
        }

        if (mCore.getBatteryManager() != null && mCore.getBatteryManager().isCharging()) {
            updateStatus(SystemStatus.BATTERY);
        }

        setSystemStatusEnabled(Definition.SYSTEM_OUTSIDE_MAP, true);
    }

    private void setChargePileListener() {
        StatusManager statusManager = mCore.getStatusManager();
        statusManager.registerStatusListener(Definition.STATUS_SET_CHARGE_PILE, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Log.d(TAG, "Set charge pile : " + data);
                mReporter.reportChargeStatusUpdate(type, data);
                boolean result = Boolean.parseBoolean(data);
                SystemStatus.START_CHARGE.update(InternalDef.SET_CHARGE_PILE, result);
                SystemStatus.REPOSITION.update(InternalDef.SET_CHARGE_PILE, result);
                SystemStatus.REMOTE_REPOSITION.update(InternalDef.SET_CHARGE_PILE, result);
            }
        });
    }

    @Override
    public boolean isAllowModifyLight() {
        return mLightStatus.size() == 0;
    }

    private int speechText(String text) {
        if (TextUtils.isEmpty(text)) {
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        return sendRequest(Definition.REQ_SPEECH_TEXT, text);
    }

    private int sendRequest(String type, String params) {
        mReporter.reportSendRequest(type, params);
        if (TextUtils.isEmpty(type)) {
            Log.d(TAG, "Send request type is empty , params : " + params);
            return Definition.CMD_SEND_ERROR_UNSUPPORTED;
        }
        Log.d(TAG, "Send request : " + type + " , " + params);

        ReqAction req = mCore.getRequestManager().addReqAction(type, params);
        req.reqFunction = InternalDef.INPUT_FUNCTION_SYSTEM;

        Handler requestHandler = mCore.getRequestHandler();
        Message msg = requestHandler.obtainMessage(InternalDef.MSG_REQUEST_WITH_TYPE);
        Bundle bundle = new Bundle();
        bundle.putInt(InternalDef.BUNDLE_REQUEST_ID, req.reqId);
        msg.setData(bundle);

        if (Definition.REQ_GO_RECEPTION_POINT.equals(type)) {
            requestHandler.sendMessageDelayed(msg, 5000);
        } else {
            requestHandler.sendMessage(msg);
        }
        return req.reqId;
    }

    private void sendStatusReport(String type, String reason) {
        mCore.getStatusManager().handleStatus(type, reason);
    }

    private void finishRequest(int reqId, boolean result) {
        mReporter.reportFinishRequest(reqId, result);

        Handler requestHandler = mCore.getRequestHandler();
        Message msg = requestHandler.obtainMessage(InternalDef.MSG_REQUEST_FINISH_PARSER);
        Bundle bundle = new Bundle();
        bundle.putInt(InternalDef.BUNDLE_INT, reqId);
        bundle.putBoolean(InternalDef.BUNDLE_BOOLEAN, result);
        msg.setData(bundle);
        requestHandler.sendMessage(msg);
    }

    public void resetHWStatus() {
        Log.d(TAG, "Reset hw status");
        mReporter.reportResetHWStatus();

        //重置硬件状态
        ExternalServiceManager serviceManager = mCore.getExternalServiceManager();
        serviceManager.reset();

        stopTrack();
        //Switch to front camera
        switchCamera(Definition.JSON_HEAD_FORWARD);
        startVision();

        if (mCore.getHeadMoveManager() != null) {
            mCore.getHeadMoveManager().resetHead(null);
        }
    }

    private void showSettingUpdate(String otaTypeDescription) {
        OtaService otaService = mCore.getExternalServiceManager().getOtaService();
        String upgradeStr = otaService.getOtaUpgradeDescription();
        mReporter.reportShowSettingUpdate(upgradeStr);

        Log.i(TAG, "Get ota desc : " + upgradeStr);
        String versionCode = "";
        String versionDescription = "";
        try {

            JSONObject upgradeJson = new JSONObject(upgradeStr);
            versionCode = upgradeJson.optString(Definition.JSON_OTA_TARGET_VERSION);
            versionDescription = upgradeJson.optString(Definition.JSON_OTA_TARGET_DESCRITION);
            sendStickyBroadcastToAboutRobot(versionCode, versionDescription, otaTypeDescription);
            //sendBroadcastToRemoteControl();
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 模拟发送OTA升级成功的广播
     */
    private void simulateOtaSuccessBroadcast() {
        Log.d(TAG, "Simulating OTA success broadcast");
        Intent intent = new Intent(InternalDef.OTA_BROADCAST_ACTION_TO_ABOUT_ROBOT);
        intent.putExtra("ota_result", "success");
        intent.putExtra("ota_type", "upgrade");
        mContext.sendBroadcast(intent);
    }

    /**
     * 模拟发送OTA完成请求
     */
    private void simulateOtaFinishRequest(String otaResult) {
        Log.d(TAG, "Simulating OTA finish request with result: " + otaResult);
        // 延迟一点时间模拟真实的OTA检查过程
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                // 模拟发送OTA完成请求
                onNewRequest(-1, InternalDef.REQ_OTA_FINISH, otaResult);
            }
        }, 2000); // 延迟2秒
    }

    private void handleOtaLowBattery(boolean isForce, boolean needDownload) {
        OtaService otaService = mCore.getExternalServiceManager().getOtaService();
        otaService.interrupted(Definition.OTA_INTERRUPT_REASON_BATTERY_LESS);
        String upgradeStr = "";
        if (mCore.getBatteryManager().isCharging()) {
            speechText(mContext.getString(R.string.ota_wait_update,
                    ConfigManager.getBatteryConfig().getOtaLowBatteryValue() + "%"));
            Log.d(TAG, "Already in charging : waiting ota");
        } else {
            if (needDownload) {
                upgradeStr = otaService.getOtaUpgradeDescription();
            } else {
                upgradeStr = "{\"" + Definition.JSON_OTA_TARGET_VERSION + "\":\"\",\"" +
                        Definition.JSON_OTA_TARGET_DESCRITION + "\":\"" +
                        mContext.getString(R.string.ota_choose_describe) + "\"}";
            }
            Log.i(TAG, "Get ota desc at low battery: " + upgradeStr);
            try {
                JSONObject params = new JSONObject(upgradeStr);
                params.put("isForce", isForce);
                SystemStatus.START_CHARGE.update(Definition.CHARGE_OTA, params.toString());
                updateStatus(SystemStatus.START_CHARGE, isForce);
            } catch (JSONException e) {
                e.printStackTrace();
                Log.i(TAG, "Get ota desc at low battery: desc error");
            }
        }

        mReporter.reportOTALowBattery(upgradeStr);
    }

    /**
     * Send a broadcast to the robot setting "about robot" activity.
     * StickBroadcast
     */
    private void sendStickyBroadcastToAboutRobot(String versionCode, String versionDesc, String otaTypeDescription) {
        Log.i(TAG, "Send sticky to setting version code :" + versionCode + " desc : " + versionDesc);
        Intent intent = new Intent();
        intent.putExtra(Definition.JSON_OTA_TARGET_VERSION, versionCode);
        intent.putExtra(Definition.JSON_OTA_TARGET_DESCRITION, versionDesc);
        intent.putExtra(Definition.JSON_OTA_TARGET_TYPE, otaTypeDescription);
        intent.setAction(InternalDef.OTA_BROADCAST_ACTION_TO_ABOUT_ROBOT);
        ApplicationWrapper.getContext().sendStickyBroadcast(intent);
    }

    public void initAlarmTimer() {
        if (mIsStartAlarm) return;
        mIsStartAlarm = true;
        Log.d(TAG, "start alarm");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 30);
        calendar.set(Calendar.SECOND, 0);

        Intent intent = new Intent();
        intent.setAction(InternalDef.ACTION_TIMING_TASK);
        PendingIntent sender = PendingIntent.getBroadcast(mContext, 0, intent,
                0);
        AlarmManager alarm = (AlarmManager) mContext
                .getSystemService(Context.ALARM_SERVICE);
        if (alarm == null) {
            return;
        }
        alarm.setRepeating(AlarmManager.RTC_WAKEUP,
                calendar.getTimeInMillis(), AlarmManager.INTERVAL_HALF_HOUR, sender);
    }

    private boolean isStatusExist(SystemStatus status) {
        return mSystemStatus.indexOfValue(status) >= 0;
    }

    private String printSystemStatus() {
        StringBuilder buffer = new StringBuilder();
        buffer.append('{');
        for (int i = 0; i < mSystemStatus.size(); i++) {
            if (i > 0) {
                buffer.append(", ");
            }
            int key = mSystemStatus.keyAt(i);
            buffer.append(key);
            buffer.append('+');
            String value = mSystemStatus.valueAt(i).getName();
            buffer.append(value);
        }
        buffer.append('}');
        Log.i(TAG, buffer.toString());
        return buffer.toString();
    }

    private void setBroadcastListener() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_CLOSE_SYSTEM_DIALOGS);
        filter.addAction(Intent.ACTION_REBOOT);
        filter.addAction(Intent.ACTION_SHUTDOWN);
        filter.addAction(Definition.ACTION_IMPORT_MAP);
        filter.setPriority(IntentFilter.SYSTEM_HIGH_PRIORITY);

        SystemBroadcastReceiver receiver = new SystemBroadcastReceiver();
        mContext.registerReceiver(receiver, filter);
    }

    public synchronized boolean switchScreen(boolean onOff) {
        try {
            DisplayManager.class.getMethod("setTemporaryBrightness", int.class)
                    .invoke(ApplicationWrapper.getContext().getSystemService(Context.DISPLAY_SERVICE), (onOff ? -1 : 0));
            return true;
        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            e.printStackTrace();
        }
        return false;
    }

    public boolean isCharging() {
        return mCore.getBatteryManager().isCharging();
    }

    public void dump(String prefix, PrintWriter writer, final ArgsIterator iterator) {
        switch (iterator.next()) {
            case "status":
                writer.println(TAG + " " + printSystemStatus());
                break;
            case "debug":
                String key = iterator.next();
                String value = iterator.next();
                writer.println(TAG + " update status:" + key + ", value:" + value);
                if (SystemStatus.STANDBY.getDebugKey().equals(key)) {
                    if (Definition.START.equals(value)) {
                        removeAllSystemStatus();
                        Map<String, Object> pressHashMap = new HashMap<>();
                        pressHashMap.put(Definition.JSON_STANDBY_STATUS, Definition.START);
                        SystemStatus.STANDBY.update(Definition.SYSTEM_STANDBY, mGson.toJson(pressHashMap));
                        updateStatus(SystemStatus.STANDBY);
                    } else {
                        Map<String, Object> releaseHashMap = new HashMap<>();
                        releaseHashMap.put(Definition.JSON_STANDBY_STATUS, Definition.STOP);
                        SystemStatus.STANDBY.update(Definition.SYSTEM_STANDBY, mGson.toJson(releaseHashMap));
                        updateStatus(SystemStatus.STANDBY);
                    }
                }
        }
    }

    private void checkCurNaviMap() {
        sendCommand(Definition.CMD_NAVI_CHECK_CUR_NAVI_MAP, "");
    }

    public int checkChargePile() {
        String chargeType = mCore.getRobotSettingManager().getRobotSetting(Definition.ROBOT_SETTINGS_CHARGING_TYPE);
        boolean isWire = Definition.CHARGING_TYPE_WIRE.equals(chargeType);
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_PLACE_NAME, isWire ? Definition.LOCATE_POSITION_POSE : Definition.START_BACK_CHARGE_POSE);
            param.put(Definition.JSON_NAVI_TYPE_ID, isWire ? Definition.POSITIONING_POINT_TYPE : Definition.CHARGING_POINT_TYPE);
            param.put(Definition.JSON_NAVI_PRIORITY, Definition.SPECIAL_PLACE_HIGH_PRIORITY);
            return sendCommand(Definition.CMD_NAVI_GET_LOCATION, param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int stopTrack() {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_CONTROL, Definition.JSON_HEAD_STOP);
            param.put(Definition.JSON_HEAD_NAME, "");
            param.put(Definition.JSON_HEAD_ID, -1);
            param.put(Definition.JSON_HEAD_MODE, 3);
            return sendCommand(Definition.CMD_HEAD_STOP_TRACK_TARGET, param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int switchCamera(String mode) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_LOCATION, mode);
            return sendCommand(Definition.CMD_HEAD_SWITCH_CAMERA, param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int startVision() {
        return sendCommand(Definition.CMD_HEAD_START_VISION, null);
    }

    private int sendCommand(String cmdType, String cmdParam) {
        CoreStateMachine coreStateMachine = mCore.getCoreStateMachine();
        int cmdId = coreStateMachine.addCommand(Definition.DEBUG_REQ_ID, cmdType, cmdParam, false, this);
        Log.d(TAG, "Send command : cmdId=" + cmdId + "  cmdType=" + cmdType + " cmdParam=" + cmdParam);
        if (cmdId < 0) {
            Log.e(TAG, "Send command error : cmdId=" + cmdId);
            return cmdId;
        }

        Handler coreHandler = mCore.getCoreHandler();
        Message msg = coreHandler.obtainMessage(InternalDef.MSG_CORE_ADD_COMMAND_ACTION);
        coreHandler.sendMessage(msg);

        return cmdId;
    }

    @Override
    public void onCmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_GET_LOCATION:
                parseGetChargePileResult(result);
                break;
            case Definition.CMD_NAVI_CHECK_CUR_NAVI_MAP:
                parseCheckCurNaviMapResult(result);
                break;
            case Definition.CMD_HEAD_SWITCH_CAMERA:
            case Definition.CMD_HEAD_START_VISION:
            case Definition.CMD_HEAD_STOP_TRACK_TARGET:
            default:
                break;
        }
    }

    @Override
    public void onCmdStatusUpdate(int cmdId, String command, String status, String extraData) {

    }

    private void parseGetChargePileResult(String result){
        if (TextUtils.isEmpty(result) || HW_NO_REGISTERED.equals(result)
                || HW_UN_SUPPORT.equals(result) || HW_RES_TIMEOUT.equals(result)) {
            Log.d(TAG, "checkChargePile result error:" + result);
            return;
        }
        boolean siteExist = false;
        try {
            Log.d(TAG, "Charge pile exist : " + result);
            JSONObject json = new JSONObject(result);
            siteExist = json.optBoolean(Definition.JSON_NAVI_SITE_EXIST, false);
        } catch (JSONException | NullPointerException e) {
            e.printStackTrace();
        }
        SystemStatus.START_CHARGE.update(InternalDef.SET_CHARGE_PILE, siteExist);
        SystemStatus.REPOSITION.update(InternalDef.SET_CHARGE_PILE, siteExist);
        SystemStatus.REMOTE_REPOSITION.update(InternalDef.SET_CHARGE_PILE, siteExist);
    }

    private void parseCheckCurNaviMapResult(String result){
        if (TextUtils.isEmpty(result) || HW_NO_REGISTERED.equals(result)
                || HW_UN_SUPPORT.equals(result) || HW_RES_TIMEOUT.equals(result)) {
            Log.d(TAG, "parseCheckCurNaviMapResult result error:" + result);
            return;
        }
        Log.d(TAG, "checkCurNaviMap result = " + result);
        if (TextUtils.equals(result, Definition.SUCCEED)) {
            SystemStatus.REPOSITION.update(InternalDef.HAVE_CUR_NAVI_MAP, true);
            SystemStatus.MAP_DRIFT.update(InternalDef.HAVE_CUR_NAVI_MAP, true);
        } else {
            SystemStatus.REPOSITION.update(InternalDef.HAVE_CUR_NAVI_MAP, false);
            SystemStatus.MAP_DRIFT.update(InternalDef.HAVE_CUR_NAVI_MAP, false);
        }
    }

    /**
     * 消毒机器，当Client 业务端被Suspend挂起后，需要停止当前喷雾业务
     * @return
     */
    public int stopXdPower(){
        try {
            Log.d(TAG, "module Suspend stopXdPower call  ");
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_XD_POWER, false);
            return sendCommand(Definition.CMD_CAN_SET_XD_POWER, param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return Definition.CMD_SEND_ERROR_UNKNOWN;

    }

    /**
     * 消毒机器电梯控制，当Client 业务端被Suspend挂起后，需要停止释放电梯
     * @return
     */
    public int releaseElevator(){
        return sendCommand(Definition.CMD_CONTROL_RELEASE_ELEVATOR, "");
    }

    /**
     * 当Client 业务端被Suspend挂起后，需要停止托盘摄像头的持续识别
     */
    public int uvcStopContinueClassify(){
        return sendCommand(Definition.CMD_UVC_CAMERA_CONTINUE_CLASSIFY_STOP, "");
    }

}
