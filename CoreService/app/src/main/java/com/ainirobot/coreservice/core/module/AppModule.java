package com.ainirobot.coreservice.core.module;

import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.service.CoreService;

import org.json.JSONException;
import org.json.JSONObject;

public class AppModule extends Module {

    private static final String TAG = "AppModule";
    public AppModule(CoreService core, String name) {
        super(core, name);
    }
    public AppModule(CoreService core, String name, boolean isSuspend) {
        super(core, name, isSuspend);
    }

    @Override
    public HandleResult handleRequest(int reqId, String reqType, String reqText, String reqParam,
                                      ModuleManager.Permission permission)
            throws RemoteException {
        Log.i(TAG, "current module state: "+ getModuleState());
        if(permission == ModuleManager.Permission.APP_ASK){
            if(getModuleState() == State.IDLE){
                return HandleResult.NORMAL;
            } else if(getModuleState() == State.BUSY){
                getCallback().onSendRequest(reqId, reqType, reqText, reqParam);
                handleRequestWhenModuleBusy(reqId, reqType, reqText, reqParam);
                return HandleResult.INTERRUPT;
            } else {
                getCallback().onSendRequest(reqId, reqType, reqText, reqParam);
                handleRequestWhenModuleBusy(reqId, reqType, reqText, reqParam);
                return HandleResult.INTERRUPT;
            }
        } else {
            getCallback().onSendRequest(reqId, reqType, reqText, reqParam);
            return HandleResult.NORMAL;
        }
    }

    private void handleRequestWhenModuleBusy(int reqId, String reqType, String reqText, String reqParam){
        Log.i(TAG, "handle this request, reqType: "+reqType);
        switch (reqType){
            case Definition.REQ_REMOTE_GO_POSE:
            case Definition.REQ_REMOTE_GO_POSITION:
                JSONObject object = new JSONObject();
                try {
                    object.put("type", reqType);
                    object.put("result", -101);
                    object.put("errmsg", "robot is busy at present, carry out the order by business");
                } catch (JSONException e) {
                    e.printStackTrace();
                    return;
                }
                mCore.getStatusManager().handleStatus(Definition.STATUS_REMOTE_NAVI, object.toString());
                break;
            default:
                break;
        }
    }
}
