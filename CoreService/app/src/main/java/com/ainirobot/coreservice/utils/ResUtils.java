package com.ainirobot.coreservice.utils;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;

import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build.VERSION_CODES;
import android.os.LocaleList;
import android.support.annotation.ArrayRes;
import android.support.annotation.RequiresApi;
import android.support.annotation.StringRes;

import com.ainirobot.coreservice.client.Definition;

import java.util.Locale;


public class ResUtils {

    @RequiresApi(api = VERSION_CODES.N)
    public static String getString(Context context, String language, @StringRes int resId, Object... formatArgs) {
        Locale locale;
        if (language.contains(Definition.UNDERLINE)) {
            String[] languageInfo = language.split(Definition.UNDERLINE);
            locale = new Locale(languageInfo[0], languageInfo[1]);
        } else {
            locale = new Locale(language);
        }

        return getString(context, locale, resId, formatArgs);
    }


    /**
     * 获取对应语言的资源
     *
     * @param context
     * @param resId
     * @param locale
     * @return
     */
    @RequiresApi(api = VERSION_CODES.N)
    public static String getString(Context context, Locale locale, @StringRes int resId, Object... formatArgs) {
        Resources resources = context.getResources();
        Configuration conf = resources.getConfiguration();
        LocaleList localeList = conf.getLocales();
        conf.setLocale(locale);
        resources.updateConfiguration(conf, resources.getDisplayMetrics());
        String content =  resources.getString(resId, formatArgs);
        conf.setLocales(localeList);
        resources.updateConfiguration(conf, resources.getDisplayMetrics());
        return content;
    }

    @RequiresApi(api = VERSION_CODES.N)
    public static String[] getStringArray(Context context, String language, @ArrayRes int resId) {
        Locale locale;
        if (language.contains(Definition.UNDERLINE)) {
            String[] languageInfo = language.split(Definition.UNDERLINE);
            locale = new Locale(languageInfo[0], languageInfo[1]);
        } else {
            locale = new Locale(language);
        }

        return getStringArray(context, locale, resId);
    }

    @RequiresApi(api = VERSION_CODES.N)
    public static String[] getStringArray(Context context, Locale locale, @ArrayRes int resId) {

        Resources resources = context.getResources();
        Configuration conf = resources.getConfiguration();
        LocaleList localeList = conf.getLocales();
        conf.setLocale(locale);
        resources.updateConfiguration(conf, resources.getDisplayMetrics());
        String[] content =  resources.getStringArray(resId);
        conf.setLocales(localeList);
        resources.updateConfiguration(conf, resources.getDisplayMetrics());
        return content;
    }


    /**
     * 获取可以内存，　单位是Byte
     */
    public static long getAvailableMemory(Context context ){
        ActivityManager manager = (ActivityManager)context.getSystemService(Activity.ACTIVITY_SERVICE);
        if (manager != null){
            ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
            manager.getMemoryInfo(memoryInfo);
            return memoryInfo.availMem;
        }
        return 0L;
    }

}
