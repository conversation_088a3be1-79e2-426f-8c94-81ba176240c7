/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.coreservice.core;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;
import android.support.annotation.NonNull;
import android.util.Log;

import com.ainirobot.coreservice.action.ActionManager;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.CommandBean;
import com.ainirobot.coreservice.client.actionbean.HeadTurnBean;
import com.ainirobot.coreservice.config.ConfigManager;
import com.ainirobot.coreservice.config.core.HeadConfig;
import com.ainirobot.coreservice.core.apiproxy.BaseApiProxy;
import com.ainirobot.coreservice.listener.IActionListener;
import com.ainirobot.coreservice.service.CoreService;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

public class HeadTurnManager {

    private static final String TAG = HeadTurnManager.class.getSimpleName();
    private static final int MIN_SPEED = 0;
    private static final int MSG_GET_CAN_SUPPORT_ROTATE = 0x1;
    private static final int MSG_GET_HEAD_STATUS = 0x2;
    private static final int MSG_MOVE_HEAD = 0x3;
    private static final int MSG_MOVE_HEAD_TIMEOUT = 0x4;
    private CoreService mCore;
    private IActionListener mActionListener;
    private HeadTurnBean mHeadTurnBean;
    private Gson mGson;
    private HeadMoveHandler mHandler;
    private boolean mIsSupportRotate;
    private long mTimeout;
    private HeadMoveStatus mStatus = HeadMoveStatus.idle;
    private HeadConfig mConfig;

    private boolean isFixHead = false;

    private enum HeadMoveStatus {
        idle, check, moving
    }

    public HeadTurnManager(CoreService core) {
        mCore = core;
        mGson = new Gson();
        HandlerThread thread = new HandlerThread("HeadMove");
        thread.start();
        mHandler = new HeadMoveHandler(this, thread.getLooper());
        mConfig = ConfigManager.getHeadConfig();
    }

    private class HeadMoveHandler extends Handler {

        private HeadTurnManager mHeadTurnManager;

        private HeadMoveHandler(HeadTurnManager manager, Looper looper) {
            super(looper);
            mHeadTurnManager = manager;
        }

        @Override
        public void handleMessage(Message msg) {
            Log.d(TAG, "handleMessage msg what: " + msg.what);
            switch (msg.what) {
                case MSG_GET_CAN_SUPPORT_ROTATE:
                    mHeadTurnManager.getCanRotateSupport();
                    break;
                case MSG_GET_HEAD_STATUS:
                    mHeadTurnManager.getHeadStatus();
                    break;
                case MSG_MOVE_HEAD:
                    mHeadTurnManager.moveHead();
                    break;
                case MSG_MOVE_HEAD_TIMEOUT:
                    changeStatus(HeadMoveStatus.idle);
                    processResult(Definition.RESULT_TURN_HEAD_TIMEOUT, "turn head timeout");
                    break;
                default:
                    break;
            }
        }
    }

    public int startAction(int actionId, String params, IActionListener listener) {
        Log.d(TAG, "startAction actionId: " + actionId + ", params: " + params);
        ActionManager actionManager = mCore.getActionManager();
        actionManager.exCmd(actionId, params, listener);
        return 0;
    }

    public int stopAction(int actionId, boolean isResetHW) {
        Log.d(TAG, "Stop action : " + actionId + "  " + isResetHW);
        ActionManager actionManager = mCore.getActionManager();
        actionManager.exStopCmd(actionId, isResetHW);
        return 0;
    }

    public synchronized int turnHead(String params, IActionListener listener) {
        Log.d(TAG, "turnHead isEnable: " + mConfig.isEnable() + ", isFixHead: " + isFixHead);
        if (!mConfig.isEnable()) {
            if (listener != null) {
                try {
                    listener.onResultWithExtraData(Definition.RESULT_TURN_HEAD_UN_SUPPORT,
                            "not support head turn", "");
                    listener.onResult(Definition.RESULT_TURN_HEAD_UN_SUPPORT,
                            "not support head turn");
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
            return Definition.CMD_SEND_SUCCESS;
        }

        if (isFixHead) {
            if (listener != null) {
                try {
                    listener.onResultWithExtraData(Definition.RESULT_TURN_HEAD_UN_SUPPORT,
                            "Current is fix head", "");
                    listener.onResult(Definition.RESULT_TURN_HEAD_UN_SUPPORT,
                            "Current is fix head");
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
            return Definition.CMD_SEND_SUCCESS;
        }

        Log.d(TAG, "turnHead config: " + mConfig.toString());
        Log.d(TAG, "turnHead params: " + params + ", mStatus: " + mStatus);
        if (mStatus != HeadMoveStatus.idle) {
            if (mStatus == HeadMoveStatus.check) {
                mHandler.removeMessages(MSG_GET_HEAD_STATUS);
                stopAction(Definition.ACTION_HEAD_GET_STATUS, false);
                mHandler.removeMessages(MSG_GET_CAN_SUPPORT_ROTATE);
            } else {
                mHandler.removeMessages(MSG_GET_HEAD_STATUS);
                stopAction(Definition.ACTION_HEAD_GET_STATUS, false);
                mHandler.removeMessages(MSG_MOVE_HEAD);
                mHandler.removeMessages(MSG_MOVE_HEAD_TIMEOUT);
                stopAction(Definition.ACTION_HEAD_MOVE_HEAD, false);
            }
            processResult(Definition.RESULT_TURN_HEAD_INTERRUPT, "interrupt by other turn head");
        }
        changeStatus(HeadMoveStatus.check);
        mActionListener = listener;
        HeadTurnBean bean = mGson.fromJson(params, HeadTurnBean.class);
        mHeadTurnBean = (bean == null) ? (new HeadTurnBean()) : bean;
        if (mIsSupportRotate) {
            mHandler.sendEmptyMessage(MSG_GET_HEAD_STATUS);
        } else if (mConfig.isSupportHorizontal270()) {
            mHandler.sendEmptyMessage(MSG_GET_CAN_SUPPORT_ROTATE);
        } else {
            mIsSupportRotate = false;
            mHandler.sendEmptyMessage(MSG_GET_HEAD_STATUS);
        }
        return Definition.CMD_SEND_SUCCESS;
    }

    public int turnHead(int vAngle, IActionListener listener) {
        HeadTurnBean bean = new HeadTurnBean();
        bean.setHorizontalMode(HeadTurnBean.HeadTurnMode.absolute);
        bean.setVerticalMode(HeadTurnBean.HeadTurnMode.absolute);
        bean.setHorizontalAngle(mConfig.getDefaultHorizontalAngle());
        if (vAngle > 0) {
            bean.setVerticalAngle(vAngle);
        } else {
            bean.setVerticalAngle(mConfig.getDefaultVerticalAngle());
        }
        return turnHead(mGson.toJson(bean), listener);
    }

    public int resetHead(IActionListener listener) {
        isFixHead = false;
        return turnHead(mConfig.getDefaultVerticalAngle(), listener);
    }

    public int resetHead(int vAngle) {
        isFixHead = false;
        return turnHead(mConfig.getDefaultVerticalAngle(), null);
    }

    public void fixHead(int vAngle) {
        Log.d(TAG, "fixHead vAngle: " + vAngle + ", prev VAngle: " + getVAngle());
        turnHead(vAngle, null);
        isFixHead = true;
    }

    public int getVAngle() {
        if (mHeadTurnBean != null) {
            return mHeadTurnBean.getVerticalAngle();
        }
        return mConfig.getDefaultVerticalAngle();
    }

    private void getCanRotateSupport() {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_GET_ROTATE_SUPPORT, null, false);
        startAction(Definition.ACTION_CAN_GET_ROTATE_SUPPORT, mGson.toJson(bean),
                new BaseApiProxy.ApiListener() {
                    @Override
                    public void onResult(int status, String responseString) {
                        Log.d(TAG, "getCanRotateSupport onResult status: " + status
                                + ", responseString: " + responseString + ", HeadStatus: " + mStatus);
                        if (status == Definition.RESULT_OK) {
                            try {
                                JSONObject jsonObject = new JSONObject(responseString);
                                String responseVersion = jsonObject.optString(Definition
                                        .JSON_CAN_BOARD_APP_VERSION, "");
                                if (responseVersion.equals("H401")) {
                                    mIsSupportRotate = true;
                                }
                            } catch (JSONException e) {
                                e.printStackTrace();
                            }
                        }
                        if (mStatus == HeadMoveStatus.check) {
                            mHandler.sendEmptyMessage(MSG_GET_HEAD_STATUS);
                            processStatus(Definition.STATUS_TURN_HEAD_SUPPORT_ROTATE,
                                    String.valueOf(mIsSupportRotate));
                        }
                    }
                });
    }

    private void getHeadStatus() {
        mHandler.removeMessages(MSG_GET_HEAD_STATUS);
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_GET_STATUS, null, false);
        startAction(Definition.ACTION_HEAD_GET_STATUS, mGson.toJson(bean),
                new BaseApiProxy.ApiListener() {
                    @Override
                    public void onResult(int status, String responseString) {
                        Log.d(TAG, "getHeadStatus onResult status: " + status
                                + ", responseString: " + responseString + ", HeadStatus: " + mStatus);

                        if (status == Definition.ACTION_RESPONSE_STOP_SUCCESS) {
                            return;
                        }

                        int horizontal;
                        int vertical;
                        try {
                            JSONObject jsonObject = new JSONObject(responseString);
                            horizontal = jsonObject.optInt("horizontal", 0);
                            vertical = jsonObject.optInt("vertical", 0) + mConfig.getVerticalAngleOffset();
                        } catch (JSONException e) {
                            horizontal = 0;
                            vertical = 45;
                        }
                        switch (mStatus) {
                            case check:
                                changeStatus(HeadMoveStatus.moving);
                                updateAngle(horizontal, vertical);
                                updateTimeout(horizontal, vertical);
                                mHandler.sendEmptyMessage(MSG_MOVE_HEAD);
                                processStatus(Definition.STATUS_TURN_HEAD_CURRENT_POSITION,
                                        responseString);
                                break;
                            case moving:
                                Log.d(TAG, "isHorizontalMove: " + isHorizontalMove(mHeadTurnBean)
                                        + ", isVerticalMove: " + isVerticalMove(mHeadTurnBean)
                                        + ", horizontal: " + horizontal + ", vertical: " + vertical);
                                if (isHorizontalMove(mHeadTurnBean)) {
                                    if (Math.abs(mHeadTurnBean.getHorizontalAngle() - horizontal) <= 5) {
                                        if (isVerticalMove(mHeadTurnBean)) {
                                            if (Math.abs(mHeadTurnBean.getVerticalAngle() - vertical) <= 5) {
                                                mHandler.removeMessages(MSG_MOVE_HEAD_TIMEOUT);
                                                changeStatus(HeadMoveStatus.idle);
                                                checkVerticalMaxAngle(mHeadTurnBean.getVerticalAngle());
                                                processResult(Definition.RESULT_TURN_HEAD_SUCCESS,
                                                        "turn head success");
                                            } else {
                                                mHandler.sendEmptyMessageDelayed(MSG_GET_HEAD_STATUS,
                                                        100);
                                                processStatus(Definition.STATUS_TURN_HEAD_PROCESS_UPDATE,
                                                        responseString);
                                            }
                                        } else {
                                            mHandler.removeMessages(MSG_MOVE_HEAD_TIMEOUT);
                                            changeStatus(HeadMoveStatus.idle);
                                            checkVerticalMaxAngle(mHeadTurnBean.getVerticalAngle());
                                            processResult(Definition.RESULT_TURN_HEAD_SUCCESS,
                                                    "turn head success");
                                        }
                                    } else {
                                        mHandler.sendEmptyMessageDelayed(MSG_GET_HEAD_STATUS, 100);
                                        processStatus(Definition.STATUS_TURN_HEAD_PROCESS_UPDATE,
                                                responseString);
                                    }
                                } else if (isVerticalMove(mHeadTurnBean)) {
                                    if (Math.abs(mHeadTurnBean.getVerticalAngle() - vertical) <= 5) {
                                        mHandler.removeMessages(MSG_MOVE_HEAD_TIMEOUT);
                                        changeStatus(HeadMoveStatus.idle);
                                        checkVerticalMaxAngle(mHeadTurnBean.getVerticalAngle());
                                        processResult(Definition.RESULT_TURN_HEAD_SUCCESS,
                                                "turn head success");
                                    } else {
                                        mHandler.sendEmptyMessageDelayed(MSG_GET_HEAD_STATUS, 100);
                                        processStatus(Definition.STATUS_TURN_HEAD_PROCESS_UPDATE,
                                                responseString);
                                    }
                                } else {
                                    mHandler.removeMessages(MSG_MOVE_HEAD_TIMEOUT);
                                    changeStatus(HeadMoveStatus.idle);
                                    checkVerticalMaxAngle(mHeadTurnBean.getVerticalAngle());
                                    processResult(Definition.RESULT_TURN_HEAD_SUCCESS,
                                            "turn head success");
                                }
                                break;
                            default:
                                Log.e(TAG, "getHeadStatus onResult status error: " + mStatus);
                                break;
                        }
                    }
                });
    }

    private void checkVerticalMaxAngle(int angle) {
        Log.d(TAG, "checkVerticalMaxAngle angle=" + angle);
        if (angle == mConfig.getMaxVerticalAngle()) {
            processStatus(Definition.STATUS_TURN_HEAD_MAX_DOWN_ANGLE,
                    "Arrived max down angle");
        } else if (angle == mConfig.getMinVerticalAngle()) {
            processStatus(Definition.STATUS_TURN_HEAD_MAX_UP_ANGLE,
                    "Arrived max up angle");
        }
    }

    private void moveHead() {
        Log.d(TAG, "moveHead mHeadTurnBean: " + mHeadTurnBean);
        try {
            processStatus(Definition.STATUS_TURN_HEAD_START,
                    null);
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_HSPEED, mHeadTurnBean.getHorizontalMaxSpeed());
            param.put(Definition.JSON_HEAD_VSPEED, mHeadTurnBean.getVerticalMaxSpeed());
            param.put(Definition.JSON_HEAD_HMODE, getHeadMoveMode(mHeadTurnBean.getHorizontalMode()));
            param.put(Definition.JSON_HEAD_VMODE, getHeadMoveMode(mHeadTurnBean.getVerticalMode()));
            param.put(Definition.JSON_HEAD_HORIZONTAL, mHeadTurnBean.getHorizontalAngle());
            param.put(Definition.JSON_HEAD_VERTICAL, mHeadTurnBean.getVerticalAngle());
            CommandBean bean = new CommandBean(Definition.CMD_HEAD_MOVE_HEAD, param.toString(),
                    false);
            startAction(Definition.ACTION_HEAD_MOVE_HEAD, mGson.toJson(bean),
                    new BaseApiProxy.ApiListener() {
                        @Override
                        public void onResult(int status, String responseString) {
                            Log.d(TAG, "moveHead onResult status: " + status
                                    + ", responseString: " + responseString
                                    + ", HeadStatus: " + mStatus + ", timeout: " + mTimeout);
                            if (status == Definition.ACTION_RESPONSE_STOP_SUCCESS) {
                                return;
                            }
                            if (status == Definition.RESULT_OK) {
                                try {
                                    JSONObject jsonObject = new JSONObject(responseString);
                                    String responseStatus = jsonObject.optString(Definition
                                            .JSON_HEAD_STATUS, "");
                                    if (responseStatus.equals("ok")) {
                                        if (!isHorizontalMove(mHeadTurnBean)
                                                && !isVerticalMove(mHeadTurnBean)) {
                                            changeStatus(HeadMoveStatus.idle);
                                            processResult(Definition.RESULT_TURN_HEAD_SUCCESS,
                                                    "stop turn head success");
                                        } else {
                                            mHandler.sendEmptyMessageDelayed(MSG_MOVE_HEAD_TIMEOUT,
                                                    mTimeout);
                                            mHandler.sendEmptyMessage(MSG_GET_HEAD_STATUS);
                                        }
                                    } else {
                                        changeStatus(HeadMoveStatus.idle);
                                        processResult(Definition.RESULT_TURN_HEAD_FAILED,
                                                "move head failed");
                                    }
                                } catch (JSONException e) {
                                    changeStatus(HeadMoveStatus.idle);
                                    processResult(Definition.RESULT_TURN_HEAD_FAILED,
                                            "move head timeout");
                                }
                            } else {
                                changeStatus(HeadMoveStatus.idle);
                                processResult(Definition.RESULT_TURN_HEAD_FAILED,
                                        "move head failed");
                            }
                        }
                    });
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public void stopAll() {
        Log.d(TAG, "stopAll mStatus: " + mStatus);
        if (mStatus != HeadMoveStatus.idle) {
            changeStatus(HeadMoveStatus.idle);
            processResult(Definition.RESULT_TURN_HEAD_INTERRUPT, "interrupt by other turn head");
        }
    }

    private void processStatus(int status, String data) {
        Log.d(TAG, "processStatus status: " + status + ", data: " + data);
        synchronized (this) {
            if (mActionListener != null) {
                try {
                    mActionListener.onStatusUpdateWithExtraData(status, data, "");
                    mActionListener.onStatusUpdate(status, data);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void processResult(int status, String message) {
        Log.d(TAG, "processResult status: " + status + ", message: " + message + ", listener: " + mActionListener);
        mHandler.removeMessages(MSG_GET_HEAD_STATUS);
        stopAction(Definition.ACTION_HEAD_GET_STATUS, false);
        mHandler.removeMessages(MSG_GET_CAN_SUPPORT_ROTATE);
        mHandler.removeMessages(MSG_MOVE_HEAD);
        mHandler.removeMessages(MSG_MOVE_HEAD_TIMEOUT);
        stopAction(Definition.ACTION_HEAD_MOVE_HEAD, false);
        synchronized (this) {
            if (mActionListener != null) {
                try {
                    mActionListener.onResultWithExtraData(status, message, "");
                    mActionListener.onResult(status, message);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
                mActionListener = null;
            }
        }
    }

    private String getHeadMoveMode(HeadTurnBean.HeadTurnMode mode) {
        return mode == HeadTurnBean.HeadTurnMode.absolute ? Definition.JSON_HEAD_ABSOLUTE
                : Definition.JSON_HEAD_RELATIVE;
    }

    private void updateAngle(int horizontalAngle, int verticalAngle) {
        if (isHorizontalMove(mHeadTurnBean)) {
            if (mHeadTurnBean.getHorizontalMode() == HeadTurnBean.HeadTurnMode.relative) {
                mHeadTurnBean.setHorizontalAngle(mHeadTurnBean.getHorizontalAngle() + horizontalAngle);
                mHeadTurnBean.setHorizontalMode(HeadTurnBean.HeadTurnMode.absolute);
            }

            if (mHeadTurnBean.getHorizontalAngle() > mConfig.getMaxHorizontalAngle()) {
                mHeadTurnBean.setHorizontalAngle(mConfig.getMaxHorizontalAngle());
            } else if (mHeadTurnBean.getHorizontalAngle() < mConfig.getMinHorizontalAngle() && !mIsSupportRotate) {
                mHeadTurnBean.setHorizontalAngle(mConfig.getMinHorizontalAngle());
            } else if (mHeadTurnBean.getHorizontalAngle() < mConfig.getMin270HorizontalAngle() && mIsSupportRotate) {
                mHeadTurnBean.setHorizontalAngle(mConfig.getMin270HorizontalAngle());
            }

            if (mHeadTurnBean.getHorizontalMaxSpeed() > mConfig.getMaxSpeed()) {
                mHeadTurnBean.setHorizontalMaxSpeed(mConfig.getMaxSpeed());
            } else if (mHeadTurnBean.getHorizontalMaxSpeed() <= MIN_SPEED) {
                mHeadTurnBean.setHorizontalMaxSpeed(mConfig.getDefaultSpeed());
            }
        }

        if (isVerticalMove(mHeadTurnBean)) {
            if (mHeadTurnBean.getVerticalMode() == HeadTurnBean.HeadTurnMode.relative) {
                mHeadTurnBean.setVerticalAngle(mHeadTurnBean.getVerticalAngle() + verticalAngle);
                mHeadTurnBean.setVerticalMode(HeadTurnBean.HeadTurnMode.absolute);
            }

            if (mHeadTurnBean.getVerticalAngle() > mConfig.getMaxVerticalAngle()) {
                mHeadTurnBean.setVerticalAngle(mConfig.getMaxVerticalAngle());
            } else if (mHeadTurnBean.getVerticalAngle() < mConfig.getMinVerticalAngle()) {
                mHeadTurnBean.setVerticalAngle(mConfig.getMinVerticalAngle());
            }

            if (mHeadTurnBean.getVerticalMaxSpeed() > mConfig.getMaxSpeed()) {
                mHeadTurnBean.setVerticalMaxSpeed(mConfig.getMaxSpeed());
            } else if (mHeadTurnBean.getVerticalMaxSpeed() <= MIN_SPEED) {
                mHeadTurnBean.setVerticalMaxSpeed(mConfig.getDefaultSpeed());
            }
        }
    }

    private void updateTimeout(int horizontalAngle, int verticalAngle) {
        int horizontalTimeout = 0;
        int verticalTimeout = 0;
        if (isHorizontalMove(mHeadTurnBean)) {
            int moveAngle = Math.abs(mHeadTurnBean.getHorizontalAngle() - horizontalAngle);
            Log.d(TAG, "updateTimeout: horizontal move angle: " + moveAngle);
            horizontalTimeout = moveAngle / mHeadTurnBean.getHorizontalMaxSpeed();
        }

        if (isVerticalMove(mHeadTurnBean)) {
            int moveAngle = Math.abs(mHeadTurnBean.getVerticalAngle() - verticalAngle);
            Log.d(TAG, "updateTimeout: vertical move angle: " + moveAngle);
            verticalTimeout = Math.abs(moveAngle / mHeadTurnBean.getVerticalMaxSpeed());
        }

        if (isVerticalMove(mHeadTurnBean) || isHorizontalMove(mHeadTurnBean)) {
            mTimeout = (Math.max(horizontalTimeout, verticalTimeout) + 2) * 1000;
        }
        Log.d(TAG, "updateTimeout: mTimeout=" + mTimeout);
    }

    private synchronized void changeStatus(HeadMoveStatus status) {
        mStatus = status;
    }

    public boolean isHorizontalMove(@NonNull HeadTurnBean bean) {
        return mConfig.isSupportHorizontal() && bean.isHorizontalMove();
    }

    public boolean isVerticalMove(@NonNull HeadTurnBean bean) {
        return mConfig.isSupportVertical() && bean.isVerticalMove();
    }
}
