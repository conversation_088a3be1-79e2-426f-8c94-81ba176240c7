package com.ainirobot.coreservice.client.actionbean;

/**
 * Created by orion on 2018/4/12.
 */

public class FollowBean extends BaseBean {

//    String personName, int type, long lostTimer
    private String personName;
    private int type;
    private long lostTimer;
    private long detectTimeout;

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public void setType(int type) {
        this.type = type;
    }

    public void setLostTimer(long lostTimer) {
        this.lostTimer = lostTimer;
    }

    public void setDetectTimeout(long detectTimeout) {
        this.detectTimeout = detectTimeout;
    }

    public String getPersonName() {
        return personName;
    }

    public int getType() {
        return type;
    }

    public long getLostTimer() {
        return lostTimer;
    }

    public long getDetectTimeout() {
        return detectTimeout;
    }
}
