/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.client.messagedispatcher;

import com.ainirobot.coreservice.client.listener.ToneListener;

class ToneMessage implements IMsgHandle {
    static final int TYPE_START = 1;
    static final int TYPE_STOP = 2;
    static final int TYPE_ERROR = 3;
    static final int TYPE_COMPLETE = 4;

    private int mType;
    private ToneListener mListener;

    ToneMessage(int type, ToneListener listener) {
        mType = type;
        mListener = listener;
    }

    @Override
    public void handleMessage() {
        switch (mType) {
            case TYPE_START:
                mListener.onStart();
                break;
            case TYPE_STOP:
                mListener.onStop();
                break;
            case TYPE_ERROR:
                mListener.onError();
                break;
            case TYPE_COMPLETE:
                mListener.onComplete();
                break;
        }
    }
}
