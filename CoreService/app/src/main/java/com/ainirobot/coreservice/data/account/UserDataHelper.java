/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.data.account;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.util.Log;

import com.ainirobot.coreservice.client.account.UserBean;
import com.ainirobot.coreservice.data.account.bean.Tables;
import com.ainirobot.coreservice.data.provider.BaseDataHelper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ainirobot.coreservice.data.account.bean.Tables.AUTHORITY_URI;
import static com.ainirobot.coreservice.data.account.bean.Tables.USER;
import static com.ainirobot.coreservice.data.account.bean.Tables.USER_DEVELOP;
import static com.ainirobot.coreservice.data.account.bean.Tables.USER_PRE_RELEASE;

class UserDataHelper extends BaseDataHelper {

    private static final String TAG = UserDataHelper.class.getSimpleName();
    private static final Uri CONTENT_URI = Uri.withAppendedPath(AUTHORITY_URI, USER);
    private static final Uri CONTENT_URI_DEVELOP = Uri.withAppendedPath(AUTHORITY_URI,
            USER_DEVELOP);
    private static final Uri CONTENT_URI_PRE_RELEASE = Uri.withAppendedPath(AUTHORITY_URI,
            USER_PRE_RELEASE);

    private String mRemoteEnv;

    UserDataHelper(Context context, String remoteEnv) {
        super(context);
        mRemoteEnv = remoteEnv;
    }

    @Override
    public Uri getContentUri() {
        switch (mRemoteEnv) {
            case "1":
                return CONTENT_URI_DEVELOP;
            case "2":
                return CONTENT_URI_PRE_RELEASE;
            default:
                return CONTENT_URI;
        }
    }

    boolean checkUserExist(String userId) {
        Log.d(TAG, "checkUserExist userId: " + userId);
        Cursor cursor = query(Tables.UserColumns.USER_ID + "=?",
                new String[] {
                        userId
                });
        if (cursor != null && cursor.moveToNext()) {
            cursor.close();
            return true;
        }
        return false;
    }

    UserBean getFamilyAdmin() {
        UserBean userBean = null;
        Cursor cursor = query(Tables.UserColumns.ROLE_ID + "=?", new String[]{
                UserBean.FAMILY_ADMIN + ""
        });
        if (cursor != null && cursor.moveToNext()) {
            userBean = cursorToUserBean(cursor);
        }
        if (cursor != null) {
            cursor.close();
        }
        return userBean;
    }

    private UserBean cursorToUserBean(Cursor cursor) {
        UserBean user = new UserBean();
        user.setUserId(cursor.getString(cursor.getColumnIndex(Tables.UserColumns.USER_ID)));
        user.setMobile(cursor.getString(cursor.getColumnIndex(Tables.UserColumns.MOBILE)));
        user.setRoleId(cursor.getInt(cursor.getColumnIndex(Tables.UserColumns.ROLE_ID)));
        user.setNickName(cursor.getString(cursor.getColumnIndex(Tables.UserColumns.NICK_NAME)));
        user.setAvatarUrl(cursor.getString(cursor.getColumnIndex(Tables.UserColumns.AVATAR_URL)));
        user.setCreateTime(cursor.getString(cursor.getColumnIndex(Tables.UserColumns.CREATE_TIME)));
        user.setUpdateTime(cursor.getString(cursor.getColumnIndex(Tables.UserColumns.UPDATE_TIME)));
        return user;
    }

    private ContentValues userBeanToContentValue(UserBean userBean) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(Tables.UserColumns.USER_ID, userBean.getUserId());
        contentValues.put(Tables.UserColumns.MOBILE, userBean.getMobile());
        contentValues.put(Tables.UserColumns.ROLE_ID, userBean.getRoleId());
        contentValues.put(Tables.UserColumns.NICK_NAME, userBean.getNickName());
        contentValues.put(Tables.UserColumns.AVATAR_URL, userBean.getAvatarUrl());
        contentValues.put(Tables.UserColumns.CREATE_TIME, userBean.getCreateTime());
        contentValues.put(Tables.UserColumns.UPDATE_TIME, userBean.getUpdateTime());
        contentValues.put(Tables.UserColumns.TOKEN_ID,
                userBean.getUserToken() == null ? null : userBean.getUserToken().getTokenId());
        return contentValues;
    }

    List<UserBean> getUserList() {
        List<UserBean> users = new ArrayList<>();
        Cursor cursor;
        cursor = query(null, null);
        while(cursor.moveToNext()) {
            users.add(cursorToUserBean(cursor));
        }
        cursor.close();
        return users;
    }

    Map<String, UserBean> getUserMap() {
        Map<String, UserBean> users = new HashMap<>();
        Cursor cursor;
        cursor = query(null, null);
        while(cursor.moveToNext()) {
            UserBean bean = cursorToUserBean(cursor);
            users.put(bean.getUserId(), bean);
        }
        cursor.close();
        return users;
    }

    UserBean getUserByUserId(String userId) {
        UserBean userBean = null;
        Cursor cursor = query(Tables.UserColumns.USER_ID + "=?", new String[]{
                userId
        });
        if (cursor != null && cursor.moveToNext()) {
            userBean = cursorToUserBean(cursor);
        }
        if (cursor != null) {
            cursor.close();
        }
        return userBean;
    }

    void updateRecentUser(String userId) {
        ContentValues clearContentValues = new ContentValues();
        clearContentValues.put(Tables.UserColumns.IS_RECENT, 0);
        update(clearContentValues, null, null);
        ContentValues contentValues = new ContentValues();
        contentValues.put(Tables.UserColumns.IS_RECENT, 1);
        update(contentValues, Tables.UserColumns.USER_ID + "=?", new String[]{
                userId
        });
    }

    UserBean getRecentUser() {
        UserBean userBean = null;
        Cursor cursor = query(Tables.UserColumns.IS_RECENT + "=?", new String[]{
                String.valueOf(1)
        });
        if (cursor != null && cursor.moveToNext()) {
            userBean = cursorToUserBean(cursor);
        }
        if (cursor != null) {
            cursor.close();
        }
        return userBean;
    }

//    int insertUserList(List<UserBean> userBeanList) {
//        List<ContentValues> dataList = new ArrayList<>();
//        for (UserBean userBean : userBeanList) {
//            dataList.add(userBeanToContentValue(userBean));
//        }
//        ContentValues[] dataArray = new ContentValues[dataList.size()];
//        return bulkInsert(dataList.toArray(dataArray));
//    }

    Uri insertUser(UserBean bean) {
        return insert(userBeanToContentValue(bean));
    }

    int updateUser(UserBean userBean) {
        ContentValues contentValues = userBeanToContentValue(userBean);
        return update(contentValues, Tables.UserColumns.USER_ID + "=?", new String[]{
                userBean.getUserId()
        });
    }

    String getTokenIdByUserId(String userId) {
        Log.d(TAG, "getTokenIdByUserId userId: " + userId);
        String tokenId = null;
        Cursor cursor = query(Tables.UserColumns.USER_ID + "=?",
                new String[]{
                        userId
                });
        if (cursor != null && cursor.moveToNext()) {
            tokenId = cursor.getString(cursor.getColumnIndex(Tables.UserColumns.TOKEN_ID));
            cursor.close();
        }
        return tokenId;
    }

    void updateAllUserTokenId(String tokenId) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(Tables.UserColumns.TOKEN_ID, tokenId);
        update(contentValues, null, null);
    }

    int deleteUser(String userId) {
        return delete(Tables.UserColumns.USER_ID + "=?",
                new String[]{
                        userId
                });
    }
//
//    int deleteAllUser() {
//        return delete(null, null);
//    }
}
