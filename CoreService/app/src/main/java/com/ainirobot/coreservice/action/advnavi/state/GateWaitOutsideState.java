package com.ainirobot.coreservice.action.advnavi.state;

import android.util.Log;

import com.ainirobot.coreservice.action.StopCommandList;
import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.action.advnavi.gatecmd.GateControlUtil;
import com.ainirobot.coreservice.action.advnavi.gatecmd.GateControlUtilListener;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.utils.DelayTask;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Date: 2024/2/22
 * Author: dengting
 * Description: GateWaitOutsideState
 * 需要不上控制之间的状态
 */
public class GateWaitOutsideState extends BaseState {

    private Status status = Status.IDLE;
    //闸机外等待超时
    private final long WAIT_TIMEOUT_MINUTE = TimeUnit.MINUTES.toMillis(15);
    //延迟执行时间
    private final long NOT_WAIT_TIME = 0L;
    private GateControlUtil controlUtil;

    private enum Status {
        IDLE,
        OPEN_FAILED,    //开门失败
        OPENED         //闸机已打开
    }

    GateWaitOutsideState(String name) {
        super(name);
    }

    @Override
    protected void preAction(StateData data, IStateListener listener) {
        super.preAction(data, listener);
        controlUtil = new GateControlUtil(this,
                elevatorControlUtilListener);
        updateCurrentStatus(Status.IDLE);
    }

    @Override
    protected void doAction() {
        super.doAction();
        delayStartAction();
    }

    @Override
    protected void exit() {
        cancelWaitTimer();
        if (null != controlUtil) {
            //这里不释放电梯，后面要控制电梯门
            controlUtil.stopControlGate();
        }
        super.exit();
    }

    @Override
    protected BaseState getNextState() {
        Log.d(TAG, "getNextState " + actionState);
        if (actionState == Definition.ElevatorAction.ACTION_GATE_OPENED) {
            return StateEnum.GO_GATE_SECOND.getState();
        }
        return super.getNextState();
    }

    @Override
    protected boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        Log.d(TAG, "cmdResponse " + command + result + extraData);
        switch (command) {
            case Definition.CMD_CONTROL_CALL_GATE:
                break;
            case Definition.CMD_CONTROL_OPEN_GATE_DOOR:
            case Definition.CMD_CONTROL_OPEN_REV_GATE_DOOR:
                callCmdResponse(result, extraData);
                return true;
        }
        if (null != controlUtil
                && controlUtil.cmdResponse(command, result, extraData)) {
            return true;
        }
        return super.cmdResponse(cmdId, command, result, extraData);
    }

    @Override
    protected List<StopCommandList.StopCommand> getStopCommands() {
        return new ArrayList<StopCommandList.StopCommand>() {{
            add(new StopCommandList.StopCommand(Definition.CMD_CONTROL_CLOSE_GATE_DOOR, null));
            add(new StopCommandList.StopCommand(Definition.CMD_CONTROL_RELEASE_GATE, null));
        }};
    }

    private void callCmdResponse(String result, String extraData) {
        Log.d(TAG, "call cmd response [ " + result + " ]  extraData:" + extraData);
        switch (result) {
            case Definition.FAILED:
                return;
            case Definition.SUCCEED:
                gateOpened();
        }
    }

    @Override
    protected boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        Log.d(TAG, "cmdStatusUpdate: " + cmdId + command + status + extraData);
        if (null != controlUtil
                && controlUtil.cmdResponse(command, status, extraData)) {
            return true;
        }
        return super.cmdStatusUpdate(cmdId, command, status, extraData);
    }

    private void updateCurrentStatus(Status status) {
        Log.d(TAG, "updateCurrentStatus " + this.status + " --> " + status);
        this.status = status;
    }

    /**
     * 根据不同事件，获得延迟执行的事件
     * 如进梯失败返回电梯口事件，需延迟一段时间再进行呼梯
     *
     * @return 延迟的毫秒
     */
    private long getDelayStartTime() {
//        if (mStateData.getActionCode() == Definition.ElevatorAction.ACTION_BACK_GATE) {
//            Log.d(TAG, "need delay start");
//            return WAIT_START_TIME_MINUTE;
//        }
        return NOT_WAIT_TIME;
    }

    /**
     * 延迟一段时间再执行 开门
     */
    private void delayStartAction() {
        long delayTime = getDelayStartTime();
        DelayTask.cancel(TAG);
        DelayTask.submit(TAG, new Runnable() {
            @Override
            public void run() {
                if (!isAlive()) {
                    return;
                }
                if (null != controlUtil) {
                    //todo
                    controlUtil.startOpenDoor();
                }
                startWaitTimer();
            }
        }, delayTime);
    }

    /**
     * 电梯到达，更新事件：elevator_arrived
     */
    private void gateOpened() {
        Log.d(TAG, "gateOpened: ");
        updateCurrentStatus(Status.OPENED);
        updateAction(Definition.ElevatorAction.ACTION_GATE_OPENED);
        onStatusUpdate(Definition.STATUS_GATE_OPENED, "gate opened");
        onSuccess();
    }
    // ---------    超时    ----------- //

    /**
     * 开始超时等待
     */
    private void startWaitTimer() {
        DelayTask.cancel(TAG);
        DelayTask.submit(TAG, new Runnable() {
            @Override
            public void run() {
                if (!isAlive()) {
                    return;
                }
                Log.d(TAG, "wait time out !");
                waitTimeOut();
            }
        }, WAIT_TIMEOUT_MINUTE);
    }

    /**
     * 超时处理
     */
    private void waitTimeOut() {
        onResult(Definition.ERROR_OPEN_GATE_DOOR_FAILED, Definition.PARAMS_TIMEOUT);
    }

    private void cancelWaitTimer() {
       // cancelCallGate();
        DelayTask.cancel(TAG);
    }

    /**
     * 控制电梯开门时状态监听
     */
    private final GateControlUtilListener elevatorControlUtilListener = new GateControlUtilListener() {
        @Override
        public void onGateStatusUpdate(int id, String param, String extraData) {
            Log.d(TAG, "onGateStatusUpdate: ");
            onStatusUpdate(id, param, extraData);
        }

        @Override
        public void onGateResult(int id, String param) {
            Log.d(TAG, "GateControlUtilListener onGateResult: ");
            onGateCmdResult(id, param);
        }

        @Override
        public void onSuccess() {
            Log.d(TAG, "GateControlUtilListener status:  onSuccess");
            gateOpened();
        }
    };

    private void onGateCmdResult(int id, String param) {
        Log.d(TAG, "onGateCmdResult status:" + id + " , data:" + param);
        switch (id) {
            case Definition.ERROR_GATE_CONTROL:
            case Definition.ERROR_CALL_GATE_FAILED:
            case Definition.ERROR_CLOSE_GATE_DOOR_FAILED:
            case Definition.ERROR_OPEN_GATE_DOOR_FAILED:
                onResult(Definition.ERROR_OPEN_GATE_DOOR_FAILED, param);
                break;
            default:
                onResult(id, param);
                break;
        }
    }


}
