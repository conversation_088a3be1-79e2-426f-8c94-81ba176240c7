/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.action;

import android.util.Log;

import com.ainirobot.coreservice.action.StopCommandList.IStopCommandChecker;
import com.ainirobot.coreservice.action.StopCommandList.StopCommand;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.Person;
import com.ainirobot.coreservice.client.actionbean.SearchPersonBean;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.utils.MessageParser;
import com.ainirobot.robotlog.RobotLog;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Timer;
import java.util.TimerTask;

public class SearchPersonAction extends AbstractAction<SearchPersonBean> {
    private final String TAG = "SearchPersonAction";

    private enum MoveHeadState {
        IDLE, MOVE_LEFT, MOVE_RIGHT, MOVE_RESET
    }

    private int mReqId;
    private int mPersonId;
    private Timer mTimer;
    private MoveHeadState mMoveHeadState = MoveHeadState.IDLE;
    private boolean isTargetFound = false;
    private int mLostAngle = 0;
    private Timer mSearchTimer;
    private long mSearchTimeout;

    protected SearchPersonAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_SEARCH_TARGET, resList);
    }

    @Override
    protected boolean startAction(SearchPersonBean parameter, LocalApi api) {
        if (verifyParam(parameter)) {
            isTargetFound = false;
            mReqId = parameter.getReqId();
            mApi.getLastPosition(mReqId, "", mPersonId);
            startSearchTimer();
            return true;
        }
        return false;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        mApi.stopSendPersonInfos();
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_HEAD_GET_LAST_POSITION:
                processLastPosition(result);
                return true;

            case Definition.CMD_HEAD_GET_STATUS:
                handleHeadStatus(result);
                return true;

            case Definition.CMD_HEAD_GET_ALL_PERSON_INFOS:
                processFaceInfo(result);
                return true;
        }
        return false;
    }

    private void processLastPosition(String result) {
        try {
            JSONObject json = new JSONObject(result);
            String name = json.getString("name");
            mLostAngle = json.getInt("horizontal");

            startBeginSearch();
        } catch (JSONException e) {
            e.printStackTrace();
            onError(Definition.RESULT_FAILURE, "Get last position failed");
        }
    }

    private void processBeginSearch(int angle) {
        mApi.getAllPersonInfos(mReqId, Definition.JSON_HEAD_CONTINUOUS);
        if (angle <= 0) {
            mApi.moveHead(mReqId, Definition.JSON_HEAD_RELATIVE,
                    Definition.JSON_HEAD_RELATIVE, -40, 0, 15, 0);
            startMoveHeadTimer(1, 2600);
        } else {
            mApi.moveHead(mReqId, Definition.JSON_HEAD_RELATIVE,
                    Definition.JSON_HEAD_RELATIVE, 40, 0, 15, 0);
            startMoveHeadTimer(2, 2600);
        }
    }

    private void processFaceInfo(String result) {
        Person person = MessageParser.parseFaceInfo(null, result);
        if (person != null && !isTargetFound) {
            cancelTimer();
            cancelSearchTimer();
            RobotLog.d("Get person info by name : " + person.getRegisterName());
            isTargetFound = true;
            onResult(Definition.RESULT_OK, "Target found");
        }
    }

    private void startMoveHeadTimer(final int mode, final long time) {
        cancelTimer();
        mTimer = new Timer();
        mTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                Log.i("SearchPersonAction", "mode is:" + mode);
                if (mode == 1) {
                    mMoveHeadState = MoveHeadState.MOVE_RIGHT;
                    mApi.moveHead(mReqId, Definition.JSON_HEAD_ABSOLUTE,
                            Definition.JSON_HEAD_RELATIVE, 40, 0, 15, 0);
                } else if (mode == 2) {
                    mMoveHeadState = MoveHeadState.MOVE_LEFT;
                    mApi.moveHead(mReqId, Definition.JSON_HEAD_ABSOLUTE,
                            Definition.JSON_HEAD_RELATIVE, -40, 0, 15, 0);
                }
                mApi.getHeadStatus(mReqId);
            }
        }, time);
    }

    private void handleHeadStatus(String params) {
        Log.d(TAG, "onCmdResponse CMD_HEAD_GET_STATUS params:" + params);
        int horizontal = 0, hspeed = 0, vertical = 0, vspeed = 0;
        try {
            JSONObject jsonObject = new JSONObject(params);
            horizontal = jsonObject.optInt("horizontal", 0);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        int moveAngle = 0;

        int tag = 40;

        switch (mMoveHeadState) {
            case MOVE_LEFT:
                if (horizontal >= 0) {
                    moveAngle = horizontal + tag;
                } else if (horizontal <= -tag) {
                    moveAngle = -tag - horizontal;
                } else if (horizontal > -tag && horizontal < 0) {
                    moveAngle = tag + horizontal;
                }
                Log.i(TAG, "MOVE_LEFT, move angle:" + moveAngle);
                break;

            case MOVE_RIGHT:
                if (horizontal <= 0) {
                    moveAngle = -horizontal + tag;
                } else if (horizontal >= tag) {
                    moveAngle = horizontal - tag;
                } else {
                    moveAngle = tag - horizontal;
                }
                Log.i(TAG, "MOVE_RIGHT, move angle:" + moveAngle);
                break;

            default:

                break;
        }

        startResetHeadTimer(moveAngle * 1000 / 15);
    }

    private void cancelTimer() {
        if (mTimer != null) {
            mTimer.cancel();
            mTimer = null;
        }
    }

    private void cancelSearchTimer() {
        if (mSearchTimer != null) {
            mSearchTimer.cancel();
            mSearchTimer = null;
        }
    }

    private void startResetHeadTimer(long time) {
        Log.i(TAG, "start reset head timer:" + time);
        cancelTimer();
        mTimer = new Timer();
        mTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                Log.i(TAG, "reset head");
                if (isTargetFound) {
                    return;
                }
                mApi.resetHead(mReqId);
                //startResultTimer();
            }
        }, time);
    }

    private void startResultTimer() {
        cancelTimer();
        mTimer = new Timer();
        mTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                Log.i(TAG, "reset head");
                if (isTargetFound) {
                    return;
                }
                onResult(Definition.RESULT_FAILURE, mPersonId + " not found");
            }
        }, 4000);
    }

    private void startSearchTimer() {
        mSearchTimer = new Timer();
        mSearchTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                RobotLog.d("Search timeout");
                if (isTargetFound) {
                    return;
                }
                onResult(Definition.RESULT_FAILURE, mPersonId + " not found");
            }
        }, mSearchTimeout);
    }

    private void startBeginSearch() {
        cancelTimer();
        mTimer = new Timer();
        mTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                Log.i(TAG, "reset head");
                if (isTargetFound) {
                    return;
                }
                processBeginSearch(mLostAngle);
            }
        }, 1000);
    }

    public boolean verifyParam(SearchPersonBean params) {
        mPersonId = params.getPersonId();
        mSearchTimeout = params.getSearchTimeout();
        return mPersonId >= 0;
    }

    @Override
    protected ArrayList<StopCommand> getStopCommands() {
        return null;
    }

    @Override
    protected IStopCommandChecker getStopCommandChecker() {
        return null;
    }

}
