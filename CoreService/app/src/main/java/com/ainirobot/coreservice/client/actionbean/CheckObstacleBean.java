package com.ainirobot.coreservice.client.actionbean;

public class CheckObstacleBean extends BaseBean {
    double startAngle;
    double endAngle;
    double distance;

    public CheckObstacleBean(double startAngle, double endAngle, double distance) {
        this.startAngle = startAngle;
        this.endAngle = endAngle;
        this.distance = distance;
    }

    public double getStartAngle() {
        return startAngle;
    }

    public void setStartAngle(double startAngle) {
        this.startAngle = startAngle;
    }

    public double getEndAngle() {
        return endAngle;
    }

    public void setEndAngle(double endAngle) {
        this.endAngle = endAngle;
    }

    public double getDistance() {
        return distance;
    }

    public void setDistance(double distance) {
        this.distance = distance;
    }
}
