/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.ainirobot.coreservice.action;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.PictureInfo;
import com.ainirobot.coreservice.client.actionbean.RegisterRemoteBean;
import com.ainirobot.coreservice.client.actionbean.RemoteRegisterBean;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.utils.MessageParser;
import com.ainirobot.coreservice.utils.VisionUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class RegisterAction extends AbstractAction<RemoteRegisterBean> {

    private static final String TAG = RegisterAction.class.getSimpleName();
    private static final int PICTURE_NUMBER = 1;

    private enum State {
        IDLE, GET_PICTURE, REGISTERING
    }

    private int mReqId = 0;
    private int mPersonId;
    private String mName;
    private String mPicPath;
    private String welcomeContent;

    private volatile State mState = State.IDLE;

    RegisterAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_REGISTER_v0, resList);
    }

    @Override
    protected boolean startAction(RemoteRegisterBean parameter, LocalApi api) {
        mPersonId = parameter.getPersonId();
        mName = parameter.getPersonName();
        welcomeContent = parameter.getWelcomeContent();
        Log.d(TAG, "startAction personName: " + mName + ", personId: " + mPersonId);
        if (TextUtils.isEmpty(parameter.getPicturePath())) {
            updateState(State.GET_PICTURE);
            mApi.getPictureById(mReqId, mPersonId, PICTURE_NUMBER);
        } else {
            updateState(State.REGISTERING);
            PictureInfo info = new PictureInfo();
            info.setName(mName);
            info.setWelcomeContent(welcomeContent);
            List<String> pictureList = new ArrayList<>();
            pictureList.add(parameter.getPicturePath());
            info.setPictures(pictureList);
            mApi.remoteRegister(0, info);
        }
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        Log.d(TAG, "cmdResponse cmdId: " + cmdId + ", command: " + command
                + ", result: " + result + ", isRunning: " + isRunning());
        if (!isRunning()) {
            return false;
        }
        switch (command) {
            case Definition.CMD_HEAD_GET_PICTURE_BY_ID:
                handleGetPictureById(result);
                return true;
            case Definition.CMD_REMOTE_ADD_FACE:
                handleRemoteRegister(result);
                return true;
            default:
                return false;
        }
    }

    private void handleGetPictureById(String result) {
        Log.d(TAG, "handleGetPictureById result: " + result + ", state: " + mState);
        if (mState != State.GET_PICTURE) {
            return;
        }
        String status = null;
        JSONArray pictures = null;
        int personId = -1;
        try {
            JSONObject json = new JSONObject(result == null ? "" :
                    result);
            status = json.optString("status");
            pictures = json.optJSONArray("pictures");
            personId = json.optInt("id", -1);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        Log.d(TAG, "handleGetPictureById status: " + status + ", id: " + personId
                + ", pictures: " + pictures);
        if (mPersonId != personId || pictures == null || pictures.length() <= 0
                || TextUtils.isEmpty(pictures.optString(0))) {
            onError(Definition.RESULT_FAILURE, "get picture failed");
        } else {
            mPicPath = pictures.optString(0);
            updateState(State.REGISTERING);
            PictureInfo info = new PictureInfo();
            info.setName(mName);
            info.setWelcomeContent(welcomeContent);
            List<String> pictureList = new ArrayList<>();
            pictureList.add(mPicPath);
            info.setPictures(pictureList);
            mApi.remoteRegister(mReqId, info);
        }
    }

    private void handleRemoteRegister(String result) {
        Log.d(TAG, "handleRemoteRegister result: " + result + ", state: " + mState);
        if (mState != State.REGISTERING) {
            return;
        }
        String faceId = null;
        try {
            JSONObject json = new JSONObject(result == null ? "" :
                    result);
            JSONObject data = json.optJSONObject("data");
            Log.d(TAG, "handleAddFace data: " + data);
            if (data != null) {
                JSONObject user = data.optJSONObject("user_info");
                Log.d(TAG, "handleAddFace user: " + user);
                if (user != null) {
                    faceId = user.optString("user_outer_id");
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        Log.d(TAG, "handleAddFace faceId: " + faceId);
        RegisterRemoteBean bean = MessageParser.parseRemoteResult(result);
        if (bean != null) {
            if (bean.getCode() == 0) { // 0  success ;  else  fail
                onResult(Definition.RESULT_OK, mGson.toJson(bean));
            } else if (bean.getCode() == Definition.REMOTE_CODE_FACE_INVALID) { // face picture invalid in Server
                onResult(Definition.RESULT_FAILURE, "face invalid");
            } else {
                onResult(Definition.RESULT_FAILURE, "code: " + bean.getCode() + ", message = " + bean.getMessage());
            }
        } else {
            onResult(Definition.RESULT_FAILURE, result);
        }
    }

    private void updateState(State newState) {
        Log.d(TAG, "updateState: newState: " + newState + ", state: " + mState);
        mState = newState;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        Log.d(TAG, "stopAction");
        VisionUtils.deletePicture(mPicPath);
        mState = State.IDLE;
        return true;
    }

    @Override
    protected boolean verifyParam(RemoteRegisterBean param) {
        return param != null && !TextUtils.isEmpty(param.getPersonName())
                && !TextUtils.isEmpty(param.getFaceId());
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return null;
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return null;
    }
}