/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
package com.ainirobot.coreservice.core;

import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.actionbean.FaceAngle;
import com.ainirobot.coreservice.client.actionbean.UserData;
import com.ainirobot.coreservice.client.listener.Person;
import com.ainirobot.coreservice.client.person.PersonResult;
import com.ainirobot.coreservice.core.apiproxy.result.BaseApiResult;
import com.ainirobot.coreservice.core.person.bean.RequestBean;
import com.ainirobot.coreservice.listener.IActionListener;
import com.ainirobot.coreservice.service.CoreService;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static com.ainirobot.coreservice.core.person.PersonManager.MSG_RECOGNIZE;

/**
 * <AUTHOR>
 */
public class FaceAngleManager {

    private static final String TAG = FaceAngleManager.class.getSimpleName();
    private CoreService mCore;
    private static final int DEFAULT_FOCUS_PERSON_ID = -1;
    private int mCurrentFocusPersonId = DEFAULT_FOCUS_PERSON_ID;
    private UserData.People mRecognisedPerson;

    public FaceAngleManager(CoreService core) {
        mCore = core;
    }

    public String getMultipleModeInfo(int index) {
        if (index != 0) {
            ArrayList<FaceAngle> faceAngles = updateFaceAnglesInfo(mCore.getPersonManager()
                    .getCompleteFacesPersonList(Double.MAX_VALUE), index);
            String result = buildFaceAnglesInfo(faceAngles, mRecognisedPerson);
    //        Log.d(TAG, "getMultipleModeInfos result = " + result);
            return result;
        }
        return null;
    }

    private String buildFaceAnglesInfo(ArrayList<FaceAngle> faceAngles, UserData.People people) {
        Gson gson = new Gson();
        HashMap<String, Object> hashMap = new HashMap<>();
        if (null != faceAngles && faceAngles.size() > 0) {
            hashMap.put("face_info", faceAngles);
            //TODO:添加时间戳间隔范围判断
            if (people != null) {
                hashMap.put("face_attr", people);
            }
        }
        return gson.toJson(hashMap);
    }

    private ArrayList<FaceAngle> updateFaceAnglesInfo(List<Person> personList, int index) {
        Log.d(TAG, "updateFaceAnglesInfo");
        ArrayList<FaceAngle> faceAngles = new ArrayList<>();
        if (personList == null || personList.size() == 0) {
            return faceAngles;
        }
        Log.d(TAG, "FaceAnglesInfo size : " + personList.size() + "," + personList.toString());
        for (Person person : personList) {
            FaceAngle faceAngle = new FaceAngle();
            faceAngle.setPerson(person);
            faceAngle.setTime(System.currentTimeMillis());
            faceAngle.setIndex(index);
            faceAngles.add(faceAngle);
        }
        return faceAngles;
    }

    public void updatePersonDetailInfo(Person focusPerson) {
        if (ProductInfo.ProductModel.CM_XIAOMI_BASE.model.equals(RobotSettings.getProductModel())
            || ProductInfo.ProductModel.CM_XIAOMI_1p1.model.equals(RobotSettings.getProductModel())) {
            getPersonDetailInfo(focusPerson);
        }
    }

    private void getPersonDetailInfo(Person focusPerson) {
        if (null == focusPerson || focusPerson.getDistance() > 1) {
            mCurrentFocusPersonId = DEFAULT_FOCUS_PERSON_ID;
            mRecognisedPerson = null;
            return;
        }
        final int id = focusPerson.getId();
        if (id >= 0 && mCurrentFocusPersonId != id) {
            mCurrentFocusPersonId = id;
            IActionListener.Stub listener = new IActionListener.Stub() {
                @Override
                public void onResult(int status, String responseString) throws RemoteException {

                }

                @Override
                public void onError(int errorCode, String errorString) throws RemoteException {

                }

                @Override
                public void onStatusUpdate(int status, String data) throws RemoteException {

                }

                @Override
                public void onResultWithExtraData(int result, String message, String extraData) {
                    Log.d(TAG, "remoteDetect result: " + result +
                            ", message: " + message + ",extraData:" + extraData);
                    if (result == PersonResult.SUCCESS
                            || result == BaseApiResult.RESULT_RECOGNIZE_ONLY_FACE) {
                        try {
                            Person.Remote currentPerson = new Gson().fromJson(message, Person.Remote.class);
                            if (null != currentPerson) {
                                long requestTime = 0;
                                try {
                                    JSONObject jsonObject = new JSONObject(extraData);
                                    requestTime = jsonObject.optLong("requestTime");
                                } catch (JSONException e) {
                                    e.printStackTrace();
                                }
                                UserData.People people = UserData.buildPeople(currentPerson);
                                people.setId(id);
                                people.setBegin_time(requestTime);
                                people.setEnd_time(System.currentTimeMillis());
                                mRecognisedPerson = people;
                                return;
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                    }
                    mRecognisedPerson = null;
                    mCurrentFocusPersonId = DEFAULT_FOCUS_PERSON_ID;
                }

                @Override
                public void onErrorWithExtraData(int errorCode, String errorString, String extraData) throws RemoteException {

                }

                @Override
                public void onStatusUpdateWithExtraData(int status, String data, String extraData) throws RemoteException {

                }
            };
            RequestBean bean = new RequestBean(id, listener);
            mCore.getPersonManager().getHandler().obtainMessage(MSG_RECOGNIZE, bean).sendToTarget();
        }
    }
}
