/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.action.advnavi.bean;

import java.util.ArrayList;

/**
 * 巡线数据 Bean
 */
public class RoadGraph {

    /**
     * 端点列表
     */
    private ArrayList<RoadGraphNode> nodes;
    /**
     * 线段列表
     */
    private ArrayList<RoadGraphEdge> edges;


    public RoadGraph() {

    }

    public RoadGraph(ArrayList nodes, ArrayList edges) {
        this.nodes = nodes;
        this.edges = edges;
    }

    public ArrayList<RoadGraphNode> getNodes() {
        return nodes;
    }

    public void setNodes(ArrayList<RoadGraphNode> nodes) {
        this.nodes = nodes;
    }

    public ArrayList<RoadGraphEdge> getEdges() {
        return edges;
    }

    public void setEdges(ArrayList<RoadGraphEdge> edges) {
        this.edges = edges;
    }

    @Override
    public String toString() {
        return "RoadGraph{" +
                "nodes=" + nodes +
                ", edges=" + edges +
                '}';
    }
}
