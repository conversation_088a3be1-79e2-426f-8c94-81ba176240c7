package com.ainirobot.coreservice.bi.report;

import com.ainirobot.coreservice.client.upload.bi.CallChainDef;
import com.ainirobot.coreservice.client.upload.bi.CallChainReport;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Created by Orion on 2020/4/29.
 */
public class MotionFollowActionReporter extends CallChainReport {

    private MotionFollowActionReporter() {
        super(CallChainDef.NAME_MOTION_BODY_FOLLOW);
    }

    public static class Builder {

        public static MotionFollowActionReporter build() {
            return new MotionFollowActionReporter();
        }

        public static MotionFollowActionReporter buildRobotOsReporter() {
            return (MotionFollowActionReporter) Builder.build()
                    .addSourceRobotOs()
                    .addPackageNameCoreService();
        }

        public static MotionFollowActionReporter buildVisionSdkReporter() {
            return (MotionFollowActionReporter) Builder.build()
                    .addSourceVisionSdk()
                    .addPackageNameCoreService();
        }

    }

    public static void startActionReport(String name, String params) {
        Builder.buildRobotOsReporter()
                .addNodeMethodTypeLifecycle()
                .addNodeMethodName(name)
                .addNodeParams(params)
                .report();
    }

    public static void stopActionReport(String name) {
        Builder.buildRobotOsReporter()
                .addNodeMethodTypeFunction()
                .addNodeMethodName(name)
                .report();
    }

    public static void onFollowStopReport(String name) {
        Builder.buildRobotOsReporter()
                .addNodeMethodTypeFunction()
                .addNodeMethodName(name)
                .report();
    }

    public static void setTrackTargetReport(String name, String personName, int personId,
                                            String trackMode) {
        JSONObject params = new JSONObject();
        try {
            params.put("personName", personName);
            params.put("personId", personId);
            params.put("trackMode", trackMode);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodTypeApiCall()
                .addNodeMethodName(name)
                .addNodeParams(params.toString())
                .report();
    }

    public static void handleTrackResultReport(String name, String params) {
        Builder.buildVisionSdkReporter()
                .addNodeMethodTypeApiCallback()
                .addNodeMethodName(name)
                .addNodeResult(params)
                .report();
    }

    public static void setObstaclesSafeDistanceReport(String name, double mDistanceR2P) {
        JSONObject params = new JSONObject();
        try {
            params.put("mDistanceR2P", mDistanceR2P);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodTypeApiCall()
                .addNodeMethodName(name)
                .addNodeParams(params.toString())
                .report();
    }

    public static void setObstaclesSafeDistanceResponseReport(String name, String result) {
        Builder.buildRobotOsReporter()
                .addNodeMethodTypeApiCallback()
                .addNodeMethodName(name)
                .addNodeResult(result)
                .report();
    }

    public static void getAllPersonInfosReport(String name) {
        Builder.buildRobotOsReporter()
                .addNodeMethodTypeApiCall()
                .addNodeMethodName(name)
                .report();
    }

    public static void handleBodyInfosReport(String name, String params) {
        Builder.buildVisionSdkReporter()
                .addNodeMethodTypeApiCallback()
                .addNodeMethodName(name)
                .addNodeResult(params)
                .report();
    }

    public static void registerObstaclesStateReport(String name) {
        Builder.buildRobotOsReporter()
                .addNodeMethodTypeApiCall()
                .addNodeMethodName(name)
                .report();
    }

    public static void handleAvoidStateReport(String name, String result) {
        Builder.buildRobotOsReporter()
                .addNodeMethodTypeApiCallback()
                .addNodeMethodName(name)
                .addNodeResult(result)
                .report();
    }

    public static void onTrackSuccessReport(String name) {
        Builder.buildRobotOsReporter()
                .addNodeMethodTypeFunction()
                .addNodeMethodName(name)
                .report();
    }

    public static void keepMotionReportEntrance(String name, String personState, boolean hasObstacle) {
        JSONObject params = new JSONObject();
        try {
            params.put("personState", personState);
            params.put("hasObstacle", hasObstacle);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodTypeFunction()
                .addNodeMethodName(name)
                .addNodeEntrance(params.toString())
                .report();
    }

    public static void keepMotionReportMiddle(String name, String person) {
        JSONObject params = new JSONObject();
        try {
            params.put("person", person);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodTypeFunction()
                .addNodeMethodName(name)
                .addNodeMiddle(params.toString())
                .report();
    }

    public static void keepMotionReportExport(String name) {
        JSONObject params = new JSONObject();
        try {
            params.put("personNull", true);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodTypeFunction()
                .addNodeMethodName(name)
                .addNodeExport(params.toString())
                .report();
    }

    public static void keepMotionReportExport(String name, int personId, int angle, double distance) {
        JSONObject params = new JSONObject();
        try {
            params.put("personNull", false);
            params.put("personId", personId);
            params.put("angle", angle);
            params.put("distance", distance);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodTypeFunction()
                .addNodeMethodName(name)
                .addNodeExport(params.toString())
                .report();
    }

    public static void mFollowTaskReport(String name, String exception) {
        JSONObject params = new JSONObject();
        try {
            params.put("exception", exception);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodTypeFunction()
                .addNodeMethodName(name)
                .addNodeExport(params.toString())
                .report();
    }

    public static void motionArcWithObstaclesReport(String name, float personDist, float angle, float headSpeed,
                                              float latency, boolean bodyFollow, double minObstacleDist,
                                              double linearSpeed, double angularSpeed) {
        JSONObject params = new JSONObject();
        try {
            params.put("personDist", personDist);
            params.put("angle", angle);
            params.put("headSpeed", headSpeed);
            params.put("latency", latency);
            params.put("bodyFollow", bodyFollow);
            params.put("minObstacleDist", minObstacleDist);
            params.put("linearSpeed", linearSpeed);
            params.put("angularSpeed", angularSpeed);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodTypeApiCall()
                .addNodeMethodName(name)
                .addNodeParams(params.toString())
                .report();
    }

    public static void statusReport(String name, int status, String data) {
        JSONObject params = new JSONObject();
        try {
            params.put("status", status);
            params.put("data", data);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodName(name)
                .addStatus(params.toString())
                .report();
    }

    public static void resultReport(String name, int code, String msg) {
        JSONObject params = new JSONObject();
        try {
            params.put("code", code);
            params.put("msg", msg);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Builder.buildRobotOsReporter()
                .addNodeMethodName(name)
                .addResult(params.toString())
                .report();
    }

}
