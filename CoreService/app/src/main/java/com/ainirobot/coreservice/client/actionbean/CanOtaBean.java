package com.ainirobot.coreservice.client.actionbean;

public class CanOtaBean extends BaseBean {

    private int board;
    private int status;
    private int failReason;
    private int progress;

    public CanOtaBean() {
    }

    public int getBoard() {
        return board;
    }

    public void setBoard(int board) {
        this.board = board;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getFailReason() {
        return failReason;
    }

    public void setFailReason(int failReason) {
        this.failReason = failReason;
    }

    public int getProgress() {
        return progress;
    }

    public void setProgress(int progress) {
        this.progress = progress;
    }

    @Override
    public String toString() {
        return "CanOtaBean{" +
                "board=" + board +
                ", status=" + status +
                ", failReason=" + failReason +
                ", progress=" + progress +
                '}';
    }
}
