
package com.ainirobot.coreservice.action.advance_charge.bean;


import com.ainirobot.coreservice.client.actionbean.Pose;

public class ChargePileInfo {

    /**
     * 充电桩
     */
    private Pose chargePilePose;
    /**
     * 回充点
     */
    private Pose chargePointPose;
    /**
     * 该充电桩在回充等待区充电桩列表中的index
     */
    private int index;

    public ChargePileInfo(Pose chargePilePose, Pose chargePointPose, int index) {
        this.chargePilePose = chargePilePose;
        this.chargePointPose = chargePointPose;
        this.index = index;
    }

    public Pose getChargePilePose() {
        return chargePilePose;
    }

    public Pose getChargePointPose() {
        return chargePointPose;
    }

    public int getIndex() {
        return index;
    }

    public String toString() {
        return "ChargePileInfo{" +
                ", chargePilePose=" + chargePilePose.getName() + '\'' +
                ", chargePointPose=" + chargePointPose.getName() + '\'' +
                ", index=" + index + '\'' +
                '}';
    }
}
