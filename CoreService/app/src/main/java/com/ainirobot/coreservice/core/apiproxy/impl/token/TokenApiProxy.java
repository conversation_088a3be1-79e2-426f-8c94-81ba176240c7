package com.ainirobot.coreservice.core.apiproxy.impl.token;

import android.database.ContentObserver;
import android.database.Cursor;
import android.net.Uri;
import android.os.Handler;
import android.os.Message;
import android.util.Log;

import com.ainirobot.coreservice.core.apiproxy.BaseApiProxy;
import com.ainirobot.coreservice.core.apiproxy.result.BaseApiResult;
import com.ainirobot.coreservice.service.CoreService;

public class TokenApiProxy extends BaseApiProxy {

    private static final String TAG = TokenApiProxy.class.getSimpleName();

    private static final String LOGIN_URI = "content://com.ainirobot.account_provider/token";

    public TokenApiProxy(CoreService core, Handler handler) {
        super(core, handler);
    }

    /**
     * 获取Token
     */
    public String getSystemToken() {
        String result = "";
        Cursor cursor = mContext.getContentResolver()
                .query(Uri.parse(LOGIN_URI),
                        null, null, null, null);
        if (null != cursor) {
            cursor.moveToFirst();
            result = cursor.getString(0);
            cursor.close();
        }
        return result;
    }

    public void registerSystemTokenChangeListener() {
        mContext.getContentResolver().registerContentObserver(Uri.parse(LOGIN_URI),
                true, new ContentObserver(new Handler()) {
                    @Override
                    public void onChange(boolean selfChange) {
                        Log.d(TAG,"onChange() selfChange: " + selfChange);
                        if (mHandler != null) {
                            Message msg = new Message();
                            msg.what = MSG_API_RESULT;
                            msg.obj = new BaseApiResult(BaseApiResult.Type.SYSTEM_TOKEN_CHANGED,
                                    BaseApiResult.RESULT_SUCCESS, null);
                            mHandler.sendMessage(msg);
                        }
                    }
                });
    }
}
