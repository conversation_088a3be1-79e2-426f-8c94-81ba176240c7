/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.inspection;

public class SensorStatus {

    boolean isFishEyeReady;//reposition camera  isMonoImageReady
    boolean isIrReady;//infrared device 已经移除
    boolean isLaserReady;//laser radar ready
    boolean isOdomReady;//odomiter ready
    boolean isRgbdReady;//Rgbd  isDepthImageReady
    boolean isLaserAvailable;//rename to laserAvailable
    boolean iscalibrationready;//vision calibration file exists
    boolean isharddiskspaceok;//disk space enough
    boolean isCanControlReady;//Can control ready
    String monoImage;
    String depthImage;
    String irImage;
    String topIrImage;
    boolean isGyroReady;
    boolean isIrImageReady;
    boolean isTopIrImageReady;


    public String getMonoImage() {
        return monoImage;
    }

    public void setMonoImage(String monoImage) {
        this.monoImage = monoImage;
    }

    public String getDepthImage() {
        return depthImage;
    }

    public void setDepthImage(String depthImage) {
        this.depthImage = depthImage;
    }

    public String getIrImage() {
        return irImage;
    }

    public void setIrImage(String irImage) {
        this.irImage = irImage;
    }

    public boolean isIscalibrationready() {
        return iscalibrationready;
    }

    public void setIscalibrationready(boolean iscalibrationready) {
        this.iscalibrationready = iscalibrationready;
    }

    public boolean isIsharddiskspaceok() {
        return isharddiskspaceok;
    }

    public void setIsharddiskspaceok(boolean isharddiskspaceok) {
        this.isharddiskspaceok = isharddiskspaceok;
    }

    public boolean isFishEyeReady() {
        return isFishEyeReady;
    }

    public void setFishEyeReady(boolean fishEyeReady) {
        isFishEyeReady = fishEyeReady;
    }

    public boolean isIrReady() {
        return isIrReady;
    }

    public void setIrReady(boolean irReady) {
        isIrReady = irReady;
    }

    public boolean isLaserReady() {
        return isLaserReady;
    }

    public void setLaserReady(boolean laserReady) {
        isLaserReady = laserReady;
    }

    public boolean isOdomReady() {
        return isOdomReady;
    }

    public void setOdomReady(boolean odomReady) {
        isOdomReady = odomReady;
    }

    public boolean isRgbdReady() {
        return isRgbdReady;
    }

    public void setRgbdReady(boolean rgbdReady) {
        isRgbdReady = rgbdReady;
    }

    public boolean isLaserAvailable() {
        return isLaserAvailable;
    }

    public void setLaserAvailable(boolean laserAvailable) {
        isLaserAvailable = laserAvailable;
    }

    public boolean isCanControlReady() {
        return isCanControlReady;
    }

    public void setCanControlReady(boolean canControlReady) {
        isCanControlReady = canControlReady;
    }

    public String getTopIrImage() {
        return topIrImage;
    }

    public void setTopIrImage(String topIrImage) {
        this.topIrImage = topIrImage;
    }

    public boolean isGyroReady() {
        return isGyroReady;
    }

    public void setGyroReady(boolean gyroReady) {
        isGyroReady = gyroReady;
    }

    public boolean isIrImageReady() {
        return isIrImageReady;
    }

    public void setIrImageReady(boolean irImageReady) {
        isIrImageReady = irImageReady;
    }

    public boolean isTopIrImageReady() {
        return isTopIrImageReady;
    }

    public void setTopIrImageReady(boolean topIrImageReady) {
        isTopIrImageReady = topIrImageReady;
    }
}
