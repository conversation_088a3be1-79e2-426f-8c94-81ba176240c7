package com.ainirobot.coreservice.data.provider;


import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import android.provider.BaseColumns;

import java.util.ArrayList;
import java.util.List;

public class SQLiteTable {

    private ArrayList<Column> mColumnsDefinitions = new ArrayList<Column>();
    private String mTableName;
    private String mAuthority;

    public SQLiteTable(String tableName, String authority, boolean needIdColumn) {
        this.mTableName = tableName;
        this.mAuthority = authority;
        if (needIdColumn) {
            mColumnsDefinitions.add(new Column(BaseColumns._ID, Column.Constraint.AUTO_INCREMENT,
                    Column.DataType.INTEGER));
        }
    }

    public SQLiteTable(String tableName, String authority) {
        this(tableName, authority, true);
    }

    public void importData(Context context, SQLiteDatabase db) {

    }

    public void update(Context context, SQLiteDatabase db, int oldVersion, int newVersion) {

    }

    public String getAuthority() {
        return mAuthority;
    }

    public Uri getContentUri() {
        return Uri.parse("content://" + this.mAuthority + "/" + this.mTableName);
    }

    public String getContentType() {
        return "vnd.android.cursor.dir/" + this.mTableName;
    }

    public String getTableName() {
        return this.mTableName;
    }

    public void create(SQLiteDatabase db) {
        db.execSQL(this.getCreateString());
    }

    public void delete(final SQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS " + this.mTableName);
    }

    private List<Column> getColumns() {
        return mColumnsDefinitions;
    }

    public SQLiteTable addColumn(String columnName, Column.DataType dataType) {
        mColumnsDefinitions.add(new Column(columnName, null, dataType));
        return this;
    }

    public SQLiteTable addColumn(String columnName, Column.Constraint constraint,
                                 Column.DataType dataType) {
        mColumnsDefinitions.add(new Column(columnName, constraint, dataType));
        return this;
    }

    public String getCreateString() {
        String formatter = " %s";
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("CREATE TABLE IF NOT EXISTS ");
        stringBuilder.append(mTableName);
        stringBuilder.append("(");
        int columnCount = getColumns().size();
        int index = 0;
        for (Column columnsDefinition : getColumns()) {
            stringBuilder.append(columnsDefinition.getColumnName()).append(
                    String.format(formatter, columnsDefinition.getDataType().name()));
            Column.Constraint constraint = columnsDefinition.getConstraint();

            if (constraint != null) {
                stringBuilder.append(String.format(formatter, constraint.toString()));
            }
            if (index < columnCount - 1) {
                stringBuilder.append(",");
            }
            index++;
        }
        stringBuilder.append(");");
        return stringBuilder.toString();
    }
}
