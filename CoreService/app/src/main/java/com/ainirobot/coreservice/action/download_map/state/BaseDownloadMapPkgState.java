package com.ainirobot.coreservice.action.download_map.state;

import com.ainirobot.coreservice.action.download_map.DownloadMapStateEnum;
import com.ainirobot.coreservice.action.download_map.StateListener;
import com.ainirobot.coreservice.client.actionbean.DownloadMapBean;

public abstract class BaseDownloadMapPkgState {

    protected String TAG;

    private boolean mIsStop;
    private StateListener stateListener;

    public BaseDownloadMapPkgState() {
        TAG = "DownloadMapAction_" + this.getClass().getSimpleName();
    }

    public void start(String paramsStr, DownloadMapBean downloadMapBean, StateListener stateListener) {
        mIsStop = false;
        this.stateListener = stateListener;
        onStart(paramsStr, downloadMapBean);
    }

    protected void sendCommand(int reqId, String command, String params) {
        stateListener.onSendCommand(reqId, command, params);
    }

    public final boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        if (mIsStop) {
            return false;
        }
        return onCmdResponse(cmdId, command, result, extraData);
    }

    protected void onStateUpdate(int state, String msg) {
        if (mIsStop) {
            return;
        }
        stateListener.onStateUpdate(state, msg);
    }

    protected void onStateRunSuccess() {
        if (mIsStop) {
            return;
        }
        stateListener.onStateRunSuccess(getNextState(), getData());
    }

    protected void onStateRunFailed(int errorCode, String errorMessage) {
        if (mIsStop) {
            return;
        }
        stateListener.onStateRunFailed(errorCode, errorMessage);
    }

    protected Object getData() {
        return null;
    }

    public void setData(Object data) {

    }

    public void stop() {
        mIsStop = true;
    }

    protected abstract void onStart(String paramsStr, DownloadMapBean downloadMapBean);

    protected abstract boolean onCmdResponse(int cmdId, String command, String result, String extraData);

    protected abstract DownloadMapStateEnum getNextState();
}
