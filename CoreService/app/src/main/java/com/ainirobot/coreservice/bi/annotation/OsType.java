package com.ainirobot.coreservice.bi.annotation;

import android.support.annotation.IntDef;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * indicate robot os type
 *
 * @version V1.0.0
 * @date 2019/2/15 17:48
 */
@Retention(RetentionPolicy.CLASS)
@Target({ElementType.FIELD
        , ElementType.PARAMETER})
@IntDef({OsType.OS_TYPE_DEV
        , OsType.OS_TYPE_CUSTOM
        , OsType.OS_TYPE_OTHER
        , OsType.OS_TYPE_DEVELOPER})
public @interface OsType {

    int OS_TYPE_OTHER = 0;

    int OS_TYPE_DEV = 1;

    int OS_TYPE_CUSTOM = 2;

    int OS_TYPE_DEVELOPER = 3;
}
