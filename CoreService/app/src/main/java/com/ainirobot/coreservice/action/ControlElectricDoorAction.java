package com.ainirobot.coreservice.action;

import android.text.TextUtils;
import android.util.Log;


import com.ainirobot.coreservice.bean.CanElectricDoorBean;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.ControlElectricDoorBean;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.utils.DelayTimer;

import java.util.ArrayList;

public class ControlElectricDoorAction extends AbstractAction<ControlElectricDoorBean> {
    private static final String TAG = "ElectricDoorAction";
    public static final int ERROR_ELECTRIC_DOOR_BLOCK = 1 << 0; // 1
    public static final int ERROR_ELECTRIC_DOOR_UPPER_BLOCK = 1 << 1; // 2
    public static final int ERROR_ELECTRIC_DOOR_LOWER_BLOCK = 1 << 2; // 4
    private static final Object mLock = new Object();
    private static final int MAX_STATE_COUNT = 3;
    private DelayTimer mTimeoutTimer;
    private static final long TIMEOUT = 5000;
    private static final long DELAY_RESPONSE_TIME = 200;
    private DelayTimer mDelayResponseTimer;
    private boolean isGetDoorState = false;
    private int mCurrentErrorState;
    private int mCurrentStateCount = 0;
    private volatile int mCommandType = -1;

    protected ControlElectricDoorAction(ActionManager manager, int id, String[] resList) {
        super(manager, id, resList);
    }

    @Override
    protected boolean startAction(ControlElectricDoorBean parameter, LocalApi api) {
        mCommandType = parameter.getDoorCmd();
        if (mCommandType == Definition.CAN_DOOR_ALL_OPEN) {
            openAllDoors(api);
        } else if (mCommandType == Definition.CAN_DOOR_ALL_CLOSE) {
            closeAllDoors(api);
        } else {
            api.controlElectricDoor(mReqId, mCommandType);
        }
        registerStatusListener(Definition.STATUS_CAN_ELECTRIC_DOOR_CTRL, this::handleStatusUpdate);
        startTimeoutTimer();
        return true;
    }

    private void openAllDoors(LocalApi api) {
        api.controlElectricDoor(mReqId, Definition.CAN_DOOR_DOOR1_DOOR2_OPEN);
        api.controlElectricDoor(mReqId, Definition.CAN_DOOR_DOOR3_DOOR4_OPEN);
    }

    private void closeAllDoors(LocalApi api) {
        api.controlElectricDoor(mReqId, Definition.CAN_DOOR_DOOR1_DOOR2_CLOSE);
        api.controlElectricDoor(mReqId, Definition.CAN_DOOR_DOOR3_DOOR4_CLOSE);
    }

    private void handleStatusUpdate(String data) {
        Log.d(TAG, "onStatusUpdate: " + data);
        if (TextUtils.isEmpty(data)) {
            Log.d(TAG, "onStatusUpdate: data is empty");
            return;
        }
        handleElectricResult(data);
    }

    private void startTimeoutTimer() {
        Log.d(TAG, "startTimeoutTimer");
        mTimeoutTimer = new DelayTimer(TIMEOUT, () -> onError(Definition.ERROR_ELECTRIC_DOOR_TIMEOUT, "electric door control timeout"));
        mTimeoutTimer.start();
    }

    private void handleElectricResult(String message) {
        CanElectricDoorBean doorBean;
        try {
            doorBean = mGson.fromJson(message, CanElectricDoorBean.class);
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "handleElectricResult: " + e.getMessage());
            return;
        }
        if (doorBean == null) {
            Log.e(TAG, "handleElectricResult: doorBean is null");
            return;
        }
        handleDoorCommand(doorBean);
    }

    private void handleDoorCommand(CanElectricDoorBean doorBean) {
        if (mCommandType == -1) {
            Log.e(TAG, "handleDoorCommand: mCommandType is default");
            return;
        }
        if (mCommandType == Definition.CAN_DOOR_DOOR1_DOOR2_OPEN) {
            onDoorStateUpdate(doorBean, Definition.CAN_DOOR_STATUS_OPEN, doorBean.getDoor1(), doorBean.getDoor2());
        } else if (mCommandType == Definition.CAN_DOOR_DOOR3_DOOR4_OPEN) {
            onDoorStateUpdate(doorBean, Definition.CAN_DOOR_STATUS_OPEN, doorBean.getDoor3(), doorBean.getDoor4());
        } else if (mCommandType == Definition.CAN_DOOR_ALL_OPEN) {
            onDoorStateUpdate(doorBean, Definition.CAN_DOOR_STATUS_OPEN, doorBean.getDoor1(), doorBean.getDoor2(), doorBean.getDoor3(), doorBean.getDoor4());
        } else if (mCommandType == Definition.CAN_DOOR_DOOR1_OPEN) {
            onDoorStateUpdate(doorBean, Definition.CAN_DOOR_STATUS_OPEN, doorBean.getDoor1());
        } else if (mCommandType == Definition.CAN_DOOR_DOOR2_OPEN) {
            onDoorStateUpdate(doorBean, Definition.CAN_DOOR_STATUS_OPEN, doorBean.getDoor2());
        } else if (mCommandType == Definition.CAN_DOOR_DOOR3_OPEN) {
            onDoorStateUpdate(doorBean, Definition.CAN_DOOR_STATUS_OPEN, doorBean.getDoor3());
        } else if (mCommandType == Definition.CAN_DOOR_DOOR4_OPEN) {
            onDoorStateUpdate(doorBean, Definition.CAN_DOOR_STATUS_OPEN, doorBean.getDoor4());
        } else if (mCommandType == Definition.CAN_DOOR_DOOR1_DOOR2_CLOSE) {
            onDoorStateUpdate(doorBean, Definition.CAN_DOOR_STATUS_CLOSE, doorBean.getDoor1(), doorBean.getDoor2());
        } else if (mCommandType == Definition.CAN_DOOR_DOOR3_DOOR4_CLOSE) {
            onDoorStateUpdate(doorBean, Definition.CAN_DOOR_STATUS_CLOSE, doorBean.getDoor3(), doorBean.getDoor4());
        } else if (mCommandType == Definition.CAN_DOOR_ALL_CLOSE) {
            onDoorStateUpdate(doorBean, Definition.CAN_DOOR_STATUS_CLOSE, doorBean.getDoor1(), doorBean.getDoor2(), doorBean.getDoor3(), doorBean.getDoor4());
        } else if (mCommandType == Definition.CAN_DOOR_DOOR1_CLOSE) {
            onDoorStateUpdate(doorBean, Definition.CAN_DOOR_STATUS_CLOSE, doorBean.getDoor1());
        } else if (mCommandType == Definition.CAN_DOOR_DOOR2_CLOSE) {
            onDoorStateUpdate(doorBean, Definition.CAN_DOOR_STATUS_CLOSE, doorBean.getDoor2());
        } else if (mCommandType == Definition.CAN_DOOR_DOOR3_CLOSE) {
            onDoorStateUpdate(doorBean, Definition.CAN_DOOR_STATUS_CLOSE, doorBean.getDoor3());
        } else if (mCommandType == Definition.CAN_DOOR_DOOR4_CLOSE) {
            onDoorStateUpdate(doorBean, Definition.CAN_DOOR_STATUS_CLOSE, doorBean.getDoor4());
        }
    }

    private void onDoorStateUpdate(CanElectricDoorBean doorBean, int flag, int... doorStates) {
        if (!isRunning()) {
            return;
        }
        if (checkAndHandleBlockState(doorBean)) {
            Log.d(TAG, "onDoorStateUpdate block state:" + mCurrentErrorState);
            return;
        }

        if (isGetDoorState) {
            Log.d(TAG, "onDoorStateUpdate isGetDoorState");
            return;
        }

        if (allDoorsInState(flag, doorStates)) {
            mCurrentStateCount++;
            Log.d(TAG, "onDoorStateUpdate: mCurrentStateCount=" + mCurrentStateCount);
            if (mCurrentStateCount >= MAX_STATE_COUNT) {
                mCurrentStateCount = 0;
                isGetDoorState = true;
                delaySuccessResponse(flag == Definition.CAN_DOOR_STATUS_OPEN ? "door is open" : "door is close");
            }
        } else {
            mCurrentStateCount = 0;
        }
    }

    private boolean checkAndHandleBlockState(CanElectricDoorBean doorBean) {
        if (doorBean.getUpStatus() == Definition.CAN_DOOR_STATUS_BLOCK_AND_BOUNCE
                && doorBean.getDownStatus() == Definition.CAN_DOOR_STATUS_BLOCK_AND_BOUNCE) {
            return handleBlockState(ERROR_ELECTRIC_DOOR_BLOCK, Definition.STATUS_ELECTRIC_DOOR_BLOCK, "electric door block");
        } else if (doorBean.getUpStatus() == Definition.CAN_DOOR_STATUS_BLOCK_AND_BOUNCE) {
            return handleBlockState(ERROR_ELECTRIC_DOOR_UPPER_BLOCK, Definition.STATUS_ELECTRIC_DOOR_UPPER_BLOCK, "electric door upper block");
        } else if (doorBean.getDownStatus() == Definition.CAN_DOOR_STATUS_BLOCK_AND_BOUNCE) {
            return handleBlockState(ERROR_ELECTRIC_DOOR_LOWER_BLOCK, Definition.STATUS_ELECTRIC_DOOR_LOWER_BLOCK, "electric door lower block");
        }
        return false;
    }

    private boolean handleBlockState(int errorBlockFlag, int state, String msg) {
        if ((mCurrentErrorState & errorBlockFlag) != 0) {
            return true;
        }
        updateBlockState(errorBlockFlag, state, msg);
        return true;
    }

    private boolean allDoorsInState(int flag, int... doorStates) {
        for (int doorState : doorStates) {
            if (doorState != flag) {
                return false;
            }
        }
        return true;
    }

    private void updateBlockState(int errorBlockFlag, int state, String msg) {
        destroyTimeout("updateBlockState:" + msg);
        mCurrentErrorState |= errorBlockFlag;
        onStatusUpdate(state, msg);
        delayErrorResponse();
    }

    private void delayErrorResponse() {
        mDelayResponseTimer = new DelayTimer(TIMEOUT, () -> {
            synchronized (mLock) {
                if ((mCurrentErrorState & ERROR_ELECTRIC_DOOR_BLOCK) != 0) {
                    onError(Definition.ERROR_ELECTRIC_DOOR_BLOCK, "electric door block");
                } else if ((mCurrentErrorState & ERROR_ELECTRIC_DOOR_UPPER_BLOCK) != 0) {
                    onError(Definition.ERROR_ELECTRIC_DOOR_UPPER_BLOCK, "electric door upper block");
                } else if ((mCurrentErrorState & ERROR_ELECTRIC_DOOR_LOWER_BLOCK) != 0) {
                    onError(Definition.ERROR_ELECTRIC_DOOR_LOWER_BLOCK, "electric door lower block");
                }
            }
        });
        mDelayResponseTimer.start();
        Log.d(TAG, "delayErrorResponse:" + mCurrentErrorState);
    }

    private void delaySuccessResponse(String msg) {
        destroyTimeout("delaySuccessResponse");
        mDelayResponseTimer = new DelayTimer(DELAY_RESPONSE_TIME, () -> {
            synchronized (mLock) {
                onResult(Definition.RESULT_OK, msg);
            }
        });
        mDelayResponseTimer.start();
        Log.d(TAG, "delaySuccessResponse: " + msg);
    }


    @Override
    protected boolean stopAction(boolean isResetHW) {
        destroyTimeout("stopAction");
        unregisterStatusListener(Definition.STATUS_CAN_ELECTRIC_DOOR_CTRL);
        mCommandType = -1;
        isGetDoorState = false;
        mCurrentErrorState = 0;
        mCurrentStateCount = 0;
        return true;
    }

    private void destroyTimeout(String reason) {
        Log.d(TAG, "destroyTimeout: " + reason);
        if (mTimeoutTimer != null) {
            mTimeoutTimer.destroy();
            mTimeoutTimer = null;
        }
        if (mDelayResponseTimer != null) {
            mDelayResponseTimer.destroy();
            mDelayResponseTimer = null;
        }
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return null;
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return null;
    }

    @Override
    protected boolean verifyParam(ControlElectricDoorBean param) {
        return param.getDoorCmd() == Definition.CAN_DOOR_DOOR1_DOOR2_OPEN
                || param.getDoorCmd() == Definition.CAN_DOOR_DOOR1_DOOR2_CLOSE
                || param.getDoorCmd() == Definition.CAN_DOOR_DOOR3_DOOR4_OPEN
                || param.getDoorCmd() == Definition.CAN_DOOR_DOOR3_DOOR4_CLOSE
                || param.getDoorCmd() == Definition.CAN_DOOR_ALL_OPEN
                || param.getDoorCmd() == Definition.CAN_DOOR_ALL_CLOSE;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        Log.d(TAG, "cmdResponse: ");
        return TextUtils.equals(command, Definition.CMD_CAN_ELECTRIC_DOOR_CTRL);
    }
}
