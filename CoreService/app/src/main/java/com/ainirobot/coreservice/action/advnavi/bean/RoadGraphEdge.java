/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.action.advnavi.bean;

import com.ainirobot.coreservice.client.Definition;

/**
 * 巡线线段
 */
public class RoadGraphEdge {

    /**
     * 线段 和 端点 id 互相不能重复，从 0 递增，唯一且不可重复
     */
    private int id;

    /**
     * 起始端点 id
     */
    private int node_start_id;

    /**
     * 结束端点 id
     */
    private int node_end_id;

    /**
     * 最高限速，范围（0.1 到 1.2），未设置默认 0 由导航决定，单位（m/s）
     */
    private double linear_speed_limit = 0;

    /**
     * 路径宽度，范围（0.65 到 5），未设置默认 1.4，单位（m）
     */
    private double line_width = 1.4;

    /**
     * 路径方向，未设置默认 0 由导航决定，1 为前行，2 为后退，3 为多通(即双向)
     */
    private int rule;

    /**
     * 只有 (rule == 1 || rule == 2) 时才可设置“绝对单向”
     * 如果勾选【绝对单向行驶】，则此字段保存为：-1
     * 如果未勾选【绝对单向行驶】，或者选择【双向】，则此字段保存为：5
     */
    private double retrograde_cost = 5;

    /**
     * 巡线调度设置，未设置默认 0 由导航决定，勾选后值为 -1 即禁止调度停靠
     */
    private double parking_cost = 0;

    private int scene_type = Definition.SCENE_NORMAL;

    public RoadGraphEdge() {

    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getNode_start_id() {
        return node_start_id;
    }

    public void setNode_start_id(int node_start_id) {
        this.node_start_id = node_start_id;
    }

    public int getNode_end_id() {
        return node_end_id;
    }

    public void setNode_end_id(int node_end_id) {
        this.node_end_id = node_end_id;
    }

    public double getLinear_speed_limit() {
        return linear_speed_limit;
    }

    public void setLinear_speed_limit(double linear_speed_limit) {
        this.linear_speed_limit = linear_speed_limit;
    }

    public double getLine_width() {
        return line_width;
    }

    public void setLine_width(double line_width) {
        this.line_width = line_width;
    }

    public int getRule() {
        return rule;
    }

    public void setRule(int rule) {
        this.rule = rule;
    }

    public double getRetrograde_cost() {
        return retrograde_cost;
    }

    public void setRetrograde_cost(double retrograde_cost) {
        this.retrograde_cost = retrograde_cost;
    }

    public double getParking_cost() {
        return parking_cost;
    }

    public void setParking_cost(double parking_cost) {
        this.parking_cost = parking_cost;
    }

    public int getScene_type() {
        return scene_type;
    }

    public void setScene_type(int scene_type) {
        this.scene_type = scene_type;
    }

    @Override
    public String toString() {
        return "{" +
                "id=" + id +
                ", rule=" + rule +
                ", linear_speed_limit=" + linear_speed_limit +
                ", line_width=" + line_width +
                ", retrograde_cost=" + retrograde_cost +
                ", parking_cost=" + parking_cost +
                ", scene_type=" + scene_type +
                '}';
    }
}
