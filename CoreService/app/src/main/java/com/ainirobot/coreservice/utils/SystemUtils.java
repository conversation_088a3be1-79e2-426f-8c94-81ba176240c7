package com.ainirobot.coreservice.utils;

import android.annotation.SuppressLint;
import android.app.ActivityManager;
import android.app.AlarmManager;
import android.app.backup.BackupManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.content.res.Configuration;
import android.os.Build;
import android.os.Build.VERSION;
import android.os.Build.VERSION_CODES;
import android.os.Handler;
import android.os.LocaleList;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.core.permission.ActivityController;
import com.ainirobot.coreservice.core.permission.ProcessObserver;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Locale;

public class SystemUtils {
    private static final String TAG = SystemUtils.class.getSimpleName();

    public static void setLanguage(String language) {
        if (TextUtils.isEmpty(language)) {
            return;
        }

        String[] languageInfo = language.split(Definition.UNDERLINE);
        Log.d(TAG, "Set language : " + language);
        Locale locale = new Locale(languageInfo[0], languageInfo[1]);
        setLanguage(locale);
    }

    /**
     * 切换系统语言
     *
     * @param locale
     */
    @SuppressLint("PrivateApi")
    public static void setLanguage(Locale locale) {
        if (locale == null) {
            return;
        }

        try {
            Object ams;
            if (Build.VERSION.SDK_INT < 26) {
                Class<?> activityManagerNative = Class.forName("android.app.ActivityManagerNative");
                Method mGetDefault = activityManagerNative.getMethod("getDefault");
                ams = mGetDefault.invoke(null);
            } else {
                Method mGetService = ActivityManager.class.getMethod("getService");
                ams = mGetService.invoke(null);
            }

            Class<?> amClazz = Class.forName("android.app.IActivityManager");
            Method getConfiguration = amClazz.getDeclaredMethod("getConfiguration");

            Configuration config = (Configuration) getConfiguration.invoke(ams);
            Class<Configuration> configClazz = Configuration.class;
            Field userSetLocale = configClazz.getField("userSetLocale");
            userSetLocale.set(config, true);

            String methodName;
            if (VERSION.SDK_INT >= VERSION_CODES.N) {
                config.setLocales(new LocaleList(locale));
                methodName = "updatePersistentConfiguration";
            } else {
                config.setLocale(locale);
                methodName = "updateConfiguration";
            }
            Class<?>[] clzParams = {configClazz};
            Method updateConfiguration = amClazz.getDeclaredMethod(methodName, clzParams);
            updateConfiguration.invoke(ams, config);
            BackupManager.dataChanged("com.android.providers.settings");
            Log.d(TAG, "Set system language : " + locale.getLanguage() + "  " + locale.getCountry());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static boolean isLaunchRecent(Context context, String pkg) {
        ComponentName recent = getActivityRecent(context, pkg);
        if (recent == null) {
            return false;
        }
        String className = recent.getClassName();
        final Intent intent = new Intent(Intent.ACTION_MAIN);
        intent.addCategory(Intent.CATEGORY_HOME);
        final ResolveInfo res = context.getPackageManager().resolveActivity(intent, 0);
        Log.d(TAG, "isLauncherRecent pkg: " + pkg + ", res: " + res);
        if (res == null || res.activityInfo == null) {
            return false;
        }
        boolean isLaunchTop = className.equals(res.activityInfo.packageName);
        Log.d(TAG, "LaunchActivity is recent : " + isLaunchTop);
        return isLaunchTop;
    }

    /**
     * 获取进程名称
     *
     * @param context
     * @return
     */
    public static String getProcessName(Context context) {
        ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningAppProcessInfo> runningApps = am.getRunningAppProcesses();
        if (runningApps == null) {
            return null;
        }
        for (ActivityManager.RunningAppProcessInfo proInfo : runningApps) {
            if (proInfo.pid == android.os.Process.myPid()) {
                if (proInfo.processName != null) {
                    return proInfo.processName;
                }
            }
        }
        return null;
    }

    public static boolean isForeground(Context context) {
        ComponentName componentName = getActivityTop(context);
        return componentName != null
                && context.getPackageName().equals(componentName.getPackageName());
    }

    public static boolean isDefaultLauncher(Context context) {
        PackageManager localPackageManager = context.getPackageManager();
        Intent intent = new Intent(Intent.ACTION_MAIN);
        intent.addCategory(Intent.CATEGORY_HOME);
        ResolveInfo info = localPackageManager.resolveActivity(intent,
                PackageManager.MATCH_DEFAULT_ONLY);
        if (info == null) {
            return false;
        }

        String launcherPkg = info.activityInfo.packageName;
        return context.getPackageName().equals(launcherPkg);
    }

    public static boolean isServiceRunning(Class cls, Context context) {
        if (cls == null || TextUtils.isEmpty(cls.getName())) {
            return false;
        }
        ActivityManager manager = (ActivityManager) context
                .getSystemService(Context.ACTIVITY_SERVICE);
        try {
            List<ActivityManager.RunningServiceInfo> list = manager
                    .getRunningServices(1000);
            if (list != null && list.size() > 0) {
                for (ActivityManager.RunningServiceInfo info : list) {
                    if (cls.getName().equals(info.service.getClassName())) {
                        return true;
                    }
                }
            }
        } catch (SecurityException e) {
            e.printStackTrace();
        }
        return false;
    }

    public static boolean isActivityRunning(Class cls, Context context) {
        if (cls == null || TextUtils.isEmpty(cls.getName())) {
            return false;
        }
        ActivityManager manager = (ActivityManager) context
                .getSystemService(Context.ACTIVITY_SERVICE);
        try {
            List<ActivityManager.RunningTaskInfo> list = manager
                    .getRunningTasks(100);

            for (ActivityManager.RunningTaskInfo info : list) {
                if (cls.getName().equals(info.baseActivity.getClassName())) {
                    return true;
                }
            }
        } catch (SecurityException e) {
            e.printStackTrace();
        }
        return false;
    }

    public static ComponentName getActivityTop(Context context) {
        ActivityManager manager = (ActivityManager) context
                .getSystemService(Context.ACTIVITY_SERVICE);
        try {
            List<ActivityManager.RunningTaskInfo> list = manager
                    .getRunningTasks(1);
            if (list != null && list.size() > 0) {
                return list.get(0).topActivity;
            }
        } catch (SecurityException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static ComponentName getActivityRecent(Context context, String pkg) {
        ActivityManager manager = (ActivityManager) context
                .getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningTaskInfo> recents = manager.getRunningTasks(5);
        if (recents == null || recents.isEmpty()) {
            return null;
        }

        ComponentName top = recents.get(0).topActivity;
        Log.d(TAG, "Get activity recent top : " + top + "   " + pkg);
        if (top == null) {
            return null;
        }

        if (recents.get(0).numActivities > 1) {
            return top;
        }

        Log.d(TAG, "Get activity recent top not null : " + recents.size());
        if (pkg.equals(top.getPackageName())) {
            return top;
        }

        if (recents.size() > 1) {
            return recents.get(1).topActivity;
        }

        return top;
    }

    public static boolean isInRecent(Context context, String pkg) {
        ActivityManager manager = (ActivityManager) context
                .getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningTaskInfo> taskInfos = manager.getRunningTasks(10);

        if (taskInfos == null || taskInfos.isEmpty()) {
            return false;
        }

        for (ActivityManager.RunningTaskInfo taskInfo : taskInfos) {
            ComponentName base = taskInfo.topActivity;
            Log.d(TAG, "Running task : " + (base != null ? base.getPackageName()
                    : "base is null"));
            if (base != null && base.getPackageName().equals(pkg)) {
                return true;
            }
        }
        return false;
    }


    @SuppressLint("PrivateApi")
    public static boolean setOrionActivityController(Handler handler) {
        try {
            Class<?> mClass;
            Object mObject;
            if (Build.VERSION.SDK_INT < 26) {
                return false;
            } else {
                Method mGetService = ActivityManager.class.getMethod("getService");
                mClass = Class.forName("android.app.IActivityManager");
                mObject = mGetService.invoke(null);
            }

            Method mSetActivityController = mClass.getMethod(
                    "setOrionActivityController",
                    Class.forName("android.app.IActivityController"));
            mSetActivityController.invoke(mObject, new ActivityController(handler));
            return true;
        } catch (ClassNotFoundException | NoSuchMethodException
                | IllegalAccessException | InvocationTargetException | IllegalArgumentException e) {
            e.printStackTrace();
            return false;
        }
    }

    @SuppressLint("PrivateApi")
    public static void setActivityController(Handler handler) {
        try {
            Class<?> mClass;
            Object mObject;
            if (Build.VERSION.SDK_INT < 26) {
                mClass = Class.forName("android.app.ActivityManagerNative");
                Method mGetDefault = mClass.getMethod("getDefault");
                mObject = mGetDefault.invoke(null);
            } else {
                Method mGetService = ActivityManager.class.getMethod("getService");
                mClass = Class.forName("android.app.IActivityManager");
                mObject = mGetService.invoke(null);
            }

            Method mSetActivityController = mClass.getMethod(
                    "setActivityController",
                    Class.forName("android.app.IActivityController"), boolean.class);
            mSetActivityController.invoke(mObject, new ActivityController(handler), false);
        } catch (ClassNotFoundException | NoSuchMethodException
                | IllegalAccessException | InvocationTargetException | IllegalArgumentException e) {
            e.printStackTrace();
        }
    }

    public static void setTimeZone(Context context, String timezone) {
        Log.d(TAG, "Set timezone : " + timezone);
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if (alarmManager != null) {
            alarmManager.setTimeZone(timezone);
        }
    }

    @SuppressLint("PrivateApi")
    public static boolean monitorAppProcess(Handler handler) {
        try {
            Class<?> mClass;
            Object mObject;
            if (Build.VERSION.SDK_INT < 28) {
                return false;
            } else {
                Method mGetService = ActivityManager.class.getMethod("getService");
                mClass = Class.forName("android.app.IActivityManager");
                mObject = mGetService.invoke(null);
            }
            Method registerMethod = mClass.getMethod("registerProcessObserver",
                    Class.forName("android.app.IProcessObserver"));
            registerMethod.invoke(mObject, new ProcessObserver(handler));
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public static String getPackageName(Context context, int pid) {
        ActivityManager am = (ActivityManager) context
                .getSystemService(Context.ACTIVITY_SERVICE);
        if (am == null) {
            Log.e(TAG, "getPackageName am null");
            return "";
        }
        List<ActivityManager.RunningAppProcessInfo> processList = am.getRunningAppProcesses();
        for (ActivityManager.RunningAppProcessInfo processInfo : processList) {
            int processPid = processInfo.pid;
            if (processPid == pid) {
                Log.d(TAG, "getPackageName pid: " + pid
                        + ", packageName: " + processInfo.processName);
                int index = processInfo.processName.indexOf(":");
                if (index >= 0) {
                    return processInfo.processName.substring(0, index);
                }
                return processInfo.processName;
            }
        }
        return "";
    }
}
