package com.ainirobot.coreservice.action.advnavi.state;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.MultiRobotConfigBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.google.gson.reflect.TypeToken;

import java.util.ArrayList;
import java.util.List;

/**
 * Data: 2022/9/19 16:14
 * Author: wanglijing
 * 多机准备状态
 */
public class PrepareMultiRobotState extends BaseState {
    private String mDestination;
    private Pose mDestinationPose;

    private static final int BASE_NAVIGATION_ERROR = -20010000;
    /**
     * Lora配置异常，此状态在NavigationService内定义
     */
    private static final int NAVI_ERROR_MULTIPLE_LORA_CONFIG_FAIL = BASE_NAVIGATION_ERROR - 1;

    PrepareMultiRobotState(String name) {
        super(name);
    }

    @Override
    protected void preAction(StateData data, IStateListener listener) {
        super.preAction(data, listener);
        mDestination = mStateData.getDestination();
    }

    @Override
    protected void doAction() {
        super.doAction();
        //1. 获得目的地及备用点
        if (findDestinationAndStandbyPose()) {
            //2. 检查定位状态
            checkEstimate();
        } else {
            onResult(Definition.ERROR_PARAMETER);
        }
    }

    @Override
    protected BaseState getNextState() {
        //多机导航
        if (actionState == Definition.ElevatorAction.ACTION_MULTI_ROBOT) {
            Definition.AdvNaviStrategy advNaviStrategy = mStateData.getNavigationBean().getNaviStrategy();
            Log.d(TAG, "getNextState multiNaviStrategy:" + advNaviStrategy);

            switch (advNaviStrategy) {
                case DEFAULT:
                    //这种情况在上层准备时候就屏蔽掉了，还出现NULL，回到准备状态
                    Log.e(TAG, " getNextState error navi strategy is null ！");
                    return StateEnum.PREPARE.getState();
                case LORA_PRIORITY:
                    return StateEnum.NAVI_LORA_PRIOR.getState();
                case TO_LAST_POINT:
                    return StateEnum.NAVI_LAST_POSE.getState();
                case DYNAMIC_POINT:
                    //导航过程中会根据点位信息动态修改目的地
                    return StateEnum.NAVI_DYNAMIC_POINT.getState();
                case STAR_BY_LORA:
                    return StateEnum.NAVI_STAR_BY_LORA.getState();
                case STAR_BY_POINT:
                    return StateEnum.NAVI_STAR_BY_POINT.getState();
                case TRAIN_BY_POINT:
                    return StateEnum.NAVI_TRAIN_BY_POINT.getState();
                case DYNAMIC_TRAIN_BY_POINT:
                    //导航过程中会根据点位信息动态修改目的地
                    return StateEnum.NAVI_DYNAMIC_TRAIN_BY_POINT.getState();
            }
        }
        //发现多机不可用、未启用，用默认导航
        if (actionState == Definition.ElevatorAction.ACTION_DEFAULT_NAVI) {
            return StateEnum.GO_DESTINATION.getState();
        }
        return super.getNextState();
    }


    @Override
    protected boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_GET_MULTI_ROBOT_CONFIG:
                parseMultiRobotConfig(result);
                return true;
        }
        return super.cmdResponse(cmdId, command, result, extraData);
    }

    /**
     * 找到目的地及备用点
     */
    private boolean findDestinationAndStandbyPose() {
        //获得所有点位
        List<Pose> allPlaceList = getAllPlaceList();
        //找到备用点
        List<Pose> validStandbyList = findBackupPoint(allPlaceList);

        //补位点列表是有可能为空的
        if (null != validStandbyList && validStandbyList.size() > 0) {
            mStateData.setValidStandbyList(validStandbyList);
        } else {
            Log.e(TAG, " *** standby list is null *** ");
        }
        return true;
    }

    /**
     * 检查定位状态
     */
    private void checkEstimate() {
        if (isEstimate()) {
            queryCurrentDeviceMultipleConfig();
        } else {
            onResult(Definition.ERROR_NOT_ESTIMATE, "not estimate");
        }
    }

    /**
     * 查询多机配置
     */
    private void queryCurrentDeviceMultipleConfig() {
        mApi.queryCurrentDeviceMultipleConfig(mReqId);
    }

    /**
     * 解析多机配置
     */
    private void parseMultiRobotConfig(String result) {
        MultiRobotConfigBean multiRobotConfig = null;
        try {
            multiRobotConfig = mGson.fromJson(result, MultiRobotConfigBean.class);
            mStateData.setMultiRobotConfigBean(multiRobotConfig);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (null == multiRobotConfig) {
            Log.i(TAG, "multi robot config is null");
            startDefaultNavi();
            return;
        }

        //判断多机是否可用
        judgeMultipleRobotEnable(multiRobotConfig);
    }

    private void judgeMultipleRobotEnable(MultiRobotConfigBean config) {
        Log.d(TAG, "judgeMultipleRobotEnable:" + config.isEnable());
        if(!config.isEnable()){
            Log.i(TAG, "judgeMultipleRobotEnable false start default navi");
            startDefaultNavi();
            return;
        }
        if (config.getErrorStatus() > 0) {
            onResult(Definition.STATUS_NAVI_MULTI_LORA_CONFIG_FAIL,"lora is in error status");
            return;
        }
        //多机正常，开始多机导航
        startMultiRobotNavi();
    }


    private boolean isEstimate() {
        return TextUtils.equals(getRobotInfo(Definition.SYNC_ACTION_IS_ROBOT_ESTIMATE),
                Definition.RESULT_TRUE);
    }

    /**
     * 获得所有地点，筛选目的地和备用点
     */
    private List<Pose> getAllPlaceList() {
        try {
            String result = getRobotInfo(Definition.SYNC_ACTION_GET_ALL_LOCATION);
            if (TextUtils.isEmpty(result)) {
                return null;
            }

            return mGson.fromJson(result, new TypeToken<List<Pose>>() {
            }.getType());
        } catch (Exception e) {
            e.printStackTrace();
            Log.d(TAG, "getAllPlaceList error");
            onResult(Definition.ERROR_PARAMETER, "get all place json error");
            return null;
        }
    }


    /**
     * 找到备用点位，并根据优先级排序
     *
     * @param poseList 所有点位列表
     * @return 排序好的备用点列表
     */
    private List<Pose> findBackupPoint(List<Pose> poseList) {
        if (null == poseList || poseList.size() <= 0) {
            return null;
        }
        //缓存点位，有去重逻辑
        ArrayList<Pose> validStandbyList = new ArrayList<>();
        ArrayList<Pose> tempPoseList = new ArrayList<>();
        List<String> mStandbyDesList = mStateData.getNavigationBean()
                .getStandbyDesList();

        for (int i = 0; i < poseList.size(); i++) {
            Pose poseInfo = poseList.get(i);
            if (poseInfo == null || TextUtils.isEmpty(poseInfo.getName())) {
                continue;
            }
            if (poseInfo.getName().equals(mDestination)) {
                mDestinationPose = poseInfo;
                continue;
            }
            for (int j = 0; j < mStandbyDesList.size(); j++) {
                String poseName = mStandbyDesList.get(j);
                if (poseInfo.getName().equals(poseName)) {
                    tempPoseList.add(poseInfo);
                }
            }
        }
        if (mDestinationPose == null && tempPoseList.isEmpty()) {
            onResult(Definition.ERROR_TARGET_NOT_FOUND, "main dest and tempPoseList is null");
            return null;
        }
        //对点位进行排序
        for (int i = 0; i < mStandbyDesList.size(); i++) {
            String poseName = mStandbyDesList.get(i);
            for (int j = 0; j < tempPoseList.size(); j++) {
                Pose poseInfo = tempPoseList.get(j);
                if (poseInfo.getName().equals(poseName)) {
                    if (validStandbyList.isEmpty()
                            || !validStandbyList.contains(poseInfo)) {
                        validStandbyList.add(poseInfo);
                    }
                }
            }
        }
        return validStandbyList;
    }


    private void startMultiRobotNavi() {
        updateAction(Definition.ElevatorAction.ACTION_MULTI_ROBOT);
        onSuccess();
    }

    /**
     * 不启用多机，默认导航
     */
    private void startDefaultNavi(){
        updateAction(Definition.ElevatorAction.ACTION_DEFAULT_NAVI);
        onSuccess();
    }
}
