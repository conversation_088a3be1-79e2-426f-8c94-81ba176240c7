/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.server;

import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.IModuleCallback;
import com.ainirobot.coreservice.IStatusListener;
import com.ainirobot.coreservice.ISystemApi;
import com.ainirobot.coreservice.action.ActionManager;
import com.ainirobot.coreservice.bean.Task;
import com.ainirobot.coreservice.bean.TaskEvent;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.config.ConfigManager;
import com.ainirobot.coreservice.core.InternalDef;
import com.ainirobot.coreservice.core.external.ExternalService;
import com.ainirobot.coreservice.core.external.ExternalServiceManager;
import com.ainirobot.coreservice.core.external.OtaService;
import com.ainirobot.coreservice.core.module.Module;
import com.ainirobot.coreservice.core.module.ModuleManager.Permission;
import com.ainirobot.coreservice.core.status.RemoteSubscriber;
import com.ainirobot.coreservice.core.status.StatusManager;
import com.ainirobot.coreservice.core.system.CoreRunStateManager;
import com.ainirobot.coreservice.core.system.SystemManager;
import com.ainirobot.coreservice.core.system.TaskManager;
import com.ainirobot.coreservice.exception.BinderDeathRecipient;
import com.ainirobot.coreservice.listener.IActionListener;
import com.ainirobot.coreservice.service.CoreService;

import java.util.List;

public class SystemServer extends ISystemApi.Stub {
    private final String TAG = "SystemServer";
    private CoreService mCore;

    public SystemServer(CoreService core) {
        this.mCore = core;
    }

    @Override
    public void setCallback(String packageName, IModuleCallback cb) throws RemoteException {
        Log.i(TAG, "System server set call back : " + packageName);
        Module module = new Module(mCore, packageName);
        module.setCallback(cb);
        mCore.getModuleManager().addModule(module, Permission.SYSTEM);

        if (cb != null) {
            cb.asBinder().linkToDeath(new BinderDeathRecipient(packageName), 0);
        }
    }

    @Override
    public int startAction(int reqId, int actionId, String parameter, IActionListener listener) {
        Log.i(TAG, "Send system command : " + actionId + " " + parameter);
        if (Definition.ACTION_HEAD_GET_ALL_PERSON_INFOS == actionId) {
            mCore.getPersonManager().setActionListener(listener);
        } else {
            ActionManager actionManager = mCore.getActionManager();
            actionManager.exCmd(actionId, parameter, listener);
        }
        return 0;
    }

    @Override
    public int stopAction(int reqId, int actionId, boolean isResetHW) {
        if (Definition.ACTION_HEAD_GET_ALL_PERSON_INFOS == actionId) {
            mCore.getPersonManager().releaseActionListener();
        } else {
            ActionManager actionManager = mCore.getActionManager();
            actionManager.exStopCmd(actionId, isResetHW);
        }
        return 0;
    }

    @Override
    public boolean finishModuleWithResponse(int reqId, boolean result, String response) {
        Handler requestHandler = mCore.getRequestHandler();
        Message msg = requestHandler.obtainMessage(InternalDef.MSG_REQUEST_FINISH_PARSER);
        Bundle bundle = new Bundle();
        bundle.putInt(InternalDef.BUNDLE_INT, reqId);
        bundle.putBoolean(InternalDef.BUNDLE_BOOLEAN, result);
        bundle.putString(InternalDef.BUNDLE_RESPONSE, response);
        msg.setData(bundle);
        requestHandler.sendMessage(msg);
        return true;
    }

    @Override
    public String registerStatusListener(String type, IStatusListener listener) {
        StatusManager statusManager = mCore.getStatusManager();
        RemoteSubscriber subscriber = new RemoteSubscriber(listener);
        return statusManager.registerStatusListener(type, subscriber);
    }

    @Override
    public boolean unregisterStatusListener(String id) {
        StatusManager statusManager = mCore.getStatusManager();
        return statusManager.unregisterStatusListener(id);
    }

    @Override
    public boolean startStatusSocket(String type, int socketPort) throws RemoteException {
        String serviceName = mCore.getSocketManager().getServiceName(type);
        ExternalService service = mCore.getExternalServiceManager().getExternalService(serviceName);
        return service.startStatusSocket(type, socketPort);
    }

    @Override
    public boolean closeStatusSocket(String type, int socketPort) throws RemoteException {
        String serviceName = mCore.getSocketManager().getServiceName(type);
        ExternalService service = mCore.getExternalServiceManager().getExternalService(serviceName);
        return service.closeStatusSocket(type, socketPort);
    }

    @Override
    public boolean startOtaUpgrade(boolean needDownload, boolean isRollback) {
        updateSystemStatus(Definition.SYSTEM_OTA, Definition.START);

        OtaService otaService = mCore.getExternalServiceManager().getOtaService();
        if (isRollback) {
            otaService.startOtaRollback();
        } else {
            otaService.startOtaUpgrade(needDownload);
        }

        return false;
    }

    @Override
    public String getOtaUpgradeDescription() {
        OtaService otaService = mCore.getExternalServiceManager().getOtaService();
        return otaService.getOtaUpgradeDescription();
    }

    @Override
    public boolean cancelOtaUpgradeDownload() {
        updateSystemStatus(Definition.SYSTEM_OTA, Definition.FINISHED);

        OtaService otaService = mCore.getExternalServiceManager().getOtaService();
        return otaService.cancelOtaUpgradeDownload();
    }

    @Override
    public boolean installPatch(Bundle bundle) {
        OtaService otaService = mCore.getExternalServiceManager().getOtaService();
        return otaService.installPatch(bundle);
    }

    @Override
    public void startAppControl(String packageName) {
        Log.i(TAG, "Start app control : " + packageName);
        CoreRunStateManager coreRunStateManager = mCore.getCoreRunSateManager();
        coreRunStateManager.startAppControl(packageName);
    }

    @Override
    public void stopAppControl() {
        Log.i(TAG, "Stop app control");
        CoreRunStateManager coreRunStateManager = mCore.getCoreRunSateManager();
        coreRunStateManager.stopCurrentAppControl();
        resetSystemStatus();
    }

    @Override
    public void updateSystemStatus(String status, String data) {
        mCore.getSystemManager().onSystemStatusUpdate(status, data);
    }

    @Override
    public void resetSystemStatus() {
        mCore.getSystemManager().resetSystemStatus();
    }

    @Override
    public void sendStatusReport(String type, String data) {
        StatusManager statusManager = mCore.getStatusManager();
        statusManager.handleStatus(type, data);
    }

    @Override
    public String getActiveApp() {
        CoreRunStateManager coreRunStateManager = mCore.getCoreRunSateManager();
        return coreRunStateManager.getActiveApp();
    }

    @Override
    public void disableEmergency() {
        setSystemStatusEnabled(Definition.SYSTEM_EMERGENCY, false);
    }

    @Override
    public void disableBattery() {
        setSystemStatusEnabled(Definition.SYSTEM_BATTERY, false);
    }

    @Override
    public void disableRadar() throws RemoteException {
        setSystemStatusEnabled(Definition.SYSTEM_RADAR, false);
    }

    @Override
    public void enableRadar() throws RemoteException {
        setSystemStatusEnabled(Definition.SYSTEM_RADAR, true);
    }

    public void setSystemStatusEnabled(String status, boolean enabled) {
        SystemManager systemManager = mCore.getSystemManager();
        systemManager.setSystemStatusEnabled(status, enabled);
    }

    @Override
    public boolean updateCurrentStatus() {
        SystemManager systemManager = mCore.getSystemManager();
        return systemManager.updateCurrentStatus();
    }

    @Override
    public String getRobotInfo(int reqType, String params) {
        Log.d(TAG, "getRobotInfo reqType:" + reqType + " params=" + params);
        return mCore.getRobotInfoManager().getRobotInfo(reqType, params);
    }

    @Override
    public String getSystemStatus() {
        SystemManager systemManager = mCore.getSystemManager();
        return systemManager.getSystemStatus();
    }

    @Override
    public boolean isExternalServiceEnable(String name) {
        ExternalServiceManager serviceManager = mCore.getExternalServiceManager();
        ExternalService service = serviceManager.getExternalService(name);
        return service != null && service.isEnable();
    }

    @Override
    public void getRobotStatus(String type, IStatusListener listener) throws RemoteException {
        StatusManager statusManager = mCore.getStatusManager();
        statusManager.getRobotStatus(type, listener);
    }

    @Override
    public String getPassword(int length) throws RemoteException {
        // FIXME(toBtoC 合并接口，空实现)
        new RuntimeException("合并接口，空实现").printStackTrace();
        return null;
    }

    @Override
    public String reportTask(Task task) throws RemoteException {
        return TaskManager.getInstance().addTask(task);
    }

    @Override
    public void reportTaskEvent(TaskEvent taskEvent) throws RemoteException {
        TaskManager.getInstance().addTakEvent(taskEvent);
    }

    @Override
    public List<Task> getCurrentTask() throws RemoteException {
        return TaskManager.getInstance().getCurrentTask();
    }

    public boolean setServerSupportLanguageList(String params) {
        return mCore.getLanguageManger().setServerLanguageList(params);
    }

    @Override
    public boolean hasTopIR() throws RemoteException {
        return ConfigManager.isHasTopIR();
    }

    @Override
    public boolean hasChargeIR() throws RemoteException {
        return ConfigManager.isHasChargeIR();
    }

    @Override
    public boolean isUseProZcbLed() throws RemoteException {
        return ConfigManager.isUseAutoEffectLed();
    }

    @Override
    public boolean isHasClavicleLight() throws RemoteException {
        return ConfigManager.isHasClavicleLed();
    }

    @Override
    public boolean isHasChestLight() throws RemoteException {
        return ConfigManager.isHasChestLed();
    }

    @Override
    public boolean isHasProTrayLED() throws RemoteException {
        return ConfigManager.isHasTrayLight();
    }

    @Override
    public boolean hasMono() throws RemoteException {
        return ConfigManager.hasMono();
    }

    @Override
    public boolean hasTopMono() throws RemoteException {
        return ConfigManager.hasTopMono();
    }

    @Override
    public void disablePushWarning() throws RemoteException {
        setSystemStatusEnabled(Definition.SYSTEM_PUSH_WARNING, false);
    }

    @Override
    public void enablePushWarning() throws RemoteException {
        setSystemStatusEnabled(Definition.SYSTEM_PUSH_WARNING, true);
    }

    @Override
    public boolean isUseAutoEffectLed() throws RemoteException {
        return ConfigManager.isUseAutoEffectLed();
    }

    @Override
    public boolean isHasClavicleLed() throws RemoteException {
        return ConfigManager.isHasClavicleLed();
    }

    @Override
    public boolean isHasChestLed() throws RemoteException {
        return ConfigManager.isHasChestLed();
    }

    @Override
    public boolean isHasTrayLight() throws RemoteException {
        return ConfigManager.isHasTrayLight();
    }

    @Override
    public String getCreateMapType() throws RemoteException {
        return ConfigManager.getCreateMapType();
    }

    public boolean isSupportElevator() {
        return ConfigManager.isSupportElevatorFunction();
    }

    @Override
    public String isAlreadyInElevator() throws RemoteException {
        return mCore.getRobotInfoManager().isAlreadyInElevator();
    }
}
