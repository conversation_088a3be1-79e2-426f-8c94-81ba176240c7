package com.ainirobot.coreservice.action.advnavi.state;

import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.MultiRobotStatus;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.google.gson.JsonSyntaxException;
import com.google.gson.annotations.SerializedName;
import com.google.gson.reflect.TypeToken;

import java.util.ArrayList;
import java.util.List;

/**
 * Data: 2022/9/26 18:05
 * Author: wanglijing
 * 多机导航，动态调度
 */
public class NaviDynamicPointStrategy extends BaseMultiRobotNaviState {

    private long mNaviTime = 0;

    private static final int NAVI_CHANGE_GOAL_TIME_OFFSET = 2000;

    NaviDynamicPointStrategy(String name) {
        super(name);
    }

    /**
     * 开始多机返航时计算出餐口和备用点距离当前的距离
     */
    public List<DistanceData> mDistanceList = new ArrayList<>();

    private volatile MultipleStatus multipleStatus;

    private enum MultipleStatus {
        PREPARE,
        START,
        NAVIGATION,

    }

    @Override
    protected void preAction(StateData data, IStateListener listener) {
        super.preAction(data, listener);
        updateMultiStatus(MultipleStatus.PREPARE);
        //动态回程策略 需考虑出餐口占用情况
        addDestinationToStandbyList();
    }


    @Override
    protected void doAction() {
        super.doAction();
        //第一次导航前获得一次距各点的距离
        calculateNavigationDistance();
    }

    @Override
    protected boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        if (Definition.CMD_NAVI_GET_NAVI_PATH_INFO_TO_GOALS.equals(command)) {//距离规划是否成功
            checkGoalsPathInfo(result);
            return true;
        }
        return super.cmdResponse(cmdId, command, result, extraData);
    }

    /**
     * 判断当前距目的地及各备用点的距离
     */
    public void calculateNavigationDistance() {
        List<String> list = new ArrayList<>();
        for (Pose pose : mValidStandbyList) {
            list.add(pose.getName());
        }

        if (list.size() <= 0) {
            //没有补位点，直接开始监听
            Log.i(TAG, " standby list is null");
            updateMultiStatus(MultipleStatus.START);
            return;
        }

        Log.d(TAG, "standby list :" + list);
        mApi.getNaviPathInfoToGoals(mReqId, list);
    }

    /**
     * 判断到各点距离是否有效
     */
    private void checkGoalsPathInfo(String result) {
        //距离信息正确
        if (parsePathInfo(result)) {
            //开始监听多机状态
            updateMultiStatus(MultipleStatus.START);
        }
    }

    @Override
    protected void handleMultipleRobotStatus(List<MultiRobotStatus> robotStatusList) {
        switch (multipleStatus) {
            case START:
                prepareNavigation(robotStatusList);
                break;
            case NAVIGATION:
                calculateIsNeedChangeGoal(robotStatusList);
                break;
        }
    }

    /**
     * 判断目的地是否可用，不可以找到合适点位
     */
    private void prepareNavigation(List<MultiRobotStatus> robotStatusList) {
        String poseName = getSuitablePoseByDistance(robotStatusList, true);
        Log.i(TAG, "prepareNavigation get pose: " + poseName);
        if (!TextUtils.isEmpty(poseName)) {
            updateMultiStatus(MultipleStatus.NAVIGATION);
            startGoSuitablePose(poseName);
        } else {
            Log.e(TAG, "null suitable pose ...");
        }
    }

    /**
     * 根据多机信息，判断是否需要更换目标点
     */
    private void calculateIsNeedChangeGoal(List<MultiRobotStatus> robotStatusList) {
        String poseName = getSuitablePoseByDistance(robotStatusList, false);
        if (isNeedChangeGoal(poseName)) {
            //需要换点，修改导航目标点
            Log.i(TAG, "isNeedChangeGoal need change goal ! change to :" + poseName);
            startGoSuitablePose(poseName);
        }
    }

    /**
     * 判断是否需要换点
     * @param poseName  需要换的点位名称
     * @return true:需要换点
     */
    private boolean isNeedChangeGoal(String poseName) {
        Log.i(TAG, "isNeedChangeGoal get pose: " + poseName);
        long curTime = SystemClock.uptimeMillis();
        //当前点不为空，且和上层导航时间相差 NAVI_CHANGE_GOAL_TIME_OFFSET，则可以换点
        boolean needChange = (poseName != null && !TextUtils.equals(poseName, getNaviDestination())
                && curTime - mNaviTime >= NAVI_CHANGE_GOAL_TIME_OFFSET);

        if (!needChange) {
            Log.d(TAG, "isNeedChangeGoal not change , suitablePose: " + poseName
                    + ", naviPose:" + getNaviDestination()
                    + ", timeDiff:" + (curTime - mNaviTime));
        }
        return needChange;
    }


    /**
     * 解析当前距目的地及各备用点的距离
     * 失败上报 ERROR_PATH_INFO
     */
    private boolean parsePathInfo(String result) {
        if (!Definition.FAILED.equals(result)
                && !Definition.PARAMS_TIMEOUT.equals(result)) {
            try {
                mDistanceList = mGson.fromJson(result, new TypeToken<List<DistanceData>>() {
                }.getType());
                for (int i = 0; i < mDistanceList.size(); i++) {
                    DistanceData p = (DistanceData) mDistanceList.get(i);
                    Log.d(TAG, "path distance[" + i + "]:" + p.distance);
                }
                return true;
            } catch (JsonSyntaxException e) {
                e.printStackTrace();
            }
        }
        processResultWithDestination(Definition.ERROR_PATH_INFO, result);
        return false;
    }

    /**
     * 从列表中找到最合适的点
     */
    private String getSuitablePoseByDistance(List<MultiRobotStatus> robotStatusList, boolean isFirst) {
        //距离目标点过近, 不更换目标点的阈值
        //两机距离相近, 不更换目标点的阈值
        MultiRobotStatus minDistanceStatus;
        MultiRobotStatus myDistanceStatus;

        for (Pose pose : mValidStandbyList) {
            minDistanceStatus = null;
            myDistanceStatus = null;
            Log.d(TAG, "findSuitPose cal pose name=" + pose.getName() + ", " + pose);
            for (MultiRobotStatus status : robotStatusList) {
                if (status.isCurRobot()) {
                    myDistanceStatus = status;
                    continue;
                }

                //判断机器人当前状态是不是在点范围内
                if (isInPoseRange(calculatePoseDistance(pose, status.getPose()))) {
                    // 有人在用这个点, 计算下一个点
                    Log.d(TAG, "findSuitPose " +
                            "robot: " + status.getId() + " , status: " + status.getStatus() +
                            " in this place:" + pose.getName());
                    minDistanceStatus = status;
                    break;
                }
                double distance = calculatePoseDistance(pose, status.getGoal());
                //在导航状态，且在点范围内
                if (status.getStatus() == 3
                        && isInPoseRange(distance)) {
                    // 有人去这个点, 继续计算, 是不是还有人去, 并记录离这个点最近的人
                    minDistanceStatus = status.getDistance() < ((minDistanceStatus == null)
                            ? Double.MAX_VALUE : minDistanceStatus.getDistance()) ? status : minDistanceStatus;
                    Log.d(TAG, "findSuitPose min distance "
                            + " ,id:[" + status.getId() + "] , status:" + status.getStatus()
                            + ", distance: " + distance);
                } else {
                    // 当前id没去这个点
                    Log.d(TAG, "findSuitPose not go this pose: " + pose.getName()
                            + " ,id:[" + status.getId() + "] , status:" + status.getStatus()
                            + ", distance:" + distance);
                }

            }
            if (minDistanceStatus == null) {
                // 所有人都没用这个点 && 所有人都没去这个点
                Log.i(TAG, " *** findSuitPose nobody goal there, i go: " + pose.getName());
                return pose.getName();
            } else {
                // 有人要去这个点，计算我是不是最小的
                if (isFirst) {
                    // 第一次计算, 预计算的distance有意义
                    double poseDistance = getFirstPoseDistance(pose);
                    if (poseDistance < minDistanceStatus.getDistance()) {
                        // 我距离这个点最近, 因为是第一次, 不计算偏移值, 一定要去
                        Log.i(TAG,  " *** findSuitPose first calculate, " + pose.getName() + " i nealy:" + poseDistance);
                        return pose.getName();
                    }
                } else {
                    // 非第一次, 要使用minDistanceStatus
                    if (myDistanceStatus == null) {
                        Log.i(TAG, " *** findSuitPose myDistanceStatus is null ????????");
                        return null;
                    }
                    if (myDistanceStatus.getStatus() != 3) {
                        Log.i(TAG, " *** findSuitPose i'm not in navi ");
                        return null;
                    }
                    if (myDistanceStatus.getDistance() + DEFAULT_DESTINATION_CHANGE_GOAL_OFFSET < minDistanceStatus.getDistance()) {
                        if (isInPoseRange(calculatePoseDistance(pose, myDistanceStatus.getGoal()))) {
                            // 我原来也是要去这个点
                            Log.i(TAG, " *** findSuitPose it's my goal:" + pose.getName() + " do not go again");
                            return null;
                        }
                        // 我加上偏移值仍然是离这个点最近, 我当前又没去这个点
                        Log.i(TAG, " *** findSuitPose i'm nealy ,distance:" + myDistanceStatus.getDistance()
                                + ", i go:" + pose.getName());
                        return pose.getName();
                    }
                }
                // 如果我去不了的话，换下一个点
            }
        }
        return null;
    }

    private double getFirstPoseDistance(Pose pose) {
        for (DistanceData data : mDistanceList) {
            if (TextUtils.equals(data.endPose.getName(), pose.getName())) {
                return data.distance;
            }
        }
        return Double.MAX_VALUE;
    }

    private void startGoSuitablePose(String poseName) {
        Log.i(TAG, "startGoSuitablePose:" + poseName + " state:" + multipleStatus);

        mNaviTime = SystemClock.uptimeMillis();
        startNavigation(poseName, isOneAvailableStandbyPoseCurrently());
    }

    private void updateMultiStatus(MultipleStatus status) {
        Log.i(TAG, "updateMultiStatus: " + status);
        this.multipleStatus = status;
    }

    private static class DistanceData {
        Pose startPose;
        Pose endPose;
        @SerializedName("pathLength")
        double distance;
        double time;
        int state;
    }

}
