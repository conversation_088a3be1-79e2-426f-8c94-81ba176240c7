package com.ainirobot.coreservice.bi.report;

import android.net.Uri;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import java.util.List;

public class RobotSettingsReporter extends SystemInformationReporter {

    public RobotSettingsReporter() {
        super("RobotSetting","RobotSettingManager");
    }

    public void reportSettingsGlobalChanged(boolean selfChange, Uri uri) {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("selfChange", selfChange);
        jsonObject.addProperty("uri", uri.toString());
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("onRobotSettingChanged")
            .addNodeParams(jsonObject.toString())
            .reportV();
    }

    public void reportSetRobotSetting(final String key, final String value, final int count) {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("key", key);
        jsonObject.addProperty("value", value);
        jsonObject.addProperty("count", count);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("setRobotSetting")
            .addNodeParams(jsonObject.toString())
            .reportV();
    }

    public void reportGetRobotSetting(final String key, String value) {
        JsonObject params = new JsonObject();
        params.addProperty("key", key);

        JsonObject result = new JsonObject();
        result.addProperty("value", value);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("getRobotSetting")
            .addNodeParams(params.toString())
            .addNodeResult(result.toString())
            .reportV();
    }

    public void reportRegisterListener(final String key) {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("key", key);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("registerListener")
            .addNodeParams(jsonObject.toString())
            .reportV();
    }

    public void reportRegisterListener(final List<String> keyList) {
        Gson gson = new Gson();
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("keyList", gson.toJson(keyList));
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("registerListener")
            .addNodeParams(jsonObject.toString())
            .reportV();
    }

    public void reportUnregisterListener() {
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("registerListener")
            .reportV();
    }
}
