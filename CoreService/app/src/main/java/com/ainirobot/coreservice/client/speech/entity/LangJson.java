package com.ainirobot.coreservice.client.speech.entity;

import java.util.Arrays;

public class Lang<PERSON><PERSON> {
    private boolean isAuto;
    private int[] langs;

    public LangJson() {
    }

    public LangJson(boolean isAuto) {
        this.isAuto = isAuto;
    }

    public LangJson(boolean isAuto, int[] langs) {
        this.isAuto = isAuto;
        this.langs = langs;
    }

    public boolean isAuto() {
        return isAuto;
    }

    public void setAuto(boolean auto) {
        isAuto = auto;
    }

    public int[] getLangs() {
        return langs;
    }

    public void setLangs(int[] langs) {
        this.langs = langs;
    }

    @Override
    public String toString() {
        return "LangJson{" +
                "isAuto=" + isAuto +
                ", langs=" + Arrays.toString(langs) +
                '}';
    }
}
