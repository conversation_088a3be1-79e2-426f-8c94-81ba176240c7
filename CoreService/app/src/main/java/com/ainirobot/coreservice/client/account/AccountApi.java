/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.client.account;

import android.content.Context;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.IAccountApi;
import com.ainirobot.coreservice.IRobotBinderPool;
import com.ainirobot.coreservice.client.BaseSubApi;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.messagedispatcher.AccountDispatcher;
import com.ainirobot.coreservice.client.person.FaceBean;
import com.ainirobot.coreservice.listener.IActionListener;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.util.ArrayList;
import java.util.List;

public class AccountApi extends BaseSubApi {

    private static final String TAG = AccountApi.class.getSimpleName();
    private static AccountApi instance;
    private IAccountApi mApi;
    private Gson mGson;
    private final ArrayList<AccountListener> mAccountListenerList = new ArrayList<>();
    private AccountListener mAccountListener;

    private AccountApi() {
        Log.i(TAG, "AccountApi create");
        startNewThread(TAG);
        mGson = new Gson();
    }

    public static synchronized AccountApi getInstance() {
        if (null == instance) {
            instance = new AccountApi();
        }
        return instance;
    }

    @Override
    public void onConnect(IRobotBinderPool robotBinderPool, Context context) {
        Log.i(TAG, "AccountApi start connect");
        try {
            mApi = IAccountApi.Stub.asInterface(
                    robotBinderPool.queryBinder(Definition.BIND_ACCOUNT, null));
            Log.i(TAG, "AccountApi connect success");
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * get all members which not mark delete
     * (memberBean contains only userBean)
     */
    public List<MemberBean> getMemberList() {
        if (null == mApi) {
            return null;
        }
        try {
            return mGson.fromJson(mApi.getMemberList(),
                    new TypeToken<List<MemberBean>>(){}.getType());
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * get all members which not mark delete
     * (memberBean contains userBean and faceBean)
     */
    public List<MemberBean> getWholeMemberList() {
        if (null == mApi) {
            return null;
        }
        try {
            return mGson.fromJson(mApi.getWholeMemberList(),
                    new TypeToken<List<MemberBean>>(){}.getType());
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * get last recognized memberBean
     */
    public MemberBean getRecentMember() {
        if (null == mApi) {
            return null;
        }
        try {
            String recentMember = mApi.getRecentMember();
            if (TextUtils.isEmpty(recentMember)) {
                return null;
            }
            return mGson.fromJson(recentMember, MemberBean.class);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return null;
    }

    public String getSystemToken() {
        if (null == mApi) {
            return null;
        }
        try {
            return mApi.getSystemToken();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return null;
    }

    public void registerAccountListener(AccountListener listener) {
        if (mApi == null || listener == null) {
            return;
        }

        if (mAccountListener == null) {
            mAccountListener = new AccountListener() {
                @Override
                public void memberUpdate() {
                    synchronized (mAccountListenerList) {
                        for (AccountListener listenerItem : mAccountListenerList) {
                            listenerItem.memberUpdate();
                        }
                    }
                }
            };
        }

        synchronized (mAccountListenerList) {
            if (!mAccountListenerList.contains(listener)) {
                mAccountListenerList.add(listener);
                try {
                    Log.d(TAG, "register account listener");
                    AccountDispatcher accountDispatcher = mMessageDispatcher.obtainAccountDispatcher(
                            mHandlerThread.getLooper(), mAccountListener);
                    mApi.registerAccountListener(accountDispatcher);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public void unregisterAccountListener(AccountListener listener) {
        if (mApi == null || listener == null) {
            return;
        }

        if (mAccountListener == null) {
            return;
        }

        synchronized (mAccountListenerList) {
            if (mAccountListenerList.remove(listener) && mAccountListenerList.size() == 0) {
                try {
                    Log.d(TAG, "unregister account listener");
                    AccountDispatcher accountDispatcher = mMessageDispatcher.obtainAccountDispatcher(
                            mHandlerThread.getLooper(), mAccountListener);
                    mApi.unregisterAccountListener(accountDispatcher);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public boolean reRegisterWithUserIdById(int personId, String userId, CommandListener listener) {
        if (mApi == null) {
            return false;
        }

        try {
            IActionListener dispatcher = mMessageDispatcher.obtainActionDispatcher(
                    mHandlerThread.getLooper(), listener);
            return mApi.reRegisterWithUserIdById(personId, userId, dispatcher);
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }

    public boolean reRegisterWithUserIdByPic(String picturePath, String userId, CommandListener listener) {
        if (mApi == null) {
            return false;
        }

        try {
            IActionListener dispatcher = mMessageDispatcher.obtainActionDispatcher(
                    mHandlerThread.getLooper(), listener);
            return mApi.reRegisterWithUserIdByPic(picturePath, userId, dispatcher);
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }

    public void recoveryRobot(String userId, String userToken, ActionListener listener) {
        if (mApi == null) {
            return;
        }

        try {
            IActionListener actionListener = mMessageDispatcher.obtainActionDispatcher(
                    mHandlerThread.getLooper(), listener);
            mApi.recoveryRobot(userId, userToken, actionListener);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public List<FaceBean> getRegisteredFaceList() {
        if (mApi == null) {
            return null;
        }

        try {
            return getFaceListFromString(mApi.getRegisteredFaceList());
        } catch (RemoteException e) {
            e.printStackTrace();
            return null;
        }
    }

    private List<FaceBean> getFaceListFromString(String message) {
        if (TextUtils.isEmpty(message)) {
            return null;
        }
        return mGson.fromJson(message, new TypeToken<List<FaceBean>>() {
        }.getType());
    }
}
