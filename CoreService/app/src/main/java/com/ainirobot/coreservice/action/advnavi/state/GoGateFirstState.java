package com.ainirobot.coreservice.action.advnavi.state;

import static com.ainirobot.coreservice.client.actionbean.NavigationAdvancedBean.TYPE_POSE;

import android.util.Log;

import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.MultiRobotStatus;
import com.ainirobot.coreservice.client.actionbean.NavigationAdvancedBean;
import com.ainirobot.coreservice.client.actionbean.Pose;

import java.util.List;

/**
 * Data: 2024/2/22
 * Author: dengting
 * GoGateFirstState
 * 导航去闸机口点位状态
 */
public class GoGateFirstState extends BaseNavigationState {

    //导航超时失败重试次数
    private int naviRetryCount = 0;
    //导航重试最多次数
    private final int MAX_NAVI_RETRY_COUNT = 5;
    private Pose firstPose;

    GoGateFirstState(String name) {
        super(name);
    }

    @Override
    protected boolean isEnableMultiRobotStatus() {
        return false;
    }

    @Override
    protected void preAction(StateData data, IStateListener listener) {
        super.preAction(data, listener);
        naviRetryCount = 0;
        if (mStateData.isMultiGateMode()) {
            firstPose = mStateData.getCurrentGateFirstPose();
        } else {
            firstPose = mStateData.getGateFirstPose();
        }
        //判断第一个点位是时入口还是出口
    }

    @Override
    protected void doAction() {
        super.doAction();
        onStatusUpdate(Definition.STATUS_START_GO_FIRST_GATE, "start to first gate");
        naviToFirstGate();   //do action
    }

    @Override
    protected void exit() {
        super.exit();
    }

    @Override
    protected BaseState getNextState() {
        switch (actionState) {
            //导航成功事件 在闸机外等待状态
            case ACTION_NAVI_SUC:
                return StateEnum.WAIT_OUTSIDE_GATE.getState();
        }
        return super.getNextState();
    }

    @Override
    protected boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_RESUME_SPECIAL_PLACE_THETA:
                parseResumeRobotTheta();
                return true;
        }
        return super.cmdResponse(cmdId, command, result, extraData);
    }


    @Override
    protected boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        return super.cmdStatusUpdate(cmdId, command, status, extraData);
    }

    @Override
    protected void onNavigationStatusUpdate(int result, String message, String extraData) {
        onStatusUpdate(result, message, extraData);
    }

    @Override
    protected void onNavigationError(int result, String message, String extraData) {
        switch (result) {
            case Definition.ERROR_DESTINATION_CAN_NOT_ARRAIVE:
            case Definition.ERROR_MULTI_ROBOT_WAITING_TIMEOUT:
            case Definition.ERROR_NAVIGATION_AVOID_TIMEOUT:
                naviRetry();    //重试
                return;
        }
        onResult(result, message, extraData);
    }

    @Override
    protected void onNavigationResult(int result, String message, String extraData) {
        switch (result) {
            case Definition.ERROR_IN_DESTINATION:
            case Definition.RESULT_OK:
                //有可能机器人朝向不对，尝试归正方向一次
                resumeRobotTheta();
                break;
        }
    }

    @Override
    protected void handleMultipleRobotStatus(List<MultiRobotStatus> robotStatusList) {

    }

    private void naviToFirstGate() {
        Log.d(TAG, "naviToFirstGate");
        NavigationAdvancedBean bean = cloneNaviBean();
        bean.setNaviType(TYPE_POSE);
        bean.setDestination("");
        bean.setNaviPose(firstPose);
        startNavigation(bean);
    }

    private void naviRetry() {
        //超过最大重试次数
        naviRetryCount++;
        if (naviRetryCount > MAX_NAVI_RETRY_COUNT) {
            naviFail();
            return;
        }
        //重试
        naviToFirstGate();   //retry
    }

    private void naviFail() {
        onResult(Definition.ERROR_NAVIGATION_FAILED);
    }

    private void naviSuccess() {
        Log.d(TAG, " navi success ");
        //否则就是正常的导航成功
        updateAction(Definition.ElevatorAction.ACTION_NAVI_SUC);
        // 这里区分一下导航过程中的机器人状态信息回复
        if (mStateData.isMultiGateMode()) {
            onStatusUpdate(Definition.STATUS_ARRIVED_GATE_FIRST_POSE,
                "Arrived at the " + (mStateData.getCurrentGateIndex() + 1) +  "gate");
        } else {
            onStatusUpdate(Definition.STATUS_ARRIVED_GATE_FIRST_POSE, "arrived first gate");
        }
        onSuccess();
    }

    /**
     * 发送归正方向命令
     * 归正机器人朝向与建图时方向相同
     */
    private void resumeRobotTheta() {
        Log.d(TAG, "start resume robot theta");
        mApi.resumeSpecialPlaceTheta(mReqId, firstPose.getName());
    }

    /**
     * 处理归正机器人朝向
     */
    private void parseResumeRobotTheta() {
        naviSuccess();
    }
}
