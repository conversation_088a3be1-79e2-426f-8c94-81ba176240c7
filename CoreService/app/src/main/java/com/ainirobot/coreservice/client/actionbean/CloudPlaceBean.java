/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.client.actionbean;

import com.google.gson.JsonObject;
import com.google.gson.annotations.SerializedName;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

public class CloudPlaceBean {
    @SerializedName("map_uuid")
    private String uuid;

    @SerializedName("update_time")
    private String updateTime;

    @SerializedName("status")
    private int status;

    @SerializedName("version")
    private String version;

    @SerializedName("sites")
    private List<JsonObject> sites;

    @SerializedName("sites_extend")
    private String sitesExtend;

    public String getMapId() {
        return uuid;
    }

    public int getStatus() {
        return status;
    }

    public List<JsonObject> getSites() {
        return sites;
    }

    public String getSitesExtend() {
        return sitesExtend;
    }

    public static class SitesExtend {
        int mapVersion;

        public int getMapVersion() {
            return mapVersion;
        }

        public SitesExtend setMapVersion(int mapVersion) {
            this.mapVersion = mapVersion;
            return this;
        }

        public JSONObject getJson() {
            JSONObject place = new JSONObject();
            try {
                place.put("map_version", this.mapVersion);
            } catch (JSONException var5) {
                var5.printStackTrace();
            }
            return place;
        }

        @Override
        public String toString() {
            return "SitesExtend{" +
                    "mapVersion=" + mapVersion +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "CloudPlaceBean{" +
                "uuid='" + uuid + '\'' +
                ", updateTime='" + updateTime + '\'' +
                ", status=" + status +
                ", version='" + version + '\'' +
                ", sites=" + sites +
                ", sitesExtend='" + sitesExtend + '\'' +
                '}';
    }

}