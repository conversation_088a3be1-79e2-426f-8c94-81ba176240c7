package com.ainirobot.coreservice.action.advnavi.state;

import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.MultiRobotStatus;
import com.ainirobot.coreservice.client.actionbean.NavigationAdvancedBean;

import java.util.List;

/**
 * Data: 2022/8/9 11:09
 * Author: wanglijing
 * Description: GoDestinationState
 */
public class GoDestinationState extends BaseNavigationState {
    private static final int GO_DESTINATION_AVOID_COUNT = 24;
    private static final long GO_DESTINATION_AVOID_INTERVAL_TIME = 5 * 1000;
    private static final long GO_DESTINATION_MOVING_TIMEOUT_TIME = 10 * 60 * 1000;

    private int naviFailCount = 0;
    //导航重试最多次数
    private final int RETRY_NAVI_MAX_COUNT = 5;

    GoDestinationState(String name) {
        super(name);
    }

    @Override
    protected boolean isEnableMultiRobotStatus() {
        return true;
    }

    @Override
    protected void doAction() {
        NavigationAdvancedBean navigationBean = mStateData.getNavigationBean();
        navigationBean.setIsInLocationCheckTheta(true);
        navigationBean.setAvoidIntervalTime(GO_DESTINATION_AVOID_INTERVAL_TIME);
        navigationBean.setMaxAvoidCount(GO_DESTINATION_AVOID_COUNT);
        navigationBean.setMovieTimeout(GO_DESTINATION_MOVING_TIMEOUT_TIME);
        navigationBean.setDestinationRange(NavigationAdvancedBean.NAVIGATION_DEFAULT_DESTINATION_RANGE);//目标点不需要停到精确位置
        navigationBean.setNaviType(NavigationAdvancedBean.TYPE_DESTINATION);
        startNavigation(navigationBean);

        onStatusUpdate(Definition.STATUS_START_GO_DESTINATION,
                navigationBean.getDestination());
    }

    @Override
    protected void onNavigationStatusUpdate(int result, String message, String extraData) {
        onStatusUpdate(result, message, extraData);
    }

    @Override
    protected void onNavigationError(int result, String message, String extraData) {
        switch (result) {
            case Definition.ERROR_DESTINATION_CAN_NOT_ARRAIVE:
            case Definition.ERROR_MULTI_ROBOT_WAITING_TIMEOUT: //TODO 多机等待超时不应该重试5次，总时间太长
            case Definition.ERROR_NAVIGATION_AVOID_TIMEOUT:
                Log.d(TAG, "onNavigationError: result = " + result + ", message = " + message + ", extraData = " + extraData);
                naviRetry(result, message, extraData);    //重试
                return;
        }
        onResult(result, message, extraData);
    }

    private void naviRetry(int result, String message, String extraData) {
        Log.d(TAG, "naviRetry: naviFailCount = " + naviFailCount);
        naviFailCount++;
        if (naviFailCount > RETRY_NAVI_MAX_COUNT) {
            onResult(result, message, extraData);
            return;
        }
        doAction();
    }

    @Override
    protected void onNavigationResult(int result, String message, String extraData) {
        processResultWithDestination(result, message);
    }

    @Override
    protected void handleMultipleRobotStatus(List<MultiRobotStatus> robotStatusList) {

    }
}
