/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.client.actionbean;

/**
 * Created by Orion on 2021/3/15.
 */
public class AuthBean {

    private String pkgName;
    private String pkgSign;
    private String appId;
    private int isAuth = AuthState.NOT_AUTH;

    public static class AuthState {
        public static final int NOT_AUTH = 0;
        public static final int AUTH = 1;
    }

    public String getPkgName() {
        return pkgName;
    }

    public void setPkgName(String pkgName) {
        this.pkgName = pkgName;
    }

    public String getPkgSign() {
        return pkgSign;
    }

    public void setPkgSign(String pkgSign) {
        this.pkgSign = pkgSign;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public int getAuth() {
        return isAuth;
    }

    public void setAuth(int isAuth) {
        this.isAuth = isAuth;
    }

    public boolean isAuth() {
        return this.isAuth == AuthState.AUTH;
    }

    @Override
    public String toString() {
        return "AuthBean{" +
                ", pkgName='" + pkgName + '\'' +
                ", pkgSign=" + pkgSign + '\'' +
                ", appId=" + appId + '\'' +
                ", isAuth=" + isAuth +
                '}';
    }
}
