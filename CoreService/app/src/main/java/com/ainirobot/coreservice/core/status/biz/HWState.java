package com.ainirobot.coreservice.core.status.biz;

import android.text.TextUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

public class HWState {

    private String mServiceName;
    private HashMap<String, String> mSyncStatusMap;
    private ConcurrentHashMap<String, String> mHWStatus;
    private OnStatusChangeListener mListener;

    public HWState(String serviceName) {
        mServiceName = serviceName;
        mHWStatus = new ConcurrentHashMap<>();
        mHWStatus.clear();

        mSyncStatusMap = new HashMap<>();
        mSyncStatusMap.clear();
        prepare();
    }

    public void setOnStatusChangeListener(OnStatusChangeListener listener) {
        this.mListener = listener;
    }

    private void prepare() {
        HashMap statusMapping = StatusMapping.getInstance().getStatusMapping();
        if (statusMapping.size() > 0) {
            loadConfig(mServiceName, statusMapping);
        }
    }

    private void loadConfig(String serviceName, HashMap<String, String> stateMap) {
        if (TextUtils.isEmpty(serviceName) || stateMap == null) {
            return;
        }

        Set<Map.Entry<String, String>> entries = stateMap.entrySet();
        for (Map.Entry<String, String> entry : entries) {
            if (serviceName.equalsIgnoreCase(entry.getValue())) {
                getStatusMap().put(entry.getKey(), "");
            }
        }
    }

    public ConcurrentHashMap<String, String> getStatusMap() {
        return mHWStatus;
    }

    public Set<String> getTypeSet() {
        return mHWStatus.keySet();
    }

    public String getServiceName() {
        return mServiceName;
    }

    public void reportStatus(String statusType, String data) {
        mHWStatus.put(statusType, data);
        if (mListener != null) {
            mListener.onChange(statusType, data);
        }
    }

    public interface OnStatusChangeListener {
        void onChange(String statusType, String data);
    }
}
