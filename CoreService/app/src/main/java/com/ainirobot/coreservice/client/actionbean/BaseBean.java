package com.ainirobot.coreservice.client.actionbean;

import android.text.TextUtils;

/**
 * Created by orion on 2018/4/11.
 */

public class BaseBean {
    private int reqId;

    private String language;

    /**
     * 是否需要进行辅助定位
     */
    private boolean needAssistEstimate = true;

    public int getReqId() {
        return reqId;
    }

    public void setReqId(int reqId) {
        this.reqId = reqId;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public boolean isNeedAssistEstimate() {
        return needAssistEstimate;
    }

    public void setNeedAssistEstimate(boolean needAssistEstimate) {
        this.needAssistEstimate = needAssistEstimate;
    }
}
