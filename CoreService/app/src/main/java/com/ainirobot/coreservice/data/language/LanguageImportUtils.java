package com.ainirobot.coreservice.data.language;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.os.Environment;
import android.util.Log;

import com.ainirobot.coreservice.R;
import com.ainirobot.coreservice.client.actionbean.LanguageBean;
import com.ainirobot.coreservice.config.RobotConfig;
import com.ainirobot.coreservice.config.core.LanguageConfig;
import com.ainirobot.coreservice.utils.Utils;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by Orion on 2021/3/16.
 */
public class LanguageImportUtils {
    private static final String TAG = "LanguageImportUtils";

    private static LanguageConfig mLanguageConfig = null;

    /**
     * 初始化语言
     *
     * @param db
     */
    public static void initLanguage(SQLiteDatabase db, Context context) {
        Log.d(TAG, "initLanguage: ----start");
        LanguageConfig languageConfig = parseLanguageConfig(context);
        if (languageConfig == null
                || languageConfig.getLangInfos() == null
                || languageConfig.getLangInfos().size() <= 0) {
            Log.e(TAG, "initLanguage: Language config null!");
            return;
        }
        for (LanguageConfig.LangInfo config : languageConfig.getLangInfos()) {
            String langCode = config.getLangCode();
            String langName = config.getLangName();
            int isDefault = LanguageBean.UseState.NOT_DEFAULT;
            int support = LanguageBean.SupportState.LOCAL;
            Log.d(TAG, "initLanguage: langCode=" + langCode + " langName="
                    + langName + " isDefault=" + isDefault + " support=" + support);
            ContentValues contentValues = new ContentValues();
            contentValues.put(LanguageDataHelper.LanguageField.LANG_CODE, langCode);
            contentValues.put(LanguageDataHelper.LanguageField.LANG_NAME, langName);
            contentValues.put(LanguageDataHelper.LanguageField.IS_DEFAULT, isDefault);
            contentValues.put(LanguageDataHelper.LanguageField.SUPPORT, support);
            db.insert(LanguageDataHelper.LanguageField.TABLE_NAME, null,
                    contentValues);
        }
        Log.d(TAG, "initLanguage: ----end");
    }

    /**
     * 初始化版本号
     *
     * @param db
     */
    public static void initLanguageVersion(SQLiteDatabase db, Context context) {
        Log.d(TAG, "initLanguageVersion: ----start");
        LanguageConfig languageConfig = parseLanguageConfig(context);
        if (languageConfig == null) {
            Log.e(TAG, "initLanguageVersion: Language config null!");
            return;
        }
        ContentValues contentValues = new ContentValues();
        contentValues.put(LanguageVersionHelper.VersionFiled.VERSION, languageConfig.getVersion());
        db.insert(LanguageVersionHelper.VersionFiled.TABLE_NAME, null,
                contentValues);
        Log.d(TAG, "initLanguageVersion: ----end");
    }

    /**
     * 检查更新语言&版本号
     *
     * @param db
     * @param context
     */
    public static void updateLanguageConfig(SQLiteDatabase db, Context context) {
        Log.d(TAG, "updateLanguageConfig: ----start");
        int configLangVersion = 0;
        int localLangVersion = 0;
        LanguageConfig languageConfig = parseLanguageConfig(context);
        if (languageConfig != null) {
            configLangVersion = Integer.valueOf(languageConfig.getVersion());
        }
        if (isTableExist(db, LanguageVersionHelper.VersionFiled.TABLE_NAME)) {
            Cursor cursor = db.query(LanguageVersionHelper.VersionFiled.TABLE_NAME,
                    null, null, null,
                    null, null, null);
            if (cursor != null && cursor.moveToNext()) {
                localLangVersion = cursor.getInt(cursor.getColumnIndex(LanguageVersionHelper.VersionFiled.VERSION));
            }
        }
        Log.d(TAG, "checkLanguageConfigVersion: configLangVersion=" + configLangVersion +
                " localLangVersion=" + localLangVersion);
        if (configLangVersion > localLangVersion) {
            doRealVersionUpdate(db, configLangVersion);
            doRealLanguageUpdate(db, languageConfig.getLangInfos());
        }
        Log.d(TAG, "updateLanguageConfig: ----end");
    }

    private static void doRealVersionUpdate(SQLiteDatabase db, int version) {
        Log.d(TAG, "doRealVersionUpdate: version=" + version);
        ContentValues values = new ContentValues();
        values.put(LanguageVersionHelper.VersionFiled.VERSION, version);
        db.delete(LanguageVersionHelper.VersionFiled.TABLE_NAME, null, null);
        db.insert(LanguageVersionHelper.VersionFiled.TABLE_NAME, null, values);
    }

    private static void doRealLanguageUpdate(SQLiteDatabase db, List<LanguageConfig.LangInfo> list) {
        Log.d(TAG, "doRealLanguageUpdate:");
        List<LanguageConfig.LangInfo> newConfig = list;
        List<LanguageBean> all = getAllLanguageList(db);
        List<LanguageBean> server = new ArrayList<>();
        //delete all
        db.delete(LanguageDataHelper.LanguageField.TABLE_NAME, null, null);
        for (LanguageBean bean : all) {
            if (bean.isServerSupport()) {
                server.add(bean);
            }
        }
        //insert local support language
        for (LanguageConfig.LangInfo newB : newConfig) {
            LanguageBean languageBean = new LanguageBean();
            languageBean.setLangCode(newB.getLangCode());
            languageBean.setLangName(newB.getLangName());
            languageBean.setIsDefault(LanguageBean.UseState.NOT_DEFAULT);
            languageBean.setSupport(LanguageBean.SupportState.LOCAL);
            insertLanguage(db, languageBean);
        }
        //insert server support language
        for (LanguageBean serverB : server) {
            boolean inLocal = false;
            for (LanguageConfig.LangInfo info : newConfig) {
                if (info.getLangCode().equals(serverB.getLangCode())) {
                    inLocal = true;
                    serverB.setLangCode(info.getLangCode());
                    serverB.setLangName(info.getLangName());
                    continue;
                }
            }
            if (inLocal) {
                serverB.setSupport(LanguageBean.SupportState.LOCAL_AND_SERVER);
                updateLanguage(db, serverB);
            } else {
                serverB.setSupport(LanguageBean.SupportState.SERVER);
                insertLanguage(db, serverB);
            }
        }
    }

    private static void updateLanguage(SQLiteDatabase db, LanguageBean bean) {
        ContentValues values = new ContentValues();
        values.put(LanguageDataHelper.LanguageField.LANG_CODE, bean.getLangCode());
        values.put(LanguageDataHelper.LanguageField.LANG_NAME, bean.getLangName());
        values.put(LanguageDataHelper.LanguageField.IS_DEFAULT, bean.getIsDefault());
        values.put(LanguageDataHelper.LanguageField.SUPPORT, bean.getSupport());
        db.update(LanguageDataHelper.LanguageField.TABLE_NAME, values,
                LanguageDataHelper.LanguageField.LANG_CODE + "=?",
                new String[]{bean.getLangCode()});
    }

    private static void insertLanguage(SQLiteDatabase db, LanguageBean bean) {
        ContentValues values = new ContentValues();
        values.put(LanguageDataHelper.LanguageField.LANG_CODE, bean.getLangCode());
        values.put(LanguageDataHelper.LanguageField.LANG_NAME, bean.getLangName());
        values.put(LanguageDataHelper.LanguageField.IS_DEFAULT, bean.getIsDefault());
        values.put(LanguageDataHelper.LanguageField.SUPPORT, bean.getSupport());
        db.insert(LanguageDataHelper.LanguageField.TABLE_NAME, null, values);
    }

    private static List<LanguageBean> getAllLanguageList(SQLiteDatabase db) {
        List<LanguageBean> langInfos = new ArrayList<>();
        Cursor cursor = db.query(LanguageDataHelper.LanguageField.TABLE_NAME, null,
                null, null, null, null, null);
        if (cursor == null) {
            return langInfos;
        }
        while (cursor.moveToNext()) {
            LanguageBean info = new LanguageBean();
            info.setLangCode(cursor.getString(cursor.getColumnIndex(LanguageDataHelper.LanguageField.LANG_CODE)));
            info.setLangName(cursor.getString(cursor.getColumnIndex(LanguageDataHelper.LanguageField.LANG_NAME)));
            info.setIsDefault(cursor.getInt(cursor.getColumnIndex(LanguageDataHelper.LanguageField.IS_DEFAULT)));
            info.setSupport(cursor.getInt(cursor.getColumnIndex(LanguageDataHelper.LanguageField.SUPPORT)));
            langInfos.add(info);
        }
        cursor.close();
        return langInfos;
    }

    private static LanguageConfig parseLanguageConfig(Context context) {
        if (mLanguageConfig != null) {
            return mLanguageConfig;
        }
        String configJson = Utils.loadConfig(context,
                Environment.getExternalStorageDirectory() + File.separator + "config.json",
                R.raw.config);
        Log.d(TAG, "getLanguageConfig: configJson: " + configJson);
        Gson gson = new Gson();
        RobotConfig robotConfig = gson.fromJson(configJson, RobotConfig.class);
        JsonObject service = robotConfig.getService();
        if (service == null) {
            Log.e(TAG, "getLanguageConfig: Error-service-null");
            return null;
        }
        JsonObject publicConfig = service.getAsJsonObject("公共配置");
        if (publicConfig == null) {
            Log.e(TAG, "getLanguageConfig: Error-publicConfig-null");
            return null;
        }
        JsonObject langConfig = publicConfig.getAsJsonObject("系统语言");
        if (langConfig == null) {
            Log.e(TAG, "getLanguageConfig: Error-langConfig-null");
            return null;
        }
        String jsonStr = new Gson().toJson(langConfig);
        Log.i(TAG, "getLanguageConfig: jsonStr=" + jsonStr);
        mLanguageConfig = new Gson().fromJson(jsonStr, LanguageConfig.class);
        Log.i(TAG, "getLanguageConfig: mLanguageConfig=" + mLanguageConfig.toString());
        return mLanguageConfig;
    }

    private static boolean isTableExist(SQLiteDatabase db, String tableName) {
        boolean result = false;
        if (tableName == null) {
            return false;
        }
        Cursor cursor = null;
        try {
            String sql = "select count(*) as c from Sqlite_master  where type ='table' and name ='" + tableName.trim() + "' ";
            cursor = db.rawQuery(sql, null);
            if (cursor.moveToNext()) {
                int count = cursor.getInt(0);
                if (count > 0) {
                    result = true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

}
