package com.ainirobot.coreservice.action.advnavi.state;

import android.util.Log;

import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.MultiRobotStatus;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.utils.DelayTask;

import java.util.List;

/**
 * Data: 2022/9/28 14:53
 * Author: wanglijing
 * <p>
 * NAVI_POINT_PRIOR_STAR
 * 点位优先级星形补位
 * 确认目的地可达后，状态结束；只上报状态，不启动导航
 */
public class NaviStarByPointStrategy extends BaseMultiRobotNaviState {

    /**
     * 当前位置名称
     */
    private String mCurrentPlace;


    private volatile MultipleStatus multipleStatus;

    private enum MultipleStatus {
        IDLE,
        START,
        WAIT_HANDLE_STATUS,   //处理状态
        HANDLE_STATUS_FINISH, //完成处理状态
        WAIT_CONFORM,
        WAIT_CONFORM_FINISH
    }

    NaviStarByPointStrategy(String name) {
        super(name);
    }

    @Override
    protected void preAction(StateData data, IStateListener listener) {
        super.preAction(data, listener);
        updateMultiStatus(MultipleStatus.IDLE);
        addDestinationToStandbyList();
        mCurrentPlace = getCurrentPlaceName();
    }

    @Override
    protected void doAction() {
        super.doAction();
        updateMultiStatus(MultipleStatus.START);
    }

    @Override
    protected void handleMultipleRobotStatus(List<MultiRobotStatus> robotStatusList) {
        switch (multipleStatus) {
            case START:
            case WAIT_CONFORM_FINISH:
            case HANDLE_STATUS_FINISH:
                handleDestinationStatus(robotStatusList);
                break;
        }
    }

    private void handleDestinationStatus(List<MultiRobotStatus> robotStatusList) {
        boolean isConformedStatus = multipleStatus == MultipleStatus.WAIT_CONFORM_FINISH;

        updateMultiStatus(MultipleStatus.WAIT_HANDLE_STATUS);
        DestinationState state = judgeDestinationIsAvailable(robotStatusList);
        Log.i(TAG, "handleDestinationStatus: " + state);
        switch (state) {
            case AVAILABLE:
                if (isConformedStatus) {
                    updateMultiStatus(MultipleStatus.IDLE);
                    //RESULT_NAVIGATION_DESTINATION_AVAILABLE
                    onResult(Definition.RESULT_DESTINATION_AVAILABLE);
                    return;
                }
                conformDestination();
                break;
            case NERA_DESTINATION:
                //确认在目标点范围内，结束任务
                updateMultiStatus(MultipleStatus.IDLE);
                //RESULT_NAVIGATION_IN_DESTINATION_RANGE
                onResult(Definition.RESULT_DESTINATION_IN_RANGE);
                break;
            case DESTINATION_INVALID:
                //目的地为空，上报异常
                Log.e(TAG, "handleDestinationStatus error !!!");
                updateMultiStatus(MultipleStatus.IDLE);
                onResult(Definition.ERROR_NO_AVAILABLE_DESTINATION);
                break;
            case OCCUPIED:
            case NAVI_IN_RANGE:
            default:
                updateMultiStatus(MultipleStatus.HANDLE_STATUS_FINISH);
                break;
        }
    }

    private void conformDestination() {
        //如果可以到达，延迟一定时间后确认是否真的可以到达，
        updateMultiStatus(MultipleStatus.WAIT_CONFORM);
        //根据点位优先级，确定延迟时间
        int posePriority = calculateHigherPriorityDeviceCnt();
        long delayTime = posePriority * Definition.SECOND + 500;
        Log.i(TAG, "delayTime = " + delayTime);
        delayConfirmDestination(delayTime);
    }

    /**
     * 根据当前所在点的优先级,获得延迟确认出餐口是否可用的时间
     * 当前所在点优先级越高，延迟确认的时间越短
     * （点位优先级越高，越先确认目的地是否可达）
     */
    private int calculateHigherPriorityDeviceCnt() {
        int posePriority = 0;
        for (int i = 0; i < mValidStandbyList.size(); i++) {
            Pose poseInfo = mValidStandbyList.get(i);
            if (poseInfo.getName().equals(mCurrentPlace)) {
                //mValidStandbyList中0点是备餐点i-1肯定>=0
                posePriority = i > 0 ? i - 1 : 0;
                Log.i(TAG, "currentDevice pose priority:" + poseInfo + ", index:" + i);
                break;
            } else {
                posePriority = 10;
                Log.d(TAG, "curPlace:" + mCurrentPlace + " != standbyPose:" + poseInfo.getName());
            }
        }
        return posePriority;
    }

    private void delayConfirmDestination(long delayTime) {
        DelayTask.cancel(DELAY_TAG);
        DelayTask.submit(DELAY_TAG, new Runnable() {
            @Override
            public void run() {
                if (multipleStatus == MultipleStatus.WAIT_CONFORM) {
                    updateMultiStatus(MultipleStatus.WAIT_CONFORM_FINISH);
                }
            }
        }, delayTime);
    }

    private void updateMultiStatus(MultipleStatus status) {
        Log.i(TAG, "updateMultiStatus: " + status);
        this.multipleStatus = status;
    }

}
