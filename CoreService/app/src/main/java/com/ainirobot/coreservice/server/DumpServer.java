/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.server;

import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.IDumpRegistry;
import com.ainirobot.coreservice.IHWService;
import com.ainirobot.coreservice.bean.Command;
import com.ainirobot.coreservice.client.exception.BinderExceptionHandler;
import com.ainirobot.coreservice.config.ServiceConfig;
import com.ainirobot.coreservice.core.CommandManager;
import com.ainirobot.coreservice.core.InternalDef;
import com.ainirobot.coreservice.core.external.ExternalService;
import com.ainirobot.coreservice.core.external.ExternalServiceManager;
import com.ainirobot.coreservice.core.status.StatusManager;
import com.ainirobot.coreservice.service.CoreService;
import com.ainirobot.robotlog.RobotLog;

import java.util.List;

public class DumpServer extends IDumpRegistry.Stub {
    private static final String TAG = "DumpServer";

    private CoreService mCore;
    private String packageName;
    private ExternalServiceManager mExternalManager;

    public DumpServer(CoreService core, String packageName) {
        this.mCore = core;
        this.packageName = packageName;
        this.mExternalManager = core.getExternalServiceManager();
    }

    @Override
    public void registerHWCallback(String serviceName, IHWService hwCallback) {
        try {
            ExternalService service = mExternalManager.getExternalService(serviceName);
            if (service == null) {
                service = new ExternalService(packageName, serviceName);
                mExternalManager.addExternalService(service);
            } else {
                if (service.isPreset()
                        && !service.getPackageName().equals(packageName)) {
                    Log.d(TAG, "Register external service failed : " + packageName + " is error");
                    return;
                }
            }

            ServiceConfig config = mExternalManager.getServiceConfig(serviceName);

            List<Command> supportCmds = hwCallback.mount(serviceName, config);
            CommandManager commandManager = mCore.getCommandManager();
            commandManager.addCommand(serviceName, supportCmds);
            service.addCommand(supportCmds);
            service.setHardware(hwCallback);
        } catch (Exception e) {
            BinderExceptionHandler.handle(e);
        }
    }

    @Override
    public void unregisterHWCallback(String serviceName) {
        ExternalService service = this.mExternalManager.getExternalService(serviceName);
        if (service == null || !TextUtils.equals(packageName, service.getPackageName())) {
            Log.d(TAG, "Unregister external service failed : " + serviceName + " is error");
            return;
        }

        this.mExternalManager.removeExternalService(service);
        Log.d(TAG, "Unregister external service : name = " + serviceName);
    }

    @Override
    public void sendAsyncResponse(String cmdType, int result, String message) {
        RobotLog.d(TAG, "Read message from HW : type=" + cmdType + " result=" + message);
        Handler handler = mCore.getResponseHandler();
        Message msg = handler.obtainMessage(InternalDef.MSG_CORE_HW_CMD_RESPONSE);
        Bundle bundle = new Bundle();
        bundle.putString(InternalDef.BUNDLE_TYPE, cmdType);
        bundle.putString(InternalDef.BUNDLE_RESPONSE, message);
        msg.setData(bundle);
        handler.sendMessage(msg);
    }

    @Override
    public void sendAsyncStatus(String cmdType, String status) {
        RobotLog.d(TAG, "Read message from HW : type=" + cmdType + " status=" + status);

        Handler handler = mCore.getResponseHandler();
        Message msg = handler.obtainMessage(InternalDef.MSG_CORE_HW_CMD_STATUS);
        Bundle bundle = new Bundle();
        bundle.putString(InternalDef.BUNDLE_TYPE, cmdType);
        bundle.putString(InternalDef.BUNDLE_RESPONSE, status);
        msg.setData(bundle);
        handler.sendMessage(msg);
    }

    @Override
    public void sendStatusReport(String serviceName, String type, String params) {
        //TODO: no use of subType currently, set it when necessary.
//        Log.d(TAG, "Send status : " + type + "   " + params);
        StatusManager statusManager = mCore.getStatusManager();
        statusManager.handleStatus(serviceName, type, params);
    }

    @Override
    public void sendExceptionReport(String serviceName, String type, String params) {
        Log.e(TAG, "sendExceptionReport : " + type + "   " + params);

        Handler handler = mCore.getRequestHandler();
        Message msg = handler.obtainMessage(InternalDef.MSG_REQUEST_HW_REPORT);
        Bundle bundle = new Bundle();
//        bundle.putInt(InternalDef.BUNDLE_INT, hwFunction);
        bundle.putString(InternalDef.BUNDLE_TYPE, type);
        bundle.putString(InternalDef.BUNDLE_RESPONSE, params);
        msg.setData(bundle);
        handler.sendMessage(msg);
    }

//    @Override
//    public ServiceConfig getConfig(String configName) {
//        return mExternalManager.getServiceConfig(configName);
//    }

}
