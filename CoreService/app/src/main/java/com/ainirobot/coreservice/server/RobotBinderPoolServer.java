/*
 *
 *   Copyright (C) 2017 OrionStar Technology Project
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 *
 */

package com.ainirobot.coreservice.server;

import android.os.IBinder;
import android.util.Log;

import com.ainirobot.coreservice.IRobotBinderPool;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.service.CoreService;

public class RobotBinderPoolServer extends IRobotBinderPool.Stub {

    private static final String TAG = "RobotBinderPoolServer";

    private CoreService mCore;

    public RobotBinderPoolServer(CoreService core) {
        mCore = core;
    }

    @Override
    public IBinder queryBinder(int binderCode, String param) {
        Log.d(TAG, "query binder:binderCode=" + binderCode + ",param=" + param);
        IBinder binder = null;
        switch (binderCode) {
            case Definition.BIND_ROBOT:
                binder = mCore.registerModule(param, false);
                break;
            case Definition.BIND_SYSTEM:
                binder = mCore.getSystemServer();
                break;
            case Definition.BIND_PERSON:
                binder = mCore.registerPersonServer(param);
                break;
            case Definition.BIND_SETTING:
                binder = mCore.getRobotSettingServer();
                break;
            case Definition.BIND_HW:
                binder = new ExternalServer(mCore, param);
                break;
            case Definition.BIND_ACCOUNT:
                binder = mCore.getAccountManager();
                break;
            case Definition.BIND_OTA:
                binder = mCore.registerOtaServer(param);
                break;
            case Definition.BIND_PERMISSION:
                binder = mCore.getPermissionManager();
                break;
            case Definition.BIND_SURFACE_SHARE:
                binder = mCore.getSurfaceShareServer();
                break;
            case Definition.BIND_SHARE_MEMORY:
                binder = mCore.getShareMemoryServer();
                break;
            case Definition.BIND_ROBOT_BACKGROUND:
                binder = mCore.registerModule(param, true);
            default:
                break;
        }
        return binder;
    }
}
