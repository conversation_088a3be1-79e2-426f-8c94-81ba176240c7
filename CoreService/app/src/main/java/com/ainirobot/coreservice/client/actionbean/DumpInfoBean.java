package com.ainirobot.coreservice.client.actionbean;

public class DumpInfoBean extends BaseBean {
    private int errorType;
    private String errorModule;
    private String description;
    private long timestamp;
    private String fileId;
    private long fileSize;


    public int getErrorType() {
        return errorType;
    }

    public void setErrorType(int errorType) {
        this.errorType = errorType;
    }

    public String getErrorModule() {
        return errorModule;
    }

    public void setErrorModule(String errorModule) {
        this.errorModule = errorModule;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public long getFileSize() {
        return fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }
}
