/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.client.account;

import com.ainirobot.coreservice.client.person.FaceBean;
import com.google.gson.Gson;

public class MemberBean {
    private UserBean userBean;
    private FaceBean faceBean;

    public UserBean getUserBean() {
        return userBean;
    }

    public void setUserBean(UserBean userBean) {
        this.userBean = userBean;
    }

    public FaceBean getFaceBean() {
        return faceBean;
    }

    public void setFaceBean(FaceBean faceBean) {
        this.faceBean = faceBean;
    }

    @Override
    public String toString() {
        return toGson();
    }

    private String toGson() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }
}
