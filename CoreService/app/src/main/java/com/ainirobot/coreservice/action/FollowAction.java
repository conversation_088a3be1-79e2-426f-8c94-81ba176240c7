/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.action;

import android.text.TextUtils;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.FollowBean;
import com.ainirobot.coreservice.client.actionbean.Person;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.utils.MessageParser;
import com.ainirobot.robotlog.RobotLog;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Timer;
import java.util.TimerTask;

public class FollowAction extends AbstractAction<FollowBean> {
    private static final String TAG = FollowAction.class.getSimpleName();

    private final int TIMEOUT_LOSE_FACE = 500;
    private final int TIMEOUT_WAITING = 1000;
    private final int TIMEOUT_SEARCH_FACE = 15 * 1000;
    private final long DETECT_TIMEOUT = 3 * 1000;  //  Face detection 2s timeout

    private IFollowPolicy[] mPolicy = new IFollowPolicy[Definition.FOLLOW_MAX_POLICY];
    private Timer mTimeoutTimer;
    private Person mPerson;
    private long mLostTimeOut;
    private long mDetectTimeout;
    private boolean mFollowing = false;

    interface IFollowPolicy {
        boolean comeHere();

        boolean stopComeHere();

        void stopFollowMe();

        boolean onCmdResponse(int cmdId, String cmdType, String params, LocalApi api);
    }

    private class BodyPolicy implements IFollowPolicy {
        private String mVelocityListener;

        @Override
        public boolean comeHere() {
            mFollowing = true;
            mApi.switchDetectAlgorithm(mReqId, LocalApi.DETECT_BODY);
            registerVelocityListener();
            return true;
        }

        @Override
        public boolean stopComeHere() {
            if (getStatus() == ActionManager.Status.RUN) {
                onStatusUpdate(Definition.STATUS_GUEST_STOP_FOLLOW, null);
                stopFollowMe();
            }
            return true;
        }

        @Override
        public void stopFollowMe() {
            RobotLog.e("BodyPolicy stop follow me");
            mReqId = INVALID_REQID;
            mApi.stopMove(mReqId);
            mApi.switchDetectAlgorithm(mReqId, LocalApi.DETECT_FACE);
            onStop();
            unregisterStatusListener(mVelocityListener);
            mVelocityListener = null;
        }

        @Override
        public boolean onCmdResponse(int cmdId, String cmdType, String params, LocalApi api) {
            switch (cmdType) {
                case Definition.CMD_HEAD_GET_ALL_PERSON_INFOS:
                    return handleGetFacesResult(params);

                case Definition.CMD_HEAD_GET_PERSON_INFO_BY_NAME:
                    return handleGetFacesByNameResult(params);

                case Definition.CMD_HEAD_GET_LAST_POSITION:
                    searchFace(params);
                    return true;

                /*case Definition.CMD_HEAD_STOP_SEND_PERSON_INFOS:
                    return handleStopFacesResult();*/

                case Definition.CMD_HEAD_IS_HEADER_CONNECTED:
                    return handleHeadStatusChange(params);

                default:
                    return true;
            }
        }

        private boolean handleGetFacesResult(String params) {
            mPerson = MessageParser.parseBodyInfo(mParam.getPersonName(), params);
            if (mPerson != null) {
                onStatusUpdate(Definition.STATUS_GUEST_APPEAR, null);
                mApi.setTrackTarget(mReqId, mPerson.getRegisterName(),
                        mPerson.getFaceInfo().getFaceId());
                mApi.motionArc(mReqId, mPerson.getFaceInfo().getDistance(), mPerson.getFaceInfo().getAngle(),
                        mPerson.getFaceInfo().getHeadSpeed(), mPerson.getFaceInfo().getLatency());
            }
            return true;
        }

        private void registerVelocityListener() {
            if (mVelocityListener != null) {
                return;
            }
            mVelocityListener = registerStatusListener(Definition.STATUS_SPEED, new com.ainirobot.coreservice.action.StatusListener() {
                @Override
                public void onStatusUpdate(String data) {
                    try {
                        JSONObject json = new JSONObject(data);
                        Double speedZ = json.getDouble(Definition.JSON_NAVI_ANGULAR_SPEED_Z);
                        Double speedX = json.getDouble(Definition.JSON_NAVI_LINEAR_SPEED_X);
                        mApi.reportNavigationStatus(Definition.MODULE_REQ_ID, speedX, speedZ);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            });
        }
    }

    private class FacePolicy implements IFollowPolicy {

        @Override
        public boolean comeHere() {
            startGetFaceTimeoutDetection();
            return true;
        }

        @Override
        public boolean stopComeHere() {
            if (mFollowing) {
                onStatusUpdate(Definition.STATUS_GUEST_STOP_FOLLOW, null);
                stopFollowMe();
            }
            return true;
        }

        @Override
        public void stopFollowMe() {
            RobotLog.e("FacePolicy stop follow me");
            mReqId = -1;
            if (mPerson != null) {
                mApi.stopSendPersonInfosByName();
            }
            onStop();
        }

        @Override
        public boolean onCmdResponse(int cmdId, String cmdType, String params, LocalApi api) {
            RobotLog.d("FacePolicy CmdType=" + cmdType + "   params=" + params);
            switch (cmdType) {
                case Definition.CMD_HEAD_GET_ALL_PERSON_INFOS:
                    return handleGetFacesResult(params);

                case Definition.CMD_HEAD_GET_PERSON_INFO_BY_NAME:
                    return handleGetFacesByNameResult(params);

                case Definition.CMD_HEAD_GET_LAST_POSITION:
                    onStatusUpdate(Definition.STATUS_GUEST_LOST, params);
                    //searchFace(params);
                    return true;

                /*case Definition.CMD_HEAD_STOP_SEND_PERSON_INFOS:
                    return handleStopFacesResult();*/

                case Definition.CMD_HEAD_IS_HEADER_CONNECTED:
                    return handleHeadStatusChange(params);

                default:
                    return true;
            }
        }

        private boolean handleGetFacesResult(String params) {
            if (mFollowing) {
                return false;
            }
            mPerson = MessageParser.parsePersonInfo(mParam.getPersonName(), params);
            if (mPerson != null) {
                if (mPerson.isLocalRegister()) {
                    cancelTimer();
                    mFollowing = true;
                    onStatusUpdate(Definition.STATUS_GUEST_APPEAR, null);
                    mApi.stopSendPersonInfos();
                    mApi.setTrackTarget(mReqId, mPerson.getRegisterName(),
                            mPerson.getFaceInfo().getFaceId());
                    mApi.getPersonInfoByName(mReqId, mPerson.getRegisterName(), LocalApi.CONTINUOUS);
                    startTrackTimeoutDetection();
                } else {
                    onError(Definition.ERROR_GUEST_STRANGE, null);
                    //stop(true);
                }
            } else {
                //stop();
            }
            return true;
        }

        private void startGetFaceTimeoutDetection() {
            cancelTimer();
            mTimeoutTimer = new Timer();
            mTimeoutTimer.schedule(new TimerTask() {
                @Override

                public void run() {
                    if (mPerson == null) {
                        //TODO: Deal with the situation where no one has been found
                    } else if (!mPerson.isLocalRegister()) {
                        //TODO: Deal with unregistered cases
                    }

                    onStatusUpdate(Definition.STATUS_GUEST_NOT_FOUND, null);
                    /*todo maybe need remove
                    if (mStatus != ActionManager.Status.RUN) {
                        mApi.getAllPersonInfos(mReqId, LocalApi.STOP);
                        stopFollowMe();
                    }*/
                }
            }, mDetectTimeout);
        }

    }

    private IFollowPolicy mCurrentPolicy;

    protected FollowAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_FOLLOW, resList);
        mPolicy[Definition.FOLLOW_BODY_POLICY] = new BodyPolicy();
        mPolicy[Definition.FOLLOW_FACE_POLICY] = new FacePolicy();
    }

    private boolean handleGetFacesByNameResult(String params) {
        if (!mFollowing) {
            return false;
        }

        Person person = MessageParser.parseOneFaceInfo(params);
        if (person != null && mPerson.getRegisterName().equals(person.getRegisterName())) {
            mApi.motionArc(mReqId, person.getFaceInfo().getDistance(), person.getFaceInfo().getAngle(),
                    person.getFaceInfo().getHeadSpeed(), person.getFaceInfo().getLatency());
            startTrackTimeoutDetection();
        }
        return true;
    }

    private void startTrackTimeoutDetection() {
        cancelTimer();
        mTimeoutTimer = new Timer();
        mTimeoutTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (mFollowing) {
                    mApi.motionArc(mReqId, 0, 0, 0, 0);
                    //waitingMoment();
                    mApi.getLastPosition(mReqId, mPerson.getRegisterName(),
                            mPerson.getFaceInfo().getFaceId());
                }
            }
        }, mLostTimeOut);
    }

    private void waitingMoment() {
        cancelTimer();
        mTimeoutTimer = new Timer();
        mTimeoutTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                mApi.getLastPosition(mReqId, mPerson.getRegisterName(), mPerson.getFaceInfo().getFaceId());
            }
        }, TIMEOUT_WAITING);
    }

    private boolean handleStopFacesResult() {
            /*todo ?
            if (mState == ModuleDef.MODULE_STATE_PAUSE) {
                cancelTimer();
                ModuleSendCmd.getInstance().finishModuleParser(mReqId, true);
                release(mReqId, RESULT_OK, null);
            }*/
        return true;
    }

    private boolean handleHeadStatusChange(String params) {
        if (params.equals(Definition.CMD_STATUS_OK)) {
            mApi.stopSendPersonInfos();
            mApi.stopTrack(mReqId);
        } else if (params.equals(Definition.CMD_STATUS_FAILED)) {
            //stop();
            onError(Definition.ERROR_HEAD, "Head disconnect");
        }
        return true;
    }

    private void searchFace(String params) {
        try {
            JSONObject json = new JSONObject(params);
            int angle = json.getInt("horizontal");
            int lastHeadAngle = json.getInt("headAngleX");
            if (angle == 0) {
                angle = lastHeadAngle;
            }
            if (Integer.signum(lastHeadAngle) != Integer.signum(angle)) {
                mApi.resetHead(mReqId);
            }

            mApi.motionArc(mReqId, -1, angle, 0, 0);
            startSearchTimeoutDetection();
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void startSearchTimeoutDetection() {
        cancelTimer();
        mTimeoutTimer = new Timer();
        mTimeoutTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (!mFollowing) {
                    return;
                }
                onStatusUpdate(Definition.STATUS_GUEST_LOST, null);
                mCurrentPolicy.stopFollowMe();
            }
        }, mLostTimeOut);
    }

    @Override
    protected boolean verifyParam(FollowBean parameter) {
        boolean result = false;
        if (parameter.getType() >= Definition.FOLLOW_FACE_POLICY &&
                parameter.getType() < Definition.FOLLOW_MAX_POLICY &&
                !TextUtils.isEmpty(parameter.getPersonName())) {
            result = true;
        }
        return result;
    }

    private void cancelTimer() {
        if (mTimeoutTimer != null) {
            mTimeoutTimer.cancel();
            mTimeoutTimer = null;
        }
    }

    private void onStop() {
        mFollowing = false;
        cancelTimer();
        mApi.resetHead(mReqId);
        mApi.stopSendPersonInfos();
        mApi.stopSendPersonInfosByName();
        mApi.stopTrack(mReqId);
    }

    @Override
    protected boolean startAction(FollowBean parameter, LocalApi api) {
        boolean result = false;
        mCurrentPolicy = mPolicy[parameter.getType()];
        mReqId = parameter.getReqId();
        mPerson = new Person();
        mPerson.setRegisterName(parameter.getPersonName());
        mLostTimeOut = parameter.getLostTimer() > 0 ? parameter.getLostTimer() : TIMEOUT_SEARCH_FACE;
        mDetectTimeout = parameter.getDetectTimeout() > 0 ?
                parameter.getDetectTimeout() : DETECT_TIMEOUT;

        if (mCurrentPolicy.comeHere()) {
            api.getAllPersonInfos(parameter.getReqId(), LocalApi.CONTINUOUS);
            result = true;
        }
        return result;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        mCurrentPolicy.stopComeHere();
        mCurrentPolicy = null;
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        if (mCurrentPolicy != null)
            mCurrentPolicy.onCmdResponse(cmdId, command, result, mApi);
        return true;
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return null;
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return null;
    }
}

