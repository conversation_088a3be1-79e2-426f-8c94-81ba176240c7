package com.ainirobot.coreservice.action.advnavi.state;

import android.util.Log;

import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.client.Definition;

import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.TimeUnit;

/**
 * 注册电梯状态
 * 注册成功即独占电梯
 * 1分钟重试一次，30分钟内未注册成功则超时退出
 * <p>
 * 优先级原则：
 * 1.谁在电梯口谁先用，在电梯口的机器注册电梯频率高
 * 2.谁占用了电梯谁先执行任务，占用电梯失败则退出递送任务返回待机点
 */
public class RegisterElevatorState extends BaseState {

    //电梯外等待超时
    private long WAIT_TIMEOUT_MINUTE;
    private Timer mTimer;
    private long startTime;
    //重试次数
    private int retryCount = 0;

    RegisterElevatorState(String name) {
        super(name);
    }

    @Override
    protected void preAction(StateData data, IStateListener listener) {
        super.preAction(data, listener);
    }

    @Override
    protected void doAction() {
        super.doAction();
        boolean isGoElevatorWaitPoint = mStateData.getIsGoElevatorWaitPoint();
        if (isGoElevatorWaitPoint) {
            WAIT_TIMEOUT_MINUTE = TimeUnit.MINUTES.toMillis(10);
        } else {
            WAIT_TIMEOUT_MINUTE = TimeUnit.MINUTES.toMillis(30);
        }
        Log.d(TAG, "doAction: register elevator isGoElevatorWaitPoint=" + isGoElevatorWaitPoint
                + " WAIT_TIMEOUT_MINUTE=" + WAIT_TIMEOUT_MINUTE);
        onStatusUpdate(Definition.STATUS_START_REGISTER_ELEVATOR, "start register elevator");
        retryCount = 0;
        registerElevatorCmd();
        startWaitTimer();//首次注册电梯后，开始超时等待
    }

    @Override
    protected void exit() {
        retryCount = 0;
        cancelWaitTimer();
        super.exit();
    }

    @Override
    protected BaseState getNextState() {
        Log.d(TAG, "getNextState: actionState=" + actionState);
        switch (actionState) {
            case ACTION_REGISTER_ELEVATOR_SUC:
                if (mStateData.getIsGoElevatorWaitPoint()) {
                    return StateEnum.GO_ELEVATOR_GATE.getState();
                } else {
                    return StateEnum.WAIT_OUTSIDE_ELEVATOR.getState();
                }
        }
        return super.getNextState();
    }

    @Override
    protected boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_CONTROL_REGISTER_ELEVATOR:
                handleRegisterElevatorResult(result, extraData);
                return true;
        }
        return super.cmdResponse(cmdId, command, result, extraData);
    }

    @Override
    protected boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        return super.cmdStatusUpdate(cmdId, command, status, extraData);
    }

    /**
     * 调用注册电梯接口
     * 这里的call一定是在电梯外，所以只需要提前注册电梯
     */
    private void registerElevatorCmd() {
        Log.d(TAG, "registerElevatorCmd: retryCount=" + retryCount);
        retryCount++;
        super.registerElevator();//1分钟重试一次
    }

    /**
     * 处理注册电梯结果
     */
    private void handleRegisterElevatorResult(String result, String extraData) {
        Log.d(TAG, "handleRegisterElevatorResult " + result + " extraData:" + extraData);
        if (Definition.SUCCEED.equals(result)) {
            Log.d(TAG, "register elevator success");
            updateAction(Definition.ElevatorAction.ACTION_REGISTER_ELEVATOR_SUC);
            onStatusUpdate(Definition.STATUS_REGISTER_ELEVATOR_SUC, "register elevator success");
            onSuccess();
        } else {
            onStatusUpdate(Definition.STATUS_REGISTER_ELEVATOR_FAIL, "register elevator fail, retry times " + retryCount);
        }
    }

    // ---------    超时    ----------- //

    /**
     * 注册电梯超时处理：
     * 每隔一分钟重新注册一次，30分钟超时
     */
    private void startWaitTimer() {
        cancelWaitTimer();
        startTime = System.currentTimeMillis();
        mTimer = new Timer();
        mTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (!isAlive()) {
                    cancelWaitTimer();
                    return;
                }

                //注册电梯超时处理
                if (System.currentTimeMillis() - startTime > WAIT_TIMEOUT_MINUTE) {
                    Log.d(TAG, "wait time out! ");
                    waitCallElevatorTimeOut();
                    cancelWaitTimer();
                } else {
                    registerElevatorCmd();
                }
            }
        }, 30 * 1000, 30 * 1000);
//        }, 20 * 1000, 20 * 1000); //测试
    }

    private void cancelWaitTimer() {
        if (mTimer != null) {
            mTimer.cancel();
        }
    }

    /**
     * 超时处理
     */
    private void waitCallElevatorTimeOut() {
        onResult(Definition.ERROR_REGISTER_ELEVATOR_FAILED, Definition.PARAMS_TIMEOUT, "register elevator time out");
    }

}
