/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.core.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.core.system.SystemManager;
import com.ainirobot.coreservice.service.CoreService;

public class SystemBroadcastReceiver extends BroadcastReceiver {
    private static final String TAG = SystemBroadcastReceiver.class.getSimpleName();

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        Log.d(TAG, "intent is " + action);

        if (Intent.ACTION_SHUTDOWN.equalsIgnoreCase(action)
                || Intent.ACTION_REBOOT.equalsIgnoreCase(action)) {
            SystemManager systemManager = CoreService.mCore.getSystemManager();
            systemManager.onSystemStatusUpdate(Definition.SYSTEM_SHUTDOWN, null);
        } else if (Definition.ACTION_IMPORT_MAP.equalsIgnoreCase(action)) {
            String mapName = intent.getStringExtra(Definition.JSON_MAP_NAME);
            SystemManager systemManager = CoreService.mCore.getSystemManager();
            systemManager.onSystemStatusUpdate(Definition.REMOTE_IMPORT_MAP_BEGIN, mapName);
        }
    }
}
