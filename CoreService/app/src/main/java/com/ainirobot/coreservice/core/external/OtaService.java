/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.core.external;

import android.os.Bundle;
import android.os.RemoteException;

import com.ainirobot.coreservice.IOtaCallback;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotOS;

/**
 * OTA
 */
public class OtaService extends ExternalService {

    private static final String PACKAGE_NAME = "com.ainirobot.ota";

    private IOtaCallback mCallback;

    public OtaService() {
        super(PACKAGE_NAME, RobotOS.OTA_SERVICE);
    }

    public void setCallback(IOtaCallback callback) {
        this.mCallback = callback;
    }

    @Override
    public boolean isKeepAlive() {
        return false;
    }

    public boolean startOtaUpgrade(boolean needDownload) {
        try {
            if (mCallback != null) {
                return mCallback.onOtaUpgradeStart(needDownload);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return false;
    }

    public boolean startOtaRollback() {
        try {
            if (mCallback != null) {
                return mCallback.onOtaRollbackStart();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return false;
    }

    public String getOtaUpgradeDescription() {
        try {
            if (mCallback != null) {
                return mCallback.onOtaGetDescription();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return "{}";
    }

    public boolean cancelOtaUpgradeDownload() {
        try {
            if (mCallback != null) {
                return mCallback.onOtaCancelDownload();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return false;
    }

    public boolean installPatch(Bundle bundle) {
        try {
            if (mCallback != null) {
                mCallback.installPatch(bundle);
                return true;
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return false;
    }

    public void interrupted(String reason) {
        try {
            if (mCallback != null) {
                mCallback.onOtaInterrupted(reason);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

}
