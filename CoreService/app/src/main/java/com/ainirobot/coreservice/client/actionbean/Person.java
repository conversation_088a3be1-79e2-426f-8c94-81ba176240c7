/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.client.actionbean;

import android.text.TextUtils;
import android.util.Log;

import com.google.gson.Gson;

public class Person {

    public static final String MARK_REMOTE = "R";
    public static final String MARK_LOCAL = "L";
    public static final String MARK_PLUS = "-";
    public static final int REGISTER_REMOTE = 1;
    public static final int REGISTER_LOCAL_OFFICIAL = 2;
    public static final int REGISTER_LOCAL_TEMPORARY = 3;
    public static final int ROLE_GUEST = 1;
    public static final int ROLE_STAFF = 2;
    public static final int ROLE_ADMIN = 3;

    private int personId;
    private String remoteId;
    private int role = ROLE_GUEST;
    private int registerType = REGISTER_LOCAL_TEMPORARY;
    private long registerTime;
    private int spec;
    private String registerName;
    private String name;
    private FaceInfo faceInfo;
    private BodyInfo bodyInfo;
    private GuestInfo guestInfo;

    public class FaceInfo {
        int faceId;
        public int associateId;
        public int angle;
        public float distance;
        public int headSpeed; // head speed;
        public int latency;
        public int pictruePath;
        public int facewidth;
        public int faceheight;
        public double faceAngleX;
        public double faceAngleY;
        public double angleInView;

        public int getFaceWidth() {
            return facewidth;
        }

        public int getFaceHeight() {
            return faceheight;
        }

        public double getFaceAngleX() {
            return faceAngleX;
        }

        public double getFaceAngleY() {
            return faceAngleY;
        }

        public int getNoseHorizontal() {
            return noseHorizontal;
        }

        public int getNoseVertical() {
            return noseVertical;
        }

        public int noseHorizontal;

        public void setFaceWidth(int faceWidth) {
            this.faceheight = faceWidth;
        }

        public void setFaceHeight(int faceHeight) {
            this.faceheight = faceHeight;
        }

        public void setFaceAngleX(double faceAngleX) {
            this.faceAngleX = faceAngleX;
        }

        public void setFaceAngleY(double faceAngleY) {
            this.faceAngleY = faceAngleY;
        }

        public void setNoseHorizontal(int noseHorizontal) {
            this.noseHorizontal = noseHorizontal;
        }

        public void setNoseVertical(int noseVertical) {
            this.noseVertical = noseVertical;
        }

        public int noseVertical;

        public int getFaceId() {
            return faceId;
        }

        public void setFaceId(int faceId) {
            this.faceId = faceId;
        }

        public int getAssociateId() {
            return associateId;
        }

        public void setAssociateId(int associateId) {
            this.associateId = associateId;
        }

        public int getAngle() {
            return angle;
        }

        public void setAngle(int angle) {
            this.angle = angle;
        }

        public float getDistance() {
            return distance;
        }

        public void setDistance(float distance) {
            this.distance = distance;
        }

        public int getHeadSpeed() {
            return headSpeed;
        }

        public void setHeadSpeed(int headSpeed) {
            this.headSpeed = headSpeed;
        }

        public int getLatency() {
            return latency;
        }

        public void setLatency(int latency) {
            this.latency = latency;
        }

        public int getPictruePath() {
            return pictruePath;
        }

        public void setPictruePath(int pictruePath) {
            this.pictruePath = pictruePath;
        }

        public double getAngleInView() {
            return angleInView;
        }

        public void setAngleInView(double angleInView) {
            this.angleInView = angleInView;
        }
    }

    public class BodyInfo {

    }

    public class GuestInfo {
        String guestName;
        String location;
        private long bookTime;
        private long invitationTime;
        private long leaveTime;

        public long getBookTime() {
            return bookTime;
        }

        public void setBookTime(long bookTime) {
            this.bookTime = bookTime;
        }

        public long getInvitationTime() {
            return invitationTime;
        }

        public void setInvitationTime(long invitationTime) {
            this.invitationTime = invitationTime;
        }

        public long getLeaveTime() {
            return leaveTime;
        }

        public void setLeaveTime(long leaveTime) {
            this.leaveTime = leaveTime;
        }

        public String getLocation() {
            return location;
        }

        public void setLocation(String location) {
            this.location = location;
        }

        public String getGuestName() {
            return guestName;
        }

        public void setGuestName(String name) {
            this.guestName = name;
        }

        public String toLeadingString() {
            return "带" + name + "去" + location;
        }
    }

    public Person() {
        faceInfo = new FaceInfo();
        bodyInfo = new BodyInfo();
        guestInfo = new GuestInfo();
    }

    public FaceInfo getFaceInfo() {
        return faceInfo;
    }

    public BodyInfo getBodyInfo() {
        return bodyInfo;
    }

    public GuestInfo getGuestInfo() {
        return guestInfo;
    }

    public String getRemoteId() {
        return remoteId;
    }

    public void setRemoteId(String remoteId) {
        this.remoteId = remoteId;
    }

    public int getRole() {
        return role;
    }

    public void setRole(int role) {
        this.role = role;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getRegisterType() {
        return registerType;
    }

    public void setRegisterType(int registerType) {
        this.registerType = registerType;
    }

    public int getPersonId() {
        return personId;
    }

    public void setPersonId(int personId) {
        this.personId = personId;
    }

    public long getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(long registerTime) {
        this.registerTime = registerTime;
    }

    public boolean isLocalRegister() {
        return !TextUtils.isEmpty(name);
    }

    public String getRegisterName() {
        return registerName;
    }

    public void setRegisterName(String registerName) {
        this.registerName = registerName;
    }

    public int getSpec() {
        return spec;
    }

    public void setSpec(int spec) {
        this.spec = spec;
    }

    public String generateRegisterName() {
        String nameInfo = null;
        personDataCheck();
        if (registerType == REGISTER_REMOTE) {
            nameInfo = MARK_REMOTE + MARK_PLUS + remoteId + MARK_PLUS + getName();
        } else if (registerType == REGISTER_LOCAL_OFFICIAL || registerType ==
                REGISTER_LOCAL_TEMPORARY) {
            nameInfo = MARK_LOCAL + MARK_PLUS + getPersonId() + MARK_PLUS + getName();
        }
        return nameInfo;
    }

    public void personDataCheck() {
        if (registerType == Person.REGISTER_REMOTE) {
            if (TextUtils.isEmpty(remoteId)) {
                throw new RuntimeException("person data error:register type is remote, remote"
                        + " id can not be null") {
                };
            }
        } else if (registerType == Person.REGISTER_LOCAL_OFFICIAL || registerType == Person
                .REGISTER_LOCAL_TEMPORARY) {
            Log.d("Person", "insertPerson remoteId: " + remoteId + ", personId: " + personId);
          /*  if ((!TextUtils.isEmpty(remoteId)) || personId <= 0) {
                throw new RuntimeException("person data error:register type is local, remote"
                        + " id should be null");
            }*/
        }
    }

    public String toGson() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }
}
