package com.ainirobot.coreservice.client.actionbean;

public class BatteryBean {

    private int level = 50;
    private int bmsTemp = 20;
    private boolean isCharging = false;
    private boolean isLow = false;

    public void setLevel(int level) {
        this.level = level;
    }

    public int getLevel() {
        return level;
    }

    public int getBmsTemp() {
        return bmsTemp;
    }

    public void setBmsTemp(int bmsTemp) {
        this.bmsTemp = bmsTemp;
    }

    public void setCharging(boolean charging) {
        isCharging = charging;
    }

    public boolean isCharging() {
        return isCharging;
    }

    public void setLow(boolean low) {
        isLow = low;
    }

    public boolean isLow() {
        return isLow;
    }

    @Override
    public String toString() {
        return "BatteryBean{" +
                "level=" + level +
                ", bmsTemp=" + bmsTemp +
                ", isCharging=" + isCharging +
                ", isLow=" + isLow +
                '}';
    }
}
