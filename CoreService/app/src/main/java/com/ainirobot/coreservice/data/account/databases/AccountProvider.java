/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.data.account.databases;

import android.content.ContentValues;
import android.content.Context;
import android.database.sqlite.SQLiteOpenHelper;
import android.net.Uri;
import android.support.annotation.NonNull;
import android.util.Log;

import com.ainirobot.coreservice.data.provider.BaseProvider;
import com.ainirobot.coreservice.data.provider.SQLiteTable;

import java.util.List;


public class AccountProvider extends BaseProvider {

    private static final String TAG = AccountProvider.class.getSimpleName();

    @Override
    public String getType(@NonNull Uri uri) {
        return "vnd.android.cursor.item/" + uri.getLastPathSegment();
    }

    @Override
    public String matchTable(Uri uri) {
        return uri.getLastPathSegment();
    }

    @Override
    public boolean onCreate() {
        Log.i(TAG, "on create");
        mDBHelper = new AccountOpenHelper(getContext());
        return true;
    }

    @Override
    public List<SQLiteTable> getTables() {
        return null;
    }

    @Override
    public SQLiteOpenHelper getDBHelper() {
        return new AccountOpenHelper(getContext());
    }

    @Override
    public int update(@NonNull Uri uri, ContentValues values, String selection, String[] selectionArgs) {
        int count = super.update(uri, values, selection, selectionArgs);
        Context context = getContext();
        Log.d(TAG, "update count: " + count);
        if (context != null) {
            context.getContentResolver().notifyChange(uri, null);
        }
        return count;
    }
}