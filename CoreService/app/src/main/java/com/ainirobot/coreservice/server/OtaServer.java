
/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.server;

import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.IOtaCallback;
import com.ainirobot.coreservice.IOtaRegistry;
import com.ainirobot.coreservice.IStatusListener;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.config.ServiceConfig;
import com.ainirobot.coreservice.core.InternalDef;
import com.ainirobot.coreservice.core.LocalApi.OnCmdResponse;
import com.ainirobot.coreservice.core.RequestManager;
import com.ainirobot.coreservice.core.RequestManager.ReqAction;
import com.ainirobot.coreservice.core.status.RemoteSubscriber;
import com.ainirobot.coreservice.core.status.StatusManager;
import com.ainirobot.coreservice.service.CoreService;

import java.util.ArrayList;
import java.util.List;

public class OtaServer extends IOtaRegistry.Stub {

    private final String TAG = "OtaServer";

    private CoreService mCore;
    private IOtaCallback mOta;
    private String packageName;

    public OtaServer(CoreService coreService) {
        this.mCore = coreService;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    @Override
    public void registerCallback(IOtaCallback callback) throws RemoteException {
        Log.d(TAG, "Ota server register callback : " + callback);
        mOta = callback;
        this.mCore.getExternalServiceManager().getOtaService().setCallback(callback);
    }

    @Override
    public String registerStatusListener(String type, IStatusListener listener)
            throws RemoteException {
        StatusManager statusManager = mCore.getStatusManager();
        RemoteSubscriber subscriber = new RemoteSubscriber(listener);
        return statusManager.registerStatusListener(type, subscriber);
    }

    @Override
    public boolean unregisterStatusListener(String id) throws RemoteException {
        StatusManager statusManager = mCore.getStatusManager();
        return statusManager.unregisterStatusListener(id);
    }

    @Override
    public int sendOtaRequest(String type, String params) throws RemoteException {
        RequestManager requestManager = mCore.getRequestManager();
        ReqAction req = requestManager.addReqAction(type, params);
        req.reqFunction = InternalDef.INPUT_FUNCTION_OTA;

        Handler requestHandler = mCore.getRequestHandler();
        Message msg = requestHandler.obtainMessage(InternalDef.MSG_REQUEST_WITH_TYPE);
        Bundle bundle = new Bundle();
        bundle.putInt(InternalDef.BUNDLE_REQUEST_ID, req.reqId);
        msg.setData(bundle);
        requestHandler.sendMessage(msg);

        return req.reqId;
    }

    @Override
    public int sendOtaCommand(String cmdType, String params, boolean isContinue) {
        int reqId = getReqId();
        CommandListener listener = new CommandListener(mOta);
        int cmdId = mCore.getCoreStateMachine().addCommand(reqId, cmdType, params, isContinue, listener);

        if (cmdId < 0) {
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }

        Handler coreHandler = mCore.getCoreHandler();
        Message msg = coreHandler.obtainMessage(InternalDef.MSG_CORE_ADD_COMMAND_ACTION);
        coreHandler.sendMessage(msg);
        return cmdId;
    }

    @Override
    public void updateProgress(String params) throws RemoteException {
        StatusManager statusManager = mCore.getStatusManager();
        statusManager.handleStatus(RobotOS.OTA_SERVICE, Definition.STATUS_OTA_PROGRESS, params);
    }

    private int getReqId() {
        RequestManager requestManager = mCore.getRequestManager();
        RequestManager.ReqAction req = requestManager.getReqAction(Definition.OTA_REQ_ID);
        if (req == null) {
            req = requestManager.addReqAction(Definition.OTA_REQ_ID, "OTA", null);
            req.reqFunction = InternalDef.INPUT_FUNCTION_OTA;
        }
        return req.reqId;
    }

    @Override
    public List<ServiceConfig> getConfig() {
        final ServiceConfig config = mCore.getExternalServiceManager().getServiceConfig(RobotOS.OTA_SERVICE);
        return new ArrayList<ServiceConfig>() {{
            add(config);
        }};
    }

    private class CommandListener implements OnCmdResponse {

        private IOtaCallback mCallback;

        public CommandListener(IOtaCallback otaCallback) {
            mCallback = otaCallback;
        }

        @Override
        public void onCmdResponse(int cmdId, String cmdType, String cmdResponse, String extraData) {
            try {
                if (mCallback == null) {
                    Log.e(TAG, "Call back is null : " + cmdType + "   " + cmdResponse);
                    return;
                }

                mCallback.onCmdResponse(cmdId, cmdType, cmdResponse);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onCmdStatusUpdate(int cmdId, String command, String status, String extraData) {

        }
    }
}
