package com.ainirobot.coreservice.config.core;

import android.util.Log;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import java.util.Map;

public class BatteryConfig extends BaseConfig {

    private static final String TAG = BatteryConfig.class.getSimpleName();

    private int lowBatteryValue;
    private int otaLowBatteryValue;

    public BatteryConfig(JsonObject jsonObject) {
        super(jsonObject, true);
    }

    @Override
    public void parseConfig(JsonObject config) {
        lowBatteryValue = 10;
        otaLowBatteryValue = 50;
        if (config == null) {
            Log.d(TAG, "parseConfig config null");
            return;
        }
        for (Map.Entry<String, JsonElement> entry : config.entrySet()) {
            String enKey = ConfigDictionary.parseBatteryConfig(entry.getKey());
            switch (enKey) {
                case "lowBatteryValue":
                    lowBatteryValue = entry.getValue().getAsInt();
                    break;
                case "otaLowBatteryValue":
                    otaLowBatteryValue = entry.getValue().getAsInt();
                    break;
                default:
                    break;
            }
        }
        Log.d(TAG, toString());
    }

    public int getLowBatteryValue() {
        return lowBatteryValue;
    }

    public int getOtaLowBatteryValue() {
        return otaLowBatteryValue;
    }

    @Override
    public String toString() {
        return "BatteryConfig{"
                + "isEnable='"
                + isEnable
                + '\''
                + ", lowBatteryValue="
                + lowBatteryValue
                + '\''
                + ", otaLowBatteryValue="
                + otaLowBatteryValue
                + '}';
    }
}
