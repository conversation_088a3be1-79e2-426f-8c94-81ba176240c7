package com.ainirobot.coreservice.bi.data;

import com.google.gson.annotations.SerializedName;

public class BiData {

    public static BiData biData = new BiData();

    private String wakeUpId = "";

    private String currentModule = "";

    private String waitModule = "";

    private NlpData nlpData;

    private BiData() {
    }

    public static BiData getInstance() {
        return biData;
    }

    public String getWakeUpId() {
        return wakeUpId;
    }

    public void setWakeUpId(String wakeUpId) {
        if (wakeUpId == null) {
            wakeUpId = "";
        }
        this.wakeUpId = wakeUpId;
    }

    public String getCurrentModule() {
        return currentModule;
    }

    public void setCurrentModule(String currentModule) {
        if (currentModule == null) {
            currentModule = "";
        }
        this.currentModule = currentModule;
    }

    public String getWaitModule() {
        return waitModule;
    }

    public void setWaitModule(String waitModule) {
        if (waitModule == null) {
            waitModule = "";
        }
        this.waitModule = waitModule;
    }

    public void setNlpData(NlpData data) {
        this.nlpData = data;
    }

    public String getNlpAppId() {
        return this.nlpData == null ? "" : this.nlpData.getAppId();
    }

    public String getNlpAppPath() {
        return this.nlpData == null ? "" : this.nlpData.getAppPath();
    }

    public String getNlpAppVersion() {
        return this.nlpData == null ? "" : this.nlpData.getAppVersion();
    }

    public static class NlpData {
        @SerializedName("app_id")
        private String appId = "";
        @SerializedName("path")
        private String appPath = "";
        @SerializedName("version")
        private String appVersion = "";

        public NlpData(String appId, String appPath, String appVersion) {
            this.appId = appId;
            this.appPath = appPath;
            this.appVersion = appVersion;
        }

        public String getAppId() {
            return appId;
        }

        public String getAppPath() {
            return appPath;
        }

        public String getAppVersion() {
            return appVersion;
        }

        @Override
        public String toString() {
            return "NlpData{" +
                    "appId='" + appId + '\'' +
                    ", appPath='" + appPath + '\'' +
                    ", appVersion='" + appVersion + '\'' +
                    '}';
        }
    }
}
