/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.client.account;

import com.google.gson.Gson;

import org.json.JSONObject;

public class UserBean {

    public static final int ORDINARY_MEMBER = 0;
    public static final int FAMILY_ADMIN = 1;

    private String userId;
    private String mobile;
    private int roleId;
    private String avatarUrl;
    private String createTime;
    private String updateTime;
    private String nickName;
    private TokenBean userToken;

    public UserBean() {
    }

    public UserBean(JSONObject jsonObject) {
        this.userId = jsonObject.optString("user_id");
        this.mobile = jsonObject.optString("mobile");
        this.roleId = jsonObject.optInt("role_id", ORDINARY_MEMBER);
        this.avatarUrl = jsonObject.optString("avatar_url");
        this.createTime = jsonObject.optString("created_at");
        this.updateTime = jsonObject.optString("updated_at");
        this.nickName = jsonObject.optString("nick_name");
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public int getRoleId() {
        return roleId;
    }

    public void setRoleId(int roleId) {
        this.roleId = roleId;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public TokenBean getUserToken() {
        return userToken;
    }

    public void setUserToken(TokenBean userToken) {
        this.userToken = userToken;
    }

    public boolean isFamilyAdmin() {
        return roleId == FAMILY_ADMIN;
    }

    @Override
    public String toString() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }
}