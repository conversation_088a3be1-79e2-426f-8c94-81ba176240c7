package com.ainirobot.coreservice.action;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.config.ConfigManager;
import com.ainirobot.coreservice.core.HeadTurnManager;
import com.ainirobot.coreservice.core.LocalApi;

public class NavigationResetHeadUtils {
    private static final String TAG = "NavigationResetHeadUtil";
    private static final int DEFAULT_VERTICAL_ANGLE_SCOPE = 5;
    private LocalApi mApi;
    private HeadTurnManager mManager;
    private ResetHeadListener mListener;

    public NavigationResetHeadUtils(LocalApi mApi, HeadTurnManager headMoveManager) {
        this.mApi = mApi;
        this.mManager = headMoveManager;
    }

    public void checkResetHeadState(ResetHeadListener listener, int reqId) {
        mListener = listener;
        //mini或者小秘 2 机型（支持移动脑袋）
        //且当前机器支持视觉导航，当前地图是视觉导航地图
        //则需要先重置脑袋到默认位置，再移动，否则直接移动
        //注意：修改此处逻辑时，需要同时修改Home 中ResetHeadUtils的checkResetHeadState逻辑
//        if (ProductInfo.isMiniProduct() || ProductInfo.isMeissa2()) {
//            Log.d(TAG, "checkResetHeadState: isMiniProduct " + ProductInfo.isMiniProduct() + " isMeissa2 " + ProductInfo.isMeissa2());
//            mApi.sendCommand(reqId, Definition.CMD_NAVI_MAP_HAS_VISION, null);
//        } else {
//            //直接移动
//            onResetHeadSuccess();
//        }
        onResetHeadSuccess();
    }

    //注册头部位置监听，如果超过设置角度，通知底盘
    public void startHeadStatusListener(int reqId) {
        mApi.sendCommand(reqId, Definition.CMD_HEAD_START_STATUS_LISTENER, null);
    }

    private ActionListenerProxy actionListenerProxy = new ActionListenerProxy() {
        @Override
        public void onResult(int status, String responseString) {
            Log.d(TAG, "onResult: " + status + " " + responseString);
            if (status == Definition.RESULT_TURN_HEAD_SUCCESS || status == Definition.RESULT_TURN_HEAD_UN_SUPPORT) {
                onResetHeadSuccess();
            } else {
                onResetHeadFailed(Definition.ERROR_NAVIGATION_RESET_HEAD, "reset head failed");
            }
        }

        @Override
        public void onError(int errorCode, String errorString) {
            Log.d(TAG, "onError: " + errorCode + " " + errorString);
            onResetHeadFailed(Definition.ERROR_NAVIGATION_RESET_HEAD, "reset head failed");
        }

        @Override
        public void onStatusUpdate(int status, String data) {
            Log.d(TAG, "onStatusUpdate: " + status + " " + data);
        }
    };

    public boolean onCmdResponse(int cmdId, String command, String result, String extraData) {
        if (TextUtils.equals(Definition.CMD_NAVI_MAP_HAS_VISION, command)) {
            Log.d(TAG, "onCmdResponse: command = " + command + " result = " + result + " extraData = " + extraData);
            if (TextUtils.equals(Definition.RESULT_TRUE, result)) {//支持视觉导航
                mManager.resetHead(actionListenerProxy);
            } else {//不支持视觉，直接移动
                onResetHeadSuccess();
            }
            return true;
        }
        return false;
    }

    public boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        if (TextUtils.equals(Definition.CMD_HEAD_START_STATUS_LISTENER, command)) {
            Log.d(TAG, "cmdStatusUpdate: command = " + command + " status = " + status + " extraData = " + extraData);
            //TODO 如果超过一定角度，通知底盘
            return true;
        }
        return false;
    }

    private void onResetHeadSuccess() {
        if (mListener != null) {
            mListener.onResetHeadSuccess();
        }
    }

    private void onResetHeadFailed(int errorCode, String errorString) {
        if (mListener != null) {
            mListener.onResetHeadFailed(errorCode, errorString);
        }
    }

    public void stop() {
        mListener = null;
        mApi.sendCommand(0, Definition.CMD_HEAD_STOP_STATUS_LISTENER, null);
    }

    public interface ResetHeadListener {
        void onResetHeadSuccess();

        void onResetHeadFailed(int errorCode, String errorString);
    }
}
