package com.ainirobot.coreservice.action.advance_charge.state;

import android.util.Log;

import com.ainirobot.coreservice.action.ChargeAdvancedAction;
import com.ainirobot.coreservice.action.RadarAlignAction;
import com.ainirobot.coreservice.action.advance_charge.AdvanceChargeStateEnum;
import com.ainirobot.coreservice.action.advance_charge.bean.StateData;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.AutoChargeBean;
import com.ainirobot.coreservice.client.actionbean.BasePoseBean;
import com.ainirobot.coreservice.client.actionbean.MultiRobotStatus;
import com.ainirobot.coreservice.client.actionbean.Pose;

import java.util.List;

public class GoChargeAreaState extends BaseAdvanceChargeState {
    private volatile Status mStatus;
    private List<Pose> mWaitPointList;    //充电等待区

    private enum Status {
        START,
        GO_CHARGE_AREA,     //导航去充电等待区

    }

    private void updateStatus(Status status) {
        Log.i(TAG, "updateStatus: " + status);
        mStatus = status;
    }

    @Override
    protected void onStart(ChargeAdvancedAction chargeAdvancedAction, StateData stateData) {
        mWaitPointList = stateData.getWaitPointList();
        updateStatus(Status.START);
        registerMultiRobotStatusListener();
    }

    @Override
    public void stop() {
        super.stop();
        Log.d(TAG, "ChargeAdvanceAction stopAction ------------  ");
        unregisterMultiRobotStatusListener();
    }

    @Override
    protected boolean onCmdResponse(int cmdId, String command, String result, String extraData) {
        Log.d(TAG, "cmdResponse cmdType=" + command + " result=" + result);
        switch (command) {
            case Definition.CMD_NAVI_GO_LOCATION:
                Log.d(TAG, "go location result = " + result);
                if (Definition.NAVIGATION_OK.equals(result)) {
                    goLocationSuccess();
                    onStateRunSuccess();
                } else {
                    Log.d(TAG, "go location failed");
                    updateStatus(Status.START);
                }
                break;
        }
        return true;
    }

    private void goLocationSuccess() {
        int curAreaId = mStateData.getCurUseAreaId();
        int curChargePriority = mStateData.getCurChargePriority();
        if (curChargePriority <= 0) {
            // 优先级从1开始递增，值越小，优先级越高
            int priority = 0;
            for (int i = 0; i < mRobotStatusList.size(); i++) {
                MultiRobotStatus robotStatus = mRobotStatusList.get(i);
                Log.d(TAG, "goLocationSuccess chargeAreaId=" + robotStatus.getChargeAreaId() + " chargePriority=" + robotStatus.getChargePriority());
                if (curAreaId == robotStatus.getChargeAreaId() && robotStatus.getChargePriority() != -1 && priority < robotStatus.getChargePriority()) {
                    priority = robotStatus.getChargePriority();
                }
            }
            priority += 1;
            Log.d(TAG, "writeMultiRobotExtraData chargeAreaId=" + curAreaId + " priority=" + priority);
            mStateData.setCurChargePriority(priority);
            writeMultiRobotChargePriority(curAreaId, priority);
        }
    }

    @Override
    protected boolean onCmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        //Log.d(TAG, "onCmdStatusUpdate cmdType=" + command + " status=" + status);
        switch (command) {
            case Definition.CMD_NAVI_GO_LOCATION:
                handleNavigationStatus(status);
                return true;
            default:
                return false;
        }
    }

    @Override
    protected AdvanceChargeStateEnum getNextState() {
        return AdvanceChargeStateEnum.GO_ADVANCE_CHARGE;
    }

    @Override
    protected void handleMultipleRobotStatus(List<MultiRobotStatus> robotStatusList) {
        switch (mStatus) {
            case START:
                startStatus(robotStatusList);
                break;
            case GO_CHARGE_AREA:
                break;
        }
    }

    private void startStatus(List<MultiRobotStatus> robotStatusList) {
        //去充电等待区可用的点
        Pose backupDes = findAvailableChargeAreaDestination(robotStatusList);
        if (backupDes == null) {
            Log.d(TAG, "startStatus no available destination");
            onStateUpdate(Definition.ERROR_NO_AVAILABLE_DESTINATION, "no available destination");
            return;
        }

        updateStatus(Status.GO_CHARGE_AREA);
        startGoChargeArea(backupDes);
    }

    private void startGoChargeArea(Pose backupDes) {
        if (backupDes == null) {
            Log.e(TAG, "startGoChargeArea backupDes is null");
            return;
        }
        mChargeAdvancedAction.getApi().goPlace(mChargeAdvancedAction.getReqId(), backupDes.getName());
    }

    /**
     * 寻找可以到达的备用点
     *
     * @return 如果所有备用点都不可到达，返回null，否则返回可到达点
     */
    public Pose findAvailableChargeAreaDestination(List<MultiRobotStatus> robotStatusList) {
        Log.d(TAG, "findAvailableChargeAreaDestination mWaitPointList:" + mGson.toJson(mWaitPointList));
        for (int i = 0; i < mWaitPointList.size(); i++) {
            Pose pose = mWaitPointList.get(i);
            boolean inUse = isPoseInUseCurrently(pose, robotStatusList);
            Log.d(TAG, "findAvailableChargeAreaDestination pose:" + pose.getName() + " inUse: " + inUse);
            if (!inUse) {
                return pose;
            }
        }
        return null;
    }

}
