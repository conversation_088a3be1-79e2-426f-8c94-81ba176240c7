/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.core.module;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.IStatusListener;
import com.ainirobot.coreservice.action.ActionManager;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.actionbean.CommandBean;
import com.ainirobot.coreservice.config.ConfigManager;
import com.ainirobot.coreservice.core.InternalDef;
import com.ainirobot.coreservice.core.external.ExternalService;
import com.ainirobot.coreservice.core.external.OtaService;
import com.ainirobot.coreservice.core.status.RemoteSubscriber;
import com.ainirobot.coreservice.core.status.StatusManager;
import com.ainirobot.coreservice.core.system.SystemManager;
import com.ainirobot.coreservice.listener.IActionListener;
import com.ainirobot.coreservice.service.CoreService;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Map;

public class ModuleClient {
    private final String TAG = "ModuleClient";
    private CoreService mCore;
    private String module;

    private Gson mGson;

    private int headVAngle = -1;

    public ModuleClient(CoreService core, String module) {
        this.mCore = core;
        this.mGson = new Gson();
        this.module = module;
    }

    public int startAction(int reqId, int actionId, String params, IActionListener listener) {
        startAction(reqId, actionId, params, listener, true);
        return 0;
    }

    public int startAction(int reqId, int actionId, String params, IActionListener listener, boolean isSystem) {
        Log.d(TAG, "Start action : " + actionId + "  " + params);
        switch (actionId) {
            case Definition.ACTION_HEAD_TURN_HEAD:
                return mCore.getHeadMoveManager().turnHead(params, listener);
            case Definition.ACTION_HEAD_RESET_HEAD:
                Log.d(TAG, "Reset head : " + headVAngle);
                return mCore.getHeadMoveManager().turnHead(headVAngle, listener);
            case Definition.ACTION_HEAD_GET_ALL_PERSON_INFOS:
                mCore.getPersonManager().setActionListener(listener);
                break;
            default:
                ActionManager actionManager = mCore.getActionManager();
                actionManager.exCmd(actionId, params, listener, module, isSystem);
                break;
        }
        return 0;
    }

    public int stopAction(int reqId, int actionId, boolean isResetHW) {
        Log.i(TAG, "Stop action : " + actionId + "  " + isResetHW);
        if (Definition.ACTION_HEAD_GET_ALL_PERSON_INFOS == actionId) {
            mCore.getPersonManager().releaseActionListener();
        }
        ActionManager actionManager = mCore.getActionManager();
        actionManager.exStopCmd(actionId, isResetHW);
        return 0;
    }

    public void setDefaultHeadAngle(int hAngle, int vAngle) {
        Log.d(TAG, "setDefaultHeadAngle hAngle: " + hAngle + ", vAngle: " + vAngle);
        headVAngle = vAngle;
    }

    public int stopActionCancelCmd(int reqId, int actionId, boolean isResetHW, boolean isCancelStopCommand) {
        Log.i(TAG, "Stop action : " + actionId + "  " + isResetHW);
        if (Definition.ACTION_HEAD_GET_ALL_PERSON_INFOS == actionId) {
            mCore.getPersonManager().releaseActionListener();
        }
        ActionManager actionManager = mCore.getActionManager();
        actionManager.exStopCmd(actionId, isResetHW , isCancelStopCommand);
        return 0;
    }

    public int stopAllAction() {
        Log.d(TAG, "Stop all action");
        ActionManager actionManager = mCore.getActionManager();
        actionManager.stopAllAction(module);
        return 0;
    }

    public int setLampColor(int reqId, int target, int color) {
        JSONObject params = new JSONObject();
        try {
            params.put(Definition.JSON_LAMP_TARGET, target);
            params.put(Definition.JSON_LAMP_COLOR_RGB_VALUE, color);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        CommandBean bean = new CommandBean(Definition.CMD_CAN_LAMP_COLOR, params.toString(), false);
        return startAction(reqId, Definition.ACTION_LAMP_COLOR, mGson.toJson(bean), null);
    }

    public int setLampAnimation(int reqId, int target, int start, int end, int startTime, int
            endTime, int repeat, int onTime, int freeze) {
        JSONObject params = new JSONObject();
        try {
            params.put(Definition.JSON_LAMP_TARGET, target);
            params.put(Definition.JSON_LAMP_RGB_START, start);
            params.put(Definition.JSON_LAMP_RGB_END, end);
            params.put(Definition.JSON_LAMP_START_TIME, startTime);
            params.put(Definition.JSON_LAMP_END_TIME, endTime);
            params.put(Definition.JSON_LAMP_REPEAT, repeat);
            params.put(Definition.JSON_LAMP_ON_TIME, onTime);
            params.put(Definition.JSON_LAMP_RGB_FREEZE, freeze);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        CommandBean bean = new CommandBean(Definition.CMD_CAN_LAMP_ANIM, params.toString(), false);
        return startAction(reqId, Definition.ACTION_LAMP_ANIMATION, mGson.toJson(bean), null);
    }

    public int setLedLight(int reqId, String properties) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_SET_LED_LIGHT,
                properties, false);
        return startAction(reqId, Definition.ACTION_COMMON, mGson.toJson(bean),
                null);
    }

    public int stopTrack(int reqId, IActionListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_CONTROL, Definition.JSON_HEAD_STOP);
            param.put(Definition.JSON_HEAD_NAME, "");
            param.put(Definition.JSON_HEAD_ID, -1);
            param.put(Definition.JSON_HEAD_MODE, 3);
            CommandBean bean = new CommandBean(Definition.CMD_HEAD_STOP_TRACK_TARGET, param
                    .toString(), false);
            return startAction(reqId, Definition.ACTION_STOP_TRACK_TARGET, mGson.toJson(bean), listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int switchCamera(int reqId, String mode, IActionListener listener) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_LOCATION, mode);

            CommandBean bean = new CommandBean(Definition.CMD_HEAD_SWITCH_CAMERA, param.toString
                    (), false);
            return startAction(reqId, Definition.ACTION_HEAD_SWITCH_CAMERA, mGson.toJson(bean), listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int startVision(int reqId, IActionListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_START_VISION,
                null, false);
        return startAction(reqId, Definition.ACTION_HEAD_START_VISION, mGson.toJson(bean), listener);
    }

    public int getChargePile(int reqId, IActionListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_GET_LOCATION,
                Definition.START_BACK_CHARGE_POSE, false);
        return startAction(reqId, Definition.ACTION_NAVI_GET_LOCATION,
                mGson.toJson(bean), listener);
    }

    public int loadCurrentMap(int reqId, IActionListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_LOAD_CURRENT_MAP, null, false);
        return startAction(reqId, Definition.ACTION_NAVI_LOAD_CURRENT_MAP,
                mGson.toJson(bean), listener);
    }

    public int checkCurNaviMap(int reqId, IActionListener listener) {
        CommandBean bean = new CommandBean(Definition.CMD_NAVI_CHECK_CUR_NAVI_MAP, null, false);
        return startAction(reqId, Definition.ACTION_NAVI_CHECK_CUR_NAVI_MAP,
                mGson.toJson(bean), listener);
    }

    public void setSystemStatusEnabled(String status, boolean enabled) {
        SystemManager systemManager = mCore.getSystemManager();
        systemManager.setSystemStatusEnabled(status, enabled);
    }

    public void updateBatteryStatus() {
        SystemManager systemManager = mCore.getSystemManager();
        systemManager.updateBatteryStatus();
    }

    public void updateFunctionKeyStatus(boolean enable) {
        SystemManager systemManager = mCore.getSystemManager();
        systemManager.updateFunctionKeyStatus(enable);
    }

    public void recoverySystemStatus(Map<String, Boolean> status) {
        SystemManager systemManager = mCore.getSystemManager();
        systemManager.recoverySystemStatus(status);
    }

    public void resetSystemStatus() {
        SystemManager systemManager = mCore.getSystemManager();
        systemManager.resetSystemStatus();
    }

    public boolean switchScreen(boolean onOff) {
        SystemManager systemManager = mCore.getSystemManager();
        return systemManager.switchScreen(onOff);
    }

    public boolean finishModuleParser(int reqId, boolean result, String response)
            throws RemoteException {
        Handler requestHandler = mCore.getRequestHandler();
        Message msg = requestHandler.obtainMessage(InternalDef.MSG_REQUEST_FINISH_PARSER);
        Bundle bundle = new Bundle();
        bundle.putInt(InternalDef.BUNDLE_INT, reqId);
        bundle.putBoolean(InternalDef.BUNDLE_BOOLEAN, result);
        bundle.putString(InternalDef.BUNDLE_RESPONSE, response);
        msg.setData(bundle);
        requestHandler.sendMessage(msg);
        return true;
    }

    public String registerStatusListener(String type, IStatusListener listener) {
        StatusManager statusManager = mCore.getStatusManager();
        RemoteSubscriber subscriber = new RemoteSubscriber(listener);
        return statusManager.registerStatusListener(type, subscriber);
    }

    public boolean unregisterStatusListener(String id) {
        StatusManager statusManager = mCore.getStatusManager();
        return statusManager.unregisterStatusListener(id);
    }

    public boolean updateSystemStatus(String status, String data) {
        switch (status) {
            case Definition.SYSTEM_STANDBY:
                mCore.getSystemManager().onSystemStatusUpdate(status, data);
                return true;
            //TODO:目前仅对待机开放
            default:
                return false;
        }
    }

    public boolean startStatusSocket(String type, int socketPort) throws RemoteException {
        String serviceName = mCore.getSocketManager().getServiceName(type);
        ExternalService service = mCore.getExternalServiceManager().getExternalService(serviceName);
        return service.startStatusSocket(type, socketPort);
    }

    public boolean closeStatusSocket(String type, int socketPort) throws RemoteException {
        String serviceName = mCore.getSocketManager().getServiceName(type);
        ExternalService service = mCore.getExternalServiceManager().getExternalService(serviceName);
        return service.closeStatusSocket(type, socketPort);
    }

    public void sendStatusReport(String type, String data) {
        StatusManager statusManager = mCore.getStatusManager();
        statusManager.handleStatus(type, data);
    }

    public int remoteBindStatus(int reqId, IActionListener listener) {
        JsonObject obj = new JsonObject();
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_BIND_STATUS, obj.toString(),
                false);
        return startAction(reqId, Definition.ACTION_REMOTE_BIND_STATUS,
                mGson.toJson(bean), listener);
    }

    public int refreshRobotProfile(int reqId, IActionListener listener) {
        JsonObject obj = new JsonObject();
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_REFRESH_ROBOT_PROFILE, obj.toString(),
                false);
        return startAction(reqId, Definition.ACTION_COMMON,
                mGson.toJson(bean), listener);
    }


    /**
     * Set lighting effects
     *
     * @param reqId   request ID
     * @param params, including:
     *                type - Lighting effect type
     *                start - Start color
     *                end - End color
     *                startTime - Start time
     *                endTime - End time
     *                repeat - Loop times
     *                onTime - Duration
     *                freese - end lighting color
     * @return the status of set action.
     */
    public int setLight(int reqId, String params) {
        CommandBean bean = new CommandBean(Definition.CMD_CAN_LAMP_ANIM, params, false);
        return startAction(reqId, Definition.ACTION_LAMP_ANIMATION, mGson.toJson(bean), null);
    }

    public void setLanguage(String lang) {
        mCore.getExternalServiceManager().setLanguage(lang);
    }

    public boolean installPatch(Bundle bundle) {
        OtaService otaService = mCore.getExternalServiceManager().getOtaService();
        return otaService.installPatch(bundle);
    }

    void suspend() {
        mCore.getHeadMoveManager().stopAll();
        mCore.getPersonManager().releaseActionListener();
        mCore.getPersonManager().releasePersonListener();
        mCore.getSystemManager().resetHWStatus();
        mCore.getSystemManager().uvcStopContinueClassify();
        if (ProductInfo.isSaiphXD()){
            mCore.getSystemManager().stopXdPower();
        }
        //梯控项目需要释放电梯
        if (ConfigManager.isSupportElevatorFunction()) {
            Log.d(TAG, "release elevator from suspend.");
            mCore.getSystemManager().releaseElevator();
        }
    }
}
