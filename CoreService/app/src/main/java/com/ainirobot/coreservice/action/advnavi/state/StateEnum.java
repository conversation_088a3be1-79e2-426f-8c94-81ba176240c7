/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.action.advnavi.state;

/**
 * 需要坐电梯流程：默认 -> 准备(初始化) -> 去电梯口 -> 在电梯外等待 -> 进电梯 -> 切图 -> 在电梯内等待 -> 出梯 -> 去目的地
 * 在电梯内流程：默认 -> 准备(初始化) -> 切图 -> 在电梯内等待 -> 出梯 -> 去目的地
 * 不需要坐电梯流程：默认 -> 准备(初始化) -> 去目的地
 * 多机导航流程：默认 -> 多机导航准备 -> 多机导航策略
 * 闸机导航流程：默认 -> 准备(初始化) -> 去闸机的一个口 -> 在闸机外等待 -> 去闸机的另一个口 -> 去目的地
 */
public enum StateEnum {
    IDLE(0, new IdleState("idle")),
    /**
     * 准备状态
     */
    PREPARE(10, new PrepareState("prepare")),
    /**
     * 多机导航准备状态
     */
    PREPARE_MULTI_ROBOT(11, new PrepareMultiRobotState("prepare_multi_robot")),
    /**
     * 电梯导航准备状态
     */
    PREPARE_ELEVATOR(12, new PrepareElevatorState("prepare_elevator")),
    /**
     * 导航去电梯等待点
     */
    GO_ELEVATOR_WAIT_POINT(13, new GoElevatorWaitPointState("go_elevator_wait_point")),
    /**
     * 注册电梯
     */
    REGISTER_ELEVATOR(15, new RegisterElevatorState("register_elevator")),
    /**
     * 导航去电梯口状态
     */
    GO_ELEVATOR_GATE(20, new GoGateState("go_elevator_gate")),
    /**
     * 在电梯外等待状态
     */
    WAIT_OUTSIDE_ELEVATOR(30, new WaitOutsideState("wait_outside_elevator")),
    /**
     * 进梯状态
     */
    ENTER_ELEVATOR(40, new EnterElevatorState("enter_elevator")),
    /**
     * 底盘切换地图状态
     */
    SWITCH_MAP(50, new SwitchMapState("switch_map")),
    /**
     * 在电梯内等待状态
     */
    WAIT_INSIDE_ELEVATOR(60, new WaitInsideState("wait_inside_elevator")),
    /**
     * 出梯状态
     */
    EXIT_ELEVATOR(70, new ExitElevatorState("exit_elevator")),
    /**
     * 导航去目的地
     */
    GO_DESTINATION(80, new GoDestinationState("go_destination")),
    /**
     * 多机导航策略：根据lora优先级
     */
    NAVI_LORA_PRIOR(81, new NaviLoraPriorStrategy("navi_lora_prior")),
    /**
     * 多机导航策略：导航去最后一个点
     */
    NAVI_LAST_POSE(82, new NaviLastPoseStrategy("navi_last_pose")),
    /**
     * 多机导航策略：动态计算导航点位
     */
    NAVI_DYNAMIC_POINT(83, new NaviDynamicPointStrategy("navi_dynamic_point")),
    /**
     * 多机导航策略：lora优先级星形补位
     */
    NAVI_STAR_BY_LORA(84, new NaviStarByLoraStrategy("navi_star_by_lora")),
    /**
     * 多机导航策略：点位优先级星形补位
     */
    NAVI_STAR_BY_POINT(85, new NaviStarByPointStrategy("navi_star_by_point")),
    /**
     * 多机导航策略：点位优先级火车补位
     */
    NAVI_TRAIN_BY_POINT(86, new NaviTrainByPointStrategy("navi_train_by_point")),
    /**
     * 多机导航策略：点位优先级动态火车补位
     */
    NAVI_DYNAMIC_TRAIN_BY_POINT(87, new NaviDynamicTrainByPointStrategy("navi_dynamic_train_by_point")),
    /**
     * 闸机导航准备
     */
    PREPARE_GATE_NAV(88, new GatePrepareState("prepare_gate_nav")),
    /**
     * 导航去第一个闸机口
     */
    GO_GATE_FIRST(89, new GoGateFirstState("go_first_gate")),
    /**
     * 在闸机口等待状态
     */
    WAIT_OUTSIDE_GATE(30, new GateWaitOutsideState("wait_outside_gate")),
    /**
     * 导航去第二个闸机口
     */
    GO_GATE_SECOND(91, new GoGateSecondState("go_second_gate"));

    private int id;
    private BaseState state;

    StateEnum(int id, BaseState state) {
        this.id = id;
        this.state = state;
    }

    public int getId() {
        return id;
    }

    public BaseState getState() {
        return state;
    }
}
