package com.ainirobot.coreservice.dump.manager;

import android.content.Context;

import com.ainirobot.coreservice.dump.DataDumpService;
import com.ainirobot.coreservice.dump.DumpDef;
import com.ainirobot.coreservice.dump.collect.AbstractCollector;
import com.ainirobot.coreservice.dump.collect.HwStatusCollector;
import com.ainirobot.coreservice.dump.collect.KeyFrameCollector;
import com.ainirobot.coreservice.dump.collect.ServiceStatusCollector;
import com.ainirobot.coreservice.dump.collect.VisionRecordCollector;

import java.util.HashMap;

/**
 * Manager data collectors.
 * Created by Orion on 2020/5/25.
 */
public class DataDumpManager {
    private static final String TAG = "DataDumpManager";

    private Context mContext;
    private DataDumpService mService;
    private HashMap mCollectors = new HashMap<String, AbstractCollector>();

    public DataDumpManager(DataDumpService service) {
        this.mContext = service.getApplicationContext();
        this.mService = service;
        initCollector();
    }

    private void initCollector() {
        addCollector(DumpDef.TYPE_VISION_RECORD);
        addCollector(DumpDef.TYPE_KEY_FRAME);
        addCollector(DumpDef.TYPE_SERVICE_STATUS);
        addCollector(DumpDef.TYPE_HW_STATUS);

        //默认启动
        start(DumpDef.TYPE_SERVICE_STATUS);
        start(DumpDef.TYPE_HW_STATUS);
    }

    private boolean start(String type) {
        return start(type, null);
    }

    public boolean start(String type, String param) {
        return getCollector(type).start(param);
    }

    public boolean stop(String type) {
        return getCollector(type).stop();
    }

    private void addCollector(String type) {
        if (mCollectors.containsKey(type)) {
            return;
        }
        mCollectors.put(type, createCollector(type));
    }

    private AbstractCollector getCollector(String type) {
        if (!mCollectors.containsKey(type)) {
            throw new UnsupportedOperationException("Unsupported data type");
        }
        return (AbstractCollector) mCollectors.get(type);
    }

    private AbstractCollector createCollector(String type) {
        AbstractCollector collector = null;
        switch (type) {
            case DumpDef.TYPE_VISION_RECORD:
                collector = new VisionRecordCollector(this);
                break;
            case DumpDef.TYPE_KEY_FRAME:
                collector = new KeyFrameCollector(this);
                break;
            case DumpDef.TYPE_SERVICE_STATUS:
                collector = new ServiceStatusCollector(this);
                break;
            case DumpDef.TYPE_HW_STATUS:
                collector = new HwStatusCollector(this);
                break;
            default:
                throw new UnsupportedOperationException("Not support data type");
        }
        return collector;
    }

}
