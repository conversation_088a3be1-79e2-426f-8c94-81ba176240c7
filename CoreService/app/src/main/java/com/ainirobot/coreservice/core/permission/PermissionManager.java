package com.ainirobot.coreservice.core.permission;

import android.content.ComponentName;
import android.content.Context;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.IPermissionApi;
import com.ainirobot.coreservice.R;
import com.ainirobot.coreservice.bi.report.TopAppCheckReport;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingListener;
import com.ainirobot.coreservice.client.techreport.CallChainReport;
import com.ainirobot.coreservice.listener.IPermissionListener;
import com.ainirobot.coreservice.service.CoreService;
import com.ainirobot.coreservice.utils.ChangeDensityUtils;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.coreservice.utils.SettingDataHelper;
import com.ainirobot.coreservice.utils.SystemUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

public class PermissionManager extends IPermissionApi.Stub {

    private static final String TAG = PermissionManager.class.getSimpleName();
    static final int TYPE_ACTIVITY_STARTING = 0x1;
    static final int TYPE_ACTIVITY_RESUMING = 0x2;
    static final int TYPE_APP_CRASHED = 0x3;
    static final int TYPE_APP_EARLY_NOT_RESPONDING = 0x4;
    static final int TYPE_APP_NOT_RESPONDING = 0x5;
    static final int TYPE_SYSTEM_NOT_RESPONDING = 0x6;
    static final int TYPE_FOREGROUND_ACTIVITIES_CHANGED = 0x7;
    static final int TYPE_PROCESS_DIED = 0x8;
    private List<String> mWhiteList;
    private List<String> mPreActiveApp = new CopyOnWriteArrayList<>();
    private CoreService mCore;
    private Context mContext;
    private static final List<IPermissionListener> sPermissionListenerList = new ArrayList<>();
    private CallChainReport mCallChain;

    public PermissionManager(CoreService core) {
        mCore = core;
        mContext = core.getApplicationContext();
        HandlerThread thread = new HandlerThread(PermissionManager.class.getSimpleName());
        thread.start();
        PermissionHandler handler = new PermissionHandler(this, thread.getLooper());
        initWhiteList();
        listenRobotSettingDefaultPackage();
        boolean isOrionActivityControllerSuccess = SystemUtils.setOrionActivityController(handler);
        Log.d(TAG, "isOrionActivityControllerSuccess: " + isOrionActivityControllerSuccess);
        if (!isOrionActivityControllerSuccess) {
            SystemUtils.setActivityController(handler);
        }
        SystemUtils.monitorAppProcess(handler);
        mCallChain = new CallChainReport(mContext, ActivityController.class.getSimpleName(),
                "app_authorize");
    }

    private static class PermissionHandler extends Handler {
        private PermissionManager mManager;
        private PermissionHandler(PermissionManager manager, Looper looper) {
            super(looper);
            mManager = manager;
        }

        @Override
        public void handleMessage(Message msg) {
            Log.d(TAG, "handleMessage msg.what: " + msg.what);
            PermissionBean bean = (PermissionBean) msg.obj;
            CoreService core = mManager.mCore;
            Context context = mManager.mContext;
            switch (msg.what) {
                case TYPE_ACTIVITY_STARTING:
                    Log.d(TAG, "activityStarting intent: " + bean.getIntent()
                            + ", package: " + bean.getPkg()
                            + ", activity : " + SystemUtils.getActivityTop(context));
                    if (bean.getIntent().getCategories() != null
                            && bean.getIntent().getCategories().contains(Definition.CATEGORY_OPEN_APP)) {
                        mManager.addThirdPartyAppToWhiteList(bean.getPkg());
                        Log.d(TAG, "activityStarting mWhiteList: " + mManager.mWhiteList.toString());
                    }
                    mManager.handleActivityChange(bean.getPkg(), true);
                    //report top app bi
                    new TopAppCheckReport().fillData(bean.getPkg()).report();
                    synchronized (sPermissionListenerList) {
                        Log.d(TAG, "activityStarting mPermissionListenerList size: "
                                + sPermissionListenerList.size());
                        Iterator<IPermissionListener> iterator = sPermissionListenerList.iterator();
                        while (iterator.hasNext()) {
                            IPermissionListener listener = iterator.next();
                            try {
                                listener.activityStarting(bean.getIntent(), bean.getPkg());
                            } catch (RemoteException e) {
                                e.printStackTrace();
                                iterator.remove();
                            }
                        }
                    }
                    break;
                case TYPE_ACTIVITY_RESUMING:
                    ComponentName topActivity = SystemUtils.getActivityTop(mManager.mContext);
                    Log.d(TAG, "activityResuming package: " + bean.getPkg() + ", activity : " + topActivity);
                    synchronized (sPermissionListenerList) {
                        Log.d(TAG, "activityResuming mPermissionListenerList size: "
                                + sPermissionListenerList.size());
                        Iterator<IPermissionListener> iterator = sPermissionListenerList.iterator();
                        while (iterator.hasNext()) {
                            IPermissionListener listener = iterator.next();
                            try {
                                listener.activityResuming(bean.getPkg());
                            } catch (RemoteException e) {
                                e.printStackTrace();
                                iterator.remove();
                            }
                        }
                    }
                    if (topActivity != null
                            && "com.ainirobot.home.LaunchActivity".equals(topActivity.getClassName())) {
                        Log.d(TAG, "Current is launch activity");
                        return;
                    }
                    mManager.handleActivityChange(bean.getPkg(), false);
                    break;
                case TYPE_APP_CRASHED:
                    Log.e(TAG, "appCrashed: " + bean.getProcessName()
                            + "[" + bean.getPid() + "]" + "\n" + bean.getStackTrace());
                    mManager.checkPermission(bean.getProcessName());
                    synchronized (sPermissionListenerList) {
                        Log.d(TAG, "appCrashed sPermissionListenerList size: "
                                + sPermissionListenerList.size());
                        Iterator<IPermissionListener> iterator = sPermissionListenerList.iterator();
                        while (iterator.hasNext()) {
                            IPermissionListener listener = iterator.next();
                            try {
                                listener.appCrashed(bean.getProcessName(), bean.getPid(),
                                        bean.getMsg(), bean.getMsg2(), bean.getTimeMillis(),
                                        bean.getStackTrace());
                            } catch (RemoteException e) {
                                e.printStackTrace();
                                iterator.remove();
                            }
                        }
                    }
                    break;
                case TYPE_APP_EARLY_NOT_RESPONDING:
                    Log.e(TAG, "appEarlyNotResponding: " + bean.getProcessName()
                            + "[" + bean.getPid() + "]" + "\n" + bean.getMsg());
                    synchronized (sPermissionListenerList) {
                        Log.d(TAG, "appEarlyNotResponding sPermissionListenerList size: "
                                + sPermissionListenerList.size());
                        Iterator<IPermissionListener> iterator = sPermissionListenerList.iterator();
                        while (iterator.hasNext()) {
                            IPermissionListener listener = iterator.next();
                            try {
                                listener.appEarlyNotResponding(bean.getProcessName(), bean.getPid(),
                                        bean.getMsg());
                            } catch (RemoteException e) {
                                e.printStackTrace();
                                iterator.remove();
                            }
                        }
                    }
                    break;
                case TYPE_APP_NOT_RESPONDING:
                    Log.e(TAG, "appNotResponding: " + bean.getProcessName()
                            + "[" + bean.getPid() + "]" + "\n" + bean.getMsg());
                    mManager.checkPermission(bean.getProcessName());
                    synchronized (sPermissionListenerList) {
                        Log.d(TAG, "appNotResponding mPermissionListenerList size: "
                                + sPermissionListenerList.size());
                        Iterator<IPermissionListener> iterator = sPermissionListenerList.iterator();
                        while (iterator.hasNext()) {
                            IPermissionListener listener = iterator.next();
                            try {
                                listener.appNotResponding(bean.getProcessName(), bean.getPid(),
                                        bean.getMsg());
                            } catch (RemoteException e) {
                                e.printStackTrace();
                                iterator.remove();
                            }
                        }
                    }
                    break;
                case TYPE_SYSTEM_NOT_RESPONDING:
                    Log.e(TAG, "systemNotResponding: " + bean.getMsg());
                    synchronized (sPermissionListenerList) {
                        Log.d(TAG, "systemNotResponding sPermissionListenerList size: "
                                + sPermissionListenerList.size());
                        Iterator<IPermissionListener> iterator = sPermissionListenerList.iterator();
                        while (iterator.hasNext()) {
                            IPermissionListener listener = iterator.next();
                            try {
                                listener.systemNotResponding(bean.getMsg());
                            } catch (RemoteException e) {
                                e.printStackTrace();
                                iterator.remove();
                            }
                        }
                    }
                    break;
                case TYPE_FOREGROUND_ACTIVITIES_CHANGED:
                    String packageName = SystemUtils.getPackageName(context, bean.getPid());
                    Log.d(TAG, "onForegroundActivitiesChanged pid: " + bean.getPid() + ", uid: " + bean.getUid()
                            + ", foregroundActivities: " + bean.isForegroundActivities()
                            + ", packageName: " + packageName);
                    if (bean.isForegroundActivities()) {
//                        ChangeDensityUtils.onForegroundAppChange(packageName);
                        String currentApp = core.getSystemServer().getActiveApp();
                        Log.d(TAG, "onForegroundActivitiesChanged currentApp: " + currentApp
                                + ", mPreActiveApp: " + mManager.mPreActiveApp);
                        if (!mManager.mPreActiveApp.contains(currentApp)
                                && currentApp != null) {
                            mManager.mPreActiveApp.add(currentApp);
                        }

                        if (mManager.mWhiteList.contains(packageName)) {
                            Log.d(TAG, "Start white list app : " + packageName);
                            if (!packageName.equals(currentApp)) {
                                core.getSystemServer().startAppControl(packageName);
                            }
                            return;
                        }

                        if (!SystemUtils.isLaunchRecent(context, packageName)
                                && (mManager.mWhiteList.contains(packageName)
                                || mManager.mPreActiveApp.contains(packageName))) {
                            Log.d(TAG, "Recovery pre app : " + packageName);
                            mManager.mPreActiveApp.remove(packageName);
                            if (!packageName.equals(currentApp)) {
                                core.getSystemServer().startAppControl(packageName);
                            }
                        }
                    }
                    synchronized (sPermissionListenerList) {
                        Log.d(TAG, "onForegroundActivitiesChanged sPermissionListenerList size: "
                                + sPermissionListenerList.size());
                        Iterator<IPermissionListener> iterator = sPermissionListenerList.iterator();
                        while (iterator.hasNext()) {
                            IPermissionListener listener = iterator.next();
                            try {
                                listener.onForegroundActivitiesChanged(bean.getPid(), bean.getUid(),
                                        bean.isForegroundActivities());
                            } catch (RemoteException e) {
                                e.printStackTrace();
                                iterator.remove();
                            }
                        }
                    }
                    break;
                case TYPE_PROCESS_DIED:
                    Log.d(TAG, "onProcessDied pid: " + bean.getPid()
                            + ", uid: " + bean.getUid() + ", packageName: "
                            + SystemUtils.getPackageName(context, bean.getPid()));
                    synchronized (sPermissionListenerList) {
                        Log.d(TAG, "onProcessDied mPermissionListenerList size: "
                                + sPermissionListenerList.size());
                        Iterator<IPermissionListener> iterator = sPermissionListenerList.iterator();
                        while (iterator.hasNext()){
                            IPermissionListener listener = iterator.next();
                            try {
                                listener.onProcessDied(bean.getPid(), bean.getUid());
                            } catch (RemoteException e) {
                                e.printStackTrace();
                                iterator.remove();
                            }
                        }
                    }
                    break;
                default:
                    break;
            }
        }
    }

    @Override
    public void registerPermissionListener(IPermissionListener listener) {
        Log.d(TAG, "registerPermissionListener listener: " + listener.toString());
        synchronized (sPermissionListenerList) {
            sPermissionListenerList.add(listener);
        }
    }

    @Override
    public void unregisterPermissionListener(IPermissionListener listener) {
        Log.d(TAG, "unregisterPermissionListener listener: " + listener.toString());
        synchronized (sPermissionListenerList) {
            sPermissionListenerList.remove(listener);
        }
    }

    private void initWhiteList() {
        mWhiteList = new ArrayList<>(Arrays.asList(mContext.getResources()
                .getStringArray(R.array.whiteList)));
        String packageName = RobotSettingApi.getInstance().getRobotString(Definition
                .BOOT_APP_PACKAGE_NAME);
        if (!TextUtils.isEmpty(packageName) && !mWhiteList.contains(packageName)) {
            Log.d(TAG, "add packageName : " + packageName + " into whiteList");
            mWhiteList.add(packageName);
        }
    }

    private void addThirdPartyAppToWhiteList(String pkg) {
        if (mWhiteList != null && !TextUtils.isEmpty(pkg) && !mWhiteList.contains(pkg)) {
            mWhiteList.add(pkg);
        }
    }

    private void checkPermission(final String pkg) {
        Log.d(TAG, "Check permission");
        DelayTask.submit(TAG, new Runnable() {
            @Override
            public void run() {
                ComponentName topActivity = SystemUtils.getActivityTop(mContext);
                if (topActivity == null) {
                    return;
                }

                String topPkg = topActivity.getPackageName();
                String currentApp = mCore.getSystemServer().getActiveApp();
                if (topPkg.equals(pkg)) {
                    checkPermission(pkg);
                    return;
                }

                Log.d(TAG, "Check permission, top : " + topPkg + " , current : " + currentApp);
                if (!topPkg.equals(currentApp)
                        && mWhiteList.contains(topPkg)) {
                    Log.d(TAG, "Start app control : " + topPkg);
                    mCore.getSystemServer().startAppControl(topPkg);
                }
            }
        }, 3000);
    }

    private void listenRobotSettingDefaultPackage() {
        RobotSettingApi.getInstance().registerRobotSettingListener(
                new RobotSettingListener() {
                    @Override
                    public void onRobotSettingChanged(String key) {
                        if (TextUtils.equals(key, Definition.BOOT_APP_PACKAGE_NAME)) {
                            initWhiteList();
                        }
                    }
                }, Definition.BOOT_APP_PACKAGE_NAME);
    }

    private synchronized void onAppChange(String pkg, ComponentName topActivity,
                                          boolean isLaunchRecent, boolean isStart) {
        String currentApp = mCore.getSystemServer().getActiveApp();
        if (!mPreActiveApp.contains(currentApp)
                && currentApp != null) {
            mPreActiveApp.add(currentApp);
        }

        Log.d(TAG, "On app change pre app : " + mPreActiveApp + "  current : " + currentApp);
        if (isStart && mWhiteList.contains(pkg)) {
            Log.d(TAG, "Start white list app : " + pkg);
            if (!pkg.equals(currentApp)) {
                mCore.getSystemServer().startAppControl(pkg);
            }
            return;
        }

        if (!isLaunchRecent
                && (mWhiteList.contains(pkg) || mPreActiveApp.contains(pkg))) {
            Log.d(TAG, "Recovery pre app : " + pkg);
            mPreActiveApp.remove(pkg);
            if (!pkg.equals(currentApp)) {
                mCore.getSystemServer().startAppControl(pkg);
            }
            return;
        }

        if (!pkg.equals(currentApp)) {
            Log.d(TAG, "Top activity no control");
            return;
        }

        if (!isStart) {
            Log.d(TAG, "Stop app control");
            mCore.getSystemServer().stopAppControl();
        }
    }

    private void handleActivityChange(final String pkg, final boolean isStart) {
        final boolean isLaunchRecent = SystemUtils.isLaunchRecent(mContext, pkg);
        final ComponentName topActivity = SystemUtils.getActivityTop(mContext);

        ChangeDensityUtils.onForegroundAppChange(pkg);

        mCallChain.invokeNodeMethod("handleActivityChange");
        StringBuilder nodeParam = new StringBuilder();
        nodeParam.append("comming package: ").append(pkg).append(" ++ ");
        nodeParam.append("recent is launcher: ").append(isLaunchRecent).append(" ++ ");
        nodeParam.append("top activity:").append(topActivity);
        nodeParam.append("isStart: ").append(isStart);
        mCallChain.invokeNodeInfo(nodeParam.toString(), "activity change");
        mCallChain.report();

        DelayTask.cancel(TAG);
        DelayTask.submit(TAG, new Runnable() {
            @Override
            public void run() {
                onAppChange(pkg, topActivity, isLaunchRecent, isStart);
            }
        });
    }
}
