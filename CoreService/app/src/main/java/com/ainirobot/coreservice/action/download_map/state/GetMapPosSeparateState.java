package com.ainirobot.coreservice.action.download_map.state;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.download_map.DownloadMapStateEnum;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.DownloadMapBean;

public class GetMapPosSeparateState extends BaseDownloadMapPkgState {
    private boolean isSeparate = false;

    @Override
    protected void onStart(String paramsStr, DownloadMapBean downloadMapBean) {
        Log.d(TAG, "onStart: ");
        sendCommand(0, Definition.CMD_REMOTE_GET_MAP_POS_SEPARATE, "");
    }

    @Override
    public boolean onCmdResponse(int cmdId, String command, String result, String extraData) {
        if (TextUtils.equals(command, Definition.CMD_REMOTE_GET_MAP_POS_SEPARATE)) {
            handlerGetMapPosSeparateResult(result);
            return true;
        }
        return false;
    }

    private void handlerGetMapPosSeparateResult(String result) {
        Log.d(TAG, "handlerGetMapPosSeparateResult:getMapPosSeparate: >" + result + "<");
        if (!TextUtils.isEmpty(result)) {
            //只要不等于0，就是默认的点图分离
            isSeparate = !"0".equals(result);
        }
        Log.d(TAG, "handlerGetMapPosSeparateResult:getMapPosSeparate: " + isSeparate);
        onStateRunSuccess();
    }

    @Override
    protected DownloadMapStateEnum getNextState() {
        Log.d(TAG, "getNextState: isSeparate=" + isSeparate);
        return isSeparate ? DownloadMapStateEnum.GET_CLOUD_MAP_PLACE_LIST
                : DownloadMapStateEnum.UPDATE_PLACE_LIST_TO_NET;
    }

    @Override
    protected Object getData() {
        return isSeparate;
    }
}
