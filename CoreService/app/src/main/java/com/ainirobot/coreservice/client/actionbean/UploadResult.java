package com.ainirobot.coreservice.client.actionbean;

public class UploadResult {

    public static final String UPLOAD_CLIENT_FAILED_ILLEGAL_ARGUMENT = "-101";
    public static final String UPLOAD_CLIENT_FAILED_USER_CANCELED = "-104";
    public static final String UPLOAD_CLIENT_FAILED_UNINITIALIZED = "-201";
    public static final String UPLOAD_CLIENT_FAILED_TIMEOUT = "-202";
    public static final String UPLOAD_CLIENT_FAILED_LOCAL_PATH_NOT_EXIST = "-203";
    public static final String UPLOAD_CLIENT_FAILED_NO_PAIR_UPLOADER = "-204";
    public static final String UPLOAD_CLIENT_FAILED_REGION_NOT_SUPPORT = "-205";
    public static final String UPLOAD_CLIENT_FAILED_BUCKET_NOT_SUPPORT = "-206";

    private String id;
    private int fileType;
    private String serverPath;
    private String clientError;
    private String clientErrorMessage;
    private String serverError;
    private String serverErrorMessage;
    private String data;
    private long cost;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getFileType() {
        return fileType;
    }

    public void setFileType(int fileType) {
        this.fileType = fileType;
    }

    public String getServerPath() {
        return serverPath;
    }

    public void setServerPath(String serverPath) {
        this.serverPath = serverPath;
    }

    public String getClientError() {
        return clientError;
    }

    public void setClientError(String clientError) {
        this.clientError = clientError;
    }

    public String getClientErrorMessage() {
        return clientErrorMessage;
    }

    public void setClientErrorMessage(String clientErrorMessage) {
        this.clientErrorMessage = clientErrorMessage;
    }

    public String getServerError() {
        return serverError;
    }

    public void setServerError(String serverError) {
        this.serverError = serverError;
    }

    public String getServerErrorMessage() {
        return serverErrorMessage;
    }

    public void setServerErrorMessage(String serverErrorMessage) {
        this.serverErrorMessage = serverErrorMessage;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public long getCost() {
        return cost;
    }

    public void setCost(long cost) {
        this.cost = cost;
    }
}
