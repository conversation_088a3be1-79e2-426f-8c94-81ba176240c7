package com.ainirobot.coreservice.config.core;

import android.util.Log;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import java.util.Map;

public class AccountConfig extends BaseConfig {

    private static final String TAG = AccountConfig.class.getSimpleName();

    private boolean isLocalRegisterEnable = false;

    public AccountConfig(JsonObject data) {
        super(data);
    }

    @Override
    public void parseConfig(JsonObject config) {
        isLocalRegisterEnable = false;
        if (config == null) {
            Log.d(TAG, "parseConfig config null");
            return;
        }
        for (Map.Entry<String, JsonElement> entry : config.entrySet()) {
            String enKey = ConfigDictionary.parseAccountConfig(entry.getKey());
            switch (enKey) {
                case "isLocalRegisterEnable":
                    isLocalRegisterEnable = entry.getValue().getAsBoolean();
                    break;
                default:
                    break;
            }
        }
        Log.d(TAG, toString());
    }

    public boolean isLocalRegisterEnable() {
        return isLocalRegisterEnable;
    }

    @Override
    public String toString() {
        return "AccountConfig{"
                + "isEnable='"
                + isEnable
                + '\''
                + ", isLocalRegisterEnable="
                + isLocalRegisterEnable
                + '}';
    }
}
