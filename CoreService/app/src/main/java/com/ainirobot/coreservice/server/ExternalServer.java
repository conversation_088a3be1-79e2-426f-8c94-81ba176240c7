/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.server;

import android.app.ActivityManager;
import android.content.ComponentName;
import android.content.Context;
import android.os.Binder;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.IHWService;
import com.ainirobot.coreservice.IRobotCore;
import com.ainirobot.coreservice.IStatusListener;
import com.ainirobot.coreservice.bean.Command;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.exception.BinderExceptionHandler;
import com.ainirobot.coreservice.config.ServiceConfig;
import com.ainirobot.coreservice.core.CommandManager;
import com.ainirobot.coreservice.core.InternalDef;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.core.RequestManager;
import com.ainirobot.coreservice.core.external.ExternalService;
import com.ainirobot.coreservice.core.external.ExternalServiceManager;
import com.ainirobot.coreservice.core.module.Module;
import com.ainirobot.coreservice.core.module.ModuleManager;
import com.ainirobot.coreservice.core.status.RemoteSubscriber;
import com.ainirobot.coreservice.core.status.StatusManager;
import com.ainirobot.coreservice.core.system.SystemStatus;
import com.ainirobot.coreservice.listener.IActionListener;
import com.ainirobot.coreservice.service.CoreService;

import org.json.JSONException;
import org.json.JSONObject;
import org.json.JSONTokener;

import java.util.List;

public class ExternalServer extends IRobotCore.Stub {
    private static final String TAG = "ExternalServer";

    private CoreService mCore;
    private String packageName;
    private ExternalServiceManager mExternalManager;

    public ExternalServer(CoreService core, String packageName) {
        this.mCore = core;
        this.packageName = packageName;
        this.mExternalManager = core.getExternalServiceManager();
    }

    @Override
    public void registerHWCallback(String name, IHWService cb) {
        long identity = Binder.clearCallingIdentity();
        try {
            Log.d(TAG, "Register HWService : " + name);
            ExternalService service = mExternalManager.getExternalService(name);
            if (service == null) {
                service = new ExternalService(packageName, name);
                mExternalManager.addExternalService(service);
            } else {
                if (service.isPreset()
                        && !service.getPackageName().equals(packageName)) {
                    Log.d(TAG, "Register external service failed : " + packageName + " is error");
                    return;
                }
            }

            ServiceConfig config = mExternalManager.getServiceConfig(name);

            if (RobotOS.NAVIGATION_SERVICE.equals(name)) {
                initEstimateStatus();
            }
            List<Command> supportCmds = cb.mount(name, config);
            if (supportCmds != null) {
                CommandManager commandManager = mCore.getCommandManager();
                commandManager.addCommand(name, supportCmds);
                service.addCommand(supportCmds);
            }
            service.setHardware(cb);
            ModuleManager moduleManager = mCore.getModuleManager();
            String activeModule = moduleManager.getActiveAppModule();
            Module module = null;
            if (!TextUtils.isEmpty(activeModule)) {
                module = mCore.getModuleManager().getModule(activeModule);
                cb.switchAppControl(activeModule, null);

            } else {
                //TODO:目前逻辑依赖于开机home和firstConfig应用自启动，且不在切换APP逻辑里
                ComponentName topActivity = getTopActivity();
                if (topActivity != null) {
                    String packageName = topActivity.getPackageName();
                    if (Definition.HOME_PACKAGE_NAME.equals(packageName)
                            || Definition.FIRST_CONFIG_PACKAGE.equals(packageName)) {
                        module = mCore.getModuleManager().getModule(packageName);
                        cb.switchAppControl(packageName, null);
                    }
                }
            }
            if (null == module || TextUtils.isEmpty(module.getLanguage())) {
                cb.setLanguage(mCore.getRobotInfoManager().getSystemLanguage());
            } else {
                cb.setLanguage(module.getLanguage());
            }
        } catch (Exception e) {
            BinderExceptionHandler.handle(e);
        } finally {
            Binder.restoreCallingIdentity(identity);
        }
    }

    @Override
    public void unregisterHWCallback(String name) {
        ExternalService service = this.mExternalManager.getExternalService(name);
        if (service == null || !TextUtils.equals(packageName, service.getPackageName())) {
            Log.d(TAG, "Unregister external service failed : " + name + " is error");
            return;
        }

        this.mExternalManager.removeExternalService(service);
        Log.d(TAG, "Unregister external service : name = " + name);
    }

    @Override
    public int sendRequest(String reqType, String requestText, String reqParam) {
        Log.d(TAG, "Read request from HW type: " + reqType
                + ", requestText: " + requestText + ", reqParam: " + reqParam);
        RequestManager requestManager = mCore.getRequestManager();
        RequestManager.ReqAction req = requestManager.addReqAction(reqType, reqParam);
        req.reqText = requestText;

        Handler handler = mCore.getRequestHandler();
        Message msg = handler.obtainMessage(InternalDef.MSG_REQUEST_WITH_TYPE);
        Bundle bundle = new Bundle();
        bundle.putInt(InternalDef.BUNDLE_REQUEST_ID, req.reqId);
        msg.setData(bundle);
        handler.sendMessage(msg);
        return req.reqId;
    }

    @Override
    public int sendCommand(String cmdType, String params, boolean isContinue,
                           final IActionListener listener) {
        Log.d(TAG, "Read command, type: " + cmdType
                + ", params: " + params + ", isContinue: " + isContinue);
        int cmdId = mCore.getCoreStateMachine().addCommand(0, cmdType, params, isContinue,
                new LocalApi.OnCmdResponse() {
                    @Override
                    public void onCmdResponse(int cmdId, String command, String result, String extraData) {
                        if (listener != null) {
                            try {
                                listener.onResultWithExtraData(Definition.RESULT_SUCCEED, result, extraData);
                                listener.onResult(Definition.RESULT_SUCCEED, result);
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }
                        }
                    }

                    @Override
                    public void onCmdStatusUpdate(int cmdId, String command, String status, String extraData) {
                        if (listener != null) {
                            try {
                                listener.onStatusUpdateWithExtraData(Definition.RESULT_SUCCEED, status, extraData);
                                listener.onStatusUpdate(Definition.RESULT_SUCCEED, status);
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                });
        if (cmdId < 0) {
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
        Handler coreHandler = mCore.getCoreHandler();
        Message msg = coreHandler.obtainMessage(InternalDef.MSG_CORE_ADD_COMMAND_ACTION);
        coreHandler.sendMessage(msg);
        return cmdId;
    }

    @Override
    public void sendAsyncResponse(String cmdType, int result, String message) {
        if(!Definition.CMD_HEAD_GET_ALL_PERSON_INFOS.equals(cmdType)){
            Log.d(TAG, "Read message from HW : type=" + cmdType + " result=" + message);
        }
        Handler handler = mCore.getResponseHandler();
        Message msg = handler.obtainMessage(InternalDef.MSG_CORE_HW_CMD_RESPONSE);
        Bundle bundle = new Bundle();
        bundle.putString(InternalDef.BUNDLE_TYPE, cmdType);
        String responseMsg = message;
        String extraData = "";
        if (!TextUtils.isEmpty(message)) {
            try {
                Object tokenObject = new JSONTokener(message).nextValue();
                if(tokenObject instanceof JSONObject){
                    JSONObject jsonObject = new JSONObject(message);
                    if (jsonObject.has(Definition.JSON_COMMAND_EXTRA_DATA)) {
                        responseMsg = jsonObject.optString(Definition.JSON_COMMAND_RESULT);
                        extraData = jsonObject.optString(Definition.JSON_COMMAND_EXTRA_DATA);
                    }
                }
            } catch (JSONException e) {
                Log.d(TAG, "sendAsyncResponse message parse JsonObject failed");
            }
        }
        bundle.putString(InternalDef.BUNDLE_RESPONSE, responseMsg);
        bundle.putString(InternalDef.BUNDLE_EXTRA_DATA, extraData);
        msg.setData(bundle);
        handler.sendMessage(msg);
    }

    @Override
    public void sendAsyncStatus(String cmdType, String status) {
        Log.d(TAG, "Read message from HW : type=" + cmdType + " status=" + status);
        Handler handler = mCore.getResponseHandler();
        Message msg = handler.obtainMessage(InternalDef.MSG_CORE_HW_CMD_STATUS);
        Bundle bundle = new Bundle();
        bundle.putString(InternalDef.BUNDLE_TYPE, cmdType);
        String responseStatus = status;
        String extraData = "";
        if (!TextUtils.isEmpty(status)) {
            try {
                Object tokenObject = new JSONTokener(status).nextValue();
                if(tokenObject instanceof JSONObject){
                    JSONObject jsonObject = new JSONObject(status);
                    if (jsonObject.has(Definition.JSON_COMMAND_EXTRA_DATA)) {
                        responseStatus = jsonObject.optString(Definition.JSON_COMMAND_STATUS);
                        extraData = jsonObject.optString(Definition.JSON_COMMAND_EXTRA_DATA);
                    }
                }
            } catch (JSONException ignored) {
            }
        }
        bundle.putString(InternalDef.BUNDLE_RESPONSE, responseStatus);
        bundle.putString(InternalDef.BUNDLE_EXTRA_DATA, extraData);
        msg.setData(bundle);
        handler.sendMessage(msg);
    }

    @Override
    public void sendStatusReport(String serviceName, String type, String params) {
        //TODO: no use of subType currently, set it when necessary.
//        Log.d(TAG, "Send status : " + type + "   " + params);
        StatusManager statusManager = mCore.getStatusManager();
        statusManager.handleStatus(serviceName, type, params);
    }

    @Override
    public void sendExceptionReport(String serviceName, String type, String params) {
        Log.e(TAG, "sendExceptionReport : " + type + "   " + params);

        Handler handler = mCore.getRequestHandler();
        Message msg = handler.obtainMessage(InternalDef.MSG_REQUEST_HW_REPORT);
        Bundle bundle = new Bundle();
//        bundle.putInt(InternalDef.BUNDLE_INT, hwFunction);
        bundle.putString(InternalDef.BUNDLE_TYPE, type);
        bundle.putString(InternalDef.BUNDLE_RESPONSE, params);
        msg.setData(bundle);
        handler.sendMessage(msg);
    }

    @Override
    public String registerStatusListener(String type, IStatusListener listener) {
        StatusManager statusManager = mCore.getStatusManager();
        RemoteSubscriber subscriber = new RemoteSubscriber(listener);
        return statusManager.registerStatusListener(type, subscriber);
    }

    @Override
    public boolean unregisterStatusListener(String id) {
        StatusManager statusManager = mCore.getStatusManager();
        return statusManager.unregisterStatusListener(id);
    }

    @Override
    public boolean startStatusSocket(String type, int socketPort) throws RemoteException {
        String serviceName = mCore.getSocketManager().getServiceName(type);
        ExternalService service = mExternalManager.getExternalService(serviceName);
        return service.startStatusSocket(type, socketPort);
    }

    @Override
    public boolean closeStatusSocket(String type, int socketPort) throws RemoteException {
        String serviceName = mCore.getSocketManager().getServiceName(type);
        ExternalService service = mExternalManager.getExternalService(serviceName);
        return service.closeStatusSocket(type, socketPort);
    }

    /**
     * 获取栈顶Activity
     */
    private ComponentName getTopActivity() {
        ActivityManager manager = (ActivityManager) ApplicationWrapper.getContext().getSystemService(Context.ACTIVITY_SERVICE);
        try {
            List<ActivityManager.RunningTaskInfo> list = manager
                    .getRunningTasks(1);
            if (list != null && list.size() > 0) {
                return list.get(0).topActivity;
            }
        } catch (SecurityException e) {
            e.printStackTrace();
        }
        return null;
    }

    private static void initEstimateStatus() {
        SystemStatus.START_CHARGE.update(InternalDef.POSE_ESTIMATE, false);
        SystemStatus.SET_CHARGE_PILE.update(InternalDef.POSE_ESTIMATE, false);
        SystemStatus.REPOSITION.update(InternalDef.POSE_ESTIMATE, false);
        SystemStatus.REMOTE_REPOSITION.update(InternalDef.POSE_ESTIMATE, false);
        SystemStatus.MAP_DRIFT.update(InternalDef.POSE_ESTIMATE, false);
    }
}
