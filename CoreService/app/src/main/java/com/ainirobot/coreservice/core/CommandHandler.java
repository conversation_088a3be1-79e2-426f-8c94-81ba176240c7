/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.core;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;

public class CommandHandler extends Handler {

    //TODO:

    public CommandHandler(Looper looper) {
        super(looper);
        //TODO: register HWService status report
        //TODO: if command finished, send next command and put coreStateMachine into running state
    }

    public void handleMessage(Message msg) {

    }
}