/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.action;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.action.StopCommandList.IStopCommandChecker;
import com.ainirobot.coreservice.action.StopCommandList.StopCommand;
import com.ainirobot.coreservice.bi.report.SdkLeadingFinishReport;
import com.ainirobot.coreservice.bi.report.SdkLeadingStartReport;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.Definition.TrackMode;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.actionbean.LeadingParams;
import com.ainirobot.coreservice.client.actionbean.Person;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.utils.MessageParser;
import com.ainirobot.robotlog.RobotLog;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Timer;
import java.util.TimerTask;

public class LeadingAction extends AbstractAction<LeadingParams> {

    private static final String TAG = "LeadingAction";

    private static final int MSG_GET_PERSON_INFO = 1;
    private static final double DISTANCE_DESTINATION_NEAR = 3.0d;
    private static final long DEFAULT_DETECT_DELAY = 2000;
    private static final long DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT = 300 * 1000;

    private enum LeadingState {
        IDLE, PREPARE, LEADING, LEADING_DESTINATION_NEAR,
        LEADING_GUEST_LOST, LEADING_GUEST_FARAWAY, LEAD_DONE
    }

    private String mPoseListener;
    private double mDestX = Double.NaN;
    private double mDestY = Double.NaN;
    private LeadingState mLeadingState = LeadingState.IDLE;
    private Handler mEventHandler;
    private int mReqId;
    private int mPersonId;
    private String mGuestName;
    private String mDestinationName;
    private volatile long mLostTimeInterval;
    private Timer mLostTimer;
    private Timer mWaitTimer;
    private long mDetectDelay;
    private long mAvoidTimeout;
    private double mAvoidDistance;
    private double mMaxDistance;
    private long mWaitTimeout;
    private Pose mFlagPose;
    private boolean isNavigating = false;
    private SdkLeadingStartReport leadingStartReport;
    private SdkLeadingFinishReport leadingFinishReport;
    private double mLinearSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED;
    private double mAngularSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED;
    /**多机模式等待超时时长，默认300s **/
    private long mMultiWaitTimeout;
    /**是否是在多机避障状态，如果是则停止处理机器N秒不动退出导航的逻辑 **/
    private volatile boolean isMultipleWaitStatus = false;
    private Timer mMultiWaitTimer;

    LeadingAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_LEAD, resList);

//        if (Looper.myLooper() == null) {
//            Looper.prepare();
//        }

        leadingStartReport = new SdkLeadingStartReport();
        leadingFinishReport = new SdkLeadingFinishReport();

        HandlerThread mEventThread = new HandlerThread("EventHandler");
        mEventThread.start();
        mEventHandler = new Handler(mEventThread.getLooper()) {
            @Override
            public void handleMessage(Message msg) {
                switch (msg.what) {
                    case MSG_GET_PERSON_INFO:
                        Log.d(TAG, "Start get all person info");
                        startLostTimer();
                        mApi.getAllPersonInfos(mReqId, Definition.JSON_HEAD_CONTINUOUS);
                        break;

                    default:
                        break;
                }
            }
        };
    }

    @Override
    protected boolean startAction(LeadingParams parameter, LocalApi api) {
        if (mLeadingState != LeadingState.IDLE) {
            Log.i(TAG, "current leading state is not idle, is " + mLeadingState + "  return");
            return false;
        }
        //processCheckRobotLocation();

        mApi.setTrackTarget(mReqId, null, mPersonId, TrackMode.BODY_LEAD);
        return true;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        RobotLog.d("Leading stop check");
        updateMultipleRobotWaitingStatus(false);
        canceMultilWaitTimer();
        destroyLostTimer();
        mApi.stopSendPersonInfos();
        unregisterStatusListener(mPoseListener);

        mLeadingState = LeadingState.IDLE;
        if (isResetHW) {
            mApi.resetHead(mReqId);
            mApi.switchCamera(mReqId, Definition.JSON_HEAD_FORWARD);
            mApi.stopTrack(mReqId);
        }
        //mApi.stopNavigation(mReqId);
        mLostTimeInterval = -1;
        mGuestName = null;
        mReqId = -1;
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_IS_IN_LOCATION:
                handleIsInLocation(result);
                break;

            case Definition.CMD_NAVI_GET_LOCATION:
                handleGetLocation(result);
                break;

            case Definition.CMD_HEAD_MOVE_HEAD:
                handleMoveHead(result);
                break;

            case Definition.CMD_HEAD_SWITCH_CAMERA:
                handleSwitchCamera(result);
                break;

            case Definition.CMD_HEAD_GET_ALL_PERSON_INFOS:
                handleGetPersonInfo(result);
                break;

            case Definition.CMD_NAVI_GO_LOCATION:
                handleNavigationResult(result);
                break;

            case Definition.CMD_HEAD_SET_TRACK_TARGET:
                handleTrackTarget(result);
                break;

            case Definition.CMD_NAVI_IS_ESTIMATE:
                handlePoseEstimate(result);
                break;
            case Definition.CMD_CAN_GET_ROTATE_SUPPORT:
                handleRotateSupport(result);
                break;
            default:
                break;
        }
        return true;
    }

    private void handleRotateSupport(String result) {
        Log.d(TAG, "handleRotateSupport  result  " + result);
        try {
            JSONObject jsonObject = new JSONObject(result);
            String hardWareVersion = jsonObject.optString(Definition.JSON_CAN_BOARD_APP_VERSION);
            if ("H401".equals(hardWareVersion)) {
                processStartLead();
            } else {
                processSwitchCamera();
            }
        } catch (JSONException e) {
            Log.e(TAG, "handleRotateSupport exception");
            processSwitchCamera();
        }
    }

    @Override
    public boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_GO_LOCATION:
                processNavigationStatus(status);
                return true;
        }
        return false;
    }

    private void processCheckPoseEstimate() {
        Log.d(TAG, "Check pose estimate");
        mApi.isRobotEstimate(mReqId);
    }

    private void processCheckRobotLocation() {
        Log.i(TAG, "processCheckRobotLocation.....");
        mLeadingState = LeadingState.PREPARE;
        JSONObject obj = makeJSONObjectForcheckInPlace(mDestinationName);
        mApi.isRobotInlocations(mReqId, obj.toString());
    }

    private void processCheckDestinition() {
        Log.i(TAG, "processCheckDestinition.....");
        mApi.getLocation(mReqId, mDestinationName);
    }

    private void processSwitchCamera() {
        Log.i(TAG, "processSwitchCamera.....");
        mApi.resetHead(mReqId);
        mApi.switchCamera(mReqId, Definition.JSON_HEAD_BACKWARD);
    }

    private void processStartLead() {
        Log.i(TAG, "processStartLead..... : " + mDetectDelay);
        mLeadingState = LeadingState.LEADING;
        registerPoseListener();
        startNavigation();
        mEventHandler.sendEmptyMessageDelayed(MSG_GET_PERSON_INFO, mDetectDelay);
        onStatusUpdate(Definition.STATUS_LEAD_NORMAL, "start lead, all normal!");

        leadingStartReport.fillData(mDestinationName)
                .report();
    }

    private void processNearDestination() {
        Log.i(TAG, "processNearDestination.....");
//        if(mLeadingState != LeadingState.LEADING){
//            mApi.goPlace(mReqId, mDestinationName);
//        }
        mLeadingState = LeadingState.LEADING_DESTINATION_NEAR;
//        mApi.stopTrack(mReqId);
//        mApi.resetHead(mReqId);
        mApi.stopSendPersonInfos();
        onStatusUpdate(Definition.STATUS_DEST_NEAR, "Near destination");
    }

    private void processGuestLost() {
        Log.i(TAG, "processGuestLost.....");
        mLeadingState = LeadingState.LEADING_GUEST_LOST;
//        mApi.stopNavigation(mReqId);
        onStatusUpdate(Definition.STATUS_GUEST_LOST, "guest lost");
    }

    private void processRelead() {
        Log.i(TAG, "processRelead.....");
        mLeadingState = LeadingState.LEADING;
        startNavigation();
        // mApi.getPersonInfoByName(mReqId, mGuestRegistName, Definition.JSON_HEAD_CONTINUOUS);
    }

    private void processGuestFaraway() {
        if (mLeadingState == LeadingState.LEADING_DESTINATION_NEAR
                || mLeadingState == LeadingState.LEADING_GUEST_FARAWAY) {
            return;
        }

        Log.i(TAG, "processGuestFaraway.....");
        mLeadingState = LeadingState.LEADING_GUEST_FARAWAY;
        onStatusUpdate(Definition.STATUS_GUEST_FARAWAY, "guest is faraway");
        startWaitTimer();
    }

    private void processLeadDone() {
        Log.i(TAG, "processLeadDone.......");
        mLeadingState = LeadingState.LEAD_DONE;
//        mApi.stopTrack(mReqId);
//        mApi.resetHead(mReqId);
//        mApi.getPersonInfoByName(mReqId, mGuestRegistName, Definition.JSON_HEAD_STOP);
        //onStatusUpdate(Definition.STATUS_LEAD_DESTINATION_ARRIVE, "destination arraive");
        //this.stop();
        onResult(Definition.RESULT_OK, "lead ok");

        leadingFinishReport.fillData()
                .report();
    }

    private void processNavigationStatus(String status) {
        switch (status) {
            case Definition.NAVIGATION_OCCLUDED:
                onStatusUpdate(Definition.STATUS_GOAL_OCCLUDED, "The target is occluded");
                break;

            case Definition.NAVIGATION_OCCLUDED_END:
                onStatusUpdate(Definition.STATUS_GOAL_OCCLUDED_END, "The occluded end");
                break;

            case Definition.NAVIGATION_AVOID:
                onStatusUpdate(Definition.STATUS_NAVI_AVOID, "The local path search failed");
                break;

            case Definition.NAVIGATION_AVOID_END:
                onStatusUpdate(Definition.STATUS_NAVI_AVOID_END, "The local path search succeed");
                break;

            case Definition.NAVIGATION_OUT_MAP:
                onStatusUpdate(Definition.STATUS_NAVI_OUT_MAP, "The goal is out of map or more dangerous");
                break;

            case Definition.NAVIGATION_GLOBAL_PATH_FAILED:
                onStatusUpdate(Definition.STATUS_NAVI_GLOBAL_PATH_FAILED, "The global is path search failed");
                break;

            case Definition.NAVIGATION_OBSTACLES_AVOID:
                onStatusUpdate(Definition.STATUS_NAVI_OBSTACLES_AVOID, "obstacles avoid");
                break;

            case Definition.NAVIGATION_STARTED:
                onStatusUpdate(Definition.STATUS_START_NAVIGATION, "Navigation has been started");
                break;

            case Definition.NAVIGATION_MULTI_ROBOT_WAITING:
                updateMultipleRobotWaitingStatus(true);
                startMultiWaitingTimer();
                onStatusUpdate(Definition.STATUS_NAVI_MULTI_ROBOT_WAITING, "Multiple robots are waiting to avoid obstacles");
                break;

            case Definition.NAVIGATION_MULTI_ROBOT_WAITING_END:
                updateMultipleRobotWaitingStatus(false);
                canceMultilWaitTimer();
                onStatusUpdate(Definition.STATUS_NAVI_MULTI_ROBOT_WAITING_END, "Multiple robots passageways returned to normal");
                break;
            default:
                Log.i(TAG, "Command status: " + status +  " doesn't be handled");
                break;
        }
    }

    private void handlePoseEstimate(String result) {
        if (!"true".equals(result)) {
            onError(Definition.ERROR_NOT_ESTIMATE, "Not pose estimate");
            return;
        }
        processCheckRobotLocation();
    }

    private void handleIsInLocation(String result) {
        try {
            JSONObject json = new JSONObject(result);
            boolean isInLocation = json.optBoolean(Definition.JSON_NAVI_IS_IN_LOCATION, false);
            if (isInLocation) {
                //stop();
                onError(Definition.ERROR_IN_DESTINATION, "already in destination" + mDestinationName);
            } else {
                processCheckDestinition();
            }
        } catch (JSONException e) {
            e.printStackTrace();
            onError(Definition.ERROR_IN_DESTINATION, " Judge at the " + mDestinationName + " failed");
        }
    }

    private void startNavigation() {
        mFlagPose = null;
        isNavigating = true;
        mApi.goPlace(mReqId, mDestinationName, mLinearSpeed, mAngularSpeed);
    }

    private void handleGetLocation(String result) {
        try {
            JSONObject json = new JSONObject(result);
            boolean isExist = json.getBoolean(Definition.JSON_NAVI_SITE_EXIST);
            if (isExist) {
                mDestX = json.getDouble(Definition.JSON_NAVI_POSITION_X);
                mDestY = json.getDouble(Definition.JSON_NAVI_POSITION_Y);

                boolean openHeadRotate = true;
                try {
                    Context context = ApplicationWrapper.getContext().createPackageContext("com.ainirobot.moduleapp", Context.CONTEXT_IGNORE_SECURITY);
                    SharedPreferences sharedPreferences = context.getSharedPreferences("developer_configuration_item", Context.MODE_MULTI_PROCESS);
                    openHeadRotate = sharedPreferences.getBoolean("open_head_rotate_item", true);
                } catch (PackageManager.NameNotFoundException e) {
                    e.printStackTrace();
                }

                if (!openHeadRotate) {
                    Log.e(TAG, "openHeadRotate false");
                    processSwitchCamera();
                    return;
                }
                mApi.getCanRotateSupport(0);
            } else {
                //stop();
                onError(Definition.ERROR_DESTINATION_NOT_EXIST, mDestinationName + " is not exist!");
            }
        } catch (JSONException e) {
            e.printStackTrace();
            //stop();
            onError(Definition.ERROR_DESTINATION_NOT_EXIST, "Get " + mDestinationName + "failed");
        }
    }

    private void handleMoveHead(String result) {
        Log.d(TAG, "Move head result : " + result);
        boolean isSucceed = MessageParser.parseResult(result);
        if (!isSucceed) {
            onError(Definition.ERROR_HEAD, "move head failure!");
        }
    }

    private void handleSwitchCamera(String result) {
        Log.d(TAG, "Switch camera result : " + result);
        boolean isSucceed = MessageParser.parseResult(result);
        switch (mLeadingState) {
            case PREPARE:
                if (!isSucceed) {
                    Log.i(TAG, "switch backforward camera failure");
                    //this.stop();
                    onError(Definition.ERROR_SWITCH_CAMERA, "camera switch to back failure!");
                    //onResult(Definition.RESULT_FAILURE, "camera switch failure");
                    return;
                } else {
                    processStartLead();
                }
                break;

            case IDLE:
//                if (!isSucceed) {
//                    onError(Definition.ERROR_SWITCH_CAMERA, "camera switch to front failure!");
//                }
                break;
        }
    }

    private void handleGetPersonInfo(String params) {
        Log.i(TAG, "Handle person info, lead state:" + mLeadingState + ", person info:" + params);
//        Person person = MessageParser.parseFaceInfo(null, params,12d);
        Person person = MessageParser.parseFaceInfo(mPersonId, params);
        startLostTimer();
        if (person == null) {
            return;
        }

        double distance = person.getFaceInfo().getDistance();
        switch (mLeadingState) {
            case LEADING:
                if (distance >= mMaxDistance) {
                    processGuestFaraway();
                }
                break;

            case LEADING_DESTINATION_NEAR:
                break;

            case LEADING_GUEST_FARAWAY:
                if (distance > 0 && distance < mMaxDistance) {
                    cancelWaitTimer();
                    onStatusUpdate(Definition.STATUS_GUEST_NEAR, "guest near");
                }
                break;

            case LEADING_GUEST_LOST:
                //processRelead();
                onStatusUpdate(Definition.STATUS_GUEST_APPEAR, "guest appear");
                break;

            default:
                break;
        }
    }

    private void handleTrackTarget(String params) {
        try {
            Log.d(TAG, "Set track target result : " + params);
            JSONObject json = new JSONObject(params);
            String status = json.getString("status");
            switch (status) {
                case Definition.RESPONSE_OK:
                    //processCheckRobotLocation();
                    processCheckPoseEstimate();
                    break;

                case Definition.RESPONSE_TARGET_NOT_FOUND:
                    onError(Definition.ERROR_TARGET_NOT_FOUND, "Target not found");
                    break;

                case Definition.RESPONSE_ALREADY_IN_MODE:
                    Log.d(TAG, "Current already int track mode");
                    //processCheckRobotLocation();
                    processStartLead();
                    break;
            }
        } catch (JSONException e) {
            onError(Definition.ERROR_SET_TRACK_FAILED, "Set track target failed");
        }
    }

    private void handleNavigationResult(String result) {
        if (mLeadingState == LeadingState.IDLE) {
            return;
        }
        isNavigating = false;
        switch (result) {
            case Definition.NAVIGATION_OK:
                switch (mLeadingState) {
                    case LEADING:
                    case LEADING_DESTINATION_NEAR:
                    case LEADING_GUEST_LOST:
                    case LEADING_GUEST_FARAWAY:
                        processLeadDone();
                        break;
                }
                break;
            case Definition.NAVIGATION_FAILED:
                //stop();
                onError(Definition.ERROR_DESTINATION_CAN_NOT_ARRAIVE, "navigation failed");
                break;
            case Definition.NAVIGATION_CANCELED:

                break;
        }
    }

    @Override
    protected boolean verifyParam(LeadingParams params) {

        if (params == null) {
            Log.i(TAG, "params is null ,return !!");
            return false;
        }

        Log.i(TAG, "params is " + params.toString());
        if (params.getPersonId() < 0) {
            return false;
        }

        if (TextUtils.isEmpty(params.getDestinationName())) {
            return false;
        }

        if (params.getLostTimer() <= 2000L) {
            params.setLostTimer(2000L);
        }
        mReqId = params.getReqId();
        mPersonId = params.getPersonId();
        mLostTimeInterval = params.getLostTimer();
        mGuestName = params.getCustomerName();

        mDestinationName = params.getDestinationName();
        mDetectDelay = params.getDetectDelay();
        mDetectDelay = (mDetectDelay == 0 ? DEFAULT_DETECT_DELAY : mDetectDelay);
        mMaxDistance = params.getMaxDistance();
        mMaxDistance = (mMaxDistance == 0 ? Double.MAX_VALUE : mMaxDistance);

        mAvoidTimeout = params.getAvoidTimeout();
        mAvoidDistance = params.getAvoidDistance();
        mWaitTimeout = params.getWaitTimeout();
        mLinearSpeed = params.getLinearSpeed();
        mAngularSpeed = params.getAngularSpeed();
        long waitTimeout = params.getMultipleWaitTime();
        mMultiWaitTimeout = (waitTimeout <= 0) ? DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT: waitTimeout;
        isMultipleWaitStatus = false;
        return true;
    }

    private void registerPoseListener() {
        mPoseListener = registerStatusListener(Definition.STATUS_POSE, new StatusListener() {
            @Override
            public void onStatusUpdate(String data) {
                Pose pose = mGson.fromJson(data, Pose.class);
                onPoseUpdate(pose);
            }
        });
    }

    private synchronized void onPoseUpdate(Pose pose) {
        if (mLeadingState == LeadingState.IDLE
                || mLeadingState == LeadingState.LEAD_DONE) {
            return;
        }

        double mDestDistance = 0;
        if (isNavigating && isAborted(pose)) {
            onError(Definition.ERROR_DESTINATION_CAN_NOT_ARRAIVE, "Timeout");
            return;
        }

        if (!Double.isNaN(mDestX)) {
            mDestDistance = Math.sqrt(Math.pow((mDestX - pose.getX()), 2)
                    + Math.pow((mDestY - pose.getY()), 2));
            //TODO: update status, distance to the dest
        }

        if (isNavigating
                && mLeadingState != LeadingState.LEADING_DESTINATION_NEAR
                && mDestDistance > 0
                && mDestDistance < DISTANCE_DESTINATION_NEAR) {
            processNearDestination();
        }
    }

    private void startWaitTimer() {
        cancelWaitTimer();
        mWaitTimer = new Timer();
        mWaitTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (mLeadingState != LeadingState.LEADING_GUEST_FARAWAY) {
                    return;
                }
                mApi.resetHead(mReqId);
                onStatusUpdate(Definition.STATUS_GUEST_WAIT_TIMEOUT, "guest wait timeout");
            }
        }, mWaitTimeout);
    }

    private void cancelWaitTimer() {
        if (mWaitTimer != null) {
            mWaitTimer.cancel();
            mWaitTimer = null;
        }
    }

    private synchronized void startLostTimer() {
        destroyLostTimer();
        Log.d(TAG, "Start lost timer : " + mLeadingState);
        if (mLeadingState == LeadingState.IDLE) {
            return;
        }
        mLostTimer = new Timer();
        mLostTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (mLeadingState == LeadingState.IDLE) {
                    return;
                }
                synchronized (mLeadingState) {
                    Log.i(TAG, "lost timer arrive, lead state:" + mLeadingState);
                    switch (mLeadingState) {
                        case LEADING_DESTINATION_NEAR:
                            break;
                        case LEADING:
                            processGuestLost();
                            break;
                        case LEADING_GUEST_FARAWAY:
                            break;
                        default:
                            break;
                    }
                }
            }
        }, mLostTimeInterval);
    }

    private void destroyLostTimer() {
        if (mLostTimer != null) {
            mLostTimer.cancel();
            mLostTimer = null;
        }
    }

    private JSONObject makeJSONObjectForcheckInPlace(String strDestinationName) {
        JSONObject obj = new JSONObject();
        try {
            obj.put(Definition.JSON_NAVI_TARGET_PLACE_NAME, strDestinationName);
            obj.put(Definition.JSON_NAVI_COORDINATE_DEVIATION, 1.0f);
            //int result = ModuleSendCmd.getInstance().isRobotInlocations(mReqId, obj.toString());
            //KRobotLog.d(TAG, "isRobotInlocations result:" + result);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return obj;
    }

    private boolean isAborted(Pose pose) {
        double distance = pose.getDistance(mFlagPose);
        if (!isMultipleWaitStatus && Double.compare(distance, mAvoidDistance) < 0) {
            long movingTime = pose.getTime() - mFlagPose.getTime();
            if (movingTime > mAvoidTimeout) {
                return true;
            }
        } else {
            mFlagPose = pose;
        }
        return false;
    }
    /**
     * 更新多机避障状态，如果是多机避障状态，则机器人超时等待策略需忽略
     * @param isWaiting 是否正在多机避障等待状态
     */
    private void updateMultipleRobotWaitingStatus(boolean isWaiting){
        isMultipleWaitStatus = isWaiting;
    }

    private void startMultiWaitingTimer() {
        cancelWaitTimer();
        mMultiWaitTimer = new Timer();
        mMultiWaitTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (!isRunning()) {
                    return;
                }
                onError(Definition.ERROR_MULTI_ROBOT_WAITING_TIMEOUT, "multiple robot wait timeout");
            }
        }, mMultiWaitTimeout);
    }

    private void canceMultilWaitTimer() {
        if (mMultiWaitTimer != null) {
            mMultiWaitTimer.cancel();
            mMultiWaitTimer = null;
        }
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return new ArrayList<StopCommand>() {{
            add(new StopCommand(Definition.CMD_NAVI_STOP_NAVIGATION, null));
//            add(new StopCommand(Definition.CMD_HEAD_STOP_TRACK_TARGET, null));
        }};
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return new IStopCommandChecker() {
            @Override
            public boolean executeSuccess(String command, String result) {
                RobotLog.d("Leading stop check : " + command);
                switch (command) {
                    case Definition.CMD_HEAD_STOP_TRACK_TARGET:
                    case Definition.CMD_NAVI_STOP_NAVIGATION:
                        return true;
                }
                return false;
            }
        };
    }
}
