/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.client.person;

import android.content.Context;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.IPersonApi;
import com.ainirobot.coreservice.IRobotBinderPool;
import com.ainirobot.coreservice.client.BaseSubApi;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.listener.Person;
import com.ainirobot.coreservice.client.messagedispatcher.PersonDispatcher;
import com.ainirobot.coreservice.listener.IActionListener;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.util.ArrayList;
import java.util.List;

public class PersonApi extends BaseSubApi {

    private static final String TAG = PersonApi.class.getSimpleName();

    private static PersonApi instance;
    private IPersonApi mApi;
    private Gson mGson;

    private final ArrayList<PersonListener> mPersonListenerList = new ArrayList<>();
    private PersonListener mPersonListener;
    private boolean mIsReconnect = false;

    private PersonApi() {
        mGson = new Gson();
        startNewThread(TAG);
    }

    public static synchronized PersonApi getInstance() {
        if (instance == null) {
            instance = new PersonApi();
        }
        return instance;
    }

    @Override
    public void onConnect(IRobotBinderPool pool, Context context) {
        try {
            mApi = IPersonApi.Stub.asInterface(pool.queryBinder(Definition.BIND_PERSON,
                    context.getPackageName()));
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        if (mIsReconnect) {
            synchronized (mPersonListenerList) {
                if (mPersonListenerList.size() > 0) {
                    try {
                        Log.d(TAG, "register person listener");
                        PersonDispatcher personDispatcher = mMessageDispatcher.obtainPersonDispatcher(
                                mHandlerThread.getLooper(), mPersonListener);
                        mApi.setPersonListener(personDispatcher);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
            }
            mIsReconnect = false;
        }
    }

    @Override
    public void onDisconnect() {
        mIsReconnect = true;
    }

    public boolean registerPersonListener(PersonListener listener) {
        if (mApi == null || listener == null) {
            return false;
        }

        if (mPersonListener == null) {
            mPersonListener = new PersonListener() {
                @Override
                public void personChanged() {
                    synchronized (mPersonListenerList) {
                        for (PersonListener listenerItem : mPersonListenerList) {
                            listenerItem.personChanged();
                        }
                    }
                }
            };
        }

        synchronized (mPersonListenerList) {
            if (mPersonListenerList.contains(listener)) {
                return true;
            } else {
                mPersonListenerList.add(listener);
                try {
                    Log.d(TAG, "register person listener");
                    PersonDispatcher personDispatcher = mMessageDispatcher.obtainPersonDispatcher(
                            mHandlerThread.getLooper(), mPersonListener);
                    return mApi.setPersonListener(personDispatcher);
                } catch (RemoteException e) {
                    e.printStackTrace();
                    return false;
                }
            }
        }
    }

    public void unregisterPersonListener(PersonListener listener) {
        if (mApi == null || listener == null) {
            return;
        }

        synchronized (mPersonListenerList) {
            if (mPersonListenerList.remove(listener) && mPersonListenerList.size() == 0) {
                try {
                    Log.d(TAG, "unregister person listener");
                    mApi.releasePersonListener();
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public List<Person> getAllPersons() {
        if (mApi == null) {
            return null;
        }
        try {
            return getPersonListFromString(mApi.getAllPersons());
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return null;
    }

    public List<Person> getAllPersons(double maxDistance) {
        if (mApi == null) {
            return null;
        }
        try {
            return getPersonListFromString(mApi.getAllPersonsByDistance(maxDistance));
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return null;
    }

    public List<Person> getAllFaceList() {
        if (mApi == null) {
            return null;
        }
        try {
            return getPersonListFromString(mApi.getAllFaceList());
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return null;
    }

    public List<Person> getAllFaceList(double maxDistance) {
        if (mApi == null) {
            return null;
        }
        try {
            return getPersonListFromString(mApi.getAllFaceListByDistance(maxDistance));
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return null;
    }

    public List<Person> getCompleteFaceList() {
        if (mApi == null) {
            return null;
        }
        try {
            return getPersonListFromString(mApi.getCompleteFaceList());
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return null;
    }

    public List<Person> getCompleteFaceList(double maxDistance) {
        if (mApi == null) {
            return null;
        }
        try {
            return getPersonListFromString(mApi.getCompleteFaceListByDistance(maxDistance));
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return null;
    }

    public List<Person> getAllBodyList() {
        if (mApi == null) {
            return null;
        }
        try {
            return getPersonListFromString(mApi.getAllBodyList());
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return null;
    }

    public List<Person> getAllBodyList(double maxDistance) {
        if (mApi == null) {
            return null;
        }
        try {
            return getPersonListFromString(mApi.getAllBodyListByDistance(maxDistance));
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return null;
    }

    public Person getFocusPerson() {
        if (mApi == null) {
            return null;
        }
        try {
            return getPersonFromString(mApi.getFocusPerson());
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return null;
    }

    public boolean registerById(int personId, String name, CommandListener listener) {
        if (mApi == null) {
            return false;
        }

        try {
            IActionListener actionListener = mMessageDispatcher.obtainActionDispatcher(
                    mHandlerThread.getLooper(), listener);
            return mApi.registerById(personId, name, actionListener);
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }

    public boolean registerByPic(String picturePath, String name, CommandListener listener) {
        if (mApi == null) {
            return false;
        }

        try {
            IActionListener actionListener = mMessageDispatcher.obtainActionDispatcher(
                    mHandlerThread.getLooper(), listener);
            return mApi.registerByPic(picturePath, name, actionListener);
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }

    public boolean recognizeById(int personId, CommandListener listener) {
        if (mApi == null) {
            return false;
        }

        try {
            IActionListener dispatcher = mMessageDispatcher.obtainActionDispatcher(
                    mHandlerThread.getLooper(), listener);
            return mApi.recognizeById(personId, dispatcher);
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }

    public boolean recognizeByPic(String picturePath, CommandListener listener) {
        if (mApi == null) {
            return false;
        }

        try {
            IActionListener dispatcher = mMessageDispatcher.obtainActionDispatcher(
                    mHandlerThread.getLooper(), listener);
            return mApi.recognizeByPic(picturePath, dispatcher);
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }

    public String getMultipleModeInfos(int index) {
        if (mApi == null) {
            return null;
        }

        try {
            return mApi.getMultipleModeInfos(index);
        } catch (RemoteException e) {
            return null;
        }
    }

    private Person getPersonFromString(String message) {
        if (TextUtils.isEmpty(message)) {
            return null;
        }
        return mGson.fromJson(message, new TypeToken<Person>() {
        }.getType());
    }

    private List<Person> getPersonListFromString(String message) {
        if (TextUtils.isEmpty(message)) {
            return null;
        }
        return mGson.fromJson(message, new TypeToken<List<Person>>() {
        }.getType());
    }
}
