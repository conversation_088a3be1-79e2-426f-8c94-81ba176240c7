package com.ainirobot.coreservice.inspection;

import android.content.res.AssetManager;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.client.actionbean.InspectionBean;
import com.ainirobot.coreservice.client.actionbean.InspectionResult;
import com.ainirobot.coreservice.config.RobotConfig;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Set;

public class InspectionStatusManager {
    private static InspectionStatusManager mInspectionStatusManager;
    private InspectionBean mInspectionBean = null;
    private InspectionResult mInspectionResult=null;
    private HashMap<String, String[]> mInspectList;

    public static InspectionStatusManager getmInspectionStatusManager() {
        if (mInspectionStatusManager == null) {
            mInspectionStatusManager = new InspectionStatusManager();
        }
        return mInspectionStatusManager;
    }

    private InspectionStatusManager() {
        try {
            //mInspectList = RobotConfig.getInstance().getInspectionConfig().getOption();
            mInspectList = new HashMap<>();
        } catch (NullPointerException e) {
            e.printStackTrace();
        }
    }

    public InspectionResult getInspectionResult() {
        return mInspectionResult;
    }

    public void setInspectionResult(InspectionResult inspectionResult) {
        this.mInspectionResult = inspectionResult;
    }

    public void setInspectionBean(InspectionBean bean) {
        mInspectionBean = bean;
    }

    public InspectionBean getInspectionBean() {
        return mInspectionBean;
    }

    public Set<String> getInspectList() {
        return mInspectList.keySet();
    }

    public String[] getChildInspectList(String key) {
        return mInspectList.get(key);
    }

    public String getJson(String fileName) {
        StringBuilder stringBuilder = new StringBuilder();
        BufferedReader bf = null;
        try {
            AssetManager assetManager = ApplicationWrapper.getContext().getAssets();
            bf = new BufferedReader(new InputStreamReader(
                    assetManager.open(fileName)));
            String line;
            while ((line = bf.readLine()) != null) {
                stringBuilder.append(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        finally {
            if (bf != null) {
                try {
                    bf.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return stringBuilder.toString();
    }
}
