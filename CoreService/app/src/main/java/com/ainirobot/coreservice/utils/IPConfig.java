/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.utils;

import android.os.Environment;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;
import java.util.Properties;

public class IPConfig {

    private static final String CONFIG_DIR = "/robot/config";
    private static final String CONFIG_FILE = "ip.properties";
    private static final String IP_NAVIGATION = "navigation";
    private static final String IP_TX1 = "tx1";
    private static final String IP_AIUI = "aiui";

    public static String getNavIp() {
        return getConfigIp(IP_NAVIGATION);
    }

    public static void setNavIp(String ip) {
        saveConfigIp(IP_NAVIGATION, ip);
    }

    public static String getTX1Ip() {
        return getConfigIp(IP_TX1);
    }

    public static boolean setTX1Ip(String ip) {
        return saveConfigIp(IP_TX1, ip);
    }

    public static String getAIUIIp() {
        return getConfigIp(IP_AIUI);
    }

    public static boolean setAIUIIp(String ip) {
        return saveConfigIp(IP_AIUI, ip);
    }

    private static String getConfigIp(String key) {
        Properties properties = getProperties();
        if (properties != null) {
            key = getLocalIp() + "_" + key;
            return properties.getProperty(key);
        }
        return null;
    }

    private static boolean saveConfigIp(String key, String ip) {
        Properties properties = getProperties();
        if (properties != null) {
            key = getLocalIp() + "_" + key;
            properties.setProperty(key, ip);
            return saveProperties(properties);
        }
        return false;
    }

    private static Properties getProperties() {
        Properties properties = new Properties();
        File file = getConfigFile();
        try {
            FileInputStream fis = new FileInputStream(file);
            properties.load(fis);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return properties;
    }

    private static boolean saveProperties(Properties properties) {
        File file = getConfigFile();
        try {
            FileOutputStream fos = new FileOutputStream(file);
            properties.store(fos, "ip");
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    private static File getConfigFile() {
        File root = Environment.getExternalStorageDirectory();
        File dir = new File(root, CONFIG_DIR);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        File file = new File(dir, CONFIG_FILE);
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return file;
    }


    public static String getLocalIp() {
        try {
            Enumeration<NetworkInterface> nets = NetworkInterface.getNetworkInterfaces();
            while (nets.hasMoreElements()) {
                NetworkInterface net = nets.nextElement();
                Enumeration<InetAddress> addresses = net.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress inetA = addresses.nextElement();
                    if (inetA instanceof Inet4Address && !inetA.isLoopbackAddress()) {
                        return inetA.getHostAddress();
                    }
                }
            }
            return null;
        } catch (SocketException e) {
            return null;
        }
    }

}
