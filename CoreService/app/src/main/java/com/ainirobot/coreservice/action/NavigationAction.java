/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.action;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.StopCommandList.IStopCommandChecker;
import com.ainirobot.coreservice.action.StopCommandList.StopCommand;
import com.ainirobot.coreservice.bi.report.SdkNavigateFinishReport;
import com.ainirobot.coreservice.bi.report.SdkNavigateStartReport;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.actionbean.NavigationBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.utils.FileUtils;
import com.ainirobot.coreservice.utils.NavigationStatusUtils;
import com.ainirobot.robotlog.RobotLog;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Timer;
import java.util.TimerTask;

public class NavigationAction extends AbstractAction<NavigationBean> {
    private static final String TAG = "NavigationAction";

    //if 20 seconds moving distance less than 0.1 meters is that navigation failed
    private static final double MIN_DISTANCE = 0.1; //unit meter
    private static final long DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT = 3600 * 1000;

    private enum NavigationState {
        IDLE, PREPARE, NAVIGATION, STOPPED
    }

    private int mReqId;
    private String mDestination;
    private double mLinearSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED;
    private double mAngularSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED;
    private boolean mAdjustAngle = false;
    /**到达目标点的区域半径，如果未到目标点正中心位置，到达周边距离点的范围。默认0.0D，即必须到达中心点 */
    private double mDestinationRange = 0.0D;
    private Pose mDestPose;
    private double mCoordinateDeviation;
    private long mTimeout;
    private String mPoseListener;
    private Gson mGson;
    private Pose mFlagPose;
    private NavigationState mStatus;
    private SdkNavigateStartReport navigateStartReport;
    private SdkNavigateFinishReport navigateFinishReport;
    /**多机模式等待超时时长，默认3600s **/
    private long mWaitTimeout;
    /**是否是在多机避障状态，如果是则停止处理机器N秒不动退出导航的逻辑 **/
    private volatile boolean isMultipleWaitStatus = false;
    private Timer mWaitTimer;
    /** 导航任务优先级 **/
    private int mTaskPriority = 0;
    private double mLinearAcceleration;
    private double mAngularAcceleration;
    private int mStartModeLevel;
    private int mBrakeModeLevel;
    private double mObsDistance;
    private double mPosTolerance;
    private double mAngleTolerance;
//    private int mRunStopCmdParam = Definition.NAVIGATE_DEFAULT;
    private NavigationResetHeadUtils mNavigationResetHeadUtils;

    protected NavigationAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_NAVIGATION, resList);
        mGson = new Gson();
        navigateStartReport = new SdkNavigateStartReport();
        navigateFinishReport = new SdkNavigateFinishReport();
        mNavigationResetHeadUtils = new NavigationResetHeadUtils(mApi, mManager.getCoreService().getHeadMoveManager());
    }

    @Override
    protected boolean startAction(NavigationBean parameter, LocalApi api) {
        if (verifyParam(parameter)) {
            prepareNavigation(parameter);
            return true;
        }
        return false;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        RobotLog.d("Stop navigation action");
//        stopCacheVisionPicture();
        updateMultipleRobotWaitingStatus(false);
        cancelWaitTimer();
        mStatus = NavigationState.STOPPED;
        unregisterStatusListener(mPoseListener);
        mFlagPose = null;
        //mApi.stopNavigation(mReqId);
        mNavigationResetHeadUtils.stop();
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_IS_IN_LOCATION:
                verifyLocation(result, extraData);
                return true;

            case Definition.CMD_NAVI_GET_LOCATION:
                processGetLocation(result, extraData);
                return true;

            case Definition.CMD_NAVI_IS_ESTIMATE:
                processPoseEstimate(result, extraData);
                return true;

            case Definition.CMD_NAVI_GO_LOCATION:
                processNavigationResult(result, extraData);
                return true;
            case Definition.CMD_VISION_START_CACHE_PICTURES:
            case Definition.CMD_VISION_SAVE_CACHE_PICTURES:
                Log.d(TAG, "cmdResponse:" + command + " result:" + result + " extraData:" +extraData);
                return true;
            default:
                return mNavigationResetHeadUtils.onCmdResponse(cmdId, command, result, extraData);
        }
    }

    private void processNavigationResult(String result, String extraData) {
        Log.d(TAG, "processNavigationResult : " + result);

        navigateFinishReport.fillData().report();

        if (Definition.NAVIGATION_OK.equals(result)) {
            if (mStatus != NavigationState.STOPPED){
                onResult(Definition.RESULT_OK, result);
            }else {
                onResult(Definition.RESULT_STOP, result);
            }
            return;
        }
        if (Definition.NAVIGATION_CANCELED.equals(result)) {
            onResult(Definition.RESULT_STOP, result);
            return;
        }
        processError(Definition.ERROR_NAVIGATION_FAILED, "Navigation failed : " + result, extraData);
    }

    private void processError(int errorCode, String errorMessage, String extraData) {
        Log.d(TAG, "processError : " + errorCode + " " + errorMessage + " " + extraData);
        super.onError(errorCode, errorMessage, extraData);
    }

    @Override
    public boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_GO_LOCATION: {
                switch (status) {
                    case Definition.NAVIGATION_MULTI_ROBOT_WAITING:
                        updateMultipleRobotWaitingStatus(true);
                        startWaitingTimer();
                        break;
                    case Definition.NAVIGATION_MULTI_ROBOT_WAITING_END:
                        updateMultipleRobotWaitingStatus(false);
                        cancelWaitTimer();
                        break;
                }
                NavigationStatusUtils.processNavigationStatus(this, TAG, status, extraData);
            }
            return true;
            default:
                return mNavigationResetHeadUtils.cmdStatusUpdate(cmdId, command, status, extraData);
        }
    }

    private void prepareNavigation(NavigationBean params) {
        Log.d(TAG, "Start navigation action");
        mStatus = NavigationState.PREPARE;
        mReqId = params.getReqId();
        mDestination = params.getDestination();
        mLinearSpeed = params.getLinearSpeed();
        mAngularSpeed = params.getAngularSpeed();
        mLinearAcceleration = params.getLinearAcceleration();
        mAngularAcceleration = params.getAngularAcceleration();
        mAdjustAngle = params.isAdjustAngle();
        mDestinationRange = params.getDestinationRange();
        mCoordinateDeviation = params.getCoordinateDeviation();
        mFlagPose = null;
        long timeout = params.getTime();
        mTimeout = ((timeout == 0) ? Long.MAX_VALUE : timeout);
        long waitTimeout = params.getMultipleWaitTime();
        mWaitTimeout = (waitTimeout <= 0) ? DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT: waitTimeout;
        isMultipleWaitStatus = false;
        mTaskPriority = params.getPriority();
        mStartModeLevel = params.getStartModeLevel();
        mBrakeModeLevel = params.getBrakeModeLevel();
        mObsDistance = params.getObsDistance();
        mPosTolerance = params.getPosTolerance();
        mAngleTolerance = params.getAngleTolerance();
//        mRunStopCmdParam = params.getRunStopCmdParam();
        //isRobotInLocation(mDestination);
        checkPoseEstimate();
        registerPoseListener();
    }

    private void isRobotInLocation(String placeName, double coordinateDeviation) {
        JSONObject obj = new JSONObject();
        try {
            obj.put(Definition.JSON_NAVI_TARGET_PLACE_NAME, placeName);
            obj.put(Definition.JSON_NAVI_COORDINATE_DEVIATION, coordinateDeviation);
            mApi.isRobotInlocations(mReqId, obj.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void checkPoseEstimate() {
        Log.d(TAG, "Check pose estimate");
        mApi.isRobotEstimate(mReqId);
    }

    private void saveVisionSnapshotData(String extraData){
        try {
            Log.d(TAG, "saveVisionSnapshotData:" + extraData);
            JSONObject jsonObject = new JSONObject(extraData);
            String message = jsonObject.optString("message");
            JSONObject msgObj = new JSONObject(message);
            String cacheId = msgObj.optString(Definition.JSON_CACHE_ID);
            Log.d(TAG, "saveVisionSnapshotData cacheID:" + cacheId);
            saveCacheVisionPicture(cacheId);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    private void processPoseEstimate(String result, String extraData) {
        if (!"true".equals(result)) {
            onError(Definition.ERROR_NOT_ESTIMATE, "Not pose estimate", extraData);
            return;
        }
        isRobotInLocation(mDestination, mCoordinateDeviation);
    }

    private void verifyLocation(String result, String extraData) {
        try {
            JSONObject json = new JSONObject(result);
            boolean isInLocation = json.optBoolean(Definition.JSON_NAVI_IS_IN_LOCATION, false);
            if (isInLocation) {
                //stop();
                onResult(Definition.ERROR_IN_DESTINATION, "Already at the " + mDestination, extraData);
            } else {
                mApi.getLocation(mReqId, mDestination);
            }
        } catch (JSONException e) {
            e.printStackTrace();
            onError(Definition.ERROR_IN_DESTINATION, "Judge at the " + mDestination + " failed", extraData);
        }
    }

    private void processGetLocation(String result, String extraData) {
        try {
            JSONObject json = new JSONObject(result);
            boolean isExist = json.getBoolean(Definition.JSON_NAVI_SITE_EXIST);
            if (isExist) {
                mDestPose = mGson.fromJson(result, Pose.class);
                mStatus = NavigationState.NAVIGATION;
                mNavigationResetHeadUtils.checkResetHeadState(new NavigationResetHeadUtils.ResetHeadListener() {
                    @Override
                    public void onResetHeadSuccess() {
                        mApi.goPlace(mReqId, mDestination, mLinearSpeed, mAngularSpeed, mAdjustAngle,
                                mDestinationRange, mTaskPriority, mLinearAcceleration, mAngularAcceleration,
                                mStartModeLevel, mBrakeModeLevel, mObsDistance, mPosTolerance, mAngleTolerance, false);
                        navigateStartReport.fillData(mDestination)
                                .report();
                        mNavigationResetHeadUtils.startHeadStatusListener(mReqId);
                    }

                    @Override
                    public void onResetHeadFailed(int errorCode, String errorString) {
                        onError(errorCode, errorString);
                    }
                }, mReqId);
            } else {
                //stop();
                onError(Definition.ERROR_DESTINATION_NOT_EXIST, "Destination not exist", extraData);
            }
        } catch (JSONException e) {
            e.printStackTrace();
            //stop();
            onError(Definition.ERROR_DESTINATION_NOT_EXIST, "Get " + mDestination + " failed", extraData);
        }
    }

    private void registerPoseListener() {
        mPoseListener = registerStatusListener(Definition.STATUS_POSE, new StatusListener() {
            @Override
            public void onStatusUpdate(String data) {
                onPoseUpdate(data);
            }
        });
    }

    private synchronized void onPoseUpdate(String data) {
        if (TextUtils.isEmpty(data) || mStatus != NavigationState.NAVIGATION) {
            return;
        }
        Pose pose = mGson.fromJson(data, Pose.class);
        if (isAborted(pose)) {
            Log.d(TAG, "Pose timeout : " + mFlagPose.toString() + "   "
                    + pose.toString() + "  " + mStatus);
            onError(Definition.ERROR_DESTINATION_CAN_NOT_ARRAIVE, "Timeout");
            //stop();
        }
    }

    /**
     * 更新多机避障状态，如果是多机避障状态，则机器人超时等待策略需忽略
     * @param isWaiting 是否正在多机避障等待状态
     */
    private void updateMultipleRobotWaitingStatus(boolean isWaiting){
        isMultipleWaitStatus = isWaiting;
    }

    private void startWaitingTimer() {
        cancelWaitTimer();
        mWaitTimer = new Timer();
        mWaitTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (mStatus != NavigationState.NAVIGATION) {
                    return;
                }
                onError(Definition.ERROR_MULTI_ROBOT_WAITING_TIMEOUT, "multiple robot wait timeout");
            }
        }, mWaitTimeout);
    }

    private void cancelWaitTimer() {
        if (mWaitTimer != null) {
            mWaitTimer.cancel();
            mWaitTimer = null;
        }
    }

    private boolean isAborted(Pose pose) {
        double distance = pose.getDistance(mFlagPose);
        if (!isMultipleWaitStatus && Double.compare(distance, MIN_DISTANCE) < 0) {
            long movingTime = pose.getTime() - mFlagPose.getTime();
            if (movingTime > mTimeout) {
                return true;
            }
        } else {
            mFlagPose = pose;
        }
        return false;
    }

    /**
     * 获取视觉相关图片缓存，需要统计视觉异常的快照事件发生时调用
     * 目前仅针对招财豹30秒无位移使用
     * @param cacheId
     */
    private void saveCacheVisionPicture(String cacheId) {
        if ((ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMeissa2()) && !TextUtils.isEmpty(cacheId)) {
            String savePath = FileUtils.getVisionSnapshotCachePath(cacheId);
            Log.d(TAG, "saveCacheVisionPicture path:" + savePath);
            mApi.saveCacheVisionPictures(mReqId, 50.0f, 0.0f, savePath);
        }
    }

    private void startCacheVisionPicture(){
        if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMeissa2()) {
            mApi.startCacheVisionPictures(mReqId, 50.0f, 3.0f);
        }
    }

    private void stopCacheVisionPicture(){
        if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMeissa2()) {
            mApi.stopCacheVisionPictures(mReqId);
        }
    }

    @Override
    public boolean verifyParam(NavigationBean params) {
        Log.d(TAG, "verifyParam : "+params.toString());
        return !TextUtils.isEmpty(params.getDestination());
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
//        if (mRunStopCmdParam == Definition.NAVIGATE_MULTI_ROBOT){
//            Log.d(TAG, "getStopCommand null ");
//            return null;
//        }
        return new ArrayList<StopCommand>() {{
            add(new StopCommand(Definition.CMD_NAVI_STOP_NAVIGATION, null));
        }};
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
//        if (mRunStopCmdParam == Definition.NAVIGATE_MULTI_ROBOT){
//            Log.d(TAG, "getStopCommandChecker null ");
//            return null;
//        }
        return new IStopCommandChecker() {
            @Override
            public boolean executeSuccess(String command, String result) {
                RobotLog.d("Leading stop check : " + command);
                switch (command) {
                    case Definition.CMD_NAVI_STOP_NAVIGATION:
                        return true;
                    default:
                        break;
                }
                return false;
            }
        };
    }
}
