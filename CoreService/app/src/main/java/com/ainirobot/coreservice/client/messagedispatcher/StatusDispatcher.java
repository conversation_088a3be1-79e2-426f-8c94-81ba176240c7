package com.ainirobot.coreservice.client.messagedispatcher;

/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

import android.os.RemoteException;
import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.ainirobot.coreservice.IStatusListener;
import com.ainirobot.coreservice.client.StatusListener;

import java.util.concurrent.ConcurrentHashMap;


public class StatusDispatcher extends IStatusListener.Stub implements IRecyclable {
    private static RecyclablePool<StatusDispatcher> sPool = new RecyclablePool<>();
    private static ConcurrentHashMap<String, StatusDispatcher> sRegisterListener
            = new ConcurrentHashMap<>();

    private DispatchHandler mDispatchHandler;
    private StatusListener mListener;
    private String mId;

    private StatusDispatcher(DispatchHandler dispatchHandler, StatusListener listener) {
        mDispatchHandler = dispatchHandler;
        mListener = listener;
    }

    static StatusDispatcher obtain(DispatchHandler dispatchHandler, StatusListener listener) {
        StatusDispatcher actionDispatcher = sPool.obtain();

        if (actionDispatcher == null) {
            actionDispatcher = new StatusDispatcher(dispatchHandler, listener);
        } else {
            actionDispatcher.mDispatchHandler = dispatchHandler;
            actionDispatcher.mListener = listener;
        }
        return actionDispatcher;
    }

    static void unregister(@NonNull String id) {
        if (!TextUtils.isEmpty(id)) {
            StatusDispatcher dispatcher = sRegisterListener.remove(id);
            if (dispatcher != null) {
                sPool.recycle(dispatcher);
            }
        }
    }

    public void register(@NonNull String id) {
        mId = id;
        mListener.setId(id);
        sRegisterListener.put(id, this);
    }

    @Override
    public void onStatusUpdate(String type, String data) throws RemoteException {
        StatusListener listener = mListener;
        DispatchHandler handler = mDispatchHandler;
        if (listener != null && handler != null) {
            StatusMessage statusMessage = StatusMessage.obtain(type, data, listener);
            handler.sendMessage(handler
                    .obtainMessage(MessageDispatcher.MSG_STATUS, statusMessage));
        }
    }

    @Override
    public void recycle() {
        mDispatchHandler = null;
        mListener = null;
        mId = null;
    }
}
