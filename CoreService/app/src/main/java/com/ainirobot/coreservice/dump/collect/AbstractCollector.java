package com.ainirobot.coreservice.dump.collect;

import android.util.Log;

import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.dump.DumpDef;
import com.ainirobot.coreservice.dump.manager.DataDumpManager;

/**
 *
 * Created by Orion on 2020/5/25.
 */
public abstract class AbstractCollector {
    private static final String TAG = AbstractCollector.class.getSimpleName();

    protected int mReq = 0;
    protected DataDumpManager mManager;
    protected SystemApi mSystemApi;
    private volatile Status mStatus = Status.IDLE;

    enum Status {
        IDLE, INIT, RUN, PAUSE
    }

    public AbstractCollector(DataDumpManager mManager) {
        this.mManager = mManager;
        mSystemApi = SystemApi.getInstance();
    }

    public final boolean start(String param) {
        boolean result;
        if (isRunning()) {
            Log.d(TAG, "Collector is already running");
            return false;
        }
        if (!checkStorePermission()) {
            Log.d(TAG, "Has no store permission");
            return false;
        }
        updateStatus(Status.INIT);
        result = startCollect(param);
        if (result) {
            updateStatus(Status.RUN);
        } else {
            updateStatus(Status.IDLE);
        }
        return result;
    }

    public final boolean stop() {
        boolean result;
        if (mStatus == Status.IDLE
                || mStatus == Status.PAUSE) {
            Log.d(TAG, "Collector is already stopped");
            return false;
        }
        Status preStatus = getStatus();
        updateStatus(Status.PAUSE);
        result = stopCollect();
        if (result) {
            updateStatus(Status.IDLE);
        } else {
            updateStatus(preStatus);
        }
        return result;
    }

    public final Status getStatus() {
        return mStatus;
    }

    public final boolean isRunning() {
        return mStatus == Status.RUN;
    }

    boolean updateStatus(Status status) {
        Log.d(TAG, "Update status : " + status);
        if (mStatus != status) {
            mStatus = status;
        }
        return true;
    }

    /**
     * 检查存储权限
     *
     * @return
     */
    public final boolean checkStorePermission() {
        return getPermission() >= 1;
    }

    /**
     * 当前数据权限
     *
     * @return
     */
    protected int getPermission() {
        return DumpDef.DATA_RIGHTS_UPLOAD;
    }

    protected abstract boolean startCollect(String param);

    protected abstract boolean stopCollect();

}
