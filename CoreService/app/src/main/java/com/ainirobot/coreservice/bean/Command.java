package com.ainirobot.coreservice.bean;

import android.os.Parcel;
import android.os.Parcelable;

public class Command implements Parcelable {

    private String type;
    private long timeout;
    private boolean isAsync = true;
    private String serviceName;

    public Command(String type) {
        this.type = type;
    }

    public Command(String type, long timeout) {
        this.type = type;
        this.timeout = timeout;
    }

    public String getType() {
        return type;
    }

    public long getTimeout() {
        return timeout;
    }

    public void setTimeout(long timeout) {
        this.timeout = timeout;
    }

    public boolean isAsync() {
        return isAsync;
    }

    public void setAsync(boolean async) {
        isAsync = async;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getServiceName() {
        return serviceName;
    }

    protected Command(Parcel in) {
        type = in.readString();
        timeout = in.readLong();
        isAsync = (in.readInt() == 1);
    }

    public static final Creator<Command> CREATOR = new Creator<Command>() {
        @Override
        public Command createFromParcel(Parcel in) {
            return new Command(in);
        }

        @Override
        public Command[] newArray(int size) {
            return new Command[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(type);
        dest.writeLong(timeout);
        dest.writeInt(isAsync ? 1 : 0);
    }

    @Override
    public String toString() {
        return "Command[type:" + type + " , timeout:" + timeout + "]";
    }
}
