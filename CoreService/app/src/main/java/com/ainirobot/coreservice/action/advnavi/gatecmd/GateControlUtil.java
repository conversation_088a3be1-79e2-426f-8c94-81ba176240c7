package com.ainirobot.coreservice.action.advnavi.gatecmd;

import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.advnavi.state.BaseState;
import com.ainirobot.coreservice.client.Definition;
import com.google.gson.Gson;

import org.json.JSONObject;

/**
 * Date: 2024/2/22
 * Author: dengting
 * Description: GateControlUtil
 */
public class GateControlUtil implements GateCommandInterface {
    private String TAG = GateCommand.class.getSimpleName();
    private final GateControlUtilListener mListener;
    private final Gson mGson = new Gson();
    private GateCommand gateCommand;

    private volatile boolean isAlive = false;
    private final BaseState mBaseState;
    /**
     * 用户用了那个方法
     */
    private GateCommand.CmdType intentType;

    /**
     * 控制闸机开门
     *
     * @param listener listener
     */
    public GateControlUtil(@NonNull BaseState state,
                           GateControlUtilListener listener) {
        this.mBaseState = state;
        this.mListener = listener;
        Log.d(TAG, "\n" + mBaseState.TAG + "\n\n");
        TAG = mBaseState.TAG + "-" + TAG;
    }


    /**
     * 控制持续开门
     * <p>
     * listener result
     */
    public void startOpenDoor() {
        if (gateCommand != null && gateCommand instanceof CmdOpenGateDoor) {
            if (gateCommand.isAlive) {
                Log.d(TAG, "startOpenDoor cmd already start !");
                return;
            }
        }
        stopControlGate();
        commandStart(GateCommand.CmdType.OPEN_DOOR);
        gateCommand = new CmdOpenGateDoor(
                TAG,
                intentType,
                this,
                cmdListener);
        gateCommand.start();
    }

    /**
     * 控制关门
     * <p>
     * listener result
     */
    public void startCloseDoor() {
        if (gateCommand != null && gateCommand instanceof CmdCloseGateDoor) {
            if (gateCommand.isAlive) {
                Log.d(TAG, "startCloseDoor cmd already start !");
                return;
            }
        }
        stopControlGate();
        commandStart(GateCommand.CmdType.CLOSE_DOOR);
        gateCommand = new CmdCloseGateDoor(
                TAG,
                intentType,
                this,
                cmdListener);
        gateCommand.start();
    }

    /**
     * 释放闸机控制
     * 停止正在进行的命令，并释放电梯
     */
    public void releaseGateControl() {
        Log.d(TAG, "releaseGateControl");
        if (null != gateCommand && gateCommand.isAlive) {
            gateCommand.stop();
        }
        cmdReleaseGate();
    }

    /**
     * 停止持续控制
     * 一般和释放闸机一起调用
     */
    public void stopControlGate() {
        isAlive = false;
        if (null != gateCommand && gateCommand.isAlive) {
            Log.d(TAG, "stop control gate cmd");
            gateCommand.stop();
        }
    }


    private void commandStart(GateCommand.CmdType cmdType) {
        isAlive = true;
        updateIntent(cmdType);
        Log.d(TAG, "command " + cmdType.toString() + " start");
    }

    private void updateIntent(GateCommand.CmdType intentType) {
        this.intentType = intentType;
    }


    private boolean isAlive() {
        return isAlive;
    }

    /**
     * 需要透传 CMD_CONTROL_OPEN_GATE_DOOR 和 CMD_CONTROL_CALL_GATE 的响应信息
     */
    public boolean cmdResponse(String command, String result, String extraData) {
        if (null != gateCommand) {
            switch (command) {
                case Definition.CMD_CONTROL_CALL_GATE:
                case Definition.CMD_CONTROL_OPEN_GATE_DOOR:
                case Definition.CMD_CONTROL_OPEN_REV_GATE_DOOR:
                case Definition.CMD_CONTROL_CLOSE_GATE_DOOR:
                case Definition.CMD_CONTROL_RELEASE_GATE:
                    if (!isAlive()) {
                        Log.i(TAG, "gate command not alive");
                        return false;
                    }
                    gateCommand.cmdResponse(command, result, extraData);
                    return true;
            }
        }
        return false;
    }

    private void parseCommandstatus(int status, String data, String extraData) {
        onStatusUpdate(status, data, extraData);
    }

    private void parseCommandFinish(int status, String data) {
        Log.d(TAG, "parseCommandFinish" + status + data);
        if (isCmdSuc(GateCommand.CmdType.RELEASE_GATE, status, data)) {
            Log.d(TAG, "release gate suc");
            return;
        }
        if (isCmdSuc(intentType, status, data)) {
            Log.d(TAG, intentType.toString() + " success");
            onSuccess();
            return;
        }
        switch (status) {
            case Definition.ERROR_OPEN_GATE_DOOR_FAILED:
                onResult(Definition.ERROR_OPEN_GATE_DOOR_FAILED, "");
                break;
            default:
                onResult(status, data);
                break;
        }
    }

    private final GateCommandListener cmdListener = new GateCommandListener() {
        @Override
        public void onStatusUpdate(int status, String data, String extraData) {
            parseCommandstatus(status, data, extraData);
        }

        @Override
        public void onFinish(int status, String data) {
            parseCommandFinish(status, data);
        }
    };

    private void onStatusUpdate(int status, String data, String extraData) {
        if (null != mListener) {
            mListener.onGateStatusUpdate(status, data, extraData);
        }
    }

    private void onSuccess() {
        if (null != mListener) {
            mListener.onSuccess();
        }
    }

    private void onResult(int id, String param) {
        if (null != mListener) {
            mListener.onGateResult(id, param);
        }
    }

    public boolean isCmdSuc(GateCommand.CmdType cmdType, int status, String extraData) {
        try {
            if (status == Definition.RESULT_OK) {
                JSONObject object = new JSONObject(extraData);
                String type = object.getString(Definition.JSON_CMD_TYPE);
                return TextUtils.equals(type, cmdType.toString());
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public void cmdCallGate() {
        if (null != mBaseState && isAlive()) {
            mBaseState.controlGate();
        }
    }

    @Override
    public void cmdOpenGateDoor() {
        if (null != mBaseState && isAlive()) {
            mBaseState.openGateDoor();
        }
    }

    @Override
    public void cmdCloseGateDoor() {
        if (null != mBaseState && isAlive()) {
            mBaseState.closeGateDoor();
        }
    }

    @Override
    public void cmdReleaseGate() {
        if (null != mBaseState && isAlive()) {
            mBaseState.releaseGate();
        }
    }
}
