/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.core;

import android.content.Context;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.RemoteCallbackList;
import android.os.RemoteException;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import com.ainirobot.coreservice.bi.report.RobotSettingsReporter;
import com.ainirobot.coreservice.data.robotsetting.SettingDataHelper;
import com.ainirobot.coreservice.listener.IRobotSettingListener;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class RobotSettingManager {
    private static final String TAG = RobotSettingManager.class.getSimpleName();
    private Context mContext;
    private SettingDataHelper mSettingDataHelper;
    private ConcurrentHashMap<String, String> mSettingList;
    private RemoteCallbackList<IRobotSettingListener> mListenerList;

    private RobotSettingsReporter mReporter;

    public RobotSettingManager(Context context) {
        mContext = context;
        mSettingDataHelper = new SettingDataHelper(context);
        mSettingList = new ConcurrentHashMap<>();
        mListenerList = new RemoteCallbackList<>();
        initListener();

        mReporter = new RobotSettingsReporter();
    }

    private void initListener() {
        ContentObserver observer = new ContentObserver(null) {
            @Override
            public void onChange(boolean selfChange, Uri uri) {
                Log.i(TAG, "setting changed:" + uri);
                mReporter.reportSettingsGlobalChanged(selfChange, uri);

                String key = uri.getLastPathSegment();
                getRobotSetting(key, true);
                int n = mListenerList.beginBroadcast();
                for (int i = 0; i < n; i++) {
                    IRobotSettingListener listener = mListenerList.getBroadcastItem(i);
                    List<String> keyList = (List<String>) mListenerList.getBroadcastCookie(i);
                    if (listener != null && keyList.contains(key)) {
                        try {
                            Log.i(TAG, "send changed:" + key);
                            listener.onRobotSettingChanged(key);
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                    }
                }
                mListenerList.finishBroadcast();
            }
        };
        mSettingDataHelper.registerListener(observer);
        mContext.getContentResolver().registerContentObserver(
                Settings.Global.CONTENT_URI, true, observer);
    }

    public String getRobotSetting(String key) {
        return getRobotSetting(key, false);
    }

    public String getRobotSetting(String key, boolean reRead) {
        Log.i(TAG, "get robot setting:" + key + ",reRead=" + reRead);
        String value;
        if (mSettingList.containsKey(key) && !reRead) {
            value = mSettingList.get(key);
            Log.d(TAG, key + ":get from list " + value);
        } else {
            value = mSettingDataHelper.getRobotSetting(key);
            if (SettingDataHelper.NOT_SUPPORT.equals(value)) {
                value = Settings.Global.getString(mContext.getContentResolver(), key);
                Log.d(TAG, key + ":get from global " + value);
            } else {
                Log.d(TAG, key + ":get from database " + value);
            }
            if (null == value) {
                value = "";
            }
            mSettingList.put(key, value);
        }

        mReporter.reportGetRobotSetting(key, value);
        return value;
    }

    public void setRobotSetting(String key, String value) {
        Log.i(TAG, "set robot setting:key=" + key + ",value=" + value);
        if (null == value) {
            value = "";
        }
        int count = mSettingDataHelper.setRobotSetting(key, value);
        mReporter.reportSetRobotSetting(key, value, count);
        if (count > 0) {
            Log.d(TAG, key + ":set list&database");
            mSettingList.put(key, value);
        } else {
            Log.d(TAG, key + ":set list&global");
            mSettingList.put(key, value);
            Settings.Global.putString(mContext.getContentResolver(), key, value);
        }
    }

    public void registerListener(String key, ContentObserver observer) {
        mReporter.reportRegisterListener(key);
        mSettingDataHelper.registerListener(key, observer);
    }

    public void unregisterListener(ContentObserver observer) {
        mReporter.reportUnregisterListener();
        mSettingDataHelper.unRegisterListener(observer);
    }

    public void registerListener(List<String> keyList, IRobotSettingListener listener) {
        Log.i(TAG, "registerListener");
        mReporter.reportRegisterListener(keyList);
        if (mListenerList.register(listener, keyList)) {
            Log.i(TAG, "registerListener success.size:"
                    + mListenerList.getRegisteredCallbackCount());
        }
    }

    public void unRegisterListener(IRobotSettingListener listener) {
        Log.i(TAG, "unRegisterListener");
        mReporter.reportUnregisterListener();
        if (mListenerList.unregister(listener)) {
            Log.i(TAG, "unRegisterListener success.size:"
                    + mListenerList.getRegisteredCallbackCount());
        }
    }

    public boolean containsRobotConfig(String key){
        return !SettingDataHelper.NOT_SUPPORT.equals(mSettingDataHelper.getRobotSetting(key));
    }

    public boolean hasRobotSetting(String key) {
        Log.i(TAG, "get robot setting:" + key);
        String value = mSettingDataHelper.getRobotSetting(key);
        if (SettingDataHelper.NOT_SUPPORT.equals(value)) {
            value = Settings.Global.getString(mContext.getContentResolver(), key);
            Log.d(TAG, key + ":get from global " + value);
        } else {
            Log.d(TAG, key + ":get from database " + value);
        }
        return !TextUtils.isEmpty(value);
    }
}
