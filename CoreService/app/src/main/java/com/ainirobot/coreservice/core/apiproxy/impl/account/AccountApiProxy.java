package com.ainirobot.coreservice.core.apiproxy.impl.account;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Handler;
import android.os.Message;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.ActionManager;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.account.UserBean;
import com.ainirobot.coreservice.client.actionbean.CommandBean;
import com.ainirobot.coreservice.client.actionbean.FaceSyncBean;
import com.ainirobot.coreservice.client.person.FaceBean;
import com.ainirobot.coreservice.core.apiproxy.BaseApiProxy;
import com.ainirobot.coreservice.core.apiproxy.result.BaseApiResult;
import com.ainirobot.coreservice.service.CoreService;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AccountApiProxy extends BaseApiProxy {

    private static final String TAG = AccountApiProxy.class.getSimpleName();

    public AccountApiProxy(CoreService core, Handler handler) {
        super(core, handler);
    }

    public void remoteSyncMemberData() {
        Log.i(TAG, "remoteSyncMemberData");
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_GET_MEMBERS, null, false);
        ActionManager actionManager = mCore.getActionManager();
        actionManager.exCmd(Definition.ACTION_COMMON,
                mGson.toJson(bean), new ApiListener() {
                    @Override
                    public void onResult(int result, String message) {
                        int apiResult;
                        List<UserBean> data = null;
                        try {
                            JSONObject json = new JSONObject(message);
                            int code = json.optInt("code", -1);
                            if (code == 200) {
                                JSONObject dataObject = json.optJSONObject("data");
                                if (dataObject != null) {
                                    JSONArray members = dataObject.optJSONArray("members");
                                    List<UserBean> userBeanList = new ArrayList<>();
                                    Log.d(TAG, "remoteSyncMemberData size: "
                                            + (members == null ? 0 : members.length()));
                                    if (members != null && members.length() > 0) {
                                        for (int i = 0; i < members.length(); i++) {
                                            Log.d(TAG, "remoteSyncMemberData index: " + i
                                                    + ", member: " + members.getJSONObject(i));
                                            UserBean userBean =
                                                    new UserBean(members.getJSONObject(i));
                                            userBeanList.add(userBean);
                                        }
                                        if (userBeanList.size() > 0) {
                                            apiResult = BaseApiResult.RESULT_SUCCESS;
                                            data = userBeanList;
                                        } else {
                                            apiResult = BaseApiResult.RESULT_FAILED;
                                        }
                                    } else {
                                        apiResult = BaseApiResult.RESULT_FAILED;
                                    }
                                } else {
                                    apiResult = BaseApiResult.RESULT_FAILED;
                                }
                            } else {
                                apiResult = BaseApiResult.RESULT_FAILED;
                            }
                        } catch (JSONException e) {
                            apiResult = BaseApiResult.RESULT_FAILED;
                        }
                        if (mHandler != null) {
                            Message msg = new Message();
                            msg.what = MSG_API_RESULT;
                            msg.obj = new BaseApiResult(BaseApiResult.Type.REMOTE_SYNC_MEMBER,
                                    apiResult, data);
                            mHandler.sendMessage(msg);
                        }
                    }
                });
    }

    public void registerFaceList(String version, @NonNull final List<FaceBean> faceList) {
        Log.i(TAG, "registerFaceList version: " + version
                + ", faceList: " + mGson.toJson(faceList));
        List<FaceSyncBean> faceSyncBeanList = new ArrayList<>();
        for (int i = 0; i < faceList.size(); i++) {
            FaceBean faceBean = faceList.get(i);
            faceSyncBeanList.add(new FaceSyncBean(faceBean.getFaceId(), faceBean.getName(),
                    faceBean.getFeature(), version, faceBean.getRegisterTime(),
                    mCore.getPersonManager().isLocalRegisterEnable() ? 1 : 2));
        }
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_REGISTER_FACE_LIST,
                mGson.toJson(faceSyncBeanList), false);
        ActionManager actionManager = mCore.getActionManager();
        actionManager.exCmd(Definition.ACTION_COMMON,
                mGson.toJson(bean), new ApiListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.d(TAG, "registerFaceList result: " + result
                                + ", message: " + message);
                        int code = -1;
                        int apiResult;
                        Map<String, UserBean> faceIdAndUserMap = new HashMap<>();
                        try {
                            JSONObject json = new JSONObject(message);
                            code = json.optInt("code", -1);
                            JSONObject data = json.optJSONObject("data");
                            if (data != null) {
                                JSONArray users = data.optJSONArray("users");
                                if (users != null && users.length() > 0) {
                                    for (int i = 0; i < users.length(); i++) {
                                        JSONObject user = users.optJSONObject(i);
                                        String faceId = user.optString("face_id");
                                        for (FaceBean faceBean : faceList) {
                                            if (faceBean.getFaceId().equals(faceId)) {
                                                UserBean userBean = new UserBean(user);
                                                faceIdAndUserMap.put(faceId, userBean);
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        if (code == 200) {
                            apiResult = BaseApiResult.RESULT_SUCCESS;
                        } else {
                            apiResult = BaseApiResult.RESULT_FAILED;
                        }
                        if (mHandler != null) {
                            Message msg = new Message();
                            msg.what = MSG_API_RESULT;
                            msg.obj = new BaseApiResult(BaseApiResult.Type.REGISTER_FACE_LIST,
                                    apiResult, faceIdAndUserMap);
                            mHandler.sendMessage(msg);
                        }
                    }
                });
    }

    public void uploadFaceList(String version, @NonNull List<FaceBean> faceList) {
        Log.i(TAG, "uploadFaceList version: " + version + ", faceList: " + faceList);
        List<FaceSyncBean> faceSyncBeanList = new ArrayList<>();
        for (int i = 0; i < faceList.size(); i++) {
            FaceBean faceBean = faceList.get(i);
            faceSyncBeanList.add(new FaceSyncBean(faceBean.getFaceId(), faceBean.getName(),
                    faceBean.getFeature(), version, faceBean.getRegisterTime(),
                    mCore.getPersonManager().isLocalRegisterEnable() ? 1 : 2));
        }
        CommandBean bean = new CommandBean(Definition.CMD_REMOTE_UPLOAD_FACE_LIST,
                mGson.toJson(faceSyncBeanList), false);
        ActionManager actionManager = mCore.getActionManager();
        actionManager.exCmd(Definition.ACTION_COMMON,
                mGson.toJson(bean), new ApiListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.d(TAG, "uploadFaceList result: " + result
                                + ", message: " + message);
                    }
                });
    }

    public void unbind(String userId, String userToken) {
        Log.i(TAG, "unbind userId: " + userId + ", userToken: " + userToken);
        JSONObject param = new JSONObject();
        try {
            param.put(Definition.JSON_REMOTE_USER_ID, userId);
            param.put(Definition.JSON_REMOTE_USER_TOKEN, userToken);
            CommandBean bean = new CommandBean(Definition.CMD_REMOTE_UNBIND_ROBOT_MINI,
                    param.toString(), false);
            ActionManager actionManager = mCore.getActionManager();
            actionManager.exCmd(Definition.ACTION_COMMON,
                    mGson.toJson(bean), new ApiListener() {
                        @Override
                        public void onResult(int result, String message) {
                            Log.d(TAG, "unbind result: " + result
                                    + ", message: " + message);
                            int code = -1;
                            int apiResult;
                            try {
                                JSONObject jsonObject = new JSONObject(message);
                                code = jsonObject.optInt("code", -1);
                            } catch (JSONException e) {
                                e.printStackTrace();
                            }

                            if (code == 200) {
                                apiResult = BaseApiResult.RESULT_SUCCESS;
                            } else {
                                apiResult = BaseApiResult.RESULT_FAILED;
                            }

                            if (mHandler != null) {
                                Message msg = new Message();
                                msg.what = MSG_API_RESULT;
                                msg.obj = new BaseApiResult(BaseApiResult.Type.REMOTE_UNBIND,
                                        apiResult);
                                mHandler.sendMessage(msg);
                            }
                        }
                    });
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public synchronized String saveRemoteRegisterPic(String imageUrl, String faceId) {
        Log.d(TAG, "saveRemoteRegisterPic imageUrl: " + imageUrl
                + ", faceId: " + faceId);
        if (TextUtils.isEmpty(imageUrl) || TextUtils.isEmpty(faceId)) {
            Log.e(TAG, "saveRemoteRegisterPic imageUrl or faceId invalid");
            return null;
        }

        File picturePathDir = new File(PICTURE_PATH);
        if (!picturePathDir.exists() || !picturePathDir.isDirectory()) {
            boolean isCreateDirectorySuccess = picturePathDir.mkdirs();
            if (!isCreateDirectorySuccess) {
                Log.e(TAG, "saveRemoteRegisterPic newPictureDirectory create failed");
                return null;
            }
        }

        BufferedOutputStream bos = null;
        try {
            Bitmap bitmap = getImageInputStream(imageUrl);
            if (bitmap == null) {
                Log.e(TAG, "saveRemoteRegisterPic bitmap null");
                return null;
            }
            String newPicturePath = PICTURE_PATH + File.separator + faceId;
            Log.d(TAG, "saveRemoteRegisterPic newPicturePath: " + newPicturePath);
            File picturePathFile = new File(newPicturePath);
            if (picturePathDir.exists() && picturePathDir.isFile()) {
                boolean isDeleteSuccess = picturePathFile.delete();
                Log.d(TAG, "saveRemoteRegisterPic isDeleteSuccess: " + isDeleteSuccess);
            }

            bos = new BufferedOutputStream(new FileOutputStream(picturePathDir));
            bitmap.compress(Bitmap.CompressFormat.JPEG, 80, bos);
            bos.flush();
            return newPicturePath;
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (bos != null) {
                    bos.close();
                }
            } catch (IOException ie) {
                ie.printStackTrace();
            }
        }
        return null;
    }

    private Bitmap getImageInputStream(String imageUrl) {
        URL url;
        HttpURLConnection connection;
        Bitmap bitmap = null;
        InputStream inputStream = null;
        try {
            url = new URL(imageUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(6000); //超时设置
            connection.setDoInput(true);
            connection.setUseCaches(false); //设置不使用缓存
            inputStream = connection.getInputStream();
            bitmap = BitmapFactory.decodeStream(inputStream);
            inputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException ie) {
                    ie.printStackTrace();
                }
            }
        }
        return bitmap;
    }
}
