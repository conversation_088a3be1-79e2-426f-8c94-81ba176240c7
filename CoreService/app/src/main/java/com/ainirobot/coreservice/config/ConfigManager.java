package com.ainirobot.coreservice.config;

import android.content.Context;
import android.content.Intent;
import android.os.Environment;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.R;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.receiver.LogBroadcastReceiver;
import com.ainirobot.coreservice.client.subtype.DeviceSubType;
import com.ainirobot.coreservice.config.CoreConfig.ShutdownConfig;
import com.ainirobot.coreservice.config.core.AccountConfig;
import com.ainirobot.coreservice.config.core.BatteryConfig;
import com.ainirobot.coreservice.config.core.HeadConfig;
import com.ainirobot.coreservice.config.core.MapCompatibilityConfig;
import com.ainirobot.coreservice.config.core.NavigationConfig;
import com.ainirobot.coreservice.config.core.PublicConfig;
import com.ainirobot.coreservice.utils.Utils;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class ConfigManager {

    private static final String TAG = "ConfigManager";

    private static RobotConfig config;
    private static UVCCamera uvcCamera;

    public static void loadConfig(Context context) {
        String configJson = Utils.loadConfig(ApplicationWrapper.getContext(),
                Environment.getExternalStorageDirectory() + File.separator + "config.json",
                R.raw.config);
        Log.e(TAG, "loadConfig configJson: " + configJson);
        Gson gson = new Gson();
        config = gson.fromJson(configJson, RobotConfig.class);
        if (config != null) {
            config.parse();
            CoreConfig.LogConfig logConfig = getLogConfig();
            if (logConfig != null) {
                int level = logConfig.getLevel();
                sendBroadcast(context, level);
            }
            updateTopIRToSetting(context);
            updateChargeIRToSetting(context);
            updateMapCompatibilityConfig(context);
        }
        initPlatformType(context);

        uvcCamera = new UVCCamera(context);
    }

    private static void initPlatformType(Context context) {
        if (ProductInfo.isMeissaPlus()) {//小秘Plus
            Settings.Global.putString(context.getContentResolver(), Definition.ROBOT_PLATFORM_TYPE_KEY, Definition.ROBOT_PLATFORM_MEISSA_PLUS);
        } else if (ProductInfo.isMiniProduct()) {//mini
            Settings.Global.putString(context.getContentResolver(), Definition.ROBOT_PLATFORM_TYPE_KEY, Definition.ROBOT_PLATFORM_MINI);
        } else if (ProductInfo.isMeissa2()) {
            Settings.Global.putString(context.getContentResolver(), Definition.ROBOT_PLATFORM_TYPE_KEY, Definition.ROBOT_PLATFORM_MEISSA_2);
        } else if (ProductInfo.isCarryProduct()) {
            Settings.Global.putString(context.getContentResolver(), Definition.ROBOT_PLATFORM_TYPE_KEY, Definition.ROBOT_PLATFORM_CARRY);
        } else if (ProductInfo.isSaiphPro()) {
            Settings.Global.putString(context.getContentResolver(), Definition.ROBOT_PLATFORM_TYPE_KEY, Definition.ROBOT_PLATFORM_SAIPH_PRO);
        } else if (ProductInfo.isSlimProduct()) {
            Settings.Global.putString(context.getContentResolver(), Definition.ROBOT_PLATFORM_TYPE_KEY, Definition.ROBOT_PLATFORM_SLIM);
        } else if (ProductInfo.isDeliveryProduct()) {//招财
            Settings.Global.putString(context.getContentResolver(), Definition.ROBOT_PLATFORM_TYPE_KEY, Definition.ROBOT_PLATFORM_DELIVERY);
        }

        if (ProductInfo.isDeliveryProduct()) {
            Settings.Global.putInt(context.getContentResolver(), Definition.ROBOT_PLATFORM_IS_DELIVERY_PRODUCT, 1);
        }

        if (ProductInfo.isOverSea()) {
            Settings.Global.putInt(context.getContentResolver(), Definition.ROBOT_PLATFORM_IS_OVERSEA_KEY, 1);
        }
    }

    private static void updateTopIRToSetting(Context context) {
        updateConfigToSetting(context, "top_ir", isHasTopIR());
    }

    private static void updateChargeIRToSetting(Context context) {
        updateConfigToSetting(context, "charge_ir", isHasChargeIR());
    }

    private static void updateConfigToSetting(Context context, String key, boolean currentState) {
        int settingState = Settings.Global.getInt(context.getContentResolver(), key, 0);
        if (currentState && settingState == 0) {
            Settings.Global.putInt(context.getContentResolver(), key, 1);
        } else if (!currentState && settingState == 1) {
            Settings.Global.putInt(context.getContentResolver(), key, 0);
        }
    }

    private static void updateMapCompatibilityConfig(Context context) {
        PublicConfig config = getCoreConfig().getPublicConfig();
        MapCompatibilityConfig mapCompatibilityConfig;
        if (null != config && null != (mapCompatibilityConfig = config.getMapCompatibilityConfig())) {
            Settings.Global.putInt(context.getContentResolver(), Definition.ROBOT_MAP_COMPATIBLE_VERSION, mapCompatibilityConfig.getMapCompatibleVersion());
        }
    }

    private static void sendBroadcast(Context context, int level) {
        Log.d(TAG, " sendBroadcast LogLevel = " + level);
        Intent intent = new Intent();
        intent.setAction(LogBroadcastReceiver.ACTION);
        intent.putExtra(LogBroadcastReceiver.KEY, level + "");
        context.sendStickyBroadcast(intent);
    }

    public static List<ServiceConfig> getServiceConfig() {
        if (config != null) {
            List<ServiceConfig> configs = config.getServiceConfig();
            for (ServiceConfig serviceConfig : configs) {
                if (RobotOS.REMOTE_SERVICE.equals(serviceConfig.getServiceName())) {
                    serviceConfig.setSkuConfig(getSKUConfig());
                }
            }
            return configs;
        }
        return new ArrayList<>();
    }

    public static CoreConfig getCoreConfig() {
        if (config != null) {
            return config.getCoreConfig();
        }
        return new CoreConfig();
    }

    public static AccountConfig getAccountConfig() {
        AccountConfig config = getCoreConfig().getAccountConfig();
        return config == null ? new AccountConfig(null) : config;
    }

    public static HeadConfig getHeadConfig() {
        HeadConfig config = getCoreConfig().getHeadConfig();
        return config == null ? new HeadConfig(null) : config;
    }

    public static BatteryConfig getBatteryConfig() {
        BatteryConfig config = getCoreConfig().getBatteryConfig();
        return config == null ? new BatteryConfig(null) : config;
    }

    public static NavigationConfig getNavigationConfig() {
        NavigationConfig config = getCoreConfig().getNavigationConfig();
        return config == null ? new NavigationConfig(null) : config;
    }

    public static CoreConfig.StandbyConfig getStandbyConfig() {
        CoreConfig.ConfigDetail configDetail = getCoreConfig().getConfigDetail();
        return configDetail == null ? null : configDetail.getStandbyConfig();
    }

    public static ShutdownConfig getShutdownConfig() {
        CoreConfig.ConfigDetail configDetail = getCoreConfig().getConfigDetail();
        return configDetail == null ? null : configDetail.getShutdownConfig();
    }

    public static PublicConfig getPublicConfig() {
        PublicConfig config = getCoreConfig().getPublicConfig();
        return config == null ? new PublicConfig(null) : config;
    }

    public static CoreConfig.LogConfig getLogConfig() {
        CoreConfig.ConfigDetail configDetail = getCoreConfig().getConfigDetail();
        return configDetail == null ? null : configDetail.getLogConfig();
    }

    /**
     * 是否有顶部IR
     * 招财豹基础版有部分机器没有 properties 配置，是通过 RobotConfig 中配置，另外一部分机器通过 properties 配置
     * 新版本机器使用新结构 properties 配置
     *
     * @return
     */
    public static boolean isHasTopIR() {
        if (DeviceSubType.getInstance().isNewDeviceInfo()) {
            return DeviceSubType.getInstance().hasTopIr();
        }
        //没有新结构参数，才使用旧的配置
        //旧的properties配置 和 RobotConfig配置 有一个有mono就认为有mono
        CoreConfig.ConfigDetail configDetail = getCoreConfig().getConfigDetail();
        return configDetail != null && configDetail.isHasTopIR() ||
                DeviceSubType.getInstance().hasTopIr();
    }

    public static boolean isHasChargeIR() {
        return isBackCharge() || isFrontCharge();
    }

    public static boolean isBackCharge() {
        if (DeviceSubType.getInstance().isNewDeviceInfo()) {
            return DeviceSubType.getInstance().hasChargeIr();
        }
        //没有新结构参数，才使用旧的配置
        //旧的properties配置 和 RobotConfig配置 有一个有mono就认为有mono
        CoreConfig.ConfigDetail configDetail = getCoreConfig().getConfigDetail();
        return configDetail != null && configDetail.isHasChargeIR() ||
                DeviceSubType.getInstance().hasChargeIr();
    }

    public static boolean isFrontCharge() {
        return DeviceSubType.getInstance().hasFrontChargeCamera();
    }

    public static boolean isUseAutoEffectLed() {
        CoreConfig.ConfigDetail configDetail = getCoreConfig().getConfigDetail();
        return configDetail != null && configDetail.isUseAutoEffectLed();
    }

    public static boolean isHasClavicleLed() {
        CoreConfig.ConfigDetail configDetail = getCoreConfig().getConfigDetail();
        return configDetail != null && configDetail.isHasClavicleLed();
    }

    public static boolean isHasChestLed() {
        CoreConfig.ConfigDetail configDetail = getCoreConfig().getConfigDetail();
        return configDetail != null && configDetail.isHasChestLed();
    }

    public static boolean isHasTrayLight() {
        CoreConfig.ConfigDetail configDetail = getCoreConfig().getConfigDetail();
        return configDetail != null && configDetail.isHasTrayLight();
    }

    /**
     * 是否有 mono相机，包括顶部 mono和底部 mono，决定是否支持视觉功能
     * 旧版本底部 mono 在 RobotConfig 和 旧版本 properties 中配置，
     * 新版本 top mono 在新结构 properties 中配置
     *
     * @return
     */
    public static boolean hasMono() {
        if (DeviceSubType.getInstance().isNewDeviceInfo()) {
            return DeviceSubType.getInstance().hasTopMono()
                    || DeviceSubType.getInstance().hasMono();
        }
        //没有新结构参数，才使用旧的配置
        //旧的properties配置 和 RobotConfig配置 有一个有mono就认为有mono
        CoreConfig.ConfigDetail configDetail = getCoreConfig().getConfigDetail();
        return configDetail != null && configDetail.isHasMono() ||
                DeviceSubType.getInstance().hasTopMono() || DeviceSubType.getInstance().hasMono();
    }

    /**
     * TopMono 不存在 RobotConfig 中配置，只能通过 DeviceSubType 获取
     *
     * @return
     */
    public static boolean hasTopMono() {
        return DeviceSubType.getInstance().hasTopMono();
    }

    public static boolean isRobotSupportVision() {
        PublicConfig config = getCoreConfig().getPublicConfig();
        MapCompatibilityConfig mapCompatibilityConfig;
        List<Integer> visionTypes;
        return null != config
                && null != (mapCompatibilityConfig = config.getMapCompatibilityConfig())
                && null != (visionTypes = mapCompatibilityConfig.getVisionTypes())
                && visionTypes.contains(Definition.VisionType.TYPE_VISION.getValue());
    }

    public static boolean isSupportElevatorService() {
        Log.d(TAG, "isSupportElevatorService: ");
        if(config != null) {
            ServiceConfig elevatorServiceConfig = config.getElevatorServiceConfig();
            Log.d(TAG, "isSupportElevatorService elevatorServiceConfig: " + (elevatorServiceConfig!=null?
                    elevatorServiceConfig.toString() : "null"));
            return elevatorServiceConfig != null && elevatorServiceConfig.isEnable();
        }
        return false;
    }

    /**
     * 是否支持电梯功能
     * 先判断本地配置:device.priorities和config，
     * 如果本地配置不支持电梯功能或者没有配置，则判断后台配置
     */
    public static boolean isSupportElevatorFunction() {
        //device.priorities
        boolean devicePriorities = ProductInfo.isElevatorCtrlProduct();
        //config
        boolean config = isSupportElevatorService();
        Log.d(TAG, "isSupportElevatorFunction devicePriorities: " + devicePriorities + " config: " + config);
        if(devicePriorities || config) {
            return true;
        }

        //bo后台配置
        String elevatorConfig = RobotSettings.getGlobalSettings(ApplicationWrapper.getContext(), Definition.SETTING_ELEVATOR_CONFIG, "");
        Log.d(TAG, "isSupportElevatorFunction elevatorConfig: " + elevatorConfig);
        if(!TextUtils.isEmpty(elevatorConfig)) {
            String ecapiProvider = "";
            try {
                JSONObject json = new JSONObject(elevatorConfig);
                // 解析 ecapi_provider 字段
                ecapiProvider = json.getString("ecapi_provider");
                Log.d(TAG, "isSupportElevatorFunction ecapiProvider: " + ecapiProvider);
            } catch (Exception e) {
                Log.e(TAG, "isSupportElevatorFunction error: " + e.getMessage());
            }
            return !TextUtils.isEmpty(ecapiProvider);
        }

        Log.d(TAG, "isSupportElevatorFunction false");
        return false;
    }

    public static JsonObject getSKUConfig() {
        JsonObject json = new JsonObject();
        try {
            json.addProperty("content", DeviceSubType.getInstance().readFile());
            json.addProperty("topIr", isHasTopIR());
            json.addProperty("topMono", hasTopMono());
            json.addProperty("frontCamera", hasMono());
            json.addProperty("backCamera", isHasChargeIR());
            json.addProperty("usbTopIr", uvcCamera.hasTopIr());
            json.addProperty("usbTopMono", uvcCamera.hasTopMono());
            json.addProperty("usbFrontCamera", uvcCamera.hasFrontCamera());
            json.addProperty("usbBackCamera", uvcCamera.hasBackCamera());
            json.addProperty("createMapType", getCreateMapType());
        } catch (Exception e) {
            e.printStackTrace();
        }
        Log.d(TAG, "sku config : " + json);
        return json;
    }

    /**
     * 获取支持的创建地图类型
     * <p>
     * 硬件类型：
     * topIr
     * topMono
     * frontCamera（mono）
     * <p>
     * 建图类型：
     * 标准模式：target，有topIr或topMono，没有frontCamera
     * 高级模式：纯vision或vision+target，有frontCamera就支持高级模式，有topIr或topMono支持target
     * 高级视觉模式：纯vision（前视+顶视），有frontCamera+topMono
     * //纯激光：laser，都没有（不支持）
     * //模式L/D：只在机器端韩国时区支持，远程建图不需要支持
     * <p>
     * 返回结果为支持类型的集合，如："standard,visionAndTarget,vision"，结果字符串为英文逗号分隔
     */
    public static String getCreateMapType() {
        boolean topIr = isHasTopIR();
        boolean topMono = hasTopMono();
        boolean frontCamera = hasMono();
        Log.d(TAG, "getCreateMapType: topIr: " + topIr + " topMono: " + topMono + " frontCamera: " + frontCamera);

        StringBuilder result = new StringBuilder();

        //标准模式始终默认支持，不受硬件配置影响
        result.append(Definition.CreateMapType.STANDARD.getType());

        // 高级模式
        if (frontCamera) {
            if (result.length() > 0) {
                result.append(",");
            }
            result.append(Definition.CreateMapType.VISION_AND_TARGET.getType());
        }

        // 高级视觉模式
        if (frontCamera && topMono) {
            if (result.length() > 0) {
                result.append(",");
            }
            result.append(Definition.CreateMapType.VISION.getType());
        }

        String finalResult = result.toString();
        Log.d(TAG, "getCreateMapType: result: " + finalResult);
        return finalResult;
    }

}
