package com.ainirobot.coreservice.bi;

import android.app.Service;
import android.content.Intent;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Handler;
import android.os.IBinder;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.bi.server.Bi;
import com.ainirobot.coreservice.bi.server.BiSocketServer;
import com.ainirobot.coreservice.client.ApiListener;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.module.ModuleCallbackApi;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.utils.DelayTask;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class BiReportService extends Service {
    private static final String TAG = "BiReportService";

    private static final long BI_LEVEL_UPDATE_INTERVAL = 60 * 60 * 1000;

    private Object TAG_BI_LEVEL = new Object();
    private Object TAG_RETRY = new Object();
    private SystemApi mApi;

    private static final String URI = "content://com.ainirobot.coreservice.robotsettingprovider/setting";

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "Start bi report service");
        mApi = SystemApi.getInstance();
        connectCore();
        Bi.init(this);
        BiSocketServer biServer = new BiSocketServer();
        biServer.start();
        startUpdateBiLevelTask();
        if (ProductInfo.isMiniProduct() || ProductInfo.isMeissa2()) {
            setUsableWhenChargingObserver();
        }
    }

    /**
     * 连接CoreService
     */
    private void connectCore() {
        Log.d(TAG, "Connect to core service");
        mApi.connect(this, new ApiListener() {
            @Override
            public void handleApiDisabled() {
            }

            @Override
            public void handleApiConnected() {
                Log.d(TAG, "Handle api connected : BiReportService");
                DelayTask.cancel(TAG);
                mApi.setCallback(new ModuleCallbackApi());
            }

            @Override
            public void handleApiDisconnected() {
                Log.d(TAG, "Handle api disconnected : BiReportService");
                reconnect();
            }
        });

        reconnect();
    }

    /**
     * 重连CoreService
     */
    private void reconnect() {
        Log.d(TAG, "Reconnect to core service");
        DelayTask.submit(TAG, new Runnable() {
            @Override
            public void run() {
                if (mApi.isApiConnectedService()) {
                    Log.d(TAG, "The core service is connected");
                    return;
                }
                connectCore();
            }
        }, 3000);
    }

    private void setUsableWhenChargingObserver() {
        updateUsableWhenCharging();
        Uri uri = Uri.withAppendedPath(Uri.parse(URI), Definition.ROBOT_USABLE_WHEN_CHARGING);
        getContentResolver().registerContentObserver(uri, false, new ContentObserver(new Handler()) {
            @Override
            public void onChange(boolean selfChange, Uri uri) {
                super.onChange(selfChange, uri);
                updateUsableWhenCharging();
            }
        });
    }

    private void updateUsableWhenCharging() {
        String value = Bi.getRobotString(this, Definition.ROBOT_USABLE_WHEN_CHARGING);
        HashMap<String, String> map = new HashMap<>();
        map.put("opk_type", 6+"");
        map.put("type", Definition.ROBOT_USABLE_WHEN_CHARGING);
        map.put("value", value);
        map.put("ctime", String.valueOf(System.currentTimeMillis()));
        Bi.report("minitob_opk_setting", mapToStr(map), false);
    }

    private String mapToStr(Map<String, String> dataMap) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : dataMap.entrySet()) {
            sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        if (sb.length() > 1) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    private void startUpdateBiLevelTask() {
        Log.d(TAG, "startUpdateBiLevelTask");
        DelayTask.cancel(TAG_BI_LEVEL);
        DelayTask.submit(TAG_BI_LEVEL, new Runnable() {
            @Override
            public void run() {
                if (!mApi.isApiConnectedService()) {
                    Log.d(TAG, "startUpdateBiLevelTask, The core service is disconnected");
                    return;
                }
                cancelRetryTask();
                getBiLevelFromServer();
            }
        }, 3000, BI_LEVEL_UPDATE_INTERVAL);
    }

    /**
     * 业务埋点级别默认为详细0，不可修改；
     * 调用链埋点级别可设置；
     */
    private void getBiLevelFromServer() {
        Log.d(TAG, "getBiLevelFromServer");
        mApi.checkMiniTrackLevel(0, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.i(TAG, "getBiLevelFromServer result=" + result + ", message=" + message);
                if (result == 1 && !Definition.FAILED.equals(message)) {
                    handleLevelResult(message);
                } else {
                    startRetryTask();
                }
            }

            @Override
            public void onError(int errorCode, String errorString) {
                Log.i(TAG, "getBiLevelFromServer errorCode=" + errorCode
                        + ", errorString=" + errorString);
                startRetryTask();
            }
        });
    }

    private void handleLevelResult(String result) {
        if (TextUtils.isEmpty(result)) {
            Log.e(TAG, "Level result null error");
            return;
        }
        try {
            JSONObject object = new JSONObject(result);
            if (object.has(Definition.JSON_EVENT_CALLCHAIN_TRACK_LEVEL)) {
                int level = object.getInt(Definition.JSON_EVENT_CALLCHAIN_TRACK_LEVEL);
                updateCallChainLevel(level);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void updateCallChainLevel(int level) {
        int curLevel = RobotSettingApi.getInstance().
                getRobotInt(Definition.ROBOT_SETTING_BI_LEVEL_CALL_CHAIN);
        Log.d(TAG, "updateCallChainLevel, new level=" + level + ", curLevel=" + curLevel);
        if (curLevel != level) {
            RobotSettingApi.getInstance().
                    setRobotInt(Definition.ROBOT_SETTING_BI_LEVEL_CALL_CHAIN, level);
        }
    }

    private void startRetryTask() {
        Log.i(TAG, "startRetryTask");
        cancelRetryTask();
        DelayTask.submit(TAG_RETRY, new Runnable() {
            @Override
            public void run() {
                getBiLevelFromServer();
            }
        }, 60 * 1000);
    }

    private void cancelRetryTask() {
        Log.i(TAG, "cancelRetryTask");
        DelayTask.cancel(TAG_RETRY);
    }

    @Override
    public IBinder onBind(Intent intent) {
        throw new UnsupportedOperationException("Not yet implemented");
    }
}
