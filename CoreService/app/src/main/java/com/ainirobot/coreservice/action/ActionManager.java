/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.action;

import android.content.Context;
import android.util.Log;
import android.util.SparseArray;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.action.ActionCallbackProxy.Response;
import com.ainirobot.coreservice.bi.report.ActionManagerReporter;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.core.InternalDef;
import com.ainirobot.coreservice.core.RobotSettingManager;
import com.ainirobot.coreservice.core.external.ExternalServiceManager;
import com.ainirobot.coreservice.data.core.ActionDataHelper;
import com.ainirobot.coreservice.listener.IActionListener;
import com.ainirobot.coreservice.resmanage.ResManager;
import com.ainirobot.coreservice.service.CoreService;
import com.ainirobot.robotlog.RobotLog;

import java.util.ArrayDeque;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Objects;
import java.util.WeakHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class ActionManager {
    private static final String TAG = "ActionManager";

    enum Status {
        IDLE, INIT, RUN, PAUSE
    }

    interface ICache {
        AbstractAction getAction(int actionId, String[] resList);

        void removeRunAction(int actionId);

        AbstractAction finaRunAction(int actionId);

        SparseArray<AbstractAction> getRunningActions();
    }

    private class CommonActionCache implements ICache {
        private ArrayDeque<AbstractAction> mCache = new ArrayDeque<>();
        private SparseArray<AbstractAction> mRunningAction = new SparseArray<>();

        @Override
        public AbstractAction getAction(int actionId, String[] resList) {
            synchronized (this) {
                //if (mRunningAction.get(actionId) == null) {
//                if (mCache.isEmpty()) {
//                    action = new CommonAction(ActionManager.this, actionId, resList);
//                } else {
//                    action = mCache.pop();
//                    action.reuse(actionId, resList);
//                }

                AbstractAction action = new CommonAction(ActionManager.this, actionId, resList);
                mRunningAction.put(actionId, action);
                mReporter.reportCommonGetAction(actionId, Arrays.toString(resList), Objects.toString(action));
                return action;
                //}
            }

        }

        @Override
        public void removeRunAction(int actionId) {
            synchronized (this) {
                Log.d(TAG, "Remove action : " + actionId);
                AbstractAction action = mRunningAction.get(actionId);
                mReporter.reportCommonRemoveRunAction(actionId, Objects.toString(action));
                if (action != null && !action.isRunning()) {
                    //mCache.push(action);
                    mRunningAction.remove(actionId);
                }
            }
        }

        @Override
        public AbstractAction finaRunAction(int actionId) {
            AbstractAction action = mRunningAction.get(actionId);
            mReporter.reportCommonFinaRunAction(actionId, Objects.toString(action));
            return action;
        }

        @Override
        public SparseArray<AbstractAction> getRunningActions() {
            SparseArray<AbstractAction> runningActions = mRunningAction.clone();
            mReporter.reportCommonGetRunningActions(Objects.toString(runningActions));
            return runningActions;
        }
    }

    private class PersistentActionCache implements ICache {
        private WeakHashMap<Integer, AbstractAction> mCache = new WeakHashMap<>();
        //private AbstractAction[] mRunningAction = new AbstractAction[Definition.ACTION_PERSISTENT_MAX];
        private SparseArray<AbstractAction> mRunningAction = new SparseArray<>();

        @Override
        public AbstractAction getAction(int actionId, String[] resList) {
            AbstractAction action;

            synchronized (this) {
                if (mRunningAction.get(actionId) != null) {
                    return null;
                }

                action = mCache.get(actionId);
                if (action == null) {
                    Log.d(TAG, "Action new : " + actionId + "  " + (mRunningAction.get(actionId) == null));
                    action = ActionManager.this.createAction(actionId, resList);
                    mCache.put(actionId, action);
                    mRunningAction.put(actionId, action);
                } else {
                    Log.d(TAG, "Action  get cached : " + actionId);
                    mRunningAction.put(actionId, action);
                }
            }
            mReporter.reportPersistentGetAction(actionId, Arrays.toString(resList), Objects.toString(action));
            return action;
        }

        @Override
        public void removeRunAction(int actionId) {
            synchronized (this) {
                Log.d(TAG, "Action remove : " + actionId);
                mReporter.reportPersistentRemoveRunAction(actionId,
                        Objects.toString(mRunningAction.get(actionId)));
                mRunningAction.remove(actionId);
            }
        }

        @Override
        public AbstractAction finaRunAction(int actionId) {
            AbstractAction action;
            AbstractAction cloneAction = null;
            synchronized (this) {
                action = mRunningAction.get(actionId);
                if (action != null) {
                    cloneAction = (AbstractAction) action.clone();
                }
            }
            mReporter.reportPersistentFinaRunAction(actionId, Objects.toString(cloneAction));
            return action;
        }

        @Override
        public SparseArray<AbstractAction> getRunningActions() {
            SparseArray<AbstractAction> runningActions = mRunningAction.clone();
            mReporter.reportPersistentGetRunningActions(Objects.toString(runningActions));
            return runningActions;
        }

    }

    private CoreService mCoreService;
    private ActionManagerReporter mReporter;
    private PersistentActionCache mPersistentCache = new PersistentActionCache();
    private CommonActionCache mCommonCache = new CommonActionCache();
    private HashMap<Integer, ActionInfo> mActions;
    private ExecutorService mCallbackExecutor;

    public ActionManager(CoreService coreService) {
        mCoreService = coreService;
        mReporter = new ActionManagerReporter();
        mActions = new HashMap<>();
        mCallbackExecutor = new ThreadPoolExecutor(2, 10, 60,
                TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>());

        Context context = ApplicationWrapper.getContext();
        ActionDataHelper actionData = new ActionDataHelper(context);
        for (ActionInfo info : actionData.getAllAction()) {
            mActions.put(info.getActionId(), info);
        }
    }

    private ICache getCache(int actionId) {
        ActionInfo info = mActions.get(actionId);
        mReporter.reportGetCache(actionId, Objects.toString(info));
        return info.isCommon() ? mCommonCache : mPersistentCache;
    }

    public synchronized void exCmd(int actionId, String parameter, IActionListener callback) {
        exCmd(actionId, parameter, callback, InternalDef.ACTION_CALLER_SYSTEM, true);
    }

//    public synchronized void exCmd(int actionId, String parameter, IActionListener callback, boolean isSystem) {
//        exCmd(actionId, parameter, callback, InternalDef.ACTION_CALLER_SYSTEM, isSystem);
//    }

    public synchronized void exCmd(int actionId, String parameter, IActionListener callback, String caller, boolean isSystem) {
        RobotLog.i("Action manager exCmd, parameter: " + parameter);
        Response response = new Response();
        ActionCallbackProxy proxy = new ActionCallbackProxy(mCallbackExecutor, callback);

        ActionInfo info = mActions.get(actionId);
        if (info != null) {
            ICache cache = getCache(actionId);
            AbstractAction action = cache.getAction(actionId, info.getResource());
            RobotLog.d("cache.getAction is null ? " + (action == null));
            if (action == null) {
                response.responseCode = Definition.ACTION_RESPONSE_ALREADY_RUN;
                proxy.response(response);
            } else if (!isSystem && info.isSystem()) {
                response.responseCode = Definition.ACTION_RESPONSE_PERMISSION_DENIED;
                proxy.response(response);
            } else {
                action.setCaller(caller);
                if (!action.start(parameter, proxy, response)) {
                    cache.removeRunAction(actionId);
                    proxy.response(response);
                }
            }
        } else {
            response.responseCode = Definition.ACTION_RESPONSE_NOT_SUPPORT_ACTION;
            proxy.response(response);
        }

        mReporter.reportExCmd(actionId, parameter, Objects.toString(callback), isSystem, Objects.toString(info));
        RobotLog.i("Action manager exCmd, result: " + response.responseCode + " message : " + response.responseString + "   " + actionId);
//        if (response.responseCode != Definition.ACTION_RESPONSE_SUCCESS) {
//            proxy.response(response);
//        }
    }

    public synchronized boolean exStopCmd(int actionId, boolean isResetHW) {
        return exStopCmd(actionId, isResetHW, false);
    }

    public synchronized boolean exStopCmd(int actionId, boolean isResetHW, boolean isCancelStopCommand) {
        RobotLog.i("Action manager exStopCmd, parameter: " + actionId);
        boolean result = true;

        ICache cache = getCache(actionId);
        if (cache != null) {
            AbstractAction action = cache.finaRunAction(actionId);
            if (action != null && action.isRunning()) {
                Log.d(TAG, "exStopCmd actionId :" + action.getActionId() + ", actionStatus :" + action.getStatus());
                if (!action.stop(isResetHW, isCancelStopCommand)) {
                    result = false;
                }
            }
        }
        mReporter.reportExStopCmd(actionId, isResetHW, result);
        return result;
    }

    public synchronized void stopAllAction(String caller) {
        mReporter.reportStopAllAction();
        stopRunningAction(mCommonCache, caller);
        stopRunningAction(mPersistentCache, caller);
    }

//    public synchronized void stopAllAction() {
//        mReporter.reportStopAllAction();
//        Log.i(TAG, "stop all action");
//        stopAllAction(null);
//    }

    private void stopRunningAction(ICache cache, String caller) {
        SparseArray<AbstractAction> actions = cache.getRunningActions();
        mReporter.reportStopRunningAction(Objects.toString(actions.toString()));
        for (int index = 0; index < actions.size(); index++) {
            if (caller != null) {
                AbstractAction action = actions.valueAt(index);
                if (!caller.equals(action.getCaller())) {
                    continue;
                }
            }

            int actionId = actions.keyAt(index);
            exStopCmd(actionId, true);
        }
    }

    ResManager getResManager() {
        return mCoreService.getResManager();
    }

    public CoreService getCoreService() {
        return mCoreService;
    }

    private AbstractAction createAction(int id, String[] resList) {
        AbstractAction action = null;
        switch (id) {

            case Definition.ACTION_GONGFU:
                action = new GongFuAction(this, resList);
                break;

            case Definition.ACTION_LEAD:
                action = new LeadingAction(this, resList);
                break;

            case Definition.ACTION_FOCUS_FOLLOW:
                action = new FocusFollowAction(this, resList);
                break;

            case Definition.ACTION_SMART_FOCUS_FOLLOW:
                action = new SmartFocusFollowAction(this, resList);
                break;

            case Definition.ACTION_FACE_TRACK:
                action = new FaceTrackAction(this, resList);
                break;

            case Definition.ACTION_REGISTER:
                action = new RegisterActionNew(this, resList);
                break;

            case Definition.ACTION_FOLLOW:
                action = new FollowAction(this, resList);
                break;

            case Definition.ACTION_NAVIGATION:
                action = new NavigationAction(this, resList);
                break;

            case Definition.ACTION_POSE_NAVIGATION:
                action = new NavigationPoseAction(this, resList);
                break;

            case Definition.ACTION_SEARCH_TARGET:
                action = new SearchPersonAction(this, resList);
                break;

            case Definition.ACTION_INSPECTION:
                action = new InspectionAction(this, resList);
                break;

            case Definition.ACTION_WAKEUP:
                action = new WakeupAction(this, resList);
                break;

            case Definition.ACTION_AUTO_NAVI_CHARGE:
                //action = new AutoBackChargeAction(this, resList);
                action = new ChargeAdvancedAction(this, resList);
                break;

            case Definition.ACTION_SET_START_CHARGE_POSE:
                action = new SetStartChargePoseAction(this, resList);
                break;

            case Definition.ACTION_CRUISE:
                action = new CruiseAction(this, resList);
                break;

            case Definition.ACTION_RESET_ESTIMATE:
                action = new ResetEstimateAction(this, resList);
                break;

            case Definition.ACTION_LOCATE_VISION:
                action = new VisionLocateAction(this, resList);
                break;

            case Definition.ACTION_NAVI_STOP_CREATING_MAP:
                action = new StopCreateMapAction(this, resList);
                break;

            case Definition.ACTION_SPECIAL_CRUISE:
                action = new SpecialCruiseAction(this, resList);
                break;

            case Definition.ACTION_NAVIGATION_BACK:
                action = new NavigationBackAction(this, resList);
                break;

            case Definition.ACTION_ANGLE_RESET:
                action = new AngleResetAction(this, resList);
                break;

            case Definition.ACTION_HORIZONTAL_TURN_HEAD:
                action = new HorizontalTurnHeadAction(this, resList);
                break;

            case Definition.ACTION_RECOGNIZE_v0:
                action = new RecognizeAction(this, resList);
                break;

            case Definition.ACTION_RECOGNIZE_v1:
                action = new RecognizeAction_v1(this, resList);
                break;

            case Definition.ACTION_REGISTER_v1:
                action = new RegisterAction_v1(this, resList);
                break;

            case Definition.ACTION_ROBOT_STANDBY:
                action = new RobotStandbyAction(this, resList);
                break;

            case Definition.ACTION_GO_POSITION:
                action = new GoPositionAction(this, resList);
                break;

            case Definition.ACTION_NAVI_GO_POSITION_BY_TYPE:
                action = new GoPositionByTypeAction(this, resList);
                break;

            case Definition.ACTION_BODY_FOLLOW:
                action = new MotionFollowAction(this, resList);
                break;

            case Definition.ACTION_NAVIGATION_FOLLOW:
                action = new NavigationFollowAction(this, resList);
                break;

            case Definition.ACTION_HEAD_ACTIONS:
                action = new HeadAction(this, resList);
                break;

            case Definition.ACTION_BLUE_FOV_LIGHT:
                action = new BlueFovLightAction(this, resList);
                break;

            case Definition.ACTION_RED_FOV_LIGHT:
                action = new RedFovLightAction(this, resList);
                break;

            case Definition.ACTION_LEAVE_CHARGING_PILE:
                action = new LeavePileAction(this, id, resList);
                break;

            case Definition.ACTION_JUDGE_IN_CHARGING_PILE:
                action = new JudgeInChargingPile(this, id, resList);
                break;

            case Definition.ACTION_ADVANCED_NAVIGATION:
                action = new NavigationAdvancedAction(this, id, resList);
                break;

            case Definition.ACTION_DOWNLOAD_WHOLE_MAP_PKG:
                action = new DownloadMapAction(this, id, resList);
                break;
//            case Definition.ACTION_HEAD_RESET_HEAD:
//                action = new HeadResetAction(this, id, resList);
//                break;
//            case Definition.ACTION_NAVI_GO_POSITION:
//                action = new NaviGoPositionAction(this, id, resList);
//                break;
            case Definition.ACTION_RADAR_ALIGN:
                action = new RadarAlignAction(this, resList);
                break;

            case Definition.ACTION_CONTROL_ELECTRIC_DOOR:
                action = new ControlElectricDoorAction(this, id, resList);
                break;

            default:
                action = new CommonAction(this, id, resList);
                break;
        }
        mReporter.reportCreateAction(id, Arrays.toString(resList), Objects.toString(action));
        return action;
    }

    void onFinish(AbstractAction action) {
        mReporter.reportOnFinish(Objects.toString(action));
        ICache cache = getCache(action.getActionId());
        cache.removeRunAction(action.getActionId());
    }

    void registerStatusListener(String type, StatusListenerProxy listener) {
        listener.setId(mCoreService.getStatusManager().registerStatusListener(type, listener));
    }

    void unregisterStatusListener(String id) {
        mCoreService.getStatusManager().unregisterStatusListener(id);
    }

    void handleStatus(String type, String data) {
        mReporter.reportHandlerStatus(type, data);
        mCoreService.getStatusManager().handleStatus(type, data);
    }

    void notifyActionStatusChange(AbstractAction action, Status status) {

    }

    public ExternalServiceManager getServiceManager() {
        return mCoreService.getExternalServiceManager();
    }

    public RobotSettingManager getRobotSettingManager() {
        return mCoreService.getRobotSettingManager();
    }

    void setRobotSetting(String type, String data) {
        mReporter.reportSetRobotSetting(type, data);
        mCoreService.getRobotSettingManager().setRobotSetting(type, data);
    }
}
