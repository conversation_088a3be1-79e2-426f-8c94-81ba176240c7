package com.ainirobot.coreservice.action.advance_charge.state;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.ChargeAdvancedAction;
import com.ainirobot.coreservice.action.advance_charge.AdvanceChargeStateEnum;
import com.ainirobot.coreservice.action.advance_charge.bean.StateData;
import com.ainirobot.coreservice.bean.ChargeArea;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.MultiRobotConfigBean;
import com.ainirobot.coreservice.client.actionbean.MultiRobotStatus;
import com.ainirobot.coreservice.client.actionbean.PlaceBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

public class PrepareState extends BaseAdvanceChargeState {
    private boolean mIsAdvanceCharge = true;
    private ChargeArea mCurUseChargeArea = null; //找到本机使用的充电区，一个机器对应一个充电区
    private List<String> mChargePoints;

    @Override
    protected void onStart(ChargeAdvancedAction chargeAdvancedAction, StateData stateData) {
        if (isChargingTypePile()) {
            this.getCurrentUseChargeArea();
        } else {
            mIsAdvanceCharge = false;
            onStateRunSuccess();
        }
    }

    @Override
    public void stop() {
        super.stop();
        Log.d(TAG, "ChargeAdvanceAction stopAction ------------  ");
        mIsAdvanceCharge = true;
        mCurUseChargeArea = null;
    }

    private boolean isChargingTypePile() {
        String chargingType = mChargeAdvancedAction.getActionManager().getRobotSettingManager().
                getRobotSetting(Definition.ROBOT_SETTINGS_CHARGING_TYPE);
        return Definition.CHARGING_TYPE_PILE.equals(chargingType);
    }

    /**
     * 查询配置的充电区
     */
    private void getCurrentUseChargeArea() {
        mChargeAdvancedAction.getApi().queryChargeAreaConfig(mChargeAdvancedAction.getReqId());
    }

    /**
     * 获取所有回充点
     */
    private void getChargePointList() {
        mChargeAdvancedAction.getApi().getPlacesByType(mChargeAdvancedAction.getReqId(), Definition.CHARGING_POINT_TYPE);
    }

    /**
     * 查询多机配置
     */
    private void queryCurrentDeviceMultipleConfig() {
        mChargeAdvancedAction.getApi().queryCurrentDeviceMultipleConfig(mChargeAdvancedAction.getReqId());
    }

    /**
     * 找到当前机器使用的充电区域配置
     */
    private void findAllChargeAreaPose() {
        //获得所有点位
        List<Pose> allPlaceList = getAllPlaceList();

        List<Pose> availableWaitPoints = findPoint(allPlaceList, mCurUseChargeArea.getAvailableWaitPoints());
        if (null != availableWaitPoints && !availableWaitPoints.isEmpty()) {
            mStateData.setWaitPointList(availableWaitPoints);
        }

        List<Pose> availableChargePiles = findPoint(allPlaceList, mCurUseChargeArea.getAvailableChargePiles());
        if (null != availableChargePiles && !availableChargePiles.isEmpty()) {
            mStateData.setChargePileList(availableChargePiles);
        }

        List<Pose> availableChargePoints = findPoint(allPlaceList, mChargePoints);
        if (null != availableChargePoints && !availableChargePoints.isEmpty()) {
            mStateData.setChargePointList(availableChargePoints);
        }

        checkEstimate();
    }

    /**
     * 检查定位状态
     */
    private void checkEstimate() {
        if (isEstimate()) {
            queryCurrentDeviceMultipleConfig();
        } else {
            onStateRunFailed(Definition.ERROR_NOT_ESTIMATE, "not estimate");
        }
    }

    /**
     * 获得所有地点
     */
    private List<Pose> getAllPlaceList() {
        try {
            String result = mChargeAdvancedAction.getRobotInfo(Definition.SYNC_ACTION_GET_ALL_LOCATION, "");
            if (TextUtils.isEmpty(result)) {
                return null;
            }
            return mGson.fromJson(result, new TypeToken<List<Pose>>() {
            }.getType());
        } catch (Exception e) {
            e.printStackTrace();
            Log.d(TAG, "getAllPlaceList error");
            onStateRunFailed(Definition.ERROR_PARAMETER, "get all place json error");
            return null;
        }
    }

    /**
     * 找到指定名称的Pose List
     */
    private List<Pose> findPoint(List<Pose> poseList, List<String> findList) {
        if (null == poseList || poseList.size() <= 0) {
            return null;
        }

        ArrayList<Pose> resultList = new ArrayList<>();
        for (int j = 0; j < findList.size(); j++) {
            String name = findList.get(j);
            for (int i = 0; i < poseList.size(); i++) {
                Pose poseInfo = poseList.get(i);
                if (poseInfo == null || TextUtils.isEmpty(poseInfo.getName())) {
                    continue;
                }
                if (poseInfo.getName().equals(name)) {
                    resultList.add(poseInfo);
                }
            }
        }

        return resultList;
    }

    private boolean isEstimate() {
        return TextUtils.equals(mChargeAdvancedAction.getRobotInfo(Definition.SYNC_ACTION_IS_ROBOT_ESTIMATE, ""),
                Definition.RESULT_TRUE);
    }

    @Override
    protected boolean onCmdResponse(int cmdId, String command, String result, String extraData) {
        Log.d(TAG, "cmdResponse cmdType=" + command + " result=" + result);
        switch (command) {
            case Definition.CMD_NAVI_QUERY_CHARGE_AREA_CONFIG:
                processQueryChargeAreaInfo(result, extraData);
                break;
            case Definition.CMD_NAVI_GET_PLACELIST_BY_TYPE:
                processChargePointList(result, extraData);
                break;
            case Definition.CMD_NAVI_GET_MULTI_ROBOT_CONFIG:
                parseMultiRobotConfig(result);
                return true;
        }
        return false;
    }

    private void processQueryChargeAreaInfo(String result, String extraData) {
        mCurUseChargeArea = null;
        if (isResultSuccess(result)) {
            try {
                Type chargeAreaListType = new TypeToken<List<ChargeArea>>() {
                }.getType();
                List<ChargeArea> infoList = mGson.fromJson(result, chargeAreaListType);
                for (ChargeArea area : infoList) {
                    if (area.getUseState() == 1) {
                        Log.d(TAG, "processQueryChargeAreaInfo area=" + mGson.toJson(area));
                        mCurUseChargeArea = area;
                        mStateData.setCurUseChargeArea(area);
                        break;
                    }
                }

                Log.d(TAG, "processQueryChargeAreaInfo mCurUseChargeArea=" + mGson.toJson(mCurUseChargeArea));
                if (mCurUseChargeArea != null) {
                    getChargePointList();
                } else {
                    mIsAdvanceCharge = false;
                    onStateRunSuccess();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            mIsAdvanceCharge = false;
            onStateRunSuccess();
        }
    }

    private void processChargePointList(String result, String extraData) {
        if (isResultSuccess(result)) {
            try {
                Type placeInfoListType = new TypeToken<List<PlaceBean>>() {
                }.getType();
                List<PlaceBean> infoList = mGson.fromJson(result, placeInfoListType);

                this.mChargePoints = new ArrayList<>();
                List<String> chargePiles = mCurUseChargeArea.getAvailableChargePiles();
                for (int i = 0; i < chargePiles.size(); i++) {
                    for (PlaceBean placeBean : infoList) {
                        if (placeBean != null) {
                            String regex = ".+\\d+";
                            String chargePile = chargePiles.get(i);

                            String chargePoint = placeBean.getPlaceName();
                            String[] chargePointArr = chargePoint.split("-");

                            if (chargePointArr.length > 1 && chargePoint.endsWith(chargePile)) {
                                mChargePoints.add(chargePoint);
                            } else {
                                if (!chargePile.matches(regex) && chargePointArr.length == 1) {
                                    mChargePoints.add(chargePoint);
                                }
                            }
                        }
                    }
                }

                findAllChargeAreaPose();

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 解析多机配置
     */
    private void parseMultiRobotConfig(String result) {
        MultiRobotConfigBean multiRobotConfig = null;
        try {
            multiRobotConfig = mGson.fromJson(result, MultiRobotConfigBean.class);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (null == multiRobotConfig) {
            onStateRunFailed(Definition.ERROR_MULTI_ROBOT_CONFIG_ERROR, "multi robot config is null");
            return;
        }

        //判断多机是否可用
        judgeMultipleRobotEnable(multiRobotConfig);
    }

    private void judgeMultipleRobotEnable(MultiRobotConfigBean config) {
        Log.d(TAG, "judgeMultipleRobotEnable:" + config.isEnable());
        if (!config.isEnable()) {
            Log.i(TAG, "judgeMultipleRobotEnable false start default navi");
            mIsAdvanceCharge = false;
            onStateRunSuccess();
            return;
        }

        if (config.getErrorStatus() > 0) {
            onStateRunFailed(Definition.STATUS_NAVI_MULTI_LORA_CONFIG_FAIL, "lora is in error status");
            return;
        }
        //多机正常，执行下个流程
        mIsAdvanceCharge = true;
        onStateRunSuccess();
    }

    @Override
    protected boolean onCmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        return false;
    }

    @Override
    protected AdvanceChargeStateEnum getNextState() {
        return mIsAdvanceCharge ? AdvanceChargeStateEnum.GO_CHARGE_AREA : AdvanceChargeStateEnum.GO_NORMAL_CHARGE;
    }

}
