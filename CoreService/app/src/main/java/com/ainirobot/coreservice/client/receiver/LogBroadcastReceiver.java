package com.ainirobot.coreservice.client.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.log.RLog;

public class  LogBroadcastReceiver extends BroadcastReceiver {
    public static final String ACTION = "com.ainirobot.coreservice.client.LogBroadcastReceiver";
    public static final String KEY = "log_level_broadcast_receiver_key";
    @Override
    public void onReceive(Context context, Intent intent) {
        if(intent!=null&&ACTION.equals(intent.getAction())) {
            String level = intent.getStringExtra(KEY);
            Log.d("LogBroadcastReceiver ", " level = " + level );
            if (!TextUtils.isEmpty(level)){
                RLog.setLogLevel(Integer.parseInt(level));
            }
        }

    }
}
