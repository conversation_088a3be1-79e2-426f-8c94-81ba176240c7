/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.core.account;

import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.IAccountApi;
import com.ainirobot.coreservice.client.account.AccountResult;
import com.ainirobot.coreservice.client.account.UserBean;
import com.ainirobot.coreservice.client.person.FaceBean;
import com.ainirobot.coreservice.core.account.bean.RequestBean;
import com.ainirobot.coreservice.core.apiproxy.impl.account.AccountApiProxy;
import com.ainirobot.coreservice.core.apiproxy.result.BaseApiResult;
import com.ainirobot.coreservice.data.account.MemberDataHelper;
import com.ainirobot.coreservice.listener.IAccountListener;
import com.ainirobot.coreservice.listener.IActionListener;
import com.ainirobot.coreservice.service.CoreService;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

import static com.ainirobot.coreservice.core.apiproxy.BaseApiProxy.MSG_API_RESULT;
import static com.ainirobot.coreservice.core.person.PersonManager.MSG_RE_REGISTER;

public class AccountManager extends IAccountApi.Stub {

    private static final String TAG = AccountManager.class.getSimpleName();

    private static final String LOGIN_URI = "content://com.ainirobot.account_provider/token";
    private static final int MSG_TRIGGER_GENERAL_USER = 0x1;
    private static final int MSG_REMOTE_SYNC_MEMBER = 0x2;
    private static final int MSG_TRIGGER_FACE_DATA_UPLOAD = 0x3;
    private static final int MSG_RECOVERY = 0x4;
    private static final long TIMEOUT_TRIGGER_GENERAL_USER = 2 * 60 * 1000;
    private static final long TIMEOUT_REMOTE_SYNC_MEMBER = 10 * 60 * 1000;
    private static final long TIMEOUT_TRIGGER_FACE_DATA_UPLOAD = 60 * 60 * 1000;

    private static final String ACTION_FACTORY_RESET = "android.intent.action.FACTORY_RESET";
    private static final String EXTRA_REASON = "android.intent.extra.REASON";
    private static final String EXTRA_WIPE_EXTERNAL_STORAGE = "android.intent.extra.WIPE_EXTERNAL_STORAGE";
    private static final String EXTRA_WIPE_ESIMS = "com.android.internal.intent.extra.WIPE_ESIMS";

    private MemberDataHelper mDataHelper;
    private AccountHandler mHandler;
    private AccountApiProxy mApiProxy;
    private final List<IAccountListener> mAccountListenerList;
    private RemoteUpdateState mRemoteUpdateState;
    private RecoveryState mRecoveryState;
    private RequestBean mRecoveryBean;
    private Gson mGson;
    private CoreService mCore;

    private enum RecoveryState {
        IDLE, UNBINDING, RECOVERING
    }

    private enum RemoteUpdateState {
        IDLE, UPDATING
    }

    public AccountManager(CoreService core) {
        HandlerThread thread = new HandlerThread("AccountManager");
        thread.start();
        mCore = core;
        mGson = new Gson();
        mRemoteUpdateState = RemoteUpdateState.IDLE;
        mRecoveryState = RecoveryState.IDLE;
        mAccountListenerList = new CopyOnWriteArrayList<>();
        mHandler = new AccountHandler(this, thread.getLooper());
        mDataHelper = MemberDataHelper.getInstance(core);
        new TokenManager(core);
        mApiProxy = new AccountApiProxy(core, mHandler);
        mApiProxy.registerNetworkStateListener();
        mApiProxy.registerRemoteLoginStateListener();
        mApiProxy.registerTokenTableChangeListener();
        mApiProxy.registerFaceTableChangeListener();
        mDataHelper.updateAllUserTokens();
        mHandler.sendEmptyMessageDelayed(MSG_TRIGGER_GENERAL_USER, TIMEOUT_TRIGGER_GENERAL_USER);
        mHandler.sendEmptyMessageDelayed(MSG_REMOTE_SYNC_MEMBER, TIMEOUT_REMOTE_SYNC_MEMBER);
        mHandler.sendEmptyMessageDelayed(MSG_TRIGGER_FACE_DATA_UPLOAD,
                TIMEOUT_TRIGGER_FACE_DATA_UPLOAD);
    }

    public static class AccountHandler extends Handler {

        private AccountManager mManager;

        private AccountHandler(AccountManager manager, Looper looper) {
            super(looper);
            mManager = manager;
        }

        @Override
        public void handleMessage(Message msg) {
            Log.d(TAG, "handleMessage what: " + msg.what);
            switch (msg.what) {
                case MSG_REMOTE_SYNC_MEMBER:
                    mManager.remoteSyncMemberData();
                    break;
                case MSG_TRIGGER_GENERAL_USER:
                    mManager.updateRemoteUpdateState(RemoteUpdateState.IDLE);
                    mManager.triggerGeneralUser();
                    break;
                case MSG_TRIGGER_FACE_DATA_UPLOAD:
                    mManager.triggerFaceDataUpload();
                    break;
                case MSG_API_RESULT:
                    mManager.handleApiResult((BaseApiResult) msg.obj);
                    break;
                case MSG_RECOVERY:
                    RequestBean bean = (RequestBean) msg.obj;
                    if (mManager.mRecoveryState != RecoveryState.IDLE) {
                        mManager.processResult(bean, AccountResult.Recovery.RECOVERY_ALREADY_RUN,
                                "recovery already run");
                        return;
                    }
                    mManager.startRecoveryTask(bean);
                    break;
                default:
                    break;
            }
        }
    }

    private void startRecoveryTask(RequestBean bean) {
        if (TextUtils.isEmpty(bean.getUserId())) {
            processResult(bean, AccountResult.Recovery.RECOVERY_USER_ID_EMPTY,
                    "user id null");
            return;
        }
        UserBean userBean = mDataHelper.getFamilyAdmin();
        if (userBean == null || !bean.getUserId().equals(userBean.getUserId())) {
            processResult(bean, AccountResult.Recovery.RECOVERY_USER_ID_INVALID,
                    "user id invalid");
            return;
        }
        updateRecoveryState(RecoveryState.UNBINDING);
        mHandler.removeMessages(MSG_TRIGGER_FACE_DATA_UPLOAD);
        mHandler.removeMessages(MSG_REMOTE_SYNC_MEMBER);
        mHandler.removeMessages(MSG_TRIGGER_GENERAL_USER);
        mRecoveryBean = bean;
        mApiProxy.unbind(userBean.getUserId(), "");
    }

    @SuppressWarnings("unchecked")
    private void handleApiResult(BaseApiResult apiResult) {
        Log.d(TAG, "handleApiResult apiResult: " + apiResult);
        BaseApiResult.Type type = apiResult.getType();
        int result = apiResult.getResult();
        Object data = apiResult.getData();
        switch (type) {
            case NETWORK_STATE:
            case REMOTE_LOGIN_STATE:
                triggerGeneralUser();
                break;
            case FACE_TABLE_CHANGED:
                triggerGeneralUser();
                notifyMemberUpdate();
                break;
            case REMOTE_SYNC_MEMBER:
                if (BaseApiResult.RESULT_SUCCESS == result) {
                    List<UserBean> memberData
                            = (List<UserBean>) data;
                    mDataHelper.updateMemberData(memberData);
                    notifyMemberUpdate();
                }
                break;
            case TOKEN_TABLE_CHANGED:
                mDataHelper.updateAllUserTokens();
                break;
            case REGISTER_FACE_LIST:
                if (result == BaseApiResult.RESULT_SUCCESS) {
                    mDataHelper.insertFaceIdAndUserMap((Map<String, UserBean>) data);
                    notifyMemberUpdate();
                }
                updateRemoteUpdateState(RemoteUpdateState.IDLE);
                remoteSyncMemberData();
                break;
            case REMOTE_UNBIND:
                if (mRecoveryState == RecoveryState.UNBINDING) {
                    if (result == BaseApiResult.RESULT_SUCCESS) {
                        updateRecoveryState(RecoveryState.RECOVERING);
                        processResult(mRecoveryBean, AccountResult.SUCCESS, "");
                        final Intent intent = new Intent(ACTION_FACTORY_RESET);
                        intent.setPackage("android");
                        intent.addFlags(Intent.FLAG_RECEIVER_FOREGROUND);
                        intent.putExtra(EXTRA_REASON, ApplicationWrapper.getContext().getPackageName() + " Master Clear");
                        intent.putExtra(EXTRA_WIPE_EXTERNAL_STORAGE, true);
                        intent.putExtra(EXTRA_WIPE_ESIMS, true);
                        ApplicationWrapper.getContext().sendBroadcast(intent);
                        return;
                    } else {
                        updateRecoveryState(RecoveryState.IDLE);
                        processResult(mRecoveryBean, AccountResult.Recovery.RECOVERY_FAILED, "");
                    }
                }
                mHandler.sendEmptyMessageDelayed(MSG_TRIGGER_GENERAL_USER, TIMEOUT_TRIGGER_GENERAL_USER);
                mHandler.sendEmptyMessageDelayed(MSG_REMOTE_SYNC_MEMBER, TIMEOUT_REMOTE_SYNC_MEMBER);
                mHandler.sendEmptyMessageDelayed(MSG_TRIGGER_FACE_DATA_UPLOAD,
                        TIMEOUT_TRIGGER_FACE_DATA_UPLOAD);
                break;
            default:
                break;
        }
    }

    private synchronized void triggerGeneralUser() {
        Log.d(TAG, "triggerGeneralUser mRemoteUpdateState: " + mRemoteUpdateState);
        if (mRemoteUpdateState != RemoteUpdateState.IDLE) {
            return;
        }
        String version = mDataHelper.getRegisterAlgorithmVersion();
        List<FaceBean> faceBeanList = mDataHelper.getFaceList();
        if (TextUtils.isEmpty(version) || faceBeanList == null || faceBeanList.size() <= 0) {
            return;
        }

        List<FaceBean> registerFaceList = new ArrayList<>();
        for (FaceBean faceBean : faceBeanList) {
            if (TextUtils.isEmpty(faceBean.getUserId())) {
                registerFaceList.add(faceBean);
            }
        }

        mHandler.removeMessages(MSG_TRIGGER_GENERAL_USER);
        mHandler.sendEmptyMessageDelayed(MSG_TRIGGER_GENERAL_USER,
                TIMEOUT_TRIGGER_GENERAL_USER);

        if (registerFaceList.size() <= 0) {
            remoteSyncMemberData();
            return;
        }

        mApiProxy.registerFaceList(version, registerFaceList);
        updateRemoteUpdateState(RemoteUpdateState.UPDATING);
    }

    private void triggerFaceDataUpload() {
        Log.d(TAG, "triggerFaceDataUpload");
        mHandler.removeMessages(MSG_TRIGGER_FACE_DATA_UPLOAD);
        mHandler.sendEmptyMessageDelayed(MSG_TRIGGER_FACE_DATA_UPLOAD,
                TIMEOUT_TRIGGER_FACE_DATA_UPLOAD);
        String version = mDataHelper.getRegisterAlgorithmVersion();
        List<FaceBean> faceBeanList = mDataHelper.getFaceList();
        if (TextUtils.isEmpty(version) || faceBeanList == null || faceBeanList.size() <= 0) {
            return;
        }

        List<FaceBean> uploadFaceList = new ArrayList<>();
        for (FaceBean faceBean : faceBeanList) {
            if (!TextUtils.isEmpty(faceBean.getUserId())) {
                uploadFaceList.add(faceBean);
            }
        }
        mApiProxy.uploadFaceList(version, uploadFaceList);
    }

    private void remoteSyncMemberData() {
        Log.d(TAG, "remoteSyncMemberData");
        mHandler.removeMessages(MSG_REMOTE_SYNC_MEMBER);
        mHandler.sendEmptyMessageDelayed(MSG_REMOTE_SYNC_MEMBER, TIMEOUT_REMOTE_SYNC_MEMBER);
        mApiProxy.remoteSyncMemberData();
    }

    private void updateRemoteUpdateState(RemoteUpdateState newState) {
        Log.d(TAG, "updateRemoteUpdateState newState: " + newState
                + ", mRemoteUpdateState: " + mRemoteUpdateState);
        mRemoteUpdateState = newState;
    }

    private void updateRecoveryState(RecoveryState newState) {
        Log.d(TAG, "updateRecoveryState newState: " + newState
                + ", mRecoveryState: " + mRecoveryState);
        mRecoveryState = newState;
    }

    @Override
    public String getMemberList() {
        return mGson.toJson(mDataHelper.getMemberList());
    }

    @Override
    public String getWholeMemberList() {
        return mGson.toJson(mDataHelper.getWholeMemberList());
    }

    @Override
    public String getRecentMember() {
        return mGson.toJson(mDataHelper.getRecentMember());
    }

    private void notifyMemberUpdate() {
        synchronized (mAccountListenerList) {
            Log.d(TAG, "notifyMemberUpdate mAccountListenerList size: "
                    + mAccountListenerList.size());
            for (IAccountListener listener : mAccountListenerList) {
                try {
                    listener.memberUpdate();
                } catch (RemoteException e) {
                    e.printStackTrace();
                    mAccountListenerList.remove(listener);
                }
            }
        }
    }

    @Override
    public void registerAccountListener(IAccountListener listener) {
        Log.d(TAG, "registerAccountListener listener: " + listener.toString());
        synchronized (mAccountListenerList) {
            if (!mAccountListenerList.contains(listener)) {
                mAccountListenerList.add(listener);
            }
        }
    }

    @Override
    public void unregisterAccountListener(IAccountListener listener) {
        Log.d(TAG, "unregisterAccountListener listener: " + listener.toString());
        synchronized (mAccountListenerList) {
            mAccountListenerList.remove(listener);
        }
    }

    @Override
    public String getSystemToken() {
        String result = "";
        Cursor cursor = mCore.getApplicationContext().getContentResolver()
                .query(Uri.parse(LOGIN_URI),
                        null, null, null, null);
        if (null != cursor) {
            cursor.moveToFirst();
            result = cursor.getString(0);
            cursor.close();
        }
        return result;
    }

    @Override
    public void syncServerDataToLocale(IActionListener listener) {
    }

    @Override
    public void recoveryRobot(String userId, String userToken, IActionListener listener) {
        Log.d(TAG, "recoveryRobot userId: " + userId + ", userToken: " + userToken);
        mHandler.obtainMessage(MSG_RECOVERY,
                new RequestBean(userId, userToken, listener)).sendToTarget();
    }

    private void processResult(RequestBean bean, int result, String message) {
        if (bean.getListener() != null) {
            try {
                bean.getListener().onResult(result, message);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public boolean reRegisterWithUserIdById(int personId, String userId, IActionListener listener) {
        com.ainirobot.coreservice.core.person.bean.RequestBean bean
                = new com.ainirobot.coreservice.core.person.bean.RequestBean(personId,
                userId, null, listener);
        mCore.getPersonManager().getHandler().obtainMessage(MSG_RE_REGISTER, bean).sendToTarget();
        return true;
    }

    @Override
    public boolean reRegisterWithUserIdByPic(String picturePath, String userId, IActionListener listener) {
        com.ainirobot.coreservice.core.person.bean.RequestBean bean
                = new com.ainirobot.coreservice.core.person.bean.RequestBean(picturePath,
                userId, null, listener);
        mCore.getPersonManager().getHandler().obtainMessage(MSG_RE_REGISTER, bean).sendToTarget();
        return true;
    }

    @Override
    public String getRegisteredFaceList() {
        List<FaceBean> faceList = MemberDataHelper
                .getInstance(mCore).getFaceList();
        return mGson.toJson(faceList);
    }
}
