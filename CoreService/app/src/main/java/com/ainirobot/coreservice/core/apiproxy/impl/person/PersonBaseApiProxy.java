package com.ainirobot.coreservice.core.apiproxy.impl.person;

import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.IStatusListener;
import com.ainirobot.coreservice.action.ActionManager;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.actionbean.CommandBean;
import com.ainirobot.coreservice.client.person.FaceBean;
import com.ainirobot.coreservice.core.apiproxy.BaseApiProxy;
import com.ainirobot.coreservice.core.apiproxy.result.BaseApiResult;
import com.ainirobot.coreservice.core.status.RemoteSubscriber;
import com.ainirobot.coreservice.core.status.StatusManager;
import com.ainirobot.coreservice.service.CoreService;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;

import static com.ainirobot.coreservice.utils.TimeUtils.md5;

public abstract class PersonBaseApiProxy extends BaseApiProxy {

    private static final String TAG = PersonBaseApiProxy.class.getSimpleName();

    PersonBaseApiProxy(CoreService core, Handler handler) {
        super(core, handler);
    }

    public void checkVisionConnect() {
        Log.i(TAG, "checkVisionConnect");
        JSONObject param = new JSONObject();
        CommandBean bean = new CommandBean(Definition.CMD_HEAD_IS_HEADER_CONNECTED,
                param.toString(), false);
        ActionManager actionManager = mCore.getActionManager();
        actionManager.exCmd(Definition.ACTION_COMMON,
                mGson.toJson(bean), new ApiListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.i(TAG, "checkVisionConnect onResult result: "
                                + result + ", message: " + message);
                        Message msg = new Message();
                        msg.what = MSG_API_RESULT;
                        BaseApiResult apiResult = new BaseApiResult(BaseApiResult.Type
                                .CHECK_VISION_CONNECT);
                        if (result != Definition.RESULT_SUCCEED
                                || !Definition.CMD_STATUS_OK.equals(message)) {
                            apiResult.setResult(BaseApiResult.RESULT_FAILED);
                        } else {
                            apiResult.setResult(BaseApiResult.RESULT_SUCCESS);
                        }
                        if (mHandler != null) {
                            msg.obj = apiResult;
                            mHandler.sendMessage(msg);
                        }
                    }
                });
    }

    public void registerVisionConnectStateListener() {
        Log.i(TAG, "registerVisionConnectStateListener");
        StatusManager statusManager = mCore.getStatusManager();
        RemoteSubscriber subscriber = new RemoteSubscriber(new IStatusListener.Stub() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Log.i(TAG, "registerVisionConnectStateListener type: " + type
                        + ", data: " + data);
                Message message = new Message();
                message.what = MSG_API_RESULT;
                BaseApiResult result = new BaseApiResult(BaseApiResult.Type.VISION_STATE);
                if (Definition.STATUS_HW_CONNECTED.equalsIgnoreCase(data)) {
                    result.setResult(BaseApiResult.RESULT_SUCCESS);
                } else if (Definition.STATUS_HW_DISCONNECTED.equalsIgnoreCase(data)) {
                    result.setResult(BaseApiResult.RESULT_FAILED);
                }
                if (mHandler != null) {
                    message.obj = result;
                    mHandler.sendMessage(message);
                }
            }
        });
        statusManager.registerStatusListener(Definition.STATUS_HEAD, subscriber);
    }

    public void startGetAllPersonInfo() {
        Log.i(TAG, "startGetAllPersonInfo");
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_LOOPER, Definition.JSON_HEAD_CONTINUOUS);
            param.put(Definition.JSON_HEAD_IS_NEED_INVALID_PERSON,
                    Definition.JSON_HEAD_NEED_INVALID_PERSON);
            CommandBean bean = new CommandBean(Definition.CMD_HEAD_GET_ALL_PERSON_INFOS,
                    param.toString(), true);
            ActionManager actionManager = mCore.getActionManager();
            actionManager.exCmd(Definition.ACTION_HEAD_GET_ALL_PERSON_INFOS,
                    mGson.toJson(bean), new ApiListener() {
                        @Override
                        public void onResult(int result, String message) {
                            Log.i(TAG, "startGetAllPersonInfo onResult result"
                                    + result + ", message" + message);
                            if (result != Definition.RESULT_OK) {
                                startGetAllPersonInfo();
                            }
                        }

                        @Override
                        public void onError(int errorCode, String errorString) {
                            Log.i(TAG, "personListener onError:errorCode"
                                    + errorCode + ",errorString" + errorString);
                            if (errorCode == Definition.ERROR_LISTENER_INVALID) {
                                startGetAllPersonInfo();
                            }
                        }

                        @Override
                        public void onStatusUpdate(int status, String data) {
                            if (mHandler != null) {
                                Message message = new Message();
                                message.what = MSG_API_RESULT;
                                message.obj = new BaseApiResult(BaseApiResult
                                        .Type.PERSON_DATA, status, data);
                                mHandler.sendMessage(message);
                            }
                        }
                    });
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public void stopGetAllPersonInfo() {
        Log.i(TAG, "stopGetAllPersonInfo");
        ActionManager actionManager = mCore.getActionManager();
        actionManager.exStopCmd(Definition.ACTION_HEAD_GET_ALL_PERSON_INFOS, true);
    }

    public abstract void localFaceDataSync();

    public abstract void registerById(final int personId, final String name,
                                      final boolean isReEntry);

    public abstract void registerByPic(final String picturePath, final String name,
                                       final boolean isReEntry);

    public abstract void recognizeById(final int personId);

    public abstract void recognizeByPic(final String picturePath);

    public abstract void deleteFaceList(List<FaceBean> deleteFaceList);

    synchronized String saveLocalRegisterPic(String oldPicturePath, String faceId) {
        Log.d(TAG, "saveLocalRegisterPic oldPicturePath: " + oldPicturePath
                + ", faceId: " + faceId);
        if (TextUtils.isEmpty(oldPicturePath) || TextUtils.isEmpty(faceId)) {
            Log.e(TAG, "saveLocalRegisterPic oldPicturePath or faceId invalid");
            return null;
        }

        File file = new File(oldPicturePath);
        if (!file.exists() || !file.isFile()) {
            Log.e(TAG, "saveLocalRegisterPic oldPicturePath not exist");
            return null;
        }

        File picturePathDir = new File(PICTURE_PATH);
        if (!picturePathDir.exists() || !picturePathDir.isDirectory()) {
            boolean isCreateDirectorySuccess = picturePathDir.mkdirs();
            if (!isCreateDirectorySuccess) {
                Log.e(TAG, "saveLocalRegisterPic newPictureDirectory create failed");
                return null;
            }
        }

        String newPicturePath = PICTURE_PATH + File.separator + faceId;
        Log.d(TAG, "saveLocalRegisterPic newPicturePath: " + newPicturePath);
        File picturePathFile = new File(newPicturePath);
        if (picturePathDir.exists() && picturePathDir.isFile()) {
            boolean isDeleteSuccess = picturePathFile.delete();
            Log.d(TAG, "saveLocalRegisterPic isDeleteSuccess: " + isDeleteSuccess);
        }

        InputStream inputStream = null;
        OutputStream outputStream = null;
        try {
            int byteread;
            inputStream = new FileInputStream(file);
            outputStream = new FileOutputStream(newPicturePath);
            byte[] buffer = new byte[1024];
            while ((byteread = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, byteread);
            }
            return newPicturePath;
        } catch (IOException e) {
            e.printStackTrace();
            Log.e(TAG, "saveLocalRegisterPic copy exception");
            return null;
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    protected String generalFaceId() {
        return "face_" + md5(RobotSettings.getSystemSn() + System.currentTimeMillis());
    }
}
