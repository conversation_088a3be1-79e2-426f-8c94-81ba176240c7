package com.ainirobot.coreservice.client.input;

import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.IDumpRegistry;
import com.ainirobot.coreservice.IHWService;
import com.ainirobot.coreservice.client.BaseApi;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.IntentUtil;


public class DumpApi extends BaseApi {
    private static final String TAG = DumpApi.class.getSimpleName();

    private IDumpRegistry mDumpRegistry;

    public DumpApi(Context ctx) {
        super(ctx);
    }

    @Override
    public void connectApi() {
        Intent intent = IntentUtil.createExplicitIntent(Definition.CORE_SERVICE_NAME,
                IDumpRegistry.class.getName());
        intent.putExtra("packageName", ctx.getPackageName());
        ctx.bindService(intent, apiConnection, Service.BIND_AUTO_CREATE);
    }

    @Override
    public void disconnectApi() {
        try {
            ctx.unbindService(apiConnection);
        } catch (IllegalArgumentException e) {
            // Nothing to do
        }
    }

    protected ServiceConnection apiConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName className, IBinder service) {
            mDumpRegistry = IDumpRegistry.Stub.asInterface(service);
            mIsServiceConnected = true;
            notifyEventApiConnected();
        }

        @Override
        public void onServiceDisconnected(ComponentName className) {
            mIsServiceConnected = false;
            notifyEventApiDisconnected();
            mDumpRegistry = null;
        }
    };

    public void registerHWService(String serviceName, IHWService HWService) {
        try {
            mDumpRegistry.registerHWCallback(serviceName, HWService);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    private boolean isConnected() {
        return mDumpRegistry != null && mDumpRegistry.asBinder().pingBinder();
    }

    public void sendAsyncResponse(String cmdType, int result, String message) {
        try {
            mDumpRegistry.sendAsyncResponse(cmdType, result, message);
        } catch (RemoteException e) {
            Log.d(TAG, "Send async response failed : " + cmdType + "  " + message);
            e.printStackTrace();
        }
    }

    public void sendAysncStatus(String cmdType, String status) {
        try {
            mDumpRegistry.sendAsyncStatus(cmdType, status);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void sendStatusReport(String serviceName, String type, String params) {
        try {
            mDumpRegistry.sendStatusReport(serviceName, type, params);
        } catch (RemoteException e) {
            Log.d(TAG, "Send status report failed : " + type + "  " + params);
            e.printStackTrace();
        }
    }

    public void sendExceptionReport(String serviceName, String type, String params) {
        try {
            mDumpRegistry.sendExceptionReport(serviceName, type, params);
        } catch (RemoteException e) {
            Log.d(TAG, "Send exception report failed : " + type + "  " + params);
            e.printStackTrace();
        }
    }

}
