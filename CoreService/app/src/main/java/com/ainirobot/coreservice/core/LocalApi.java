package com.ainirobot.coreservice.core;

import android.util.Log;

import com.ainirobot.coreservice.action.AbstractAction;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.Definition.TrackMode;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.actionbean.DumpInfoBean;
import com.ainirobot.coreservice.client.actionbean.NaviCmdTimeOutBean;
import com.ainirobot.coreservice.client.actionbean.PictureInfo;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.config.ConfigManager;
import com.ainirobot.coreservice.resmanage.AssignedRes;
import com.google.gson.Gson;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class LocalApi {
    private final static String TAG = "LocalApi";

    public static final String NAME = "name";
    public static final String ID = "id";
    public static final String ABSOLUTE = "absolute";
    public static final String RELATIVE = "relative";
    public static final String MODE = "mode";
    public static final String HORIZONTAL_MODE = "hmode";
    public static final String VERTICAL_MODE = "vmode";
    public static final String HORIZONTAL = "horizontal";
    public static final String VERTICAL = "vertical";
    public static final String LOOPER = "looper";
    public static final String CONTINUOUS = "continuous";
    public static final String LEFT_ANGLE = "leftangle";
    public static final String RIGHT_ANGLE = "rightangle";
    public static final String TIMEOUT = "timeout";
    public static final String DETECT_BODY = "body";
    public static final String DETECT_FACE = "face";
    public static final String DETECT = "detect";
    public static final String TRACK = "track";

    private AbstractAction mResponseProcessor;
    private ConcurrentHashMap<String, Integer> mContinueCmds;
    private ConcurrentHashMap<Integer, String> mWaitingCmds;
    private AssignedRes mRes;
    private Gson mGson;

    private int preHeadAngle = 0;

    public LocalApi(AbstractAction responseProcessor, int vAngle) {
        mGson = new Gson();
        this.mResponseProcessor = responseProcessor;
        this.mContinueCmds = new ConcurrentHashMap<>();
        this.mWaitingCmds = new ConcurrentHashMap<>();
        preHeadAngle = vAngle;
    }

    public boolean prepare(AssignedRes res) {
        boolean result = false;
        if (res != null) {
            mRes = res;
            result = true;
        }
        return result;
    }

    public boolean stop() {
        Log.d(TAG, "Stop waiting command : " + mWaitingCmds.size());
        for (Map.Entry<Integer, String> entry : mWaitingCmds.entrySet()) {
            cancelCommand(entry.getKey(), entry.getValue());
        }
        mWaitingCmds.clear();
        return mRes == null || mRes.stop();
    }

    public void releaseRes() {
        if (mRes != null) {
            mRes.release();
            mRes = null;
        }
    }

    public int startVision(int reqId) {
        JSONObject param = new JSONObject();
        return sendCommand(reqId, Definition.CMD_HEAD_START_VISION,
                param.toString());
    }

    public int stopVision(int reqId) {
        JSONObject param = new JSONObject();
        return sendCommand(reqId, Definition.CMD_HEAD_STOP_VISION,
                param.toString());
    }

    public int goBackward(int reqId, float speed) {
        return goBackward(reqId, speed, Float.MAX_VALUE);
    }

    public int goBackward(int reqId, float speed, float distance) {
        return motionLine(reqId, Definition.CMD_NAVI_MOVE_SUB_BACKWARD, speed, distance);
    }

    public int goForward(int reqId, float speed) {
        return goForward(reqId, speed, Float.MAX_VALUE);
    }

    public int goForward(int reqId, float speed, float distance) {
        return motionLine(reqId, Definition.CMD_NAVI_MOVE_SUB_FORWARD, speed, distance);
    }

    public int goForwardAvoid(int reqId, float speed, float distance) {
        return motionLineAvoid(reqId, Definition.CMD_NAVI_MOVE_SUB_FORWARD, speed, distance, true);
    }

    public int turnLeft(int reqId, float speed) {
        return turnLeft(reqId, speed, Float.MAX_VALUE);
    }

    public int turnLeft(int reqId, float speed, float angle) {
        return motionAngle(reqId, Definition.CMD_NAVI_MOVE_SUB_TURN_LEFT, speed, angle);
    }

    /**
     * @param reqId
     * @param speed
     * @param angle
     * @param dir   false is right, true is left
     * @return
     */
    public int wakeUpTurn(int reqId, float speed, float angle, boolean dir) {
        float distance;
        float radianspeed = (float) Math.toRadians(speed);
        if (angle == Float.MAX_VALUE) {
            distance = Float.MAX_VALUE;
        } else {
            distance = (float) Math.toRadians(angle);
        }
        if (dir) {
            return motionLine(reqId, Definition.CMD_NAVI_MOVE_SUB_TURN_LEFT, radianspeed, distance, true);
        } else {
            return motionLine(reqId, Definition.CMD_NAVI_MOVE_SUB_TURN_RIGHT, radianspeed, distance, true);
        }
    }

    public int turnRight(int reqId, float speed) {
        return turnRight(reqId, speed, Float.MAX_VALUE);
    }

    public int turnRight(int reqId, float speed, float angle) {
        return motionAngle(reqId, Definition.CMD_NAVI_MOVE_SUB_TURN_RIGHT, speed, angle);
    }

    public int turnBack(int reqId, float speed) {
        return motionAngle(reqId, Definition.CMD_NAVI_MOVE_SUB_TURN_BACK, speed, 180f);
    }

    public int rotate(int reqId, float speed) {
        return motionAngle(reqId, Definition.CMD_NAVI_MOVE_SUB_ROTATE, speed, Float.MAX_VALUE);
    }

    public int setTopAssistedEstimate(int reqId, boolean enable) {
        try {
            JSONObject param = new JSONObject();
            param.put("enable", enable);
            return sendCommand(reqId, Definition.CMD_NAVI_SET_TOP_ASSISTED_ESTIMATE, param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    public int setNavigationSpeed(int reqId, float linespeed, float anglespeed) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_NAVIGATION_LINEAR_SPEED, linespeed);
            param.put(Definition.JSON_NAVI_NAVIGATION_ANGULAR_SPEED, anglespeed);
            return sendCommand(reqId, Definition.CMD_NAVI_SET_NAVIGATION_SPEED,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    public int changeNavigationSpeed(int reqId, float linea, float linespeed,
                                     float anglea, float anglespeed) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_NAVIGATION_ACCLINEAR_SPEED, linea);
            param.put(Definition.JSON_NAVI_NAVIGATION_LINEAR_SPEED, linespeed);
            param.put(Definition.JSON_NAVI_NAVIGATION_ACCANGULAR_SPEED, anglea);
            param.put(Definition.JSON_NAVI_NAVIGATION_ANGULAR_SPEED, anglespeed);
            return sendCommand(reqId, Definition.CMD_NAVI_CHANGE_NAVIGATION_SPEED,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    public int startVelocityReport(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_START_VELOCITY_REPORT, null);
    }

    public int stopVelocityReport(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_STOP_VELOCITY_REPORT, null);
    }

    // xAcc, yAcc,线加速度，zAcc角加速度，zAcc必须是1.7以上
    public int setMaxAcceleration(int reqId, float xAcc, float yAcc, float zAcc) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_X_ACCELERATE, xAcc);
            param.put(Definition.JSON_NAVI_Y_ACCELERATE, yAcc);
            param.put(Definition.JSON_NAVI_Z_ACCELERATE, zAcc);
            return sendCommand(reqId, Definition.CMD_NAVI_SET_MAX_ACCELERATION,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int motionAngle(int reqId, String direction, float speed, float angle) {
        float distance;
        float radianspeed = (float) Math.toRadians(speed);
        if (angle == Float.MAX_VALUE) {
            distance = Float.MAX_VALUE;
        } else {
            distance = (float) Math.toRadians(angle);
        }
        return motionLine(reqId, direction, radianspeed, distance);
    }

    protected int motionLineAvoid(int reqId, String direction, float speed, float distance, boolean avoid) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_DERICTION, direction);
            param.put(Definition.JSON_NAVI_DISTANCE, distance);
            param.put(Definition.JSON_NAVI_LINEAR_SPEED, speed);
            param.put(Definition.JSON_NAVI_FORWARD_AVOID, avoid);
            return sendCommand(reqId, Definition.CMD_NAVI_MOVE_DIRECTION,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }


    protected int motionLine(int reqId, String direction, float speed, float distance) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_DERICTION, direction);
            param.put(Definition.JSON_NAVI_DISTANCE, distance);
            param.put(Definition.JSON_NAVI_LINEAR_SPEED, speed);
            return sendCommand(reqId, Definition.CMD_NAVI_MOVE_DIRECTION,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    protected int motionLine(int reqId, String direction, float speed, float distance, boolean wakeUpAction) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_DERICTION, direction);
            param.put(Definition.JSON_NAVI_DISTANCE, distance);
            param.put(Definition.JSON_NAVI_LINEAR_SPEED, speed);
            param.put(Definition.JSON_NAVI_WAKEUP_ACTION, wakeUpAction);
            return sendCommand(reqId, Definition.CMD_NAVI_MOVE_DIRECTION,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }


    public int motionArc(int reqId, float lineSpeed, float angularSpeed) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_LINEAR_SPEED, lineSpeed);
            param.put(Definition.JSON_NAVI_ANGULAR_SPEED, angularSpeed);
            return sendCommand(reqId, Definition.CMD_NAVI_MOVE_DIRECTION_ANGLE,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    public int motionArc(int reqId, float distance, float angle, float angularSpeed) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_DISTANCE, distance);
            param.put(Definition.JSON_NAVI_ANGLE, angle);
            param.put(Definition.JSON_NAVI_LINEAR_SPEED, angularSpeed);
            return sendCommand(reqId, Definition.CMD_NAVI_MOVE_DISTANCE_ANGLE,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    public int motionArc(int reqId, float distance, float angle, float angularSpeed,
                         float latency) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_DISTANCE, distance);
            param.put(Definition.JSON_NAVI_ANGLE, angle);
            param.put(Definition.JSON_NAVI_ANGULAR_SPEED, angularSpeed);
            param.put(Definition.JSON_NAVI_LATENCY, latency);
            return sendCommandNoResponse(reqId, Definition.CMD_NAVI_MOVE_DISTANCE_ANGLE,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    public int motionArc(int reqId, float distance, float angle, float headSpeed,
                         float latency, boolean bodyFollow) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_DISTANCE, distance);
            param.put(Definition.JSON_NAVI_ANGLE, angle);
            param.put(Definition.JSON_NAVI_ANGULAR_SPEED, headSpeed);
            param.put(Definition.JSON_NAVI_LATENCY, latency);
            param.put(Definition.JSON_NAVI_BODY_FOLLOW, bodyFollow);
            return sendCommandNoResponse(reqId, Definition.CMD_NAVI_MOVE_DISTANCE_ANGLE,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    public int motionPid(int reqId, float angle,float latency) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_ANGLE, angle);
            param.put(Definition.JSON_NAVI_LATENCY, latency);
            return sendCommandNoResponse(reqId, Definition.CMD_NAVI_MOVE_ANGLE_PID,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    public int motionArcWithObstacles(int reqId, float personDist, float angle, float headSpeed,
                                      float latency, boolean bodyFollow, double minObstacleDist,
                                      double linearSpeed, double angularSpeed) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_DISTANCE, personDist);
            param.put(Definition.JSON_NAVI_ANGLE, angle);
            param.put(Definition.JSON_NAVI_LATENCY, latency);
            param.put(Definition.JSON_NAVI_ANGULAR_SPEED, headSpeed);
            param.put(Definition.JSON_NAVI_BODY_FOLLOW, bodyFollow);
            param.put(Definition.JSON_NAVI_MIN_OBSTACLES_DISTANCE, minObstacleDist);
            param.put(Definition.JSON_NAVI_MAX_LINEAR_SPEED, linearSpeed);
            param.put(Definition.JSON_NAVI_MAX_ANGULAR_SPEED, angularSpeed);
            return sendCommandNoResponse(reqId, Definition.CMD_NAVI_MOVE_DISTANCE_ANGLE_WITH_OBSTACLES,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    public int stopMove(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_STOP_MOVE, null);
    }

    public int stopNavigation(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_STOP_NAVIGATION, null);
    }

    public int goPlace(int reqId, String destinationName) {

        return goPlace(reqId, destinationName, SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED, SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED);
    }

    public int goPlace(int reqId, String destinationName, double lineSpeed, double angularSpeed) {

        return goPlace(reqId, destinationName, lineSpeed, angularSpeed, false);
    }

    public int goPlace(int reqId, String destinationName, double lineSpeed, double angularSpeed, boolean isAdjustAngle) {
        return goPlace(reqId, destinationName, lineSpeed, angularSpeed, isAdjustAngle, 0.0D);
    }

    public int goPlace(int reqId, String destinationName, double lineSpeed, double angularSpeed, boolean isAdjustAngle, double destinationRange) {
        return goPlace(reqId, destinationName, lineSpeed, angularSpeed, isAdjustAngle, 0.0D, 0);
    }

    public int goPlace(int reqId, String destinationName, double lineSpeed, double angularSpeed, boolean isAdjustAngle,
                       double destinationRange, int priority) {
        return goPlace(reqId, destinationName, lineSpeed, angularSpeed, isAdjustAngle, destinationRange, priority, 0, 0, 0, 0, 0);
    }

    /**
     * @param isAdjustAngle 是否适应导航结束时朝向的角度，默认 false:即归正目标点设定角度
     * @param priority  任务优先级，目前仅针对招财豹使用
     * @return
     */
    public int goPlace(int reqId, String destinationName, double lineSpeed, double angularSpeed, boolean isAdjustAngle,
                       double destinationRange, int priority, double linearAcc, double angularAcc, int startModeLevel, int brakeModeLevel, double obsDistance) {
        return goPlace(reqId, destinationName, lineSpeed, angularSpeed, isAdjustAngle, destinationRange, priority,
                linearAcc, angularAcc, startModeLevel, brakeModeLevel, obsDistance, 0, 0, false);
    }

    public int goPlace(int reqId, String destinationName, double lineSpeed, double angularSpeed, boolean isAdjustAngle,
                       double destinationRange, int priority, double linearAcc, double angularAcc, int startModeLevel, int brakeModeLevel,
                       double obsDistance, double posTolerance, double angleTolerance, boolean isReversePoseTheta) {
        JSONObject param = new JSONObject();
        try {
            param.put(Definition.JSON_NAVI_DESTINATION_NAME, destinationName);
            param.put(Definition.JSON_NAVI_LINEAR_SPEED, lineSpeed);
            param.put(Definition.JSON_NAVI_ANGULAR_SPEED, angularSpeed);
            param.put(Definition.JSON_NAVI_ISADJUST_ANGLE, isAdjustAngle);
            param.put(Definition.JSON_NAVI_DESTINATION_RANGE, destinationRange);
            param.put(Definition.JSON_NAVI_TASK_PRIORITY, priority);
            param.put(Definition.JSON_NAVI_LINEAR_ACCELERATION, linearAcc);
            param.put(Definition.JSON_NAVI_ANGULAR_ACCELERATION, angularAcc);
            param.put(Definition.JSON_NAVI_START_MODE_LEVEL, startModeLevel);
            param.put(Definition.JSON_NAVI_BRAKE_MODE_LEVEL, brakeModeLevel);
            param.put(Definition.JSON_NAVI_OBSTACLE_DISTANCE, obsDistance);
            param.put(Definition.JSON_NAVI_POS_TOLERANCE, posTolerance);
            param.put(Definition.JSON_NAVI_ANGLE_TOLERANCE, angleTolerance);
            param.put(Definition.JSON_NAVI_IS_REVERSE_POSE_THETA, isReversePoseTheta);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        String type = Definition.CMD_NAVI_GO_LOCATION;
        if (mContinueCmds.containsKey(type)) {
            int cmdId = mContinueCmds.get(type);
            cancelCommand(cmdId, type, false);
        }

        int cmdId = sendCommand(reqId, Definition.CMD_NAVI_GO_LOCATION, param.toString());
        mContinueCmds.put(type, cmdId);
        return cmdId;

    }

    public int setLocation(int reqId, String param) {
        return sendCommand(reqId, Definition.CMD_NAVI_SET_LOCATION, param);
    }

    public int setPointLocation(int reqId, String param) {
        return sendCommand(reqId, Definition.CMD_NAVI_ADD_MAPPING_POSE, param);
    }

    public int removePointLocation(int reqId, String param) {
        return sendCommand(reqId, Definition.CMD_NAVI_DELETE_MAPPING_POSE, param);
    }

    public int setPoseLocation(int reqId, String param) {
        return sendCommand(reqId, Definition.CMD_NAVI_SET_POSE_LOCATION, param);
    }

    public int setPoseEstimate(int reqId, String param) {
        return sendCommand(reqId, Definition.CMD_NAVI_SET_POSE_ESTIMATE, param);
    }

    public int setFixedEstimate(int reqId, String param) {
        return sendCommand(reqId, Definition.CMD_NAVI_SET_FIXED_ESTIMATE, param);
    }

    public int setForceEstimate(int reqId, String param) {
        return sendCommand(reqId, Definition.CMD_NAVI_SET_FORCE_ESTIMATE, param);
    }

    public int startCreatingMap(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_START_CREATING_MAP, null);
    }

    public int stopCreatingMap(int reqId, String param) {
        return sendCommand(reqId, Definition.CMD_NAVI_STOP_CREATING_MAP, param);
    }

    public int switchMap(int reqId, String param) {
        return sendCommand(reqId, Definition.CMD_NAVI_SWITCH_MAP, param);
    }

    public int getPlaceName(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_GET_PLACE_NAME, null);
    }

    public int getPlaceList(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_GET_PLACE_LIST, null);
    }

    public int goPosition(int reqId, double x, double y, double theta) {
        return goPosition(reqId, x, y, theta, SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED, SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED);
    }

    public int goPosition(int reqId, double x, double y, double theta, double linearSpeed, double angularSpeed) {
        return goPosition(reqId, x, y, theta, linearSpeed, angularSpeed, false);
    }

    public int goPosition(int reqId, double x, double y, double theta, double linearSpeed, double angularSpeed
            , boolean isAdjustAngle) {
        return goPosition(reqId, x, y, theta, linearSpeed, angularSpeed, isAdjustAngle, false);
    }

    public int goPosition(int reqId, double x, double y, double theta, double linearSpeed, double angularSpeed
            , boolean isAdjustAngle, boolean keepMove) {
        return goPosition(reqId, x, y, theta, linearSpeed, angularSpeed, isAdjustAngle, keepMove, 0.0D);
    }

    public int goPosition(int reqId, double x, double y, double theta, double linearSpeed, double angularSpeed
            , boolean isAdjustAngle, boolean keepMove, double destinationRange) {
        return goPosition(reqId, x, y, theta, linearSpeed, angularSpeed, isAdjustAngle, keepMove, destinationRange, 0);
    }

    public int goPosition(int reqId, double x, double y, double theta, double linearSpeed, double angularSpeed
            , boolean isAdjustAngle, boolean keepMove, double destinationRange, int priority) {
        return goPosition(reqId, x, y, theta, linearSpeed, angularSpeed, isAdjustAngle, keepMove, destinationRange, priority,
                0, 0, 0, 0, 0.0D, 0.0D, 0.0D);
    }

    public int goPosition(int reqId, double x, double y, double theta, double linearSpeed, double angularSpeed
            , boolean isAdjustAngle, boolean keepMove, double destinationRange, int priority, double linearAc
            , double angularAcc, int startModeLevel, int brakeModeLevel, double obsDistance, double posTolerance, double angleTolerance) {
        return goPosition(reqId, new Pose((float) x, (float) y, (float) theta), linearSpeed, angularSpeed, isAdjustAngle, keepMove, destinationRange, priority,
                linearAc, angularAcc, startModeLevel, brakeModeLevel, obsDistance, posTolerance, angleTolerance, 1);
    }

    public int goPosition(int reqId, Pose pose, double linearSpeed, double angularSpeed
            , boolean isAdjustAngle, boolean keepMove, double destinationRange, int priority, double linearAc
            , double angularAcc, int startModeLevel, int brakeModeLevel, double obsDistance
            , double posTolerance, double angleTolerance, int roadMode) {
        try {
            JSONObject json = new JSONObject();
            json.put(Definition.JSON_NAVI_DIRECTION_X, pose.getX());
            json.put(Definition.JSON_NAVI_DIRECTION_Y, pose.getY());
            json.put(Definition.JSON_NAVI_POSITION_THETA, pose.getTheta());
            json.put(Definition.NEWPLACENAME, pose.getName());
            json.put(Definition.NEWPLACE_IGNORE_DISTANCE, pose.getIgnoreDistance());
            json.put(Definition.NEWPLACE_SAFE_DISTANCE, pose.getSafeDistance());
            json.put(Definition.NEWPLACE_TYPE_ID, pose.getTypeId());
            json.put(Definition.NEWPLACE_PRIORITY, pose.getPriority());
            json.put(Definition.NO_DIRECTIONAL_PARKING, pose.getNoDirectionalParking());

            json.put(Definition.JSON_NAVI_LINEAR_SPEED, linearSpeed);
            json.put(Definition.JSON_NAVI_ANGULAR_SPEED, angularSpeed);
            json.put(Definition.JSON_NAVI_ISADJUST_ANGLE, isAdjustAngle);
            json.put(Definition.JSON_NAVI_IS_KEEP_MOVE, keepMove);
            json.put(Definition.JSON_NAVI_DESTINATION_RANGE, destinationRange);
            json.put(Definition.JSON_NAVI_TASK_PRIORITY, priority);
            json.put(Definition.JSON_NAVI_LINEAR_ACCELERATION, linearAc);
            json.put(Definition.JSON_NAVI_ANGULAR_ACCELERATION, angularAcc);
            json.put(Definition.JSON_NAVI_START_MODE_LEVEL, startModeLevel);
            json.put(Definition.JSON_NAVI_BRAKE_MODE_LEVEL, brakeModeLevel);
            json.put(Definition.JSON_NAVI_OBSTACLE_DISTANCE, obsDistance);
            json.put(Definition.JSON_NAVI_POS_TOLERANCE, posTolerance);
            json.put(Definition.JSON_NAVI_ANGLE_TOLERANCE, angleTolerance);
            json.put(Definition.JSON_NAVI_ROAD_MODE, roadMode);
            String type = Definition.CMD_NAVI_GO_POSITION;
            if (mContinueCmds.containsKey(type)) {
                int cmdId = mContinueCmds.get(type);
                cancelCommand(cmdId, type, false);
            }

            int cmdId = sendCommand(reqId, Definition.CMD_NAVI_GO_POSITION, json.toString());
            mContinueCmds.put(type, cmdId);
            return cmdId;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    public int goPositionByType(int reqId, int typeId, int priority, boolean isReversePoseTheta) {
        return  goPositionByType(reqId, typeId, priority, SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED,
                SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED, false, false,
                0.0D, 0, 0, 0, 0, 0, 0.0D, isReversePoseTheta);
    }

    public int goPositionByType(int reqId, int typeId, int priority) {
        return  goPositionByType(reqId, typeId, priority, SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED, SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED);
    }

    public int goPositionByType(int reqId, int typeId, int priority, double linearSpeed, double angularSpeed) {
        return goPositionByType(reqId, typeId, priority, linearSpeed, angularSpeed, false);
    }

    public int goPositionByType(int reqId, int typeId, int priority, double linearSpeed, double angularSpeed, boolean isAdjustAngle) {
        return goPositionByType(reqId, typeId, priority, linearSpeed, angularSpeed, isAdjustAngle, false);
    }

    public int goPositionByType(int reqId, int typeId, int priority, double linearSpeed, double angularSpeed
            , boolean isAdjustAngle, boolean keepMove) {
        return goPositionByType(reqId, typeId, priority, linearSpeed, angularSpeed, isAdjustAngle, keepMove, 0.0D);
    }

    public int goPositionByType(int reqId, int typeId, int priority, double linearSpeed, double angularSpeed
            , boolean isAdjustAngle, boolean keepMove, double destinationRange) {
        return goPositionByType(reqId, typeId, priority, linearSpeed, angularSpeed, isAdjustAngle, keepMove, destinationRange, 0);
    }

    public int goPositionByType(int reqId, int typeId, int priority, double linearSpeed, double angularSpeed
            , boolean isAdjustAngle, boolean keepMove, double destinationRange, int taskPriority) {
        return goPositionByType(reqId, typeId, priority, linearSpeed, angularSpeed, isAdjustAngle, keepMove, destinationRange, taskPriority,
                0, 0, 0, 0, 0.0D, false);
    }

    public int goPositionByType(int reqId, int typeId, int priority, double linearSpeed, double angularSpeed
            , boolean isAdjustAngle, boolean keepMove, double destinationRange, int taskPriority, double linearAc,
                          double angularAcc, int startModeLevel, int brakeModeLevel, double obsDistance, boolean isReversePoseTheta) {
        try {
            JSONObject json = new JSONObject();
            json.put(Definition.JSON_NAVI_TYPE_ID, typeId);
            json.put(Definition.JSON_NAVI_PRIORITY, priority);
            json.put(Definition.JSON_NAVI_LINEAR_SPEED, linearSpeed);
            json.put(Definition.JSON_NAVI_ANGULAR_SPEED, angularSpeed);
            json.put(Definition.JSON_NAVI_ISADJUST_ANGLE, isAdjustAngle);
            json.put(Definition.JSON_NAVI_IS_KEEP_MOVE, keepMove);
            json.put(Definition.JSON_NAVI_DESTINATION_RANGE, destinationRange);
            json.put(Definition.JSON_NAVI_TASK_PRIORITY, taskPriority);
            json.put(Definition.JSON_NAVI_LINEAR_ACCELERATION, linearAc);
            json.put(Definition.JSON_NAVI_ANGULAR_ACCELERATION, angularAcc);
            json.put(Definition.JSON_NAVI_START_MODE_LEVEL, startModeLevel);
            json.put(Definition.JSON_NAVI_BRAKE_MODE_LEVEL, brakeModeLevel);
            json.put(Definition.JSON_NAVI_OBSTACLE_DISTANCE, obsDistance);
            json.put(Definition.JSON_NAVI_IS_REVERSE_POSE_THETA, isReversePoseTheta);
            String type = Definition.CMD_NAVI_GO_POSITION;
            if (mContinueCmds.containsKey(type)) {
                int cmdId = mContinueCmds.get(type);
                cancelCommand(cmdId, type, false);
            }

            int cmdId = sendCommand(reqId, Definition.CMD_NAVI_GO_POSITION_BY_TYPE, json.toString());
            mContinueCmds.put(type, cmdId);
            return cmdId;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    public int getLocation(int reqId, String param) {
        return sendCommand(reqId, Definition.CMD_NAVI_GET_LOCATION, param);
    }

    public int getLocation(int reqId, int typeId) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_TYPE_ID, typeId);
            param.put(Definition.JSON_NAVI_PRIORITY, Definition.SPECIAL_PLACE_HIGH_PRIORITY);
            return sendCommand(reqId, Definition.CMD_NAVI_GET_LOCATION,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int getPosition(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_GET_POSITION, null);
    }

    public int getPositionWithoutEstimate(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_GET_POSITION_WITHOUT_ESTIMATE, null);
    }

    public int getNavigationLineSpeed(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_GET_NAVIGATION_LINE_SPEED, null);
    }

    public int getNavigationAngleSpeed(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_GET_NAVIGATION_ANGLE_SPEED, null);
    }

    public int isRobotInlocations(int reqId, String param) {
        return sendCommand(reqId, Definition.CMD_NAVI_IS_IN_LOCATION, param);
    }

    public int isRobotInLocations(int reqId, int typeId, int priority, double coordinateDeviation) {
        JSONObject obj = new JSONObject();
        try {
            obj.put(Definition.JSON_NAVI_TYPE_ID, typeId);
            obj.put(Definition.JSON_NAVI_PRIORITY, priority);
            obj.put(Definition.JSON_NAVI_COORDINATE_DEVIATION, coordinateDeviation);
            return isRobotInlocations(reqId, obj.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int resumeSpecialPlaceTheta(int reqId, String placeName) {
        return sendCommand(reqId, Definition.CMD_NAVI_RESUME_SPECIAL_PLACE_THETA, placeName);
    }

    public int isRobotEstimate(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_IS_ESTIMATE, null);
    }

    public int saveRobotEstimate(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_SAVE_ESTIMATE, null);
    }

    public int goDefaultTheta(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_GO_DEFAULT_THETA, null);
    }

    public int isInNavigation(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_IS_IN_NAVIGATION, null);
    }

    //------------------------------ head apis --------------------------------------//
    public int register(int reqId, String name, int id) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_NAME, name);
            param.put(Definition.JSON_HEAD_ID, id);
            return sendCommand(reqId, Definition.CMD_HEAD_REGISTER,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int unregister(int reqId, String name, int id) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_NAME, name);
            param.put(Definition.JSON_HEAD_ID, id);
            return sendCommand(reqId, Definition.CMD_HEAD_UNREGISTER,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int switchCamera(int reqId, String mode) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_LOCATION, mode);
            return sendCommand(reqId, Definition.CMD_HEAD_SWITCH_CAMERA,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int moveHead(int reqId, String hmode, String vmode, int hangle, int vangle) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_HMODE, hmode);
            param.put(Definition.JSON_HEAD_VMODE, vmode);
            param.put(Definition.JSON_HEAD_HORIZONTAL, hangle);
            param.put(Definition.JSON_HEAD_VERTICAL, vangle);
            param.put(Definition.JSON_HEAD_HSPEED, 30);
            return sendCommand(reqId, Definition.CMD_HEAD_MOVE_HEAD,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int moveHead(int reqId, String hmode, String vmode,
                        int hangle, int vangle, int hMaxSpeed, int vMaxSpeed) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_HSPEED, hMaxSpeed);
            param.put(Definition.JSON_HEAD_VSPEED, vMaxSpeed);
            param.put(Definition.JSON_HEAD_HMODE, hmode);
            param.put(Definition.JSON_HEAD_VMODE, vmode);
            param.put(Definition.JSON_HEAD_HORIZONTAL, hangle);
            param.put(Definition.JSON_HEAD_VERTICAL, vangle);
            return sendCommand(reqId, Definition.CMD_HEAD_MOVE_HEAD,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int getHeadStatus(int reqId) {
        return sendCommand(reqId, Definition.CMD_HEAD_GET_STATUS, null);
    }

    public int setTrackTarget(int reqId, String name, int id) {
        return -1;
    }

    public int setTrackTarget(int reqId, String name, int id, TrackMode mode) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_NAME, name);
            param.put(Definition.JSON_HEAD_ID, id);
            param.put(Definition.JSON_HEAD_MODE, mode.getValue());
            param.put(Definition.JSON_HEAD_CONTROL, Definition.JSON_HEAD_START);
            return sendCommand(reqId, Definition.CMD_HEAD_SET_TRACK_TARGET,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int setFaceTrackTarget(int reqId, int id, boolean isDetectOtherFace) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_NAME, "");
            param.put(Definition.JSON_HEAD_ID, id);
            param.put(Definition.JSON_HEAD_MODE, TrackMode.FACE.getValue());
            param.put(Definition.JSON_HEAD_CONTROL, Definition.JSON_HEAD_START);
            param.put("isDetectOtherFace", isDetectOtherFace);
            return sendCommand(reqId, Definition.CMD_HEAD_SET_TRACK_TARGET,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int stopTrack(int reqId) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_CONTROL, Definition.JSON_HEAD_STOP);
            param.put(Definition.JSON_HEAD_NAME, "");
            param.put(Definition.JSON_HEAD_ID, -1);
            param.put(Definition.JSON_HEAD_MODE, 3);
            return sendCommand(reqId, Definition.CMD_HEAD_STOP_TRACK_TARGET, param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int getAllPersonInfos(int reqId, String mode) {
        try {
            String type = Definition.CMD_HEAD_GET_ALL_PERSON_INFOS;
            if (mContinueCmds.containsKey(type)) {
                int cmdId = mContinueCmds.get(type);
                cancelCommand(cmdId, Definition.CMD_HEAD_GET_ALL_PERSON_INFOS);
            }

            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_LOOPER, mode);
            int cmdId;
            if (Definition.JSON_HEAD_CONTINUOUS.equals(mode)) {
                cmdId = sendCommand(reqId, type, param.toString(), true);
                mContinueCmds.put(type, cmdId);
            } else {
                cmdId = sendCommand(reqId, type, param.toString(), false);
            }
            return cmdId;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public boolean stopSendPersonInfos() {
        if (!mContinueCmds.containsKey(Definition.CMD_HEAD_GET_ALL_PERSON_INFOS)) {
            return true;
        }
        int cmdId = mContinueCmds.get(Definition.CMD_HEAD_GET_ALL_PERSON_INFOS);
        return cancelCommand(cmdId, Definition.CMD_HEAD_GET_ALL_PERSON_INFOS);
    }


    public int getAllPersonInfosOnTrack(int reqId, String mode) {
        try {
            String type = Definition.CMD_HEAD_GET_ALL_PERSON_INFOS_ON_TRACK;
            if (mContinueCmds.containsKey(type)) {
                int cmdId = mContinueCmds.get(type);
                cancelCommand(cmdId, Definition.CMD_HEAD_GET_ALL_PERSON_INFOS_ON_TRACK);
            }

            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_LOOPER, mode);
            int cmdId;
            if (Definition.JSON_HEAD_CONTINUOUS.equals(mode)) {
                cmdId = sendCommand(reqId, type, param.toString(), true);
                mContinueCmds.put(type, cmdId);
            } else {
                cmdId = sendCommand(reqId, type, param.toString(), false);
            }
            return cmdId;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public boolean stopSendPersonInfosOnTrack() {
        if (!mContinueCmds.containsKey(Definition.CMD_HEAD_GET_ALL_PERSON_INFOS_ON_TRACK)) {
            return true;
        }
        int cmdId = mContinueCmds.get(Definition.CMD_HEAD_GET_ALL_PERSON_INFOS_ON_TRACK);
        return cancelCommand(cmdId, Definition.CMD_HEAD_GET_ALL_PERSON_INFOS_ON_TRACK);
    }

    public int getPersonInfoByName(int reqId, String name, String mode) {
        try {
            String type = Definition.CMD_HEAD_GET_PERSON_INFO_BY_NAME;
            if (mContinueCmds.containsKey(type)) {
                int cmdId = mContinueCmds.get(type);
                cancelCommand(cmdId, Definition.CMD_HEAD_GET_PERSON_INFO_BY_NAME);
            }

            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_NAME, name);
            param.put(Definition.JSON_HEAD_LOOPER, mode);

            int cmdId;
            if (Definition.JSON_HEAD_CONTINUOUS.equals(mode)) {
                cmdId = sendCommand(reqId, type, param.toString(), true);
                mContinueCmds.put(type, cmdId);
            } else {
                cmdId = sendCommand(reqId, type, param.toString(), false);
            }
            return cmdId;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public boolean stopSendPersonInfosByName() {
        if (!mContinueCmds.containsKey(Definition.CMD_HEAD_GET_PERSON_INFO_BY_NAME)) {
            return true;
        }
        int cmdId = mContinueCmds.get(Definition.CMD_HEAD_GET_PERSON_INFO_BY_NAME);
        return cancelCommand(cmdId, Definition.CMD_HEAD_GET_PERSON_INFO_BY_NAME);
    }

    public int searchPersonInfoByName(int reqId, String control, String name, int leftAngle,
                                      int rightAngle, int timeout) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_CONTROL, control);
            param.put(Definition.JSON_HEAD_NAME, name);
            param.put(Definition.JSON_HEAD_LEFT_ANGLE, leftAngle);
            param.put(Definition.JSON_HEAD_RIGHT_ANGLE, rightAngle);
            param.put(Definition.JSON_HEAD_TIMEOUT, timeout);
            return sendCommand(reqId, Definition.CMD_HEAD_SEARCH_PERSON_BY_NAME,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int searchPersonByName(int reqId, String control, String name, int langle, int rangle) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_CONTROL, control);
            param.put(Definition.JSON_HEAD_NAME, name);
            param.put(Definition.JSON_HEAD_LEFT_ANGLE, langle);
            param.put(Definition.JSON_HEAD_RIGHT_ANGLE, rangle);
            return sendCommand(reqId, Definition.CMD_HEAD_SEARCH_PERSON_BY_NAME,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int searchPersonByName(int reqId, String control, String name, int timeout) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_CONTROL, control);
            param.put(Definition.JSON_HEAD_NAME, name);
            param.put(Definition.JSON_HEAD_TIMEOUT, timeout);
            return sendCommand(reqId, Definition.CMD_HEAD_SEARCH_PERSON_BY_NAME,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int getLastPosition(int reqId, String name, int id) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_NAME, name);
            param.put(Definition.JSON_HEAD_ID, id);
            return sendCommand(reqId, Definition.CMD_HEAD_GET_LAST_POSITION,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int isHeaderConnected(int reqId) {
        return sendCommand(reqId, Definition.CMD_HEAD_IS_HEADER_CONNECTED, null);
    }

    public int getPictureById(int reqId, int id, int count) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_ID, id);
            param.put(Definition.JSON_HEAD_COUNT, count);
            return sendCommand(reqId, Definition.CMD_HEAD_GET_PICTURE_BY_ID,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int getDepthCameraStatus(int reqId, int id, int count) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_ID, id);
            param.put(Definition.JSON_HEAD_COUNT, count);
            return sendCommand(reqId, Definition.CMD_HEAD_GET_DEPTH_STATUS,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int getFovCameraStatus(int reqId, int id, int count) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_ID, id);
            param.put(Definition.JSON_HEAD_COUNT, count);
            return sendCommand(reqId, Definition.CMD_HEAD_GET_FOV_STATUS,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * Get gesture info
     *
     * @param reqId request action id
     * @param mode  continuous/oneshot
     * @return
     */
    public int getGestureInfos(int reqId, String mode) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_LOOPER, mode);
            return sendCommand(reqId, Definition.CMD_HEAD_GET_GESTURE_INFOS,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int stopGestureInfos(int reqId) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_LOOPER, Definition.JSON_HEAD_STOP);
            return sendCommand(reqId, Definition.CMD_HEAD_STOP_GESTURE_INFOS,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int getMovement(int reqId, String type, long timer, int range) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_TYPE, type);
            param.put(Definition.JSON_TIMER, timer);
            param.put(Definition.JSON_RANGE, range);
            return sendCommand(reqId, Definition.CMD_HEAD_GET_MOVEMENT,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int resetHead(int reqId) {
        // meissa  horizontal - 90    max 95 min 30
        // mini    horizontal - 90    max 115 min 42
        try {
            JSONObject param = new JSONObject();
            param.put(HORIZONTAL_MODE, ABSOLUTE);
            param.put(VERTICAL_MODE, ABSOLUTE);
            param.put(HORIZONTAL, ConfigManager.getHeadConfig().getDefaultHorizontalAngle());
            param.put(VERTICAL, preHeadAngle);
            param.put(Definition.JSON_HEAD_HSPEED, ConfigManager.getHeadConfig().getDefaultSpeed());

            Log.d(TAG, "resetHead preHeadAngle: " + preHeadAngle);
            return sendCommand(reqId, Definition.CMD_HEAD_MOVE_HEAD, param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int switchDetectAlgorithm(int reqId, String algorithm) {
        try {
            JSONObject params = new JSONObject();
            params.put("mode", DETECT);
            params.put("algorithm", algorithm);
            return sendCommand(reqId,
                    Definition.CMD_SWITCH_DETECT_ALGORITHM, params.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int remoteRegister(int reqId, PictureInfo info) {
        return sendCommand(reqId,
                Definition.CMD_REMOTE_REGISTER, new Gson().toJson(info));
    }

    public int remoteDetect(int reqId, PictureInfo info) {
        return sendCommand(reqId,
                Definition.CMD_REMOTE_DETECT, new Gson().toJson(info));
    }

    public int remoteDetectFace(int reqId, String picturePath) {
        try {
            JSONObject params = new JSONObject();
            params.put(Definition.JSON_IMAGE, picturePath);
            return sendCommand(reqId,
                    Definition.CMD_REMOTE_DETECT_FACE, params.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int addFace(int reqId, String picturePath, String name, String faceId) {
        try {
            JSONObject params = new JSONObject();
            params.put(Definition.JSON_IMAGE, picturePath);
            params.put(Definition.JSON_REMOTE_USER_NAME, name);
            params.put(Definition.JSON_FACE_ID, faceId);
            return sendCommand(reqId,
                    Definition.CMD_REMOTE_ADD_FACE, params.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int verifyFace(int reqId, String picturePath) {
        try {
            JSONObject params = new JSONObject();
            params.put(Definition.JSON_IMAGE, picturePath);
            return sendCommand(reqId,
                    Definition.CMD_REMOTE_VERIFY_FACE, params.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int remoteGetDomain(int reqId) {
        return sendCommand(reqId,
                Definition.CMD_REMOTE_GET_DOMAIN, null);
    }

    public int remoteGetBindStatus(int reqId) {
        return sendCommand(reqId,
                Definition.CMD_REMOTE_CHECK_BIND_STATUS, null);
    }

    public int locateVision(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_LOCATE_VISION, null);
    }

    public int resetEstimate(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_RESET_ESTIMATE, null);
    }

    public int isRobotHasVision(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_IS_HAS_VISIION, null);
    }

    public int getNavigationConfig(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_GET_CONFIG, null);
    }

    public int getFullCheckStatus(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_GET_FULL_CHECK_STATUS, null);
    }

    public int getNavSensorStatus(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_GET_SENSOR_STATUS, null);
    }

    public int getErrorLog(int reqId, String filePath) {
        return sendCommand(reqId, Definition.CMD_NAVI_GET_ERROR_LOG, filePath);
    }

    public int getLogFile(int reqId, String filePath) {
        return sendCommand(reqId, Definition.CMD_NAVI_GET_LOG_FILE, filePath);
    }

    public int naviTimeOutCmdReport(long timestamp, String cacheId, int reqId, String type, String params) {
        Log.i(TAG, Definition.TAG_NAVI_LOG_REPORT + " type : " + type);
        NaviCmdTimeOutBean bean = new NaviCmdTimeOutBean(timestamp, cacheId, reqId, type, params);
        return sendCommand(reqId, Definition.CMD_NAVI_TIME_OUT_REPORT, new Gson().toJson(bean));
    }

    public int setObstaclesSafeDistance(int reqId, double distance) {
        try {
            JSONObject params = new JSONObject();
            params.put(Definition.JSON_NAVI_MIN_OBSTACLES_DISTANCE, distance);
            return sendCommand(reqId,
                    Definition.CMD_NAVI_SET_MIN_OBSTACLES_DISTANCE, params.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int resetObstaclesSafeDistance(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_SET_MIN_OBSTACLES_DISTANCE, null);
    }

    /**
     * this interface Use SET_GOAL to turn, so you should call it only when estimate Ok.
     *
     * @param reqId
     * @param theta    目标角度
     * @param turnLeft true表示往左转，false表示往右转。TK1限制：如果转动角度小于30度，则TK1不受转向控制，选择最小方向转动
     * @return
     */
    public int turnByNavigation(int reqId, double x, double y, double theta, double linearSpeed, double angularSpeed, boolean turnLeft) {

        try {
            JSONObject json = new JSONObject();
            json.put("x", x);
            json.put("y", y);
            json.put("theta", theta);
            json.put(Definition.JSON_NAVI_LINEAR_SPEED, linearSpeed == 0
                    ? SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED : linearSpeed);
            json.put(Definition.JSON_NAVI_ANGULAR_SPEED, angularSpeed == 0
                    ? SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED : angularSpeed);
            json.put(Definition.JSON_NAVI_LEFT_OR_RIGHT, turnLeft);


            String type = Definition.CMD_NAVI_TURN_BY_NAVIGATION;
            if (mContinueCmds.containsKey(type)) {
                int cmdId = mContinueCmds.get(type);
                cancelCommand(cmdId, type, false);
            }

            int cmdId = sendCommand(reqId, Definition.CMD_NAVI_TURN_BY_NAVIGATION, json.toString());
            mContinueCmds.put(type, cmdId);
            return cmdId;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    public int packLogFile(int reqId, long startTime, long endTime) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_LAMP_START_TIME, startTime);
            param.put(Definition.JSON_LAMP_END_TIME, endTime);
            return sendCommand(reqId, Definition.CMD_NAVI_PACK_LOG_FILE, param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int reportNavigationStatus(int reqId, double x, double z) {
        try {
            double degreeSpeed = Math.toDegrees(z);
            JSONObject params = new JSONObject();
            params.put("speedZ", degreeSpeed);
            params.put("speedX", x);

            return sendCommand(reqId,
                    Definition.CMD_REPORT_NAVIGATION_STATUS, params.toString());

        } catch (JSONException e) {
            e.printStackTrace();
        }

        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int getCanRotateSupport(int reqId) {
        return sendCommand(reqId, Definition.CMD_CAN_GET_ROTATE_SUPPORT, null);
    }

    public int remotePostPrepared(int reqId, CommandListener listener) {
        return sendCommand(reqId, Definition.CMD_REMOTE_POST_PREPARED, null);
    }

    public int getRemotePersonInfo(int reqId, PictureInfo info) {
        return sendCommand(reqId, Definition.CMD_REMOTE_GET_PERSON_INFO, new Gson().toJson(info));
    }

    //Vision auto charge - the same function with can auto charge.
    public int startVisionCharge(int reqId, boolean isFrontCharge) {
        try {
            JSONObject json = new JSONObject();
            json.put(Definition.JSON_NAVI_IS_FRONT_CHARGE, isFrontCharge);
            return sendCommand(reqId, Definition.CMD_NAVI_VISION_CHARGE_START, json.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int stopVisionCharge(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_VISION_CHARGE_STOP, null);
    }

    //------------------------------ Can apis --------------------------------------//

    public int switchChargeMode(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_SWITCH_AUTO_CHARGE_MODE, null);
    }

    public int switchManualMode(int reqId) {
        return sendCommand(reqId, Definition.CMD_NAVI_SWITCH_MANUAL_MODE, null);
    }

    //Can auto charge
    public int startAutoCharge(int reqId) {
        return sendCommand(reqId, Definition.CMD_CAN_AUTO_CHARGE_START, null);
    }

    public int stopAutoCharge(int reqId) {
        return sendCommand(reqId, Definition.CMD_CAN_AUTO_CHARGE_END, null);
    }

    public int getChargeStatus(int reqId) {
        return sendCommand(reqId, Definition.CMD_CAN_GET_CHARGE_STATUS, null);
    }

    public int getCanStatus(int reqId) {
        return sendCommand(reqId, Definition.CMD_CAN_GET_STATUS, null);
    }

    public int setLampColor(int reqId, int target, int color) {
        JSONObject params = new JSONObject();
        try {
            params.put(Definition.JSON_LAMP_TARGET, target);
            params.put(Definition.JSON_LAMP_COLOR_RGB_VALUE, color);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return sendCommand(reqId, Definition.CMD_CAN_LAMP_COLOR, params.toString());
    }

    public int startHeadInspection(int reqId, String inspectionJson) {
        try {
            return sendCommand(reqId, Definition.CMD_HEAD_INSPECTION,
                    inspectionJson);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 开始录视频
     *
     * @param reqId
     * @param path  全路径：文件夹路径+文件名.mp4
     * @return
     */
    public int startVisionRecord(int reqId, String path) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_VISION_RECORD_PATH, path);
            return sendCommand(reqId, Definition.CMD_VISION_START_VISION_RECORD, param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int stopVisionRecord(int reqId) {
        return sendCommand(reqId, Definition.CMD_VISION_STOP_VISION_RECORD, null);
    }

    /**
     * 快照操作，开始采集视频
     */
    public int startDumpRecord(int reqId) {
        return sendCommand(reqId, Definition.CMD_DUMP_START_RECORD, null);
    }

    /**
     * 快照操作，停止采集视频
     */
    public int stopDumpRecord(int reqId) {
        return sendCommand(reqId, Definition.CMD_DUMP_STOP_RECORD, null);
    }

    /**
     * 快照操作，采集跟随关键帧
     */
    public int getDumpKeyFrame(int reqId) {
        return sendCommand(reqId, Definition.CMD_DUMP_GET_KEY_FRAME, null);
    }

    /**
     * 快照操作，发生异常时主动上报快照的简要信息（不含文件）
     */
    public int reportMiniDumpInfo(int reqId, final DumpInfoBean infoBean) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_DUMP_INOF_ERROR_TYPE, infoBean.getErrorType());
            param.put(Definition.JSON_DUMP_INOF_ERROR_MODULE, infoBean.getErrorModule());
            param.put(Definition.JSON_DUMP_INOF_ERROR_TIMESTAMP, infoBean.getTimestamp());
            param.put(Definition.JSON_DUMP_INOF_DESCRIPTION, infoBean.getDescription());
            param.put(Definition.JSON_DUMP_INOF_FILE_ID, infoBean.getFileId());
            param.put(Definition.JSON_DUMP_INOF_FILE_SIZE, infoBean.getFileSize());

            return sendCommand(reqId, Definition.CMD_DUMP_REPORT, param.toString());

        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    /**
     * 快照操作，上报dump文件
     */
    public int uploadMiniDumpFile(int reqId, final String fileId, final String filePath, boolean isNeedDelete) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_REMOTE_UPLOAD_FILE_ID, fileId);
            param.put(Definition.JSON_REMOTE_UPLOAD_FILE_TYPE, Definition.UPLOAD_FILE_TYPE_SNAPSHOT);
            param.put(Definition.JSON_REMOTE_UPLOAD_FILE_PATH, filePath);
            param.put(Definition.JSON_REMOTE_UPLOAD_NEED_DELETE, isNeedDelete);

            return sendCommand(reqId, Definition.CMD_DUMP_UPLOAD_FILE, param.toString());

        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    /**
     * 快照操作，上报dump文件上传状态
     * status取值：
     * public static final int START = 0;
     * public static final int ON_PROGRESS = 1;
     * public static final int SUCCESS = 2;
     * public static final int FAILED = 3;
     * failReasion 没有可传null
     */
    public int reportDumpFileUploadStatus(int reqId, final String fileId, final int status, final String failReason) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_REMOTE_UPLOAD_FILE_ID, fileId);
            param.put(Definition.JSON_REMOTE_UPLOAD_STATUS, status);
            param.put(Definition.JSON_REMOTE_UPLOAD_FAIL_REASON, failReason);

            return sendCommand(reqId, Definition.CMD_DUMP_UPLOAD_REPORT_STATUS, param.toString());

        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_NO_MATCH_REQ;
    }

    public int startVisionBodyTracker(int reqId, String name, int id, TrackMode mode) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_NAME, name);
            param.put(Definition.JSON_HEAD_ID, id);
            param.put(Definition.JSON_HEAD_MODE, mode.getValue());
            param.put(Definition.JSON_HEAD_CONTROL, Definition.JSON_HEAD_START);
            return sendCommand(reqId, Definition.CMD_HEAD_START_BODY_TRACKER,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int stopVisionBodyTracker(int reqId) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_HEAD_CONTROL, Definition.JSON_HEAD_STOP);
            param.put(Definition.JSON_HEAD_NAME, "");
            param.put(Definition.JSON_HEAD_ID, -1);
            param.put(Definition.JSON_HEAD_MODE, 3);
            return sendCommand(reqId, Definition.CMD_HEAD_STOP_BODY_TRACKER, param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int setChassisRelocation(int reqId, int mode, Pose pose, CommandListener listener) {
        try {
            Pose relocationPose = new Pose();
            if (pose != null) {
                relocationPose = pose;
            }
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_RELOCATION_POSE, relocationPose);
            param.put(Definition.JSON_NAVI_RELOCATION_TYPE, mode);
            return sendCommand(reqId, Definition.CMD_NAVI_SET_RELOCATION,
                    param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public void updateRadarStatus(int reqId, boolean openRadar) {
        try {
            JSONObject param = new JSONObject();
            param.put("openRadar", openRadar);
            sendCommand(reqId, Definition.CMD_NAVI_SET_RADAR_STATUS, param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public int setPowerLpm(int reqId, String toJson) {
        return sendCommand(reqId, Definition.CMD_CAN_SET_POWER_LPM, toJson);
    }

    public void queryRadarStatus(int reqId) {
        sendCommand(reqId, Definition.CMD_NAVI_QUERY_RADAR_STATUS, null);
    }

    public void queryChargeAreaConfig(int reqId) {
        sendCommand(reqId, Definition.CMD_NAVI_QUERY_CHARGE_AREA_CONFIG, null);
    }

    public void getPlacesByType(int reqId, int typeId) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(Definition.JSON_NAVI_TYPE_ID, typeId);
            sendCommand(reqId, Definition.CMD_NAVI_GET_PLACELIST_BY_TYPE, jsonObject.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public void writeMultiRobotExtraData(int reqId, JSONArray array, double time) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(Definition.JSON_NAVI_DATA, array);
            jsonObject.put(Definition.JSON_NAVI_TIME, time);
            sendCommand(reqId, Definition.CMD_NAVI_WRITE_MULTI_ROBOT_EXTRA_DATA, jsonObject.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public void resetWheel(int reqId) {
        sendCommand(reqId, Definition.CMD_CAN_RESET_WHEEL, null);
    }

    public int updateChassisCameraEnableState(int reqId, Definition.ChassisCameraType cameraType, boolean enable){
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_CAMERA_TYPE, cameraType.getValue());
            param.put(Definition.JSON_NAVI_ENABLE_STATE, enable);
            return sendCommand(reqId, Definition.CMD_NAVI_SET_CAMERA_STATE, param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int getMapInfo(int reqId, String mapName) {
        return sendCommand(reqId, Definition.CMD_NAVI_GET_MAP_INFO, mapName);
    }

    /**
     * 开始缓存视觉图片信息
     * @param reqId
     * @param cacheSec 代表快照的时间跨度，单位：秒，比如40.0f表示向前缓存40秒，如果该字段为空，使用默认值40.0f
     * @param intervalSec 代表快照的存储间隔，单位：秒，比如2.0f表示每隔2秒存一帧，如果该字段为空，使用默认值2.0f
     * @return
     */
    public int startCacheVisionPictures(int reqId, double cacheSec, double intervalSec) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CACHE_SEC, cacheSec);
            param.put(Definition.JSON_INTERVAL_SEC, intervalSec);
            return sendCommand(reqId, Definition.CMD_VISION_START_CACHE_PICTURES, param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 停止缓存视觉图片信息，结合startCacheVisionPictures使用
     */
    public int stopCacheVisionPictures(int reqId) {
        return sendCommand(reqId, Definition.CMD_VISION_STOP_CACHE_PICTURES, null);
    }

    /**
     * 获取缓存视觉图片信息，beginSec=40.0f，endSec=20.0f 表示向前查找40秒到20秒之间的快照并存储
     * @param reqId
     * @param beginSec  快照区间的起始时间，默认40.0f
     * @param endSec    快照区间的结束时间，默认20.0f
     * @param folderPath 代表快照存储的路径，如果该路径不存在，会自动创建，默认 "/storage/emulated/0/Download/frame_capture/"
     * @return
     */
    public int saveCacheVisionPictures(int reqId, double beginSec, double endSec, String folderPath) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_BEGIN_SEC, beginSec);
            param.put(Definition.JSON_END_SEC, endSec);
            param.put(Definition.JSON_FILE_PATH, folderPath);
            return sendCommand(reqId, Definition.CMD_VISION_SAVE_CACHE_PICTURES, param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;

    }

    /**
     * 呼叫电梯到指定楼层
     * @param inElevator 是否在电梯内
     * @param originFloor 出发楼层，楼层的楼层映射
     * @param targetFloor 目标楼层，楼层的楼层映射
     */
    public int callElevatorToTargetFloor(int reqId, boolean inElevator, int originFloor, int targetFloor) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CONTROL_IS_IN_ELEVATOR, inElevator?1:0);//0:不在电梯内，1:在电梯内
            param.put(Definition.JSON_CONTROL_ORIGIN_FLOOR, originFloor);
            param.put(Definition.JSON_CONTROL_TARGET_FLOOR, targetFloor);
            //call 接口不应该是持续的，并且没有封装对应的stop接口
            return sendCommand(reqId, Definition.CMD_CONTROL_CALL_ELEVATOR, param.toString(), false);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 注册电梯
     */
    public int registerElevator(int reqId) {
        try {
            return sendCommand(reqId,Definition.CMD_CONTROL_REGISTER_ELEVATOR, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 上报机器人状态给梯控
     * @state 机器人状态 {@link Definition.RobotElevatorState}
     */
    public int updateRobotElevatorStatus(int reqId, Definition.RobotElevatorState robotState) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CONTROL_ROBOT_STATE, robotState.getValue());
            return sendCommand(reqId, Definition.CMD_CONTROL_ROBOT_STATE_UPDATE, param.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 打开电梯门
     */
    public int openElevatorDoor(int reqId) {
        try {
            return sendCommand(reqId, Definition.CMD_CONTROL_OPEN_ELEVATOR_DOOR, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 关闭电梯门
     */
    public int closeElevatorDoor(int reqId) {
        try {
            return sendCommand(reqId, Definition.CMD_CONTROL_CLOSE_ELEVATOR_DOOR, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 释放电梯
     */
    public int releaseElevator(int reqId) {
        try {
            return sendCommand(reqId,Definition.CMD_CONTROL_RELEASE_ELEVATOR, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }
    /**
     * 控制闸机
     */
    public int contralGate(int reqId, String mapName) {
        try {
            return sendCommand(reqId,Definition.CMD_CONTROL_CALL_GATE, mapName);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 控制闸机
     */
    public int controlGate(int reqId, String params) {
        try {
            return sendCommand(reqId,Definition.CMD_CONTROL_CALL_GATE, params);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 打开闸机门
     */
    public int openGateDoor(int reqId, String params) {
        try {
            return sendCommand(reqId, Definition.CMD_CONTROL_OPEN_GATE_DOOR, params);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 反向打开闸机门
     */
    public int openATGateDoor(int reqId, String params) {
        try {
            return sendCommand(reqId, Definition.CMD_CONTROL_OPEN_REV_GATE_DOOR, params);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 打开闸机门
     */
    public int closeGateDoor(int reqId, String params) {
        try {
            return sendCommand(reqId, Definition.CMD_CONTROL_CLOSE_GATE_DOOR, params);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 释放闸机
     */
    public int releaseGate(int reqId, String params) {
        try {
            return sendCommand(reqId,Definition.CMD_CONTROL_RELEASE_GATE, params);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int getNaviPathInfoToGoals(int reqId, List<String> goalsName){
        if (goalsName != null && goalsName.size() != 0) {
            JSONArray array = new JSONArray();

            JSONObject object;
            for(Iterator var5 = goalsName.iterator(); var5.hasNext(); array.put(object)) {
                String goalName = (String)var5.next();
                object = new JSONObject();

                try {
                    object.put("name", goalName);
                } catch (JSONException var9) {
                    var9.printStackTrace();
                    return Definition.CMD_SEND_ERROR_UNKNOWN;
                }
            }

            return sendCommand(reqId,Definition.CMD_NAVI_GET_NAVI_PATH_INFO_TO_GOALS, array.toString());
        } else {
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    public int queryCurrentDeviceMultipleConfig(int reqId) {
        try {
            return sendCommand(reqId, Definition.CMD_NAVI_GET_MULTI_ROBOT_CONFIG, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int controlElectricDoor(int reqId, int doorCmd) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_CAN_ELECTRIC_DOOR_CTRL, doorCmd);
            return sendCommand(reqId, Definition.CMD_CAN_ELECTRIC_DOOR_CTRL, param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public boolean cancelCommand(int cmdId, String cmdType) {
        return cancelCommand(cmdId, cmdType, true);
    }

    public boolean cancelCommand(int cmdId, String cmdType, boolean isForceStop) {
        return mRes != null && mRes.cancelCommand(cmdId, cmdType, isForceStop);
    }

    public int sendCommand(int reqId, String command, String params) {
        return sendCommand(reqId, command, params, false);
    }

    public int sendCommandNoResponse(int reqId, String command, String params) {
        if (mRes == null) {
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
        return mRes.sendCommand(reqId, command, params, false, null);
    }

    public int sendCommand(int reqId, String command, String params, boolean isContinues) {
        if (mRes == null) {
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
        int cmdId = mRes.sendCommand(reqId, command, params, isContinues, new OnCmdResponse() {
            @Override
            public void onCmdResponse(int cmdId, String command, String result, String extraData) {
                if(!Definition.CMD_HEAD_GET_ALL_PERSON_INFOS.equals(command)){
                    Log.d(TAG, "Local api receive command : " + cmdId + "  type : " + command + "  result : " + result);
                }
                mWaitingCmds.remove(cmdId);
                if (mResponseProcessor != null) {
                    mResponseProcessor.onCmdResponse(cmdId, command, result, extraData);
                }
            }

            @Override
            public void onCmdStatusUpdate(int cmdId, String command, String status, String extraData) {
                if (mResponseProcessor != null) {
                    mResponseProcessor.onCmdStatusUpdate(cmdId, command, status, extraData);
                }
            }
        });
        Log.d(TAG, "Local api send command : " + cmdId + "  type : " + command + "  params : " + params);
        if (mResponseProcessor.isRunning()) {
            mWaitingCmds.putIfAbsent(cmdId, command);
        }
        return cmdId;
    }

    public interface OnCmdResponse {
        void onCmdResponse(int cmdId, String command, String result, String extraData);

        void onCmdStatusUpdate(int cmdId, String command, String status, String extraData);
    }

    public int startAlign(int reqId, String param) {
        return sendCommand(reqId, Definition.CMD_NAVI_ALIGN_START, param);
    }

    public int stopAlign(int reqId, String param) {
        return sendCommand(reqId, Definition.CMD_NAVI_ALIGN_CANCEL, param);
    }

    public int startHumanFollow(int reqId, String followId, int lostFindTimeout) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_NAVI_FOLLOW_ID, followId);
            param.put(Definition.JSON_NAVI_FOLLOW_LOST_FIND_TIMEOUT, lostFindTimeout);
            return sendCommand(reqId, Definition.CMD_NAVI_START_HUMAN_FOLLOW, param.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    public int stopHumanFollow(int reqId, String param) {
        return sendCommand(reqId, Definition.CMD_NAVI_STOP_HUMAN_FOLLOW, param);
    }
}
