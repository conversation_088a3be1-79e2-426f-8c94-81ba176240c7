/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.ainirobot.coreservice.action;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.RemoteRegisterBean;
import com.ainirobot.coreservice.client.person.FaceBean;
import com.ainirobot.coreservice.core.LocalApi;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;

public class RegisterAction_v1 extends AbstractAction<RemoteRegisterBean> {

    private static final String TAG = RegisterAction_v1.class.getSimpleName();
    private static final int PICTURE_NUMBER = 1;

    private enum State {
        IDLE, GET_PICTURE, REGISTERING
    }

    private int mReqId = 0;
    private int mPersonId;
    private FaceBean mFaceBean;

    private volatile State mState = State.IDLE;

    RegisterAction_v1(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_REGISTER_v1, resList);
    }

    @Override
    protected boolean startAction(RemoteRegisterBean parameter, LocalApi api) {
        mPersonId = parameter.getPersonId();
        mFaceBean = new FaceBean();
        mFaceBean.setName(parameter.getPersonName());
        mFaceBean.setFaceId(parameter.getFaceId());
        Log.d(TAG, "startAction personName: " + mFaceBean.getName()
                + ", personId: " + mPersonId + ", faceId: " + mFaceBean.getFaceId());
        if (TextUtils.isEmpty(parameter.getPicturePath())) {
            updateState(State.GET_PICTURE);
            mApi.getPictureById(mReqId, mPersonId, PICTURE_NUMBER);
        } else {
            updateState(State.REGISTERING);
            mFaceBean.setPictureUri(parameter.getPicturePath());
            mApi.addFace(mReqId, parameter.getPicturePath(), mFaceBean.getName(),
                    mFaceBean.getFaceId());
        }
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        Log.d(TAG, "cmdResponse cmdId: " + cmdId + ", command: " + command
                + ", result: " + result + ", isRunning: " + isRunning());
        if (!isRunning()) {
            return false;
        }
        switch (command) {
            case Definition.CMD_HEAD_GET_PICTURE_BY_ID:
                handleGetPictureById(result);
                return true;
            case Definition.CMD_REMOTE_ADD_FACE:
                handleAddFace(result);
                return true;
            default:
                return false;
        }
    }

    private void handleGetPictureById(String result) {
        Log.d(TAG, "handleGetPictureById result: " + result + ", state: " + mState);
        if (mState != State.GET_PICTURE) {
            return;
        }
        String status = null;
        JSONArray pictures = null;
        int personId = -1;
        try {
            JSONObject json = new JSONObject(result == null ? "" :
                    result);
            status = json.optString("status");
            pictures = json.optJSONArray("pictures");
            personId = json.optInt("id", -1);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        Log.d(TAG, "handleGetPictureById status: " + status + ", id: " + personId
                + ", pictures: " + pictures);
        if (mPersonId != personId || !"ok".equals(status)
                || pictures == null || pictures.length() <= 0
                || TextUtils.isEmpty(pictures.optString(0))) {
            onError(Definition.RESULT_FAILURE, "get picture failed");
        } else {
            mFaceBean.setPictureUri(pictures.optString(0));
            updateState(State.REGISTERING);
            mApi.addFace(mReqId, pictures.optString(0), mFaceBean.getName(),
                    mFaceBean.getFaceId());
        }
    }

    private void handleAddFace(String result) {
        Log.d(TAG, "handleAddFace result: " + result + ", state: " + mState);
        if (mState != State.REGISTERING) {
            return;
        }
        String faceId = null;
        try {
            JSONObject json = new JSONObject(result == null ? "" :
                    result);
            JSONObject data = json.optJSONObject("data");
            Log.d(TAG, "handleAddFace data: " + data);
            if (data != null) {
                JSONObject user = data.optJSONObject("user_info");
                Log.d(TAG, "handleAddFace user: " + user);
                if (user != null) {
                    faceId = user.optString("user_outer_id");
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        Log.d(TAG, "handleAddFace faceId: " + faceId);
        if (!TextUtils.isEmpty(faceId) && faceId.equals(mFaceBean.getFaceId())) {
            mFaceBean.setOnlyLocal(false);
            onResult(Definition.RESULT_SUCCEED, mGson.toJson(mFaceBean));
        } else {
            onError(Definition.RESULT_FAILURE, "add face failed");
        }
    }

    private void updateState(State newState) {
        Log.d(TAG, "updateState: newState: " + newState + ", state: " + mState);
        mState = newState;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        Log.d(TAG, "stopAction");
        mState = State.IDLE;
        return true;
    }

    @Override
    protected boolean verifyParam(RemoteRegisterBean param) {
        return param != null && !TextUtils.isEmpty(param.getPersonName())
                && !TextUtils.isEmpty(param.getFaceId());
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return null;
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return null;
    }
}
