package com.ainirobot.coreservice.client.speech.entity;

import android.text.TextUtils;
import android.util.Log;

public enum LangParamsEnum {
    /**
     * 未定义
     */
    UNDEFINED("未定义", "undefined", -1),

    /**
     * 普通话
     */
    ZH_CN("普通话", "zh_CN", 1),

    /**
     * 美国英语
     */
    EN_US("美国英语", "en_US", 2),

    /**
     * 韩语
     */
    KO_KR("韩语", "ko_KR", 3),

    /**
     * 日语
     */
    JA_JP("日语", "ja_JP", 4),

    /**
     * 泰语
     */
    TH_TH("泰语", "th_TH", 5),

    /**
     * 西班牙语
     */
    ES_ES("西班牙语", "es_ES", 6),

    /**
     * 德语
     */
    DE_DE("德语", "de_DE", 7),

    /**
     * 俄语
     */
    RU_RU("俄语", "ru_RU", 8),

    /**
     * 法语
     */
    FR_FR("法语", "fr_FR", 9),

    /**
     * 葡萄牙语
     */
    PT_PT("葡萄牙语", "pt_PT", 10),

    /**
     * 荷兰语
     */
    NL_NL("荷兰语", "nl_NL", 11),

    /**
     * 意大利语
     */
    IT_IT("意大利语", "it_IT", 12),

    /**
     * 阿拉伯语
     */
    AR_EG("阿拉伯语", "ar_EG", 13),

    /**
     * 丹麦语
     */
    DA_DK("丹麦语", "da_DK", 14),

    /**
     * 芬兰语
     */
    FI_FI("芬兰语", "fi_FI", 15),

    /**
     * 北印度语
     */
    HI_IN("北印度语", "hi_IN", 16),

    /**
     * 挪威语
     */
    NB_NO("挪威语", "nb_NO", 17),

    /**
     * 波兰语
     */
    PL_PL("波兰语", "pl_PL", 18),

    /**
     * 瑞典语
     */
    SV_SE("瑞典语", "sv_SE", 19),

    /**
     * 嘉泰罗尼亚语
     */
    CA_ES("嘉泰罗尼亚语", "ca_ES", 20),

    /**
     * 澳洲英语
     */
    EN_AU("澳洲英语", "en_AU", 22),

    /**
     * 加拿大英语
     */
    EN_CA("加拿大英语", "en_CA", 23),

    /**
     * 英国英语
     */
    EN_GB("英国英语", "en_GB", 24),

    /**
     * 印度英语
     */
    EN_IN("印度英语", "en_IN", 25),

    /**
     * 新西兰英语
     */
    EN_NZ("新西兰英语", "en_NZ", 26),

    /**
     * 粤语
     */
    ZH_GD("粤语", "zh_GD", 100),

    /**
     * 上海话
     */
    ZH_SH("上海话", "zh_SH", 101),

    /**
     * 台湾话
     */
    ZH_TW("台湾话", "zh_TW", 102),

    /**
     * 长沙话
     */
    ZH_CS("长沙话", "zh_CS", 103),
    /**
     * 沙特阿拉伯语
     */
    AR_SA("沙特阿拉伯语", "ar_SA", 104),

    /**
     * 罗马尼亚语
     */
    Ro_Ro("罗马尼亚语", "ro_RO", 105),

    /**
     * 马来西亚语
     */
    MS_MY("马来西亚语", "ms_MY", 106),

    /**
     * 越南语
     */
    VI_VN("越南语", "vi_VN", 107),

    /**
     * 印度尼西亚语
     */

    ID_ID("印度尼西亚语", "id_ID", 108),

    /**
     * 菲律宾语
     */
    FIL_PH("菲律宾语", "fil_PH", 109),

    /**
     * 土耳其语
     */
    TR_TR("土耳其语","tr_TR",110),

    /**
     * 捷克语
     */
    CS_CZ("捷克语","cs_CZ",111),

    /**
     * 希腊语
     */
    EL_GR("希腊语","el_GR",112),

    /**
     * 匈牙利语
     */
    HU_HU("匈牙利语","hu_HU",113),

    /**
     * 葡萄牙语（巴西）
     */
    PT_BR("葡萄牙语（巴西）","pt_BR",114),

    /**
     * 斯洛伐克语
     */
    SK_SK("斯洛伐克语","sk_SK",115);

    private static String TAG = LangParamsEnum.class.getSimpleName();
    public final String chineseName;
    public final String codeName;
    public final int codeValue;

    LangParamsEnum(String chineseName, String codeName, int codeValue) {
        this.chineseName = chineseName;
        this.codeName = codeName;
        this.codeValue = codeValue;
    }

    public static LangParamsEnum getLangEnumByChineseName(String chineseName) {
        if (TextUtils.isEmpty(chineseName)) {
            Log.e(TAG, "chineseName: " + chineseName);
            return LangParamsEnum.ZH_CN;
        }
        for (LangParamsEnum lang : LangParamsEnum.values()) {
            if (chineseName.equals(lang.chineseName)) {
                return lang;
            }
        }
        return LangParamsEnum.ZH_CN;
    }

    public static LangParamsEnum getLangEnumByCodeValue(int codeValue) {
        for (LangParamsEnum lang : LangParamsEnum.values()) {
            if (codeValue == lang.codeValue) {
                return lang;
            }
        }
        Log.e(TAG, "codeValue: " + codeValue);
        return LangParamsEnum.ZH_CN;
    }

    public static LangParamsEnum getLangEnumByCodeName(String codeName) {
        if (TextUtils.isEmpty(codeName)) {
            Log.e(TAG, "codeName: " + codeName);
            return LangParamsEnum.ZH_CN;
        }
        for (LangParamsEnum lang : LangParamsEnum.values()) {
            if (codeName.equals(lang.codeName)) {
                return lang;
            }
        }
        return LangParamsEnum.ZH_CN;
    }
}
