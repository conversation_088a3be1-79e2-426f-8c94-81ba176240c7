package com.ainirobot.coreservice.utils;

import android.util.Log;

import com.ainirobot.coreservice.action.AbstractAction;
import com.ainirobot.coreservice.client.Definition;

public class NavigationStatusUtils {
    public static void processNavigationStatus(AbstractAction action, String tag, String status, String extraData) {
        switch (status) {
            case Definition.NAVIGATION_OCCLUDED:
                action.onStatusUpdate(Definition.STATUS_GOAL_OCCLUDED, "The target is occluded", extraData);
                break;

            case Definition.NAVIGATION_OCCLUDED_END:
                action.onStatusUpdate(Definition.STATUS_GOAL_OCCLUDED_END, "The occluded end", extraData);
                break;

            case Definition.NAVIGATION_AVOID:
                action.onStatusUpdate(Definition.STATUS_NAVI_AVOID, "The local path search failed", extraData);
                break;

            case Definition.NAVIGATION_AVOID_END:
                action.onStatusUpdate(Definition.STATUS_NAVI_AVOID_END, "The local path search succeed", extraData);
                break;

            case Definition.NAVIGATION_OUT_MAP:
                action.onStatusUpdate(Definition.STATUS_NAVI_OUT_MAP, "The goal is out of map or more dangerous", extraData);
                break;

            case Definition.NAVIGATION_GLOBAL_PATH_FAILED:
                action.onStatusUpdate(Definition.STATUS_NAVI_GLOBAL_PATH_FAILED, "The global is path search failed", extraData);
                break;

            case Definition.NAVIGATION_OBSTACLES_AVOID:
                action.onStatusUpdate(Definition.STATUS_NAVI_OBSTACLES_AVOID, "obstacles avoid", extraData);
                break;

            case Definition.NAVIGATION_STARTED:
                action.onStatusUpdate(Definition.STATUS_START_NAVIGATION, "Navigation has been started", extraData);
                break;

            case Definition.NAVIGATION_MULTI_ROBOT_WAITING:
                action.onStatusUpdate(Definition.STATUS_NAVI_MULTI_ROBOT_WAITING, "Multiple robots are waiting to avoid obstacles");
                break;

            case Definition.NAVIGATION_MULTI_ROBOT_WAITING_END:
                action.onStatusUpdate(Definition.STATUS_NAVI_MULTI_ROBOT_WAITING_END, "Multiple robots passageways returned to normal");
                break;

            case Definition.NAVIGATION_GO_STRAIGHT:
                action.onStatusUpdate(Definition.STATUS_NAVI_GO_STRAIGHT, "The robot is walking in a straight line", extraData);
                break;

            case Definition.NAVIGATION_TURN_LEFT:
                action.onStatusUpdate(Definition.STATUS_NAVI_TURN_LEFT, "The robot is currently turning left", extraData);
                break;

            case Definition.NAVIGATION_TURN_RIGHT:
                action.onStatusUpdate(Definition.STATUS_NAVI_TURN_RIGHT, "The robot is currently turning right", extraData);
                break;

            case Definition.NAVIGATION_NOT_MOVING_LONG_TIME:
//                saveVisionSnapshotData(extraData);
                break;

            case Definition.NAVIGATION_SET_PRIORITY_FAILED:
                action.onStatusUpdate(Definition.STATUS_NAVI_SET_PRIORITY_FAILED, "Current navigation task priority setting failed!", extraData);
                break;

            case Definition.NAVIGATION_MULTI_MAP_NOT_MATCH:
                action.onStatusUpdate(Definition.STATUS_NAVI_MULTI_MAP_NOT_MATCH, "navigation multi robot map not match!", extraData);
                break;

            case Definition.NAVIGATION_MULTI_LORA_DISCONNECT:
                action.onStatusUpdate(Definition.STATUS_NAVI_MULTI_LORA_DISCONNECT, "navigation multi robot lora disconnect!", extraData);
                break;

            case Definition.NAVIGATION_MULTI_LORA_CONFIG_FAIL:
                action.onStatusUpdate(Definition.STATUS_NAVI_MULTI_LORA_CONFIG_FAIL, "navigation multi robot lora config fail!", extraData);
                break;

            case Definition.NAVIGATION_MULTI_VERSION_NOT_MATCH:
                action.onStatusUpdate(Definition.STATUS_NAVI_MULTI_VERSION_NOT_MATCH, "navigation multi robot version not match!", extraData);
                break;

            case Definition.NAVIGATION_WHEEL_SLIP:
                action.onStatusUpdate(Definition.STATUS_NAVI_WHEEL_SLIP, "navigation wheel slip", extraData);
                break;
            default:
                Log.i(tag, "Command status: " + status + " doesn't be handled");
                break;
        }
    }
}
