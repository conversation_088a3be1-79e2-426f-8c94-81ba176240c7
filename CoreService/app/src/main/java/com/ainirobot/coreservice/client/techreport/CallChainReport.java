package com.ainirobot.coreservice.client.techreport;

import android.content.Context;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

public class CallChainReport extends BaseBiReport {


    /*
     * 串联id，主要用于流程串联
     */
    private static final String CALL_ID = "call_id";

    /*
     * 调用链涉及的业务名称
     * inspect: 开机自检
     */
    private static final String BUSINESS_NAME = "business_name";

    /*
     * 此次调用链数据来源
     */
    private static final String DATA_SOURCE = "data_source";

    /*
     * 点用链app包名
     */
    private static final String PACKAGE_NAME = "package_name";

    /*
     * 调用链节点类名
     */
    private static final String NODE_CLASS_NAME = "node_class_name";

    /*
     * 调用链节点方法类型
     */
    private static final String NODE_METHOD_TYPE = "node_method_type";

    /*
     * 调用链节点方法名
     */
    private static final String NODE_METHOD_NAME = "node_method_name";

    /*
     * 调用链节点数据
     */
    private static final String NODE_PARAMS = "node_params";

    /*
     *调用链节点注释
     */
    private static final String NODE_NOTE = "node_note";

    /*
     * 节点调用栈
     */
    private static final String NODE_STACK_TRACE = "node_stack_trace";

    /*
     * 调用链节点类型
     * 正常流程，异常流程，error
     */
    private static final String NODE_TYPE = "node_type";

    private static final String WAKEUP_ID = "wakeup_id";

    private static final String CTIME = "ctime";

    private Context mContext;

    public CallChainReport() {
        super("gb_robot_os_call_chain");
    }

    public CallChainReport(Context context, String className){
        super("gb_robot_os_call_chain");
        mContext = context;
        addData(NODE_CLASS_NAME, className);
    }

    public CallChainReport(Context cotext, String className, String businessName){
        super("gb_robot_os_call_chain");
        mContext = cotext;
        addData(NODE_CLASS_NAME, className);
        addData(BUSINESS_NAME, businessName);
    }

    public void setClassName(String className) {
        addData(NODE_CLASS_NAME, className);
    }

    public void setBusinessName(String businessName){
        addData(BUSINESS_NAME, businessName);
    }

    @Override
    public void report() {
        if (!ProductInfo.isMeissa()) {
            return;
        }

        if(mContext != null){
            addData(PACKAGE_NAME, mContext.getPackageName());
        }
        addData(CTIME, System.currentTimeMillis());
        if (!ProductInfo.isDeliveryProduct() && !ProductInfo.isMeissaPlus()) {
            super.report();
        }
    }

    public NodeMethod invokeNodeMethod(String methodName, int type){
        NodeMethod method = new NodeMethod(methodName, type);
        return method;
    }

    public NodeMethod invokeNodeMethod(String methodName){
        NodeMethod method = new NodeMethod(methodName);
        return method;
    }

    public NodeInfo invokeNodeInfo(String callId, String param, String note, int type, int dataResource, String stackTrace){
        NodeInfo info = new NodeInfo(callId, param, note, type, dataResource, stackTrace);
        return info;
    }

    public NodeInfo invokeNodeInfo(String param, String note, int type, int dataResource){
        NodeInfo info = new NodeInfo(param, note, type, dataResource);
        return info;
    }

    public NodeInfo invokeNodeInfo(String param, String note, int type){
        NodeInfo info = new NodeInfo(param, note, type, NodeInfo.DATA_ROBOT_OS);
        return info;
    }

    public NodeInfo invokeNodeInfo(String param, String note){
        NodeInfo info = new NodeInfo(param, note);
        return info;
    }

    public class NodeMethod {
        public static final int TYPE_API_CALL = 1;
        public static final int TYPE_API_CALLBACK = 2;
        public static final int TYPE_EVENT_REPORT = 3;
        public static final int TYPE_LIFE_CYCLE = 4;
        public static final int TYPE_FUNCTION = 5;

        NodeMethod(String methodName, int type){
            addData(NODE_METHOD_NAME, methodName);
            addData(NODE_METHOD_TYPE, type);
        }

        NodeMethod(String methodName){
            addData(NODE_METHOD_NAME, methodName);
            addData(NODE_METHOD_TYPE, TYPE_FUNCTION);
        }

        public void setMethodName(String methodName){
            addData(NODE_METHOD_NAME, methodName);
        }

        public void setType(int type) {
            addData(NODE_METHOD_TYPE, type);
        }
    }

    public class NodeInfo {

        public static final int TYPE_NORMAL = 1;
        public static final int TYPE_ABNORMAL = 2;
        public static final int TYPE_ERROR = -1;

        public static final int DATA_ROBOT_OS = 1;
        public static final int DATA_ROM = 2;
        public static final int DATA_VISION = 3;
        public static final int DATA_ROVER = 4;
        public static final int DATA_SPEECH = 5;
        public static final int DATA_CAN = 6;
        public static final int DATA_TK1 = 7;
        public static final int DATA_TX1 = 8;

        NodeInfo(String callId, String param, String note, int type, int dataResource, String stackTrace){
            addData(CALL_ID, callId);
            addData(NODE_NOTE, note);
            addData(NODE_PARAMS, param);
            addData(NODE_STACK_TRACE, stackTrace);
            addData(NODE_TYPE, type);
            addData(DATA_SOURCE, dataResource);
        }

        NodeInfo(String param, String note, int type, int dataResource){
            addData(NODE_NOTE, note);
            addData(NODE_PARAMS, param);
            addData(NODE_TYPE, type);
            addData(DATA_SOURCE, dataResource);
        }

        NodeInfo(String param, String note){
            addData(NODE_NOTE, note);
            addData(NODE_PARAMS, param);
            addData(NODE_TYPE, TYPE_NORMAL);
            addData(DATA_SOURCE, DATA_ROBOT_OS);
        }

        public NodeInfo(String param){
            addData(NODE_PARAMS, param);
            addData(NODE_TYPE, TYPE_NORMAL);
            addData(DATA_SOURCE, DATA_ROBOT_OS);
        }

        public void setParam(String param) {
            addData(NODE_PARAMS, param);
        }

        public void setNote(String note) {
            addData(NODE_NOTE, note);
        }

        public void setType(int type) {
            addData(NODE_TYPE, type);
        }

        public void setStackTrace(String stackTrace) {
            addData(NODE_STACK_TRACE, stackTrace);
        }

        public void setDataResource(int dataResource) {
            addData(DATA_SOURCE, dataResource);
        }
    }
}
