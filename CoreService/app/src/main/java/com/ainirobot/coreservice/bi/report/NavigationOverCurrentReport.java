package com.ainirobot.coreservice.bi.report;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

public class NavigationOverCurrentReport extends BaseBiReport {

    private static final String TABLE_NAME = "base_robot_navigate_overcurrent";
    private static final String TYPE = "type";
    private static final String EVENT_TYPE = "event_type";
    private static final String RESTART_TIMES = "restart_times";
    private static final String CTIME = "ctime";

    public static final int TYPE_WHEEL = 1;

    public static final int EVENT_TYPE_ABNORMAL = 1;
    public static final int EVENT_TYPE_TRY_RESET = 2;
    public static final int EVENT_TYPE_NORMAL = 3;
    public static final int EVENT_TYPE_FAILED_RETRY_TIMES = 4;

    public NavigationOverCurrentReport() {
        super(TABLE_NAME);
    }

    public NavigationOverCurrentReport addEventType(int eventType) {
        addData(EVENT_TYPE, eventType);
        return this;
    }

    public NavigationOverCurrentReport addRestartTimes(int restartTimes) {
        addData(RESTART_TIMES, restartTimes);
        return this;
    }

    @Override
    public void report() {
        addData(TYPE, TYPE_WHEEL);
        addData(CTIME, System.currentTimeMillis());
        super.report();
    }
}
