package com.ainirobot.coreservice.core.apiproxy.impl.person;

import android.os.Handler;
import android.os.Message;
import android.util.Log;

import com.ainirobot.coreservice.action.ActionManager;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.RecognizeBean;
import com.ainirobot.coreservice.client.actionbean.RemoteRegisterBean;
import com.ainirobot.coreservice.client.person.FaceBean;
import com.ainirobot.coreservice.core.apiproxy.result.BaseApiResult;
import com.ainirobot.coreservice.service.CoreService;

import java.util.List;

public class PersonApiProxy_v0 extends PersonBaseApiProxy {

    private static final String TAG = PersonApiProxy_v0.class.getSimpleName();

    public PersonApiProxy_v0(CoreService core, Handler handler) {
        super(core, handler);
    }

    @Override
    public void localFaceDataSync() {
    }

    @Override
    public void registerById(final int personId, final String name, final boolean isReEntry) {
        Log.i(TAG, "registerById personId: " + personId
                + ", name: " + name + ", isReEntry: " + isReEntry);
        register(personId, "", name, isReEntry);
    }

    @Override
    public void registerByPic(final String picturePath, final String name,
                              final boolean isReEntry) {
        Log.i(TAG, "registerByPic picturePath: " + picturePath
                + ", name: " + name + ", isReEntry: " + isReEntry);
        register(-1, picturePath, name, isReEntry);
    }

    private void register(final int personId, final String picturePath, final String name,
                          final boolean isReEntry) {
        RemoteRegisterBean bean = new RemoteRegisterBean();
        bean.setPersonId(personId);
        bean.setPicturePath(picturePath);
        bean.setPersonName(name);
        ActionManager actionManager = mCore.getActionManager();
        actionManager.exStopCmd(Definition.ACTION_REGISTER_v0, false);
        actionManager.exCmd(Definition.ACTION_REGISTER_v0, mGson.toJson(bean),
                new ApiListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.d(TAG, "register result: " + result
                                + ", message: " + message);
                        int apiResult;
                        if (result == Definition.RESULT_SUCCEED) {
                            apiResult = BaseApiResult.RESULT_SUCCESS;
                        } else {
                            apiResult = BaseApiResult.RESULT_FAILED;
                        }
                        if (mHandler != null) {
                            Message msg = new Message();
                            msg.what = MSG_API_RESULT;
                            msg.obj = new BaseApiResult(BaseApiResult
                                    .Type.REGISTER, apiResult, message);
                            mHandler.sendMessage(msg);
                        }
                    }

                    @Override
                    public void onError(int errorCode, String errorString) {
                        Log.d(TAG, "registerByPic errorCode: " + errorCode
                                + ", errorString: " + errorString);
                        int apiResult = BaseApiResult.RESULT_FAILED;
                        if (mHandler != null) {
                            Message msg = new Message();
                            msg.what = MSG_API_RESULT;
                            msg.obj = new BaseApiResult(BaseApiResult
                                    .Type.REGISTER, apiResult, errorString);
                            mHandler.sendMessage(msg);
                        }
                    }
                });
    }

    @Override
    public void recognizeById(final int personId) {
        Log.i(TAG, "recognizeById personId: " + personId);
        recognize(personId, "");
    }

    @Override
    public void recognizeByPic(final String picturePath) {
        Log.i(TAG, "recognizeByPic picturePath: " + picturePath);
        recognize(-1, picturePath);
    }

    private void recognize(final int personId, final String picturePath) {
        RecognizeBean bean = new RecognizeBean();
        bean.setPersonId(personId);
        bean.setPicturePath(picturePath);
        ActionManager actionManager = mCore.getActionManager();
        actionManager.exStopCmd(Definition.ACTION_RECOGNIZE_v0, false);
        actionManager.exCmd(Definition.ACTION_RECOGNIZE_v0, mGson.toJson(bean),
                new ApiListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.d(TAG, "recognize result: " + result
                                + ", message: " + message);
                        int apiResult;
                        Object data;
                        if (result == Definition.RESULT_SUCCEED) {
                            apiResult = BaseApiResult.RESULT_SUCCESS;
                            data = message;
                        } else {
                            apiResult = BaseApiResult.RESULT_FAILED;
                            data = message;
                        }
                        if (mHandler != null) {
                            Message msg = new Message();
                            msg.what = MSG_API_RESULT;
                            msg.obj = new BaseApiResult(BaseApiResult
                                    .Type.RECOGNIZE, apiResult, data);
                            mHandler.sendMessage(msg);
                        }
                    }

                    @Override
                    public void onError(int errorCode, String errorString) {
                        Log.d(TAG, "recognizeByPic errorCode: " + errorCode
                                + ", errorString: " + errorString);
                        int apiResult = BaseApiResult.RESULT_FAILED;
                        if (mHandler != null) {
                            Message msg = new Message();
                            msg.what = MSG_API_RESULT;
                            msg.obj = new BaseApiResult(BaseApiResult
                                    .Type.RECOGNIZE, apiResult, null);
                            mHandler.sendMessage(msg);
                        }
                    }
                });
    }

    @Override
    public void deleteFaceList(List<FaceBean> deleteFaceList) {
    }
}
