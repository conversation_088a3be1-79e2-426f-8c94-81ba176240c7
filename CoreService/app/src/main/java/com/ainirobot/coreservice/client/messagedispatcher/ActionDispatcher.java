/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.client.messagedispatcher;

import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.coreservice.listener.IActionListener;

class ActionDispatcher extends IActionListener.Stub implements IRecyclable {
    private static final String TAG = ActionDispatcher.class.getSimpleName();
    private static RecyclablePool<ActionDispatcher> sPool = new RecyclablePool<>();

    private DispatchHandler mDispatchHandler;
    private ActionListener mListener;

    private ActionDispatcher(DispatchHandler dispatchHandler, ActionListener listener) {
        mDispatchHandler = dispatchHandler;
        mListener = listener;
    }

    static ActionDispatcher obtain(DispatchHandler dispatchHandler, ActionListener listener) {
        ActionDispatcher actionDispatcher = sPool.obtain();

        if (actionDispatcher == null) {
            actionDispatcher = new ActionDispatcher(dispatchHandler, listener);
            if (Definition.DEBUG_ALL_DISPATHER) Log.v(Definition.DISPATCHER_TAG,
                    "new actionDispatcher:"
                    + actionDispatcher.hashCode()
                    + ", dispatchHandler:"
                    + (dispatchHandler == null ? "null" : dispatchHandler.hashCode())
                    + ", listener:"
                    + (listener == null ? "null" : listener.hashCode()));
        } else {
            actionDispatcher.mDispatchHandler = dispatchHandler;
            actionDispatcher.mListener = listener;
            if (Definition.DEBUG_ALL_DISPATHER) Log.v(Definition.DISPATCHER_TAG,
                    "obtain actionDispatcher:"
                    + actionDispatcher.hashCode()
                    + ", dispatchHandler:"
                    + (dispatchHandler == null ? "null" : dispatchHandler.hashCode())
                    + ", listener:"
                    + (listener == null ? "null" : listener.hashCode()));
        }
        return actionDispatcher;
    }

    @Override
    public void onResult(int status, String responseString) throws RemoteException {

    }

    @Override
    public void onError(int errorCode, String errorString) throws RemoteException {

    }

    @Override
    public void onStatusUpdate(int status, String data) throws RemoteException {

    }

    @Override
    public void onResultWithExtraData(int status, String responseString, String extraData) throws RemoteException {
        DispatchHandler dispatchHandler = mDispatchHandler;
        ActionListener listener = mListener;
        Log.d(Definition.DISPATCHER_TAG,"onResultWithExtraData responseString : " + responseString+", extraData : "+extraData);
        if (dispatchHandler != null && listener != null) {
            ActionMessage actionMessage = ActionMessage.obtain(listener
                    , ActionMessage.MsgType.result, responseString, status, extraData);
            dispatchHandler.sendMessage(dispatchHandler.obtainMessage(MessageDispatcher.MSG_ACTION
                    , actionMessage));
            if (Definition.DEBUG_ALL_DISPATHER) Log.v(Definition.DISPATCHER_TAG,
                    "onResult status:" + status + ", data:" + responseString);
            sPool.recycle(this);
        } else {
            Log.e(TAG, "onResult status = " + status + "; data = " + responseString);
            if (Definition.DEBUG_ALL_DISPATHER) Log.v(Definition.DISPATCHER_TAG,
                    "onResult status:" + status + ", data:" + responseString
                    + (dispatchHandler == null ? "null" : dispatchHandler.hashCode())
                    + ", listener:"
                    + (listener == null ? "null" : listener.hashCode()));
        }
    }

    @Override
    public void onErrorWithExtraData(int errorCode, String errorString, String extraData) throws RemoteException {
        DispatchHandler dispatchHandler = mDispatchHandler;
        ActionListener listener = mListener;
        if (dispatchHandler != null && listener != null) {
            ActionMessage actionMessage = ActionMessage.obtain(listener
                    , ActionMessage.MsgType.error, errorString, errorCode, extraData);
            dispatchHandler.sendMessage(dispatchHandler.obtainMessage(MessageDispatcher.MSG_ACTION
                    , actionMessage));
            if (Definition.DEBUG_ALL_DISPATHER) Log.v(Definition.DISPATCHER_TAG,
                    "onError status:" + errorCode + ", data:" + errorString);

            sPool.recycle(this);
        } else {
            Log.e(TAG, "onError status = " + errorCode + "; data = " + errorString);
            if (Definition.DEBUG_ALL_DISPATHER) Log.v(Definition.DISPATCHER_TAG,
                    "onError status:" + errorCode + ", data:" + errorString
                    + (dispatchHandler == null ? "null" : dispatchHandler.hashCode())
                    + ", listener:"
                    + (listener == null ? "null" : listener.hashCode()));
        }
    }

    @Override
    public void onStatusUpdateWithExtraData(int status, String data, String extraData) throws RemoteException {
        DispatchHandler dispatchHandler = mDispatchHandler;
        ActionListener listener = mListener;
        if (dispatchHandler != null && listener != null) {
            ActionMessage actionMessage = ActionMessage.obtain(listener
                    , ActionMessage.MsgType.status, data, status, extraData);
            dispatchHandler.sendMessage(dispatchHandler.obtainMessage(MessageDispatcher.MSG_ACTION
                    , actionMessage));
            if (Definition.DEBUG_ALL_DISPATHER) Log.v(Definition.DISPATCHER_TAG,
                    "onStatusUpdate status:" + status + ", data:" + data);
        } else {
            Log.e(TAG, "onStatusUpdate status = " + status + "; data = " + data);
            if (Definition.DEBUG_ALL_DISPATHER) Log.v(Definition.DISPATCHER_TAG,
                    "onStatusUpdate status:" + status + ", data:" + data
                    + (dispatchHandler == null ? "null" : dispatchHandler.hashCode())
                    + ", listener:"
                    + (listener == null ? "null" : listener.hashCode()));
        }
    }

    @Override
    public void recycle() {
        if (Definition.DEBUG_ALL_DISPATHER) Log.v(Definition.DISPATCHER_TAG,
                "recycle mDispatchHandler:"
                + (mDispatchHandler == null ? "null" : mDispatchHandler.hashCode())
                + ", mListener:"
                + (mListener == null ? "null" : mListener.hashCode()));
        mDispatchHandler = null;
        mListener = null;
    }
}
