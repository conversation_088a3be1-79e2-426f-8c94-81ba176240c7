package com.ainirobot.coreservice.action.advance_charge.state;

import android.util.Log;

import com.ainirobot.coreservice.action.ChargeAdvancedAction;
import com.ainirobot.coreservice.action.advance_charge.AdvanceChargeStateEnum;
import com.ainirobot.coreservice.action.advance_charge.bean.ChargeResult;
import com.ainirobot.coreservice.action.advance_charge.bean.StateData;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.AutoChargeBean;
import com.ainirobot.coreservice.client.actionbean.MultiRobotStatus;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.config.ConfigManager;
import com.google.gson.Gson;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.Timer;
import java.util.TimerTask;

public class GoAdvanceChargeState extends BaseAdvanceChargeState {
    private volatile Status mStatus;
    private List<Pose> mChargePointList;    //回充点列表
    private List<Pose> mChargePileList;    //充电桩列表
    private long navigationTimeout;
    private Timer mNaviToPoseTimer;
    private Pose curChargePilePose;    //当前机器要去的充电桩

    private Set<Integer> mChargePileOccupiedList = new HashSet<>();   //充电桩被占用列表
    private Set<Integer> mGoChargeFailedList = new HashSet<>();   //去回充点失败列表，失败之后去别的可用充电桩

    private enum Status {
        IDLE,
        WAIT,
        GO_CHARGE_POINT,     //导航去回充点
        GO_CHARGE_POINT_FAILED,  //导航去回充点失败
        START_CHARGE,      //开始回充

    }

    private void updateStatus(Status status) {
        Log.i(TAG, "updateStatus: " + status + " mStatus=" + mStatus);
        if (mStatus == status) {
            return;
        }
        switch (status) {
            case WAIT:
                onStateUpdate(Definition.STATUS_CHARGE_WAIT_IN_AREA, "wait in charge area");
                break;
            case GO_CHARGE_POINT:
                onStateUpdate(Definition.STATUS_CHARGE_GO_CHARGE_POINT, "start go charge point");
                break;
            case GO_CHARGE_POINT_FAILED:
            case START_CHARGE:
            default:
                break;
        }
        mStatus = status;
    }

    @Override
    protected void onStart(ChargeAdvancedAction chargeAdvancedAction, StateData stateData) {
        mChargePointList = stateData.getChargePointList();
        mChargePileList = stateData.getChargePileList();
        mChargePileOccupiedList = new HashSet<>();
        mGoChargeFailedList = new HashSet<>();
        updateStatus(Status.IDLE);
        updateParams(stateData.getAutoChargeBean());
        registerMultiRobotStatusListener();
    }

    private void updateParams(AutoChargeBean autoChargeBean) {
        navigationTimeout = NAVIGATION_TIMEOUT;
    }

    private void startNaviToPoseTimer() {
        Log.d(TAG, "startNaviToPoseTimer mStatus:" + mStatus );
        if (mStatus != Status.GO_CHARGE_POINT){
            return;
        }
        cancelNaviToPoseTimer();
        mNaviToPoseTimer = new Timer();
        mNaviToPoseTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (mStatus == Status.GO_CHARGE_POINT) {
                    Log.d(TAG, "go location timeout");
                    int index = mStateData.getChargePileIndex(curChargePilePose);
                    mChargePileOccupiedList.add(index);
                    mGoChargeFailedList.add(index);
                    updateStatus(Status.GO_CHARGE_POINT_FAILED);
                }
            }
        }, navigationTimeout);

    }

    private void cancelNaviToPoseTimer() {
        if (mNaviToPoseTimer != null) {
            mNaviToPoseTimer.cancel();
            mNaviToPoseTimer = null;
        }
    }

    @Override
    public void stop() {
        super.stop();
        Log.d(TAG, "ChargeAdvanceAction stopAction ------------  ");
        cancelNaviToPoseTimer();
        unregisterMultiRobotStatusListener();
        stopVisionCharge();
    }

    @Override
    protected boolean onCmdResponse(int cmdId, String command, String result, String extraData) {
        Log.d(TAG, "cmdResponse cmdType=" + command + " result=" + result);
        switch (command) {
            case Definition.CMD_NAVI_GO_LOCATION:
                Log.d(TAG, "go location result = " + result);
                cancelNaviToPoseTimer();
                if (Definition.NAVIGATION_OK.equals(result)) {
                    updateStatus(Status.START_CHARGE);
                    startVisionCharge(); //开始视觉回充
                } else {
                    Log.d(TAG, "go location failed");
                    int index = mStateData.getChargePileIndex(curChargePilePose);
                    mChargePileOccupiedList.add(index);
                    mGoChargeFailedList.add(index);
                    updateStatus(Status.GO_CHARGE_POINT_FAILED);
                }
                break;
            case Definition.CMD_NAVI_VISION_CHARGE_START:
                if ("timeout".equals(result)) {
                    finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.CHARGE_FAIL_VISION_CHARGE_TIMEOUT));
                    return false;
                }

                Gson gson = new Gson();
                ChargeResult chargeResult = gson.fromJson(result, ChargeResult.class);
                int code = chargeResult.getCode();
                if (code == 25) {
                    chargeResult.setCode(Definition.RESULT_OK);
                    finishAction(Definition.RESULT_OK, mGson.toJson(chargeResult));
                } else if (code == 26) {
                    chargeResult.setCode(Definition.RESULT_FAILED);
                    finishAction(Definition.RESULT_FAILURE, buildFailJson(Definition.CHARGE_FAIL_WHEN_VISION_CHARGE_START));
                }
                break;

            case Definition.CMD_NAVI_VISION_CHARGE_STOP:
                gson = new Gson();
                chargeResult = gson.fromJson(result, ChargeResult.class);
                code = chargeResult.getCode();
                if (code == 0) {
                    writeMultiRobotExtraData(-1, -1, -1);
                    chargeResult.setCode(Definition.RESULT_OK);
                    finishAction(Definition.RESULT_OK, gson.toJson(chargeResult));
                } else {
                    chargeResult.setCode(Definition.RESULT_FAILED);
                    finishAction(Definition.RESULT_FAILED, gson.toJson(chargeResult));
                }
                break;

            default:
                break;
        }
        return true;
    }

    private void startVisionCharge() {
        boolean isFrontCharge = ConfigManager.isFrontCharge();
        Log.d(TAG, "startVisionCharge  isFrontCharge=" + isFrontCharge);
        mChargeAdvancedAction.getApi().startVisionCharge(mChargeAdvancedAction.getReqId(), isFrontCharge);
    }

    private void stopVisionCharge() {
        mChargeAdvancedAction.getApi().stopVisionCharge(mChargeAdvancedAction.getReqId());
    }

    @Override
    protected boolean onCmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        //Log.d(TAG, "onCmdStatusUpdate cmdType=" + command + " status=" + status);
        switch (command) {
            case Definition.CMD_NAVI_GO_LOCATION:
                handleNavigationStatus(status);
                return true;
            case Definition.CMD_NAVI_VISION_CHARGE_START:
                ChargeResult chargeResult = mGson.fromJson(status, ChargeResult.class);
                int code = chargeResult.getCode();

                if (code == 0) {
                    onStateUpdate(Definition.STATUS_VISION_CHARGE_START_GOTO_CHARGING_PILE,
                            "Start go to charging pile");
                } else {
                    onStateUpdate(code, status);
                }
                return true;
        }
        return false;
    }

    @Override
    protected AdvanceChargeStateEnum getNextState() {
        return AdvanceChargeStateEnum.GO_CHARGE_AREA;
    }

    @Override
    protected void handleMultipleRobotStatus(List<MultiRobotStatus> robotStatusList) {
        switch (mStatus) {
            case IDLE:
            case WAIT:
            case GO_CHARGE_POINT_FAILED:
                // 检查优先级，根据优先级高低去充电
                checkChargePriority(robotStatusList);
                break;
            case GO_CHARGE_POINT:
            case START_CHARGE:
                break;
        }
    }

    private void checkChargePriority(List<MultiRobotStatus> robotStatusList) {
        int curChargePriority = mStateData.getCurChargePriority();
        int curChargeAreaId = mStateData.getCurUseAreaId();
        int availablePileCount = 0;
        int higherPriorityRobotCount = 0;

        mChargePileOccupiedList.clear();
        mChargePileOccupiedList.addAll(mGoChargeFailedList);

        // 统计更高优先级的机器人数量
        for (MultiRobotStatus robotStatus : robotStatusList) {
            if (robotStatus.isCurRobot()) {
                continue;
            }
            
            int otherRobotPriority = robotStatus.getChargePriority();
            // 统计更高优先级的机器人（数值更小且不为-1的为更高优先级）
            if (curChargeAreaId == robotStatus.getChargeAreaId() 
                && otherRobotPriority != -1 
                && otherRobotPriority < curChargePriority) {
                Log.d(TAG, "checkChargePriority otherRobotPriority=" + otherRobotPriority);
                higherPriorityRobotCount++;
            }
            // 记录已被锁定的充电桩
            if (curChargeAreaId == robotStatus.getChargeAreaId() 
                && robotStatus.getChargeOccupiedPileIndex() >= 0) {
                Log.d(TAG, "checkChargePriority getChargeOccupiedPileIndex=" + robotStatus.getChargeOccupiedPileIndex());
                mChargePileOccupiedList.add(robotStatus.getChargeOccupiedPileIndex());
            }
        }

        // 计算可用充电桩数量（即将前往被锁定的桩不算）
        for (int i = 0; i < mChargePileList.size(); i++) {
            if (!isPoseInUseCurrently(mChargePileList.get(i), robotStatusList)) {
                availablePileCount++;
            }
        }

        Log.d(TAG, "checkChargePriority availablePileCount=" + availablePileCount + " higherPriorityRobotCount=" + higherPriorityRobotCount);
        // 如果可用充电桩数量小于等于更高优先级机器人数量，进入等待状态
        if (availablePileCount <= higherPriorityRobotCount) {
            if (mStatus == Status.GO_CHARGE_POINT_FAILED) {
                // 没有可用充电桩，返回充电等待区
                onStateRunSuccess();
            } else {
                updateStatus(Status.WAIT);
            }
            return;
        }

        // 查找未被占用的充电桩
        Pose chargePilePose = findAvailableChargePile(robotStatusList);
        if (chargePilePose != null) {
            // 如果找到可用充电桩，且有足够的充电桩，直接去充电
            updateStatus(Status.GO_CHARGE_POINT);
            goChargePoint(chargePilePose);
        } else {
            if (mStatus == Status.GO_CHARGE_POINT_FAILED) {
                // 没有可用充电桩，返回充电等待区
                onStateRunSuccess();
            } else {
                updateStatus(Status.WAIT);
            }
        }
    }

    private Pose findAvailableChargePile(List<MultiRobotStatus> robotStatusList) {
        for (int i = 0; i < mChargePileList.size(); i++) {
            Pose pile = mChargePileList.get(i);
            // 检查充电桩是否已被占用或正在被前往
            if (mChargePileOccupiedList.contains(i)) {
                continue;
            }
            // 检查充电桩是否正在被使用
            if (!isPoseInUseCurrently(pile, robotStatusList)) {
                return pile;
            }
        }
        return null;
    }

    private void goChargePoint(Pose chargePilePose) {
        curChargePilePose = chargePilePose;
        Pose chargePointPose = mStateData.getChargePointForPile(chargePilePose);
        int index = mStateData.getChargePileIndex(chargePilePose);
        mChargePileOccupiedList.add(index);
        writeMultiRobotOccupiedPileIndex(index);
        if (chargePointPose == null) {
            Log.e(TAG, "goChargePoint chargePointPose is null");
            return;
        }
        startGoChargePoint(chargePointPose);
    }

    private void startGoChargePoint(Pose chargePointPose) {
        startNaviToPoseTimer();
        mChargeAdvancedAction.getApi().goPlace(mChargeAdvancedAction.getReqId(), chargePointPose.getName());
    }
}
