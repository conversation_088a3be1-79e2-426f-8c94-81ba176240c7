/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.core;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.core.RequestManager.ReqAction;
import com.ainirobot.coreservice.core.module.Module;
import com.ainirobot.coreservice.core.module.ModuleManager;
import com.ainirobot.coreservice.core.status.StatusManager;
import com.ainirobot.coreservice.service.CoreService;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Set;

public class RequestHandler extends Handler {
    private static final String TAG = "RequestHandler";

    private CoreService mCoreService;
    private Handler mCoreHandler;
    private RequestManager mRequestManager;

    public RequestHandler(Looper looper, CoreService coreService) {
        super(looper);

        mCoreService = coreService;
        mCoreHandler = coreService.getCoreHandler();
        mRequestManager = mCoreService.getRequestManager();
    }

    @Override
    public void handleMessage(Message msg) {
        switch (msg.what) {
            case InternalDef.MSG_REQUEST_WITH_TYPE:
            case InternalDef.MSG_REQUEST_WITH_TEXT:
                handleRequest(msg);
                break;
            case InternalDef.MSG_REQUEST_FINISH_PARSER:
                handleRequestFinished(msg);
                break;
            case InternalDef.MSG_REQUEST_HW_REPORT:
                handleHWReport(msg);
                break;
            case InternalDef.MSG_REQUEST_DEFAULT:
            default:
                break;
        }
    }

    private void handleRequest(Message msg) {
        Bundle bundle = msg.getData();
        int reqId = bundle.getInt(InternalDef.BUNDLE_REQUEST_ID);
        ReqAction reqAction = mRequestManager.getReqAction(reqId);
        if (reqAction == null) {
            Log.e(TAG, "handleRequest reqAction is null");
            return;
        }
        ModuleManager moduleManager = mCoreService.getModuleManager();
        Set<Module> modules = null;
        if(reqAction != null){
            modules = moduleManager.getActiveModules(reqAction.permission);
            Log.d(TAG, "handleRequest permission: " + (reqAction.permission == null ? "" : reqAction.permission.name()));
        }

        if (modules == null || modules.isEmpty()) {
            //If no module can parse successfully, return status with error to audio service
            Log.d(TAG, "Module is null");
            mRequestManager.removeReqAction(reqId);
            sendStatus(Definition.STATUS_REQUEST_FINISHED, String.valueOf(false));
            //mCoreService.getSpeechServer().notifyPlayText(SpeechText.ERROR_CANT_PARSE_TEXT);
            return;
        }

        Log.d(TAG, "Handle request : " + reqAction.reqType + "  text : " + reqAction.reqText);
        for (Module module : modules) {
            if (module.getCallback() != null) {
                try {
                    Log.d(TAG, "Handle request : "
                            + reqAction.reqType + "  module : " + module.getPackageName());
                    Module.HandleResult result = module.handleRequest(reqId,reqAction.reqType,
                            reqAction.reqText, reqAction.reqParam, reqAction.permission);
                    if(result == Module.HandleResult.INTERRUPT){
                        Log.i(TAG, "module result interrupt, break");
                        break;
                    }
                } catch (RemoteException e) {
                    e.printStackTrace();
                    //module.suspend();
                    sendStatus(Definition.STATUS_REQUEST_FINISHED, String.valueOf(false));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                Log.d(TAG, module.getPackageName() + " call back is null");
            }
        }
        Message returnMsg = mCoreHandler.obtainMessage(InternalDef.MSG_CORE_ADD_REQUEST_ACTION);
        mCoreHandler.sendMessage(returnMsg);
    }

    private void handleRequestFinished(Message msg) {
        Bundle bundle = msg.getData();
        int reqId = bundle.getInt(InternalDef.BUNDLE_INT);
        boolean result = bundle.getBoolean(InternalDef.BUNDLE_BOOLEAN);
        String response = bundle.getString(InternalDef.BUNDLE_RESPONSE);

        RequestManager requestManager = mCoreService.getRequestManager();
        if (!requestManager.isExits(reqId)) {
            Log.d(TAG, "handleRequestFinished reqId : " + reqId + " has't a ReqAction !");
            return;
        }

        JSONObject json = new JSONObject();
        try {
            json.put("reqId", reqId);
            json.put("result", result);
            if (!TextUtils.isEmpty(response)) {
                json.put("response", response);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        sendStatus(Definition.STATUS_REQUEST_FINISHED, json.toString());
        requestManager.removeReqAction(reqId);

        if (result) {
            //TODO: Request execute successful event handle
        } else {
            //TODO: Request execute failed event handle
        }
    }

    private void sendStatus(String type, String data) {
        StatusManager statusManager = mCoreService.getStatusManager();
        statusManager.handleStatus(type, data);
    }

    private void handleHWReport(Message msg) {
        Bundle bundle = msg.getData();
        Set<Module> modules = mCoreService.getModuleManager().getActiveModules(null);

        try {
            for (Module module : modules) {
                if (module.getCallback() != null) {
                    Log.d(TAG, "Handle report : " + bundle.getString(InternalDef.BUNDLE_TYPE)
                            + "   " + bundle.getString(InternalDef.BUNDLE_RESPONSE)
                            + "   " + module.getPackageName());
                    module.getCallback().onHWReport(
                            bundle.getInt(InternalDef.BUNDLE_INT),
                            bundle.getString(InternalDef.BUNDLE_TYPE),
                            bundle.getString(InternalDef.BUNDLE_RESPONSE)
                    );
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
