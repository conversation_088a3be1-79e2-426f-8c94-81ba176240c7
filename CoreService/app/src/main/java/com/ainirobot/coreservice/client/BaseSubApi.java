/*
 *   Copyright (C) 2017 OrionStar Technology Project
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.ainirobot.coreservice.client;

import android.content.Context;
import android.os.HandlerThread;

import com.ainirobot.coreservice.IRobotBinderPool;
import com.ainirobot.coreservice.client.messagedispatcher.MessageDispatcher;

public abstract class BaseSubApi {

    protected MessageDispatcher mMessageDispatcher = new MessageDispatcher();
    protected HandlerThread mHandlerThread;

    protected void startNewThread(String name) {
        if (null == mHandlerThread) {
            mHandlerThread = new HandlerThread(name);
            mHandlerThread.start();
        }
    }

    /**
     * 连接service后触发
     * @param robotBinderPool - binder池
     * @param context-service context
     */
    public abstract void onConnect(IRobotBinderPool robotBinderPool, Context context);

    /**
     * service断链后触发
     */
    public void onDisconnect() {
    }
}
