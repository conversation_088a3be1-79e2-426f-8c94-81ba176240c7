/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.data.provider;


import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.support.v4.content.CursorLoader;
import android.text.TextUtils;


public abstract class BaseDataHelper {
    private Context mContext;

    public BaseDataHelper(Context context) {
        mContext = context;
    }

    public Context getContext() {
        return mContext;
    }

    public abstract Uri getContentUri();

    public void notifyChange() {
        mContext.getContentResolver().notifyChange(getContentUri(), null);
    }

    public void notifyChange(String event) {
        if (TextUtils.isEmpty(event)) {
            this.notifyChange();
            return;
        }
        mContext.getContentResolver().notifyChange(Uri.withAppendedPath(getContentUri(), event), null);
    }

    protected final Cursor query(String[] projection, String selection, String[] selectionArgs,
                                 String sortOrder) {
        return mContext.getContentResolver().query(getContentUri(), projection, selection,
                selectionArgs, sortOrder);
    }

    public final Cursor query(String selection, String[] selectionArgs) {
        return query(null, selection, selectionArgs, null);
    }

    public final Uri insert(ContentValues values) {
        return mContext.getContentResolver().insert(getContentUri(), values);
    }

    public final int bulkInsert(ContentValues[] values) {
        try {
            return mContext.getContentResolver().bulkInsert(getContentUri(), values);
        } catch (Exception e) {
            return -1;
        }
    }

    public final int update(ContentValues values, String where, String[] whereArgs) {
        return mContext.getContentResolver().update(getContentUri(), values, where, whereArgs);
    }

    public final int delete(String selection, String[] selectionArgs) {
        return mContext.getContentResolver().delete(getContentUri(), selection, selectionArgs);
    }

    protected final Cursor getList(String[] projection, String selection, String[] selectionArgs,
                                   String sortOrder) {
        return mContext.getContentResolver().query(
                getContentUri(), projection, selection, selectionArgs, sortOrder);
    }

    public CursorLoader getCursorLoader() {
        return getCursorLoader(null, null, null, null);
    }

    public final CursorLoader getCursorLoader(String[] projection, String selection,
                                              String[] selectionArgs, String sortOrder) {
        return new CursorLoader(getContext(),
                getContentUri(), projection, selection, selectionArgs, sortOrder);
    }

}
