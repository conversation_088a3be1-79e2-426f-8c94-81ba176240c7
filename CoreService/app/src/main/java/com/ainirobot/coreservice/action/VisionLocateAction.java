package com.ainirobot.coreservice.action;

import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.ResetEstimateParams;
import com.ainirobot.coreservice.core.LocalApi;

import java.util.ArrayList;

public class VisionLocateAction extends AbstractAction<ResetEstimateParams> {
    private static final String TAG = VisionLocateAction.class.getSimpleName();
    private int mRetryCount = 0;

    protected VisionLocateAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_LOCATE_VISION, resList);
    }

    @Override
    protected boolean startAction(ResetEstimateParams parameter, LocalApi api) {
        mRetryCount = parameter.getRetryCount();
        mReqId = parameter.getReqId();

        mApi.locateVision(mReqId);
        return true;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        mRetryCount = 0;
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_LOCATE_VISION:
                handleVisionRelocateResult(result);
                break;
        }
        return true;
    }

    private void handleVisionRelocateResult(String result) {
        Log.d(TAG, "vision relocate result : " + result);
        if ("succeed".equals(result)) {
            onResult(Definition.RESULT_OK, result);
        } else if (mRetryCount >= 1) {
            mApi.locateVision(mReqId);
            mRetryCount--;
        } else {
            onResult(Definition.RESULT_FAILURE, result);
        }
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return null;
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return null;
    }

    @Override
    protected boolean verifyParam(ResetEstimateParams param) {
        return param.getRetryCount() >= 0;
    }
}
