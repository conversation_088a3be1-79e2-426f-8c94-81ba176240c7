package com.ainirobot.coreservice.bi.report;

import com.google.gson.JsonObject;

public class SystemManagerReporter extends SystemInformationReporter {
    public SystemManagerReporter() {
        super("SystemStatus", "SystemManager");
    }

    private void reportStatusUpdate(String methodNote, String params) {
        mReportEditor.reset()
            .addNodeMethodTypeApiCallback()
            .addNodeMethodName("onStatusUpdate")
            .addNodeParams(params)
            .addNodeNote(methodNote)
            .reportV();
    }

    public void reportSettingUpdate(final String type, final String param) {
        JsonObject params = new JsonObject();
        params.addProperty("type", type);
        params.addProperty("param", param);
        reportStatusUpdate("", params.toString());
    }

    public void reportEmergencyUpdate(final String type, final String data) {
        JsonObject params = new JsonObject();
        params.addProperty("type", type);
        params.addProperty("data", data);
        reportStatusUpdate("callback from : status_emergency", params.toString());
    }

    public void reportPoseEstimateUpdate(final String type, final String data) {
        JsonObject params = new JsonObject();
        params.addProperty("type", type);
        params.addProperty("data", data);
        reportStatusUpdate("callback from : status_pose_estimate", params.toString());
    }

    public void reportPoseNavigationUpdate(final String type, final String data) {
        JsonObject params = new JsonObject();
        params.addProperty("type", type);
        params.addProperty("data", data);
        reportStatusUpdate("callback from : status_navi_service_ok", params.toString());
    }

    public void reportPoseSwitchMapUpdate(final String type, final String data) {
        JsonObject params = new JsonObject();
        params.addProperty("type", type);
        params.addProperty("data", data);
        reportStatusUpdate("callback from : action_switch_map", params.toString());
    }

    public void reportHWStatusUpdate(final String type, final String data) {
        JsonObject params = new JsonObject();
        params.addProperty("type", type);
        params.addProperty("data", data);
        reportStatusUpdate("callback from : status_chassis_command | status_chassis_event",
            params.toString());
    }

    public void reportSystemStatusUpdate(String status, String param) {
        JsonObject params = new JsonObject();
        params.addProperty("status", status);
        params.addProperty("param", param);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("onSystemStatusUpdate")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportSystemRecovery(final String param) {
        JsonObject params = new JsonObject();
        params.addProperty("param", param);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("systemRecovery")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportSystemInterrupted(final String statusInfo) {
        JsonObject params = new JsonObject();
        params.addProperty("statusInfo", statusInfo);
        mReportEditor.reset()
            .addNodeMethodTypeApiCallback()
            .addNodeMethodName("onSystemStatusInterrupted")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportRequestOtaAuth(final boolean isForce, final String param) {
        JsonObject params = new JsonObject();
        params.addProperty("isForce", isForce);
        params.addProperty("param", param);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("requestOtaAuth")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportChargeStatusUpdate(final String param) {
        JsonObject params = new JsonObject();
        params.addProperty("param", param);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("onStartChargeStatusUpdate")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportChargeStatusUpdate(final String type, final String data) {
        JsonObject params = new JsonObject();
        params.addProperty("type", type);
        params.addProperty("data", data);
        reportStatusUpdate("callback from : status_set_charge_pile", params.toString());
    }

    public void reportChargeStatusInterrupted(final String currentStatus) {
        JsonObject params = new JsonObject();
        params.addProperty("currentStatus", currentStatus);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("onStartChargeStatusUpdate")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportOTAStatusUpdate(final String status) {
        JsonObject params = new JsonObject();
        params.addProperty("status", status);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("onOTAStatusUpdate")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportCheckOTAResult() {
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("startCheckOtaResult")
            .reportV();
    }

    public void reportCheckOTADowngradeResult() {
        mReportEditor.reset()
                .addNodeMethodTypeFunction()
                .addNodeMethodName("startCheckOtaDowngradeResult")
                .reportV();
    }

    public void reportInspectionUpdate(final String status) {
        JsonObject params = new JsonObject();
        params.addProperty("status", status);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("onInspectionUpdate")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportBatteryUpdate(final String batteryStatus, final int level) {
        JsonObject params = new JsonObject();
        params.addProperty("batteryStatus", batteryStatus);
        params.addProperty("level", level);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("updateBattery")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportUpdateStatus(final String statusInfo, final boolean isJoinQueue,
        final boolean result) {
        JsonObject params = new JsonObject();
        params.addProperty("statusInfo", statusInfo);
        params.addProperty("isJoinQueue", isJoinQueue);
        JsonObject results = new JsonObject();
        results.addProperty("result", result);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("updateStatus")
            .addNodeParams(params.toString())
            .addNodeResult(results.toString())
            .reportV();
    }

    public void reportRemoveSystemStatus(final String statusInfo) {
        JsonObject params = new JsonObject();
        params.addProperty("statusInfo", statusInfo);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("removeSystemStatus")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportUpdateCurrentStatus(final String statusInfo, final boolean result) {
        JsonObject params = new JsonObject();
        params.addProperty("statusInfo", statusInfo);
        JsonObject results = new JsonObject();
        results.addProperty("result", result);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("updateCurrentStatus")
            .addNodeParams(params.toString())
            .addNodeResult(results.toString())
            .reportV();
    }

    public void reportUpdateLightStatus(final String statusInfo) {
        JsonObject params = new JsonObject();
        params.addProperty("statusInfo", statusInfo);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("updateLightStatus")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportRemoveLightStatus(final String statusInfo) {
        JsonObject params = new JsonObject();
        params.addProperty("statusInfo", statusInfo);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("removeLightStatus")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportSetLight(final String type) {
        JsonObject params = new JsonObject();
        params.addProperty("type", type);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("setLight")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportSwitchSystemControl(final boolean isSwitchSystemControl) {
        JsonObject results = new JsonObject();
        results.addProperty("isSwitchSystemControl", isSwitchSystemControl);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("switchSystemControl")
            .addNodeResult(results.toString())
            .reportV();
    }

    public void reportSwitchAppControl(final boolean isSwitchAppControl) {
        JsonObject results = new JsonObject();
        results.addProperty("isSwitchAppControl", isSwitchAppControl);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("switchAppControl")
            .addNodeResult(results.toString())
            .reportV();
    }

    public void reportSetSystemStatusEnable(final String statusInfo, final boolean isEnabled) {
        JsonObject params = new JsonObject();
        params.addProperty("statusInfo", statusInfo);
        params.addProperty("isEnable", isEnabled);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("setSystemStatusEnabled")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportRecoverSystemStatus(final String systemStatus) {
        JsonObject params = new JsonObject();
        params.addProperty("systemStatus", systemStatus);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("setSystemStatusEnabled")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportResetSystemStatus() {
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("resetSystemStatus")
            .reportV();
    }

    public void reportCheckNavigationMapCallback(final int status, final String response) {
        JsonObject params = new JsonObject();
        params.addProperty("status", status);
        params.addProperty("response", response);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("onResult")
            .addNodeParams(params.toString())
            .addNodeNote("callback from : checkCurNaviMap(Definition.DEBUG_REQ_ID)")
            .reportV();
    }

    public void reportCheckChargePileCallback(final int status, final String response) {
        JsonObject params = new JsonObject();
        params.addProperty("status", status);
        params.addProperty("response", response);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("onResult")
            .addNodeParams(params.toString())
            .addNodeNote("callback from : getChargePile(Definition.DEBUG_REQ_ID)")
            .reportV();
    }

    public void reportSendRequest(final String type, final String param) {
        JsonObject params = new JsonObject();
        params.addProperty("type", type);
        params.addProperty("param", param);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("sendRequest")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportFinishRequest(final int reqId, final boolean result) {
        JsonObject params = new JsonObject();
        params.addProperty("reqId", reqId);
        params.addProperty("result", result);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("finishRequest")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportResetHWStatus() {
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("resetHWStatus")
            .reportV();
    }

    public void reportShowSettingUpdate(final String upgradeStr) {
        JsonObject params = new JsonObject();
        params.addProperty("upgradeStr", upgradeStr);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("showSettingUpdate")
            .addNodeParams(params.toString())
            .reportV();
    }

    public void reportOTALowBattery(final String upgradeStr) {
        JsonObject params = new JsonObject();
        params.addProperty("upgradeStr", upgradeStr);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("handleOtaLowBattery")
            .addNodeParams(params.toString())
            .reportV();
    }
}
