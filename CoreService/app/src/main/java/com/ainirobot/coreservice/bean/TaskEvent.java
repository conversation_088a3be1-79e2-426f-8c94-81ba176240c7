package com.ainirobot.coreservice.bean;

import android.os.Parcel;
import android.os.Parcelable;

import org.json.JSONException;
import org.json.JSONObject;

public class TaskEvent implements Parcelable {

    private String taskId;

    private String taskType;

    private String eventType;

    private String eventData;

    private String taskData;

    private String subTaskId;

    private String subTaskType;

    private String subTaskData;

    private int isCreateOnStart = 1;

    public TaskEvent(){
        super();
    }

    protected TaskEvent(Parcel in) {
        taskId = in.readString();
        taskType = in.readString();
        eventType = in.readString();
        eventData = in.readString();
        taskData = in.readString();
        subTaskId = in.readString();
        subTaskType = in.readString();
        subTaskData = in.readString();
        isCreateOnStart = in.readInt();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(taskId);
        dest.writeString(taskType);
        dest.writeString(eventType);
        dest.writeString(eventData);
        dest.writeString(taskData);
        dest.writeString(subTaskId);
        dest.writeString(subTaskType);
        dest.writeString(subTaskData);
        dest.writeInt(isCreateOnStart);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<TaskEvent> CREATOR = new Creator<TaskEvent>() {
        @Override
        public TaskEvent createFromParcel(Parcel in) {
            return new TaskEvent(in);
        }

        @Override
        public TaskEvent[] newArray(int size) {
            return new TaskEvent[size];
        }
    };

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String type) {
        this.eventType = type;
    }

    public String getTaskData() {
        return taskData;
    }

    public void setTaskData(String data) {
        this.taskData = data;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public String getEventData() {
        return eventData;
    }

    public void setEventData(String eventData) {
        this.eventData = eventData;
    }

    public String getSubTaskId() {
        return subTaskId;
    }

    public void setSubTaskId(String subTaskId) {
        this.subTaskId = subTaskId;
    }

    public String getSubTaskType() {
        return subTaskType;
    }

    public void setSubTaskType(String subTaskType) {
        this.subTaskType = subTaskType;
    }

    public String getSubTaskData() {
        return subTaskData;
    }

    public void setSubTaskData(String subTaskData) {
        this.subTaskData = subTaskData;
    }

    public int getIsCreateOnStart() {
        return isCreateOnStart;
    }

    public void setIsCreateOnStart(int isCreateOnStart) {
        this.isCreateOnStart = isCreateOnStart;
    }

    public String toJson(){
        JSONObject eventObj = new JSONObject();
        try {
            eventObj.put("task_id", taskId);
            eventObj.put("event_data", eventData);
            eventObj.put("event_type", eventType);
            eventObj.put("task_type", taskType);
            eventObj.put("task_data", taskData);
            eventObj.put("sub_task_id", subTaskId);
            eventObj.put("sub_task_type", subTaskType);
            eventObj.put("sub_task_data", subTaskData);
            eventObj.put("is_create_on_start", isCreateOnStart);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return eventObj.toString();
    }

    @Override
    public String toString() {
        return toJson();
    }
}
