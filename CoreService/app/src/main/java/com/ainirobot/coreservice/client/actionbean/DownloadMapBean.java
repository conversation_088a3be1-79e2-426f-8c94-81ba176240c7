package com.ainirobot.coreservice.client.actionbean;

public class DownloadMapBean extends BaseBean {
    private String mapName;
    private String mapUrl;
    private String mapUuid;
    private String mapMd5;
    private boolean overwriteLocalPlace;
    private String extraMd5;
    private String extraId;
    private String extraUrl;
    private String mapSupportType;
    private boolean isOldMap;

    /**
     * 是否导入地图，（小助手adb push地图包到机器端）
     */
    private boolean hasLocalMap;

    public String getMapName() {
        return mapName;
    }

    public String getMapUrl() {
        return mapUrl;
    }

    public String getMapUuid() {
        return mapUuid;
    }

    public String getMapMd5() {
        return mapMd5;
    }

    public boolean isOverwriteLocalPlace() {
        return overwriteLocalPlace;
    }

    public String getExtraMd5() {
        return extraMd5;
    }

    public String getExtraId() {
        return extraId;
    }

    public String getExtraUrl() {
        return extraUrl;
    }

    public boolean isOldMap() {
        return isOldMap;
    }

    public void setOldMap(boolean oldMap) {
        isOldMap = oldMap;
    }

    public String getMapSupportType() {
        return mapSupportType;
    }

    public boolean hasLocalMap() {
        return hasLocalMap;
    }
}
