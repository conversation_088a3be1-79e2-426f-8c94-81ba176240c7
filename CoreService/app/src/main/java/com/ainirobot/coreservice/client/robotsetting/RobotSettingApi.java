/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.client.robotsetting;

import android.content.Context;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.base.OrionBase;
import com.ainirobot.coreservice.IRobotBinderPool;
import com.ainirobot.coreservice.IRobotSettingApi;
import com.ainirobot.coreservice.client.BaseSubApi;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.messagedispatcher.RobotSettingDispatcher;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class RobotSettingApi extends BaseSubApi {
    public static final String TAG = RobotSettingApi.class.getSimpleName();

    private static RobotSettingApi instance;

    private IRobotSettingApi mApi;

    private RobotSettingApi() {
        startNewThread(TAG);
    }

    public static synchronized RobotSettingApi getInstance() {
        if (instance == null) {
            instance = new RobotSettingApi();
        }
        return instance;
    }

    @Override
    public void onConnect(IRobotBinderPool robotBinderPool, Context context) {
        Log.i(TAG, "on connect");
        try {
            mApi = IRobotSettingApi.Stub.asInterface(
                    robotBinderPool.queryBinder(Definition.BIND_SETTING, null));
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        // 这个时机不能改动，必须等到setting服务bind上之后，查询的env才是准确的。
        String zone = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_SETTINGS_CLOUD_SERVER_ZONE);
        OrionBase.setDomainEnv(ProductInfo.isOverSea(), zone,RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_SYSTEM_ENV) == 1);
    }

    /**
     * 判断对应平台的RobotConfig中是否有包含 key 的配置项
     * @param key
     * @return
     */
    public boolean containsRobotConfig(String key) {
        if (TextUtils.isEmpty(key) || null == mApi) {
            return false;
        }
        try {
            return mApi.containsRobotConfig(key);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 判断对应平台的RobotSetting中key对应的value值是否存在（包括GlobalSetting和DB）
     * @param key
     * @return
     */
    public boolean hasRobotSetting(String key) {
        if (TextUtils.isEmpty(key) || null == mApi) {
            return false;
        }
        try {
            return mApi.hasRobotSetting(key);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return false;
    }

    public int getRobotInt(String key) {
        String value = getRobotString(key);
        try {
            return Integer.valueOf(value);
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    public float getRobotFloat(String key) {
        String value = getRobotString(key);
        try {
            return Float.valueOf(value);
        } catch (NumberFormatException e) {
            e.printStackTrace();
            return 0;
        }
    }

    public String getRobotString(String key) {
        if (TextUtils.isEmpty(key) || null == mApi) {
            return "";
        }
        try {
            return mApi.getRobotSetting(key);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return "";
    }

    public void setRobotInt(String key, int value) {
        setRobotString(key, String.valueOf(value));
    }

    public void setRobotFloat(String key, float value) {
        setRobotString(key, String.valueOf(value));
    }

    public void setRobotString(String key, String value) {
        if (TextUtils.isEmpty(key) || null == mApi) {
            return;
        }
        try {
            mApi.setRobotSetting(key, value);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void registerRobotSettingListener(RobotSettingListener listener, String... key) {
        if (null == listener || key.length == 0 || null == mApi) {
            return;
        }
        List<String> keyList = new ArrayList<>(Arrays.asList(key));
        try {
            RobotSettingDispatcher dispatcher;
            if (null != listener.getDispatcher()) {
                dispatcher = listener.getDispatcher();
                for (String oldKey : listener.getKeyList()) {
                    if (!keyList.contains(oldKey)) {
                        keyList.add(oldKey);
                    }
                }
            } else {
                if (null == mHandlerThread) {
                    dispatcher = mMessageDispatcher.obtainRobotSettingDispatcher(listener);
                } else {
                    dispatcher = mMessageDispatcher.obtainRobotSettingDispatcher(
                            mHandlerThread.getLooper(), listener);
                }
            }
            listener.onRegistered(dispatcher, keyList);
            mApi.registerRobotSettingListener(keyList, dispatcher);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void unRegisterRobotSettingListener(RobotSettingListener listener) {
        if (null == listener || null == listener.getDispatcher() || mApi == null) {
            return;
        }
        try {
            mApi.unregisterRobotSettingListener(listener.getDispatcher());
            listener.onUnRegistered();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }
}
