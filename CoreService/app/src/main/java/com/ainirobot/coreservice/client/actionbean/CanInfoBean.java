/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.client.actionbean;

import com.google.gson.Gson;

/**
 * Created by Orion on 2018/5/14.
 */
public class CanInfoBean {

    private int link;
    private int motorHorizontal=-1; //0:init 1:normal 2:update 3:error
    private int motorVertical=-1;
    private int battery = -1 ;
    private int charge = -1; //is in charge
    private int powerPanel=-1; //0:init 1:normal 2:update 3:error
    private int chargingPanel=-1; //Whether it can be recharged automatically.
    private int scramStatus; //is pressed or not
    private int wheelLeft=-1;
    private int wheelRight=-1;
    private int motorZeroCheck = -1;//0 abnormal, 1 normal -1 uncheck

    public int getCameraInUse() {
        return cameraInUse;
    }

    public void setCameraInUse(int cameraInUse) {
        this.cameraInUse = cameraInUse;
    }

    public int getSensorStatus() {
        return sensorStatus;
    }

    public void setSensorStatus(int sensorStatus) {
        this.sensorStatus = sensorStatus;
    }

    private int cameraInUse = -1;
    private int sensorStatus = -1;

    public int getMotorZeroCheck() {
        return motorZeroCheck;
    }

    public void setMotorZeroCheck(int motorZeroCheck) {
        this.motorZeroCheck = motorZeroCheck;
    }

    public int getLink() {
        return link;
    }

    public void setLink(int link) {
        this.link = link;
    }

    public int getMotorHorizontal() {
        return motorHorizontal;
    }

    public void setMotorHorizontal(int motorHorizontal) {
        this.motorHorizontal = motorHorizontal;
    }

    public int getMotorVertical() {
        return motorVertical;
    }

    public void setMotorVertical(int motorVertical) {
        this.motorVertical = motorVertical;
    }

    public int getBattery() {
        return battery;
    }

    public void setBattery(int battery) {
        this.battery = battery;
    }

    public int getCharge() {
        return charge;
    }

    public void setCharge(int charge) {
        this.charge = charge;
    }

    public int getPowerPanel() {
        return powerPanel;
    }

    public void setPowerPanel(int powerPanel) {
        this.powerPanel = powerPanel;
    }

    public int getChargingPanel() {
        return chargingPanel;
    }

    public void setChargingPanel(int charginPanel) {
        this.chargingPanel = charginPanel;
    }

    public int getScramStatus() {
        return scramStatus;
    }

    public void setScramStatus(int scarmStatus) {
        this.scramStatus = scarmStatus;
    }

    public int getWheelLeft() {
        return wheelLeft;
    }

    public void setWheelLeft(int wheelLeft) {
        this.wheelLeft = wheelLeft;
    }

    public int getWheelRight() {
        return wheelRight;
    }

    public void setWheelRight(int wheelRight) {
        this.wheelRight = wheelRight;
    }

    @Override
    public String toString() {
        return "Can{" +
                "link=" + link +
                ", motorHorizontal=" + motorHorizontal +
                ", motorVertical=" + motorVertical +
                ", battery=" + battery +
                ", charge=" + charge +
                ", powerPanel=" + powerPanel +
                ", chargingPanel=" + chargingPanel +
                ", scramStatus=" + scramStatus +
                ", wheelLeft=" + wheelLeft +
                ", wheelRight=" + wheelRight +
                ", cameraInUse=" + cameraInUse +
                ", sensorStatus=" + sensorStatus +
                '}';
    }

    public String toJsonStr() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }

}
