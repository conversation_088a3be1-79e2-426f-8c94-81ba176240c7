package com.ainirobot.coreservice.client.log;

import android.content.Context;
import android.content.IntentFilter;
import android.os.Build;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.receiver.LogBroadcastReceiver;

import java.util.concurrent.ConcurrentHashMap;

public final class RLog {


    private static final int LEVEL_V = 1 << 0;
    private static final int LEVEL_I = 1 << 1;
    private static final int LEVEL_D = 1 << 2;

    private static ConcurrentHashMap<String, LogInfo> mPruneLog = new ConcurrentHashMap<>();
    private static int mLevel = LEVEL_I | LEVEL_D;
    private static int mLevelValue = 2;

    public static void initRLog(Context context){
        receiver(context);
    }

    private static void receiver(Context context){
        LogBroadcastReceiver receiver = new LogBroadcastReceiver();
        IntentFilter filter = new IntentFilter();
        filter.addAction(LogBroadcastReceiver.ACTION);
        context.registerReceiver(receiver, filter);
    }

    public synchronized static void pruneLog(String tag, String msg, long period) {
        if(TextUtils.isEmpty(tag)){
            return;
        }
        if (mPruneLog.containsKey(tag)){
            LogInfo log = mPruneLog.get(tag);
            long currentTime = SystemClock.elapsedRealtime();
            if(currentTime - log.getPreTime() > period){
                i(tag, msg + "   ++++++ expire count: "+log.getCount());
                log.setPreTime(currentTime);
                log.setCount(0);
            } else {
                log.setCount(log.getCount()+1);
            }
        }else {
            LogInfo log = new LogInfo();
            log.setCount(0);
            log.setPreTime(SystemClock.currentThreadTimeMillis());
            i(tag, msg + "   ++++++ expire count: "+log.getCount());
            mPruneLog.put(tag, log);
        }
    }

    public static boolean setLogLevel(int level){
        Log.i("RLog", " set log level: " + level);
        switch (level){
            case 1:
                mLevel = LEVEL_V | LEVEL_D | LEVEL_I;
                return true;

            case 2:
                mLevel = LEVEL_I | LEVEL_D;
                return true;

            case 3:
                mLevel = LEVEL_D;
                return true;

            default:
                return false;
        }
    }

    public static void v(String tag, String msg){
        if((mLevel & LEVEL_V) == LEVEL_V){
            Log.v(tag, msg);
        }
    }

    public static void i(String tag, String msg){
        if((mLevel & LEVEL_I) == LEVEL_I){
            Log.i(tag, msg);
        }
    }

    public static void d(String tag, String msg){
        if((mLevel & LEVEL_D) == LEVEL_D){
            Log.d(tag, msg);
        }
    }


    private static class LogInfo {
        private long preTime;
        private long count;

        public long getPreTime() {
            return preTime;
        }

        public void setPreTime(long preTime) {
            this.preTime = preTime;
        }

        public long getCount() {
            return count;
        }

        public void setCount(long count) {
            this.count = count;
        }
    }
}
