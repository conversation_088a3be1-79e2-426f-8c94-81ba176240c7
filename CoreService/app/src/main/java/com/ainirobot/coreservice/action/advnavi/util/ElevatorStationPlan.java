package com.ainirobot.coreservice.action.advnavi.util;

import android.util.Log;

import com.ainirobot.coreservice.action.advnavi.bean.ElevatorInfoBean;
import com.ainirobot.coreservice.bean.MultiFloorInfo;
import com.ainirobot.coreservice.client.Definition;

import java.util.ArrayList;
import java.util.List;

/**
 * Data: 2021/11/20 11:46 上午
 * Author: wanglijing
 * Description: ElevatorPathPlan
 * <p>
 * 乘梯换乘站点规划
 * 目前只支持换乘一次电梯
 */
public class ElevatorStationPlan {

    private static final String TAG = ElevatorStationPlan.class.getSimpleName();
    private static ElevatorStationPlan mInstance;
    private final String DEFAULT_STRING_VALUE = "default_string_value";
    public static final int DEFAULT_INT_VALUE = -1000;

    private int mStartFloor;
    private String mStartElevatorName;
    private int mTargetFloor;
    private ElevatorStationListener mListener;

    public static ElevatorStationPlan getInstance() {
        if (mInstance == null) {
            synchronized (ElevatorStationPlan.class) {
                if (mInstance == null) {
                    mInstance = new ElevatorStationPlan();
                }
            }
        }
        return mInstance;
    }

    /**
     * 用于第一次未设置电梯中心点名称时，获得当前应该用当前地图那台电梯
     *
     * @param targetFloor
     * @param listener
     */
    public void getCurrentMapElevator(int currentFloor,
                                      int targetFloor,
                                      List<MultiFloorInfo> multiFloorInfos,
                                      ElevatorStationListener listener) {
        clearData();
        this.mListener = listener;
        if (currentFloor == DEFAULT_INT_VALUE) {
            processResult("当前地图信息为空");
            return;
        }
        //由于是第一次，没有电梯中心名称时候的判断，这里的判断只需要考虑是不是同一楼层
        boolean isNeed = currentFloor != targetFloor;
        addElevatorStation(isNeed, currentFloor, targetFloor, multiFloorInfos, listener);
    }

    public void addElevatorStation(boolean mNeedTakeElevator,
                                   int startFloor,
                                   int targetFloor,
                                   List<MultiFloorInfo> multiFloorInfos,
                                   ElevatorStationListener listener) {
        clearData();
        this.mListener = listener;
        if (!mNeedTakeElevator) {
            onFinish("");
            return;
        }
        this.mStartFloor = startFloor;
        this.mTargetFloor = targetFloor;
        Log("addElevatorStation startFloor: " + mStartFloor);
        ElevatorPathPlaning(multiFloorInfos);
    }

    public void ElevatorPathPlaning(List<MultiFloorInfo> mapInfos) {
        if (isEmpty(mapInfos)) {
            processResult("地图点位为空");
            return;
        }
        Log(mapInfos.toString());

        //获得当前楼层及目标楼层点位信息
        List<String> currentFloorElevators = getElevatorList(mapInfos, mStartFloor);
        List<String> targetFloorElevators = getElevatorList(mapInfos, mTargetFloor);

        if (isEmpty(targetFloorElevators)) {
            processResult("目标楼层无电梯");
            return;
        }

        Log("currentFloorElevators: " + currentFloorElevators +
                "\ntargetFloorElevators: " + targetFloorElevators);
        if (useSameElevator(currentFloorElevators, targetFloorElevators)) {
            //到目的地不需要换乘电梯时，添加要乘电梯的信息
            addStation(new ElevatorInfoBean(
                    mStartElevatorName,
                    mStartFloor,
                    mTargetFloor));

            onFinish(mStartElevatorName);
        } else {
            //到目的地需要换乘电梯时的路径规划
            getOtherElevatorStation(mapInfos, currentFloorElevators, targetFloorElevators);
        }
        currentFloorElevators = null;
        targetFloorElevators = null;
    }

    /**
     * 根据地图id获得该地图所有点位名称
     *
     * @param mapInfos 所有楼层信息
     * @param floor    楼层
     * @return 该地图中所有电梯名称
     */
    private List<String> getElevatorList(List<MultiFloorInfo> mapInfos, int floor) {
        for (int i = 0; i < mapInfos.size(); i++) {
            MultiFloorInfo multifloorinfo = mapInfos.get(i);
            if (multifloorinfo.getFloorIndex() == floor) {
                return multifloorinfo.getAvailableElevators();
            }
        }
        return null;
    }

    private void getOtherElevatorStation(List<MultiFloorInfo> mapInfos, List<String> currentFloorElevators, List<String> targetFloorElevators) {
        List<String> temp = new ArrayList<>();
        for (MultiFloorInfo mapInfo : mapInfos) {
            List<String> elevatorNames = mapInfo.getAvailableElevators();
            List<String> startOverlap;
            List<String> targetOverLap;
            if ((startOverlap = isOverlap(temp, elevatorNames, currentFloorElevators)) != null &&
                    (targetOverLap = isOverlap(temp, elevatorNames, targetFloorElevators)) != null) {
                addStation(new ElevatorInfoBean(startOverlap.get(0), mStartFloor,
                        mapInfo.getFloorIndex()));
                addStation(new ElevatorInfoBean(targetOverLap.get(0), mapInfo.getFloorIndex(),
                        mTargetFloor));
                onFinish(startOverlap.get(0));
                return;
            }
        }

        processResult("未找到换乘楼层，请检查地图电梯相关点位");
    }

    private List<String> isOverlap(List<String> temp, List<String> check, List<String> checkList) {
        temp.clear();
        temp.addAll(check);
        temp.retainAll(checkList);
        return temp.size() > 0 ? new ArrayList<>(temp) : null;
    }

    /**
     * 目标楼层与起始楼层是否有同一台电梯
     *
     * @param currentMapPlaceList 起始楼层所有点位
     * @param targetMapPlaceList  目标楼层所有点位
     * @return true  有同一台电梯，mStartElevatorName，mTargetElevatorName赋相同的电梯名字
     * false 没有同一台电梯，mTargetElevatorName赋值为最后一次遍历得到的电梯名
     */
    private boolean useSameElevator(List<String> currentMapPlaceList, List<String> targetMapPlaceList) {
        String targetMapElevatorName = "";
        for (String poseName : targetMapPlaceList) {
            //找到目标楼层的电梯中心点位
            targetMapElevatorName = poseName;
            //当前楼层地图中，包含目的地楼层使用电梯的电梯中心点位
            if (currentMapPlaceList.contains(poseName)) {
                Log("useSameElevator: " + targetMapElevatorName);
                //不需要换乘电梯
                mStartElevatorName = targetMapElevatorName;
                return true;
            }
        }
        return false;
    }

    public void clearData() {
        mStartFloor = DEFAULT_INT_VALUE;
        mStartElevatorName = DEFAULT_STRING_VALUE;
        mTargetFloor = DEFAULT_INT_VALUE;
    }

    public interface ElevatorStationListener {
        void updateStation(ElevatorInfoBean elevator);

        void processResult(int result, String error);

        void onFinish(String elevatorName);
    }

    private void addStation(ElevatorInfoBean elevator) {
        Log("addStation: " + elevator.toString());
        if (null != mListener) {
            mListener.updateStation(elevator);
        }
    }

    private void processResult(String error) {
        Log("processResult: " + error);
        if (null != mListener) {
            mListener.processResult(Definition.ERROR_NOT_FOUND_ELEVATOR_PATH, error);
        }
    }

    private void onFinish(String elevatorName) {
        Log("onFinish elevatorName: " + elevatorName);
        if (null != mListener) {
            mListener.onFinish(elevatorName);
        }
    }

    private void Log(String msg) {
        Log.d(TAG, msg);
    }

    private boolean isEmpty(List<?> list) {
        return null == list || list.isEmpty();
    }
}
