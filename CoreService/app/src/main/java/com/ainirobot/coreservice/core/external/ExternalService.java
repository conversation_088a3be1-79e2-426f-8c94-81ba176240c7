/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.core.external;

import android.content.Context;
import android.content.Intent;
import android.os.ParcelFileDescriptor;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.IHWService;
import com.ainirobot.coreservice.IInspectCallBack;
import com.ainirobot.coreservice.bean.Command;
import com.ainirobot.coreservice.bi.report.ExternalServiceReporter;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.config.ServiceConfig;

import org.json.JSONArray;
import org.json.JSONException;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;

/**
 * 外设Service
 */
public class ExternalService {

    private final String TAG = "ExternalService";

    private String name;

    /**
     * Service包名
     */
    private String packageName;

    /**
     * Service注册的回调
     */
    private IHWService callback;

    /**
     * Service支持的指令集
     */
    private HashSet<String> supportCommands = new HashSet<>();

    /**
     * Service配置
     */
    private ServiceConfig config;

    /**
     * 是否配置中预置的Service
     */
    private boolean isPreset = false;

    private ExternalServiceReporter mReporter;

    public ExternalService(String packageName) {
        this(packageName, null);
    }

    public ExternalService(String packageName, String name) {
        this.packageName = packageName;
        this.name = name;
        mReporter = new ExternalServiceReporter();
    }

    /**
     * 启动外设Service
     */
    public void start() {
        String packageName = this.getPackageName();
        Log.d(TAG, "Start external service : " + packageName);

        mReporter.reportStart(packageName);
        Intent intent = new Intent(Definition.ACTION_EXTERNAL_SERVICE);
        intent.setPackage(packageName);
        Context context = ApplicationWrapper.getContext();
        try {
            String systemOSType = RobotSettings.getGlobalSettings(ApplicationWrapper.getContext(), Definition.ROBOT_SETTINGS_SYSTEM_OS_TYPE, "");
            Log.d(TAG, "start systemOSType=" + systemOSType);
            if (TextUtils.equals(systemOSType, Definition.OSType.AGENTOS.getValue())) {
                if (TextUtils.equals(packageName, Definition.SPEECH_PACKAGE_NAME))
                    return;
            } else {
                if (TextUtils.equals(packageName, Definition.AGENT_PACKAGE_NAME))
                    return;
            }
            Log.d(TAG, "Real Start external service : " + packageName);
            context.startService(intent);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public void setHardware(IHWService callback) {
        this.callback = callback;
    }

    public String getPackageName() {
        return packageName;
    }

    public String getName() {
        return name;
    }

    public IHWService getCallback() {
        return callback;
    }

    public boolean isPreset() {
        return isPreset;
    }

    public void setPreset(boolean preset) {
        isPreset = preset;
    }

    /**
     * Service是否可用
     *
     * @return true 可用, false 已被禁用
     */
    public boolean isEnable() {
        return config == null || config.isEnable();
    }

    /**
     * Service是否已连接
     *
     * @return
     */
    public boolean isConnected() {
        return callback != null && callback.asBinder().pingBinder();
    }

    /**
     * 是否需要保活
     *
     * @return
     */
    public boolean isKeepAlive() {
        return true;
    }

    public boolean isInspection() {
        if (config == null) {
            return false;
        }
        return config.isEnable() && config.isInspection();
    }

    public void addCommand(List<Command> commands) {
        mReporter.reportAddCommand(Objects.toString(commands));
        supportCommands.clear();
        for (Command command : commands) {
            supportCommands.add(command.getType());
        }
    }

    public void addCommand(String commands) {
        mReporter.reportAddCommand(Objects.toString(commands));
        try {
            supportCommands.clear();
            JSONArray cmdArray = new JSONArray(commands);
            for (int i = 0; i < cmdArray.length(); i++) {
                String cmd = cmdArray.optString(i);
                if (cmd != null) {
                    supportCommands.add(cmd);
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public boolean isSupport(String cmdType) {
        return supportCommands.contains(cmdType);
    }

    public void setConfig(ServiceConfig config) {
        mReporter.reportSetConfig(Objects.toString(config));
        this.config = config;
    }

    public ServiceConfig getConfig() {
        return this.config;
    }

    public int getStatus() throws RemoteException {
        if (callback == null || !callback.asBinder().pingBinder()) {
            return Definition.HW_STATUS_NONE;
        }

        return Definition.HW_STATUS_IDLE;
    }

    public boolean startStatusSocket(String type, int socketPort) throws RemoteException {
        boolean hasStart;
        if (callback != null) {
            hasStart = callback.startStatusSocket(type, socketPort);
        } else {
            Log.d(TAG, "Start status socket failed, type : " + type + " , service : " + this.getPackageName());
            hasStart = false;
        }
        mReporter.reportStartStatusSocket(type, socketPort, hasStart);
        return hasStart;
    }

    public boolean closeStatusSocket(String type, int socketPort) throws RemoteException {
        boolean hasClose;
        if (callback != null) {
            hasClose = callback.closeStatusSocket(type, socketPort);
        } else {
            Log.d(TAG, "Close status socket failed, type : " + type + " , service : " + this.getPackageName());
            hasClose = false;
        }
        mReporter.reportCloseStatusSocket(type, socketPort, hasClose);
        return hasClose;
    }

    public String exeSyncCommand(String cmdType, String params, String language) throws RemoteException {
        if (callback != null) {
            callback.exeSyncCommand(cmdType, params, language);
        }
        return null;
    }

    public boolean exeAsyncCommand(String cmdType, String params, String language) throws RemoteException {
        if (callback != null) {
            callback.exeAsyncCommand(cmdType, params, language);
        }
        return false;
    }

    public void reset() {
        mReporter.reportReset();
        try {
            if (callback != null) {
                callback.reset();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void switchAppControl(String packageName, String lastPackageName) {
        try {
            if (callback != null) {
                callback.switchAppControl(packageName, lastPackageName);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void setLanguage(String language) {
        try {
            if (callback != null) {
                callback.setLanguage(language);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public boolean startInspect(IInspectCallBack callBack) {
        try {
            if (callback != null) {
                callback.startInspect(callBack);
                return true;
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return false;
    }

    public ParcelFileDescriptor getParcelFileDescriptor(String type, String params){
        try {
            if (callback != null) {
                return callback.getParcelFileDescriptor(type, params);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return null;
    }

    public boolean setParcelFileDescriptor(String type, ParcelFileDescriptor pfd, String params){
        try {
            if (callback != null) {
                callback.setParcelFileDescriptor(type, pfd, params);
                return true;
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return false;
    }

    public void releaseParcelFileDescriptor(String type){
        try {
            if (callback != null) {
                callback.releaseParcelFileDescriptor(type);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof ExternalService)) {
            return false;
        }

        ExternalService service = (ExternalService) obj;
        return TextUtils.equals(this.packageName, service.getPackageName())
                && TextUtils.equals(this.name, service.getName());
    }

    @Override
    public int hashCode() {
        int hashCode = 1;
        hashCode = 31 * hashCode + (packageName == null ? -1 : packageName.hashCode());
        hashCode = 31 * hashCode + (name == null ? -1 : name.hashCode());
        return hashCode;
    }

    @Override
    public String toString() {
        return "ExternalService[name:"
                + name
                + ", packageName : "
                + packageName
                + ", config : "
                + config
                + ", isEnable: "
                + isEnable()
                + ", isConnected: "
                + isConnected()
                + ", isInspection: "
                + isInspection()
                + "]";
    }
}
