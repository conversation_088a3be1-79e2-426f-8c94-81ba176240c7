package com.ainirobot.coreservice.bi.server;

import android.content.Context;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.utils.DelayTask;

/**
 * Created by Orion on 2020/5/14.
 */
public class BiLevelStrategy {
    private static final String TAG = "BiLevelStrategy";

    private static final Object TAG_REPORT = new Object();
    private static final String URI = "content://com.ainirobot.coreservice.robotsettingprovider/setting";
    private static volatile int mLevelNormal = Definition.BI_LEVEL_VERBOSE;
    private static volatile int mLevelCallChain = Definition.BI_LEVEL_VERBOSE;
    private static Context mContext;

    public static void initLevel(Context context) {
        Log.i(TAG, "initLevel");
        mContext = context;

        updateNormalLevel();
        updateCallChainLevel();
        registerLevelChangeListener();
    }

    private static void updateNormalLevel() {
        String levelNormal = Bi.getRobotString(mContext, Definition.ROBOT_SETTING_BI_LEVEL_NORMAL);
        Log.i(TAG, "updateNormalLevel levelNormal=" + levelNormal);
        if (!TextUtils.isEmpty(levelNormal)) {
            int level = Integer.valueOf(levelNormal);
            if (level < 0) {
                return;
            }
            mLevelNormal = level;
        }
    }

    private static void updateCallChainLevel() {
        String levelCallChain = Bi.getRobotString(mContext,
                Definition.ROBOT_SETTING_BI_LEVEL_CALL_CHAIN);
        Log.i(TAG, "updateCallChainLevel levelCallChain=" + levelCallChain);
        if (!TextUtils.isEmpty(levelCallChain)) {
            int level = Integer.valueOf(levelCallChain);
            if (level < 0) {
                return;
            }
            mLevelCallChain = level;
            sendLevelToServer();
        }
    }

    /**
     * 是否中止上报
     *
     * @param type  类型
     * @param level 上报级别
     * @return true 终止上报，false 允许上报
     */
    public static boolean isAbortReport(String type, int level) {
        return level < getTypeLevel(type);
    }

    private static int getTypeLevel(String type) {
        int level = Definition.BI_LEVEL_NONE;
        switch (type) {
            case Definition.BI_TYPE_NORMAL:
                level = mLevelNormal;
                break;
            case Definition.BI_TYPE_CALL_CHAIN:
                level = mLevelCallChain;
                break;
            default:
                break;
        }
        return level;
    }

    private static void registerLevelChangeListener() {
        Handler handler = new Handler(Looper.getMainLooper());
        registerSettingsObserver(Definition.ROBOT_SETTING_BI_LEVEL_NORMAL,
                new ContentObserver(handler) {
                    @Override
                    public void onChange(boolean selfChange) {
                        updateNormalLevel();
                    }
                });
        registerSettingsObserver(Definition.ROBOT_SETTING_BI_LEVEL_CALL_CHAIN,
                new ContentObserver(handler) {
                    @Override
                    public void onChange(boolean selfChange) {
                        updateCallChainLevel();
                    }
                });
    }

    private static void registerSettingsObserver(String key, ContentObserver observer) {
        Uri uri = Uri.withAppendedPath(Uri.parse(URI), key);
        mContext.getContentResolver().registerContentObserver(uri, false,
                observer);
    }

    /**
     * 上报到后台
     * 业务埋点级别默认为详细0，不可修改；
     * 调用链埋点级别可设置；
     */
    private static void sendLevelToServer() {
        Log.i(TAG, "sendLevelToServer mLevelCallChain=" + mLevelCallChain);
        if (!SystemApi.getInstance().isApiConnectedService()) {
            Log.d(TAG, "sendLevelToServer, The core service is disconnected");
            retryDelay();
            return;
        }
        SystemApi.getInstance().reportMiniTrackLevel(0, mLevelCallChain,
                new CommandListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.i(TAG, "sendLevelToServer result=" + result
                                + ", message=" + message);
                        if (result == 1 && Definition.SUCCEED.equals(message)) {
                            DelayTask.cancel(TAG_REPORT);
                        } else {
                            retryDelay();
                        }
                    }

                    @Override
                    public void onError(int errorCode, String errorString) {
                        Log.i(TAG, "sendLevelToServer errorCode=" + errorCode
                                + ", errorString=" + errorString);
                        retryDelay();
                    }
                });
    }

    private static void retryDelay() {
        Log.i(TAG, "retryDelay");
        DelayTask.cancel(TAG_REPORT);
        DelayTask.submit(TAG_REPORT, new Runnable() {
            @Override
            public void run() {
                sendLevelToServer();
            }
        }, 60 * 1000);
    }

}
