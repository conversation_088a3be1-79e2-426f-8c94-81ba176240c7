/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.client.input;

import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.RemoteException;

import com.ainirobot.coreservice.IOtaRegistry;
import com.ainirobot.coreservice.IRobotBinderPool;
import com.ainirobot.coreservice.client.BaseApi;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.IntentUtil;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.messagedispatcher.StatusDispatcher;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.config.ServiceConfig;

import java.util.ArrayList;
import java.util.List;

public class OtaApi extends BaseApi {

    private IOtaRegistry mOtaRegistry;

    private static OtaApi mOtaApi = new OtaApi();

    private ServiceConnection apiConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName className, IBinder service) {
            mRobotBinderPool = IRobotBinderPool.Stub.asInterface(service);
            try {
                mOtaRegistry = IOtaRegistry.Stub.asInterface(
                        mRobotBinderPool.queryBinder(Definition.BIND_OTA, ctx.getPackageName()));
                notifyEventApiConnected();
            } catch (RemoteException | NullPointerException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName className) {
            notifyEventApiDisconnected();
            mOtaRegistry = null;
        }
    };

    private OtaApi() {
        super();
        mSubApiList.add(RobotSettingApi.getInstance());
    }

    public static OtaApi getInstance() {
        if (mOtaApi == null) {
            mOtaApi = new OtaApi();
        }
        return mOtaApi;
    }

    public void connectApi(Context context) {
        this.ctx = context;
        Intent intent = IntentUtil.createExplicitIntent(Definition.CORE_SERVICE_NAME,
                IRobotBinderPool.class.getName());
        context.bindService(intent, apiConnection, Service.BIND_AUTO_CREATE);
    }

    public void disconnectApi() {
        try {
            ctx.unbindService(apiConnection);
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
        }
    }

    public void registerCallBack(OtaCallback callback) {
        try {
            mOtaRegistry.registerCallback(callback);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public String registerStatusListener(String type, StatusListener listener) {
        try {
            if (listener == null) {
                return null;
            }
            StatusDispatcher statusListener = mMessageDispatcher.obtainStatusDispatcher(listener);

            String id = mOtaRegistry.registerStatusListener(type, statusListener);
            statusListener.register(id);
            return id;
        } catch (RemoteException e) {
            e.printStackTrace();
            return null;
        }
    }

    public boolean unregisterStatusListener(StatusListener listener) {
        try {
            if (listener == null) {
                return false;
            }
            mMessageDispatcher.unregisterStatusDispatcher(listener.getId());
            return mOtaRegistry.unregisterStatusListener(listener.getId());
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        }
    }

    public int sendOtaEvent(boolean isForceUpdate, String params) {
        try {
            return mOtaRegistry.sendOtaRequest(isForceUpdate ?
                    Definition.REQ_OTA_UPGRADE_FORCE : Definition.REQ_OTA_UPGRADE, params);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    public int sendDowngradeOtaEvent(String params) {
        try {
            return mOtaRegistry.sendOtaRequest(Definition.REQ_OTA_DOWNGRADE, params);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    public int sendOtaDownloadSuccess(String params) {
        try {
            return mOtaRegistry.sendOtaRequest(Definition.REQ_OTA_DOWNLOAD_SUCCESS, params);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    public int finishOtaUpgrade(String params) {
        try {
            return mOtaRegistry.sendOtaRequest(Definition.REQ_OTA_FINISH, params);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    public void updateProgress(String params) {
        try {
            mOtaRegistry.updateProgress(params);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public int startDownloadFullPack(String params) {
        try {
            return mOtaRegistry.sendOtaRequest(Definition.REQ_OTA_DOWNLOAD_FULL_PAC, params);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    public int getNavigationVersion() {
        try {
            return mOtaRegistry.sendOtaCommand(Definition.CMD_NAVI_GET_VERSION, null, false);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    public int startNavigationUpdate(String params) {
        try {
            return mOtaRegistry.sendOtaCommand(Definition.CMD_NAVI_START_UPDATE, params, false);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    /**
     * @return response params is like
     * {"freeSize":65536, "totalSize":100000}
     */
    public int getNavigationUpdateParams() {
        try {
            return mOtaRegistry.sendOtaCommand(Definition.CMD_NAVI_GET_UPDATE_PARAMS, null, false);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    public int getHeadVersion() {
        try {
            return mOtaRegistry.sendOtaCommand(Definition.CMD_HEAD_GET_VERSION, null, false);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    public int startHeadScp(String params) {
        try {
            return mOtaRegistry.sendOtaCommand(Definition.CMD_HEAD_START_SCP, params, false);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    public int getOtaHeadVersion() {
        try {
            return mOtaRegistry.sendOtaCommand(Definition.CMD_HEAD_OTA_GET_VERSION, null, false);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    public int startHeadUpdate(String params) {
        try {
            return mOtaRegistry.sendOtaCommand(Definition.CMD_HEAD_START_UPDATE, params, true);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    /**
     * @return response params is like
     * {"path":"/home/<USER>/OTA”, "freeSize":65536, "totalSize":100000 }
     */
    public int getHeadUpdateParams() {
        try {
            return mOtaRegistry.sendOtaCommand(Definition.CMD_HEAD_GET_UPDATE_PARAMS, null, false);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    public int startCanUpdate(String params) {
        try {
            return mOtaRegistry.sendOtaCommand(Definition.CMD_CAN_OTA_START, params, false);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    public int getCanOtaState() {
        try {
            return mOtaRegistry.sendOtaCommand(Definition.CMD_CAN_OTA_GET_STATE, null, true);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    public int getCanBoardVersion(String params) {
        try {
            return mOtaRegistry.sendOtaCommand(Definition.CMD_CAN_GET_BOARD_VERSION, params, false);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    public int getCanMotorHorizontalVersion() {
        try {
            return mOtaRegistry.sendOtaCommand(Definition.CMD_CAN_GET_MOTOR_H_VERSION, null, false);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    public int getCanMotorVerticalVersion() {
        try {
            return mOtaRegistry.sendOtaCommand(Definition.CMD_CAN_GET_MOTOR_V_VERSION, null, false);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    public int getCanPsbVersion() {
        try {
            return mOtaRegistry.sendOtaCommand(Definition.CMD_CAN_GET_PSB_VERSION, null, false);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    public int getCanPsbSVersion() {
        try {
            return mOtaRegistry.sendOtaCommand(Definition.CMD_CAN_GET_PSB_S_VERSION, null, false);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    public int getCanAutoChargeVersion() {
        try {
            return mOtaRegistry.sendOtaCommand(Definition.CMD_CAN_GET_AUTO_CHARGE_VERSION, null, false);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    public int getCanBatteryVersion() {
        try {
            return mOtaRegistry.sendOtaCommand(Definition.CMD_CAN_GET_BATTERY_VERSION, null, false);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    public int isHeadConnected() {
        try {
            return mOtaRegistry.sendOtaCommand(Definition.CMD_HEAD_IS_HEADER_CONNECTED, null, false);
        } catch (RemoteException e) {
            e.printStackTrace();
            return Definition.CMD_SEND_ERROR_UNKNOWN;
        }
    }

    public List<ServiceConfig> getConfig() {
        try {
            return mOtaRegistry.getConfig();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        return new ArrayList<>();
    }
}
