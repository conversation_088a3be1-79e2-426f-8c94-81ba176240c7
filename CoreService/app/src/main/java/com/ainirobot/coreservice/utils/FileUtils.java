package com.ainirobot.coreservice.utils;

import static com.ainirobot.coreservice.utils.Utils.getFileContent;

import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.Properties;

/**
 * Created by Orion on 2020/5/21.
 */
public class FileUtils {
    private static final String TAG = "FileUtils:Core";

    private static final Properties sProperties = ProductInfo.getDeviceProperties();
    private static final String LXC_VERSION_FILE_PATH = "/data/lxc/rootfs/etc/VERSION";

    public static boolean isEnableVisionRecordForTest() {
        String path = Environment.getExternalStorageDirectory() + File.separator + "enableRecord.json";
        File file = new File(path);
        if (file.isFile() && file.exists()) {
            boolean enable = false;
            String string = loadConfig(file);
            Log.d(TAG, "isEnableVisionRecordForTest string=" + string);
            try {
                JSONObject jsonObject = new JSONObject(string);
                enable = jsonObject.optBoolean("enable", false);
                Log.d(TAG, "isEnableVisionRecordForTest enable=" + enable);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            return enable;
        }
        return false;
    }

    private static String loadConfig(File file) {
        InputStream is = null;
        try {
            is = new FileInputStream(file);
            int total = is.available();
            byte[] bytes = new byte[total];
            int len = is.read(bytes);
            if (total == len) {
                return new String(bytes);
            } else {
                Log.e(TAG, "config read err");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            IOUtils.close(is);
        }
        return "";
    }

    public static boolean isFileExists(String path, String name) {
        File file = new File(path, name);
        if (file.isFile() && file.exists()) {
            return true;
        }
        return false;
    }

    public static boolean isFileExists(String fullPath) {
        File file = new File(fullPath);
        if (file.isFile() && file.exists()) {
            return true;
        }
        return false;
    }

    public static boolean isFolderExists(String strFolder) {
        File file = new File(strFolder);
        if (!file.exists()) {
            if (file.mkdirs()) {
                return true;
            } else {
                return false;
            }
        }
        return true;
    }

    public static String getVisionSnapshotCachePath(String cacheId){
        String path = Environment.getExternalStorageDirectory() + File.separator
                + Definition.LOGS_DIR_VISION_SNAPSHOT_CACHE + cacheId + File.separator;
        return path;
    }

    //Mini TopIr+Esp32版本
    public static boolean hasTopIR() {
        return sProperties != null && "1".equals(sProperties.getProperty("TOPIR_mini2", "0"));
    }

    //Mini Esp32版本
    public static boolean hasEsp32() {
        return sProperties != null && "1".equals(sProperties.getProperty("esp32", "0"));
    }

    //招财自动回充版本
    public static boolean hasChargeIR() {
        return sProperties != null && ("1".equals(sProperties.getProperty("CHGIR", "0"))
        || !TextUtils.isEmpty(sProperties.getProperty("BackCamera")));
    }

    //招财肇观版本
    public static boolean hasRgbdFm1() {
        return sProperties != null && "1".equals(sProperties.getProperty("RGBD_FM1", "0"));
    }

    //有梯控的版本
    public static boolean hasElevator() {
        return sProperties != null && !"0".equals(sProperties.getProperty("elevator_type", "0"));
    }

    //Pro是否含有取餐电动门
    public static boolean hasElectricDoor(){
        if(sProperties == null){
            return false;
        }
        String autodoor_4 = sProperties.getProperty("autodoor_4", "0");
        String AUTODOOR_4 = sProperties.getProperty("AUTODOOR_4", "0");
        Log.d(TAG,"hasElectricDoor autodoor_4:"+autodoor_4+",large case AUTODOOR_4:"+AUTODOOR_4);
        return "1".equals(autodoor_4) || "1".equals(AUTODOOR_4);
    }

    //Pro是否支持线激光摄像头（二、三层）
    public static boolean hasKKCamera(){
        if(sProperties == null){
            return false;
        }
        String smartCam = sProperties.getProperty(Definition.SMARTCAM_KEY, "");
        Log.d(TAG,"hasKKCamera smartCam:"+smartCam);
        return Definition.SMARTCAM_VALUE.equalsIgnoreCase(smartCam);
    }

    //Carry是否支持线激光摄像头（一层）
    public static boolean hasHeightLimitCamera(){
        if(sProperties == null){
            return false;
        }
        String smartCam = sProperties.getProperty(Definition.HEIGHTCAM_KEY, "");
        Log.d(TAG,"hasHeightLimitCamera smartCam:"+smartCam);
        return Definition.SMARTCAM_HIGHTLIMIT_VALUE.equalsIgnoreCase(smartCam);
    }

    //是否150公斤负重电机
    public static boolean isD150() {
        if(sProperties == null){
            return false;
        }
        String motors = sProperties.getProperty(Definition.MOTORS_KEY, "");
        Log.d(TAG,"isD150 motors:"+motors);
        return Definition.MOTORS_D150.equalsIgnoreCase(motors);
    }

    /**
     * 获取底盘LXC版本号
     */
    public static String getLxcVersion() {
        String version = "";
        File file = new File(LXC_VERSION_FILE_PATH);
        if (file.exists()) {
            version = getFileContent(LXC_VERSION_FILE_PATH);
        }
        Log.d(TAG, "getLXCVersion: " + version);
        return version;
    }
}