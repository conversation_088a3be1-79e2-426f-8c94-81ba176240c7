/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.action;

import java.util.Arrays;

public class ActionInfo {

    private String actionName;
    private int actionId;
    private boolean isCommon;
    private boolean isSystem;
    private String[] resource;

    public String getActionName() {
        return actionName;
    }

    public void setActionName(String actionName) {
        this.actionName = actionName;
    }

    public int getActionId() {
        return actionId;
    }

    public void setActionId(int actionId) {
        this.actionId = actionId;
    }

    public boolean isCommon() {
        return isCommon;
    }

    public void setCommon(boolean common) {
        isCommon = common;
    }

    public boolean isSystem() {
        return isSystem;
    }

    public void setSystem(boolean status) {
        isSystem = status;
    }

    public String[] getResource() {
        return resource;
    }

    public void setResource(String[] resource) {
        this.resource = resource;
    }

    @Override public String toString() {
        return "ActionInfo{"
            + "actionName='"
            + actionName
            + '\''
            + ", actionId="
            + actionId
            + ", isCommon="
            + isCommon
            + ", isSystem="
            + isSystem
            + ", resource="
            + Arrays.toString(resource)
            + '}';
    }
}

