package com.ainirobot.coreservice.surfaceshare;

import android.annotation.SuppressLint;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.IBinder;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;
import android.view.Surface;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.IntentUtil;
import com.ainirobot.coreservice.client.surfaceshare.SurfaceShareBean;
import com.ainirobot.coreservice.client.surfaceshare.SurfaceShareError;
import com.ainirobot.coreservice.client.surfaceshare.SurfaceShareStatus;
import com.ainirobot.coreservice.listener.ISurfaceShareListener;
import com.ainirobot.coreservice.service.CoreService;
import com.google.gson.Gson;

public class SurfaceShareManager {

    public static final String TAG = SurfaceShareManager.class.getSimpleName();

    private static final int MSG_REQUEST_IMAGE_FRAME = 1;
    private static final int MSG_ABANDON_IMAGE_FRAME = 2;
    private static final int MSG_BIND_SERVER_TIMEOUT = 3;
    private static final int MSG_SERVER_EXIT = 4;

    private static final String URI = "content://com.ainirobot.coreservice.robotsettingprovider/setting";
    private static final String KEY_ID = "id";
    private static final String KEY_VALUE = "value";

    private static final long TIMEOUT_BIND_SERVER = 2000;

    private Context mContext;
    private ServiceConnection conn = null;
    private ISurfaceShareCommand mCommand;
    private SurfaceShareState mState = SurfaceShareState.idle;
    private SurfaceShareContent mContent;
    private SurfaceShareHandler mHandler;
    private ISurfaceShareCallback mCallback;
    private String mSurfaceSharePackageName;
    private String mSurfaceShareServiceName;
    private String mSurfaceShareActionName;
    private Gson mGson;

    private SurfaceShareManager() {
        HandlerThread thread = new HandlerThread("SurfaceShare");
        thread.start();
        mHandler = new SurfaceShareHandler(this, thread.getLooper());
        mGson = new Gson();
    }

    private enum SurfaceShareState {
        idle, used, wait
    }

    public static SurfaceShareManager getInstance() {
        return SingletonHolder.INSTANCE;
    }

    private static class SingletonHolder {
        @SuppressLint("StaticFieldLeak")
        private static final SurfaceShareManager INSTANCE = new SurfaceShareManager();
    }

    static class SurfaceShareHandler extends Handler {

        private SurfaceShareManager mManager;

        SurfaceShareHandler(SurfaceShareManager manager, Looper looper) {
            super(looper);
            mManager = manager;
        }

        @Override
        public void handleMessage(Message msg) {
            Log.d(TAG, "handleMessage msg: " + msg.what + ", mManager: " + mManager);
            if (mManager == null) {
                return;
            }
            Log.d(TAG, "handleMessage command: " + mManager.mCommand + ", state: " + mManager.mState
                    + ", message: " + msg.obj);
            switch (msg.what) {
                case MSG_REQUEST_IMAGE_FRAME:
                    SurfaceShareContent content = (SurfaceShareContent) msg.obj;
                    if (mManager.mState != SurfaceShareState.idle) {
                        if (content.getBean().getPriority()
                                > mManager.mContent.getBean().getPriority()) {
                            mManager.processResult(SurfaceShareError.ERROR_SURFACE_SHARE_PREEMPTED,
                                    mManager.mGson.toJson(mManager.mContent.getBean()));
                            //                        } else if (content.getBean().getPriority()
                            //                                == mManager.mContent.getBean().getPriority()) {
                        } else {
                            mManager.processResult(content, SurfaceShareError.ERROR_SURFACE_SHARE_USED,
                                    mManager.mGson.toJson(mManager.mContent.getBean()));
                            return;
                        }
                    }
                    mManager.mContent = content;
                    if (SurfaceShareState.idle == mManager.mState) {
                        if (mManager.mCommand == null) {
                            mManager.updateSurfaceShareState(SurfaceShareState.wait);
                            mManager.bindSurfaceShareServerService(mManager.mContext);
                            sendEmptyMessageDelayed(MSG_BIND_SERVER_TIMEOUT, TIMEOUT_BIND_SERVER);
                        } else {
                            mManager.updateSurfaceShareState(SurfaceShareState.used);
                            try {
                                boolean isSuccess ;
                                SurfaceShareBean bean = mManager.mContent.getBean();
                                if (bean != null && bean.getIsUseToPreview()){
                                    Log.d(TAG, "handleMessage only showSurface ");
                                    isSuccess = mManager.mCommand.showSurface(content.getSurface(),true);
                                }else {
                                    isSuccess = mManager.mCommand.setStreamSurface(content.getSurface());
                                }
                                Log.d(TAG, "MSG_REQUEST_IMAGE_FRAME setStreamSurface: "
                                        + isSuccess);
                                if (!isSuccess) {
                                    mManager.processResult(SurfaceShareError
                                            .ERROR_SET_STREAM_SURFACE_FAILED, "");
                                } else {
                                    mManager.processStatus(SurfaceShareStatus.STATUS_SET_STREAM_SUCCESS, "");
                                }
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }
                        }
                    } else if (SurfaceShareState.used == mManager.mState) {
                        try {
                            boolean isSuccess ;
                            SurfaceShareBean bean = mManager.mContent.getBean();
                            if (bean != null && bean.getIsUseToPreview()){
                                Log.d(TAG, "handleMessage only showSurface ");
                                isSuccess = mManager.mCommand.showSurface(content.getSurface(),true);
                            }else {
                                isSuccess = mManager.mCommand.setStreamSurface(content.getSurface());
                            }
                            Log.d(TAG, "MSG_REQUEST_IMAGE_FRAME setStreamSurface: "
                                    + isSuccess);
                            if (!isSuccess) {
                                mManager.processResult(SurfaceShareError
                                        .ERROR_SET_STREAM_SURFACE_FAILED, "");
                            } else {
                                mManager.processStatus(SurfaceShareStatus.STATUS_SET_STREAM_SUCCESS, "");
                            }
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                    }
                    break;
                case MSG_BIND_SERVER_TIMEOUT:
                    if (SurfaceShareState.wait == mManager.mState) {
                        mManager.processResult(SurfaceShareError.ERROR_CONNECT_SERVER_TIMEOUT,
                                "");
                    }
                    break;
                case MSG_ABANDON_IMAGE_FRAME:
                    if (SurfaceShareState.idle == mManager.mState) {
                        return;
                    }

                    SurfaceShareBean bean = (SurfaceShareBean) msg.obj;
                    if (mManager.mContent == null || !bean.getPackageName().equals(mManager.mContent
                            .getBean().getPackageName())) {
                        return;
                    }
                    removeMessages(MSG_BIND_SERVER_TIMEOUT);
                    if (SurfaceShareState.used == mManager.mState) {
                        try {
                            SurfaceShareBean shareBean = mManager.mContent.getBean();
                            if (shareBean != null && shareBean.getIsUseToPreview()){
                                mManager.mCommand.showSurface(mManager.mContent.getSurface(),false);
                            }else {
                                mManager.mCommand.unSetStreamSurface();
                            }
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                    }
                    mManager.mContent = null;
                    if (mManager.conn != null) {
                        mManager.conn = null;
                    }
                    mManager.mCommand = null;
                    Log.d(TAG, "mCommand: " + mManager.mCommand + ", mState: " + mManager.mState);
                    mManager.updateSurfaceShareState(SurfaceShareState.idle);
                    break;
                case MSG_SERVER_EXIT:
                    if (SurfaceShareState.used == mManager.mState) {
                        if (mManager.mContent != null && mManager.mContent.getListener() != null) {
                            mManager.processResult(SurfaceShareError.ERROR_SERVER_EXIT, "");
                        }
                    }
                    break;
                default:
                    break;
            }
        }
    }

    public SurfaceShareManager init(CoreService core) {
        Log.d(TAG, "init mState: " + mState);
        mContext = core.getApplicationContext();
        updateSurfaceShareState(SurfaceShareState.idle);
        mSurfaceSharePackageName = getRobotString(mContext, Definition.SURFACE_SHARE_PACKAGE_NAME);
        mSurfaceShareServiceName = getRobotString(mContext, Definition.SURFACE_SHARE_SERVICE_NAME);
        mSurfaceShareActionName = getRobotString(mContext, Definition.SURFACE_SHARE_ACTION_NAME);
        if (TextUtils.isEmpty(mSurfaceSharePackageName)
                || TextUtils.isEmpty(mSurfaceShareServiceName)
                || TextUtils.isEmpty(mSurfaceShareActionName)) {
            mSurfaceSharePackageName = Definition.VISION_PACKAGE_NAME;
            mSurfaceShareServiceName = Definition.VISION_SERVICE_NAME;
            mSurfaceShareActionName = Definition.VISION_ACTION_NAME;
        }
        return this;
    }

    public int requestImageFrame(Bundle bundle, SurfaceShareBean bean,
                                 ISurfaceShareListener listener) {
        Log.d(TAG, "requestImageFrame mCommand: " + mCommand + ", mState: " + mState
                + ", bean: " + bean.toString());
        Surface surface = bundle.getParcelable("surface");
        SurfaceShareContent content = new SurfaceShareContent(surface, bean, listener);
        mHandler.obtainMessage(MSG_REQUEST_IMAGE_FRAME, content).sendToTarget();
        return 0;
    }

    public int abandonImageFrame(SurfaceShareBean bean) {
        Log.d(TAG, "abandonImageFrame mState: " + mState + ", bean: " + bean.toString());
        mHandler.obtainMessage(MSG_ABANDON_IMAGE_FRAME, bean).sendToTarget();
        return 0;
    }

    public boolean isUsed() {
        return mState != SurfaceShareState.idle;
    }

    private void updateSurfaceShareState(SurfaceShareState state) {
        Log.d(TAG, "updateSurfaceShareState state: " + state + ", mState: " + mState);
        if (mState != state) {
            if (SurfaceShareState.used == state) {
                mCallback = new SurfaceShareCallback();
                if (mCommand != null) {
                    try {
                        mCommand.registerCallback(mCallback);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
            } else if (SurfaceShareState.idle == state) {
                if (mCommand != null) {
                    try {
                        mCommand.unRegisterCallback(mCallback);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
                mCallback = null;
            }
        }
        mState = state;
    }

    private void bindSurfaceShareServerService(Context context) {
        Log.e(TAG, "bindSurfaceShareServerService");
        if (null == conn) {
            Intent visionIntent = IntentUtil.createExplicitIntent(mSurfaceSharePackageName,
                    mSurfaceShareServiceName, mSurfaceShareActionName);
            conn = new ServiceConnection() {
                @Override
                public void onServiceConnected(ComponentName name, IBinder service) {
                    Log.d(TAG, "bindSurfaceShareServerService onServiceConnected name: " + name);
                    mCommand = ISurfaceShareCommand.Stub.asInterface(service);
                    try {
                        mCommand.asBinder().linkToDeath(mBinderDeathRecipient, 1);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                    Surface surface = mContent == null ? null : mContent.getSurface();
                    Log.e(TAG, "bindSurfaceShareServerService onServiceConnected" +
                            " mState: " + mState + ", mSurface: " + surface);
                    if (SurfaceShareState.used == mState) {
                        return;
                    } else if (SurfaceShareState.wait == mState) {
                        mHandler.removeMessages(MSG_BIND_SERVER_TIMEOUT);
                        if (surface != null) {
                            try {
                                updateSurfaceShareState(SurfaceShareState.used);
                                boolean isSuccess ;
                                SurfaceShareBean bean = mContent.getBean();
                                if (bean != null && bean.getIsUseToPreview()){
                                    Log.d(TAG, "handleMessage only showSurface ");
                                    isSuccess = mCommand.showSurface(mContent.getSurface(),true);
                                }else {
                                    isSuccess = mCommand.setStreamSurface(mContent.getSurface());
                                }
                                Log.d(TAG, "bindSurfaceShareServerService setStreamSurface: "
                                        + isSuccess);
                                if (!isSuccess) {
                                    processResult(SurfaceShareError.ERROR_SET_STREAM_SURFACE_FAILED,
                                            "");
                                } else {
                                    processStatus(SurfaceShareStatus.STATUS_SET_STREAM_SUCCESS, "");
                                }
                                return;
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }
                        }
                    }

                    updateSurfaceShareState(SurfaceShareState.idle);
                }

                @Override
                public void onServiceDisconnected(ComponentName name) {
                    Log.d(TAG, "bindSurfaceShareServerService onServiceDisconnected name: " + name);
                    mHandler.removeMessages(MSG_BIND_SERVER_TIMEOUT);
                    conn = null;
                    if (mCommand != null) {
                        mCommand.asBinder().unlinkToDeath(mBinderDeathRecipient, 1);
                        mCommand = null;
                    }
                    if (SurfaceShareState.idle != mState) {
                        processResult(SurfaceShareError.ERROR_SERVER_EXIT_ABNORMALLY, "");
                    }
                }
            };
            context.bindService(visionIntent, conn, Context.BIND_AUTO_CREATE);
            Log.e(TAG, "bindSurfaceShareServerService start");
        } else {
            Log.e(TAG, "have bindSurfaceShareServerService");
        }
    }

    private IBinder.DeathRecipient mBinderDeathRecipient = new IBinder.DeathRecipient() {
        @Override
        public void binderDied() {
            Log.d(TAG, "binderDied mCommand: " + mCommand + ", mState: " + mState);
            conn = null;
            if (null == mCommand) {
                return;
            }
            mCommand.asBinder().unlinkToDeath(this, 1);
            mCommand = null;
            if (SurfaceShareState.idle != mState) {
                processResult(SurfaceShareError.ERROR_SERVER_EXIT_ABNORMALLY, "");
            }
        }
    };

    private void processResult(int errorId, String message) {
        Log.d(TAG, "processResult mContent: " + mContent + ", errorId: " + errorId
                + ", message: " + message);
        processResult(mContent, errorId, message);
        mHandler.removeMessages(MSG_BIND_SERVER_TIMEOUT);
        mContent = null;
        updateSurfaceShareState(SurfaceShareState.idle);
    }

    private void processStatus(int status, String message) {
        Log.d(TAG, "processStatus status: " + status
                + ", message: " + message);
        if (mContent != null && mContent.getListener() != null) {
            try {
                mContent.getListener().onStatusUpdate(status, message);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    private void processResult(SurfaceShareContent content, int errorId, String message) {
        Log.d(TAG, "processResult content: " + content + ", errorId: " + errorId
                + ", message: " + message);
        if (content != null && content.getListener() != null) {
            try {
                content.getListener().onError(errorId, message);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    public void onDisconnect(String packageName) {
        Log.d(TAG, "onDisconnect packageName: " + packageName + ", mState: " + mState
                + ", mContent: " + mContent);
        if (mState != SurfaceShareState.idle) {
            if (mContent != null && mContent.getBean() != null) {
                String contentPackageName = mContent.getBean().getPackageName();
                if (packageName.equals(contentPackageName)) {
                    if (SurfaceShareState.used == mState) {
                        try {
                            mCommand.unSetStreamSurface();
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                    } else {
                        mHandler.removeMessages(MSG_BIND_SERVER_TIMEOUT);
                    }
                    mContent = null;
                    updateSurfaceShareState(SurfaceShareState.idle);
                }
            }
        }
    }

    void onSurfaceShareStop(int reason) {
        Log.d(TAG, "onSurfaceShareStop reason: " + reason + ", mState: " + mState
                + ", mContent: " + mContent);
        mHandler.sendEmptyMessage(MSG_SERVER_EXIT);
    }

    public static String getRobotString(Context context, String key) {
        Cursor cursor = context.getContentResolver().query(Uri.parse(URI), new String[]{KEY_VALUE},
                KEY_ID + "=?", new String[]{key}, null);
        if (cursor != null && cursor.moveToNext()) {
            String value = cursor.getString(cursor.getColumnIndex(KEY_VALUE));
            cursor.close();
            Log.d(TAG, "get robot string:key=" + key + ",value=" + value);
            return value;
        }

        return "";
    }
}
