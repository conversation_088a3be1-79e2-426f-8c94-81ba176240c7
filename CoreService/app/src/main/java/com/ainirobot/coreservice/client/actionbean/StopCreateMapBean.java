package com.ainirobot.coreservice.client.actionbean;


import java.util.List;

public class StopCreateMapBean extends BaseBean {

    private String mapName;
    private String mapLanguage;
    private int visionType;
    private int targetType;
    private int finishState;
    /**
     * 存储特殊点，主要用于处理优先级修改。
     * 业务层维护了特殊点位的优先级缓存，需要在点位存入数据库之前做一次数据更新。
     */
    private List<Pose> specialPoseList;

    public String getMapName() {
        return mapName;
    }

    public StopCreateMapBean setMapName(String mapName) {
        this.mapName = mapName;
        return this;
    }

    public String getMapLanguage() {
        return mapLanguage;
    }

    public StopCreateMapBean setMapLanguage(String mapLanguage) {
        this.mapLanguage = mapLanguage;
        return this;
    }

    public int getVisionType() {
        return visionType;
    }

    public StopCreateMapBean setVisionType(int visionType) {
        this.visionType = visionType;
        return this;
    }

    public int getTargetType() {
        return targetType;
    }

    public StopCreateMapBean setTargetType(int targetType) {
        this.targetType = targetType;
        return this;
    }

    public int getFinishState() {
        return finishState;
    }

    public StopCreateMapBean setFinishState(int finishState) {
        this.finishState = finishState;
        return this;
    }

    public StopCreateMapBean setSpecialPoseList(List<Pose> specialPoseList) {
        this.specialPoseList = specialPoseList;
        return this;
    }

    public List<Pose> getSpecialPoseList() {
        return specialPoseList;
    }

    @Override
    public String toString() {
        return "StopCreateMapBean{" +
                "mapName='" + mapName + '\'' +
                ", mapLanguage='" + mapLanguage + '\'' +
                ", visionType=" + visionType +
                ", targetType=" + targetType +
                ", finishState=" + finishState +
                ", specialPoseList" + specialPoseList +
                '}';
    }
}
