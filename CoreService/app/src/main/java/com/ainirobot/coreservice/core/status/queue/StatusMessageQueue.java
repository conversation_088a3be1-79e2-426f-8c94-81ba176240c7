package com.ainirobot.coreservice.core.status.queue;

import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.core.CoreStateMachine;
import com.ainirobot.coreservice.core.InternalDef;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.core.status.biz.StatusMapping;
import com.ainirobot.coreservice.service.CoreService;

import java.util.Iterator;
import java.util.concurrent.LinkedBlockingDeque;

public class StatusMessageQueue {

    private static final String TAG = "StatusMessageQueue";

    private LinkedBlockingDeque<StatusMessage> mQueue;
    private LinkedBlockingDeque<StatusMessage> mSendHistoryQueue;
    private final Thread mThread;
    private volatile boolean isRunning = true;
    private final Handler mHandler;
    private CoreService mCore;

    public StatusMessageQueue(CoreService coreService) {
        mCore = coreService;
        mQueue = new LinkedBlockingDeque<StatusMessage>();
        mSendHistoryQueue = new LinkedBlockingDeque<StatusMessage>();

        mThread = new Thread(mRunnable);
        mThread.start();

        HandlerThread receiveThread = new HandlerThread("receive msg");
        receiveThread.start();
        mHandler = new ReceiveHandler(receiveThread.getLooper());
    }

    private Runnable mRunnable = new Runnable() {
        @Override
        public void run() {
            while (isRunning) {
                pollStatusMessage();
            }
        }
    };

    private void pollStatusMessage() {
        StatusMessage statusMessage = null;
        try {
            statusMessage = mQueue.take();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        if (statusMessage != null) {
            sendCommand(statusMessage);
        }
    }

    private void sendCommand(StatusMessage statusMessage) {
        String params = StatusMapping.getInstance().getParams(statusMessage.getStatusType());
        int cmdId = getCmdId(statusMessage, params);

        statusMessage.setCmdId(cmdId);
        mSendHistoryQueue.offer(statusMessage);

        Handler coreHandler = mCore.getCoreHandler();
        Message msg = coreHandler.obtainMessage(InternalDef.MSG_CORE_ADD_COMMAND_ACTION);
        coreHandler.sendMessage(msg);
        Log.i(TAG, "pollStatusMessage : cmdId:" + cmdId + " statusType:" + statusMessage.getStatusType() + " params:" + params);
    }

    private int getCmdId(StatusMessage statusMessage, String params) {
        CoreStateMachine coreStateMachine = mCore.getCoreStateMachine();
        return coreStateMachine.addCommand(0, statusMessage.getStatusType(), params, false, new LocalApi.OnCmdResponse() {
                @Override
                public void onCmdResponse(int cmdId1, String statusType, String result, String extraData) {

                    Message obtain = Message.obtain();
                    Bundle bundle = new Bundle();
                    bundle.putInt("cmdId", cmdId1);
                    bundle.putString("statusType", statusType);
                    bundle.putString("data", result);
                    obtain.setData(bundle);
                    mHandler.sendMessage(obtain);
                }

                @Override
                public void onCmdStatusUpdate(int cmdId1, String statusType, String status, String extraData) {
                    Message obtain = Message.obtain();
                    Bundle bundle = new Bundle();
                    bundle.putInt("cmdId", cmdId1);
                    bundle.putString("statusType", statusType);
                    bundle.putString("data", status);
                    obtain.setData(bundle);
                    mHandler.sendMessage(obtain);
                }
            });
    }

    private class ReceiveHandler extends Handler {

        public ReceiveHandler(Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(Message msg) {
            Bundle bundle = msg.getData();
            String statusType = bundle.getString("statusType");
            String data = bundle.getString("data");
            int cmdId = bundle.getInt("cmdId");
            receiveMessage(statusType, data, cmdId);
        }

        private void receiveMessage(String statusType, String data, int cmdId) {
            Log.i(TAG, "receiveMessage : cmdId:" + cmdId + " statusType:" + statusType);
            if (mSendHistoryQueue.size() > 0) {
                Iterator<StatusMessage> iterator = mSendHistoryQueue.iterator();
                while (iterator.hasNext()) {
                    StatusMessage receiveMsg = iterator.next();
                    if (receiveMsg != null && receiveMsg.getCmdId() == cmdId) {
                        if (!TextUtils.isEmpty(data)) {
                            receiveMsg.getHWState().reportStatus(statusType, data);
                        }
                        mSendHistoryQueue.remove(receiveMsg);
                        receiveMsg.recycle();
                        break;
                    }
                }
            }
        }
    }

    public void offer(StatusMessage msg) {
        if (mQueue != null && msg != null) {
            try {
                mQueue.put(msg);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    public void exitMsgQueue() {
        isRunning = false;
        mQueue.clear();
        mSendHistoryQueue.clear();
    }
}
