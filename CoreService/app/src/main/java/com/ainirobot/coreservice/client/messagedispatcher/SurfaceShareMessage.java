/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.client.messagedispatcher;

import com.ainirobot.coreservice.client.surfaceshare.SurfaceShareListener;

class SurfaceShareMessage implements IMsgHandle, IRecyclable {
    private static RecyclablePool<SurfaceShareMessage> sPool = new RecyclablePool<>();

    @Override
    public void handleMessage() {
        switch (msgType) {
            case error:
                msgListener.onError(msgCode, msgStr);
                break;
            case status:
                msgListener.onStatusUpdate(msgCode, msgStr);
                break;
        }
        sPool.recycle(this);
    }

    @Override
    public void recycle() {
        msgStr = null;
        msgListener = null;
    }

    enum MsgType {
        error, status
    }

    MsgType msgType;
    String msgStr;
    SurfaceShareListener msgListener;
    int msgCode;

    private SurfaceShareMessage(SurfaceShareListener msgListener, MsgType msgType, String msgStr, int msgCode) {
        this.msgCode = msgCode;
        this.msgStr = msgStr;
        this.msgType = msgType;
        this.msgListener = msgListener;
    }

    static SurfaceShareMessage obtain(SurfaceShareListener msgListener, MsgType msgType, String msgStr, int msgCode) {
        SurfaceShareMessage message = sPool.obtain();

        if (message == null) {
            message = new SurfaceShareMessage(msgListener, msgType, msgStr, msgCode);
        } else {
            message.msgType = msgType;
            message.msgStr = msgStr;
            message.msgCode = msgCode;
            message.msgListener = msgListener;
        }
        return message;
    }
}
