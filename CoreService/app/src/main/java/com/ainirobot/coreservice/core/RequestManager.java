/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.core;

import android.content.Context;
import android.os.Environment;
import android.util.Log;
import android.util.SparseArray;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.R;
import com.ainirobot.coreservice.config.RequestConfig;
import com.ainirobot.coreservice.core.module.Module;
import com.ainirobot.coreservice.core.module.ModuleManager.Permission;
import com.ainirobot.coreservice.data.core.RequestDataHelper;
import com.ainirobot.coreservice.utils.Utils;
import com.google.gson.Gson;

import java.io.File;
import java.util.Map;

public class RequestManager {

    private static final String TAG = RequestManager.class.getSimpleName();
    private static final int START_ID = 10;

    private static int mReqCount = START_ID;
    private SparseArray<ReqAction> mReqActions;
    private Map<String, Permission> mReqPermissions;

    public class ReqAction {
        public final int reqId;
        public final String reqType;
        public final String reqParam;
        public final Permission permission;

        public String reqText;
        public Module reqModule;
        public int reqFunction;
        public String reqUser;

        public ReqAction(int reqId, String reqType, String reqParam) {
            this.reqId = reqId;
            this.reqType = reqType;
            this.reqParam = reqParam;
            this.permission = mReqPermissions.get(reqType);
        }
    }

    public class ReqStatus {
        public int reqId;
        public int reqResult;
        public String returnContent;
    }

    public RequestManager() {
        mReqActions = new SparseArray<>();
        mReqCount = START_ID;
        Map<String, Permission> internalReqPermissions = getInternalRequestInfo();
        Map<String, Permission> externalReqPermissions = getExternalRequestInfo();
        Log.d(TAG, "internalReqPermissions: " + internalReqPermissions.size());
        Log.d(TAG, "externalReqPermissions: " + externalReqPermissions.size());
        mergeRequestInfo(internalReqPermissions, externalReqPermissions);
        Log.d(TAG, "mReqPermissions: " + mReqPermissions.size());
    }

    private Map<String, Permission> getInternalRequestInfo() {
        Context context = ApplicationWrapper.getContext();
        RequestDataHelper requestData = new RequestDataHelper(context);
        return requestData.getRequestInfos();
    }

    private Map<String, Permission> getExternalRequestInfo() {
        String requestJson = Utils.loadConfig(ApplicationWrapper.getContext(),
                Environment.getExternalStorageDirectory()
                        + File.separator + "request.json", R.raw.request);
        Gson gson = new Gson();
        Log.d(TAG, "getExternalRequestInfo requestJson: " + requestJson);
        RequestConfig requestConfig = gson.fromJson(requestJson, RequestConfig.class);
        return requestConfig.parseRequest();
    }

    private void mergeRequestInfo(Map<String, Permission> internalRequestInfo,
                                  Map<String, Permission> externalRequestInfo) {
        if (internalRequestInfo == null || internalRequestInfo.size() <= 0) {
            mReqPermissions = externalRequestInfo;
        } else if (externalRequestInfo == null || externalRequestInfo.size() <= 0) {
            mReqPermissions = internalRequestInfo;
        } else {
            mReqPermissions = internalRequestInfo;
            for (String type : externalRequestInfo.keySet()) {
                if (internalRequestInfo.containsKey(type)) {
                    continue;
                }
                mReqPermissions.put(type, externalRequestInfo.get(type));
            }
        }
    }

    public synchronized ReqAction addReqAction(String type, String params) {
        ReqAction req = new ReqAction(mReqCount, type, params);
        mReqActions.put(req.reqId, req);
        mReqCount++;
        return req;
    }

    public synchronized ReqAction addReqAction(int reqId, String type, String params) {
        ReqAction req = new ReqAction(reqId, type, params);
        mReqActions.put(req.reqId, req);
        return req;
    }

    public synchronized void removeReqAction(int id) {
        if (id < START_ID) {
            return;
        }
        mReqActions.delete(id);
    }

    public ReqAction getReqAction(int id) {
        return mReqActions.get(id);
    }

    public boolean isExits(int reqId) {
        return getReqAction(reqId) != null;
    }
}
