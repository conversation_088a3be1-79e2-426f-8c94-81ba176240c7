package com.ainirobot.coreservice.data.core;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.text.TextUtils;

import com.ainirobot.coreservice.utils.IOUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

public class DataImportUtils {

    public static void importDataFromAsset(Context context, SQLiteDatabase db, String fileName) {
        BufferedReader reader = null;
        try {
            InputStream is = context.getAssets().open(fileName);
            reader = new BufferedReader(new InputStreamReader(is));
            String sql = reader.readLine();
            String line;
            while ((line = reader.readLine()) != null) {
                if (TextUtils.isEmpty(line.trim())) {
                    continue;
                }
                db.execSQL(sql + line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            IOUtils.close(reader);
        }
    }


}
