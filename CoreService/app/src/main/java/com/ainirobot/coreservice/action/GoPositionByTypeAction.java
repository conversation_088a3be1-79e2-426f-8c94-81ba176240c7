package com.ainirobot.coreservice.action;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.bi.report.SdkNavigateFinishReport;
import com.ainirobot.coreservice.bi.report.SdkNavigateStartReport;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.actionbean.GoPositionByTypeBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.core.LocalApi;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Timer;
import java.util.TimerTask;

public class GoPositionByTypeAction extends AbstractAction<GoPositionByTypeBean> {

    private static final String TAG = "GoPositionByTypeAction";

    //if 20 seconds moving distance less than 0.1 meters is that navigation failed
    private static final double MIN_DISTANCE = 0.1; //unit meter
    private static final long DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT = 3600 * 1000;

    private enum NavigationState {
        IDLE, PREPARE, NAVIGATION, STOPPED
    }

    private int mReqId;
    private int mTypeId; // 特殊点位类型
    private int mPriority; // 特殊点位优先级
    private boolean mAdjustAngle;
    private double mLinearSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED;
    private double mAngularSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED;
    private double mCoordinateDeviation;
    private long mTimeout;
    private String mPoseListener;
    private Pose mFlagPose;
    private GoPositionByTypeAction.NavigationState mStatus;
    private SdkNavigateFinishReport navigateFinishReport;
    /**多机模式等待超时时长，默认3600s **/
    private long mWaitTimeout;
    /**是否是在多机避障状态，如果是则停止处理机器N秒不动退出导航的逻辑 **/
    private volatile boolean isMultipleWaitStatus = false;
    private Timer mWaitTimer;
    private int mTaskPriority;
    private double mLinearAcceleration;
    private double mAngularAcceleration;
    private int mStartModeLevel;
    private int mBrakeModeLevel;
    private double mObsDistance;
    private double mDestinationRange;

    protected GoPositionByTypeAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_NAVI_GO_POSITION_BY_TYPE, resList);
        navigateFinishReport = new SdkNavigateFinishReport();
    }

    @Override
    protected boolean startAction(GoPositionByTypeBean parameter, LocalApi api) {
        if (verifyParam(parameter)) {
            prepareNavigation(parameter);
            return true;
        }
        return false;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        Log.d(TAG, "stop goPositionByType action");
        updateMultipleRobotWaitingStatus(false);
        cancelWaitTimer();
        mStatus = GoPositionByTypeAction.NavigationState.STOPPED;
        unregisterStatusListener(mPoseListener);
        mFlagPose = null;
        return true;
    }

    @Override
    protected boolean verifyParam(GoPositionByTypeBean param) {
        return param != null;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_IS_ESTIMATE:
                processPoseEstimate(result);
                return true;
            case Definition.CMD_NAVI_GO_POSITION_BY_TYPE:
                processNavigationResult(result, extraData);
                return true;
            case Definition.CMD_NAVI_IS_IN_LOCATION:
                verifyLocation(result, extraData);
                return true;
            default:
                return false;
        }
    }

    @Override
    public boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        if (command.equals(Definition.CMD_NAVI_GO_POSITION_BY_TYPE)) {
            processNavigationStatus(status);
            return true;
        }
        return false;
    }

    private void prepareNavigation(GoPositionByTypeBean params) {
        Log.d(TAG, "start goPositionByType action, parameter: " + params.toString());
        mStatus = GoPositionByTypeAction.NavigationState.PREPARE;
        mReqId = params.getReqId();
        mTypeId = params.getTypeId();
        mPriority = params.getPriority();
        mAdjustAngle = params.getIsAdjustAngle();
        mLinearSpeed = params.getLinearSpeed();
        mAngularSpeed = params.getAngularSpeed();
        mLinearAcceleration = params.getLinearAcceleration();
        mAngularAcceleration = params.getAngularAcceleration();
        mCoordinateDeviation = params.getCoordinateDeviation();
        mStartModeLevel = params.getStartModeLevel();
        mBrakeModeLevel = params.getBrakeModeLevel();
        mObsDistance = params.getObsDistance();
        mDestinationRange = params.getDestinationRange();
        long timeout = params.getTime();
        mTimeout = ((timeout == 0) ? Long.MAX_VALUE : timeout);
        long waitTimeout = params.getMultipleWaitTime();
        mWaitTimeout = (waitTimeout <= 0) ? DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT: waitTimeout;
        isMultipleWaitStatus = false;
        mTaskPriority = params.getTaskPriority();
        checkPoseEstimate();
        registerPoseListener();
    }

    private void checkPoseEstimate() {
        Log.d(TAG, "Check pose estimate");
        mApi.isRobotEstimate(mReqId);
    }

    private void registerPoseListener() {
        mPoseListener = registerStatusListener(Definition.STATUS_POSE, new StatusListener() {
            @Override
            public void onStatusUpdate(String data) {
                onPoseUpdate(data);
            }
        });
    }

    private synchronized void onPoseUpdate(String data) {
        if (TextUtils.isEmpty(data) || mStatus != GoPositionByTypeAction.NavigationState.NAVIGATION) {
            return;
        }
        Pose pose = mGson.fromJson(data, Pose.class);
        if (isAborted(pose)) {
            Log.d(TAG, "Pose timeout : " + mFlagPose.toString() + "   "
                    + pose.toString() + "  " + mStatus);
            onError(Definition.ERROR_DESTINATION_CAN_NOT_ARRAIVE, "Timeout");
        }
    }

    private boolean isAborted(Pose pose) {
        double distance = pose.getDistance(mFlagPose);
        if (!isMultipleWaitStatus && Double.compare(distance, MIN_DISTANCE) < 0) {
            long movingTime = pose.getTime() - mFlagPose.getTime();
            return movingTime > mTimeout;
        } else {
            mFlagPose = pose;
        }
        return false;
    }

    private void processPoseEstimate(String result) {
        if (!"true".equals(result)) {
            onError(Definition.ERROR_NOT_ESTIMATE, "Not pose estimate");
            return;
        }
        mApi.isRobotInLocations(mReqId, mTypeId, mPriority, mCoordinateDeviation);
    }

    private void verifyLocation(String result, String extraData) {
        try {
            JSONObject json = new JSONObject(result);
            boolean isInLocation = json.optBoolean(Definition.JSON_NAVI_IS_IN_LOCATION, false);
            if (isInLocation) {
                //stop();
                onResult(Definition.ERROR_IN_DESTINATION, "Already at the typeId:" + mTypeId, extraData);
            } else {
                if (isValidTypeId(mTypeId)) {
                    mStatus = NavigationState.NAVIGATION;
                    mApi.goPositionByType(mReqId, mTypeId, mPriority, mLinearSpeed, mAngularSpeed,
                            mAdjustAngle, false, mDestinationRange, mTaskPriority, mLinearAcceleration,
                            mAngularAcceleration, mStartModeLevel, mBrakeModeLevel, mObsDistance, false);
                } else {
                    onError(Definition.ERROR_DESTINATION_CAN_NOT_ARRAIVE, "Special Place typeId is invalid!");
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
            onError(Definition.ERROR_IN_DESTINATION, "Judge at the typeId " + mTypeId + " failed", extraData);
        }
    }

    private void processNavigationStatus(String status) {
        switch (status) {
            case Definition.NAVIGATION_OCCLUDED:
                onStatusUpdate(Definition.STATUS_GOAL_OCCLUDED, "The target is occluded");
                break;

            case Definition.NAVIGATION_OCCLUDED_END:
                onStatusUpdate(Definition.STATUS_GOAL_OCCLUDED_END, "The occluded end");
                break;

            case Definition.NAVIGATION_AVOID:
                onStatusUpdate(Definition.STATUS_NAVI_AVOID, "The local path search failed");
                break;

            case Definition.NAVIGATION_AVOID_END:
                onStatusUpdate(Definition.STATUS_NAVI_AVOID_END, "The local path search succeed");
                break;

            case Definition.NAVIGATION_OUT_MAP:
                onStatusUpdate(Definition.STATUS_NAVI_OUT_MAP, "The goal is out of map or more dangerous");
                break;

            case Definition.NAVIGATION_GLOBAL_PATH_FAILED:
                onStatusUpdate(Definition.STATUS_NAVI_GLOBAL_PATH_FAILED, "The global is path search failed");
                break;

            case Definition.NAVIGATION_OBSTACLES_AVOID:
                onStatusUpdate(Definition.STATUS_NAVI_OBSTACLES_AVOID, "obstacles avoid");
                break;

            case Definition.NAVIGATION_STARTED:
                onStatusUpdate(Definition.STATUS_START_NAVIGATION, "Navigation has been started");
                break;

            case Definition.NAVIGATION_MULTI_ROBOT_WAITING:
                updateMultipleRobotWaitingStatus(true);
                startWaitingTimer();
                onStatusUpdate(Definition.STATUS_NAVI_MULTI_ROBOT_WAITING, "Multiple robots are waiting to avoid obstacles");
                break;

            case Definition.NAVIGATION_MULTI_ROBOT_WAITING_END:
                updateMultipleRobotWaitingStatus(false);
                cancelWaitTimer();
                onStatusUpdate(Definition.STATUS_NAVI_MULTI_ROBOT_WAITING_END, "Multiple robots passageways returned to normal");
                break;

            case Definition.NAVIGATION_SET_PRIORITY_FAILED:
                onStatusUpdate(Definition.STATUS_NAVI_SET_PRIORITY_FAILED, "Current navigation task priority setting failed!");
                break;

            default:
                Log.i(TAG, "Command status: " + status +  " doesn't be handled");
                break;
        }
    }

    private void processNavigationResult(String result, String extraData) {
        Log.d(TAG, "processNavigationResult : " + result);

        navigateFinishReport.fillData().report();

        if (Definition.NAVIGATION_OK.equals(result)) {
            if (mStatus != GoPositionByTypeAction.NavigationState.STOPPED){
                onResult(Definition.RESULT_OK, result);
            }else {
                onResult(Definition.RESULT_STOP, result);
            }
            return;
        }
        if (Definition.NAVIGATION_CANCELED.equals(result)) {
            onResult(Definition.RESULT_STOP, result);
            return;
        }
        processError(Definition.ERROR_NAVIGATION_FAILED, "Navigation failed : " + result, extraData);
    }

    private void processError(int errorCode, String errorMessage, String extraData) {
        Log.d(TAG, "processError : " + errorCode + " " + errorMessage + " " + extraData);
        super.onError(errorCode, errorMessage, extraData);
    }

    /**
     * 更新多机避障状态，如果是多机避障状态，则机器人超时等待策略需忽略
     * @param isWaiting 是否正在多机避障等待状态
     */
    private void updateMultipleRobotWaitingStatus(boolean isWaiting){
        isMultipleWaitStatus = isWaiting;
    }

    private void startWaitingTimer() {
        cancelWaitTimer();
        mWaitTimer = new Timer();
        mWaitTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (mStatus != GoPositionByTypeAction.NavigationState.NAVIGATION) {
                    return;
                }
                onError(Definition.ERROR_MULTI_ROBOT_WAITING_TIMEOUT, "multiple robot wait timeout");
            }
        }, mWaitTimeout);
    }

    private void cancelWaitTimer() {
        if (mWaitTimer != null) {
            mWaitTimer.cancel();
            mWaitTimer = null;
        }
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return new ArrayList<StopCommandList.StopCommand>() {{
            add(new StopCommandList.StopCommand(Definition.CMD_NAVI_STOP_NAVIGATION, null));
        }};
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return new StopCommandList.IStopCommandChecker() {
            @Override
            public boolean executeSuccess(String command, String result) {
                Log.d(TAG, "Leading stop check : " + command);
                switch (command) {
                    case Definition.CMD_NAVI_STOP_NAVIGATION:
                        return true;
                    default:
                        return false;
                }
            }
        };
    }

    private boolean isValidTypeId(int typeId) {
        return typeId >= Definition.CHARGING_POINT_TYPE && typeId <= Definition.GATE_OUTER_TYPE;
    }
}
