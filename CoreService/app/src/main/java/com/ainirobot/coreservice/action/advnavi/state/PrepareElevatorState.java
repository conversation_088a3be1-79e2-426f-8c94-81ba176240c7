package com.ainirobot.coreservice.action.advnavi.state;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.bean.ElevatorWaitArea;
import com.ainirobot.coreservice.bean.MultiFloorInfo;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.MultiRobotConfigBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

/**
 * 电梯准备阶段
 * 先检查是否启用电梯等待点
 */
public class PrepareElevatorState extends BaseState {
    private MultiFloorInfo mCurrentMultiFloorInfo;

    PrepareElevatorState(String name) {
        super(name);
    }

    @Override
    protected void preAction(StateData data, IStateListener listener) {
        super.preAction(data, listener);
    }

    @Override
    protected void doAction() {
        super.doAction();
        mCurrentMultiFloorInfo = mStateData.getCurrentFloorMultiFloorInfo();
        checkCurMapIsUseWaitPoint();
    }

    /**
     * 检查当前地图是否启用了电梯等待点
     */
    private void checkCurMapIsUseWaitPoint() {
        if (mCurrentMultiFloorInfo.getIsUseElevatorWaitPoint() == 1 && findElevatorWaitPose()) {
            queryCurrentDeviceMultipleConfig();
        } else {
            noNeedGoWaitPoint();
        }
    }

    private void noNeedGoWaitPoint() {
        updateAction(Definition.ElevatorAction.ACTION_NO_NEED_ELEVATOR_WAIT_POINT);
        mStateData.setIsGoElevatorWaitPoint(false);
        onSuccess();
    }

    /**
     * 查询多机配置
     */
    private void queryCurrentDeviceMultipleConfig() {
        mApi.queryCurrentDeviceMultipleConfig(mReqId);
    }

    private boolean findElevatorWaitPose() {
        String elevatorWaitAreaJson = mCurrentMultiFloorInfo.getElevatorWaitAreaJson();
        if (!TextUtils.isEmpty(elevatorWaitAreaJson)) {
            Type listType = new TypeToken<List<ElevatorWaitArea>>() {
            }.getType();
            List<ElevatorWaitArea> elevatorWaitAreas = mGson.fromJson(elevatorWaitAreaJson, listType);

            ElevatorWaitArea curUseElevatorWaitArea = findElevatorWaitArea(elevatorWaitAreas);

            //获得所有点位
            List<Pose> allPlaceList = getAllPlaceList();

            //找到电梯等待点
            List<Pose> elevatorWaitPoints = findPoint(allPlaceList, curUseElevatorWaitArea.getAvailableWaitPoints());
            if (null != elevatorWaitPoints && elevatorWaitPoints.size() > 0) {
                mStateData.setElevatorWaitPoints(elevatorWaitPoints);
                return true;
            } else {
                Log.e(TAG, " *** elevator wait list is null *** ");
                return false;
            }
        } else {
            Log.e(TAG, " *** elevator wait area is null *** ");
            return false;
        }

    }

    /**
     * 找到包含本次导航所用电梯的电梯等待区
     */
    private ElevatorWaitArea findElevatorWaitArea(List<ElevatorWaitArea> elevatorWaitAreas) {
        ElevatorWaitArea elevatorWaitArea = new ElevatorWaitArea();
        for (int i = 0; i < elevatorWaitAreas.size(); i++) {
            ElevatorWaitArea tempElevatorWaitArea = elevatorWaitAreas.get(i);
            if (tempElevatorWaitArea.getAvailableElevators().contains(mStateData.getElevatorName())) {
                elevatorWaitArea = tempElevatorWaitArea;
                break;
            }
        }
        return elevatorWaitArea;
    }

    /**
     * 找到指定名称的Pose List
     */
    private List<Pose> findPoint(List<Pose> poseList, List<String> findList) {
        if (null == poseList || poseList.size() <= 0 || null == findList || findList.size() <= 0) {
            return null;
        }

        ArrayList<Pose> resultList = new ArrayList<>();
        for (int j = 0; j < findList.size(); j++) {
            String name = findList.get(j);
            for (int i = 0; i < poseList.size(); i++) {
                Pose poseInfo = poseList.get(i);
                if (poseInfo == null || TextUtils.isEmpty(poseInfo.getName())) {
                    continue;
                }
                if (poseInfo.getName().equals(name)) {
                    resultList.add(poseInfo);
                }
            }
        }

        return resultList;
    }

    /**
     * 获得所有地点，筛选目的地和备用点
     */
    private List<Pose> getAllPlaceList() {
        try {
            String result = getRobotInfo(Definition.SYNC_ACTION_GET_ALL_LOCATION);
            if (TextUtils.isEmpty(result)) {
                return null;
            }

            return mGson.fromJson(result, new TypeToken<List<Pose>>() {
            }.getType());
        } catch (Exception e) {
            e.printStackTrace();
            Log.d(TAG, "getAllPlaceList error");
            onResult(Definition.ERROR_PARAMETER, "get all place json error");
            return null;
        }
    }

    @Override
    protected BaseState getNextState() {
        switch (actionState) {
            //开启电梯等待点，前往电梯等待点，先注册电梯，再去电梯口
            case ACTION_NEED_ELEVATOR_WAIT_POINT:
                return StateEnum.GO_ELEVATOR_WAIT_POINT.getState();

            //未开启电梯等待点，先去电梯口，再注册电梯
            case ACTION_NO_NEED_ELEVATOR_WAIT_POINT:
                return StateEnum.GO_ELEVATOR_GATE.getState();
            default:
                return super.getNextState();
        }
    }


    @Override
    protected boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        if (!isAlive()) {
            Log.d(TAG, "current is not alive");
            return false;
        }
        switch (command) {
            case Definition.CMD_NAVI_GET_MULTI_ROBOT_CONFIG:
                parseMultiRobotConfig(result);
                return true;
        }
        return super.cmdResponse(cmdId, command, result, extraData);
    }

    /**
     * 解析多机配置
     */
    private void parseMultiRobotConfig(String result) {
        MultiRobotConfigBean multiRobotConfig = null;
        try {
            multiRobotConfig = mGson.fromJson(result, MultiRobotConfigBean.class);
            mStateData.setMultiRobotConfigBean(multiRobotConfig);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (null == multiRobotConfig) {
            onResult(Definition.ERROR_MULTI_ROBOT_CONFIG_ERROR, "multi robot config is null");
            return;
        }

        //判断多机是否可用
        judgeMultipleRobotEnable(multiRobotConfig);
    }

    private void judgeMultipleRobotEnable(MultiRobotConfigBean config) {
        Log.d(TAG, "judgeMultipleRobotEnable:" + config.isEnable());
        if (!config.isEnable()) {
            Log.i(TAG, "judgeMultipleRobotEnable false  not go elevator wait point");
            noNeedGoWaitPoint();
            return;
        }
        if (config.getErrorStatus() > 0) {
            onResult(Definition.STATUS_NAVI_MULTI_LORA_CONFIG_FAIL, "lora is in error status");
            return;
        }
        //多机正常，开始导航去电梯等待点
        updateAction(Definition.ElevatorAction.ACTION_NEED_ELEVATOR_WAIT_POINT);
        mStateData.setIsGoElevatorWaitPoint(true);
        onSuccess();
    }

}
