package com.ainirobot.coreservice.action.advnavi.state;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.MultiRobotStatus;
import com.ainirobot.coreservice.client.actionbean.NavigationAdvancedBean;
import com.ainirobot.coreservice.client.actionbean.Pose;

import java.util.ArrayList;
import java.util.List;

/**
 * Data: 2022/10/11 18:21
 * Author: wanglijing
 * <p>
 * 动态火车补位
 * 该状态下，会从后往前遍历补位列表，找到优先级最高，但是空闲的点位
 *
 */
public class NaviDynamicTrainByPointStrategy extends BaseMultiRobotNaviState {

    private volatile MultipleStatus multipleStatus;

    private Pose mTempNaviPose;

    NaviDynamicTrainByPointStrategy(String name) {
        super(name);
    }

    private enum MultipleStatus {
        IDLE,
        START,
        NAVIGATION,    //导航去补位区空闲点
        WAIT_HANDLE_STATUS,   //处理状态
        HANDLE_STATUS_FINISH, //完成处理状态
    }

    @Override
    protected void preAction(StateData data, IStateListener listener) {
        super.preAction(data, listener);
        updateMultiStatus(MultipleStatus.IDLE);
        addDestinationToStandbyList();
        fixStandbyListByCurrentPose();
    }

    @Override
    protected void doAction() {
        super.doAction();
        updateMultiStatus(MultipleStatus.START);
    }

    @Override
    protected void handleMultipleRobotStatus(List<MultiRobotStatus> robotStatusList) {
        switch (multipleStatus) {
            case START:
            case HANDLE_STATUS_FINISH:
                handleMultipleRobotCheckState(robotStatusList);
                break;
        }
    }

    /**
     * 以参数中的currentPlace为补位区最后一个点
     */
    private void fixStandbyListByCurrentPose() {
        String name = mNavigationBean.getCurrentPlace();
        //不是空的才执行
        if (!TextUtils.isEmpty(name)) {
            removeLowerThenCurrentPose();
        }
    }

    /**
     * 删除比当前点优先级低的点
     */
    private void removeLowerThenCurrentPose() {
        String name = mNavigationBean.getCurrentPlace();
        //找到当前点在列表中的位置
        boolean isLower = false;
        List<Pose> lowerPose = new ArrayList<>();
        for (Pose pose : mValidStandbyList) {
            if (TextUtils.equals(pose.getName(), name)) {
                isLower = true;
                continue;
            }

            if (isLower) {
                Log.d(TAG, "find lower pose: "+pose);
                lowerPose.add(pose);
            }
        }
        mValidStandbyList.removeAll(lowerPose);
        Log.i(TAG, "removed standby list: " + mValidStandbyList.size());
    }

    /**
     * 处理补位状态时多机状态
     */
    private void handleMultipleRobotCheckState(List<MultiRobotStatus> robotStatusList) {
        updateMultiStatus(MultipleStatus.WAIT_HANDLE_STATUS);
        DestinationState desState = getDestinationIsAvailableDynamic(robotStatusList);
        Log.i(TAG, "handleMultipleRobotCheckState desState: " + desState);
        switch (desState) {
            case AVAILABLE:
                updateMultiStatus(MultipleStatus.NAVIGATION);
                startGoCheckPose();
                break;
            case DESTINATION_INVALID:
                Log.e(TAG, "handleMultipleRobotCheckState error !!!");
                updateMultiStatus(MultipleStatus.IDLE);
                onResult(Definition.ERROR_NO_AVAILABLE_DESTINATION);
                break;
            case EXIT:
                //找不到要去的点，报到达
                arrivedMainDestination();
                break;
            case OCCUPIED:
            case NAVI_IN_RANGE:
            default:
                updateMultiStatus(MultipleStatus.HANDLE_STATUS_FINISH);
                break;
        }
    }

    /**
     * 遍历补位点，获得可以去的点位
     *
     * @param robotStatusList 多机状态
     * @return 是否可以补位
     */
    private DestinationState getDestinationIsAvailableDynamic(List<MultiRobotStatus> robotStatusList) {
        //多机为空，直接去出餐点
        if (robotStatusList == null || robotStatusList.size() <= 0) {
            if (null != mValidStandbyList && mValidStandbyList.size() > 0) {
                Log.i(TAG, "dynamicPose multiRobot is null ");
                updateTempNaviPose(mValidStandbyList.get(0));
                return DestinationState.AVAILABLE;
            }
            return DestinationState.DESTINATION_INVALID;
        }

        //从后往前遍历，找到优先级最高，但是空闲的点位
        Pose freeCheckPose = null;
        boolean isHaveNavigation;   //是否有机器导航到该点
        for (int i = mValidStandbyList.size() - 1; i >= 0; i--) {
            isHaveNavigation = false;
            Pose checkPose = mValidStandbyList.get(i);

            //status 为1表示无定位 2表示未在导航 3表示在导航中
            for (MultiRobotStatus robotStatus : robotStatusList) {

                //当前机器的多机信息，不用管
                if (robotStatus.isCurRobot() || robotStatus.getPose() == null) {
                    continue;
                }

                //判断该点是否被空闲的机器占用
                if (calculateTargetPoseInUse(checkPose, robotStatus.getPose())
                        && robotStatus.getStatus() == 2) {
                    //不是导航经过该范围，而是空闲的占用中，不需要遍历下面的点了
                    Log.i(TAG, "dynamicPose robot:" + robotStatus.getId() + " using pose:" + checkPose.getName());
                    return isAvailablePose(freeCheckPose);

                }

                //判断是否有人导航去某点时，忽略补位列表中最后一个点
                //有可能存在从餐桌回到补位区最后一个点的机器，goal是最后一个点
                if (i != mValidStandbyList.size() - 1
                        && calculateTargetPoseInNavigation(checkPose, robotStatus)) {
                    isHaveNavigation = true;
                    Log.d(TAG, "dynamicPose robot:" + robotStatus.getId() + " is navigating to pose:" + checkPose.getName());
                    break;
                }

            }

            //没机器导航去该点
            if (!isHaveNavigation) {
                freeCheckPose = checkPose;
                //更新后接着找有没有优先级更高，但是空闲的点
                Log.i(TAG, "dynamicPose update freeCheckPose: " + freeCheckPose.getName());
            }
        }
        return isAvailablePose(freeCheckPose);
    }

    /**
     * 判断获得的点是否为空，且是否为正在导航的点
     */
    private DestinationState isAvailablePose(Pose pose) {
        //从后往前遍历，但是freePose一直没被赋值，说明最后一个点都被占用了，动态火车补位直接退出
        //交给火车补位处理
        if (poseIsEmpty(pose)) {
            Log.d(TAG, "isAvailablePose pose is empty " + pose);
            return DestinationState.EXIT;
        }

        //当前正在导航
        if (mTempNaviPose != null
                && TextUtils.equals(mTempNaviPose.getName(), pose.getName())
                && multipleStatus == MultipleStatus.NAVIGATION) {
            Log.d(TAG, "isAvailablePose current is navigating to pose " + pose);
            return DestinationState.NAVI_IN_RANGE;
        }

        //更新导航点
        updateTempNaviPose(pose);
        return DestinationState.AVAILABLE;
    }

    /**
     * 判断是否有机器正在导航到同一目标点
     *
     * @return true 有机器正在导航去该点 false 没机器导航去该点
     */
    private boolean calculateTargetPoseInNavigation(Pose pose, MultiRobotStatus status) {
        double distance = calculatePoseDistance(pose, status.getGoal());
        Log.d(TAG, "calculateTargetPoseInNavigation distance:" + distance);
        return status.getStatus() == 3 && isInPoseRange(distance);
    }

    private boolean poseIsEmpty(Pose pose) {
        return null == pose || TextUtils.isEmpty(pose.getName());
    }

    /**
     * 导航去补位点
     */
    private void startGoCheckPose() {
        try {
            NavigationAdvancedBean bean = cloneNaviBean();
            bean.setMaxAvoidCount(mNavigationBean.getMaxAvoidCount());
            bean.setDestination(mTempNaviPose.getName());
            startNavigation(bean);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void updateTempNaviPose(Pose pose) {
        Log.i(TAG, "updateTempNaviPose: " + pose.getName());
        mTempNaviPose = pose;
    }

    private void updateMultiStatus(MultipleStatus status) {
        Log.i(TAG, "updateMultiStatus: " + status);
        this.multipleStatus = status;
    }
}
