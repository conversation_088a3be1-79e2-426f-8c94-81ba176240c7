package com.ainirobot.coreservice.action.download_map;

import com.ainirobot.coreservice.action.download_map.state.BaseDownloadMapPkgState;
import com.ainirobot.coreservice.action.download_map.state.CopyImportMapState;
import com.ainirobot.coreservice.action.download_map.state.DownloadMapExtraState;
import com.ainirobot.coreservice.action.download_map.state.DownloadMapPkgState;
import com.ainirobot.coreservice.action.download_map.state.DownloadSucceedState;
import com.ainirobot.coreservice.action.download_map.state.GetCloudMapPlaceListState;
import com.ainirobot.coreservice.action.download_map.state.GetMapPosSeparateState;
import com.ainirobot.coreservice.action.download_map.state.ParsePlaceListState;
import com.ainirobot.coreservice.action.download_map.state.ParseRoadDataState;
import com.ainirobot.coreservice.action.download_map.state.SetImportMapState;
import com.ainirobot.coreservice.action.download_map.state.UnzipMapFileState;
import com.ainirobot.coreservice.action.download_map.state.UpdatePlaceListToNaviState;
import com.ainirobot.coreservice.action.download_map.state.UpdatePlaceListToNetState;

public enum DownloadMapStateEnum {

    DOWNLOAD_MAP_PKG(new DownloadMapPkgState()),//下载地图整包
    UNZIP_MAP_FILE(new UnzipMapFileState()),//解压地图包
    COPY_IMPORT_MAP_FILE(new CopyImportMapState()),//**导入地图**，地图文件拷贝到地图目录
    PARSE_PLACE_LIST(new ParsePlaceListState()),//解析地图全部信息数据（本地place.json点位，mapInfo.json）
    PARSE_ROAD_DATA(new ParseRoadDataState()),//解析地图信息（不包括点位，地图工具选择“不覆盖本地点位”）
    GET_MAP_POS_SEPARATE(new GetMapPosSeparateState()),//是否点图分离，非点图分离则直接进入上报本地点位到云端
    GET_CLOUD_MAP_PLACE_LIST(new GetCloudMapPlaceListState()),//获取云端分离点位信息
    UPDATE_PLACE_LIST_TO_NAVI(new UpdatePlaceListToNaviState()),//更新分离点位到本地
    UPDATE_PLACE_LIST_TO_NET(new UpdatePlaceListToNetState()),//上报本地点位到云端
    DOWNLOAD_MAP_EXTRA(new DownloadMapExtraState()),//下载地图扩展包（视觉图原始文件）
    SET_IMPORT_MAP_SATE(new SetImportMapState()),//**导入地图**，地图上传状态-未上传，uuid-置空
    DOWNLOAD_SUCCEED(new DownloadSucceedState());

    private BaseDownloadMapPkgState downloadMapPkgState;

    DownloadMapStateEnum(BaseDownloadMapPkgState downloadMapPkgState) {
        this.downloadMapPkgState = downloadMapPkgState;
    }

    public BaseDownloadMapPkgState getDownloadMapPkgState() {
        return downloadMapPkgState;
    }
}
