/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.client;

import android.content.ComponentName;
import android.content.Intent;

public class IntentUtil {
    public static Intent createExplicitIntent(String pkgName, String className, String action) {
        ComponentName component = new ComponentName(pkgName, className);
        Intent intent = new Intent(action);
        intent.setComponent(component);
        return intent;
    }

    public static Intent createExplicitIntent(String className, String action) {
        ComponentName component = new ComponentName(Definition.CORE_PACKAGE_NAME, className);
        Intent intent = new Intent(action);
        intent.setComponent(component);
        return intent;
    }
}
