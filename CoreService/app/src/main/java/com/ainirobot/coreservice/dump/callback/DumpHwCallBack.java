package com.ainirobot.coreservice.dump.callback;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.util.Log;

import com.ainirobot.coreservice.IInspectCallBack;
import com.ainirobot.coreservice.bean.Command;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.hardware.HWService;
import com.ainirobot.coreservice.client.input.DumpApi;
import com.ainirobot.coreservice.config.ServiceConfig;
import com.ainirobot.coreservice.dump.CommandRegistry;
import com.ainirobot.coreservice.dump.DataDumpService;
import com.ainirobot.coreservice.dump.DumpDef;
import com.ainirobot.coreservice.dump.manager.DataDumpManager;

import java.util.List;

/**
 * Handle command from other module and service.
 * Created by Orion on 2020/5/26.
 */
public class DumpHwCallBack extends HWService {
    private static final String TAG = "DumpHwCallBack";

    private static final int EVENT_CMD = 1;
    private static final String KEY_CMD_TYPE = "cmdType";
    private static final String KEY_PARAMS = "params";

    private Context mContext;
    private DataDumpService mService;
    private Handler mCommandHandler;
    private DataDumpManager mDumpManager;
    private DumpApi mDumpApi;

    private class CommandHandler extends Handler {

        CommandHandler(Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(android.os.Message msg) {
            switch (msg.what) {
                case EVENT_CMD:
                    Bundle bundle = msg.getData();
                    String cmdType = bundle.getString(KEY_CMD_TYPE);
                    String params = bundle.getString(KEY_PARAMS);

                    try {
                        handleAsyncCommand(cmdType, params);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    break;

                default:
                    break;
            }
        }
    }

    public DumpHwCallBack(DataDumpService service) {
        this.mContext = service.getApplicationContext();
        this.mService = service;
        this.mDumpManager = service.getDataDumpManager();
        this.mDumpApi = service.getDumpApi();

        HandlerThread commandThread = new HandlerThread("CommandThread:DataDump");
        commandThread.start();
        mCommandHandler = new CommandHandler(commandThread.getLooper());
    }

    @Override
    public List<Command> onMount(String serviceName, ServiceConfig config) {
        Log.d(TAG, "DataDumpService mount : " + (config == null ? "null" : config.toString()));
        return CommandRegistry.getDumpCommands();
    }

    /**
     * Start inspect
     *
     * @param callBack inspect result callback
     */
    @Override
    public void onInspectStart(IInspectCallBack callBack) {

    }

    /**
     * Start upgrade hardware function
     *
     * @param subsystem
     * @param params    upgrade params
     * @return
     */
    @Override
    public boolean onUpgrade(String subsystem, String params) {
        return false;
    }

    /**
     * Reset hardware status
     */
    @Override
    public void onReset() {

    }

    @Override
    public void onAsyncCommand(String type, String params, String language) {

    }

    @Override
    public String onSyncCommand(String type, String params, String language) {
        return null;
    }

    @Override
    public String getBoardName() {
        return null;
    }

    @Override
    public boolean isNeedUpgrade(String subsystem, String version) {
        return false;
    }

    @Override
    public String getVersion(String subsystem) {
        return null;
    }

    /**
     * 其它模块以指令形式控制数据采集
     *
     * @param cmdType
     * @param cmdParam
     * @return
     */
    public boolean handleAsyncCommand(String cmdType, String cmdParam) {
        Log.d(TAG, "cmdType = " + cmdType + ", cmdParam = " + cmdParam);
        switch (cmdType) {
            case Definition.CMD_DUMP_START_RECORD:
                return startDump(DumpDef.TYPE_VISION_RECORD, cmdType, cmdParam);
            case Definition.CMD_DUMP_STOP_RECORD:
                return stopDump(DumpDef.TYPE_VISION_RECORD, cmdType);
            case Definition.CMD_DUMP_GET_KEY_FRAME:
                return startDump(DumpDef.TYPE_KEY_FRAME, cmdType, cmdParam);
            default:
                return false;
        }
    }

    /**
     * 开始采集
     *
     * @param dataType 数据类型
     * @param cmdType  指令类型
     * @param cmdParam 指令参数
     * @return
     */
    private boolean startDump(String dataType, String cmdType, String cmdParam) {
        if (mDumpManager == null) {
            return false;
        }
        boolean result = mDumpManager.start(dataType, cmdParam);
        mDumpApi.sendAsyncResponse(cmdType,
                result ? Definition.RESULT_SUCCEED : Definition.RESULT_FAILED, "");
        return result;
    }

    /**
     * 停止采集
     *
     * @param dataType 数据类型
     * @param cmdType  指令类型
     * @return
     */
    private boolean stopDump(String dataType, String cmdType) {
        if (mDumpManager == null) {
            return false;
        }
        boolean result = mDumpManager.stop(dataType);
        mDumpApi.sendAsyncResponse(cmdType,
                result ? Definition.RESULT_SUCCEED : Definition.RESULT_FAILED, "");
        return result;
    }
}
