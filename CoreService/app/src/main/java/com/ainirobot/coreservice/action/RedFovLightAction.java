package com.ainirobot.coreservice.action;

import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.BaseBean;
import com.ainirobot.coreservice.core.LocalApi;

import java.io.FileNotFoundException;
import java.io.PrintStream;
import java.util.ArrayList;

public class RedFovLightAction extends AbstractAction<BaseBean> {
    static final String TAG = "RedFovLightAction";

    protected RedFovLightAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_RED_FOV_LIGHT, resList);
    }

    @Override
    protected boolean startAction(BaseBean parameter, LocalApi api) {
        Log.i(TAG, "startAction");
        try (PrintStream printStream = new PrintStream("/sys/kernel/r_led_ctrl/value")) {
            printStream.print(1);
            return true;
        } catch (FileNotFoundException e) {
            Log.w(TAG, "fail to open devnode", e);
            return false;
        }
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        Log.i(TAG, "stopAction");
        try (PrintStream printStream = new PrintStream("/sys/kernel/r_led_ctrl/value")) {
            printStream.print(0);
            return true;
        } catch (FileNotFoundException e) {
            Log.w(TAG, "stopAction:: fail to open devnode", e);
            return false;
        }
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return null;
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return null;
    }

    @Override
    protected boolean verifyParam(BaseBean param) {
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        return true;
    }
}
