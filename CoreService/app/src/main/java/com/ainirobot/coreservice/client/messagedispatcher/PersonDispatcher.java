/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.ainirobot.coreservice.client.messagedispatcher;

import com.ainirobot.coreservice.client.person.PersonListener;
import com.ainirobot.coreservice.listener.IPersonListener;

public class PersonDispatcher extends IPersonListener.Stub {

    private DispatchHandler mDispatchHandler;
    private PersonListener mListener;
    private static PersonDispatcher mPersonDispatcher;

    private PersonDispatcher(DispatchHandler dispatchHandler, PersonListener listener) {
        mDispatchHandler = dispatchHandler;
        mListener = listener;
    }

    private void changeListener(PersonListener listener) {
        mListener = listener;
    }

    static synchronized PersonDispatcher obtain(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dispatchHandler, PersonListener listener) {
        if (mPersonDispatcher == null) {
            mPersonDispatcher = new PersonDispatcher(dispatchHandler, listener);
        } else {
            mPersonDispatcher.changeListener(listener);
        }
        return mPersonDispatcher;
    }

    @Override
    public void personChanged() {
        PersonListener listener = mListener;
        DispatchHandler handler = mDispatchHandler;
        if (listener != null && handler != null) {
            PersonMessage personMessage = new PersonMessage(listener);
            handler.sendMessage(handler.obtainMessage(MessageDispatcher.MSG_PERSON, personMessage));
        }
    }
}
