package com.ainirobot.coreservice.bi.report;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

public class RadarReport extends BaseBiReport {

    private static final String TABLE_NAME = "base_robot_radar_temporary";
    private static final String TYPE = "type";
    private static final String EVENT = "event";
    private static final String CTIME = "ctime";

    public static final int TYPE_STANDBY = 1;

    public static final int EVENT_OPEN = 1;
    public static final int EVENT_CLOSE = 2;


    public RadarReport() {
        super(TABLE_NAME);
    }

    public RadarReport addEvent(int event) {
        addData(EVENT, event);
        return this;
    }

    public RadarReport addType(int type) {
        addData(TYPE, type);
        return this;
    }

    @Override
    public void report() {
        addData(CTIME, System.currentTimeMillis());
        super.report();
    }
}
