package com.ainirobot.coreservice.action;

import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.GongFuBean;
import com.ainirobot.coreservice.core.LocalApi;

import java.util.ArrayList;
import java.util.List;

public class GongFuAction extends AbstractAction<GongFuBean> {

    private static final String TAG = "GongFuAction";

    private static final int MSG_FOOT = 1;
    private static final int MSG_HEAD = 2;
    private static final int MSG_REDAY_STOP = 3;

    private static final String ANGLE = "angle";
    private static final String DISTANCE = "distance";
    private static final String SPEED = "speed";
    private static final String SPEED_HORIZONTAL = "speed_horizontal";
    private static final String SPEED_VERTICAL = "speed_vertical";
    private static final String ANGLE_HORIZONTAL = "angle_horizontal";
    private static final String ANGLE_VERTICAL = "angle_vertical";

    private Handler mHandler;

    private int mReqId = -1;
    private List<GongFuBean.HeadAction> mHeadActionList;
    private List<GongFuBean.FootAction> mFootActionList;
    private int mActionsType;


    protected GongFuAction(ActionManager manager, int id, String[] resList) {
        super(manager, id, resList);

    }

    protected GongFuAction(ActionManager manager, String[] resList){
        super(manager, Definition.ACTION_GONGFU, resList);
        initHandler();
    }

    private void initHandler(){
        HandlerThread thread = new HandlerThread(GongFuAction.class.getSimpleName());
        thread.start();
        mHandler = new Handler(thread.getLooper()){
            @Override
            public void handleMessage(Message msg) {

                Log.i(TAG, "action come on, action type: "+ msg.what);

                Bundle bundle = (Bundle) msg.obj;
                switch (msg.what){

                    case MSG_HEAD:
                        moveHead(bundle.getInt(ANGLE_HORIZONTAL), bundle.getInt(ANGLE_VERTICAL),
                                bundle.getInt(SPEED_HORIZONTAL), bundle.getInt(SPEED_VERTICAL));
                        break;

                    case MSG_FOOT:
                        moveFoot(bundle.getFloat(DISTANCE), bundle.getFloat(ANGLE),
                                bundle.getFloat(SPEED));
                        break;

                    case MSG_REDAY_STOP:
                        if(!(mHandler.hasMessages(MSG_HEAD) || mHandler.hasMessages(MSG_FOOT))){
                            Log.i(TAG, "this is the last action, stop");
                            mHandler.removeMessages(MSG_REDAY_STOP);
                            onResult(Definition.RESULT_OK, "do ok");
                        } else {
                            Log.i(TAG, "playing...");
                            mHandler.sendEmptyMessageDelayed(MSG_REDAY_STOP, 2000);
                        }
                        break;

                    default:

                        break;
                }
            }
        };
    }

    @Override
    protected boolean startAction(GongFuBean parameter, LocalApi api) {
        if((mHeadActionList == null || mHeadActionList.size() < 1)
                && (mFootActionList == null || mFootActionList.size() < 1)){
            return false;
        }

        if(mHeadActionList != null){
            Log.i(TAG, "head action list size:"+mHeadActionList.size());
        }

        if(mFootActionList != null){
            Log.i(TAG, "foot action list size:"+mFootActionList.size());
        }

        startPlay();
        return true;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        mHeadActionList = null;
        mFootActionList = null;
        mHandler.removeMessages(MSG_HEAD);
        mHandler.removeMessages(MSG_FOOT);
        mHandler.removeMessages(MSG_REDAY_STOP);
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        return false;
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return new ArrayList<StopCommandList.StopCommand>() {{
//            add(new StopCommandList.StopCommand(Definition.CMD_NAVI_STOP_MOVE, null));
//            add(new StopCommandList.StopCommand(Definition.CMD_HEAD_MOVE_HEAD, null));
        }};
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return null;
    }

    @Override
    protected boolean verifyParam(GongFuBean param) {
        Log.i(TAG, "varify param");
        if(param == null){
            Log.i(TAG, "param is null, return!!");
            return false;
        }
        mActionsType = param.getmGongFuTyep();
        mHeadActionList = param.getHeadActionList();
        mFootActionList = param.getFootActionList();
        mReqId = param.getReqId();
        return true;
    }

    private void startPlay(){

        Log.i(TAG, "add action to queen ");

        if(mFootActionList != null && mFootActionList.size() > 0){
            for(GongFuBean.FootAction action : mFootActionList){

                Log.i(TAG, action.toString());

                Message msg = Message.obtain();
                msg.what = MSG_FOOT;
                Bundle bundle = new Bundle();
                bundle.putFloat(ANGLE, action.getAngle());
                bundle.putFloat(DISTANCE, action.getDistance());
                bundle.putFloat(SPEED, action.getSpeed());
                msg.obj = bundle;

                mHandler.sendMessageDelayed(msg, action.getBeginTime());
            }
        }

        if(mHeadActionList != null && mHeadActionList.size() > 0) {

            for(GongFuBean.HeadAction action : mHeadActionList){

                Log.i(TAG, action.toString());

                Message msg = Message.obtain();
                msg.what = MSG_HEAD;
                Bundle bundle = new Bundle();
                bundle.putInt(SPEED_HORIZONTAL, action.getSpeed_horizontal());
                bundle.putInt(SPEED_VERTICAL, action.getSpeed_vertical());
                bundle.putInt(ANGLE_HORIZONTAL, action.getAngle_horizontal());
                bundle.putInt(ANGLE_VERTICAL, action.getAngle_vertical());
                msg.obj = bundle;

                mHandler.sendMessageDelayed(msg, action.getBeginTime());
            }
        }

        mHandler.sendEmptyMessageDelayed(MSG_REDAY_STOP, 1000);
        Log.i(TAG, "add action to queen done!!");
    }

    private void moveHead(int angle_h, int angle_v, int speed_h, int speed_v){
        Log.i(TAG, "move head angle_h:"+angle_h+" angle_v:"+angle_v+" speed_h:"+speed_h+" speed_v:"+speed_v);
        mApi.moveHead(mReqId, Definition.JSON_HEAD_RELATIVE, Definition.JSON_HEAD_RELATIVE, angle_h, angle_v, speed_h, speed_v);
    }

    private void moveFoot(float distance, float angle, float angularSpeed){
//        mApi.motionArc(mReqId, distance, angle, angularSpeed);

        Log.i(TAG, "move foot distance:"+distance+" angle:"+angle+" speed:"+angularSpeed);
        if(angle > 0f){
            mApi.turnLeft(mReqId, angularSpeed, Math.abs(angle));
        }else{
            mApi.turnRight(mReqId, angularSpeed, Math.abs(angle));
        }
    }

}
