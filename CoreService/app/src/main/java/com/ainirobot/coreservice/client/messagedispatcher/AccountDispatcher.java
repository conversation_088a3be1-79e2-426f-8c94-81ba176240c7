/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.ainirobot.coreservice.client.messagedispatcher;

import com.ainirobot.coreservice.client.account.AccountListener;
import com.ainirobot.coreservice.listener.IAccountListener;

public class AccountDispatcher extends IAccountListener.Stub {

    private DispatchHandler mDispatchHandler;
    private AccountListener mListener;
    private static AccountDispatcher mAccountDispatcher;

    private AccountDispatcher(DispatchHandler dispatchHandler, AccountListener listener) {
        mDispatchHandler = dispatchHandler;
        mListener = listener;
    }

    private void changeListener(AccountListener listener) {
        mListener = listener;
    }

    static synchronized AccountDispatcher obtain(DispatchHandler dispatchHandler, AccountListener listener) {
        if (mAccountDispatcher == null) {
            mAccountDispatcher = new AccountDispatcher(dispatchHandler, listener);
        } else {
            mAccountDispatcher.changeListener(listener);
        }
        return mAccountDispatcher;
    }

    @Override
    public void memberUpdate() {
        AccountListener listener = mListener;
        DispatchHandler handler = mDispatchHandler;
        if (listener != null && handler != null) {
            AccountMessage accountMessage = new AccountMessage(listener);
            handler.sendMessage(handler.obtainMessage(MessageDispatcher.MSG_ACCOUNT, accountMessage));
        }
    }
}
