/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.client.upload.bi;

import android.util.Log;

import com.ainirobot.coreservice.client.Definition;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class BaseBiReport {
    protected final static String TAG = "BIReport";
    private static final String DEFAULT_VALUE = "";

    private String tableName;
    private ConcurrentHashMap<String, String> reportDatas = new ConcurrentHashMap<>(8);

    public BaseBiReport(String tableName) {
        this.tableName = tableName;
    }

    private BaseBiReport() {
    }

    public void addData(String key, Object value) {
        reportDatas.put(key, value == null ? DEFAULT_VALUE : String.valueOf(value));
    }

    public void clearReportDatas() {
        reportDatas.clear();
    }

    public void report() {
        reportNormalV();
    }

    /**
     * 普通业务埋点，默认级别Verbose
     */
    protected void reportNormalV() {
        startBiReport(Definition.BI_TYPE_NORMAL, Definition.BI_LEVEL_VERBOSE);
    }

    /**
     * 调用链埋点，详细日志
     */
    protected void reportCallChainV() {
        startBiReport(Definition.BI_TYPE_CALL_CHAIN, Definition.BI_LEVEL_VERBOSE, true);
    }

    /**
     * 调用链埋点，调式日志
     */
    protected void reportCallChainD() {
        startBiReport(Definition.BI_TYPE_CALL_CHAIN, Definition.BI_LEVEL_DEBUG, true);
    }

    private void startBiReport(String type, int level) {
        if (reportDatas.size() > 0) {
            Log.i(TAG, "start bi report: type=" + type + " level=" + level
                    + " " + tableName + "-" + reportDatas.toString());
            BiReport.report(type, level, tableName, mapToStr(reportDatas));
        }
    }

    private void startBiReport(String type, int level, boolean notForce) {
        if (reportDatas.size() > 0) {
            Log.i(TAG, "start bi report: type=" + type + " level=" + level
                    + " notForce=" + notForce + " " + tableName + "-" + reportDatas.toString());
            BiReport.report(type, level, tableName, mapToStr(reportDatas));
        }
    }

    private String mapToStr(Map<String, String> dataMap) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : dataMap.entrySet()) {
            sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        if (sb.length() > 1) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }
}