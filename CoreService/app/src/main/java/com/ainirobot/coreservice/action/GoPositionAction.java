/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.action;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.StopCommandList.IStopCommandChecker;
import com.ainirobot.coreservice.action.StopCommandList.StopCommand;
import com.ainirobot.coreservice.bi.report.SdkNavigateFinishReport;
import com.ainirobot.coreservice.bi.report.SdkNavigateStartReport;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.actionbean.GoPositionBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.robotlog.RobotLog;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Timer;
import java.util.TimerTask;

public class GoPositionAction extends AbstractAction<GoPositionBean> {
    private static final String TAG = "GoPositionAction";

    //if 20 seconds moving distance less than 0.1 meters is that navigation failed
    private static final double MIN_DISTANCE = 0.1; //unit meter
    private static final long DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT = 3600 * 1000;

    private enum NavigationState {
        IDLE, PREPARE, NAVIGATION, STOPPED
    }

    private int mReqId;
    private Pose mDestPose;
    private boolean mAdjustAngle;
    /**到达目标点的区域半径，如果未到目标点正中心位置，到达周边距离点的范围。默认0.0D，即必须到达中心点 */
    private double mDestinationRange = 0.0D;
    private float mTheta;
    private double mLinearSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED;
    private double mAngularSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED;
    private Pose mCurrentPose;
    private double mCoordinateDeviation;
    private long mTimeout;
    private String mPoseListener;
    private Gson mGson;
    private Pose mFlagPose;
    private NavigationState mStatus;
    private SdkNavigateStartReport navigateStartReport;
    private SdkNavigateFinishReport navigateFinishReport;
    /**多机模式等待超时时长，默认3600s **/
    private long mWaitTimeout;
    /**是否是在多机避障状态，如果是则停止处理机器N秒不动退出导航的逻辑 **/
    private volatile boolean isMultipleWaitStatus = false;
    private Timer mWaitTimer;
    private int mTaskPriority;
    private double mLinearAcceleration;
    private double mAngularAcceleration;
    private int mStartModeLevel;
    private int mBrakeModeLevel;
    private double mObsDistance;
    private double mPosTolerance;
    private double mAngleTolerance;
    private NavigationResetHeadUtils mNavigationResetHeadUtils;
    private int mRoadMode;
    private boolean mIsNeedEstimate; // 远程建图时，进行pose导航可以不用判断是否定位成功

    protected GoPositionAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_GO_POSITION, resList);
        mGson = new Gson();

        navigateStartReport = new SdkNavigateStartReport();
        navigateFinishReport = new SdkNavigateFinishReport();
        mNavigationResetHeadUtils = new NavigationResetHeadUtils(mApi, mManager.getCoreService().getHeadMoveManager());
    }

    @Override
    protected boolean startAction(GoPositionBean parameter, LocalApi api) {
        if (verifyParam(parameter)) {
            prepareNavigation(parameter);
            return true;
        }
        return false;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        RobotLog.d("Stop navigation action");
        updateMultipleRobotWaitingStatus(false);
        cancelWaitTimer();
        mStatus = NavigationState.STOPPED;
        unregisterStatusListener(mPoseListener);
        mFlagPose = null;
        mNavigationResetHeadUtils.stop();
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_IS_ESTIMATE:
                processPoseEstimate(result);
                return true;
            case Definition.CMD_NAVI_GET_POSITION:
            case Definition.CMD_NAVI_GET_POSITION_WITHOUT_ESTIMATE:
                processGetCurrentPosition(result);
                return true;
            case Definition.CMD_NAVI_GO_POSITION:
                processNavigationResult(result, extraData);
                return true;
            default:
                return mNavigationResetHeadUtils.onCmdResponse(cmdId, command, result, extraData);
        }
    }

    private void processNavigationResult(String result, String extraData) {
        Log.d(TAG, "processNavigationResult : " + result);

        navigateFinishReport.fillData().report();

        if (Definition.NAVIGATION_OK.equals(result)) {
            if (mStatus != NavigationState.STOPPED){
                onResult(Definition.RESULT_OK, result);
            }else {
                onResult(Definition.RESULT_STOP, result);
            }
            return;
        }
        if (Definition.NAVIGATION_CANCELED.equals(result)) {
            onResult(Definition.RESULT_STOP, result);
            return;
        }
        processError(Definition.ERROR_NAVIGATION_FAILED, "Navigation failed : " + result, extraData);
    }

    private void processError(int errorCode, String errorMessage, String extraData) {
        Log.d(TAG, "processError : " + errorCode + " " + errorMessage + " " + extraData);
        super.onError(errorCode, errorMessage, extraData);
    }

    @Override
    public boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_GO_POSITION:
                processNavigationStatus(status);
                return true;
            default:
                return mNavigationResetHeadUtils.cmdStatusUpdate(cmdId, command, status, extraData);
        }
    }

    private void prepareNavigation(GoPositionBean params) {
        Log.d(TAG, "Start go position action");
        mStatus = NavigationState.PREPARE;
        mReqId = params.getReqId();
        mDestPose = params.getPose();
        mCoordinateDeviation = params.getCoordinateDeviation();
        mAdjustAngle = params.getIsAdjustAngle();
        mDestinationRange = params.getDestinationRange();
        mLinearSpeed = params.getLinearSpeed();
        mAngularSpeed = params.getAngularSpeed();
        mLinearAcceleration = params.getLinearAcceleration();
        mAngularAcceleration = params.getAngularAcceleration();
        mStartModeLevel = params.getStartModeLevel();
        mBrakeModeLevel = params.getBrakeModeLevel();
        mObsDistance = params.getObsDistance();
        long timeout = params.getTime();
        mTimeout = ((timeout == 0) ? Long.MAX_VALUE : timeout);
        long waitTimeout = params.getMultipleWaitTime();
        mWaitTimeout = (waitTimeout <= 0) ? DEFAULT_MULTI_ROBOT_AVOID_WAITING_TIMEOUT: waitTimeout;
        isMultipleWaitStatus = false;
        mTaskPriority = params.getPriority();
        mPosTolerance = params.getPosTolerance();
        mAngleTolerance = params.getAngleTolerance();
        mRoadMode = params.getRoadMode();
        mIsNeedEstimate = params.getIsNeedEstimate();
        mFlagPose = null;
        checkPoseEstimate();
        registerPoseListener();
    }

    private void checkPoseEstimate() {
        if (!mIsNeedEstimate) {
            Log.d(TAG, "do not need estimate, because auto remote mapping in progress");
            mApi.getPositionWithoutEstimate(mReqId);
            return;
        }
        Log.d(TAG, "Check pose estimate");
        mApi.isRobotEstimate(mReqId);
    }

    private void processNavigationStatus(String status) {
        switch (status) {
            case Definition.NAVIGATION_OCCLUDED:
                onStatusUpdate(Definition.STATUS_GOAL_OCCLUDED, "The target is occluded");
                break;

            case Definition.NAVIGATION_OCCLUDED_END:
                onStatusUpdate(Definition.STATUS_GOAL_OCCLUDED_END, "The occluded end");
                break;

            case Definition.NAVIGATION_AVOID:
                onStatusUpdate(Definition.STATUS_NAVI_AVOID, "The local path search failed");
                break;

            case Definition.NAVIGATION_AVOID_END:
                onStatusUpdate(Definition.STATUS_NAVI_AVOID_END, "The local path search succeed");
                break;

            case Definition.NAVIGATION_OUT_MAP:
                onStatusUpdate(Definition.STATUS_NAVI_OUT_MAP, "The goal is out of map or more dangerous");
                break;

            case Definition.NAVIGATION_GLOBAL_PATH_FAILED:
                onStatusUpdate(Definition.STATUS_NAVI_GLOBAL_PATH_FAILED, "The global is path search failed");
                break;

            case Definition.NAVIGATION_OBSTACLES_AVOID:
                onStatusUpdate(Definition.STATUS_NAVI_OBSTACLES_AVOID, "obstacles avoid");
                break;

            case Definition.NAVIGATION_STARTED:
                onStatusUpdate(Definition.STATUS_START_NAVIGATION, "Navigation has been started");
                break;

            case Definition.NAVIGATION_MULTI_ROBOT_WAITING:
                updateMultipleRobotWaitingStatus(true);
                startWaitingTimer();
                onStatusUpdate(Definition.STATUS_NAVI_MULTI_ROBOT_WAITING, "Multiple robots are waiting to avoid obstacles");
                break;

            case Definition.NAVIGATION_MULTI_ROBOT_WAITING_END:
                updateMultipleRobotWaitingStatus(false);
                cancelWaitTimer();
                onStatusUpdate(Definition.STATUS_NAVI_MULTI_ROBOT_WAITING_END, "Multiple robots passageways returned to normal");
                break;

            case Definition.NAVIGATION_SET_PRIORITY_FAILED:
                onStatusUpdate(Definition.STATUS_NAVI_SET_PRIORITY_FAILED, "Current navigation task priority setting failed!");
                break;

            default:
                Log.i(TAG, "Command status: " + status +  " doesn't be handled");
                break;
        }
    }

    private void processPoseEstimate(String result) {
        if (!"true".equals(result)) {
            onError(Definition.ERROR_NOT_ESTIMATE, "Not pose estimate");
            return;
        }
        mApi.getPosition(mReqId);
    }

    private void processGetCurrentPosition(String result){
        mNavigationResetHeadUtils.checkResetHeadState(new NavigationResetHeadUtils.ResetHeadListener() {
            @Override
            public void onResetHeadSuccess() {
                mCurrentPose = new Pose();
                try {
                    JSONObject object = new JSONObject(result);
                    mCurrentPose.setX((float) object.optDouble("px"));
                    mCurrentPose.setY((float) object.optDouble("py"));
                    mCurrentPose.setTheta((float) object.optDouble("theta"));
                } catch (JSONException e) {
                    e.printStackTrace();
                    mCurrentPose = null;
                }

                goToPosition(mCurrentPose);
                mNavigationResetHeadUtils.startHeadStatusListener(mReqId);
            }

            @Override
            public void onResetHeadFailed(int errorCode, String errorString) {
                onError(errorCode, errorString);
            }
        }, mReqId);
    }

    private void goToPosition(Pose pose){
        Log.d(TAG, "goToPosition pose:" + pose);
        if (pose == null){
            onError(Definition.ERROR_NAVIGATION_FAILED, "Get current pose fail");
        }else {
            double distance = mCurrentPose.getDistance(mDestPose);
            if (distance < mCoordinateDeviation) {
                onResult(Definition.ERROR_IN_DESTINATION, "Already at this pose");
            } else {
                mStatus = NavigationState.NAVIGATION;
                mApi.goPosition(mReqId, mDestPose,
                        mLinearSpeed, mAngularSpeed, mAdjustAngle, false,
                        mDestinationRange, mTaskPriority, mLinearAcceleration, mAngularAcceleration,
                        mStartModeLevel, mBrakeModeLevel, mObsDistance, mPosTolerance, mAngleTolerance, mRoadMode);
                navigateStartReport.fillData("Position")
                        .report();
            }

        }
    }

    private void registerPoseListener() {
        mPoseListener = registerStatusListener(Definition.STATUS_POSE, new StatusListener() {
            @Override
            public void onStatusUpdate(String data) {
                onPoseUpdate(data);
            }
        });
    }

    private synchronized void onPoseUpdate(String data) {
        if (TextUtils.isEmpty(data) || mStatus != NavigationState.NAVIGATION) {
            return;
        }
        Pose pose = mGson.fromJson(data, Pose.class);
        if (isAborted(pose)) {
            Log.d(TAG, "Pose timeout : " + mFlagPose.toString() + "   "
                    + pose.toString() + "  " + mStatus);
            onError(Definition.ERROR_DESTINATION_CAN_NOT_ARRAIVE, "Timeout");
        }
    }

    private boolean isAborted(Pose pose) {
        double distance = pose.getDistance(mFlagPose);
        if (!isMultipleWaitStatus && Double.compare(distance, MIN_DISTANCE) < 0) {
            long movingTime = pose.getTime() - mFlagPose.getTime();
            if (movingTime > mTimeout) {
                return true;
            }
        } else {
            mFlagPose = pose;
        }
        return false;
    }

    /**
     * 更新多机避障状态，如果是多机避障状态，则机器人超时等待策略需忽略
     * @param isWaiting 是否正在多机避障等待状态
     */
    private void updateMultipleRobotWaitingStatus(boolean isWaiting){
        isMultipleWaitStatus = isWaiting;
    }

    private void startWaitingTimer() {
        cancelWaitTimer();
        mWaitTimer = new Timer();
        mWaitTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (mStatus != NavigationState.NAVIGATION) {
                    return;
                }
                onError(Definition.ERROR_MULTI_ROBOT_WAITING_TIMEOUT, "multiple robot wait timeout");
            }
        }, mWaitTimeout);
    }

    private void cancelWaitTimer() {
        if (mWaitTimer != null) {
            mWaitTimer.cancel();
            mWaitTimer = null;
        }
    }

    @Override
    public boolean verifyParam(GoPositionBean params) {
        if (params != null && params.getPose() != null) {
            return true;
        }
        return false;
    }

    @Override
    protected ArrayList<StopCommand> getStopCommands() {
        return new ArrayList<StopCommand>() {{
            add(new StopCommand(Definition.CMD_NAVI_STOP_NAVIGATION, null));
        }};
    }

    @Override
    protected IStopCommandChecker getStopCommandChecker() {
        return new IStopCommandChecker() {
            @Override
            public boolean executeSuccess(String command, String result) {
                RobotLog.d("Leading stop check : " + command);
                switch (command) {
                    case Definition.CMD_NAVI_STOP_NAVIGATION:
                        return true;
                    default:
                        return false;
                }
            }
        };
    }
}
