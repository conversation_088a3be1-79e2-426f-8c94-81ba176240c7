/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.client.messagedispatcher;

import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.coreservice.listener.ITextListener;

public class TextDispatcher extends ITextListener.Stub implements IRecyclable {
    private static final String TAG = TextDispatcher.class.getSimpleName();
    private static RecyclablePool<TextDispatcher> sPool = new RecyclablePool<>();

    private DispatchHandler mDispatchHandler;
    private TextListener mListener;

    private TextDispatcher(DispatchHandler dispatchHandler, TextListener listener) {
        mDispatchHandler = dispatchHandler;
        mListener = listener;
    }

    static TextDispatcher obtain(DispatchHandler dispatchHandler, TextListener listener) {
        TextDispatcher textDispatcher = sPool.obtain();

        if (textDispatcher == null) {
            textDispatcher = new TextDispatcher(dispatchHandler, listener);
        } else {
            textDispatcher.mDispatchHandler = dispatchHandler;
            textDispatcher.mListener = listener;
        }
        return textDispatcher;
    }

    @Override
    public void onStart() {
        TextListener listener = mListener;
        DispatchHandler handler = mDispatchHandler;
        if (listener != null && handler != null) {
            TextMessage textMessage = new TextMessage(TextMessage.TYPE_START, listener);
            handler.sendMessage(handler
                    .obtainMessage(MessageDispatcher.MSG_TEXT, textMessage));
        }
    }

    @Override
    public void onStop() {
        TextListener listener = mListener;
        DispatchHandler handler = mDispatchHandler;
        if (listener != null && handler != null) {
            TextMessage textMessage = new TextMessage(TextMessage.TYPE_STOP, listener);
            handler.sendMessage(handler
                    .obtainMessage(MessageDispatcher.MSG_TEXT, textMessage));
        }
        sPool.recycle(this);
    }

    @Override
    public void onError() {
        TextListener listener = mListener;
        DispatchHandler handler = mDispatchHandler;
        if (listener != null && handler != null) {
            TextMessage textMessage = new TextMessage(TextMessage.TYPE_ERROR, listener);
            handler.sendMessage(handler
                    .obtainMessage(MessageDispatcher.MSG_TEXT, textMessage));
        }
        sPool.recycle(this);
    }

    @Override
    public void onComplete() {
        TextListener listener = mListener;
        DispatchHandler handler = mDispatchHandler;
        if (listener != null && handler != null) {
            TextMessage textMessage = new TextMessage(TextMessage.TYPE_COMPLETE, listener);
            handler.sendMessage(handler
                    .obtainMessage(MessageDispatcher.MSG_TEXT, textMessage));
        }
        sPool.recycle(this);
    }

    @Override
    public void onStreamComplete(String streamSid, String textSid) {
        TextListener listener = mListener;
        DispatchHandler handler = mDispatchHandler;
        if (listener != null && handler != null) {
            TextMessage textMessage = new TextMessage(TextMessage.TYPE_STREAM_COMPLETE, listener);
            textMessage.setStreamSid(streamSid);
            textMessage.setTextSid(textSid);
            handler.sendMessage(handler
                    .obtainMessage(MessageDispatcher.MSG_TEXT, textMessage));
        }
    }

    @Override
    public void recycle() {
        mDispatchHandler = null;
        mListener = null;
    }
}
