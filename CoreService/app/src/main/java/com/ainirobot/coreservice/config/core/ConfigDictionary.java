package com.ainirobot.coreservice.config.core;

public class ConfigDictionary {

    private static final String[] HEAD_CONFIG_CN =
            {"支持270度水平云台", "支持水平云台", "支持俯仰云台",
                    "默认水平角度", "默认俯仰角度", "俯仰最大角度",
                    "俯仰最小角度", "水平最大角度", "水平最小角度",
                    "270度水平最小角度", "最大速度", "默认速度", "俯仰角度起始坐标"};

    private static final String[] HEAD_CONFIG_EN =
            {"isSupportHorizontal270", "isSupportHorizontal", "isSupportVertical",
                    "defaultHorizontalAngle", "defaultVerticalAngle", "maxVerticalAngle",
                    "minVerticalAngle", "maxHorizontalAngle", "minHorizontalAngle",
                    "min270HorizontalAngle", "maxSpeed", "defaultSpeed", "verticalAngleOffset"};

    private static final String[] BATTERY_CONFIG_CN =
            {"低电值", "OTA低电值"};

    private static final String[] BATTERY_CONFIG_EN =
            {"lowBatteryValue", "otaLowBatteryValue"};

    private static final String[] ACCOUNT_CONFIG_CN =
            {"是否支持本地注册"};

    private static final String[] ACCOUNT_CONFIG_EN =
            {"isLocalRegisterEnable"};

    private static final String[] NAVIGATION_CONFIG_CN =
            {"回充方式", "底盘半径", "弹片距墙", "红外距墙"};

    private static final String[] NAVIGATION_CONFIG_EN =
            {"chargePointType", "naviRadius", "spring2WallDistance", "infrared2WallDistance"};

    private static final String[] PUBLIC_CONFIG_CN =
            {"语音ID", "系统语言", "地图兼容性配置"};

    private static final String[] PUBLIC_CONFIG_EN =
            {"clientId", "systemLanguage", "mapCompatibilityConfig"};

    public static String parseHeadConfig(String cnKey) {
        return parse(cnKey, HEAD_CONFIG_CN, HEAD_CONFIG_EN);
    }

    public static String parseBatteryConfig(String cnKey) {
        return parse(cnKey, BATTERY_CONFIG_CN, BATTERY_CONFIG_EN);
    }

    public static String parseAccountConfig(String cnKey) {
        return parse(cnKey, ACCOUNT_CONFIG_CN, ACCOUNT_CONFIG_EN);
    }

    public static String parseNavigationConfig(String cnKey) {
        return parse(cnKey, NAVIGATION_CONFIG_CN, NAVIGATION_CONFIG_EN);
    }

    public static String parsePublicConfig(String cnKey) {
        return parse(cnKey, PUBLIC_CONFIG_CN, PUBLIC_CONFIG_EN);
    }

    private static String parse(String cnKey, String[] cn, String[] en) {
        if (cnKey != null && cnKey.length() > 0 && cn.length == en.length) {
            for (int i = 0; i < cn.length; ++i) {
                if (cn[i].equals(cnKey)) {
                    return en[i];
                }
            }
        }

        return cnKey;
    }
}
