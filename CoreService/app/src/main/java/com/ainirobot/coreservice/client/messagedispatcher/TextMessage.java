/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.client.messagedispatcher;

import com.ainirobot.coreservice.client.listener.TextListener;

class TextMessage implements IMsgHandle {
    static final int TYPE_START = 1;
    static final int TYPE_STOP = 2;
    static final int TYPE_ERROR = 3;
    static final int TYPE_COMPLETE = 4;
    static final int TYPE_STREAM_COMPLETE = 5;

    private int mType;
    private TextListener mListener;
    private String mStreamSid;
    private String mTextSid;

    TextMessage(int type, TextListener listener) {
        mType = type;
        mListener = listener;
    }

    public void setStreamSid(String streamSid) {
        this.mStreamSid = streamSid;
    }

    public void setTextSid(String textSid) {
        this.mTextSid = textSid;
    }

    @Override
    public void handleMessage() {
        switch (mType) {
            case TYPE_START:
                mListener.onStart();
                break;
            case TYPE_STOP:
                mListener.onStop();
                break;
            case TYPE_ERROR:
                mListener.onError();
                break;
            case TYPE_COMPLETE:
                mListener.onComplete();
                break;
            case TYPE_STREAM_COMPLETE:
                mListener.onStreamComplete(mStreamSid, mTextSid);
                break;
        }
    }
}
