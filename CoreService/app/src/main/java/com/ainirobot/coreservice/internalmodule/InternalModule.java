/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.internalmodule;

import com.ainirobot.coreservice.core.InternalDef;
import com.ainirobot.robotlog.RobotLog;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;

public class InternalModule {

    private static final String TAG = "ModuleManager";

    private static HashMap<String, ArrayList<CmdModule>> mModuleHashMap;

    public static class CmdModule {
        public String cmdType;
        public String cmdParam;
        public boolean ifWaitingResult;
    }

    public static void initModuleMap() {
        //TODO: get module list from DB, analyze by "while function
        //while ()
//        parseCmdModule(InternalDef.MODULE_GET_WATER);
//        parseCmdModule(InternalDef.MODULE_GO_PLACE);
    }

    public static ArrayList<CmdModule> getCmdModule(String reqType) {
        return mModuleHashMap.get(reqType);
    }

    private static void parseCmdModule(String jsonString) {
        try {
            JSONObject jsonObject = new JSONObject(jsonString);
            String reqType = jsonObject.optString(InternalDef.REQ_TYPE);

            JSONArray jsonArray = jsonObject.getJSONArray(InternalDef.CMD_LIST);
            if (jsonArray == null) {
                RobotLog.e(TAG, "Parse module list error!");
                return;
            }

            ArrayList<CmdModule> listModule = new ArrayList<CmdModule>();
            for (int i = 0; i < jsonArray.length(); i++) {
                CmdModule cmdModule = new CmdModule();
                JSONObject cmdObject = jsonArray.getJSONObject(i);
                cmdModule.cmdType = cmdObject.optString(InternalDef.CMD_TYPE);
                cmdModule.cmdParam = cmdObject.optString(InternalDef.CMD_PARAM);
                cmdModule.ifWaitingResult =
                        (cmdObject.optInt(InternalDef.CMD_IF_WAITING_RESULT) == 1) ? true : false;
            }
            mModuleHashMap.put(reqType, listModule);
        } catch (JSONException e) {
            RobotLog.e(TAG, "Parse module list error!");
        }
    }
}