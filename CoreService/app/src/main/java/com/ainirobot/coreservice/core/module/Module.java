/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.core.module;

import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.IModuleCallback;
import com.ainirobot.coreservice.service.CoreService;

import java.util.HashMap;
import java.util.Map;

public class Module {
    private final String TAG = "Module";

    private final String mPackageName;
    protected CoreService mCore;
    private IModuleCallback mCallback;
    private ModuleClient mClient;
    private boolean isSuspend = false;
    private boolean isSystem = true;
    private State mState = State.IDLE;

    private Map<String, Boolean> mSystemStatus;

    public enum State{
        IDLE,BUSY
    }

    public enum HandleResult{
        NORMAL, INTERRUPT
    }

    /**
     * 当前App使用的语言
     */
    private String language;

    private int headVAngle = -1;

    public Module(CoreService core, String name) {
        this(core, name, false);
    }

    public Module(CoreService core, String name, boolean isSuspend) {
        this.mPackageName = name;
        this.mCore = core;
        this.isSuspend = isSuspend;
        this.mClient = isSuspend ? new SuspendClient(core, mPackageName) : new ModuleClient(core, mPackageName);
        this.isSystem = isSystemApp(name);
        this.mSystemStatus = new HashMap<>();
    }

    public void setCallback(IModuleCallback callback) {
        this.mCallback = callback;
        if (isSuspend && mCallback != null) {
            suspendModule();
        }
    }

    public void suspend() {
        if (isSuspend) {
            return;
        }
        Log.d(TAG, "Suspend module : " + mPackageName);
        isSuspend = true;
        suspendModule();
        mClient.suspend();
        mClient = new SuspendClient(mCore, mPackageName);
    }

    public void recovery() {
        if (!isSuspend) {
            return;
        }
        Log.d(TAG, "Recovery module : " + mPackageName);
        isSuspend = false;
        this.mClient = new ModuleClient(mCore, mPackageName);
        this.mClient.setDefaultHeadAngle(0, headVAngle);
        recoverySystemStatus();
        //TODO: 切换SpeechService的语言环境
        recoveryModule();
    }

    public boolean isSuspend() {
        return isSuspend;
    }

    public boolean isSystem() {
        return isSystem;
    }

    public ModuleClient getClient() {
        return mClient;
    }

    public CoreService getCore() {
        return mCore;
    }

    public IModuleCallback getCallback() {
        Log.d(TAG, mPackageName + "  isSuspend : " + isSuspend());
        return isSuspend ? null : mCallback;
    }

    public String getPackageName() {
        return mPackageName;
    }

    public void setDefaultHeadAngle(int hAngle, int vAngle) {
        Log.d(TAG, "setDefaultHeadAngle hAngle: " + hAngle + ", vAngle: " + vAngle);
        headVAngle = vAngle;
        this.mClient.setDefaultHeadAngle(hAngle, vAngle);
    }

    private synchronized void suspendModule() {
        try {
            if (mCallback != null) {
                Log.d(TAG, "Suspend module call : " + mPackageName);
                mCallback.onSuspend();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private synchronized void recoveryModule() {
        try {
            if (mCallback != null) {
                mCallback.onRecovery();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean isAllowModifyLight() {
        return mCore.getSystemManager().isAllowModifyLight();
    }

    private boolean isSystemApp(String packageName) {
        try {
            ApplicationInfo info = ApplicationWrapper
                    .getContext()
                    .getPackageManager()
                    .getApplicationInfo(packageName, 0);
            if ((info.flags & ApplicationInfo.FLAG_SYSTEM) > 0) {
                Log.d(TAG, packageName + " is system app");
                return true;
            }
        } catch (PackageManager.NameNotFoundException e) {
            Log.d(TAG, "not found package:" + packageName);
            e.printStackTrace();
        }
        Log.d(TAG, packageName + "is user app");
        return false;
    }

    public void setSystemStatusEnabled(String status, boolean enabled) {
        mClient.setSystemStatusEnabled(status, enabled);
        mSystemStatus.put(status, enabled);
    }

    public void updateBatteryStatus() {
        mClient.updateBatteryStatus();
    }

    public void updateFunctionKeyStatus(boolean enable) {
        mClient.updateFunctionKeyStatus(enable);
    }

    public void recoverySystemStatus() {
        Log.d(TAG, "recovery system status");
        mClient.recoverySystemStatus(mSystemStatus);
    }

    public void resetSystemStatus() {
        mClient.resetSystemStatus();
        mSystemStatus.clear();
    }

    public void setLanguage(String lang) {
        Log.d(TAG, "Set language : " + lang + "  " + this.language);
        if (this.language != null
                && this.language.equals(lang)) {
            return;
        }

        this.language = lang;
        mClient.setLanguage(lang);
        Log.d(TAG, "Set language : " + lang + "  " + mPackageName);
    }

    public String getLanguage() {
        return language;
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof Module)) {
            return false;
        }

        Module module = (Module) obj;
        return TextUtils.equals(this.mPackageName, module.getPackageName());
    }

    @Override
    public int hashCode() {
        int hashCode = 1;
        hashCode = 31 * hashCode + (mPackageName == null ? -1 : mPackageName.hashCode());
        return hashCode;
    }

    public HandleResult handleRequest(int reqId, String reqType, String reqText, String reqParam,
                                      ModuleManager.Permission permission) throws RemoteException{
        getCallback().onSendRequest(reqId, reqType, reqText, reqParam);
        return HandleResult.NORMAL;
    }

    public void updateModuleState(State state){
        Log.i(TAG, "update module state: "+ state);
        this.mState = state;
    }

    State getModuleState(){
        return mState;
    }


}
