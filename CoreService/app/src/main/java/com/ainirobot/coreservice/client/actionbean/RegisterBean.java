package com.ainirobot.coreservice.client.actionbean;

/**
 * Created by orion on 2018/4/12.
 */

public class RegisterBean extends BaseBean {
    //String personName, long time

    private String personName;
    private long time;
    private int count = 1; // count collecting picture
    private long secondDelay = 0L; // long delay to second getPictureById when getting Picture failed first.
    private int remoteType = 0; // 1 is RemoteRegister action ; 2 is RemoteDetect action ;
    private String welcomeContent;

    public long getSecondDelay() {
        return secondDelay;
    }

    public void setSecondDelay(long secondDelay) {
        this.secondDelay = secondDelay;
    }

    private Policy policy = Policy.ALL;

    public Policy getPolicy() {
        return policy;
    }

    public void setPolicy(Policy policy) {
        this.policy = policy;
    }

    public String getPersonName() {
        return personName;
    }

    public long getTime() {
        return time;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getRemoteType() {
        return remoteType;
    }

    public void setRemoteType(int remoteType) {
        this.remoteType = remoteType;
    }

    public String getWelcomeContent() {
        return welcomeContent;
    }

    public void setWelcomeContent(String welcomeContent) {
        this.welcomeContent = welcomeContent;
    }

    public enum Policy {
        LOCAL,
        REMOTE,
        ALL
    }
}
