/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.bi.server;


import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.Closeable;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

public class BiSocketServer {

    private static final String TAG = "BiSocketServer";
    private static final int CORE_POOL_SIZE = 20;
    private static final int PORT = 9191;

    private ExecutorService executorService;
    private AtomicInteger atomicInteger;
    private ServerSocket serverSocket;

    public BiSocketServer() {
        atomicInteger = new AtomicInteger(0);
        executorService = Executors.newFixedThreadPool(CORE_POOL_SIZE, new ThreadFactory() {
            @Override
            public Thread newThread(@NonNull Runnable r) {
                return new Thread(r, "BiSocketServer Thread-" + atomicInteger.incrementAndGet());
            }
        });
    }

    public void start() {
        try {
            Log.i(TAG, "create socket server");
            serverSocket = new ServerSocket(PORT);
            executorService.execute(new Runnable() {
                @Override
                public void run() {
                    acceptSocketMsg();
                }
            });
        } catch (IOException e) {
            Log.e(TAG, e.toString());
        }
    }

    private void acceptSocketMsg() {
        try {
            while (true) {
                Log.i(TAG, "ready to accept socket connection");
                Socket socket = serverSocket.accept();
                Log.i(TAG, "accept a new socket connection");

                executorService.execute(new SocketInstanceRunnable(socket));
            }

        } catch (IOException e) {
            Log.e(TAG, e.toString());
        }
    }

    private class SocketInstanceRunnable implements Runnable {

        private Socket socket;

        public SocketInstanceRunnable(Socket socket) {
            this.socket = socket;
        }

        @Override
        public void run() {
            InputStream inputStream = null;
            BufferedReader bufferedReader = null;
            try {
                inputStream = socket.getInputStream();
                bufferedReader = new BufferedReader(new InputStreamReader(inputStream));

                while (true) {
                    String msg = bufferedReader.readLine();
                    if (!TextUtils.isEmpty(msg)) {
                        Log.v(TAG, "received msg-：" + msg);
                        biReport(msg);
                    } else {
                        Log.e(TAG, "one socket is interrupted!");
                        break;
                    }
                }

            } catch (IOException e) {
                Log.e(TAG, e.toString());
            } finally {
                //close all resource
                closeResource(bufferedReader);
                closeResource(inputStream);
                closeResource(socket);
            }
        }
    }

    private void biReport(String msg) {
        try {
            JSONObject jsonObject = new JSONObject(msg);
            String tableName = jsonObject.optString(Definition.BI_PARAM_TABLE_NAME);
            String data = jsonObject.optString(Definition.BI_PARAM_DATA);
            String type = jsonObject.optString(Definition.BI_PARAM_TYPE, Definition.BI_TYPE_NORMAL);
            int level = jsonObject.optInt(Definition.BI_PARAM_LEVEL);
            boolean notForce = jsonObject.optBoolean(Definition.BI_PARAM_NOT_FORCE);
            if (!TextUtils.isEmpty(tableName) && !TextUtils.isEmpty(data)) {
                Log.v(TAG, "biReport: msg:" + msg);
                Bi.report(type, level, tableName, data, notForce);
            }

        } catch (JSONException e) {
            Log.e(TAG, "biReport error:" + e.toString());
        }
    }

    private void closeResource(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
                closeable = null;
            } catch (IOException e) {
                Log.e(TAG, e.toString());
            }
        }
    }

}
