package com.ainirobot.coreservice.bi.report;

import com.google.gson.JsonObject;

public class SystemStatusReporter extends SystemInformationReporter {
    public SystemStatusReporter() {
        super("SystemStatus", "SystemStatus");
    }

    public void reportStatusUpdate(final String statusInfo, final String status, final String param) {
        JsonObject params = new JsonObject();
        params.addProperty("statusInfo", statusInfo);
        params.addProperty("status", status);
        params.addProperty("param", param);
        mReportEditor.reset()
            .addNodeMethodTypeFunction()
            .addNodeMethodName("update")
            .addNodeParams(params.toString())
            .reportV();
    }
}
