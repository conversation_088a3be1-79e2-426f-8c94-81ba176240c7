package com.ainirobot.coreservice.client.actionbean;

/**
 * Created by orion on 2018/4/12.
 */

public class ObstacleFollowBean extends BaseBean {

    private int personID;
    private int type;
    private long lostTime;
    private long lostNaviTime;
    private long outOfMapTime;
    private double linearSpeed;
    private double angularSpeed;
    private double distanceR2P;

    public ObstacleFollowBean() {
        this.personID = -1;
    }

    public double getDistanceR2P() {
        return distanceR2P;
    }

    public void setDistanceR2P(double distanceR2P) {
        this.distanceR2P = distanceR2P;
    }

    public int getPersonID() {
        return personID;
    }

    public void setPersonID(int personID) {
        this.personID = personID;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public long getLostTime() {
        return lostTime;
    }

    public void setLostTime(long lostTime) {
        this.lostTime = lostTime;
    }

    public double getLinearSpeed() {
        return linearSpeed;
    }

    public void setLinearSpeed(double linearSpeed) {
        this.linearSpeed = linearSpeed;
    }

    public double getAngularSpeed() {
        return angularSpeed;
    }

    public void setAngularSpeed(double argularSpeed) {
        this.angularSpeed = argularSpeed;
    }

    public long getLostNaviTime() {
        return lostNaviTime;
    }

    public void setLostNaviTime(long lostNaviTime) {
        this.lostNaviTime = lostNaviTime;
    }

    public long getOutOfMapTime() {
        return outOfMapTime;
    }

    public void setOutOfMapTime(long outOfMapTime) {
        this.outOfMapTime = outOfMapTime;
    }

    @Override
    public String toString() {
        return "ObstacleFollowBean{" +
                "personID=" + personID +
                ", type=" + type +
                ", lostTime=" + lostTime +
                ", lostNaviTime=" + lostNaviTime +
                ", outOfMapTime=" + outOfMapTime +
                ", linearSpeed=" + linearSpeed +
                ", angularSpeed=" + angularSpeed +
                ", distanceR2P=" + distanceR2P +
                '}';
    }
}
