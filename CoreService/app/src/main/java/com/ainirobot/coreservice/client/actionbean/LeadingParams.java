package com.ainirobot.coreservice.client.actionbean;

/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

import com.ainirobot.coreservice.client.SettingsUtil;

public class LeadingParams extends BaseBean {
    private int mPersonId;
    private String mCustomerName;
    private String mDestinationName;
    private Long lostTimer;
    private long detectDelay; //Start detect person delay time
    private long avoidTimeout = 20 * 1000;
    private double avoidDistance = 0.1;
    private double maxDistance;
    private long waitTimeout = 2 * 60 * 1000;
    private double mLinearSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED;
    private double mAngularSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED;
    private long mMultipleWaitTime = 300*1000;

    public LeadingParams setPersonId(int personId) {
        mPersonId = personId;
        return this;
    }

    public int getPersonId() {
        return mPersonId;
    }

    public LeadingParams setCustomerName(String customerName) {
        this.mCustomerName = customerName;
        return this;
    }

    public String getCustomerName() {
        return mCustomerName;
    }

    public LeadingParams setDestinationName(String destinationName) {
        this.mDestinationName = destinationName;
        return this;
    }

    public String getDestinationName() {
        return mDestinationName;
    }

    public long getLostTimer() {
        return lostTimer;
    }

    public LeadingParams setLostTimer(long timer) {
        this.lostTimer = timer;
        return this;
    }

    public long getDetectDelay() {
        return detectDelay;
    }

    public LeadingParams setDetectDelay(long detectDelay) {
        this.detectDelay = detectDelay;
        return this;
    }

    public long getAvoidTimeout() {
        return avoidTimeout;
    }

    public LeadingParams setAvoidTimeout(long avoidTimeout) {
        this.avoidTimeout = avoidTimeout;
        return this;
    }

    public double getAvoidDistance() {
        return avoidDistance;
    }

    public LeadingParams setAvoidDistance(double avoidDistance) {
        this.avoidDistance = avoidDistance;
        return this;
    }

    public double getMaxDistance() {
        return maxDistance;
    }

    public LeadingParams setMaxDistance(double maxDistance) {
        this.maxDistance = maxDistance;
        return this;
    }

    public long getWaitTimeout() {
        return waitTimeout;
    }

    public LeadingParams setWaitTimeout(long waitTimeout) {
        this.waitTimeout = waitTimeout;
        return this;
    }

    public double getLinearSpeed(){
        return mLinearSpeed;
    }

    public LeadingParams setLinearSpeed(double linearSpeed){
        this.mLinearSpeed = linearSpeed;
        return this;
    }

    public double getAngularSpeed(){
        return mAngularSpeed;
    }

    public LeadingParams setAngularSpeed(double angularSpeed){
        this.mAngularSpeed = angularSpeed;
        return this;
    }

    public long getMultipleWaitTime() {
        return mMultipleWaitTime;
    }

    public LeadingParams setMultipleWaitTime(long multipleWaitTime) {
        this.mMultipleWaitTime = multipleWaitTime;
        return this;
    }

}
