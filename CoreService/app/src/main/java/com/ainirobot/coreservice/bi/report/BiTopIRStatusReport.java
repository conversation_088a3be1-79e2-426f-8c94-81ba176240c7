package com.ainirobot.coreservice.bi.report;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

/**
 * 分撒的埋点，如果使用无Target的地图，关闭顶部IR LED，降低电量损耗
 * 重定位、切图、建图结束都会进行上报。
 * 只针对招财豹生效
 */
public class BiTopIRStatusReport extends BaseBiReport {

    private static final String TABLE_NAME = "base_robot_ir";

    /**
     * 开---0   关---1
     */
    private static final String STATUS = "status";

    /**
     * 1-开机重定位 2-休眠 3-maptool切图 4-建图完成
     */
    private static final String SOURCE = "source";

    /**
     * 1-纯激光地图 2-target地图
     */
    private static final String MAP_TYPE = "map_type";

    private static final String CTIME = "ctime";


    public static final int SOURCE_REPOSITION = 1;
    public static final int SOURCE_STANDBY = 2;
    public static final int SOURCE_SWITCH_MAP = 3;
    public static final int SOURCE_CREATE_MAP_END = 4;

    public static final int MAP_TYPE_GENERAL = 1;
    public static final int MAP_TYPE_TARGET = 2;

    public static final int STATUS_OPEN = 0;
    public static final int STATUS_CLOSE = 1;

    public BiTopIRStatusReport() {
        super(TABLE_NAME);
    }

    public BiTopIRStatusReport addStatus(int status) {
        addData(STATUS, status);
        return this;
    }

    public BiTopIRStatusReport addSource(int source) {
        addData(SOURCE, source);
        return this;
    }

    public BiTopIRStatusReport addMapType(int mapType) {
        addData(MAP_TYPE, mapType);
        return this;
    }

    @Override
    public void report() {
        addData(CTIME, System.currentTimeMillis());
        super.report();
    }
}
