package com.ainirobot.coreservice.server;

import android.os.ParcelFileDescriptor;
import android.os.RemoteCallbackList;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.IShareMemoryApi;
import com.ainirobot.coreservice.IShareMemoryCallback;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.core.external.ExternalService;
import com.ainirobot.coreservice.core.external.ExternalServiceManager;
import com.ainirobot.coreservice.service.CoreService;


public class ShareMemoryServer extends IShareMemoryApi.Stub {

    private static final String TAG = ShareMemoryServer.class.getSimpleName();
    private final CoreService mCore;
    private final RemoteCallbackList<IShareMemoryCallback> mListeners = new RemoteCallbackList<>();

    public ShareMemoryServer(CoreService core) {
        mCore = core;
    }

    @Override
    public ParcelFileDescriptor getParcelFileDescriptor(String type, String params) throws RemoteException {
        Log.d(TAG, "getParcelFileDescriptor: type=" + type + " params=" + params);
        ExternalServiceManager mServices = mCore.getExternalServiceManager();
        ExternalService naviService = mServices.getExternalService(RobotOS.NAVIGATION_SERVICE);
        Log.d(TAG, "getParcelFileDescriptor: naviService=" + naviService);
        if (naviService != null && naviService.isEnable()) {
            return naviService.getParcelFileDescriptor(type, params);
        }
        return null;
    }

    @Override
    public boolean setParcelFileDescriptor(String type, ParcelFileDescriptor pfd, String params) throws RemoteException {
        Log.d(TAG, "setParcelFileDescriptor: type=" + type + " pfd=" + pfd + " params=" + params);
        ExternalServiceManager mServices = mCore.getExternalServiceManager();
        ExternalService naviService = mServices.getExternalService(RobotOS.NAVIGATION_SERVICE);
        Log.d(TAG, "setParcelFileDescriptor: naviService=" + naviService);
        if (naviService != null && naviService.isEnable()) {
            return naviService.setParcelFileDescriptor(type, pfd, params);
        }
        return false;
    }

    @Override
    public void releaseParcelFileDescriptor(String type) throws RemoteException {
        Log.d(TAG, "releaseParcelFileDescriptor: type=" + type);
        ExternalServiceManager mServices = mCore.getExternalServiceManager();
        ExternalService naviService = mServices.getExternalService(RobotOS.NAVIGATION_SERVICE);
        Log.d(TAG, "releaseParcelFileDescriptor: naviService=" + naviService);
        if (naviService != null && naviService.isEnable()) {
            naviService.releaseParcelFileDescriptor(type);
        }
    }

    public void broadcastPfd(ParcelFileDescriptor pfd) throws RemoteException {
        final int size = mListeners.beginBroadcast();
        Log.d(TAG, "pfd+" + pfd + ", listerners size = " + size);
        for (int i = 0; i < size; i++) {
            IShareMemoryCallback l = mListeners.getBroadcastItem(i);
            if (l != null) {
                l.onUpdateParcelFileDescriptor(pfd);
            }
        }
        mListeners.finishBroadcast();
    }

    @Override
    public void registerCallback(IShareMemoryCallback callback) throws RemoteException {
        mListeners.register(callback);
    }

    @Override
    public void unregisterCallback(IShareMemoryCallback callback) throws RemoteException {
        mListeners.unregister(callback);
    }

    @Override
    public void startShare() throws RemoteException {
    }

    @Override
    public void stopShare() throws RemoteException {
    }
}
