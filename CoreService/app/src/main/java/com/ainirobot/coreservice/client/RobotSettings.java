/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.client;

import static android.Manifest.permission.WRITE_SECURE_SETTINGS;
import static android.Manifest.permission.WRITE_SETTINGS;

import android.annotation.SuppressLint;
import android.content.ContentValues;
import android.content.Context;
import android.content.pm.PackageManager;
import android.database.ContentObserver;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.exception.InvalidArgumentException;
import com.ainirobot.coreservice.client.exception.NoSuchKeyException;
import com.ainirobot.coreservice.client.exception.ValueFormatException;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingListener;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 获取robot基础信息
 */
public class RobotSettings {
    private static final String TAG = RobotSettings.class.getSimpleName();

    /**
     * 机器人 是否有profile
     */
    public static final String SETTINGS_GLOBAL_ROBOT_PROFILE = "robot_profile";
    /**
     * 机器人 uuid
     */
    public static final String SETTINGS_GLOBAL_ROBOT_UUID = "robot_uuid";
    /**
     * 机器人名
     */
    public static final String SETTINGS_GLOBAL_ROBOT_NAME = "robot_name";
    /**
     * 机器人版本
     */
    public static final String SETTINGS_GLOBAL_ROBOT_VERSION = "robot_version";
    /**
     * 机器人型号
     */
    public static final String SETTINGS_GLOBAL_ROBOT_MODEL = "robot_model";

    /**
     * 企业uuid
     */
    public static final String SETTINGS_GLOBAL_CORP_UUID = "corp_uuid";
    /**
     * 企业名称
     */
    public static final String SETTINGS_GLOBAL_CORP_NAME = "corp_name";
    /**
     * 企业行业信息
     */
    public static final String SETTINGS_GLOBAL_CORP_SCENARIO = "corp_scenario";

    /**
     * 企业联系人手机号
     */
    public static final String SETTINGS_GLOBAL_CROP_CONTACTS_MOBILE = "crop_contacts_mobile";

    /**
     * 代理商uuid
     */
    public static final String SETTINGS_GLOBAL_CORP_AGENCY_ID = "corp_agency_id";

    /**
     * 机器人角色
     */
    public static final String SETTINGS_GLOBAL_ROBOT_MODE = "robot_mode";

    /**
     * 机器人是否是样机
     */
    public static final String SETTINGS_GLOBAL_ROBOT_SAMPLE = "robot_sample";

    /**
     * 机器人坐标
     */
    public static final String SETTINGS_GLOBAL_ROBOT_COORDINATE = "robot_coordinate";
    /**
     * 机器人位置信息
     * 	"pos_info": "{\"city\":\"北京市\",\"country\":\"中国\",\"describe\":\"北京市丰台区右安门街道北京市丰台区翠林小区翠林小区三里\",\"district\":\"丰台区\",\"latitude\":39.8576,\"location_type\":\"amap\",\"longitude\":116.3694,\"province\":\"北京市\",\"street\":\"南三环西路\",\"town\":\"右安门街道\"}"
     */
    public static final String SETTINGS_GLOBAL_ROBOT_POS_INFO = "robot_pos_info";

    /**
     * AOS版本号，最后一次通过 login / update_profile 这 2 个接口传递给服务端的非空值
     */
    public static final String SETTINGS_GLOBAL_ROBOT_AIOS_VERSION = "robot_aios_version";

    /**

    /**
     * 语音 OS 的 client_id
     */
    public static final String SETTINGS_GLOBAL_VOICE_CLIENT_ID = "voice_client_id";
    /**
     * 语音 OS 的企业 id
     */
    public static final String SETTINGS_GLOBAL_VOICE_CORP_ID = "voice_corp_id";
    /**
     * 语音 OS 的 场景id
     */
    public static final String SETTINGS_GLOBAL_VOICE_GROUP_ID = "voice_group_id";
    /**
     * 引领中是否等人
     */
    public static final String SETTINGS_GLOBAL_GUIDE_WAIT = "guide_wait";
    /**
     * 引领中是否等人
     */
    public static final String SETTINGS_GLOBAL_GUIDE_TITLE = "guide_title";
    /**
     * 是否开启语音数据上传
     */
    public static final String SETTINGS_GLOBAL_NEED_VOICE_REPORT = "voice_report";
    /**
     * 是否开启预唤醒
     */
    public static final String SETTINGS_GLOBAL_NEED_SAY_HELLO = "need_say_hello";

    /**
     * 是否开启深度摄像头
     */
    public static final String SETTINGS_GLOBAL_ENBALE_RGBD = "rgbd";

    /**
     * 是否开启RGBD避障策略，此功能如需使用需保证打开深度摄像头
     */
    public static final String SETTINGS_GLOBAL_ENABLE_RGBD_AVOID_OBS = "rgbd_avoid_obs";

    /**
     * 问答TTS防打断
     */
    public static final String SETTINGS_GLOBAL_CHAT_TTS_PREVENT_BREAK = "chat_tts_prevent_break";

    /**
     * 交互模式选择
     */
    public static final String SETTINGS_GLOBAL_INTERACTION_MODE = "interaction_mode";

    /**
     * 各刹车模式下速度，保存对象BreakSpeedBean, 有可能为空
     */
    public static final String SETTINGS_BREAK_SPEED_THRESHOLD = "break_speed_threshold";

    /**
     * 13位的sn
     */
    private static final String SERIAL_NUM_KEY_ROBOT = "ro.serialno.robot";

    /**
     * 13位的sn 9.0
     */
    private static final String SERIAL_NUM_KEY_ROBOT_9 = "ro.robot.serialno";
    /**
     * 8位hwId
     */
    private static final String QUALCOMMSN_KEY_ROBOT = "devid.qualcommSN";
    /**
     * 8位sn
     */
    private static final String SERIAL_NUM_KEY_CHIPSET = "ro.serialno";

    /**
     * 错误码前缀
     */
    private static final String SERIAL_NUM_ERR_CONST = "Error";

    /**
     * 默认model名称
     */
    private static final String ROBOT_MODEL_NAME = "Meissa";
    /**
     * model的key值
     */
    private static final String SERIALNO_KEY_PRODUCTMODEL = "ro.product.model";
    /**
     * 默认brand名称
     */
    private static final String ROBOT_BRAND_NAME = "Orion";
    /**
     * brand的key值
     */
    private static final String SERIALNO_KEY_PRODUCTBRAND = "ro.product.brand";

    /**
     * 是否自动同步时区
     */
    private static final String AUTO_TIME_ZONE = "auto_time_zone";


    /**
     * platform的key值
     */
    private static final String SERIALNO_KEY_BUILDTYPE = "ro.build.type";
    private static final String SERIALNO_KEY_PRODUCTNAME = "ro.product.name";
    private static final String SERIALNO_KEY_PRODUCTVERSION = "ro.product.version";
    private static final String SERIALNO_KEY_RELEASENUM = "ro.product.releasenum";
    private static final String PROPERTY_ORIONSTAR_VERSION = "ro.product.orionstaros";
    private static final String AUDIO_APM_VERSION = "sys.audio.apm.version";
    private static final String AUDIO_ELEVOC_VERSION = "sys.audio.elevoc.version";

    private static final String AUTHORITY = "com.ainirobot.coreservice.robotsettingprovider";
    private static final Uri CONTENT_URI = Uri.parse("content://" + AUTHORITY + "/setting");

    private static Map<RobotSettingListener, List<String>> mListenerAndKeysMap
            = new ConcurrentHashMap<>();


    public static final String ROBOT_LOW_POWER_LEVEL = "robot_settings_battery_low_level";
    /**
     * 系统应用app 检查
     */
     private static final String CORE_SERVICE = "com.ainirobot.coreservice";
     private static final String HOME = "com.ainirobot.home";
     private static final String FIRST_CONFIG = "com.ainirobot.firstconfig";
     private static final String MODULE_APP = "com.ainirobot.moduleapp";
     private static final String MAP_TOOL = "com.ainirobot.maptool";
     private static final List<String> ACCEPT_PACKAGE_LIST = Collections.unmodifiableList(
     Arrays.asList(CORE_SERVICE, HOME, FIRST_CONFIG, MODULE_APP, MAP_TOOL));

    /**
     * 获取机器人SN(对外提供接口)
     *
     * @return 机器人SN
     */
    static int getSystemSn(CommandListener listener) {
        String serialNum = getSystemSn();

        boolean isSuccess = !TextUtils.isEmpty(serialNum);
        if (listener != null) {
            listener.onResult(isSuccess ? Definition.RESULT_OK : Definition.RESULT_FAILURE, serialNum);
            listener.onResult(isSuccess ? Definition.RESULT_OK : Definition.RESULT_FAILURE, serialNum, "");
        }
        return isSuccess ? Definition.CMD_SEND_SUCCESS : Definition.CMD_SEND_ERROR_UNKNOWN;
    }

    /**
     * 获取机器人SN
     *
     * @return 机器人SN
     */
    public static String getSystemSn() {
        String serialNum;
        if (Build.VERSION.SDK_INT < 26) {
            serialNum = getSystemProperties(SERIAL_NUM_KEY_ROBOT, "");
            if (TextUtils.isEmpty(serialNum) || serialNum.startsWith(SERIAL_NUM_ERR_CONST)) {
                serialNum = getSystemProperties(SERIAL_NUM_KEY_CHIPSET, "");
            }
        } else {
            serialNum = getSystemProperties(SERIAL_NUM_KEY_ROBOT_9, "");
            if (TextUtils.isEmpty(serialNum) || serialNum.startsWith(SERIAL_NUM_ERR_CONST)) {
                serialNum = Build.SERIAL;
            }
        }
        return serialNum;
    }

    /**
     * 获取机器人model
     *
     * @return 机器人model
     */
    public static String getProductModel() {
        return getSystemProperties(SERIALNO_KEY_PRODUCTMODEL, ROBOT_MODEL_NAME);
    }

    /**
     * 获取机器人brand
     *
     * @return 机器人brand
     */
    public static String getBrand() {
        return getSystemProperties(SERIALNO_KEY_PRODUCTBRAND, ROBOT_BRAND_NAME);
    }

    /**
     * 获取平台名称
     *
     * @return 机器人平台名称
     */
    public static String getPlatform() {
        return getSystemProperties(SERIALNO_KEY_PRODUCTNAME, "");
    }

    public static String getRomVersion() {
        String v = getSystemProperties(SERIALNO_KEY_PRODUCTVERSION, "");
        if (!TextUtils.isEmpty(v)) {
            return v;
        }
        return "";
    }

    /**
     * 获取系统Timezone
     *
     * @return Timezone
     */
    public static String getSysTimeZone() {
        return getSystemProperties("persist.sys.timezone", "Asia/Shanghai");
    }
    /**
     * 获取系统Timezone
     *
     * @return Timezone,如果获取失败，则无默认值，返回空串．
     */
    public static String getSysTimeZoneNoDefault() {
        return getSystemProperties("persist.sys.timezone", "");
    }

    public static String getAndroidVersion() {
        switch (android.os.Build.VERSION.SDK_INT) {
            case Build.VERSION_CODES.KITKAT:
            case Build.VERSION_CODES.KITKAT_WATCH:
                return "Android 4.0";
            case Build.VERSION_CODES.LOLLIPOP:
            case Build.VERSION_CODES.LOLLIPOP_MR1:
                return "Android 5.0";
            case Build.VERSION_CODES.M:
                return "Android 6.0";
            case Build.VERSION_CODES.N:
            case Build.VERSION_CODES.N_MR1:
                return "Android 7.0";
            default:
                return "Android XX";
        }
    }

    /**
     * @param context
     * @return
     */
    @SuppressLint({"HardwareIds"})
    public static String getIMEI(Context context) {
        TelephonyManager tm = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
        return tm != null ? tm.getDeviceId() : null;
    }

    public static String getVersion() {
        return getSystemProperties(SERIALNO_KEY_RELEASENUM, "1.100.0");
    }

    /**
     * [ro.product.orionstaros]: [6.8]
     * @return
     */
    public static String getOrionStarPropertyVersion() {
        return getSystemProperties(PROPERTY_ORIONSTAR_VERSION, "6.8");
    }

    /**
     * 获取高通板子SN
     *
     * @return 高通板子SN
     */
    public static String getQualcommSn() {
        return getSystemProperties(QUALCOMMSN_KEY_ROBOT, "");
    }


    @SuppressLint("PrivateApi")
    private static String getSystemProperties(String key, String defaultVal) {
        String ret;
        try {
            Method systemPropertiesGet = Class.forName("android.os.SystemProperties").getMethod("get", String.class);
            if ((ret = (String) systemPropertiesGet.invoke(null, key)) != null)
                return TextUtils.isEmpty(ret) ? defaultVal : ret;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return defaultVal;
    }


    public static String getRobotUUID(Context ctx) {
        return getGlobalSettings(ctx, SETTINGS_GLOBAL_ROBOT_UUID, "");
    }

    // ------------------ruuid
    public static void putRobotUUID(Context ctx, String ruuid) {
        storage2SystemSettings(ctx, SETTINGS_GLOBAL_ROBOT_UUID, ruuid);
    }


    public static void cleanRobotUUID(Context ctx) {
        storage2SystemSettings(ctx, SETTINGS_GLOBAL_ROBOT_UUID, "");
    }

    public static String getRobotName(Context ctx) {
        return getGlobalSettings(ctx, SETTINGS_GLOBAL_ROBOT_NAME, "");
    }

    // ------------------cuuid
    public static String getCorpUUID(Context ctx) {
        return getGlobalSettings(ctx, SETTINGS_GLOBAL_CORP_UUID, "");
    }

    public static String getVoiceCorpId(Context ctx) {
        return getGlobalSettings(ctx, SETTINGS_GLOBAL_VOICE_CORP_ID, "");
    }

    public static void cleanCorpUUID(Context ctx) {
        storage2SystemSettings(ctx, SETTINGS_GLOBAL_CORP_UUID, "");
    }

    public static String getCorpName(Context ctx){
        return getGlobalSettings(ctx, SETTINGS_GLOBAL_CORP_NAME, "");
    }

    public static String getApmVersion() {
        return getSystemProperties(AUDIO_APM_VERSION, "apm");
    }

    public static String getElevocVersion() {
        return getSystemProperties(AUDIO_ELEVOC_VERSION, "elevoc");
    }

    public static String getProfile(Context ctx) {
        return getGlobalSettings(ctx, SETTINGS_GLOBAL_ROBOT_PROFILE, "");
    }

    public static void setProfile(Context ctx, String profile) {
        storage2SystemSettings(ctx, SETTINGS_GLOBAL_ROBOT_PROFILE, profile);
    }

    public static void closeAutoTimeZone(Context ctx) {
        Settings.Global.putInt(ctx.getContentResolver(), AUTO_TIME_ZONE, 0);
    }

    public static void openAutoTiemZone(Context ctx) {
        Settings.Global.putInt(ctx.getContentResolver(), AUTO_TIME_ZONE, 1);
    }

    /**
     * 获取设置项
     *
     * @param ctx      上下文对象
     * @param key      设置项
     * @param defValue 默认值
     * @return 设置项value
     */
    public static String getGlobalSettings(Context ctx, final String key, final String defValue) {
        String global = defValue;

        if (TextUtils.isEmpty(key) || null == ctx) {
            return global;
        }
        global = Settings.Global.getString(ctx.getContentResolver(), key);
        if (TextUtils.isEmpty(global)) {
            global = defValue;
        }
        return global;
    }

    public static void storage2SystemSettings(Context ctx, final String key, final String value) {
        if (TextUtils.isEmpty(key) || null == ctx) {
            Log.w(TAG, "can't put system.settings a empty key or null context!");
            return;
        }

        // 检测是否有 写系统settings权限
        int writePerm = ctx.checkCallingOrSelfPermission(WRITE_SETTINGS);
        int writeSecurePerm = ctx.checkCallingOrSelfPermission(WRITE_SECURE_SETTINGS);
        if (PackageManager.PERMISSION_GRANTED != writePerm || PackageManager.PERMISSION_GRANTED != writeSecurePerm) {
            Log.e(TAG, "This app have no WRITE_SETTINGS or WRITE_SECURE_SETTINGS permission!");
            return;
        }
        // put to system.settings
        if (!Settings.Global.putString(ctx.getContentResolver(), key, value)) {
            Log.w(TAG, "Settings.Global put error! key: " + key + ", value: " + value);
        }
    }

    public static int getRobotInt(Context context, String key)
            throws InvalidArgumentException, NoSuchKeyException, ValueFormatException {
        String value = getRobotString(context, key);
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            throw new ValueFormatException(value);
        }
    }

    public static float getRobotFloat(Context context, String key)
            throws InvalidArgumentException, NoSuchKeyException, ValueFormatException {
        String value = getRobotString(context, key);
        try {
            return Float.parseFloat(value);
        } catch (NumberFormatException e) {
            throw new ValueFormatException(value);
        }
    }

    public static String getRobotString(Context context, String key)
            throws InvalidArgumentException, NoSuchKeyException {
        if (context == null) {
            throw new InvalidArgumentException("context null");
        }
        if (TextUtils.isEmpty(key)) {
            throw new InvalidArgumentException("key null");
        }
        Cursor cursor = context.getContentResolver().query(CONTENT_URI, null,
                "id=?", new String[]{
                        key
                }, null);
        String value;
        if (cursor != null && cursor.moveToNext()) {
            value = cursor.getString(cursor.getColumnIndex("value"));
            cursor.close();
            return value;
        } else {
            throw new NoSuchKeyException("not support");
        }
    }

    public static boolean setRobotInt(Context context, String key, int value)
            throws InvalidArgumentException {
        return setRobotString(context, key, String.valueOf(value));
    }

    public static boolean setRobotFloat(Context context, String key, float value)
            throws InvalidArgumentException {
        return setRobotString(context, key, String.valueOf(value));
    }

    public static boolean setRobotString(Context context, String key, String value)
            throws InvalidArgumentException {
        if (context == null) {
            throw new InvalidArgumentException("context null");
        }
        if (TextUtils.isEmpty(key)) {
            throw new InvalidArgumentException("key null");
        }
        ContentValues contentValues = new ContentValues();
        contentValues.put("value", value);
        int rows = context.getContentResolver().update(CONTENT_URI, contentValues,
                "id=?", new String[]{
                        key
                });
        return rows > 0;
    }

    public static void registerRobotSettingListener(Context context, RobotSettingListener listener,
                                                    String... key)
            throws InvalidArgumentException {
        if (context == null) {
            throw new InvalidArgumentException("context null");
        }
        if (null == listener) {
            throw new InvalidArgumentException("listener null");
        }
        if (key.length <= 0) {
            throw new InvalidArgumentException("key must has one at least");
        }
        List<String> keyList = new ArrayList<>(Arrays.asList(key));
        if (keyList.size() <= 0) {
            throw new InvalidArgumentException("key must has one at least");
        }
        addListener(context, listener, keyList);
    }

    public static void unRegisterRobotSettingListener(Context context,
                                                      RobotSettingListener listener)
            throws InvalidArgumentException {
        if (context == null) {
            throw new InvalidArgumentException("context null");
        }
        if (null == listener) {
            throw new InvalidArgumentException("listener null");
        }
        removeListener(context, listener);
    }

    private static void addListener(Context context, RobotSettingListener listener,
                                    List<String> keyList) {
        Log.d(TAG, "robots settings add listener:" + listener.toString()
                + ", size: " + mListenerAndKeysMap.size());
        if (mListenerAndKeysMap.size() <= 0) {
            context.getContentResolver().registerContentObserver(CONTENT_URI,
                    true, observer);
        }
        mListenerAndKeysMap.put(listener, keyList);
    }

    private static void removeListener(Context context, RobotSettingListener listener) {
        Log.d(TAG, "robots settings remove listener:" + listener.toString()
                        + ", size: " + mListenerAndKeysMap.size());
        mListenerAndKeysMap.remove(listener);
        if (mListenerAndKeysMap.size() <= 0) {
            context.getContentResolver().unregisterContentObserver(observer);
        }
    }

    static ContentObserver observer = new ContentObserver(null) {
        @Override
        public void onChange(boolean selfChange, Uri uri) {
            String key = uri.getLastPathSegment();
            Log.d(TAG, "robots settings changed uri:" + uri + ", key: " + key);
            for (RobotSettingListener listener : mListenerAndKeysMap.keySet()) {
                List<String> keyList = mListenerAndKeysMap.get(listener);
                if (keyList != null) {
                    if (keyList.contains(key)) {
                        listener.onRobotSettingChanged(key);
                    }
                }
            }
        }
    };

    public static boolean putGlobalInt(Context context, String name, int value) {
        boolean result = false;
        if (!checkPermission(context)) {
            Log.e(TAG, "Permission deny");
            return result;
        }
        try {
            result = Settings.Global.putInt(context.getContentResolver(), name, value);
        } catch (Exception e) {
            e.printStackTrace();
            result = false;
        }
        return result;
    }

    private static boolean checkPermission(Context context) {
        return ACCEPT_PACKAGE_LIST.contains(context.getPackageName());
    }

    /**
     * If not take a default value.  If the setting has not
     * been set, or the string value is not a number,
     * it throws {SettingNotFoundException}
     * @param context
     * @param key
     * @param defaultValue
     * @return
     */
    public static int getGlobalInt(Context context, String key, int defaultValue) {
        return Settings.Global.getInt(context.getContentResolver(), key, defaultValue);
    }

    private static Uri getGlobalUriFor(String key) {
        return Settings.Global.getUriFor(key);
    }

    public static void registerGlobalContentObserver(Context context, String key, ContentObserver observer) {
        context.getContentResolver().registerContentObserver(
                getGlobalUriFor(key), false, observer);
    }

    public static void unregisterGlobalContentObserver(Context context, ContentObserver observer){
        context.getContentResolver().unregisterContentObserver(observer);
    }

    public static String getCorpAgencyId(Context ctx) {
        return getGlobalSettings(ctx, SETTINGS_GLOBAL_CORP_AGENCY_ID, "");
    }

}
