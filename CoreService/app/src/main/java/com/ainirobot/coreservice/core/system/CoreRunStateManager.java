/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.core.system;

import android.os.HandlerThread;
import android.os.Handler;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.action.ActionManager;
import com.ainirobot.coreservice.client.techreport.CallChainReport;
import com.ainirobot.coreservice.core.module.ModuleManager;
import com.ainirobot.coreservice.service.CoreService;

public class CoreRunStateManager {
    private final String TAG = "CoreRunStateManager";
    private CallChainReport mCallChain;

    private enum CoreRunState {
        SYSTEM,
        APP,
    }

    private CoreService mCore;
    private CoreRunState mState = CoreRunState.APP;

    private Handler mHandler;

    public CoreRunStateManager(CoreService core) {
        mCore = core;
        mCallChain = new CallChainReport(ApplicationWrapper.getContext(),
                CoreRunStateManager.class.getSimpleName(), "app_authorize");

        HandlerThread thread = new HandlerThread("CoreRunStateManager");
        thread.start();
        mHandler = new Handler(thread.getLooper());
    }

    synchronized boolean switchSystemControl() {
        Log.d(TAG, "Switch system control");
        mCallChain.invokeNodeMethod("switchSystemControl");
        mCallChain.invokeNodeInfo("currentState:" + mState, "switch system control success");
        mCallChain.report();
        if (mState == CoreRunState.SYSTEM) {
            Log.d(TAG, "Switch system control : current already in the system control");
            return false;
        }

        stopAppControl();
        mState = CoreRunState.SYSTEM;
        return true;
    }

    boolean switchAppControl() {
        ModuleManager moduleManager = mCore.getModuleManager();
        return switchAppControl(moduleManager.getActiveAppModule());
    }

    private synchronized boolean switchAppControl(final String packageName) {
        Log.d(TAG, "Switch app control : " + packageName);
        mCallChain.invokeNodeMethod("switchAppControl");
        if (TextUtils.isEmpty(packageName)) {
            Log.d(TAG, "Switch app control : package name is null");
            mState = CoreRunState.APP;
            return false;
        }

        final ModuleManager moduleManager = mCore.getModuleManager();
        if (mState == CoreRunState.APP
                && moduleManager.isActiveAppModule(packageName)) {
            Log.d(TAG, "Switch app control : current already in the app control");
            mCallChain.invokeNodeInfo(packageName, "apply package is current, return ");
            mCallChain.report();
            return false;
        }
        mState = CoreRunState.APP;
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                String activeModule = moduleManager.getActiveAppModule();
                moduleManager.setActiveAppModule(packageName);
                moduleManager.suspendModule(activeModule);
                stopAction(activeModule);
                mCore.getExternalServiceManager().switchAppControl(packageName, activeModule);
                moduleManager.recoveryModule(packageName);
                //moduleManager.setActiveAppModule(packageName);
                //mState = CoreRunState.APP;
                mCallChain.invokeNodeInfo("current: " + activeModule + " ++ switch to: " + packageName,
                        "switch app control success");
                mCallChain.report();
            }
        });
        return true;
    }

    public synchronized boolean startAppControl(String packageName) {
        mCallChain.invokeNodeMethod("startAppControl");
        mCallChain.invokeNodeInfo("current state: " + mState + " ++ package: " + packageName,
                "apply app control");
        mCallChain.report();
        ModuleManager moduleManager = mCore.getModuleManager();
        if (mState == CoreRunState.SYSTEM) {
            moduleManager.setActiveAppModule(packageName);
            return false;
        } else {
            return switchAppControl(packageName);
        }
    }

    public void stopCurrentAppControl() {
        Log.d(TAG, "Stop current app control");
        ModuleManager moduleManager = mCore.getModuleManager();
        if (moduleManager.activeModuleIsBackGround()) {
            Log.d(TAG, "current module:"
                    + moduleManager.getActiveAppModule() + " is background module");
            return;
        }
        if (!isSystemControl()) {
            stopAppControl();
        }
        moduleManager.setActiveAppModule(null);
    }

    private synchronized void stopAppControl() {
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                ModuleManager moduleManager = mCore.getModuleManager();
                String activeModule = moduleManager.getActiveAppModule();
                Log.d(TAG, "Stop app control : " + moduleManager.getActiveAppModule());

                moduleManager.suspendApp();
                stopAction(activeModule);

                String systemModule = moduleManager.getSystemModule();
                mCore.getExternalServiceManager().switchAppControl(systemModule, moduleManager.getActiveAppModule());
                mCallChain.invokeNodeMethod("stopAppControl");
                mCallChain.invokeNodeInfo("", "stop all app permission control");
                mCallChain.report();
            }
        });
    }

    private void stopAction(String module) {
        ActionManager actionManager = mCore.getActionManager();
        actionManager.stopAllAction(module);
    }

    public String getActiveApp() {
        ModuleManager moduleManager = mCore.getModuleManager();
        return moduleManager.getActiveAppModule();
    }

    public synchronized boolean isSystemControl() {
        return mState == CoreRunState.SYSTEM;
    }

}
