package com.ainirobot.coreservice.config.core;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

public abstract class BaseConfig {

    private static final String IS_ENABLE = "是否启用";
    private static final String CONFIG_DETAIL = "配置项";

    protected boolean isEnable;
    protected JsonObject configData;

    public BaseConfig(JsonObject data) {
        this(data, false);
    }

    public BaseConfig(JsonObject data, boolean defaultEnable) {
        isEnable = defaultEnable;
        if (data != null) {
            JsonElement element = data.get(IS_ENABLE);
            if (element != null) {
                isEnable = element.getAsBoolean();
            }

            JsonElement configDataElement = data.get(CONFIG_DETAIL);
            if (configDataElement != null) {
                configData = configDataElement.getAsJsonObject();
            }
        }
        parseConfig(configData);
    }

    public abstract void parseConfig(JsonObject configData);

    public boolean isEnable() {
        return isEnable;
    }
}
