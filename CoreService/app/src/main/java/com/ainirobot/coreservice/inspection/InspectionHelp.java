package com.ainirobot.coreservice.inspection;

import android.hardware.Camera;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

import static android.content.Context.SENSOR_SERVICE;

/**
 * Created by Orion on 2018/5/8.
 */
public class InspectionHelp {

    private static final String PING = "ping -c 3 -w 10 %s";
    private static SensorManager sensorManager;
    private static int THRESHOLD_VALUE = 1;
    static List<Float> listSensorVal = new ArrayList<>();
    private static int count = 0;

    public static boolean ping(String address) {
        long startTime = System.currentTimeMillis();

        address = address.replace("http://", "");
        String command = String.format(PING, address);
        Log.d("InspectionHelp", command);
        String result = null;
        result = exec(command);
        Log.e("Terminal", "" + result + "  time=" + (System.currentTimeMillis() - startTime));
        return result != null;
    }

    private static String exec(String command){

        String result;
        BufferedReader reader = null;
        try {
            Process process = Runtime.getRuntime().exec(command);
            reader = new BufferedReader(
                    new InputStreamReader(process.getInputStream()));

            int status = process.waitFor();
            if (status != 0) {
                reader.close();
                return null;
            }

            String line;
            StringBuilder output = new StringBuilder();
            while ((line = reader.readLine()) != null) {
                if (output.length() > 0) {
                    output.append("\n");
                }
                output.append(line);
            }
            result = output.toString();
        }
        catch (IOException e) {
            e.printStackTrace();
            result = null;
        }
        catch (InterruptedException e) {
            e.printStackTrace();
            result = null;
        }
        finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return result;

    }

    public static boolean is821CameraCanUse() {
        boolean canUse = true;
        Camera camera = null;
        int number = Camera.getNumberOfCameras();
        if (number > 0) {
            try {
                camera = Camera.open(0);
            } catch (Exception e) {
                e.printStackTrace();
                canUse = false;
            }
        }
        if (null == camera) {
            canUse = false;
        } else {
            camera.release();
            camera = null;
        }
        return canUse;
    }

    private static Object syncLock = new Object();
    private static void waitTask(long time) throws InterruptedException {
        synchronized (syncLock) {
            syncLock.wait(time);
        }
    }

    private static void notifyTask() {
        synchronized (syncLock) {
            syncLock.notify();
        }
    }
    public static boolean syncGetLightSensorState() {
        sensorManager = (SensorManager) ApplicationWrapper.getContext().getSystemService(SENSOR_SERVICE);
        //此方法必须在子线程调用，因为sensorEventListener回调在主线程，因此不能再主线程等待。
        if (sensorManager != null) {
            sensorManager.registerListener(sensorEventListener, sensorManager.getDefaultSensor(Sensor.TYPE_LIGHT)
                    , SensorManager.SENSOR_DELAY_UI);
        }

        try {
            waitTask(5 * 1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        Log.d("inspectHelp", "lightsensor val = " + listSensorVal.toString());
        return means(listSensorVal) > THRESHOLD_VALUE;
    }

    private static SensorEventListener sensorEventListener = new SensorEventListener() {
        public void onAccuracyChanged(Sensor sensor, int accuracy) {
        }

        public void onSensorChanged(SensorEvent event) {
            if (event.sensor.getType()==Sensor.TYPE_LIGHT) {

                float x = event.values[0];
                listSensorVal.add(x);
                count++;
                if (count > 10) {
                    notifyTask();
                    if (sensorManager != null) {
                        sensorManager.unregisterListener(sensorEventListener);
                    }
                }
            }
        }
    };

    private static float means(List<Float> listSensorVal) {
        if (listSensorVal.size() == 0) {
            return 0;
        }
        float sum = 0;
        for (float x : listSensorVal) {
            sum = sum + x;
        }
        return sum/listSensorVal.size();
    }

}
