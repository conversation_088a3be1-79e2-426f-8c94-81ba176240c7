package com.ainirobot.coreservice.surfaceshare;

import android.view.Surface;

import com.ainirobot.coreservice.client.surfaceshare.SurfaceShareBean;
import com.ainirobot.coreservice.listener.ISurfaceShareListener;

public class SurfaceShareContent {

    private Surface surface;
    private SurfaceShareBean bean;
    private ISurfaceShareListener listener;

    public SurfaceShareContent(Surface surface, SurfaceShareBean bean,
                               ISurfaceShareListener listener) {
        this.surface = surface;
        this.bean = bean;
        this.listener = listener;
    }

    public Surface getSurface() {
        return surface;
    }

    public SurfaceShareBean getBean() {
        return bean;
    }

    public ISurfaceShareListener getListener() {
        return listener;
    }

    @Override
    public String toString() {
        return "SurfaceShareContent {\n" +
                "    surface: " + surface + ",\n" +
                "    bean: " + bean + ",\n" +
                "    listener: " + listener + "\n" +
                "}";
    }
}
