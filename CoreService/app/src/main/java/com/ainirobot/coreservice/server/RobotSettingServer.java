/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.server;

import android.os.RemoteException;

import com.ainirobot.coreservice.IRobotSettingApi;
import com.ainirobot.coreservice.client.exception.BinderExceptionHandler;
import com.ainirobot.coreservice.listener.IRobotSettingListener;
import com.ainirobot.coreservice.service.CoreService;

import java.util.List;

public class RobotSettingServer extends IRobotSettingApi.Stub {

    private CoreService mCore;

    public RobotSettingServer(CoreService core) {
        mCore = core;
    }

    @Override
    public boolean containsRobotConfig(String key) throws RemoteException {
        return mCore.getRobotSettingManager().containsRobotConfig(key);
    }

    @Override
    public boolean hasRobotSetting(String key) {
        return mCore.getRobotSettingManager().hasRobotSetting(key);
    }

    @Override
    public String getRobotSetting(String key) {
        return mCore.getRobotSettingManager().getRobotSetting(key, false);
    }

    @Override
    public void setRobotSetting(String key, String value) {
        try {
            mCore.getRobotSettingManager().setRobotSetting(key, value);
        } catch (Exception e) {
            BinderExceptionHandler.handle(e);
        }
    }

    @Override
    public void registerRobotSettingListener(List<String> keyList, IRobotSettingListener listener) {
        mCore.getRobotSettingManager().registerListener(keyList, listener);
    }

    @Override
    public void unregisterRobotSettingListener(IRobotSettingListener listener) {
        mCore.getRobotSettingManager().unRegisterListener(listener);
    }
}
