package com.ainirobot.coreservice.action.advnavi.state;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.BasePoseBean;
import com.ainirobot.coreservice.client.actionbean.MultiRobotStatus;
import com.ainirobot.coreservice.client.actionbean.NavigationAdvancedBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.utils.DelayTask;

import java.util.List;

/**
 * Data: 2022/9/27 15:45
 * Author: wanglijing
 * 点位优先级火车导航
 * prepare -> 判断补位点是否可达 -> 延迟确认点位是否可达 -> navi -> nextPose
 * 按补位点列表依次导航，到达出餐点后，状态结束
 * 该状态会自己导航到目的地
 */
public class NaviTrainByPointStrategy extends BaseMultiRobotNaviState {

    /**
     * 当前位置名称
     */
    private String mCurrentPlace;

    private int mIndex;

    private volatile MultipleStatus multipleStatus;

    /**
     * 仅在导航时补位: false 机器推动离开位置也补位（默认）, true 机器导航离开才补位
     */
    private boolean mFillingStrategy = false;

    /**
     * 记录占用补位目标点的机器id,mFillingStrategy = true时生效,仅导航任务会清理该标记位
     */
    private MultiRobotStatus mInPoseRobotStatus;

    /**
     * 临时导航点
     */
    private Pose mTempNaviPose;

    private enum MultipleStatus {
        IDLE,
        START,
        WAIT_HANDLE_STATUS,   //处理状态
        HANDLE_STATUS_FINISH, //完成处理状态
        WAIT_CONFORM,
        WAIT_CONFORM_FINISH,
        NAVIGATION,
    }

    NaviTrainByPointStrategy(String name) {
        super(name);
    }

    @Override
    protected void preAction(StateData data, IStateListener listener) {
        super.preAction(data, listener);
        updateMultiStatus(MultipleStatus.IDLE);
        addDestinationToStandbyList();
        mCurrentPlace = getCurrentPlaceName();
        findNextNaviPose();
        mFillingStrategy = mStateData.getNavigationBean().isFillingStrategy();
    }

    @Override
    protected void doAction() {
        super.doAction();
        updateMultiStatus(MultipleStatus.START);
    }

    @Override
    protected void onNavigationResult(int result, String message, String extraData) {
        switch (result) {
            case Definition.ERROR_IN_DESTINATION:
            case Definition.RESULT_OK:
                if (hasNextPose()) {
                    findNextPose();
                    //STATUS_NAVIGATION_TRANSFER_REPLACE_DESTINATION
                    processStatusWithDestination(Definition.STATUS_NAVI_ARRIVED_REPLACE_DESTINATION, extraData);
                } else {
                    updateMultiStatus(MultipleStatus.IDLE);
                    //ComponentResult.RESULT_NAVIGATION_TRANSFER_ARRIVED,
                    arrivedMainDestination();
                }
                break;
        }
    }

    @Override
    protected void handleMultipleRobotStatus(List<MultiRobotStatus> robotStatusList) {
        switch (multipleStatus) {
            case START:
            case WAIT_CONFORM_FINISH: //已确认了目的地信息
            case HANDLE_STATUS_FINISH: //完成了上一次状态处理
                handleDestinationStatus(robotStatusList);
                break;
        }
    }

    /**
     * 处理多机状态
     *
     * @param robotStatusList 多机状态
     */
    private void handleDestinationStatus(List<MultiRobotStatus> robotStatusList) {
        boolean isConformedStatus = multipleStatus == MultipleStatus.WAIT_CONFORM_FINISH;

        updateMultiStatus(MultipleStatus.WAIT_HANDLE_STATUS);
        DestinationState desState = judgeDestinationIsAvailableDynamic(robotStatusList);
        Log.i(TAG, "handleDestinationStatus: " + desState);
        switch (desState) {
            case AVAILABLE:
                //确认可以到达，导航去该点
                if (isConformedStatus) {
                    Log.i(TAG, "conformed " + mTempNaviPose.getName() + " available");
                    updateMultiStatus(MultipleStatus.NAVIGATION);
                    startGoPose();
                    return;
                }

                conformDestination();
                break;
            case NERA_DESTINATION:
                //确认在目标点范围内，结束任务
                updateMultiStatus(MultipleStatus.IDLE);
                //RESULT_NAVIGATION_IN_DESTINATION_RANGE
                onResult(Definition.RESULT_DESTINATION_IN_RANGE);
                break;
            case DESTINATION_INVALID:
                //目的地为空，上报异常
                Log.e(TAG, "handleDestinationStatus error !!!");
                updateMultiStatus(MultipleStatus.IDLE);
                onResult(Definition.ERROR_NO_AVAILABLE_DESTINATION);
            case OCCUPIED:
            case NAVI_IN_RANGE:
            default:
                updateMultiStatus(MultipleStatus.HANDLE_STATUS_FINISH);
                break;
        }

    }

    private void startGoPose() {
        NavigationAdvancedBean bean = cloneNaviBean();
        bean.setDestination(mTempNaviPose.getName());

        startNavigation(bean);
    }

    private void conformDestination() {
        //如果可以到达，延迟一定时间后确认是否真的可以到达，
        updateMultiStatus(MultipleStatus.WAIT_CONFORM);
        long delayTime = calculateDelayTimeDynamic();
        Log.e(TAG, "delayTime:" + delayTime);
        delayConfirmDestination(delayTime);
    }

    private void delayConfirmDestination(long delayTime) {
        DelayTask.cancel(DELAY_TAG);
        DelayTask.submit(DELAY_TAG, new Runnable() {
            @Override
            public void run() {
                if (multipleStatus == MultipleStatus.WAIT_CONFORM) {
                    updateMultiStatus(MultipleStatus.WAIT_CONFORM_FINISH);
                }
            }
        }, delayTime);
    }

    /**
     * 找到要导航的下一个点
     */
    private void findNextNaviPose() {
        Log.d(TAG, "findNextNaviPose");
        Pose nextPose = null;
        for (int i = mValidStandbyList.size() - 1; i >= 0; i--) {
            Pose poseInfo = mValidStandbyList.get(i);
            if (poseInfo.getName().equals(mCurrentPlace)) {
                //mValidStandbyList中0点是备餐点肯定不是列表第一个，即 i>0
                //那么需要导航到前一个点，即mIndex需要i - 1
                //否则就是出餐口这个点，直接mIndex = 0
                mIndex = i > 0 ? i - 1 : 0;
                nextPose = mValidStandbyList.get(mIndex);
                Log.d(TAG, "findNextNaviPose currentPose: " + poseInfo.getName()
                        + " ,nextPose:" + nextPose.getName() + ", index:" + mIndex);
                break;
            }
        }

        if (nextPose == null) {
            Log.e(TAG, "findNextNaviPose next pose is null");
            //ERROR_DESTINATION_LIST_NOT_EXIST
            onResult(Definition.ERROR_TARGET_NOT_FOUND,"next pose not fond");
        }

        Log.i(TAG, "findNextNaviPose update");
        updateTempNaviPose(nextPose);
    }


    private DestinationState judgeDestinationIsAvailableDynamic(List<MultiRobotStatus> robotStatusList) {
        if (mTempNaviPose == null) {
            return DestinationState.DESTINATION_INVALID;
        }
        if (robotStatusList == null || robotStatusList.size() <= 0) {
            return DestinationState.AVAILABLE;
        }
        for (int j = 0; j < robotStatusList.size(); j++) {
            MultiRobotStatus robotStatus = robotStatusList.get(j);
            //status 为1表示无定位 2表示未在导航 3表示在导航中
            if (robotStatus.isCurRobot() || robotStatus.getPose() == null) {
                continue;
            }
            if (robotStatus.getStatus() == 2 || robotStatus.getStatus() == 3) {
                if (calculateTargetPoseInUse(mTempNaviPose, robotStatus.getPose())
                        || calculateTargetPoseInNavigation(mTempNaviPose, robotStatus)) {
                    mInPoseRobotStatus = robotStatus;
                    Log.e(TAG, "dynamicPose find robotId:" + mInPoseRobotStatus.getId() + " in pose:" + mInPoseRobotStatus.getPose());
                    return DestinationState.OCCUPIED;
                }
            }
        }
        if (!mFillingStrategy || mInPoseRobotStatus == null) {
            Log.d(TAG, "dynamicPose not use mFillingStrategy :" + mFillingStrategy + ", mInPoseRobotStatus:" + mInPoseRobotStatus);
        } else {
            //机器id的机器人是怎么离开的
            for (int k = 0; k < robotStatusList.size(); k++) {
                MultiRobotStatus robotStatus = robotStatusList.get(k);
                //status 为1表示无定位 2表示未在导航 3表示在导航中
                if (robotStatus.isCurRobot() || robotStatus.getPose() == null) {
                    continue;
                }
                if (mInPoseRobotStatus.getId() == robotStatus.getId() && robotStatus.getStatus() == 2) {
                    return DestinationState.OCCUPIED;
                }
                if (mInPoseRobotStatus.getId() == robotStatus.getId() && robotStatus.getStatus() == 3) {
                    Log.i(TAG, "dynamicPose robotId:" + mInPoseRobotStatus.getId() + " is leave pose:" + mTempNaviPose.getName());
                    mInPoseRobotStatus = null;
                    return DestinationState.AVAILABLE;
                }
            }
        }
        return DestinationState.AVAILABLE;
    }


    /**
     * 判断是否有机器正在导航到同一目标点
     *
     * @return true 有机器正在导航去该点 false 没机器导航去该点
     */
    private boolean calculateTargetPoseInNavigation(Pose pose, MultiRobotStatus status) {

        double distance = calculateTargetPoseDistance(pose, status.getGoal());
        boolean inRange = isInPoseRange(distance);
        Log.d(TAG, "check pose in navigation , distance:" + distance
                + " ,inRange: " + inRange
                + " ,robotId: " + status.getId()
                + " ,robotStatus: " + status.getStatus());

        return status.getStatus() == 3 && inRange;
    }

    private double calculateTargetPoseDistance(Pose pose, BasePoseBean goalPose) {
        return Math.sqrt(Math.pow((pose.getX() - goalPose.getX()), 2)
                + Math.pow((pose.getY() - goalPose.getY()), 2));
    }

    /**
     * 计算延迟导航任务需要多久下发
     */
    private long calculateDelayTimeDynamic() {
        long time = 400;
        for (int i = 0; i < mIndex; i++) {
            time = 400 + time / 2;
        }
        return time;
    }

    private boolean hasNextPose() {
        if (TextUtils.equals(getNaviDestination(), mDestination)) {
            return false;
        }
        if (mIndex - 1 >= 0) {
            Log.i(TAG, "hasNextPose index:" + mIndex);
            return true;
        } else {
            Log.i(TAG, "noNextPose index:" + mIndex);
            return false;
        }
    }

    private void findNextPose() {
        updateTempNaviPose(mValidStandbyList.get(mIndex - 1));
        mIndex--;
        conformDestination();
        Log.i(TAG, "findNextPose :" + mTempNaviPose.getName() + ", next mIndex:" + mIndex + ", state:" + multipleStatus);
    }

    private void updateMultiStatus(MultipleStatus status) {
        Log.i(TAG, "updateMultiStatus: " + status);
        this.multipleStatus = status;
    }

    private void updateTempNaviPose(Pose pose) {
        Log.i(TAG, "updateTempNaviPose: " + pose);
        mTempNaviPose = pose;
    }
}

