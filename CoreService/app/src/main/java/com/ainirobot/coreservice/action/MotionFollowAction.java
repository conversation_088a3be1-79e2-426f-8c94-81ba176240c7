package com.ainirobot.coreservice.action;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.bi.report.MotionFollowActionReporter;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.actionbean.BodyFollowBean;
import com.ainirobot.coreservice.client.listener.Person;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.utils.DelayTask;
import com.google.gson.reflect.TypeToken;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 基础运动跟随
 * <p>
 * Action id 35
 * <p>
 * 线速度、角速度、机器与人体距离，可配置；
 * 障碍物安全距离、加速度，不可配置；
 * <p>
 * Action层面处理实时人体采集、实时障碍物状态判断和运动等基础跟随能力，
 * 业务、配置参数以及体验相关的逻辑在组件层封装；
 */
public class MotionFollowAction extends AbstractAction<BodyFollowBean> {
    private static final String TAG = MotionFollowAction.class.getSimpleName();
    private static final int TIMEOUT_SEARCH_FACE = 30 * 1000;
    private static final int TIMEOUT_SET_TRACK = 20 * 1000;
    private static final Object TAG_FPS_COUNTS = new Object();
    private static final Object TAG_SET_TRACK = new Object();
    private static final Object TAG_RESET_TRACK = new Object();

    private int mFollowId = Integer.MIN_VALUE;
    private IFollowPolicy mCurrentPolicy;
    private Timer mLostTimer;
    private Timer mTimeoutTimer;
    private String mObstacleState;
    private long mLostTimeOut = TIMEOUT_SEARCH_FACE;
    private long mSetTrackTimeOut = TIMEOUT_SET_TRACK;
    private volatile boolean mHasObstacle = false;
    private PersonState mPersonState = PersonState.IDLE;
    private double mLinearSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_FOLLOW_LINEAR_SPEED;
    private double mAngularSpeed = SettingsUtil.ROBOT_SETTING_DEFAULT_FOLLOW_ANGULAR_SPEED;
    private double mDistanceR2P = SettingsUtil.ROBOT_SETTING_FOLLOW_DISTANCE_TO_PERSON;
    private Handler mMotionHandler;
    private HandlerThread mMotionThread;

    enum PersonState {
        PREPARE,
        SET_TRACK,
        TRACKING,
        NEAR,
        LOST,
        IDLE;
    }

    //持续1s没有人体数据认为人丢失
    private static final long PERSON_CHECK_TIME = 500;

    interface IFollowPolicy {
        boolean startFollow();

        boolean stopFollow();

        boolean onCmdResponse(int cmdId, String cmdType, String params);
    }

    protected MotionFollowAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_BODY_FOLLOW, resList);
        mMotionThread = new HandlerThread("MotionFollowActionThread");
        mMotionThread.start();
        mMotionHandler = new MotionHandler(mMotionThread.getLooper());
    }

    @Override
    protected boolean startAction(BodyFollowBean parameter, LocalApi api) {
        Log.d(TAG, "startAction:: parameter= " + parameter.toString());
        MotionFollowActionReporter.startActionReport(TAG + "_startAction",
                parameter.toString());
        parseParams(parameter);
        mPersonState = PersonState.PREPARE;
        mCurrentPolicy.startFollow();
        return true;
    }

    private void parseParams(BodyFollowBean parameter) {
        mCurrentPolicy = new BodyPolicy();
        mReqId = parameter.getReqId();
        mFollowId = parameter.getPersonID();
        mFollowId = mFollowId >= 0 ? mFollowId : Definition.BODY_FOLLOW_PERSON_ID_LARGE;
        mLostTimeOut = parameter.getLostTime() > 0 ? parameter.getLostTime() : TIMEOUT_SEARCH_FACE;
        mSetTrackTimeOut = parameter.getSetTrackTime() > 0 ?
                parameter.getSetTrackTime() : TIMEOUT_SET_TRACK;
        if (parameter.getLinearSpeed() > 0) {
            mLinearSpeed = parameter.getLinearSpeed();
        }
        if (parameter.getAngularSpeed() > 0) {
            mAngularSpeed = parameter.getAngularSpeed();
        }
        mDistanceR2P = parameter.getDistanceR2P();
        if (mDistanceR2P < SettingsUtil.ROBOT_SETTING_FOLLOW_DISTANCE_TO_PERSON_MIN) {
            mDistanceR2P = SettingsUtil.ROBOT_SETTING_FOLLOW_DISTANCE_TO_PERSON_MIN;
        }
    }

    @Override
    protected boolean verifyParam(BodyFollowBean param) {
        return true;
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        MotionFollowActionReporter.stopActionReport(TAG + "_stopAction");
        mCurrentPolicy.stopFollow();
        mCurrentPolicy = null;
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        if (mCurrentPolicy != null)
            mCurrentPolicy.onCmdResponse(cmdId, command, result);
        return true;
    }

    private void handleTrackResult(String params) {
        MotionFollowActionReporter.handleTrackResultReport(TAG + "_handleTrackResult", params);
        try {
            Log.d(TAG, "Set track target result : " + params);
            JSONObject json = new JSONObject(params);
            String status = json.getString("status");
            switch (status) {
                case Definition.RESPONSE_OK:
                case Definition.RESPONSE_ALREADY_IN_MODE:
                    onTrackSuccess();
                    updateStatus(Definition.STATUS_TRACK_TARGET_SUCCEED
                            , "Track target succeed");
                    break;

                case Definition.JSON_HEAD_TIMEOUT:
                case Definition.FAILED:
                default:
                    onTrackFailed(status);
                    break;
            }
        } catch (JSONException e) {
            processError(Definition.ERROR_SET_TRACK_FAILED, "Set track target failed");
        }
    }

    @Override
    public boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        return false;
    }

    @Override
    protected ArrayList<StopCommandList.StopCommand> getStopCommands() {
        return null;
    }

    @Override
    protected StopCommandList.IStopCommandChecker getStopCommandChecker() {
        return null;
    }

    private class BodyPolicy implements IFollowPolicy {

        @Override
        public boolean startFollow() {
            Log.d(TAG, "BodyPolicy startFollow mFollowId=" + mFollowId);
            MotionFollowActionReporter.setTrackTargetReport(TAG + "_setTrackTarget",
                    "", mFollowId, Definition.TrackMode.BODY_FOLLOW.toString());
            mApi.startVisionBodyTracker(mReqId, "", mFollowId, Definition.TrackMode.BODY_FOLLOW);
            mPersonState = PersonState.SET_TRACK;
            startSetTrackTimer();
            return true;
        }

        @Override
        public boolean stopFollow() {
            Log.d(TAG, "BodyPolicy::stopFollow");
            onFollowStop();
            return true;
        }

        @Override
        public boolean onCmdResponse(int cmdId, String cmdType, String params) {
            switch (cmdType) {
                case Definition.CMD_HEAD_START_BODY_TRACKER:
                    handleTrackResult(params);
                    return true;
                case Definition.CMD_HEAD_GET_ALL_PERSON_INFOS:
                    return handleBodyInfos(params);
                case Definition.CMD_NAVI_SET_MIN_OBSTACLES_DISTANCE:
                    handleSetMinObstaclesDistance(params);
                    return true;
                case Definition.CMD_DUMP_START_RECORD:
                case Definition.CMD_DUMP_STOP_RECORD:
                case Definition.CMD_DUMP_GET_KEY_FRAME:
                    Log.d(TAG, "Data dump result : cmdType=" + cmdType + ", params=" + params);
                    return true;

                default:
                    return true;
            }
        }

        private void handleSetMinObstaclesDistance(String params) {
            MotionFollowActionReporter.setObstaclesSafeDistanceResponseReport(
                    TAG + "_handleSetMinObstaclesDistance", params);
            if (Definition.SUCCEED.equals(params)) {
                registerObstaclesState();
            }
        }

        private boolean handleBodyInfos(String params) {
//            Log.d(TAG, "handleBodyInfos, params=" + params);
            MotionFollowActionReporter.handleBodyInfosReport(TAG + "_handleBodyInfos", params);
            List<Person> list = parsePersonData(params);
            Person person = (list.size() > 0 && list.get(0).getId() == 11311) ? list.get(0) : null;
            if (person != null) {
                mFpsComing.incrementAndGet();
                checkPersonState(person);
                if (person.getDistance() < 0) {//无效距离
                    Log.e(TAG, "handleBodyInfos:: Invalid person : id=" + person.getId()
                            + " distance=" + person.getDistance() + " angle=" + person.getAngle());
                    person.setDistance(0);
                }
                if (mMotionHandler != null) {
                    mMotionHandler.sendMessage(mMotionHandler.obtainMessage(100, person));
                }
            }
            return true;
        }
    }

    private volatile AtomicInteger mFpsComing = new AtomicInteger(0);

    private void startFrameCount() {
        DelayTask.submit(TAG_FPS_COUNTS, new Runnable() {
            @Override
            public void run() {
                int fps = mFpsComing.getAndSet(0);
                Log.i(TAG, "Fps :newFrame coming fps=" + fps);
                updateStatus(Definition.STATUS_FOLLOW_PERSON_FPS, fps + "");
            }
        }, 1000, 1000);
    }

    /**
     * 监听障碍物避停状态
     */
    private void registerObstaclesState() {
        MotionFollowActionReporter.registerObstaclesStateReport(TAG + "_registerObstaclesState");
        if (!TextUtils.isEmpty(mObstacleState)) {
            return;
        }
        mObstacleState = registerStatusListener(Definition.STATUS_STATIC_AVOID_STOPPING
                , new StatusListener() {
                    @Override
                    public void onStatusUpdate(String data) {
                        handleAvoidState(data);
                    }
                });
    }

    private void handleAvoidState(String data) {
        MotionFollowActionReporter.handleAvoidStateReport(TAG + "_handleAvoidState", data);
        Log.d(TAG, "Obstacle sate : " + data + ", mPersonState=" + mPersonState);
        if (TextUtils.isEmpty(data)) {
            return;
        }

        try {
            JSONObject jsonObject = new JSONObject(data);
            mHasObstacle = jsonObject.optBoolean(Definition.JSON_NAVI_IS_AVOID_STOPPING);
            if (mHasObstacle && mPersonState != PersonState.NEAR) {
                updateStatus(Definition.STATUS_NAVI_OBSTACLES_AVOID, "Obstacles stop");
            } else {
                updateStatus(Definition.STATUS_NAVI_OBSTACLES_DISAPPEAR, "Obstacles disappear");
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void unregisterObstaclesState() {
        unregisterStatusListener(mObstacleState);
        mObstacleState = null;
    }

    private void onTrackSuccess() {
        cancelSetTrackTimer();
        MotionFollowActionReporter.onTrackSuccessReport(TAG + "_onTrackSuccess");
        Log.d(TAG, "onTrackSuccess");
        mPersonState = PersonState.TRACKING;
        MotionFollowActionReporter.setObstaclesSafeDistanceReport(
                TAG + "_setObstaclesSafeDistance", mDistanceR2P);
        mApi.setObstaclesSafeDistance(mReqId, mDistanceR2P);
        MotionFollowActionReporter.getAllPersonInfosReport(TAG + "_getAllPersonInfos");
        mApi.getAllPersonInfos(mReqId, LocalApi.CONTINUOUS);
        startLostDetection();
        startFrameCount();
        startDumpData();
        mApi.resetHead(mReqId);
    }

    private void onTrackFailed(String status) {
        Log.d(TAG, "onTrackFailed, mPersonState=" + mPersonState);
        if (mPersonState == PersonState.SET_TRACK) {
            updateStatus(Definition.STATUS_FOLLOW_RESET_TRACK
                    , "Reset track : " + status);
            DelayTask.submit(TAG_RESET_TRACK, new Runnable() {
                @Override
                public void run() {
                    if (mPersonState == PersonState.SET_TRACK) {
                        Log.d(TAG, "onTrackFailed, Retry set track");
                        mApi.startVisionBodyTracker(mReqId, "", mFollowId,
                                Definition.TrackMode.BODY_FOLLOW);
                    }
                }
            }, 500);
        }
    }

    private void startDumpData() {
        Log.d(TAG, "startDumpData");
//        mApi.startDumpRecord(mReqId);
//        mApi.getDumpKeyFrame(mReqId);
    }

    private void stopDumpData() {
        Log.d(TAG, "stopDumpData");
//        mApi.stopVisionRecord(mReqId);
    }

    private class MotionHandler extends Handler {

        public MotionHandler(Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(Message msg) {
            if (msg == null) {
                return;
            }
            if (msg.obj == null || !(msg.obj instanceof Person)) {
                return;
            }
            keepMotion((Person) msg.obj);
        }
    }

    private void keepMotion(Person person) {
        Log.i(TAG, "keepMotion : mPersonState=" + mPersonState + " mHasObstacle=" + mHasObstacle);
        MotionFollowActionReporter.keepMotionReportEntrance(TAG + "_keepMotion",
                mPersonState.toString(), mHasObstacle);

        if (person == null) {
            Log.e(TAG, "keepMotion : Person null error");
            return;
        }
        final Person curPerson = person;

        if (mHasObstacle) {
            curPerson.setDistance(0);
        }
        if (mPersonState == PersonState.NEAR) {
            curPerson.setDistance(0);
        } else if (mPersonState == PersonState.LOST) {
            updateStatus(Definition.STATUS_FOLLOW_PERSON_NULL_STOP
                    , "Miss person in a row, stop motion arc");
            curPerson.setDistance(0);
            curPerson.setAngle(0);
        }

        MotionFollowActionReporter.keepMotionReportExport(TAG + "_keepMotion",
                curPerson.getId(), curPerson.getAngle(), curPerson.getDistance());
        Log.d(TAG, "keepMotion : personId = " + curPerson.getId()
                + ", angle = " + curPerson.getAngle()
                + ", distance = " + curPerson.getDistance());
        motionArc((float) curPerson.getDistance(), curPerson.getAngle(),
                curPerson.getHeadSpeed(), curPerson.getLatency());
    }

    /**
     * 非法值-1的motion运动按照0距离处理，但不能把-1当作靠近状态的条件
     *
     * @param person
     */
    private void checkPersonState(Person person) {
        startLostDetection();
        switch (mPersonState) {
            case LOST:
                updateStatus(Definition.STATUS_GUEST_APPEAR, "Guest appear");
                if (person.getDistance() >= 0 && person.getDistance() < mDistanceR2P) {
                    processNearSate();
                } else {
                    processTrackingSate();
                }
                break;
            case TRACKING:
                if (person.getDistance() >= 0 && person.getDistance() <= mDistanceR2P) {
                    processNearSate();
                }
                break;
            case NEAR:
                if (person.getDistance() > mDistanceR2P) {
                    processTrackingSate();
                }
                break;

            default:
                break;
        }
    }

    private void processNearSate() {
        Log.d(TAG, "processNearSate");
        mPersonState = PersonState.NEAR;
        updateStatus(Definition.STATUS_GUEST_NEAR, "Guest near, less than 1 m");
    }

    private void processTrackingSate() {
        Log.d(TAG, "processTrackingSate");
        mPersonState = PersonState.TRACKING;
        updateStatus(Definition.STATUS_FOLLOW_TRACKING, "Guest tracking");
    }

    private void motionArc(float distance, float angle, float headSpeed, float latency) {
        Log.d(TAG, "motionArc: " + distance + " " + angle + " " + headSpeed + " " + latency
                + " Max speed: " + mLinearSpeed + " " + mAngularSpeed
                + " mDistanceR2P: " + mDistanceR2P);
        MotionFollowActionReporter.motionArcWithObstaclesReport(TAG + "_motionArcWithObstacles"
                , distance, angle, headSpeed, latency, true, mDistanceR2P, mLinearSpeed,
                mAngularSpeed);
        mApi.motionArcWithObstacles(mReqId, distance, angle, headSpeed, latency,
                true, mDistanceR2P,
                mLinearSpeed, mAngularSpeed);
    }

    private void onFollowStop() {
        MotionFollowActionReporter.stopActionReport(TAG + "_onFollowStop");
        Log.d(TAG, "onFollowStop");
        mHasObstacle = false;
        mPersonState = PersonState.IDLE;
        DelayTask.cancel(TAG_FPS_COUNTS);
        cancelLostTimer();
        cancelTimeoutTimer();
        cancelSetTrackTimer();
        unregisterObstaclesState();
        mApi.resetObstaclesSafeDistance(mReqId);
        mApi.resetHead(mReqId);
        mApi.stopSendPersonInfos();
        mApi.stopMove(mReqId);
        mApi.stopVisionBodyTracker(mReqId);
        stopDumpData();
    }

    private List<Person> parsePersonData(String params) {
        if (mPersonState == PersonState.IDLE) {
            return Collections.emptyList();
        }
        if (TextUtils.isEmpty(params)) {
            return Collections.emptyList();
        }

        Type type = new TypeToken<List<Person>>() {
        }.getType();
        List<Person> result;
        try {
            result = mGson.fromJson(params, type);
        } catch (Exception e) {
            result = new ArrayList<>();
        }
        return result;
    }

    /**
     * 人丢失计时，默认1s认为处于丢失状态
     */
    private void startLostDetection() {
        cancelLostTimer();
        cancelTimeoutTimer();
        mLostTimer = new Timer();
        mLostTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                Log.d(TAG, "Lost timer run, mPersonState=" + mPersonState);
                if (mPersonState == PersonState.IDLE) {
                    return;
                }
                mPersonState = PersonState.LOST;
                if (mMotionHandler != null) {
                    mMotionHandler.sendMessage(mMotionHandler.obtainMessage(100, new Person()));
                }
                updateStatus(Definition.STATUS_GUEST_LOST, "Person lost");
                startTimeoutDetection();
            }
        }, PERSON_CHECK_TIME);
    }

    private void cancelLostTimer() {
        if (mLostTimer != null) {
            mLostTimer.cancel();
            mLostTimer = null;
        }
    }

    /**
     * 人丢失超时计时，默认30s丢人超时
     */
    private void startTimeoutDetection() {
        cancelTimeoutTimer();
        mTimeoutTimer = new Timer();
        mTimeoutTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                Log.d(TAG, "Lost time out timer run, mPersonState=" + mPersonState);
                if (mPersonState != PersonState.LOST) {
                    return;
                }
                mCurrentPolicy.stopFollow();
                processError(Definition.ERROR_FOLLOW_TIME_OUT, "Guest Lost time out");
            }
        }, mLostTimeOut);
    }

    private void cancelTimeoutTimer() {
        if (mTimeoutTimer != null) {
            mTimeoutTimer.cancel();
            mTimeoutTimer = null;
        }
    }

    private void startSetTrackTimer() {
        Log.d(TAG, "startSetTrackTimer");
        cancelSetTrackTimer();
        DelayTask.submit(TAG_SET_TRACK, new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "Set track time out timer run, mPersonState=" + mPersonState);
                if (mPersonState != PersonState.SET_TRACK) {
                    return;
                }
                mCurrentPolicy.stopFollow();
                processError(Definition.ERROR_FOLLOW_SET_TRACK_TIME_OUT,
                        "Set vision track time out");
            }
        }, mSetTrackTimeOut);
    }

    private void cancelSetTrackTimer() {
        Log.d(TAG, "cancelSetTrackTimer");
        DelayTask.cancel(TAG_SET_TRACK);
    }

    private void updateStatus(int status, String data) {
        Log.d(TAG, "updateStatus : " + status + " " + data);
        MotionFollowActionReporter.statusReport(TAG + "_updateStatus", status, data);
        super.onStatusUpdate(status, data);
    }

    private void processError(int errorCode, String errorMessage) {
        Log.d(TAG, "processError : " + errorCode + " " + errorMessage);
        MotionFollowActionReporter.resultReport(TAG + "_processError", errorCode, errorMessage);
        super.onError(errorCode, errorMessage);
    }

}
