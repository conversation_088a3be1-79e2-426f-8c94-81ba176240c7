package com.ainirobot.coreservice.client.actionbean;/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

public class HeadTurnBean {

    private HeadTurnMode horizontalMode;
    private HeadTurnMode verticalMode;
    private int horizontalAngle;
    private int verticalAngle;
    private int horizontalMaxSpeed;
    private int verticalMaxSpeed;

    public enum HeadTurnMode {
        absolute, relative
    }

    public HeadTurnBean() {
        horizontalMode = HeadTurnMode.relative;
        horizontalAngle = 0;
        horizontalMaxSpeed = 30;
        verticalMode = HeadTurnMode.relative;
        verticalAngle = 0;
        verticalMaxSpeed = 30;
    }

    public HeadTurnMode getHorizontalMode() {
        return horizontalMode;
    }

    public void setHorizontalMode(HeadTurnMode horizontalMode) {
        this.horizontalMode = horizontalMode;
    }

    public HeadTurnMode getVerticalMode() {
        return verticalMode;
    }

    public void setVerticalMode(HeadTurnMode verticalMode) {
        this.verticalMode = verticalMode;
    }

    public int getHorizontalAngle() {
        return horizontalAngle;
    }

    public void setHorizontalAngle(int horizontalAngle) {
        this.horizontalAngle = horizontalAngle;
    }

    public int getVerticalAngle() {
        return verticalAngle;
    }

    public void setVerticalAngle(int verticalAngle) {
        this.verticalAngle = verticalAngle;
    }

    public int getHorizontalMaxSpeed() {
        return horizontalMaxSpeed;
    }

    public void setHorizontalMaxSpeed(int horizontalMaxSpeed) {
        this.horizontalMaxSpeed = horizontalMaxSpeed;
    }

    public int getVerticalMaxSpeed() {
        return verticalMaxSpeed;
    }

    public void setVerticalMaxSpeed(int verticalMaxSpeed) {
        this.verticalMaxSpeed = verticalMaxSpeed;
    }

    public boolean isHorizontalMove() {
        return horizontalMode != HeadTurnMode.relative || horizontalAngle != 0;
    }

    public boolean isVerticalMove() {
        return verticalMode != HeadTurnMode.relative || verticalAngle != 0;
    }

    @Override
    public String toString() {
        return "HeadTurnBean horizontalMode: " + horizontalMode + ", verticalMode: " + verticalMode
                + ", horizontalAngle: " + horizontalAngle + ", verticalAngle: " + verticalAngle
                + ", horizontalSpeed: " + horizontalMaxSpeed + ", verticalSpeed: " + verticalMaxSpeed;
    }
}
