/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.data.provider;

public class Column {
    public enum Constraint {
        /**
         * 数据库列约束
         */
        UNIQUE("UNIQUE"),
        NOT("NOT"),
        NULL("NULL"),
        CHECK("CHECK"),
        FOREIGN_KEY("FOREIGN KEY"),
        PRIMARY_KEY("PRIMARY KEY"),
        AUTO_INCREMENT("PRIMARY KEY AUTOINCREMENT"),
        IDENTITY("identity(1,1)");

        private String value;

        Constraint(String value) {
            this.value = value;
        }

        @Override
        public String toString() {
            return value;
        }
    }

    public enum DataType {
        /**
         * 数据库列数据类型
         */
        NULL, INTEGER, REAL, TEXT, BLOB,TIMESTAMP
    }

    private String mColumnName;

    private Constraint mConstraint;

    private DataType mDataType;

    Column(String columnName, Constraint constraint, DataType dataType) {
        mColumnName = columnName;
        mConstraint = constraint;
        mDataType = dataType;
    }

    String getColumnName() {
        return mColumnName;
    }

    Constraint getConstraint() {
        return mConstraint;
    }

    DataType getDataType() {
        return mDataType;
    }
}
