/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.data.robotsetting;

import static com.ainirobot.coreservice.data.robotsetting.SettingDataHelper.SettingField.COLUMN_ID;
import static com.ainirobot.coreservice.data.robotsetting.SettingDataHelper.SettingField.COLUMN_VALUE;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.os.Environment;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.R;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.config.RobotSettingConfig;
import com.ainirobot.coreservice.utils.FileUtils;
import com.ainirobot.coreservice.utils.Utils;
import com.google.gson.Gson;

import java.io.File;
import java.util.Set;

public class SettingInitDataHelper extends BaseInitDataHelper {

    private static final String TAG = SettingInitDataHelper.class.getSimpleName();
    private static final String TEST_FILE = Environment.getExternalStorageDirectory()
            + File.separator + "settingTest.json";
    private static final String TEST_FILE_AGENT = Environment.getExternalStorageDirectory()
            + File.separator + "settingAgentTest.json";
    private static final String VERSION_COLUMN_ID = "robot_setting_config_version";
    private static final String SPECIAL_KEY = "robot_settings_charging_type";
    private static final String SPECIAL_VALUE = "charging_pile";

    private RobotSettingConfig mConfig;
    private Context mContext;
    private boolean mIsAgentOS;

    SettingInitDataHelper(Context context) {
        super(SettingDataHelper.TABLE);
        Gson gson = new Gson();
        mContext = context;
        
        // 检查系统类型
        String systemOSType = RobotSettings.getGlobalSettings(mContext,
                Definition.ROBOT_SETTINGS_SYSTEM_OS_TYPE, "");
        Log.i(TAG, "Constructor: Raw systemOSType from settings: '" + systemOSType + "'");
        
        
        mIsAgentOS = TextUtils.equals(systemOSType, Definition.OSType.AGENTOS.getValue());
        Log.i(TAG, "Constructor: System OS Type: '" + systemOSType + "', isAgentOS: " + mIsAgentOS);
        
        // 根据系统类型选择配置文件
        if (mIsAgentOS) {
            // AgentOS 使用 settingagent.json
            Log.i(TAG, "Loading AgentOS configuration from settingagent.json");
            String configJson = Utils.loadConfig(mContext, TEST_FILE_AGENT, R.raw.settingagent);
            mConfig = gson.fromJson(configJson, RobotSettingConfig.class);
            Log.i(TAG, "AgentOS config loaded, version: " + mConfig.getVersion());
        } else {
            // RobotOS 使用原来的 setting.json
            Log.i(TAG, "Loading RobotOS configuration from setting.json");
            mConfig = gson.fromJson(
                    Utils.loadConfig(mContext, TEST_FILE, R.raw.setting), RobotSettingConfig.class);
            Log.i(TAG, "RobotOS config loaded, version: " + mConfig.getVersion());
        }
    }



    @Override
    void init(SQLiteDatabase db) {
        Log.i(TAG, "init db");
        
        // 如果是 AgentOS，检查是否需要重建或升级
        if (mIsAgentOS) {
            // 检查数据库中是否已经是 AgentOS 的数据
            boolean isFirstTimeAgentOS = checkIfFirstTimeAgentOS(db);
            
            if (isFirstTimeAgentOS) {
                // 首次切换到 AgentOS，删除表并重新创建
                Log.i(TAG, "First time switching to AgentOS, recreating setting table");
                dropAndRecreateTable(db);
            } else {
                // 已经是 AgentOS，检查是否需要版本升级
                checkAndUpgradeVersion(db);
            }
            return;
        }
        
        // RobotOS 保持原有逻辑
        checkAndUpgradeVersion(db);
    }
    
    /**
     * 删除并重新创建表
     */
    private void dropAndRecreateTable(SQLiteDatabase db) {
        // 先检查表是否存在
        Cursor cursor = db.rawQuery("SELECT name FROM sqlite_master WHERE type='table' AND name=?", 
                new String[]{mTableField.getTableName()});
        boolean tableExists = cursor.getCount() > 0;
        cursor.close();
        
        if (tableExists) {
            Log.i(TAG, "Table exists, dropping and recreating");
            // 删除表
            db.execSQL("DROP TABLE IF EXISTS " + mTableField.getTableName());
        }
        
        // 重新创建表和数据
        onCreate(db);
    }
    
    /**
     * 检查版本并在需要时进行升级
     */
    private void checkAndUpgradeVersion(SQLiteDatabase db) {
        Cursor cursor = null;
        try {
            cursor = db.query(mTableField.getTableName(), new String[]{COLUMN_VALUE},
                    COLUMN_ID + "=?", new String[]{VERSION_COLUMN_ID},
                    null, null, null);
            if (cursor.getCount() > 0) {
                cursor.moveToFirst();
                int oldVersion = Integer.parseInt(cursor.getString(0));
                if (oldVersion < mConfig.getVersion()) {
                    if (mIsAgentOS) {
                        Log.i(TAG, "AgentOS version upgrade from " + oldVersion + " to " + mConfig.getVersion());
                    }
                    updateData(db);
                }
            } else {
                // 如果没有版本信息，可能是新数据库，需要初始化
                Log.i(TAG, "No version info found, initializing database");
                onCreate(db);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking version", e);
            // 出错时尝试重新创建
            onCreate(db);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
    }
    
    /**
     * 检查是否是首次切换到 AgentOS
     * 通过检查数据库中的 robot_os_type 是否为 agentos 来判断
     */
    private boolean checkIfFirstTimeAgentOS(SQLiteDatabase db) {
        Cursor cursor = null;
        try {
            // 检查 robot_os_type 是否为 agentos
            cursor = db.query(mTableField.getTableName(), new String[]{COLUMN_VALUE},
                    COLUMN_ID + "=?", new String[]{"robot_os_type"},
                    null, null, null);
            
            if (cursor.getCount() > 0) {
                cursor.moveToFirst();
                String osType = cursor.getString(0);
                // 如果 os_type 是 agentos，说明已经是 AgentOS
                boolean isAgentOS = TextUtils.equals(osType, Definition.OSType.AGENTOS.getValue());
                Log.i(TAG, "Current robot_os_type: " + osType + ", is AgentOS: " + isAgentOS);
                return !isAgentOS;  // 如果不是 agentos，返回 true（首次切换）
            } else {
                // 没有 robot_os_type 数据，可能是新数据库或旧版本
                Log.i(TAG, "No robot_os_type found, considering as first time");
                return true;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking if first time AgentOS", e);
            return true;  // 出错时保守处理，当作首次切换
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
    }

    @Override
    void onCreate(SQLiteDatabase db) {
        Log.i(TAG, "create db");
        db.execSQL(mTableField.getCreateString());
        ContentValues contentValues = new ContentValues();
        contentValues.put(COLUMN_ID, VERSION_COLUMN_ID);
        contentValues.put(COLUMN_VALUE, mConfig.getVersion());
        db.insert(mTableField.getTableName(), null, contentValues);
        mConfig.parseSetting();
        Set<String> keys = mConfig.getSettingKeys();
        for (String key : keys) {
            insertNew(db, key);
        }
        //型号为非招财Pro的招财，且Properties中配置有ChargeIR，更新为桩充
        if (ProductInfo.isDeliveryProduct() && !ProductInfo.isSaiphPro() && !ProductInfo.isAlnilamPro() && FileUtils.hasChargeIR()) {
            insertOrUpdate(db, SPECIAL_KEY, SPECIAL_VALUE);
        }
    }

    @Override
    void onUpgrade(SQLiteDatabase db) {

    }

    @Override
    void onDowngrade(SQLiteDatabase db) {
        Log.i(TAG, "onDowngrade : table name=" + mTableField.getTableName());
        mTableField.delete(db);
        onCreate(db);
    }

    private void insertNew(SQLiteDatabase db, String key) {
        String value = Settings.Global.getString(mContext.getContentResolver(), key);
        if (value == null) {
            Log.d(TAG, "insertNew add from config");
            value = mConfig.getValue(key);
        }
        Log.d(TAG, "insertNew key: " + key + ", value: " + value);
        ContentValues contentValues = new ContentValues();
        contentValues.put(COLUMN_ID, key);
        contentValues.put(COLUMN_VALUE, value);
        db.insert(mTableField.getTableName(), null, contentValues);
    }

    private void updateData(SQLiteDatabase db) {
        Log.i(TAG, "update data");
        ContentValues contentValues = new ContentValues();
        contentValues.put(COLUMN_VALUE, mConfig.getVersion());
        db.update(mTableField.getTableName(), contentValues,
                COLUMN_ID + "=?", new String[]{VERSION_COLUMN_ID});
        mConfig.parseSetting();
        Set<String> keys = mConfig.getSettingKeys();
        for (String key : keys) {
            Cursor cursor = db.query(mTableField.getTableName(), new String[]{COLUMN_VALUE},
                    COLUMN_ID + "=?", new String[]{key},
                    null, null, null);
            if (cursor.getCount() > 0) {
//                String value = mConfig.getValue(key);
//                Log.d(TAG, "update key: " + key + ", value: " + value);
//                ContentValues updateValues = new ContentValues();
//                updateValues.put(COLUMN_VALUE, value);
//                db.update(mTableField.getTableName(), updateValues, COLUMN_ID + "=?",
//                        new String[]{String.valueOf(key)});
                cursor.close();
                continue;
            }
            insertNew(db, key);
            cursor.close();
        }
    }

    private void insertOrUpdate(SQLiteDatabase db, String key, String value) {
        Log.d(TAG, "insertNew key: " + key + ", value: " + value);
        ContentValues contentValues = new ContentValues();
        contentValues.put(COLUMN_ID, key);
        contentValues.put(COLUMN_VALUE, value);
        Cursor cursor = db.query(mTableField.getTableName(), new String[]{COLUMN_VALUE},
                COLUMN_ID + "=?", new String[]{key},
                null, null, null);
        if (cursor.getCount() > 0) {
            Log.d(TAG, "update key: " + key + ", value: " + value);
            db.update(mTableField.getTableName(), contentValues, COLUMN_ID + "=?",
                    new String[]{String.valueOf(key)});
        } else {
            Log.d(TAG, "insert key: " + key + ", value: " + value);
            db.insert(mTableField.getTableName(), null, contentValues);
        }
        cursor.close();
    }

}