package com.ainirobot.coreservice.action.advnavi.state;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.NavigationResetHeadUtils;
import com.ainirobot.coreservice.action.StatusListener;
import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.action.advnavi.util.PoseTransformation;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.MultiRobotConfigBean;
import com.ainirobot.coreservice.client.actionbean.MultiRobotStatus;
import com.ainirobot.coreservice.client.actionbean.NavigationAdvancedBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.utils.DelayTask;
import com.google.gson.reflect.TypeToken;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.Iterator;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

public abstract class BaseNavigationState extends BaseState {

    private static final double MIN_DISTANCE = 0.1; //unit meter
    private static final int DEFAULT_MAX_AVOID_COUNT = 5;
    private static final long DEFAULT_AVOID_INTERVAL_TIME = 5 * 1000;
    private static final double DEFAULT_NEAR_DISTANCE = 3.0d;

    private static final String NAVI_TAG = "base_navi_tag";

    private String mPoseListener;
    private Pose mFlagPose;
    private Pose mCurrentPose;
    /**
     * 本次任务的最终目的地名称
     */
    protected String mDestination;

    /**
     * 目的地点对象
     */
    protected Pose mDestinationPose;

    /**
     * 导航使用的目的地（分阶段导航，有可能和最终目的地不一样）
     */
    private String mNaviTempDestination;
    /**
     * 是否是在多机避障状态，如果是则停止处理机器N秒不动退出导航的逻辑
     **/
    private volatile boolean isMultipleWaitStatus = false;
    private Timer mWaitTimer;
    private Timer mAvoidTimer;

    private int mMaxAvoidCount = DEFAULT_MAX_AVOID_COUNT;
    private int mAvoidCounter = 0;
    private long mAvoidIntervalTime = DEFAULT_AVOID_INTERVAL_TIME;
    private String mMultipleRobotStatusListener;

    protected NavigationAdvancedBean mNavigationBean;
    /**
     * 多机状态
     */
    protected volatile List<MultiRobotStatus> mRobotStatusList;

    /**
     * 是否已经开始导航了
     */
    protected volatile boolean mHasStartNavigation = false;

    /**
     * 坐标偏差
     */
    private double mCoordinateDeviation;

    protected String mNaviParams;

    private volatile float mCurrentDistance;
    private double mDestinationX;
    private double mDestinationY;
    private String mEstimateListener;
    /**
     * 是否有新的实时pose上报
     * 切图成功后置为false
     * status_pose上报后置为true
     */
    private volatile boolean mHasNewPose = false;

    protected BaseNaviStatus baseNaviStatus;

    protected enum BaseNaviStatus {
        IDLE,
        NAVIGATION,
        DESTINATION_NEAR
    }

    BaseNavigationState(String name) {
        super(name);
    }


    @Override
    protected void preAction(StateData data, IStateListener listener) {
        super.preAction(data, listener);
        updateNaviStatus(BaseNaviStatus.IDLE);
        mNavigationBean = mStateData.getNavigationBean();
        mDestination = mStateData.getDestination();
        mDestinationPose = getPoseByName(mDestination);
        mNaviParams = mNavigationBean.getNavigationParams();
    }

    protected void startNavigation(NavigationAdvancedBean navigationBean) {
        isMultipleWaitStatus = false;
        mNavigationBean = navigationBean;
        mNaviTempDestination = navigationBean.getDestination();
        checkNavigationConfig();
        checkPoseEstimate();
        registerPoseListener();
        registerEstimateStatusListener();
        queryCurrentDeviceMultipleConfig();
        Log.d(TAG, "startNavigation tempDestination=" + mNaviTempDestination + " ," + mNavigationBean);
    }

    /**
     * 停止导航
     * <p>
     * 通过processStopNavigation方法处理回调消息
     */
    protected void stopNavigation() {
        Log.d(TAG, "stopNavigation");
        stopGetDistance();
        updateNaviStatus(BaseNaviStatus.IDLE);
        mApi.stopNavigation(mReqId);
    }

    protected void updateNaviStatus(BaseNaviStatus baseNaviStatus) {
        Log.i(TAG, "updateNaviStatus: " + baseNaviStatus);
        this.baseNaviStatus = baseNaviStatus;
    }

    @Override
    protected boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_GO_LOCATION:
            case Definition.CMD_NAVI_GO_POSITION:
                processNavigationStatus(status, extraData);
                return true;
            default:
                return mNavigationResetHeadUtils.cmdStatusUpdate(cmdId, command, status, extraData);
        }
    }

    @Override
    protected boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_GO_LOCATION:
            case Definition.CMD_NAVI_GO_POSITION:
                processNavigationResult(result, extraData);
                return true;
            case Definition.CMD_NAVI_IS_ESTIMATE:
                processPoseEstimate(result, extraData);
                return true;
            case Definition.CMD_NAVI_IS_IN_LOCATION:
                verifyLocation(result, extraData);
                return true;
            case Definition.CMD_NAVI_GET_LOCATION:
                processGetLocation(result, extraData);
                return true;
            case Definition.CMD_NAVI_GET_MULTI_ROBOT_CONFIG:
                processMultiRobotConfig(result);
                return true;
            case Definition.CMD_NAVI_STOP_NAVIGATION:
                processStopNavigation(result);
                return true;
            default:
                return mNavigationResetHeadUtils.onCmdResponse(cmdId, command, result, extraData);
        }
    }

    /**
     * 停止导航成功
     */
    protected void processStopNavigation(String result) {

    }

    protected void processStatusWithDestination(int status, String extraData) {
        processWithDestination(status, extraData, true);
    }

    /**
     * 到达目的地
     */
    protected void arrivedMainDestination() {
        //ComponentResult.RESULT_NAVIGATION_TRANSFER_ARRIVED
        processResultWithDestination(Definition.RESULT_NAVIGATION_ARRIVED, "");
    }

    protected void processResultWithDestination(int result, String extraData) {
        processWithDestination(result, extraData, false);
    }

    private void processWithDestination(int result, String extraData, boolean isStatus) {
        Log.d(TAG, "processWithDestination id: " + result
                + ", extraData:" + extraData);

        JSONObject jsonObject = new JSONObject();
        try {
            boolean isMainDes = TextUtils.equals(mNaviTempDestination, mDestination);

            jsonObject.put("mainDestination", isMainDes);
            jsonObject.put("destination", mNaviTempDestination);

            if (isStatus) {
                onStatusUpdate(result, jsonObject.toString(), extraData);
            } else {
                onResult(result, jsonObject.toString(), extraData);
            }
        } catch (JSONException e) {
            e.printStackTrace();
            if (!isStatus) {
                onResult(result, "", extraData);
            }
        }
    }

    protected String getNaviDestination() {
        return mNaviTempDestination;
    }

    /**
     * 检查导航配置是否有问题
     */
    private void checkNavigationConfig() {
        NavigationAdvancedBean bean = mNavigationBean;
        if (null == bean) {
            onResult(Definition.ERROR_PARAMS_JSON_PARSER_ERROR, "json parse exception");
            return;
        }

        if (!(bean.getNaviType() == NavigationAdvancedBean.TYPE_POSE
                || bean.getNaviType() == NavigationAdvancedBean.TYPE_DESTINATION)) {
            onResult(Definition.ERROR_PARAMS_JSON_PARSER_ERROR, "navi type param error");
            return;
        }

        if (bean.getNaviType() == NavigationAdvancedBean.TYPE_DESTINATION
                && TextUtils.isEmpty(bean.getDestination())) {
            onResult(Definition.ERROR_DESTINATION_NOT_EXIST);
            return;
        }

        Pose destinationPose = bean.getNaviPose();
        if (bean.getNaviType() == NavigationAdvancedBean.TYPE_POSE
                && ((destinationPose == null) || (destinationPose.getTheta() == 0
                && destinationPose.getX() == 0
                && destinationPose.getY() == 0))) {
            onResult(Definition.ERROR_PARAMS_JSON_PARSER_ERROR, "pose parse error");
            return;
        }

        mMaxAvoidCount = bean.getMaxAvoidCount();
        mAvoidIntervalTime = bean.getAvoidIntervalTime();
        mCoordinateDeviation = bean.getCoordinateDeviation();
        mNaviParams = bean.getNavigationParams();

    }

    @Override
    protected void exit() {
        super.exit();
        stopNavigation();
        updateMultipleRobotWaitingStatus(false);
        cancelWaitTimer();
        cancelAvoidTimer();
        unregisterStatusListener(mPoseListener);
        unregisterStatusListener(mMultipleRobotStatusListener);
        unregisterStatusListener(mEstimateListener);
        mMultipleRobotStatusListener = null;
        mFlagPose = null;
        mCurrentPose = null;
    }

    protected abstract void onNavigationStatusUpdate(int result, String message, String extraData);

    protected abstract void onNavigationError(int result, String message, String extraData);

    protected abstract void onNavigationResult(int result, String message, String extraData);

    /**
     * 处理多机状态
     */
    protected abstract void handleMultipleRobotStatus(List<MultiRobotStatus> robotStatusList);

    /**
     * 是否启用多机状态 return:true
     * 启用后多机状态异常会导致action停止
     */
    protected abstract boolean isEnableMultiRobotStatus();

    private void queryCurrentDeviceMultipleConfig() {
        mApi.queryCurrentDeviceMultipleConfig(mReqId);
    }

    private void processMultiRobotConfig(String result) {
        try {
            MultiRobotConfigBean multiRobotConfig = mGson.fromJson(result, MultiRobotConfigBean.class);
            if (multiRobotConfig.getErrorStatus() > 0 || !multiRobotConfig.isEnable()) {
                Log.d(TAG, "Lora config fail! " + multiRobotConfig.getErrorStatus() + " " + multiRobotConfig.isEnable());
                return;
            }
            //需要监听多机状态，监听为空，未注册，尝试注册
            if (mMultipleRobotStatusListener == null
                    && isEnableMultiRobotStatus()) {
                registerMultiRobotStatusListener();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    protected void registerMultiRobotStatusListener() {
        mMultipleRobotStatusListener = registerStatusListener(Definition.STATUS_MULTIPLE_ROBOT_WORKING, new StatusListener() {
            @Override
            public void onStatusUpdate(String data) {
                if (!isAlive()) {
                    return;
                }
                //需要监听才处理
                if (isEnableMultiRobotStatus()) {
                    handleStatusWork(data);
                }
            }
        });
    }

    private void handleStatusWork(String data) {
        try {
            Type dataType = new TypeToken<List<MultiRobotStatus>>() {
            }.getType();
            mRobotStatusList = mGson.fromJson(data, dataType);
            boolean canContinue = checkMultiRobotStatus(mRobotStatusList);
            if (canContinue) {
                handleMultipleRobotStatus(mRobotStatusList);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 当底盘的多机状态报错时，导航任务不能继续
     * 多机报错状态只有当前机器才会上报，其他机器的errorStatus无意义。
     * 当时间戳相差超过5s 认为该条状态不可用【删除该状态】
     *
     * @param robotStatusList 多机状态信息
     * @return true无报错信息任务继续  false会结束当前任务。
     */
    private boolean checkMultiRobotStatus(List<MultiRobotStatus> robotStatusList) {
        for (int i = 0; i < robotStatusList.size(); i++) {
            MultiRobotStatus robotStatus = robotStatusList.get(i);
            if (robotStatus.isCurRobot()) {
                //删除超时的状态
                checkMultiRobotStatusTimeout(robotStatus.getTime(), robotStatusList);
                //判断error状态
                return checkMultiRobotErrorStatus(robotStatus);
            }
        }
        return true;
    }

    /**
     * 找到当前多机状态中是否有超时的状态，若有则删除
     *
     * @param curTime         当前机器的时间戳
     * @param robotStatusList 多机状态列表
     */
    private void checkMultiRobotStatusTimeout(long curTime, List<MultiRobotStatus> robotStatusList) {
        //遍历找到超时的状态数据，并删除
        Iterator<MultiRobotStatus> iterator = robotStatusList.iterator();
        while (iterator.hasNext()) {
            MultiRobotStatus robotStatus = (MultiRobotStatus) iterator.next();
            //数据延迟超过5s，认为该条数据不可用状态
            if (Math.abs(curTime - robotStatus.getTime()) > 5000) {
                Log.e(TAG, "!! checkMultiRobotStatusTimeout remove timeout status : " + robotStatus);
                iterator.remove();
            }
        }
        mRobotStatusList = robotStatusList;
    }

    /**
     * 处理当前机器人的error状态
     *
     * @param robotStatus 多机列表
     * @return true无报错信息任务继续  false会结束当前任务。
     */
    private boolean checkMultiRobotErrorStatus(MultiRobotStatus robotStatus) {
        switch (robotStatus.getErrorStatus()) {
            case Definition.MULTIPLE_STATUS_ERROR_MAP_NOT_MATCH:
                onNavigationError(Definition.STATUS_NAVI_MULTI_MAP_NOT_MATCH,
                        "Multiple map not match!", "");
                //这里不处理地图不匹配的情况，只处理由navi层直接上报的地图不匹配状态
                return true; //这里的地图不匹配不影响多机策略，底盘上报的地图不匹配会直接结束任务
            case Definition.MULTIPLE_STATUS_ERROR_LORA_DISCONNECT:
                onNavigationError(Definition.STATUS_NAVI_MULTI_LORA_DISCONNECT,
                        "Lora disconnect!", "");
                Log.e(TAG, "checkMultiRobotErrorStatus: Lora disconnect!");
                return false;
            case Definition.MULTIPLE_STATUS_ERROR_VERSION_NOT_MATCH:
                onNavigationError(Definition.STATUS_NAVI_MULTI_VERSION_NOT_MATCH,
                        "Multiple version not match", "");
                Log.e(TAG, "checkMultiRobotErrorStatus: Multiple version not match");
                return false;
            case Definition.MULTIPLE_STATUS_NORMAL:
            default:
                return true;
        }
    }

    private void checkPoseEstimate() {
        Log.d(TAG, "Check pose estimate");
        mApi.isRobotEstimate(mReqId);
    }

    private void processPoseEstimate(String result, String extraData) {
        if (!"true".equals(result)) {
            onNavigationError(Definition.ERROR_NOT_ESTIMATE, "Not pose estimate", extraData);
            return;
        }
        isRobotInLocation();
    }

    /**
     * 处理导航result，与NavigationAction处理保持一致
     */
    private void processNavigationResult(String result, String extraData) {
        Log.d(TAG, "processNavigationResult : " + result);
        stopGetDistance();  //navigation result

        if (Definition.NAVIGATION_OK.equals(result)) {
            if (baseNaviStatus != BaseNaviStatus.IDLE) {
                onNavigationResult(Definition.RESULT_OK, result, extraData);
            } else {
                onNavigationResult(Definition.RESULT_STOP, result, extraData);
            }
            return;
        }
        if (Definition.NAVIGATION_CANCELED.equals(result)) {
            onNavigationResult(Definition.RESULT_STOP, result, extraData);
            return;
        }
        onNavigationError(Definition.ERROR_NAVIGATION_FAILED, "Navigation failed : " + result, extraData);
    }

    private void isRobotInLocation() {
        JSONObject obj = new JSONObject();
        try {
            obj.put(Definition.JSON_NAVI_TARGET_PLACE_NAME, mNavigationBean.getDestination());
            obj.put(Definition.JSON_NAVI_COORDINATE_DEVIATION, mNavigationBean.getCoordinateDeviation());
            obj.put(Definition.IS_IN_LOCATION_CHECK_THETA, mNavigationBean.isInLocationCheckTheta());
            mApi.isRobotInlocations(mReqId, obj.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void verifyLocation(String result, String extraData) {
        try {
            JSONObject json = new JSONObject(result);
            boolean isInLocation = json.optBoolean(Definition.JSON_NAVI_IS_IN_LOCATION, false);
            if (isInLocation) {
                //stop();
                onNavigationResult(Definition.ERROR_IN_DESTINATION, "Already at the " + mNavigationBean.getDestination(), extraData);
            } else {
                mApi.getLocation(mReqId, mNavigationBean.getDestination());
            }
        } catch (JSONException e) {
            e.printStackTrace();
            onNavigationError(Definition.ERROR_IN_DESTINATION, "Judge at the " + mNavigationBean.getDestination() + " failed", extraData);
        }
    }

    private void processGetLocation(String result, String extraData) {
        try {
            JSONObject json = new JSONObject(result);
            boolean isExist = json.getBoolean(Definition.JSON_NAVI_SITE_EXIST);
            if (mNavigationBean.getNaviType() == NavigationAdvancedBean.TYPE_POSE || isExist) {
                mNavigationResetHeadUtils.checkResetHeadState(new NavigationResetHeadUtils.ResetHeadListener() {
                    @Override
                    public void onResetHeadSuccess() {
                        mDestinationX = json.optDouble("px", 0.0f);
                        mDestinationY = json.optDouble("py", 0.0f);
                        if (mNavigationBean.getNaviType() == NavigationAdvancedBean.TYPE_POSE) {
                            mApi.goPosition(mReqId, mNavigationBean.getNaviPose(),
                                    mNavigationBean.getLinearSpeed(),
                                    mNavigationBean.getAngularSpeed(),
                                    mNavigationBean.isAdjustAngle(),false,
                                    mNavigationBean.getDestinationRange(),
                                    mNavigationBean.getPriority(),
                                    mNavigationBean.getLinearAcceleration(),
                                    mNavigationBean.getAngularAcceleration(),
                                    mNavigationBean.getStartModeLevel(),
                                    mNavigationBean.getBrakeModeLevel(),
                                    mNavigationBean.getObsDistance(),
                                    mNavigationBean.getPosTolerance(),
                                    mNavigationBean.getAngleTolerance(),-1);
                        } else {
                            mApi.goPlace(
                                    mReqId,
                                    mNavigationBean.getDestination(),
                                    mNavigationBean.getLinearSpeed(),
                                    mNavigationBean.getAngularSpeed(),
                                    mNavigationBean.isAdjustAngle(),
                                    mNavigationBean.getDestinationRange(),
                                    mNavigationBean.getPriority(),
                                    mNavigationBean.getLinearAcceleration(),
                                    mNavigationBean.getAngularAcceleration(),
                                    mNavigationBean.getStartModeLevel(),
                                    mNavigationBean.getBrakeModeLevel(),
                                    mNavigationBean.getObsDistance(),
                                    mNavigationBean.getPosTolerance(),
                                    mNavigationBean.getAngleTolerance(),
                                    mNavigationBean.getIsReversePoseTheta());
                        }
                        mNavigationResetHeadUtils.startHeadStatusListener(mReqId);
                    }

                    @Override
                    public void onResetHeadFailed(int errorCode, String errorString) {
                        onNavigationError(errorCode, errorString, "");
                    }
                }, mReqId);
            } else {
                onNavigationError(Definition.ERROR_DESTINATION_NOT_EXIST, "Destination not exist", extraData);
            }
        } catch (JSONException e) {
            e.printStackTrace();
            onNavigationError(Definition.ERROR_DESTINATION_NOT_EXIST, "Get " + mNavigationBean.getDestination() + " failed", extraData);
        }
    }

    private void processNavigationStatus(String status, String extraData) {
        switch (status) {
            case Definition.NAVIGATION_OUT_MAP:
                onNavigationError(Definition.STATUS_NAVI_OUT_MAP, "The goal is out of map or more dangerous", extraData);
                break;

            case Definition.NAVIGATION_GLOBAL_PATH_FAILED:
                onNavigationError(Definition.STATUS_NAVI_GLOBAL_PATH_FAILED, "The global is path search failed", extraData);
                break;

            case Definition.NAVIGATION_MULTI_MAP_NOT_MATCH:
                onNavigationError(Definition.STATUS_NAVI_MULTI_MAP_NOT_MATCH, "navigation multi robot map not match!", extraData);
                break;

            case Definition.NAVIGATION_MULTI_LORA_DISCONNECT:
                onNavigationError(Definition.STATUS_NAVI_MULTI_LORA_DISCONNECT, "navigation multi robot lora disconnect!", extraData);
                break;

            case Definition.NAVIGATION_MULTI_LORA_CONFIG_FAIL:
                onNavigationError(Definition.STATUS_NAVI_MULTI_LORA_CONFIG_FAIL, "navigation multi robot lora config fail!", extraData);
                break;

            case Definition.NAVIGATION_MULTI_VERSION_NOT_MATCH:
                onNavigationError(Definition.STATUS_NAVI_MULTI_VERSION_NOT_MATCH, "navigation multi robot version not match!", extraData);
                break;

            case Definition.NAVIGATION_WHEEL_SLIP:
                onNavigationError(Definition.STATUS_NAVI_WHEEL_SLIP, "navigation wheel slip", extraData);

                break;
            case Definition.NAVIGATION_OCCLUDED:
                onNavigationStatusUpdate(Definition.STATUS_GOAL_OCCLUDED, "The target is occluded", extraData);
                startAvoidTimer();
                break;
            case Definition.NAVIGATION_AVOID:
                onNavigationStatusUpdate(Definition.STATUS_NAVI_AVOID, "The local path search failed", extraData);
                startAvoidTimer();
                break;
            case Definition.NAVIGATION_OCCLUDED_END:
                if (cancelAvoidTimer()) {
                    onNavigationStatusUpdate(Definition.STATUS_GOAL_OCCLUDED_END, "The occluded end", extraData);
                }
                break;
            case Definition.NAVIGATION_AVOID_END:
                if (cancelAvoidTimer()) {
                    onNavigationStatusUpdate(Definition.STATUS_NAVI_AVOID_END, "The local path search succeed", extraData);
                }
                break;

            case Definition.NAVIGATION_OBSTACLES_AVOID:
                onNavigationStatusUpdate(Definition.STATUS_NAVI_OBSTACLES_AVOID, "obstacles avoid", extraData);
                break;

            case Definition.NAVIGATION_STARTED:
                updateNaviStatus(BaseNaviStatus.NAVIGATION);    // navigation_started
                onNavigationStatusUpdate(Definition.STATUS_START_NAVIGATION, "Navigation has been started", extraData);
                if (!mHasStartNavigation) {
                    mHasStartNavigation = true;
                    startGetDistanceInterval();
                }
                break;

            case Definition.NAVIGATION_MULTI_ROBOT_WAITING:
                updateMultipleRobotWaitingStatus(true);
                startWaitingTimer();
                onNavigationStatusUpdate(Definition.STATUS_NAVI_MULTI_ROBOT_WAITING, "Multiple robots are waiting to avoid obstacles", extraData);
                break;

            case Definition.NAVIGATION_MULTI_ROBOT_WAITING_END:
                updateMultipleRobotWaitingStatus(false);
                cancelWaitTimer();
                onNavigationStatusUpdate(Definition.STATUS_NAVI_MULTI_ROBOT_WAITING_END, "Multiple robots passageways returned to normal", extraData);
                break;

            case Definition.NAVIGATION_GO_STRAIGHT:
                onNavigationStatusUpdate(Definition.STATUS_NAVI_GO_STRAIGHT, "The robot is walking in a straight line", extraData);
                break;

            case Definition.NAVIGATION_TURN_LEFT:
                onNavigationStatusUpdate(Definition.STATUS_NAVI_TURN_LEFT, "The robot is currently turning left", extraData);
                break;

            case Definition.NAVIGATION_TURN_RIGHT:
                onNavigationStatusUpdate(Definition.STATUS_NAVI_TURN_RIGHT, "The robot is currently turning right", extraData);
                break;

            case Definition.NAVIGATION_SET_PRIORITY_FAILED:
                onNavigationStatusUpdate(Definition.STATUS_NAVI_SET_PRIORITY_FAILED, "Current navigation task priority setting failed!", extraData);
                break;
            default:
                Log.i(TAG, "Command status: " + status + " doesn't be handled");
                break;
        }
    }

    private void registerPoseListener() {
        mPoseListener = registerStatusListener(Definition.STATUS_POSE, new StatusListener() {
            @Override
            public void onStatusUpdate(String data) {
                if (isAlive() && baseNaviStatus != BaseNaviStatus.IDLE) {
                    onPoseUpdate(data);
                }
            }
        });
    }

    private synchronized void onPoseUpdate(String data) {
        if (TextUtils.isEmpty(data) || !isAlive()) {
            return;
        }
        setHasNewPose(true);
        updateCurrentDistance(data);  //更新currentDistance
        Pose pose = mGson.fromJson(data, Pose.class);
        mCurrentPose = pose;
        if (isAborted(pose)) {
            Log.d(TAG, "Pose timeout : " + mFlagPose + "  " + pose);
            onNavigationError(Definition.ERROR_DESTINATION_CAN_NOT_ARRAIVE, "navigation moving time out", "");
        }
    }

    /**
     * 更新多机避障状态，如果是多机避障状态，则机器人超时等待策略需忽略
     *
     * @param isWaiting 是否正在多机避障等待状态
     */
    private void updateMultipleRobotWaitingStatus(boolean isWaiting) {
        isMultipleWaitStatus = isWaiting;
    }

    private void startWaitingTimer() {
        cancelWaitTimer();
        try {
            mWaitTimer = new Timer();
            mWaitTimer.schedule(new TimerTask() {
                @Override
                public void run() {
                    if (!isAlive()) {
                        cancelWaitTimer();
                        return;
                    }
                    onNavigationError(Definition.ERROR_MULTI_ROBOT_WAITING_TIMEOUT, "multiple robot wait timeout", "");
                }
            }, mNavigationBean.getMultipleWaitTime());
        } catch (Exception e) {
            e.printStackTrace();
            cancelWaitTimer();
        }
    }

    private void cancelWaitTimer() {
        if (mWaitTimer != null) {
            mWaitTimer.cancel();
            mWaitTimer = null;
        }
    }

    private boolean isAborted(Pose pose) {
        try {
            double distance = pose.getDistance(mFlagPose);
            if (mFlagPose != null && !isMultipleWaitStatus && Double.compare(distance, MIN_DISTANCE) < 0) {
                long movingTime = pose.getTime() - mFlagPose.getTime();
                if (movingTime > mNavigationBean.getMovieTimeout()) {
                    Log.d(TAG, "isAborted movingTime: " + movingTime + "   timeout: " + mNavigationBean.getMovieTimeout() + "  " + mNavigationBean);
                    return true;
                }
            } else {
                mFlagPose = pose;
            }
        } catch (Exception e) {
            Log.e(TAG, "isAborted error ,pose=" + pose + " ,mFlagPose=" + mFlagPose + " ,isAlive=" + isAlive());
            e.printStackTrace();
        }
        return false;
    }

    private void startAvoidTimer() {
        if (mAvoidTimer == null) {
            mAvoidCounter = 0;
            try {
                mAvoidTimer = new Timer();
                mAvoidTimer.schedule(new TimerTask() {
                    @Override
                    public void run() {
                        if (!isAlive()) {
                            cancelAvoidTimer();
                            return;
                        }
                        mAvoidCounter++;
                        if (mAvoidCounter > mMaxAvoidCount) {
                            cancelAvoidTimer();
                            onNavigationError(Definition.ERROR_NAVIGATION_AVOID_TIMEOUT, "navigation avoid time out", "");
                        }
                    }
                }, mAvoidIntervalTime, mAvoidIntervalTime);
            } catch (Exception e) {
                e.printStackTrace();
                cancelAvoidTimer();
            }
        }
    }

    private boolean cancelAvoidTimer() {
        if (mAvoidTimer != null) {
            mAvoidTimer.cancel();
            mAvoidTimer = null;
            return true;
        }
        return false;
    }

    /**
     * 上报距离信息
     */
    private void startGetDistanceInterval() {
        DelayTask.cancel(NAVI_TAG);
        DelayTask.submit(NAVI_TAG, new Runnable() {
            @Override
            public void run() {
                if (mCurrentDistance >= 0
                        && baseNaviStatus != BaseNaviStatus.IDLE) {
                    onNavigationStatusUpdate(Definition.STATUS_DISTANCE_WITH_DESTINATION,
                            "current destination", String.valueOf(mCurrentDistance));
                    processNearDestination(mCurrentDistance);
                }
            }
        }, 0, mNavigationBean.getDistanceIntervalTime());
    }

    public void stopGetDistance() {
        DelayTask.cancel(NAVI_TAG); //stop get distance
        mHasStartNavigation = false;    //stop get distance
    }

    /**
     * 计算距离
     */
    private void updateCurrentDistance(String data) {
        try {
            JSONObject jsonObject = new JSONObject(data);
            double selfX = jsonObject.optDouble("px", 0.0f);
            double selfY = jsonObject.optDouble("py", 0.0f);
            mCurrentDistance = (float) Math.sqrt(Math.pow(Math.abs(selfX - mDestinationX), 2)
                    + Math.pow(Math.abs(selfY - mDestinationY), 2));
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }


    /**
     * 监听是否到目的地范围内
     */
    private void processNearDestination(float distance) {
        if (baseNaviStatus == BaseNaviStatus.DESTINATION_NEAR) {

            return;
        }
        if (distance > mCoordinateDeviation && distance < DEFAULT_NEAR_DISTANCE) {
            updateNaviStatus(BaseNaviStatus.DESTINATION_NEAR);  //near destination
            onNavigationStatusUpdate(Definition.STATUS_NAVI_NEAR_DESTINATION,
                    "near destination", String.valueOf(distance));
        }
    }

    /**
     * 定位状态监听
     */
    private void registerEstimateStatusListener() {
        mEstimateListener = registerStatusListener(Definition.STATUS_EVENT, new StatusListener() {
            @Override
            public void onStatusUpdate(String data) {
                Log.d(TAG, "estimate status listener: " + data);
                if (!isAlive()) {
                    return;
                }
                try {
                    JSONObject json = new JSONObject(data);
                    //定位丢失
                    if (json.has(Definition.HW_NAVI_ESTIMATE_LOST) && !TextUtils.isEmpty(json.optString(Definition.HW_NAVI_ESTIMATE_LOST))) {
                        //危险码导致的定位丢失，走系统接管
                        JSONObject lostMsgJson = new JSONObject(json.optString(Definition.HW_NAVI_ESTIMATE_LOST));
                        String lostCode = lostMsgJson.optString(Definition.HW_NAVI_ESTIMATE_LOSE_CODE);
                        if (TextUtils.equals(lostCode, Definition.ESTIMATE_LOST_BY_DANGER_CODE)) {
                            Log.e(TAG, "estimate lost by danger code");
                            onNavigationError(Definition.ERROR_NOT_ESTIMATE, "lost by danger code", data);
                            return;
                        }

                        //其他情况上报丢失状态
                        Log.e(TAG, "estimate lost");
                        onNavigationError(Definition.ERROR_ESTIMATE_ERROR, "", data);
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    protected NavigationAdvancedBean cloneNaviBean() {
        return mStateData.getNavigationBean().clone();
    }

    /**
     * 保存实际进电梯位置和当前楼层电梯中心位置的相对位姿
     */
    protected void saveDeltaPoseEnter() {
        Pose elevaterCenterPose = mStateData.getElevatorCenterPose();
        if (elevaterCenterPose != null && mCurrentPose != null) {
            Pose delta = PoseTransformation.relativePose(elevaterCenterPose, mCurrentPose);
            Log.d(TAG, "saveDeltaPoseEnter: delta=" + delta
                    + ", elevaterCenterPose=" + elevaterCenterPose + ", mCurrentPose=" + mCurrentPose);
            mStateData.setDeltaPoseEnter(delta);
        }
    }

    /**
     * 根据相对位姿恢复进电梯后的位姿
     * 入参：当前楼层电梯中心pose
     * 返回：进电梯时的位姿在当前楼层的pose
     */
    protected Pose getEnterPose(Pose elevatorCenterPose) {
        Pose delta = mStateData.getDeltaPoseEnter();
        if (delta == null) {
            return elevatorCenterPose;
        }
        Pose enterPose = PoseTransformation.finalPose(elevatorCenterPose, delta);
        Log.d(TAG, "getEnterPose: delta=" + delta
                + ", elevatorCenterPose=" + elevatorCenterPose + ", enterPose=" + enterPose);
        return enterPose;
    }

    protected void setHasNewPose(boolean hasNewPose){
        this.mHasNewPose = hasNewPose;
    }

    protected boolean hasNewPose(){
        return mHasNewPose;
    }
}
