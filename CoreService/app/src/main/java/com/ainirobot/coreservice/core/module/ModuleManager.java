/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.core.module;

import android.util.Log;
import android.util.SparseArray;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.client.techreport.CallChainReport;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;

public class ModuleManager {
    private static final String TAG = "ModuleManager";

    public enum Permission {
        ROOT(0),
        SYSTEM(1),
        BACKGROUND(10),
        APP(20),
        ALL(30),
        APP_ASK(40);

        int value;

        Permission(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        public static Permission fromValue(int value) {
            switch (value) {
                case 0:
                    return ROOT;
                case 1:
                    return SYSTEM;
                case 20:
                    return APP;
                case 40:
                    return APP_ASK;
                case 30:
                    return ALL;
                default:
                    return null;
            }
        }
    }

    private SparseArray<HashSet<Module>> mPermissionGroup;
    private HashMap<String, Module> mModules;
    private String mActiveAppModule;
    private CallChainReport mCallChain;

    public ModuleManager() {
        mPermissionGroup = new SparseArray<>();
        mModules = new HashMap<>();
        mCallChain = new CallChainReport(ApplicationWrapper.getContext(), this.getClass().getSimpleName());
    }

    public synchronized void addModule(Module module, Permission permission) {
        HashSet<Module> moduleSet = mPermissionGroup.get(permission.getValue());
        if (moduleSet == null) {
            moduleSet = new HashSet<>();
        }
        if (moduleSet.contains(module)) {
            moduleSet.remove(module);
        }
        moduleSet.add(module);
        mPermissionGroup.put(permission.getValue(), moduleSet);
        mModules.put(module.getPackageName(), module);
        mCallChain.setBusinessName("app_authorize");
        mCallChain.invokeNodeMethod("addModule");
        mCallChain.invokeNodeInfo("package name: "+module.getPackageName()+" ++ permission:"
                + permission, "module register RobotOS");
        mCallChain.report();
    }

    public Module getModule(String packageName) {
        return mModules.get(packageName);
    }

    public void setActiveAppModule(String packageName) {
        Log.d(TAG, "Set active module");
        mCallChain.setBusinessName("app_authorize");
        mCallChain.invokeNodeMethod("setActiveAppModule");
        mCallChain.invokeNodeInfo(mActiveAppModule, "set active app module");
        mCallChain.report();
        this.mActiveAppModule = packageName;
    }

    public String getActiveAppModule() {
        Log.d(TAG, "mActiveAppModule : " + mActiveAppModule);
        return mActiveAppModule;
    }

    public boolean activeModuleIsBackGround() {
        Set<Module> modules = getModules(Permission.BACKGROUND);
        for (Module module : modules) {
            return module.getPackageName().equals(mActiveAppModule);
        }
        return false;
    }

    public String getSystemModule() {
        Set<Module> modules = getActiveModules(Permission.SYSTEM);
        if (modules.iterator().hasNext()) {
            Module module = modules.iterator().next();
            return module.getPackageName();
        }
        return null;
    }

    public boolean isActiveAppModule(String packageName) {
        return packageName != null && packageName.equals(mActiveAppModule);
    }

    public synchronized void removeModule(String packageName) {
        Module module = mModules.remove(packageName);
        for (Permission permission : Permission.values()) {
            mPermissionGroup.get(permission.getValue()).remove(module);
        }
    }

    public synchronized void suspendModule(String packageName) {
        Module module = mModules.get(packageName);
        if (module != null) {
            module.suspend();
        }
    }

    public synchronized boolean contains(String packageName) {
        return mModules.containsKey(packageName);
    }

    public synchronized void recoveryModule(String packageName) {
        Module module = mModules.get(packageName);
        if (module != null) {
            module.recovery();
        }
    }

    public void suspendApp() {
        Set<Module> modules = getModules(Permission.APP);
        for (Module module : modules) {
            module.suspend();
        }
    }

    public Set<Module> getActiveModules(Permission permission) {
        if (permission == Permission.ALL) {
            return getAllModules();
        }

        /**
         * sort modules when permission is APP_ASK
         * this request resolve by app or system, system can handle it if app donot handle it
         */
        if (permission == Permission.APP_ASK) {
            Set<Module> modules = new HashSet<>();
            if (hasActiveBackGroundModule()) {
                modules = getModules(Permission.BACKGROUND);
            }
            if (hasActiveAppModule()) {
                modules = getModules(Permission.APP);
            }
            modules.addAll(getModules(Permission.ROOT));
            return modules;
        }

        if (permission == null) {
            if (hasActiveBackGroundModule()) {
                permission = hasActiveBackGroundModule() ? Permission.BACKGROUND : Permission.SYSTEM;
            } else {
                permission = hasActiveAppModule() ? Permission.APP : Permission.SYSTEM;
            }
        }

        return getModules(permission);
    }

    private synchronized boolean hasActiveBackGroundModule() {
        for (Module module : getModules(Permission.BACKGROUND)) {
            if (!module.isSuspend()) {
                return true;
            }
        }
        return false;
    }

    private synchronized boolean hasActiveAppModule() {
        for (Module module : getModules(Permission.APP)) {
            if (!module.isSuspend()) {
                return true;
            }
        }
        return false;
    }

    private synchronized Set<Module> getModules(Permission permission) {
        Set<Module> modules = mPermissionGroup.get(permission.getValue());
        return modules == null ? new HashSet<Module>() : new HashSet<>(modules);
    }

    private synchronized Set<Module> getAllModules() {
        Set<Module> modules = new HashSet<>();
        for (Permission permission : Permission.values()) {
            modules.addAll(getModules(permission));
        }
        return modules;
    }

}
