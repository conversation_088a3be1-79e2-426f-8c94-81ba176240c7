package com.ainirobot.coreservice.client.actionbean;

/**
 * Created by Orion on 2018/12/11.
 */
public class CruiseRouteBean {

    //unused fields
    private int routeID;
    private String routeName;
    private String dockIndexArray; //dock pose name list
    private int intervalTime; // (s)
    private int pauseTime; //dock pose time (s)

    //already used fields
    private String coordinate; //route pose name list
    private String mapName;
    private String speechText;
    private long speechInterval;
    /**
     * tts  播报TTS
     * advertising  播报广告
     * nothingPlaying 什么都不播放
     */
    private String palyInfo;

    public CruiseRouteBean(){
        this.routeID = 0;
        this.routeName = "CruiseRoute";
        this.intervalTime = 5;
        this.pauseTime = 5;
        this.dockIndexArray = "";
    }

    public String getPalyInfo() {
        return palyInfo;
    }

    public void setPalyInfo(String palyInfo) {
        this.palyInfo = palyInfo;
    }

    public int getRouteID() {
        return routeID;
    }

    public void setRouteID(int routeID){
        this.routeID = routeID;
    }

    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    public String getCoordinate() {
        return coordinate;
    }

    public void setCoordinate(String coordinate) {
        this.coordinate = coordinate;
    }

    public String getMapName() {
        return mapName;
    }

    public void setMapName(String mapName) {
        this.mapName = mapName;
    }

    public int getIntervalTime() {
        return intervalTime;
    }

    public void setIntervalTime(int intervalTime) {
        this.intervalTime = intervalTime;
    }

    public int getPauseTime() {
        return pauseTime;
    }

    public void setPauseTime(int pauseTime) {
        this.pauseTime = pauseTime;
    }

    public String getDockIndexArray() {
        return dockIndexArray;
    }

    public void setDockIndexArray(String dockIndexArray) {
        this.dockIndexArray = dockIndexArray;
    }

    public String getSpeechText() {
        return speechText;
    }

    public void setSpeechText(String speechText) {
        this.speechText = speechText;
    }

    public long getSpeechInterval() {
        return speechInterval;
    }

    public void setSpeechInterval(long speechInterval) {
        this.speechInterval = speechInterval;
    }

    @Override
    public String toString() {
        return "CruiseRouteBean{" +
                "routeID=" + routeID +
                ", routeName='" + routeName + '\'' +
                ", dockIndexArray='" + dockIndexArray + '\'' +
                ", intervalTime=" + intervalTime +
                ", pauseTime=" + pauseTime +
                ", coordinate='" + coordinate + '\'' +
                ", mapName='" + mapName + '\'' +
                ", speechText='" + speechText + '\'' +
                ", speechInterval=" + speechInterval +
                ", palyInfo='" + palyInfo + '\'' +
                '}';
    }
}
