package com.ainirobot.coreservice.action.advance_charge;

import com.ainirobot.coreservice.action.advance_charge.state.BaseAdvanceChargeState;
import com.ainirobot.coreservice.action.advance_charge.state.GoChargeAreaState;
import com.ainirobot.coreservice.action.advance_charge.state.GoAdvanceChargeState;
import com.ainirobot.coreservice.action.advance_charge.state.GoNormalChargeState;
import com.ainirobot.coreservice.action.advance_charge.state.PrepareState;

public enum AdvanceChargeStateEnum {

    /**
     * 准备阶段，判断有充电区设置，先去充电区；若无充电区，直接去充电
     */
    PREPARE(new PrepareState()),
    /**
     * 前往充电等候区
     */
    GO_CHARGE_AREA(new GoChargeAreaState()),
    /**
     * 高级回充，按充电桩优先级高低，找空闲的充电桩
     */
    GO_ADVANCE_CHARGE(new GoAdvanceChargeState()),

    /**
     * 普通回充，前往优先级最高的充电桩
     */
    GO_NORMAL_CHARGE(new GoNormalChargeState());

    private BaseAdvanceChargeState advanceChargeState;

    AdvanceChargeStateEnum(BaseAdvanceChargeState advanceChargeState) {
        this.advanceChargeState = advanceChargeState;
    }

    public BaseAdvanceChargeState getChargeState() {
        return advanceChargeState;
    }
}
