/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.action;

import android.util.Log;

import com.ainirobot.coreservice.action.StopCommandList.IStopCommandChecker;
import com.ainirobot.coreservice.action.StopCommandList.StopCommand;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.WakeUpBean;
import com.ainirobot.coreservice.config.ConfigManager;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.core.system.RobotSetting;
import com.ainirobot.coreservice.utils.DelayTimer;
import com.ainirobot.coreservice.utils.MessageParser;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class WakeupAction extends AbstractAction<WakeUpBean> {
    private static final String TAG = "WakeupAction";
    private static final int MAX_HEAD_ANGLE = 45; // Max angle that the navigation can move
    private static final int HEAD_ANGLE_RANGE = 5;

    private List<String> mWaitingCommands;
    private int mMoveHeadAngle = 0;

    private int mReqId;
    private int mAngle;
    private long mMoveHeadTimeout = Long.MAX_VALUE;
    private long mMoveHeadTime;
    private int mVHeadAngle;
    private float mBodySpeed;
    private static final long MOVE_DIRECTION_TIMEOUT = 5000;
    private DelayTimer moveDirTimer;
    private final static String MOVE_DIR_TIMEOUT = "move direction timeout";

    WakeupAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_WAKEUP, resList);
        mWaitingCommands = new ArrayList<>();
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_MOVE_DIRECTION:
                Log.d(TAG, "Turn result : " + result + "  waiting:" + mWaitingCommands.size());
                cancelMoveDirTimer();
                onResponse(Definition.CMD_NAVI_MOVE_DIRECTION, "wake up succeed");
                return true;

            case Definition.CMD_HEAD_GET_STATUS:
                handleHeadStatus(result);
                return true;

            case Definition.CMD_HEAD_MOVE_HEAD:
                handleMoveHeadResult(result);
                return true;

            case Definition.CMD_HEAD_STOP_TRACK_TARGET:
                Log.d(TAG, "Stop track : " + result);
                if (!ConfigManager.getHeadConfig().isSupportHorizontal()) {
                    wakeupWithoutMoveHead();
                } else if (!RobotSetting.isNavigationSupport()) {
                    wakeupWithoutNavigation();
                } else {
                    wakeup(mAngle);
                }
                return true;
        }
        return false;
    }

    @Override
    protected boolean startAction(WakeUpBean parameter, LocalApi api) {
        if (!ConfigManager.getHeadConfig().isSupportHorizontal() && !RobotSetting.isNavigationSupport()) {
            return false;
        }
        mAngle = (int) parameter.getAngle();
        mApi.stopTrack(mReqId);
        return true;
    }

    private void wakeupWithoutMoveHead() {
        int moveAngle = (mAngle < 180) ? mAngle : (mAngle - 360);
        int bodyTurnAngle = Math.abs(moveAngle);
        if (moveAngle < 0) {
            Log.i(TAG, "wakeupWithoutHead, turn left : " + bodyTurnAngle);
            mApi.wakeUpTurn(mReqId, mBodySpeed, bodyTurnAngle, true);
        } else {
            Log.i(TAG, "wakeupWithoutHead, turn right : " + bodyTurnAngle);
            mApi.wakeUpTurn(mReqId, mBodySpeed, bodyTurnAngle, false);
        }
        mWaitingCommands.add(Definition.CMD_NAVI_MOVE_DIRECTION);
    }

    private void wakeupWithoutNavigation() {
        int moveAngle = (mAngle < 180) ? mAngle : (mAngle - 360);
        moveHead(moveAngle);
    }

    private void wakeup(int angle) {
        int moveAngle = (angle < 180) ? angle : (angle - 360);
        if (Math.abs(moveAngle) <= MAX_HEAD_ANGLE) {
            Log.d(TAG, "Move head : " + moveAngle);
            moveHead(moveAngle);
        } else {
            int bodyTurnAngle = Math.abs(moveAngle) - MAX_HEAD_ANGLE;
            if (moveAngle < 0) {
                Log.i(TAG, "Move head : " + MAX_HEAD_ANGLE + "  and turn left : " + bodyTurnAngle);
                moveHead(-MAX_HEAD_ANGLE);
                mApi.wakeUpTurn(mReqId, mBodySpeed, bodyTurnAngle, true);
            } else {
                Log.i(TAG, "Move head : " + MAX_HEAD_ANGLE + "  and turn right : " + bodyTurnAngle);
                moveHead(MAX_HEAD_ANGLE);
                mApi.wakeUpTurn(mReqId, mBodySpeed, bodyTurnAngle, false);
            }
            mWaitingCommands.add(Definition.CMD_NAVI_MOVE_DIRECTION);
            startMoveDirTimer();
        }
    }

    private synchronized void startMoveDirTimer() {
        if (moveDirTimer == null) {
            moveDirTimer = new DelayTimer(MOVE_DIRECTION_TIMEOUT, new Runnable() {
                @Override
                public void run() {
                    Log.d(TAG, "move direction timeout");
                    onResponse(Definition.CMD_NAVI_MOVE_DIRECTION, MOVE_DIR_TIMEOUT);
                }
            });
            moveDirTimer.start();
        }
    }

    private synchronized void cancelMoveDirTimer() {
        if (moveDirTimer != null) {
            moveDirTimer.destroy();
            moveDirTimer = null;
        }
    }

    private void moveHead(int angle) {
        mMoveHeadAngle = angle;
        mApi.moveHead(mReqId, Definition.JSON_HEAD_ABSOLUTE,
                Definition.JSON_HEAD_ABSOLUTE, angle, mVHeadAngle);
        mWaitingCommands.add(Definition.CMD_HEAD_MOVE_HEAD);
        mMoveHeadTime = System.currentTimeMillis();
        mMoveHeadTimeout = (long) ((Math.abs(angle) / 30.0) * 1000 + 500);
    }

    private void handleMoveHeadResult(String params) {
        Log.d(TAG, "Move head result : " + params);
        boolean isSucceed = MessageParser.parseResult(params);
        if (isSucceed) {
            mApi.getHeadStatus(mReqId);
        } else {
            onError(Definition.ERROR_MOVE_HEAD_FAILED, "move head failed");
        }
    }

    private void handleHeadStatus(String params) {
        Log.d(TAG, "Get head status :" + params + "      " + mMoveHeadAngle);
        if (System.currentTimeMillis() - mMoveHeadTime > mMoveHeadTimeout) {
            onResponse(Definition.CMD_HEAD_MOVE_HEAD, "Move head timeout");
            return;
        }

        int horizontal;
        try {
            JSONObject jsonObject = new JSONObject(params);
            horizontal = jsonObject.optInt("horizontal", 0);

            if (Math.abs(mMoveHeadAngle - horizontal) < HEAD_ANGLE_RANGE) {
                Log.d(TAG, "Move head finished : " + mWaitingCommands.size());
                onResponse(Definition.CMD_HEAD_MOVE_HEAD, "wake up succeed");
            } else {
                mApi.getHeadStatus(mReqId);
            }
        } catch (JSONException e) {
            e.printStackTrace();
            onError(Definition.ERROR_RESULT_PARSE, "Get head status result error");
        }
    }

    private void onResponse(String type, String message) {
        mWaitingCommands.remove(type);
        if (mWaitingCommands.isEmpty()) {
            onResult(Definition.RESULT_OK, message);
        }
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        cancelMoveDirTimer();
        mWaitingCommands.clear();
        mApi.moveHead(mReqId, Definition.JSON_HEAD_RELATIVE, Definition.JSON_HEAD_RELATIVE, 0, 0);
        mApi.stopMove(mReqId);
        mApi.stopNavigation(mReqId);
        return true;
    }

    @Override
    protected ArrayList<StopCommand> getStopCommands() {
        return null;
    }

    @Override
    protected IStopCommandChecker getStopCommandChecker() {
        return null;
    }

    @Override
    protected boolean verifyParam(WakeUpBean param) {
        mReqId = param.getReqId();
        int vHeadAngle = param.getvHeadAngle();
        mVHeadAngle = (vHeadAngle == 0
                ? ConfigManager.getHeadConfig().getDefaultVerticalAngle()
                : vHeadAngle);

        int bodySpeed = param.getBodySpeed();
        mBodySpeed = (bodySpeed == 0 ? RobotSetting.getDefaultBodySpeed() : bodySpeed);
        return true;
    }
}
