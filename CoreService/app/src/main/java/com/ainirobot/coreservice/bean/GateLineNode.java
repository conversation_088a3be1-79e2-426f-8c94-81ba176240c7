package com.ainirobot.coreservice.bean;

import com.ainirobot.coreservice.client.actionbean.GateLineUnit;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GateLineNode {
    private Map<String, LineNode> gateLineNodeMap;

    public LineNode getLineNode(String mapName) {
        checkMap();
        return gateLineNodeMap.get(mapName);
    }

    public void setLineNode(String mapName, LineNode lineNode) {
        checkMap();
        gateLineNodeMap.put(mapName, lineNode);
    }

    private void checkMap() {
        if (gateLineNodeMap == null) {
            gateLineNodeMap = new HashMap<>();
        }
    }

    public static class LineNode {
        private Node enterNode;
        private Node outerNode;

        private List<GateLineUnit> gateLines;

        public Node getEnterNode() {
            return enterNode;
        }

        public void setEnterNode(Node enterNode) {
            this.enterNode = enterNode;
        }

        public Node getOuterNode() {
            return outerNode;
        }

        public void setOuterNode(Node outerNode) {
            this.outerNode = outerNode;
        }

        public List<GateLineUnit> getGateLines() {
            return gateLines;
        }

        public void setGateLines(List<GateLineUnit> gateLines) {
            this.gateLines = gateLines;
        }
    }

    public static class Node {
        private double x;
        private double y;

        public Node(double x, double y) {
            this.x = x;
            this.y = y;
        }

        public double getX() {
            return x;
        }

        public void setX(double x) {
            this.x = x;
        }

        public double getY() {
            return y;
        }

        public void setY(double y) {
            this.y = y;
        }
    }

}