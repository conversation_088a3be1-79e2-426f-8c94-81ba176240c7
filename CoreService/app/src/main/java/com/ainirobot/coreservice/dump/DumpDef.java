package com.ainirobot.coreservice.dump;

/**
 * Created by Orion on 2020/5/25.
 */
public class DumpDef {

    //----------------- Result ----------------//
    public static final String RESULT_OK = "ok";
    public static final String RESULT_FAILED = "failed";

    //----------------- Message ----------------//
    public static final int MSG_NEW_REQUEST = 101;
    public static final int MSG_CMD_RESPONSE = 102;
    public static final int MSG_HW_RESPORT = 103;

    public static final String REQ_ID = "req_id";
    public static final String REQ_INTENT = "req_intent";
    public static final String REQ_TEXT = "req_text";
    public static final String REQ_PARAMS = "req_params";
    public static final String FUNCTION = "function";
    public static final String TYPE = "type";
    public static final String MESSAGE = "message";

    //----------------- Data permission ----------------//
    public static final int DATA_RIGHTS_PRIVACY  = 0; //不可存储，不可上传
    public static final int DATA_RIGHTS_STORE = 1;    //可存储，不可上传
    public static final int DATA_RIGHTS_UPLOAD = 2;   //可存储，可上传

    //----------------- Data type ----------------//
    public static final String TYPE_VISION_RECORD = "type_vision_record";
    public static final String TYPE_SERVICE_STATUS = "type_service_status";
    public static final String TYPE_HW_STATUS = "type_hw_status";
    public static final String TYPE_KEY_FRAME = "type_key_frame";

}
