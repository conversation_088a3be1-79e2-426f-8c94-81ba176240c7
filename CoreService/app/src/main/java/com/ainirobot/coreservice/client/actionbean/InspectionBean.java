/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.client.actionbean;

import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Orion on 2018/5/7.
 * default：1:ok 0:bad/false/disabled.
 * The individual fields are assigned as previously defined(for example: socket link status).
 */
public class InspectionBean {

    private int result = 1;
    private Data821 data821;
    private Can can;
    private Head head;
    private Navigation navigation;

    public InspectionBean() {
        data821 = new Data821();
        can = new Can();
        head = new Head();
        navigation = new Navigation();
    }

    public Data821 getData821() {
        return data821;
    }

    public Can getCan() {
        return can;
    }

    public Head getHead() {
        return head;
    }

    public Navigation getNavigation() {
        return navigation;
    }

    public class Data821 {
        private int net = 1;
        private int camera = 1;
        private int bindStatus = 1;
        private int lightSensor = 1;

        public int getLightSensor() {
            return lightSensor;
        }

        public void setLightSensor(int lightSensor) {
            this.lightSensor = lightSensor;
        }

        public int getNet() {
            return net;
        }

        public void setNet(int net) {
            this.net = net;
        }

        public int getCamera() {
            return camera;
        }

        public void setCamera(int camera) {
            this.camera = camera;
        }

        public int getBindStatus() {
            return bindStatus;
        }

        public void setBindStatus(int bindStatus) {
            this.bindStatus = bindStatus;
        }

        @Override
        public String toString() {
            return "Data821{" +
                    "net=" + net +
                    ", camera=" + camera +
                    ", bindStatus=" + bindStatus +
                    ", lightSensor=" + lightSensor +
                    '}';
        }
    }

    public class Can {
        private int link = 1;
        private int motorHorizontal = 1; //0:init 1:normal 2:update 3:error
        private int motorVertical = 1;
        private int battery;
        private int charge; //is in charge
        private int powerPanel = 1; //0:init 1:normal 2:update 3:error
        private int chargingPanel = 1; //Whether it can be recharged automatically. 0-shutdown 1-init 2-ready 3-onging 4-locked 5-done (非0/5都是OK)
        private int scramStatus; //is pressed or not. 0-released 1-pressed
        private int wheelLeft = 1;
        private int wheelRight = 1;
        private int motorZeroCheck = 1;//0 abnormal, 1 normal -1 uncheck

        public int getMotorZeroCheck() {
            return motorZeroCheck;
        }

        public void setMotorZeroCheck(int motorZeroCheck) {
            this.motorZeroCheck = motorZeroCheck;
        }

        public int getLink() {
            return link;
        }

        public void setLink(int link) {
            this.link = link;
        }

        public int getMotorHorizontal() {
            return motorHorizontal;
        }

        public void setMotorHorizontal(int motorHorizontal) {
            this.motorHorizontal = motorHorizontal;
        }

        public int getMotorVertical() {
            return motorVertical;
        }

        public void setMotorVertical(int motorVertical) {
            this.motorVertical = motorVertical;
        }

        public int getBattery() {
            return battery;
        }

        public void setBattery(int battery) {
            this.battery = battery;
        }

        public int getCharge() {
            return charge;
        }

        public void setCharge(int charge) {
            this.charge = charge;
        }

        public int getPowerPanel() {
            return powerPanel;
        }

        public void setPowerPanel(int powerPanel) {
            this.powerPanel = powerPanel;
        }

        public int getChargingPanel() {
            return chargingPanel;
        }

        public void setChargingPanel(int charginPanel) {
            this.chargingPanel = charginPanel;
        }

        public int getScramStatus() {
            return scramStatus;
        }

        public void setScramStatus(int scarmStatus) {
            this.scramStatus = scarmStatus;
        }

        public int getWheelLeft() {
            return wheelLeft;
        }

        public void setWheelLeft(int wheelLeft) {
            this.wheelLeft = wheelLeft;
        }

        public int getWheelRight() {
            return wheelRight;
        }

        public void setWheelRight(int wheelRight) {
            this.wheelRight = wheelRight;
        }

        @Override
        public String toString() {
            return "Can{" +
                    "link=" + link +
                    ", motorHorizontal=" + motorHorizontal +
                    ", motorVertical=" + motorVertical +
                    ", battery=" + battery +
                    ", charge=" + charge +
                    ", powerPanel=" + powerPanel +
                    ", chargingPanel=" + chargingPanel +
                    ", scramStatus=" + scramStatus +
                    ", wheelLeft=" + wheelLeft +
                    ", wheelRight=" + wheelRight +
                    '}';
        }
    }

    public class Head {
        private int ping = 1;
        private int socket = 2; //2-idle 1-disabled
        private int visionFont = 1;
        private int visionBack = 1;
        private int wideFont = 1;
        private int wideBack = 1;

        public int getPing() {
            return ping;
        }

        public void setPing(int ping) {
            this.ping = ping;
        }

        public int getSocket() {
            return socket;
        }

        public void setSocket(int socket) {
            this.socket = socket;
        }

        public int getVisionFont() {
            return visionFont;
        }

        public void setVisionFont(int visionFont) {
            this.visionFont = visionFont;
        }

        public int getVisionBack() {
            return visionBack;
        }

        public void setVisionBack(int visionBack) {
            this.visionBack = visionBack;
        }

        public int getWideFont() {
            return wideFont;
        }

        public void setWideFont(int wideFont) {
            this.wideFont = wideFont;
        }

        public int getWideBack() {
            return wideBack;
        }

        public void setWideBack(int wideBack) {
            this.wideBack = wideBack;
        }

        @Override
        public String toString() {
            return "Head{" +
                    "ping=" + ping +
                    ", socket=" + socket +
                    ", visionFont=" + visionFont +
                    ", visionBack=" + visionBack +
                    ", wideFont=" + wideFont +
                    ", wideBack=" + wideBack +
                    '}';
        }
    }

    public class Navigation {
        private int ping = 1;
        private int socket = 2; //2-idle 1-disabled
        private int resetCamera = 1; //重定位摄像头
        private int rgbd = 1; //避障rgbg
        private int laser = 1; //激光雷达
        private int infrared = 1; //红外
        private int speedometer = 1; //里程计
        private int laserAvailable = 1;
        private int calibrationReady = 1;
        private int hardDiskSpaceEnough = 1;
        private int canControlReady = 1;//检查Can是否连接正常
        private int serviceOk = 1;
        private int selfCheckResponse = 1;//检测接口正常返回

        public int getServiceOk() {
            return serviceOk;
        }

        public void setServiceOk(int serviceOk) {
            this.serviceOk = serviceOk;
        }

        public int getSelfCheckResponse() {
            return selfCheckResponse;
        }

        public void setSelfCheckResponse(int selfCheckResponse) {
            this.selfCheckResponse = selfCheckResponse;
        }

        public int getLaserAvailable() {
            return laserAvailable;
        }

        public void setLaserAvailable(int laserAvailable) {
            this.laserAvailable = laserAvailable;
        }

        public int getCalibrationReady() {
            return calibrationReady;
        }

        public void setCalibrationReady(int calibrationReady) {
            this.calibrationReady = calibrationReady;
        }

        public int getHardDiskSpaceEnough() {
            return hardDiskSpaceEnough;
        }

        public void setHardDiskSpaceEnough(int hardDiskSpaceEnough) {
            this.hardDiskSpaceEnough = hardDiskSpaceEnough;
        }

        public int getPing() {
            return ping;
        }

        public void setPing(int ping) {
            this.ping = ping;
        }

        public int getSocket() {
            return socket;
        }

        public void setSocket(int socket) {
            this.socket = socket;
        }

        public int getResetCamera() {
            return resetCamera;
        }

        public void setResetCamera(int resetCamera) {
            this.resetCamera = resetCamera;
        }

        public int getRgbd() {
            return rgbd;
        }

        public void setRgbd(int rgbd) {
            this.rgbd = rgbd;
        }

        public int getLaser() {
            return laser;
        }

        public void setLaser(int laser) {
            this.laser = laser;
        }

        public int getInfrared() {
            return infrared;
        }

        public void setInfrared(int infrared) {
            this.infrared = infrared;
        }

        public int getSpeedometer() {
            return speedometer;
        }

        public void setSpeedometer(int speedometer) {
            this.speedometer = speedometer;
        }

        public int getCanControlReady() {
            return canControlReady;
        }

        public void setCanControlReady(int canControlReady) {
            this.canControlReady = canControlReady;
        }

        @Override
        public String toString() {
            return "Navigation{" +
                    "ping=" + ping +
                    ", socket=" + socket +
                    ", resetCamera=" + resetCamera +
                    ", rgbd=" + rgbd +
                    ", laser=" + laser +
                    ", infrared=" + infrared +
                    ", speedometer=" + speedometer +
                    ", laserAvailable = " + laserAvailable +
                    ", calibrationReady = " + calibrationReady +
                    ", hardDiskSpaceEnough = " + hardDiskSpaceEnough +
                    ", canControlReady = " + canControlReady +
                    ", serviceOK = " + serviceOk +
                    ", selfCheckResponse" + selfCheckResponse +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "InspectionBean{" +
                "data821=" + data821 +
                ", can=" + can +
                ", head=" + head +
                ", navigation=" + navigation +
                '}';
    }

    public String toJsonStr() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }

    public void setResultChecked() {
        result = checkResult();
    }


    public int getResult() {
        return result;
    }

    /**
     * 自检结果是否pass
     *
     * @return 1:pass, -1：821 error，-2：can error，-3：head error，-4：navigation error
     */
    private int checkResult() {
        //
        Log.d("InspectionBean", toString());
        //821, free:net
        if (/*isStateError(data821.getCamera())*/snapDragon821checkHasError()) {
            return Definition.INSPECTION_ERROR_821;
        }

        //can, free:battery/charge/powerPanel/chargingPanel/scramStatus
        if (/*isStateError(can.getLink()) || isCanStateError(can.getMotorHorizontal())
                || isCanStateError(can.getMotorVertical())
                || isWheelError(can.getWheelLeft())
                || isWheelError(can.getWheelRight())*/canCheckHasError()) {
            return Definition.INSPECTION_ERROR_CAN;
        }

        //head, free:none
        if (/*isStateError(head.getPing()) || isSocketStateError(head.getSocket())
                || isStateError(head.getWideBack()) || isStateError(head.getWideFont())
                || isStateError(head.getVisionBack()) || isStateError(head.getVisionFont())*/headCheckHasError()) {
            return Definition.INSPECTION_ERROR_HEAD;
        }

        //navigation, free:none
        if (/*isStateError(navigation.getPing()) || isSocketStateError(navigation.getSocket())
                || isStateError(navigation.getResetCamera()) || isStateError(navigation.getRgbd())
                || isStateError(navigation.getLaser()) || isStateError(navigation.getInfrared())
                || isStateError(navigation.getSpeedometer())*/navCheckHasError()) {
            return Definition.INSPECTION_ERROR_NAVIGATION;
        }

        return Definition.INSPECTION_PASS;
    }

    public boolean needShowErrorUi() {
        if (snapDragon821checkShowErrorUi()
                || canCheckShowErrorUi()
                || headCheckShowErrorUi()
                || navCheckShowErrorUi()) {
            return true;
        }
        return false;
    }

    public boolean needReportError() {
        if (snapDragon821checkHasError()
                || canCheckHasError()
                || headCheckHasError()
                || navCheckHasError()) {
            return true;
        }
        return false;
    }

    private boolean snapDragon821checkShowErrorUi() {
        //目前没有自检项需要UI显示error
        return false;
    }

    private boolean snapDragon821checkHasError() {
        if (isStateError(data821.getCamera())//821摄像头是否OK
                || isStateError(data821.getLightSensor())//821光线传感器是否OK
        ) {
            Log.d("InspectionBean", "snapDragon821checkHasError true");
            return true;
        }
        return false;
    }

    private boolean canCheckShowErrorUi() {
        if (isStateError(can.getLink()) //CanService 初始化
                || isCanStateError(can.getMotorHorizontal())//水平电机
                || isCanStateError(can.getMotorVertical())//俯仰电机
                || isWheelError(can.getWheelLeft())//左侧轮毂
                || isWheelError(can.getWheelRight())//右侧轮毂
                || isStateError(can.getMotorZeroCheck()) //水平电机零位检测
                || isPowerPanelError(can.getPowerPanel()) //PSB连接
                || isBMSError(can.getBattery())//电池电量检测有无，代表BMS是否正常
                || isChargingPanelerror(can.getChargingPanel())//自动回充正常与否
        ) {
            Log.d("InspectionBean", "canCheckShowErrorUi true");
            return true;
        }
        return false;
    }

    private boolean canCheckHasError() {
        if (canCheckShowErrorUi()) {
            //还有急停按键是否按下和是否正在充电，不做为自检结果判断
            Log.d("InspectionBean", "canCheckHasError true");
            return true;
        }
        return false;
    }

    private boolean headCheckShowErrorUi() {
        //以下几项为TX1关注的需要弹error UI的条目
        if (isStateError(head.getPing()) //IP地址能否ping通
                || isSocketStateError(head.getSocket())//socket连接OK
                || isStateError(head.getWideFont())//前置广角摄像头
        ) {
            Log.d("InspectionBean", "headCheckShowErrorUi true");
            return true;
        }
        return false;
    }

    private boolean headCheckHasError() {
        if (headCheckShowErrorUi()
                || isStateError(head.getWideBack()) //后置广角
                || isStateError(head.getVisionBack()) //后置深度
                || isStateError(head.getVisionFont()) //前置深度
        ) {
            Log.d("InspectionBean", "headCheckHasError true");
            return true;
        }
        return false;
    }

    private boolean navCheckShowErrorUi() {
        //以下几项为TK1关注的需要弹error UI的项目
        if (isStateError(navigation.getPing())//IP地址能否ping通
                || isSocketStateError(navigation.getSocket())//socket连接是否OK
                || isStateError(navigation.getServiceOk())//TK1服务是否OK
                || isStateError(navigation.getSelfCheckResponse())//自检接口返回是否OK
                || isStateError(navigation.getLaser())//激光雷达是否OK
                || isStateError(navigation.getLaserAvailable())//雷达数据是否OK
                || isStateError(navigation.getInfrared())//红外是否OK
                || isStateError(navigation.getSpeedometer())
                || isStateError(navigation.getCanControlReady()) //里程计是否OK
        ) {
            Log.d("InspectionBean", "navCheckShowErrorUi true");
            return true;
        }
        return false;
    }

    private boolean navCheckHasError() {
        //需要showUI的error和其他项异常，都要上报
        if (navCheckShowErrorUi()
                || isStateError(navigation.getResetCamera())//重定位摄像头
                || isStateError(navigation.getRgbd()) //RGBD异常
                || isStateError(navigation.getCalibrationReady())//视觉校准文件
                || isStateError(navigation.getHardDiskSpaceEnough())//硬盘空间
                || isStateError(navigation.getCanControlReady())//Can是否连接正常
        ) {
            Log.d("InspectionBean", "navCheckHasError true");
            return true;
        }
        return false;
    }

    /**
     * 云台/轮毂异常判断
     * 0/1/2正常，other异常，-1是初始值-异常，-2是跳过检测
     *
     * @param state
     * @return
     */
    private boolean isCanStateError(int state) {
        List<Integer> listPass = new ArrayList<Integer>() {{
            add(0);
            add(1);
            add(2);
            add(-2);
        }};
        if (listPass.contains(state)) {
            return false;
        }
        return true;
    }

    /**
     * socket状态异常判断
     * 2：成功，-2：跳过检测
     *
     * @param state
     * @return
     */
    private boolean isSocketStateError(int state) {
        List<Integer> listPass = new ArrayList<Integer>() {{
            add(2);
            add(-2);
        }};
        if (listPass.contains(state)) {
            return false;
        }
        return true;
    }

    /**
     * 其他状态异常判断
     * 0：失败，1：成功，-1：未检测，-2：跳过检测
     *
     * @param state
     * @return
     */
    private boolean isStateError(int state) {
        List<Integer> listPass = new ArrayList<Integer>() {{
            add(1);
            add(-2);
        }};
        if (listPass.contains(state)) {
            return false;
        }
        return true;
    }

    private boolean isWheelError(int state) {
        if (state == 3 || state == -1) {
            return true;
        }
        return false;
    }

    private boolean isPowerPanelError(int state) {
        if (state == 3 || state == -1) {
            return true;
        }
        return false;
    }

    private boolean isBMSError(int battery) {
        if (battery == -1) {
            return true;
        }
        return false;
    }

    /**
     * 0xFF异常，other正常，-1是初始值-异常
     *
     * @param state
     * @return
     */
    private boolean isChargingPanelerror(int state) {
        if (state == 255 || state == -1) {
            return true;
        }
        return false;
    }
}
