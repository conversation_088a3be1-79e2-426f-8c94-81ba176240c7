package com.ainirobot.coreservice.action.download_map.state;

import android.text.TextUtils;

import com.ainirobot.coreservice.action.download_map.DownloadMapStateEnum;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.DownloadMapBean;

import org.json.JSONObject;

/**
 * 无论上一步成功、失败，都会进行当前同步
 */
public class UpdatePlaceListToNetState extends BaseDownloadMapPkgState {

    private DownloadMapBean downloadMapBean;
    private Object data;

    @Override
    protected void onStart(String paramsStr, DownloadMapBean downloadMapBean) {
        this.downloadMapBean = downloadMapBean;
        sendCommand(0, Definition.CMD_REMOTE_UPLOAD_PLACE_LIST, downloadMapBean.getMapName());
    }

    @Override
    public boolean onCmdResponse(int cmdId, String command, String result, String extraData) {
        if (TextUtils.equals(command, Definition.CMD_REMOTE_UPLOAD_PLACE_LIST)) {
            boolean succeed = TextUtils.equals(Definition.SUCCEED, result);
            updateMapSyncState(downloadMapBean.getMapName(), !succeed);

            if (getData() == null) {
                onStateUpdate(Definition.STATUS_DOWNLOAD_MAP_CLOUD_PLACE_EMPTY, "cloud map is empty");
            }
            return true;
        } else if (TextUtils.equals(command, Definition.CMD_NAVI_SET_MAP_SYNC_STATE)) {
            if (TextUtils.equals(Definition.SUCCEED, result)) {
                onStateRunSuccess();
            } else {
                onStateRunFailed(0, "update map sync state failed");
            }
            return true;
        }
        return false;
    }

    @Override
    protected DownloadMapStateEnum getNextState() {
        return DownloadMapStateEnum.DOWNLOAD_MAP_EXTRA;
    }

    @Override
    public Object getData() {
        return data;
    }

    @Override
    public void setData(Object data) {
        this.data = data;
    }

    private void updateMapSyncState(String mapName, boolean needReUpload) {
        try {
            JSONObject param = new JSONObject();
            param.put(Definition.JSON_MAP_NAME, mapName);
            param.put(Definition.JSON_MAP_IS_MAP_STATE, false);
            param.put(Definition.JSON_MAP_NEED_RE_UPLOAD, needReUpload);
            sendCommand(0, Definition.CMD_NAVI_SET_MAP_SYNC_STATE, param.toString());
        } catch (Exception e) {
            e.printStackTrace();
            onStateRunFailed(0, "send map sync state failed");
        }
    }
}
