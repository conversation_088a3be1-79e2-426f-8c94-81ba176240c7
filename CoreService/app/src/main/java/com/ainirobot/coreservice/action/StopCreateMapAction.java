/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.action;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.StopCommandList.IStopCommandChecker;
import com.ainirobot.coreservice.action.StopCommandList.StopCommand;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.StopCreateMapBean;
import com.ainirobot.coreservice.core.LocalApi;
import com.google.gson.Gson;

import java.util.ArrayList;

public class StopCreateMapAction extends AbstractAction<StopCreateMapBean> {
    private static final String TAG = StopCreateMapAction.class.getSimpleName();

    private StopCreateMapState mStatus = StopCreateMapState.IDLE;

    private enum StopCreateMapState {
        IDLE, RUNNING;
    }

    protected StopCreateMapAction(ActionManager manager, String[] resList) {
        super(manager, Definition.ACTION_NAVI_STOP_CREATING_MAP, resList);
    }

    @Override
    protected boolean startAction(StopCreateMapBean parameter, LocalApi api) {
        if (mStatus == StopCreateMapState.IDLE) {
            startProcess(parameter);
            return true;
        }
        return false;
    }

    private void startProcess(StopCreateMapBean parameter) {
        Log.d(TAG, "startProcess: parameter" + parameter.toString());
        mStatus = StopCreateMapState.RUNNING;
//        mApi.stopCreatingMap(parameter.getReqId(), parameter.getMapName());
        mApi.stopCreatingMap(parameter.getReqId(), new Gson().toJson(parameter));
    }

    @Override
    protected boolean stopAction(boolean isResetHW) {
        mStatus = StopCreateMapState.IDLE;
        return true;
    }

    @Override
    public boolean cmdResponse(int cmdId, String command, String result, String extraData) {
        Log.d(TAG, "stopCreatingMap cmdResponse result:" + result);
        switch (command) {
            case Definition.CMD_NAVI_STOP_CREATING_MAP:
                processStopCreatingMapResult(result);
                return true;
        }
        return false;
    }

    private void processStopCreatingMapResult(String result) {
        if (TextUtils.equals(result, "failed")) {
            onResult(Definition.RESULT_FAILURE, result);
        } else {
            onResult(Definition.RESULT_OK, result);
        }
        mStatus = StopCreateMapState.IDLE;
    }

    @Override
    public boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        switch (command) {
            case Definition.CMD_NAVI_STOP_CREATING_MAP:
                processStopCreatingMapStatus(status);
                return true;
        }
        return false;
    }

    private void processStopCreatingMapStatus(String status) {
        onStatusUpdate(Definition.STATUS_STOP_CREATE_MAP_EXPECTED_TIME, status);
    }

    @Override
    protected boolean verifyParam(StopCreateMapBean param) {
        return true;
    }

    @Override
    protected ArrayList<StopCommand> getStopCommands() {
        return null;
    }

    @Override
    protected IStopCommandChecker getStopCommandChecker() {
        return null;
    }
}
