package com.ainirobot.coreservice.data.core;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;

import com.ainirobot.coreservice.data.provider.BaseProvider;
import com.ainirobot.coreservice.data.provider.SQLiteTable;

import java.util.ArrayList;
import java.util.List;

public class DataProvider extends BaseProvider {
    private static final String TAG = "DataProvider";

    public static final String AUTHORITY = "com.ainirobot.coreservice.dataprovider";

    private static final List<SQLiteTable> TABLES = new ArrayList<SQLiteTable>() {{
        add(ActionDataHelper.TABLE);
        add(PersonDataHelper.TABLE);
        add(RequestDataHelper.TABLE);
    }};

    @Override
    public List<SQLiteTable> getTables() {
        return TABLES;
    }

    @Override
    public SQLiteOpenHelper getDBHelper() {
        return new DBHelper(getContext());
    }

    static class DBHelper extends SQLiteOpenHelper {
        private static final String DB_NAME = "core.db";

        private static final int VERSION = 201;

        private Context mContext;

        private DBHelper(Context context) {
            super(context, DB_NAME, null, VERSION);
            mContext = context;
        }

        @Override
        public void onCreate(SQLiteDatabase db) {
            for (SQLiteTable table : TABLES) {
                table.create(db);
                table.importData(mContext, db);
            }
        }

        @Override
        public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            Log.d(TAG, "onUpgrade oldVersion: " + oldVersion + ", newVersion: " + newVersion);
            for (SQLiteTable table : TABLES) {
                table.delete(db);
                table.create(db);
                table.importData(mContext, db);
            }
        }

        @Override
        public void onDowngrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            for (SQLiteTable table : TABLES) {
                table.delete(db);
                table.create(db);
                table.importData(mContext, db);
            }
        }
    }
}
