package com.ainirobot.coreservice.bi.data;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.ContentObserver;
import android.database.Cursor;
import android.net.ConnectivityManager;
import android.net.ConnectivityManager.NetworkCallback;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.net.NetworkRequest;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.ainirobot.base.analytics.utils.PublicDataUtils;
import com.ainirobot.coreservice.bi.annotation.OsType;
import com.ainirobot.coreservice.bi.server.Bi;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.SettingsUtil;

import org.json.JSONException;
import org.json.JSONObject;

import static com.ainirobot.coreservice.client.Definition.MODULE_PACKAGE_NAME;

public class BiPublicDataMonitor {

    private static final String TAG = "BiPublicDataMonitor";

    private static final String NET_WIFI = "wifi";
    private static final String NET_MOBILE = "mobile";
    private static final String URI = "content://com.ainirobot.coreservice.robotsettingprovider/setting";

    private static BiPublicDataMonitor monitor;

    private Context mContext;
    private Handler mHandler;

    private BiPublicDataMonitor(Context context) {
        this.mContext = context.getApplicationContext();
        this.mHandler = new Handler(Looper.getMainLooper());
    }

    public static void start(Context context) {
        synchronized (TAG) {
            if (monitor != null) {
                return;
            }
            monitor = new BiPublicDataMonitor(context);
        }
        monitor.start();
    }

    private void start() {
        setNetworkMonitor();
        setPlatformObserver();
        setOpkInfoObserver();
        setOsTypeObserver();
        setOsLanguageObserver();
        setAppLanguageObserver();
    }

    /**
     * observer robot os type change
     */
    private void setOsTypeObserver() {
        updateOsType();
        registerProviderObserver(Definition.BOOT_APP_PACKAGE_NAME, new ContentObserver(mHandler) {
            @Override
            public void onChange(boolean selfChange, Uri uri) {
                super.onChange(selfChange, uri);
                updateOsType();
            }
        });
    }

    private void updateOsType() {
        String defaultApp = Bi.getRobotString(mContext, Definition.BOOT_APP_PACKAGE_NAME);
        Log.i(TAG, "updateOsType: observe default app change:" + defaultApp);
        if (!MODULE_PACKAGE_NAME.equals(defaultApp)) {
            Bi.setOsType(OsType.OS_TYPE_DEVELOPER);
        } else {
            Bi.setOsType(Bi.calculateOsType(mContext));
        }
    }

    public void setOsLanguageObserver() {
        updateOsLang();
        registerProviderObserver(Definition.ROBOT_LANGUAGE, new ContentObserver(mHandler) {
            @Override
            public void onChange(boolean selfChange, Uri uri) {
                super.onChange(selfChange, uri);
                updateOsLang();
            }
        });
    }

    public void setAppLanguageObserver() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(Definition.ACTION_APP_LANGUAGE_CHANGE);
        mContext.registerReceiver(new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String lang = intent.getStringExtra(Definition.LANGUAGE);
                PublicDataUtils.setLan(lang);
                Log.d(TAG, "Update app language : " + lang);
            }
        }, filter);
    }

    private void updateOsLang() {
        String lang = Bi.getRobotString(mContext, Definition.ROBOT_LANGUAGE);
        PublicDataUtils.setOSLan(lang);
        Log.d(TAG, "Update os language : " + lang);
    }

    /**
     * 监听网络变化
     */
    private void setNetworkMonitor() {
        ConnectivityManager connectivityManager =
                (ConnectivityManager) mContext.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager == null) {
            Log.d(TAG, "Get connectivityManager failed");
            return;
        }

        NetworkInfo info = connectivityManager.getActiveNetworkInfo();
        if (info != null) {
            updateNetworkType(info.getType());
        }
        connectivityManager.registerNetworkCallback(new NetworkRequest.Builder().build(),
                new NetworkCallback() {
                    @Override
                    public void onCapabilitiesChanged(Network network, NetworkCapabilities networkCapabilities) {
                        boolean isWifi = networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI);
                        if (isWifi) {
                            updateNetworkType(ConnectivityManager.TYPE_WIFI);
                            return;
                        }

                        boolean isCellular = networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR);
                        if (isCellular) {
                            updateNetworkType(ConnectivityManager.TYPE_MOBILE);
                        }
                    }
                });
    }

    private void updateNetworkType(int type) {
        switch (type) {
            case ConnectivityManager.TYPE_WIFI:
                PublicDataUtils.setNetworkType(NET_WIFI);
                break;

            case ConnectivityManager.TYPE_MOBILE:
                PublicDataUtils.setNetworkType(NET_MOBILE);
                break;

            default:
                break;
        }
    }

    private void setPlatformObserver() {
        updatePlatform();
        registerSettingsObserver(SettingsUtil.ROBOT_SETTING_PLATFORM_NAME,
                new ContentObserver(mHandler) {
                    @Override
                    public void onChange(boolean selfChange) {
                        updatePlatform();
                    }
                });
    }

    private void updatePlatform() {
        String platformName = RobotSettings.getGlobalSettings(mContext, SettingsUtil.ROBOT_SETTING_PLATFORM_NAME, "");
        Log.d(TAG, "Update platform name : " + platformName);
        PublicDataUtils.setPlatform(platformName);
    }

    private void setOpkInfoObserver() {
        updateOpkInfo();
        registerSettingsObserver(SettingsUtil.ROBOT_SETTING_OPK_INFO,
                new ContentObserver(mHandler) {
                    @Override
                    public void onChange(boolean selfChange) {
                        updateOpkInfo();
                    }
                });
    }

    private void updateOpkInfo() {
        String opkInfo = RobotSettings.getGlobalSettings(mContext, SettingsUtil.ROBOT_SETTING_OPK_INFO, "");
        Log.d(TAG, "Update opk info : " + opkInfo);
        try {
            JSONObject json = new JSONObject(opkInfo);
            String rnVersion = json.optString("versionName", "");
            PublicDataUtils.setRnVersion(rnVersion);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void registerProviderObserver(String key, ContentObserver observer) {
        Uri uri = Uri.withAppendedPath(Uri.parse(URI), key);
        mContext.getContentResolver().registerContentObserver(uri, false, observer);
    }

    private void registerSettingsObserver(String key, ContentObserver observer) {
        Uri uri = SettingsUtil.getUriFor(key);
        mContext.getContentResolver().registerContentObserver(uri, false, observer);
    }



}
