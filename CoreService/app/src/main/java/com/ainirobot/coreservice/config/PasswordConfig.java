package com.ainirobot.coreservice.config;

import com.google.gson.annotations.SerializedName;

public class PasswordConfig {

    public static final String ENCRYPT_MD5 = "md5";
    public static final String ENCRYPT_NONE = "none";

    @SerializedName("原始项")
    private SourceConfig sourceConfig;

    @SerializedName("加密方式")
    private String encryptStrategy;

    public String getEncryptStrategy() {
        return encryptStrategy;
    }

    public SourceConfig getSourceConfig() {
        return sourceConfig == null ? new SourceConfig() : sourceConfig;
    }

    @Override
    public String toString() {
        return "sourceConfig: " + getSourceConfig() + ", encryptStrategy: " + encryptStrategy;
    }

    public class SourceConfig {
        @SerializedName("sn")
        private boolean isNeedSn = true;

        @SerializedName("时间")
        private boolean isNeedTime;

        public boolean isNeedSn() {
            return isNeedSn;
        }

        public boolean isNeedTime() {
            return isNeedTime;
        }

        @Override
        public String toString() {
            return "isNeedSn: " + isNeedSn + ", isNeedTime: " + isNeedTime;
        }
    }
}
