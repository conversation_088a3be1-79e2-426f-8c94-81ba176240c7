/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.coreservice.data.account;

import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.account.MemberBean;
import com.ainirobot.coreservice.client.account.TokenBean;
import com.ainirobot.coreservice.client.account.UserBean;
import com.ainirobot.coreservice.client.person.FaceBean;
import com.ainirobot.coreservice.service.CoreService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ainirobot.coreservice.client.account.TokenBean.LOCAL_SYSTEM_TOKEN;

public class MemberDataHelper {

    private static final String TAG = MemberDataHelper.class.getSimpleName();
    private static final String LOCAL_REGISTER_MODULE = "localRegister";

    private volatile static MemberDataHelper mInstance;

    private UserDataHelper mUserDataHelper;
    private FaceDataHelper mFaceDataHelper;
    private TokenDataHelper mTokenDataHelper;
    private MetaDataHelper mMetaDataHelper;

    public static MemberDataHelper getInstance(CoreService core) {
        if (mInstance == null) {
            synchronized (MemberDataHelper.class) {
                if (mInstance == null) {
                    mInstance = new MemberDataHelper(core);
                }
            }
        }
        return mInstance;
    }

    private MemberDataHelper(CoreService core) {
        String remoteEnv = core.getRobotSettingManager()
                .getRobotSetting(Definition.ROBOT_SETTING_SYSTEM_ENV);
        Log.d(TAG, "MemberDataHelper init remoteEnv: " + remoteEnv);
        mUserDataHelper = new UserDataHelper(core.getApplicationContext(), remoteEnv);
        mFaceDataHelper = new FaceDataHelper(core.getApplicationContext(), remoteEnv);
        mTokenDataHelper = new TokenDataHelper(core.getApplicationContext());
        mMetaDataHelper = new MetaDataHelper(core.getApplicationContext());
        mTokenDataHelper.deleteAllToken();
    }

    public synchronized List<MemberBean> getMemberList() {
        Log.d(TAG, "getMemberList");
        List<UserBean> userBeanList = mUserDataHelper.getUserList();
        if (userBeanList == null) {
            return null;
        }
        List<MemberBean> memberBeansList = new ArrayList<>();
        for (UserBean userBean : userBeanList) {
            MemberBean memberBean = new MemberBean();
            String tokenId = mUserDataHelper.getTokenIdByUserId(userBean.getUserId());
            if (!TextUtils.isEmpty(tokenId)) {
                userBean.setUserToken(mTokenDataHelper.getToken(tokenId));
            }
            memberBean.setUserBean(userBean);
            memberBeansList.add(memberBean);
        }
        return memberBeansList;
    }

    public synchronized List<MemberBean> getWholeMemberList() {
        Log.d(TAG, "getWholeMemberList");
        List<UserBean> userBeanList = mUserDataHelper.getUserList();
        List<FaceBean> faceBeanList = mFaceDataHelper.getFaceList(false);
        List<MemberBean> memberBeansList = new ArrayList<>();
        for (FaceBean faceBean : faceBeanList) {
            MemberBean memberBean = new MemberBean();
            memberBean.setFaceBean(faceBean);
            if (!TextUtils.isEmpty(faceBean.getUserId())) {
                for (UserBean userBean : userBeanList) {
                    if (faceBean.getUserId().equals(userBean.getUserId())) {
                        String tokenId = mUserDataHelper.getTokenIdByUserId(userBean.getUserId());
                        if (!TextUtils.isEmpty(tokenId)) {
                            userBean.setUserToken(mTokenDataHelper.getToken(tokenId));
                        }
                        memberBean.setUserBean(userBean);
                        userBeanList.remove(userBean);
                        break;
                    }
                }
            }
            memberBeansList.add(memberBean);
        }

        for (UserBean userBean : userBeanList) {
            MemberBean memberBean = new MemberBean();
            String tokenId = mUserDataHelper.getTokenIdByUserId(userBean.getUserId());
            if (!TextUtils.isEmpty(tokenId)) {
                userBean.setUserToken(mTokenDataHelper.getToken(tokenId));
            }
            memberBean.setUserBean(userBean);
            memberBeansList.add(memberBean);
        }
        Log.d(TAG, "getWholeMemberList size: " + memberBeansList.size());
        return memberBeansList;
    }

    public synchronized String getRegisterAlgorithmVersion() {
        Log.d(TAG, "getRegisterAlgorithmVersion");
        return mMetaDataHelper.getMetaValue(LOCAL_REGISTER_MODULE);
    }

    public synchronized void updateRegisterAlgorithmVersion(String version) {
        Log.d(TAG, "updateRegisterAlgorithmVersion version: " + version);
        mMetaDataHelper.updateMetaValue(LOCAL_REGISTER_MODULE, version);
    }

    public synchronized List<FaceBean> getFaceList() {
        Log.d(TAG, "getFaceList");
        return mFaceDataHelper.getFaceList(false);
    }

    public synchronized List<FaceBean> getOnlyLocalFaceList() {
        Log.d(TAG, "getOnlyLocalFaceList");
        return mFaceDataHelper.getOnlyLocalFaceList();
    }

    //    public synchronized boolean checkUserExist(String userId) {
    //        Log.d(TAG, "checkUserExist userId: " + userId);
    //        return mUserDataHelper.checkUserExist(userId);
    //    }

    public synchronized boolean checkNameExist(String name) {
        Log.d(TAG, "checkNameExist name: " + name);
        return mFaceDataHelper.checkNameExist(name);
    }

    public synchronized void insertNewFace(FaceBean faceBean) {
        Uri uri = mFaceDataHelper.insertNewFace(faceBean);
        Log.d(TAG, "insertNewFace uri: " + uri + ", faceBean: " + faceBean);
    }

    public synchronized List<FaceBean> upgradeFaceList(List<FaceBean> newFaceList) {
        Log.d(TAG, "upgradeFaceList");
        return mFaceDataHelper.upgradeFaceList(newFaceList);
    }

    public synchronized void insertFaceIdAndUserMap(Map<String, UserBean> faceIdAndUserMap) {
        Log.d(TAG, "insertFaceIdAndUserMap");
        for (String faceId : faceIdAndUserMap.keySet()) {
            Log.d(TAG, "insertFaceIdAndUserMap faceId: " + faceId);
            UserBean bean = faceIdAndUserMap.get(faceId);
            if (bean == null || TextUtils.isEmpty(bean.getUserId())) {
                continue;
            }
            TokenBean tokenBean = mTokenDataHelper.getToken(LOCAL_SYSTEM_TOKEN);
            if (tokenBean != null) {
                bean.setUserToken(tokenBean);
            }
            Uri newUserUri = mUserDataHelper.insertUser(bean);
            Log.d(TAG, "insertFaceIdAndUserMap newUserUri: " + newUserUri);
            if (newUserUri != null) {
                int updateRow = mFaceDataHelper.updateUserIdByFaceId(faceId,
                        bean.getUserId());
                Log.d(TAG, "insertFaceIdAndUserMap updateRow: " + updateRow);
            }
        }
    }

    public synchronized MemberBean getMemberByFaceId(String faceId) {
        Log.d(TAG, "getMemberByFaceId faceId:" + faceId);
        MemberBean memberBean = null;
        FaceBean faceBean = mFaceDataHelper.getFaceByFaceId(faceId);
        if (faceBean == null) {
            return null;
        }
        if (!TextUtils.isEmpty(faceBean.getUserId())) {
            UserBean userBean = mUserDataHelper
                    .getUserByUserId(faceBean.getUserId());
            if (userBean != null) {
                memberBean = new MemberBean();
                String tokenId = mUserDataHelper.getTokenIdByUserId(userBean.getUserId());
                if (!TextUtils.isEmpty(tokenId)) {
                    userBean.setUserToken(mTokenDataHelper.getToken(tokenId));
                }
                memberBean.setUserBean(userBean);
            }
        }
        return memberBean;
    }

    //    public synchronized MemberBean getMemberByUserId(String userId) {
    //        Log.d(TAG, "getMemberByUserId userId:" + userId);
    //        MemberBean memberBean = null;
    //        UserBean userBean = mUserDataHelper.getUserByUserId(userId);
    //        if (userBean != null) {
    //            memberBean = new MemberBean();
    //            String tokenId = mUserDataHelper.getTokenIdByUserId(userBean.getUserId());
    //            if (!TextUtils.isEmpty(tokenId)) {
    //                userBean.setUserToken(mTokenDataHelper.getToken(tokenId));
    //            }
    //            memberBean.setUserBean(userBean);
    //        }
    //        return memberBean;
    //    }

    public synchronized FaceBean getFaceByUserId(String userId) {
        Log.d(TAG, "getFaceByUserId userId:" + userId);
        return mFaceDataHelper.getFaceByUserId(userId);
    }

    public synchronized FaceBean getFaceByFaceId(String faceId) {
        Log.d(TAG, "getFaceByFaceId faceId:" + faceId);
        return mFaceDataHelper.getFaceByFaceId(faceId);
    }

    public synchronized UserBean getUserByUserId(String userId) {
        Log.d(TAG, "getUserByUserId userId:" + userId);
        return mUserDataHelper.getUserByUserId(userId);
    }

    public synchronized UserBean getFamilyAdmin() {
        Log.d(TAG, "getFamilyAdmin");
        return mUserDataHelper.getFamilyAdmin();
    }

    public synchronized void updateRecentUser(String userId) {
        Log.d(TAG, "updateRecentUser userId:" + userId);
        mUserDataHelper.updateRecentUser(userId);
    }

    public synchronized MemberBean getRecentMember() {
        Log.d(TAG, "getRecentMember");
        UserBean userBean = mUserDataHelper.getRecentUser();
        if (userBean == null) {
            return null;
        }
        String tokenId = mUserDataHelper.getTokenIdByUserId(userBean.getUserId());
        if (!TextUtils.isEmpty(tokenId)) {
            userBean.setUserToken(mTokenDataHelper.getToken(tokenId));
        }
        MemberBean memberBean = new MemberBean();
        memberBean.setUserBean(userBean);
        return memberBean;
    }

    public synchronized void updateMemberData(List<UserBean> userBeanList) {
        Log.d(TAG, "updateMemberData userBeanList:" + userBeanList);
        Map<String, UserBean> oldUserMap = mUserDataHelper.getUserMap();
        if (oldUserMap == null) {
            oldUserMap = new HashMap<>();
        }
        if (userBeanList != null && userBeanList.size() > 0) {
            Log.d(TAG, "updateMemberData size:" + userBeanList.size());
            for (UserBean userBean : userBeanList) {
                oldUserMap.remove(userBean.getUserId());
                Log.d(TAG, "updateMemberData success row:"
                        + mUserDataHelper.updateUser(userBean));
            }
        }
        Log.d(TAG, "allUser: " + mUserDataHelper.getUserList().size());
        Log.d(TAG, "removeUser: " + oldUserMap.size());
        for (String userId : oldUserMap.keySet()) {
            int updateRows = mFaceDataHelper.markFaceDeleteByUserId(userId);
            int deleteRow = mUserDataHelper.deleteUser(userId);
            Log.d(TAG, "removeUser userId: " + userId + ", updateRows: " + updateRows
                    + ", deleteRow: " + deleteRow);
        }
    }

    public synchronized List<FaceBean> getAllMarkDeleteFaces() {
        return mFaceDataHelper.getFaceList(true);
    }

    public synchronized void deleteFaceList(List<FaceBean> faceBeanList) {
        if (faceBeanList == null || faceBeanList.size() <= 0) {
            return;
        }
        int deleteRow = mFaceDataHelper.deleteFaceList();
        Log.d(TAG, "deleteFaceList deleteRow: " + deleteRow);
    }

    public synchronized void updateLocalSystemToken(String value) {
        Log.d(TAG, "updateLocalSystemToken value:" + value);
        TokenBean bean = new TokenBean(LOCAL_SYSTEM_TOKEN, value);
        mTokenDataHelper.updateToken(bean);
    }

    public synchronized void updateAllUserTokens() {
        TokenBean bean = mTokenDataHelper.getToken(LOCAL_SYSTEM_TOKEN);
        mUserDataHelper.updateAllUserTokenId(bean == null ? null : bean.getTokenId());
    }

    public synchronized Uri getFaceDataHelperUri() {
        return mFaceDataHelper.getContentUri();
    }

    public synchronized Uri getTokenDataHelperUri() {
        return mTokenDataHelper.getContentUri();
    }

    public synchronized MemberBean getBestMember(List<FaceBean> faceBeanList) {
        if (faceBeanList == null || faceBeanList.size() <= 0) {
            return null;
        }

        MemberBean memberBean = null;
        for (FaceBean faceBean : faceBeanList) {
            MemberBean localMember = getMemberByFaceId(faceBean.getFaceId());
            if (localMember == null) {
                if (memberBean == null) {
                    FaceBean localFace = getFaceByFaceId(faceBean.getFaceId());
                    if (localFace != null) {
                        memberBean = new MemberBean();
                        memberBean.setFaceBean(faceBean);
                    }
                }
            } else {
                if (memberBean == null) {
                    memberBean = localMember;
                    if (memberBean.getUserBean().isFamilyAdmin()) {
                        break;
                    }
                } else {
                    if (localMember.getUserBean().isFamilyAdmin()) {
                        memberBean = localMember;
                    }
                }
            }
        }
        return memberBean;
    }
}
