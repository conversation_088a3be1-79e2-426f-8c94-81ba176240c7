package com.ainirobot.coreservice.data.account;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.util.Log;

import com.ainirobot.coreservice.client.person.FaceBean;
import com.ainirobot.coreservice.data.account.bean.Tables;
import com.ainirobot.coreservice.data.provider.BaseDataHelper;

import java.util.ArrayList;
import java.util.List;

import static com.ainirobot.coreservice.data.account.bean.Tables.AUTHORITY_URI;
import static com.ainirobot.coreservice.data.account.bean.Tables.FACE;
import static com.ainirobot.coreservice.data.account.bean.Tables.FACE_DEVELOP;
import static com.ainirobot.coreservice.data.account.bean.Tables.FACE_PRE_RELEASE;

public class FaceDataHelper extends BaseDataHelper {

    private static final String TAG = FaceDataHelper.class.getSimpleName();
    private static final Uri CONTENT_URI = Uri.withAppendedPath(AUTHORITY_URI, FACE);
    private static final Uri CONTENT_URI_DEVELOP = Uri.withAppendedPath(AUTHORITY_URI, FACE_DEVELOP);
    private static final Uri CONTENT_URI_PRE_RELEASE = Uri.withAppendedPath(AUTHORITY_URI, FACE_PRE_RELEASE);

    private String mRemoteEnv;

    FaceDataHelper(Context context, String remoteEnv) {
        super(context);
        mRemoteEnv = remoteEnv;
    }

    @Override
    public Uri getContentUri() {
        switch (mRemoteEnv) {
            case "1":
                return CONTENT_URI_DEVELOP;
            case "2":
                return CONTENT_URI_PRE_RELEASE;
            default:
                return CONTENT_URI;
        }
    }

    boolean checkNameExist(String name) {
        Log.d(TAG, "checkNameExist name: " + name);
        Cursor cursor = query(Tables.FaceColumns.NAME + "=? AND "
                        + Tables.FaceColumns.IS_DELETE + "=?",
                new String[] {
                        name, "0"
                });
        if (cursor != null && cursor.moveToNext()) {
            cursor.close();
            return true;
        }
        return false;
    }

    Uri insertNewFace(FaceBean faceBean) {
        Log.d(TAG, "insertNewFace faceBean: " + faceBean);
        return insert(faceBeanToContentValue(faceBean));
    }

    private void updateFaceData(String faceId, String feature) {
        Log.d(TAG, "updateFaceData faceId: " + faceId + ", feature: " + feature);
        ContentValues contentValues = new ContentValues();
        contentValues.put(Tables.FaceColumns.FEATURE, feature);
        update(contentValues, Tables.FaceColumns.FACE_ID + "=?", new String[]{
                faceId
        });
    }

    int updateUserIdByFaceId(String faceId, String userId) {
        Log.d(TAG, "updateUserIdByFaceId faceId: " + faceId + ", userId: " + userId);
        ContentValues contentValues = new ContentValues();
        contentValues.put(Tables.FaceColumns.USER_ID, userId);
        return update(contentValues, Tables.FaceColumns.FACE_ID + "=?", new String[]{
                faceId
        });
    }

    int markFaceDeleteByUserId(String userId) {
        Log.d(TAG, "markFaceDeleteByUserId userId: " + userId);
        ContentValues contentValues = new ContentValues();
        contentValues.put(Tables.FaceColumns.IS_DELETE, 1);
        return update(contentValues, Tables.FaceColumns.USER_ID + "=?", new String[]{
                userId
        });
    }

    int deleteFaceList() {
        return delete(Tables.FaceColumns.IS_DELETE + "=?", new String[]{
                "1"
        });
    }

    private void deleteFace(String faceId) {
        Log.d(TAG, "deleteFace faceId: " + faceId);
        delete(Tables.FaceColumns.FACE_ID + "=?", new String[]{
                faceId
        });
    }

    List<FaceBean> upgradeFaceList(List<FaceBean> faceList) {
        List<FaceBean> currentFaceList = getFaceList(false);
        List<FaceBean> lostFaceBeanList = new ArrayList<>();
        Log.d(TAG, "upgradeFaceList faceList: " + faceList.size()
                + ", currentFaceList: " + currentFaceList.size());
        for (FaceBean currentFace : currentFaceList) {
            boolean isUpgrade = false;
            for (FaceBean faceBean : faceList) {
                if (faceBean.getFaceId().equals(currentFace.getFaceId())) {
                    isUpgrade = true;
                    updateFaceData(faceBean.getFaceId(), faceBean.getFeature());
                }
            }
            if (!isUpgrade) {
                lostFaceBeanList.add(currentFace);
                deleteFace(currentFace.getFaceId());
            }
        }
        return lostFaceBeanList;
    }

    List<FaceBean> getFaceList(boolean isMarkDelete) {
        Log.d(TAG, "getFaceList isMarkDelete: " + isMarkDelete);
        List<FaceBean> faceBeanList = new ArrayList<>();
        Cursor cursor = query(Tables.FaceColumns.IS_DELETE + "=?", new String[] {
                isMarkDelete ? "1" : "0"
        });
        while (cursor != null && cursor.moveToNext()) {
            faceBeanList.add(cursorToFaceBean(cursor));
        }
        if (cursor != null) {
            cursor.close();
        }
        return faceBeanList;
    }

    List<FaceBean> getOnlyLocalFaceList() {
        Log.d(TAG, "getOnlyLocalFaceList");
        List<FaceBean> faceBeanList = new ArrayList<>();
        Cursor cursor = query(Tables.FaceColumns.IS_ONLY_LOCAL + "=? AND "
                + Tables.FaceColumns.IS_DELETE + "=?", new String[] {
                "1", "0"
        });
        while (cursor != null && cursor.moveToNext()) {
            faceBeanList.add(cursorToFaceBean(cursor));
        }
        if (cursor != null) {
            cursor.close();
        }
        return faceBeanList;
    }

    FaceBean getFaceByFaceId(String faceId) {
        Log.d(TAG, "getFaceByFaceId");
        FaceBean faceBean = null;
        Cursor cursor = query(Tables.FaceColumns.FACE_ID + "=? AND "
                + Tables.FaceColumns.IS_DELETE + "=?", new String[] {
                faceId, "0"
        });
        if (cursor != null && cursor.moveToNext()) {
            faceBean = cursorToFaceBean(cursor);
        }
        if (cursor != null) {
            cursor.close();
        }
        return faceBean;
    }

    FaceBean getFaceByUserId(String userId) {
        Log.d(TAG, "getFaceByUserId");
        FaceBean faceBean = null;
        Cursor cursor = query(Tables.UserColumns.USER_ID + "=? AND "
                + Tables.FaceColumns.IS_DELETE + "=?", new String[] {
                userId, "0"
        });
        if (cursor != null && cursor.moveToNext()) {
            faceBean = cursorToFaceBean(cursor);
        }
        if (cursor != null) {
            cursor.close();
        }
        return faceBean;
    }

    private FaceBean cursorToFaceBean(Cursor cursor) {
        FaceBean faceBean = new FaceBean();
        faceBean.setFaceId(cursor.getString(cursor.getColumnIndex(Tables.FaceColumns.FACE_ID)));
        faceBean.setName(cursor.getString(cursor.getColumnIndex(Tables.FaceColumns.NAME)));
        faceBean.setPictureUri(cursor.getString(cursor
                .getColumnIndex(Tables.FaceColumns.PICTURE_URI)));
        faceBean.setUserId(cursor.getString(cursor.getColumnIndex(Tables.FaceColumns.USER_ID)));
        faceBean.setRegisterTime(cursor.getLong(cursor
                .getColumnIndex(Tables.FaceColumns.REGISTER_TIME)));
        faceBean.setFeature(cursor.getString(cursor.getColumnIndex(Tables.FaceColumns.FEATURE)));
        faceBean.setOnlyLocal(cursor.getInt(cursor.getColumnIndex(Tables.FaceColumns.IS_ONLY_LOCAL)) == 1);
        faceBean.setDelete(cursor.getInt(cursor.getColumnIndex(Tables.FaceColumns.IS_DELETE)) == 1);
        return faceBean;
    }

    private ContentValues faceBeanToContentValue(FaceBean faceBean) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(Tables.FaceColumns.FACE_ID, faceBean.getFaceId());
        contentValues.put(Tables.FaceColumns.NAME, faceBean.getName());
        contentValues.put(Tables.FaceColumns.PICTURE_URI, faceBean.getPictureUri());
        contentValues.put(Tables.FaceColumns.USER_ID, faceBean.getUserId());
        contentValues.put(Tables.FaceColumns.FEATURE, faceBean.getFeature());
        contentValues.put(Tables.FaceColumns.IS_ONLY_LOCAL, faceBean.isOnlyLocal() ? 1 : 0);
        contentValues.put(Tables.FaceColumns.IS_DELETE, faceBean.isDelete() ? 1 : 0);
        contentValues.put(Tables.FaceColumns.REGISTER_TIME, System.currentTimeMillis());
        return contentValues;
    }
}
