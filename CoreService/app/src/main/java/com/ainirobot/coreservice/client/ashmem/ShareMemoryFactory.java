package com.ainirobot.coreservice.client.ashmem;

import android.os.MemoryFile;
import android.os.ParcelFileDescriptor;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.utils.DelayTask;

import java.io.FileDescriptor;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * 共享内存空间工厂类
 * <p>
 * 创建共享内存映射：MemoryFile
 * 创建共享内存对外部进程暴露的序列化文件描述符：ParcelFileDescriptor
 * <p>
 * 扩展约束：
 * 1.每新增一个共享文件，创建自己的文件类型和共享内存块名称
 * 2.获取共享文件的文件操作符，需要提供单独的资源释放接口
 */
public class ShareMemoryFactory {
    private static final String TAG = "ShareMemoryFactory";

    private static final String MAP_PGM_OUTPUT_MEMORY_FILE = "map_pgm_output_memory_file";
    private static final String MAP_PGM_INPUT_MEMORY_FILE = "map_pgm_input_memory_file";

    private static CountDownLatch mCreateMemoryFileLatch;

    /**
     * 通过 MemoryFile 创建共享数据的共享内存空间和文件描述符
     *
     * @param type  共享数据类型
     * @param bytes 共享数据
     * @return
     */
    public static ParcelFileDescriptor createPfdFromMemoryFile(String type, byte[] bytes) {
        Log.d(TAG, "createPfdFromMemoryFile: type=" + type);
        ParcelFileDescriptor pfd = null;
        switch (type) {
            case Definition.TYPE_PFD_MAP_PGM_GET:
                pfd = getPfdFromMemoryFile(MAP_PGM_OUTPUT_MEMORY_FILE, bytes);
                break;
            case Definition.TYPE_PFD_MAP_PGM_SET:
                pfd = getPfdFromMemoryFile(MAP_PGM_INPUT_MEMORY_FILE, bytes);
                break;
            default:
                break;
        }
        return pfd;
    }

    private static ParcelFileDescriptor getPfdFromMemoryFile(final String name, final byte[] bytes) {
        mCreateMemoryFileLatch = new CountDownLatch(1);
        final ParcelFileDescriptor[] pfd = {null};
        try {
            DelayTask.submit(new Runnable() {
                public void run() {
                    long startTime = System.currentTimeMillis();
                    MemoryFile memoryFile = null;
                    try {
                        Log.d(TAG, "getPfdFromMemoryFile: --Start!");
                        memoryFile = new MemoryFile(name, bytes.length);
                        memoryFile.allowPurging(true);
                        memoryFile.writeBytes(bytes, 0, 0, bytes.length);
                        pfd[0] = getParcelFileDescriptor(memoryFile);
                    } catch (Exception e) {
                        Log.d(TAG, "getPfdFromMemoryFile:Exception: " + e.getMessage());
                        e.printStackTrace();
                    } finally {
                        closeMemoryFile(memoryFile, null);
                        Log.d(TAG, "getPfdFromMemoryFile: --Done! Cost time=" +
                                (System.currentTimeMillis() - startTime));
                        mCreateMemoryFileLatch.countDown();
                    }
                }
            });
            mCreateMemoryFileLatch.await(5, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Log.d(TAG, "requestForUploadExtraFile::InterruptedException:");
            e.printStackTrace();
        }
        return pfd[0];
    }

    private static ParcelFileDescriptor getParcelFileDescriptor(MemoryFile memoryFile) {
        try {
            Method method = MemoryFile.class.getDeclaredMethod("getFileDescriptor");
            method.setAccessible(true);
            FileDescriptor fd = (FileDescriptor) method.invoke(memoryFile);
            return ParcelFileDescriptor.dup(fd);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private static void closeMemoryFile(MemoryFile memoryFile, ParcelFileDescriptor pfd) {
        if (pfd != null) {
            try {
                pfd.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        if (memoryFile != null) {
            memoryFile.close();
        }
    }

}
