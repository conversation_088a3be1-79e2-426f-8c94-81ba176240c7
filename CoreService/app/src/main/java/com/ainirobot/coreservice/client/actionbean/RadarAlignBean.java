package com.ainirobot.coreservice.client.actionbean;

import com.ainirobot.coreservice.action.RadarAlignAction;

public class RadarAlignBean extends BaseBean {
    private String destination;
    private long startAlignTimeout = RadarAlignAction.START_ALIGN_TIMEOUT;
    private long retryDelayTime = RadarAlignAction.RETRY_DELAY_TIME;
    private long navigationTimeout = RadarAlignAction.NAVIGATION_TIMEOUT;
    private boolean isNeedRetry = true;

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public long getStartAlignTimeout() {
        return startAlignTimeout;
    }

    public void setStartAlignTimeout(long timeout) {
        this.startAlignTimeout = timeout;
    }

    public long getRetryDelayTime() {
        return retryDelayTime;
    }

    public void setRetryDelayTime(long time) {
        this.retryDelayTime = time;
    }

    public long getNavigationTimeout() {
        return navigationTimeout;
    }

    public void setNavigationTimeout(long timeout) {
        this.navigationTimeout = timeout;
    }

    public boolean getIsNeedRetry() {
        return isNeedRetry;
    }

    public void setIsNeedRetry(boolean isNeedRetry) {
        this.isNeedRetry = isNeedRetry;
    }

    @Override
    public String toString() {
        return "RadarAlignBean{" +
                "destination=" + destination +
                ", radarAlignTimeout=" + startAlignTimeout +
                ", retryDelayTime=" + retryDelayTime +
                ", navigationTimeout=" + navigationTimeout +
                ", isNeedRetry=" + isNeedRetry +
                '}';
    }
}
