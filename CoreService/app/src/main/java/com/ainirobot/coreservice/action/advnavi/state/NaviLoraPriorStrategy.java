package com.ainirobot.coreservice.action.advnavi.state;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.advnavi.bean.StateData;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.BasePoseBean;
import com.ainirobot.coreservice.client.actionbean.MultiRobotStatus;
import com.ainirobot.coreservice.client.actionbean.NavigationAdvancedBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.utils.DelayTask;

import java.util.List;

/**
 * Data: 2022/9/22 14:43
 * Author: wanglijing
 * 多机导航，lora优先级
 */
public class NaviLoraPriorStrategy extends BaseMultiRobotNaviState {

    /**
     * 当机器人执行优先级+距离策略时，只有当机器人之间的距离大于某一范围才生效。默认2m
     */
    private static final double DEFAULT_DISTANCE_BETWEEN_ROBOT = 2.0;

    /**
     * 临时导航点位
     */
    private Pose mTempNaviPose;

    private volatile MultipleStatus multipleStatus;
    private NaviStatus naviStatus;

    private enum MultipleStatus {
        PREPARE,
        START,
        GO_MAIN_DESTINATION,        //导航去目的地
        GO_STANDBY_DESTINATION,     //导航去备用点
        DELAY_FINISH_MAIN_AVAILABLE, //确认目的地可达延迟结束
        DELAY_FINISH_MAIN_DISABLE,   //确认目的地不可达延迟结束
        DELAY_CONFORM,             //延迟确认

    }

    private enum NaviStatus {
        REPLACE_TO_MAIN,       //替换导航点为目的地点
        REPLACE_TO_STANDBY,    //替换导航点为备用点
    }

    NaviLoraPriorStrategy(String name) {
        super(name);
    }


    @Override
    protected void preAction(StateData data, IStateListener listener) {
        super.preAction(data, listener);
        updateMultipleState(MultipleStatus.PREPARE);
    }


    @Override
    protected void doAction() {
        super.doAction();
        updateMultipleState(MultipleStatus.START);
    }


    @Override
    protected void handleMultipleRobotStatus(List<MultiRobotStatus> robotStatusList) {
        switch (multipleStatus) {
            case START:
                startStatus(robotStatusList);
                break;
            case GO_MAIN_DESTINATION:
                goMainDestinationStatus(robotStatusList);
                break;
            case DELAY_FINISH_MAIN_DISABLE:
                conformMainDisableStatus(robotStatusList);
                break;
            case GO_STANDBY_DESTINATION:
                goStandbyPoseStatus(robotStatusList);
                break;
            case DELAY_FINISH_MAIN_AVAILABLE:
                conformMainDestinationStatus(robotStatusList);
                break;
        }
    }


    @Override
    protected void onNavigationError(int result, String message, String extraData) {
        //去主目的地失败，则退出组件，如果多机未开，也会直接结束。否则继续尝试下一个点任务
        if (isContinue(result) && isNeedContinueNavigationTask()) {
            //存储已经导航失败的点位
            mExecutedPoseVector.add(getNaviDestination());
            goNextAvailablePoint(result, extraData);
        } else {
            processResultWithDestination(result, extraData);
        }
    }

    /**
     * 是否可以继续的错误
     *
     * @return true: 可以继续下面的策略  false: 不能继续需要结束事件
     */
    private boolean isContinue(int result) {
        if (result == Definition.ERROR_IN_DESTINATION) {
            return true;
        }
        return false;
    }

    private boolean isNeedContinueNavigationTask() {
        try {
            return !mDestination.equals(getNaviDestination()) && mMultiRobotConfig.isEnable();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private void startStatus(List<MultiRobotStatus> robotStatusList) {
        Log.i(TAG, "startStatus get pose");
        //根据lora优先级，获得导航点位
        updateTempNaviPose(getTempNavigationDestination(robotStatusList));
        if (mTempNaviPose == null) {
            //des为null指的是没有可用的目标点位，表明当前所有地点都有导航任务
            updateMultipleState(MultipleStatus.PREPARE);
            //ERROR_NO_AVAILABLE_DESTINATION
            onResult(Definition.ERROR_NO_AVAILABLE_DESTINATION);
            return;
        }

        if (TextUtils.equals(mDestination, mTempNaviPose.getName())) {
            //去主目标点
            updateMultipleState(MultipleStatus.GO_MAIN_DESTINATION);
            startGoMainDestination();
            return;
        }

        Pose tmpPose = isRobotInStandbyPoseArea(robotStatusList);
        if (tmpPose != null) {
            Log.d(TAG, "is arrived standby area");
            //表明机器人已经在备用点位，无需导航去备用点位
            updateMultipleState(MultipleStatus.PREPARE);
            //ComponentResult.RESULT_NAVIGATION_TRANSFER_ARRIVED
            arrivedMainDestination();
            return;
        }

        updateMultipleState(MultipleStatus.GO_STANDBY_DESTINATION);
        startGoStandbyDestination();
    }

    /**
     * 准备导航去目的地，但是去之前延迟确认
     */
    private void goMainDestinationStatus(List<MultiRobotStatus> robotStatusList) {
        if (judgeMainDestinationIsAvailable(robotStatusList)) {
            //可达继续导航，不需要换点
            Log.i(TAG, " main dest available ");
        } else {
            //如果主目标点不可达，延迟二次确认。
            Log.i(TAG, " main dest NOT available ");
            updateMultipleState(MultipleStatus.DELAY_CONFORM);
            delayConfirmMainDesDisable(robotStatusList);
        }
    }

    /**
     * 发现目的地不可达的延迟结束，确认目的地状态
     * 如果可达，继续导航去目的地
     * 发现不可达，导航去备用点
     */
    private void conformMainDisableStatus(List<MultiRobotStatus> robotStatusList) {
        //延迟确认发现主目标点可达，重置状态，否则终止去主目标点任务，去备用点位。
        if (judgeMainDestinationIsAvailable(robotStatusList)) {
            Log.i(TAG, "conformed main dest is available");
            delayConfirmMainDesAvailable(robotStatusList);
        } else {
            Pose standbyPose = findAvailableStandbyDestination(robotStatusList);
            if (standbyPose != null) {
                //找到了备用点，导航去备用点
                Log.i(TAG, "conformed main dest not available");
                startReplaceDestinationToStandby(standbyPose);
            } else {
                Log.i(TAG, "no free pose， continue go main");
                //如果没有备用点可以去，仍然执行当前任务。
                delayConfirmMainDesAvailable(robotStatusList);
            }
        }
    }

    /**
     * 准备导航去备用点
     * 如果发现目的地可达，延迟确认目的地状态
     * 若目的地还是不可达，找到备用点，导航去备用点
     */
    private void goStandbyPoseStatus(List<MultiRobotStatus> robotStatusList) {
        //如果主目标点可达，延迟二次确认。
        if (judgeMainDestinationIsAvailable(robotStatusList)) {
            Log.i(TAG, "find main dest available when prepare to standby pose ");
            updateMultipleState(MultipleStatus.DELAY_CONFORM);
            delayConfirmMainDesAvailable(robotStatusList);
        } else {
            /// 去停靠点过程中不断监测临时点
            boolean inUse = isPoseInUseCurrently(mTempNaviPose, robotStatusList);
            Log.d(TAG, "mTempNaviPose:" + (mTempNaviPose != null ? mTempNaviPose.toString() : "null")
                    + " ,mDestinationPose:" + (mDestinationPose != null ? mDestinationPose.toString() : "null")
                    + " ,inUse:" + inUse);
            if (inUse) {
                Pose standbyPose = findAvailableStandbyDestination(robotStatusList);
                if (standbyPose != null) {
                    Log.i(TAG, "old naviPose in use，update new standby pose" + standbyPose.getName());
                    startReplaceDestinationToStandby(standbyPose);
                } else {
                    Log.i(TAG, "main dest in use && no free standby pose when go standby");
                    delayConfirmMainDesAvailable(robotStatusList);
                }
            }
        }
    }

    /**
     * 发现目的地可达的延迟结束，确认目的地状态
     * 若可达，导航去目的地
     * 若不可达，更新状态去备用点
     */
    private void conformMainDestinationStatus(List<MultiRobotStatus> robotStatusList) {
        //如果主目标点可达。
        if (judgeMainDestinationIsAvailable(robotStatusList)) {
            updateMultipleState(MultipleStatus.GO_MAIN_DESTINATION);
            updateTempNaviPose(mDestinationPose);
            startReplaceDestinationToMain();
        } else {
            updateMultipleState(MultipleStatus.GO_STANDBY_DESTINATION);
        }
    }


    private void delayConfirmMainDesDisable(List<MultiRobotStatus> robotStatusList) {
        long delayTime = calculateDelayTime(robotStatusList);
        DelayTask.cancel(DELAY_TAG);
        DelayTask.submit(DELAY_TAG, new Runnable() {
            @Override
            public void run() {
                if (multipleStatus == MultipleStatus.DELAY_CONFORM) {
                    updateMultipleState(MultipleStatus.DELAY_FINISH_MAIN_DISABLE);
                }
            }
        }, delayTime);
    }

    private void delayConfirmMainDesAvailable(List<MultiRobotStatus> robotStatusList) {
        long delayTime = calculateDelayTime(robotStatusList);
        DelayTask.cancel(DELAY_TAG);
        DelayTask.submit(DELAY_TAG, new Runnable() {
            @Override
            public void run() {
                if (multipleStatus == MultipleStatus.DELAY_CONFORM) {
                    updateMultipleState(MultipleStatus.DELAY_FINISH_MAIN_AVAILABLE);
                }
            }
        }, delayTime);
    }


    private void startReplaceDestinationToMain() {
        if (!isAlive()) {
            return;
        }
        Log.d(TAG, "startReplaceDestinationToMain:" + mDestinationPose);
        updateNaviState(NaviStatus.REPLACE_TO_MAIN);
        stopNavigation();
    }

    /**
     * 替换主目标点为备用目标点。
     */
    private void startReplaceDestinationToStandby(Pose standbyPose) {
        if (!isAlive()) {
            return;
        }
        updateMultipleState(MultipleStatus.GO_STANDBY_DESTINATION);
        updateTempNaviPose(standbyPose);
        Log.d(TAG, "startReplaceDestinationToStandby:" + mTempNaviPose);
        updateNaviState(NaviStatus.REPLACE_TO_STANDBY);
        stopNavigation();
    }

    @Override
    public void processStopNavigation(String result) {
        if (naviStatus == NaviStatus.REPLACE_TO_MAIN) {
            startGoMainDestination();
        } else if (naviStatus == NaviStatus.REPLACE_TO_STANDBY) {
            startGoStandbyDestination();
        }
    }

    /**
     * 判断目标点是否可以到达，如果目标点不可达，则需判断备用点是否可达
     */
    public boolean judgeMainDestinationIsAvailable(List<MultiRobotStatus> robotStatusList) {
        if (mDestinationPose == null) {
            return false;
        }
        if (robotStatusList == null || robotStatusList.size() <= 0) {
            return true;
        }

        double curDistance = -1;
        BasePoseBean curPose = null;
        for (int i = 0; i < robotStatusList.size(); i++) {
            MultiRobotStatus robotStatus = robotStatusList.get(i);
            if (robotStatus.isCurRobot()) {
                //status 为1表示无定位 2表示未在导航 3表示在导航中
                curPose = robotStatus.getPose();
                Log.d(TAG, "currentRobot curPose: " + curPose);
                if (curPose == null) {
                    return false;
                }
                curDistance = calculatePoseDistance(mDestinationPose, curPose);
                Log.d(TAG, "currentRobot to main pose: " + (mDestinationPose.getName()) + " distance:" + curDistance);
                break;
            }
        }
        if (curDistance < 0) {
            Log.i(TAG, "currentRobot distance is <0 ,return false ");
            return false;
        }

        for (int i = 0; i < robotStatusList.size(); i++) {
            MultiRobotStatus robotStatus = robotStatusList.get(i);
            if (robotStatus.isCurRobot()) {
                continue;
            }
            BasePoseBean poseBean;
            switch (robotStatus.getStatus()) {
                case 2:
                    //不在导航中，如果机器人当前点位在目标点，则目标点也属于不可达
                    poseBean = robotStatus.getPose();
                    boolean isInUse = calculateTargetPoseInUse(mDestinationPose, poseBean);
                    Log.d(TAG, "robot:" + robotStatus.getId()
                            + " not in navigation, use main pose: " + isInUse);
                    if (isInUse) {
                        return false;
                    }
                    break;
                case 3:
                    //处于导航模式，如果目标点被占用，需判断是否距离近，如果距离近也可以替换任务
                    poseBean = robotStatus.getGoal();
                    BasePoseBean cPose = robotStatus.getPose();
                    double distance = calculatePoseDistance(mDestinationPose, poseBean);
                    //如果被占用，对比当前点位距离差 确定那个机器距离目标点近
                    if (isInPoseRange(distance)) {
                        Log.d(TAG, "robot: " + robotStatus.getId() + " navi to main pose ,and in main range: " + distance);
                        double cDis = Math.sqrt(Math.pow((cPose.getX() - poseBean.getX()), 2)
                                + Math.pow((cPose.getY() - poseBean.getY()), 2));
                        double betweenDis = Math.sqrt(Math.pow((cPose.getX() - curPose.getX()), 2)
                                + Math.pow((cPose.getY() - curPose.getY()), 2));
                        //当前机器人距离目标点较近,且两个机器人之间距离差满足一定条件才允许替换任务
                        if (!(curDistance < cDis && betweenDis > DEFAULT_DISTANCE_BETWEEN_ROBOT)) {
                            Log.d(TAG, "robot: " + robotStatus.getId() + " is nearly main pose !");
                            return false;
                        }
                    } else {
                        Log.d(TAG, "robot: " + robotStatus.getId() + " navi to main pose ,but not in main range: " + distance);
                    }
                    break;
                default:
                    break;
            }
        }
        return true;
    }


    /**
     * 当前是否是在备用点位，如果已经在备用点位，则结束任务
     *
     * @return Pose 如果已经在某一个备用点位，返回备用点位信息
     */
    private Pose isRobotInStandbyPoseArea(List<MultiRobotStatus> robotStatusList) {
        BasePoseBean currentPose = null;
        for (int i = 0; i < robotStatusList.size(); i++) {
            MultiRobotStatus robotStatus = robotStatusList.get(i);
            if (robotStatus.isCurRobot()) {
                //status 为1表示无定位 2表示未在导航 3表示在导航中
                if (robotStatus.getStatus() == 2) {
                    currentPose = robotStatus.getPose();
                    Log.d(TAG, "isRobotInStandbyPoseArea current robot location:" + currentPose);
                }
                break;
            }
        }

        if (currentPose == null) {
            Log.e(TAG, "isRobotInStandbyPoseArea current pose is null");
            return null;
        }

        for (int i = 0; i < mValidStandbyList.size(); i++) {
            Pose pose = mValidStandbyList.get(i);
            boolean inUse = calculateTargetPoseInUse(pose, currentPose);
            if (inUse) {
                Log.d(TAG, "isRobotInStandbyPoseArea in " + pose.getName() + " area");
                return pose;
            }
        }
        Log.d(TAG, "isRobotInStandbyPoseArea -not- in area");
        return null;
    }

    private void goNextAvailablePoint(int result, String extraData) {
        Pose availablePose = getTempNavigationDestination(mRobotStatusList);
        Log.d(TAG, "goNextAvailablePoint pose: " + availablePose);
        if (availablePose == null) {
            processResultWithDestination(result, extraData);
        } else {
            if (multipleStatus != MultipleStatus.PREPARE) {
                updateMultipleState(MultipleStatus.START);
            }
        }
    }

    private long calculateDelayTime(List<MultiRobotStatus> robotStatusList) {
        int deviceCnt = calculateHigherPriorityDeviceCnt(robotStatusList);
        Log.d(TAG, "calculateDelayTime:" + deviceCnt);
        return deviceCnt * Definition.SECOND + 500;
    }

    /**
     * 根据多机状态找到适合的目的地
     */
    public Pose getTempNavigationDestination(List<MultiRobotStatus> robotStatusList) {
        //去主目的地
        if (judgeMainDestinationIsAvailable(robotStatusList)) {
            Log.d(TAG, "getTempNavigationDestination main:" + mDestinationPose.getName());
            return mDestinationPose;
        }
        //去备用目标点
        Pose standbyDes = findAvailableStandbyDestination(robotStatusList);
        if (standbyDes != null) {
            Log.d(TAG, "getTempNavigationDestination standby:" + standbyDes.getName());
            return standbyDes;
        }
        return null;
    }

    /**
     * 寻找可以到达的备用点，mValidStandbyList是备用有效点列表，不包括目的地
     *
     * @return 如果所有备用点都不可到达，返回null，否则返回可到达点
     */
    public Pose findAvailableStandbyDestination(List<MultiRobotStatus> robotStatusList) {
        for (int i = 0; i < mValidStandbyList.size(); i++) {
            Pose pose = mValidStandbyList.get(i);
            if (mExecutedPoseVector != null && mExecutedPoseVector.contains(pose.getName())) {
                continue;
            }
            boolean inUse = isPoseInUseCurrently(pose, robotStatusList);
            Log.d(TAG, "findAvailableStandbyDestination pose:" + pose.getName() + " inUse: " + inUse);
            if (!inUse) {
                return pose;
            }
        }
        return null;
    }

    /**
     * 计算延迟导航任务需要多久下发
     */
    private int calculateHigherPriorityDeviceCnt(List<MultiRobotStatus> robotStatusList) {
        int deviceCnt = 0;
        int curPriority = mMultiRobotConfig.getLoraId();
        for (int i = 0; i < robotStatusList.size(); i++) {
            MultiRobotStatus robotStatus = robotStatusList.get(i);
            if (robotStatus.getId() > curPriority) {
                deviceCnt++;
            }
        }
        return deviceCnt;
    }


    private void startGoMainDestination() {
        if (mTempNaviPose == null) {
            Log.e(TAG, "startGoMainDestination mTempNaviPose is null");
            return;
        }
        Log.d(TAG, "startGoMainDestination");
        NavigationAdvancedBean bean = cloneNaviBean();
        bean.setDestination(mTempNaviPose.getName());

        startNavigation(bean);
    }

    private void startGoStandbyDestination() {
        if(mTempNaviPose == null){
            Log.e(TAG, "startGoStandbyDestination mTempNaviPose is null");
            return;
        }
        startNavigation(mTempNaviPose.getName(), isOneAvailableStandbyPoseCurrently());
    }

    private void updateMultipleState(MultipleStatus status) {
        Log.i(TAG, "updateMultiStatus: " + status);
        multipleStatus = status;
    }

    private void updateTempNaviPose(Pose pose) {
        Log.i(TAG, "updateTempNaviPose: " + pose);
        mTempNaviPose = pose;
    }

    private void updateNaviState(NaviStatus status) {
        Log.i(TAG, "updateNaviState: " + status);
        naviStatus = status;
    }

}
