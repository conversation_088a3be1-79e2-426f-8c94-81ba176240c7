/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.coreservice.core.system;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.ContentObserver;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.ApplicationWrapper;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.SettingsUtil.SettingsListener;
import com.ainirobot.coreservice.config.ConfigManager;
import com.ainirobot.coreservice.config.CoreConfig.ShutdownConfig;
import com.ainirobot.coreservice.core.InternalDef;
import com.ainirobot.coreservice.core.RobotSettingManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

public class RobotSetting {
    private static final String TAG = "RobotSetting";
    private static final String ACTION_SET_POWEROFF_ALARM = "org.codeaurora.poweroffalarm.action.SET_ALARM";
    private static final String ACTION_CANCEL_POWEROFF_ALARM = "org.codeaurora.poweroffalarm.action.CANCEL_ALARM";
    private static final String POWER_OFF_ALARM_PACKAGE = "com.qualcomm.qti.poweroffalarm";

    private static RobotSetting mInstance;
    private static final boolean VISION_REPOSITION = true;
    private static RobotSettingManager mRobotSettingManager;

    private static Context sContext;

    private AlarmManager mAlarmManager;
    private PendingIntent mShutdownIntent;
    private SettingListener mListener;

    public static void init(RobotSettingManager robotSettingManager) {
        mRobotSettingManager = robotSettingManager;
        mInstance = new RobotSetting();
    }

    private RobotSetting() {
        Log.d(TAG, "RobotSetting register broadcast receiver");
        sContext = ApplicationWrapper.getContext();
        initAlarmManager();

        IntentFilter filter = new IntentFilter(InternalDef.ACTION_START_CHARGE);
        filter.addAction(InternalDef.ACTION_SET_CHARGE_PILE);
        filter.addAction(Definition.ACTION_REPOSITION);
        filter.addAction(Definition.ACTION_E70_RECOVERY_REPOSITION);
        filter.addAction(InternalDef.ACTION_ROBOT_REPOSITION);
        filter.addAction(InternalDef.ACTION_CONFIGURE_STOP_CHARGE);
        filter.addAction(Definition.ACTION_STOP_CHARGE_BY_UI);
        filter.addAction(Definition.ACTION_STOP_CHARGING_BY_APP);
        filter.addAction(InternalDef.ACTION_START_DORMANCY);
        filter.addAction(InternalDef.ACTION_RECOVERY);
        filter.addAction(InternalDef.ACTION_START_SHUTDOWN_TIMER);
        filter.addAction(Intent.ACTION_TIME_CHANGED);
        filter.addAction(Intent.ACTION_SHUTDOWN);
        BroadcastReceiver receiver = new RobotSettingReceiver();
        sContext.registerReceiver(receiver, filter);
        initSettingListener();

        initShutdownListener();
    }

    private void initSettingListener() {
        mRobotSettingManager.registerListener(
                Definition.ROBOT_SETTING_AUTO_CHARGE, mRobotSettingListener);
        mRobotSettingManager.registerListener(
                Definition.ROBOT_SETTING_OTA_UPDATE, mRobotSettingListener);
        mRobotSettingManager.registerListener(
                Definition.ROBOT_USABLE_WHEN_CHARGING, mRobotSettingListener);
    }

    /**
     * 监听定时关机配置变化
     */
    private void initShutdownListener() {

        ShutdownConfig config = ConfigManager.getShutdownConfig();
        if (config == null || !config.isEnabled()) {
            Log.d(TAG, "Shutdown is disabled");
            return;
        }

        Log.d(TAG, "initShutdownListener");

        //写入默认值
        SettingsUtil.putString(sContext, SettingsUtil.ROBOT_SETTING_SHUTDOWN_TIMER_DEF, config.getDefaultTime());

        startShutdownAlarm();
        SettingsUtil.registerSettingsListener(sContext, SettingsUtil.ROBOT_SETTING_SHUTDOWN_TIMER,
                new SettingsListener() {
                    @Override
                    public void onChange(boolean selfChange, String name) {
                        Log.d(TAG, "On shutdown time change : " + selfChange);
                        startShutdownAlarm();
                    }
                });
        SettingsUtil.registerSettingsListener(sContext, SettingsUtil.ROBOT_SETTING_SHUTDOWN_SWITCH,
                new SettingsListener() {
                    @Override
                    public void onChange(boolean selfChange, String name) {
                        Log.d(TAG, "On shutdown switch change : " + selfChange);
                        startShutdownAlarm();
                    }
                });
    }

    private void initAlarmManager() {
        mAlarmManager = (AlarmManager) sContext.getSystemService(Context.ALARM_SERVICE);
        try {
            //解决Alarm时间延迟问题
            Field field = AlarmManager.class.getDeclaredField("mAlwaysExact");
            field.setAccessible(true);
            field.set(mAlarmManager, true);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        }
    }

    /**
     * 创建定时关机Alarm
     */
    private void startShutdownAlarm() {
        String time = SettingsUtil.getString(sContext, SettingsUtil.ROBOT_SETTING_SHUTDOWN_TIMER);
        if (time == null) {
            //如果当前用户未配置，读取默认值
            time = SettingsUtil.getString(sContext, SettingsUtil.ROBOT_SETTING_SHUTDOWN_TIMER_DEF);
        }

        String shutDownSwitch = SettingsUtil.getString(sContext, SettingsUtil.ROBOT_SETTING_SHUTDOWN_SWITCH);

        Log.d(TAG, "Start shutdown alarm : " + time + ", switch: " + shutDownSwitch);
        mAlarmManager.cancel(mShutdownIntent);

        if (TextUtils.isEmpty(time) || !"1".equals(shutDownSwitch)) {
            Log.d(TAG, "shutdown switch closed");
            return;
        }
        String[] parts = time.split(":");

        int hour = Integer.parseInt(parts[0]);
        int minute = Integer.parseInt(parts[1]);

        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minute);
        if (calendar.getTimeInMillis() <= System.currentTimeMillis()) {
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }
        calendar.set(Calendar.SECOND, 0);

        SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss", Locale.getDefault());
        Log.d(TAG, "Start shutdown at : " + format.format(calendar.getTimeInMillis()));
        Intent intent = new Intent();
        intent.putExtra("hour", calendar.get(Calendar.HOUR_OF_DAY));
        intent.putExtra("minute", calendar.get(Calendar.MINUTE));
        intent.setAction(InternalDef.ACTION_START_SHUTDOWN_TIMER);
        mShutdownIntent = PendingIntent.getBroadcast(sContext, 0, intent, PendingIntent.FLAG_CANCEL_CURRENT);
        mAlarmManager.setRepeating(AlarmManager.RTC_WAKEUP,
                calendar.getTimeInMillis(), AlarmManager.INTERVAL_DAY, mShutdownIntent);
    }

    private void startPowerOnBroadcast() {
        String time = SettingsUtil.getString(sContext, SettingsUtil.ROBOT_SETTING_POWERON_TIMER);

        String powerOnSwitch = SettingsUtil.getString(sContext, SettingsUtil.ROBOT_SETTING_POWERON_SWITCH);

        Log.d(TAG, "startPowerOnBroadcast : " + time + ", switch: " + powerOnSwitch);

        if (TextUtils.isEmpty(time) || !"1".equals(powerOnSwitch)) {
            Log.d(TAG, "powerOn switch closed");
            return;
        }
        String[] parts = time.split(":");

        int hour = Integer.parseInt(parts[0]);
        int minute = Integer.parseInt(parts[1]);

        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        
        // 获取当前时间并增加2分钟的缓冲时间
        Calendar currentTime = Calendar.getInstance();
        currentTime.add(Calendar.MINUTE, 2);
        
        // 如果设置的时间小于等于当前时间+2分钟，则设置为明天
        if (calendar.getTimeInMillis() <= currentTime.getTimeInMillis()) {
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            Log.d(TAG, "Power on time is too close to current time, set to tomorrow");
        }

        long alarmTime = calendar.getTimeInMillis();
        
        SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss", Locale.getDefault());
        Log.d(TAG, "Set power on at : " + format.format(alarmTime) + 
                   ", current time: " + format.format(System.currentTimeMillis()));
        
        Intent setAlarmIntent = new Intent(ACTION_SET_POWEROFF_ALARM);
        setAlarmIntent.setPackage(POWER_OFF_ALARM_PACKAGE);
        setAlarmIntent.putExtra("time", alarmTime);
        ApplicationWrapper.getContext().sendBroadcast(setAlarmIntent);
    }

    private class RobotSettingReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            Log.d(TAG, "Receive : " + intent.getAction());
            if (mListener == null) {
                Log.e(TAG, "No one receive : " + intent.getAction());
                return;
            }

            switch (action) {
                case InternalDef.ACTION_SET_CHARGE_PILE:
                    int chargingEnvironment = intent.getIntExtra(InternalDef.EXTRA_SET_CHARGING_ENVIRONMENT, -1);
                    mListener.onNewSetting(InternalDef.SETTING_SET_CHARGE_PILE, String.valueOf(chargingEnvironment));
                    break;

                case InternalDef.ACTION_START_CHARGE:
                    if (null == intent.getExtras()) {
                        mListener.onNewSetting(InternalDef.SETTING_START_CHARGE, null);
                    } else {
                        mListener.onNewSetting(InternalDef.SETTING_START_CHARGE_TIMING, null);
                    }
                    break;

                case InternalDef.ACTION_ROBOT_REPOSITION:
                    mListener.onNewSetting(InternalDef.SETTING_START_REPOSITION, String.valueOf(VISION_REPOSITION));
                    break;

                case Definition.ACTION_REPOSITION:
                    boolean isVision = intent.getBooleanExtra(Definition.REPOSITION_VISION, false);
                    mListener.onNewSetting(InternalDef.SETTING_START_REPOSITION, String.valueOf(isVision));
                    break;
                case Definition.ACTION_E70_RECOVERY_REPOSITION:
                    mListener.onNewSetting(InternalDef.SETTING_E70_RECOVERY_START_REPOSITION, null);
                    break;
                case InternalDef.ACTION_CONFIGURE_STOP_CHARGE:
                    mListener.onNewSetting(InternalDef.SETTING_STOP_CHARGE, null);
                    break;

                case Definition.ACTION_STOP_CHARGING_BY_APP:
                    mListener.onNewSetting(InternalDef.SETTING_STOP_CHARGE_BY_APP, null);
                    break;

                case InternalDef.ACTION_START_SHUTDOWN_TIMER:
                    // 2021/01/01 00:00:00 000
                    Date finalData = new Date(1609430400000L);
                    final Date date = new Date();
                    if (date.before(finalData)) {
                        Log.e(TAG, "Shutdown receive, but before 2021/01/01 00:00:00 000");
                        return;
                    }
                    Calendar calendar = Calendar.getInstance();
                    int currentMinute = calendar.get(Calendar.MINUTE);
                    int currentHour = calendar.get(Calendar.HOUR_OF_DAY);
                    int minute = intent.getIntExtra("minute", currentMinute);
                    int hour = intent.getIntExtra("hour", currentHour);
                    Log.d(TAG, "Shutdown receive : " + (hour + ":" + minute) + "  current: " + (currentHour + ":" + currentMinute));
                    //相差5分钟内，认为这次定时有效
                    if (hour == currentHour && Math.abs(minute - currentMinute) < 5) {
                        mListener.onNewSetting(InternalDef.SETTING_SHUTDOWN_TIMER, null);
                    }
                    break;

                //开始休眠
                case InternalDef.ACTION_START_DORMANCY:
                    int time = intent.getIntExtra(Definition.JSON_DORMANCY_TIME, 60);
                    String name = intent.getStringExtra(Definition.JSON_DORMANCY_NAME);
                    JSONObject jsonObject = new JSONObject();
                    try {
                        jsonObject.put(Definition.JSON_DORMANCY_TIME, time);
                        jsonObject.put(Definition.JSON_DORMANCY_NAME, name);
                        mListener.onNewSetting(InternalDef.SETTING_START_DORMANCY, jsonObject.toString());
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    break;

                case InternalDef.ACTION_RECOVERY:
                    String userName = intent.getStringExtra(Definition.JSON_REMOTE_USER_NAME);
                    String password = intent.getStringExtra(Definition.JSON_REMOTE_PASSWORD);
                    JSONObject json = new JSONObject();
                    try {
                        json.put(Definition.JSON_REMOTE_USER_NAME, userName);
                        json.put(Definition.JSON_REMOTE_PASSWORD, password);
                        mListener.onNewSetting(InternalDef.SETTING_RECOVERY, json.toString());
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    break;

                case Intent.ACTION_TIME_CHANGED:
                    startShutdownAlarm(); //时间改变，重置Alarm
                    break;

                case Intent.ACTION_SHUTDOWN:
                    Log.d(TAG, "System shutdown detected, setting power on alarm");
                    // 关机时设置开机定时
                    startPowerOnBroadcast();
                    break;

                default:
                    break;
            }
        }
    }

    public static void setListener(SettingListener mListener) {
        mInstance.mListener = mListener;
    }

    public static boolean isAllowAutoCharge() {
        return ("1").equals(mRobotSettingManager.
                getRobotSetting(Definition.ROBOT_SETTING_AUTO_CHARGE));
    }

    public static boolean isAllowAutoOta() {
        return ("1").endsWith(mRobotSettingManager.
                getRobotSetting(Definition.ROBOT_SETTING_OTA_UPDATE));
    }

    public static boolean isAllowChargingChat() {
        return ("1").equals(mRobotSettingManager.
                getRobotSetting(Definition.ROBOT_USABLE_WHEN_CHARGING));
    }

    public static boolean isSituServiceOpened() {
        return !("0").equals(mRobotSettingManager.
                getRobotSetting(Definition.ROBOT_SETTING_SITU_SERVICE_STATUS));
    }

    public static boolean isNavigationSupport() {
        return !("1").equals(mRobotSettingManager.
                getRobotSetting(Definition.ROBOT_SETTING_NAVIGATION_NONSUPPORT));
    }

    public static boolean isNavigationEnableElevator() {
        return ("1").equals(mRobotSettingManager.
                getRobotSetting(Definition.ROBOT_SETTING_ELEVATOR_CONTROL_ENABLED));
    }

    public static boolean isEnableNavigationInCharging() {
        return ("1").equals(mRobotSettingManager.
                getRobotSetting(Definition.ROBOT_SETTING_ENABLE_NAVIGATION_INCHARGING));
    }

    public static boolean isOtaDowngrade() {
        return ("2").equals(mRobotSettingManager.
                getRobotSetting(Definition.ROBOT_SETTING_OTA_TYPE));
    }

    public static int getDefaultBodySpeed() {
        String speedStr = mRobotSettingManager.
                getRobotSetting(Definition.ROBOT_SETTING_DEFAULT_BODY_SPEED);
        if (TextUtils.isEmpty(speedStr)) {
            return Definition.DEFAULT_BODY_SPEED;
        }
        try {
            int speed = Integer.parseInt(speedStr);
            return speed <= 0 ? Definition.DEFAULT_BODY_SPEED : speed;
        } catch (Exception e) {
            return Definition.DEFAULT_BODY_SPEED;
        }
    }

    public static int getFocusFollowTrackTimeout() {
        String timeoutStr = mRobotSettingManager.
                getRobotSetting(Definition.ROBOT_SETTING_FOCUS_FOLLOW_TRACK_TIMEOUT);
        if (TextUtils.isEmpty(timeoutStr)) {
            return Definition.DEFAULT_FOCUS_FOLLOW_TRACK_TIMEOUT;
        }
        try {
            int timeout = Integer.parseInt(timeoutStr);
            return timeout <= 0 ? Definition.DEFAULT_FOCUS_FOLLOW_TRACK_TIMEOUT : timeout;
        } catch (Exception e) {
            return Definition.DEFAULT_FOCUS_FOLLOW_TRACK_TIMEOUT;
        }
    }

    public static float getFocusFollowAngleAdjust() {
        return SettingsUtil.getFloat(sContext, SettingsUtil.ROBOT_FOCUS_FOLLOW_ANGLE, 3.0f);
    }

    interface SettingListener {
        void onNewSetting(String type, String params);
    }

    private ContentObserver mRobotSettingListener = new ContentObserver(null) {
        private static final String TAG = "RobotSettingListener";

        @Override
        public void onChange(boolean selfChange, Uri uri) {
            String key = uri.getLastPathSegment();
            if (mListener == null || key == null) {
                return;
            }
            Log.d(TAG, key + " changed");
            switch (key) {
                case Definition.ROBOT_SETTING_AUTO_CHARGE:
                    String autoCharge = mRobotSettingManager.
                            getRobotSetting(Definition.ROBOT_SETTING_AUTO_CHARGE, true);
                    mListener.onNewSetting(InternalDef.SETTING_AUTO_CHARGE,
                            String.valueOf("1".equals(autoCharge)));
                    break;
                case Definition.ROBOT_SETTING_OTA_UPDATE:
                    String allowOta = mRobotSettingManager.
                            getRobotSetting(Definition.ROBOT_SETTING_OTA_UPDATE, true);
                    mListener.onNewSetting(InternalDef.SETTING_AUTO_OTA,
                            String.valueOf("1".equals(allowOta)));
                    break;

                case Definition.ROBOT_USABLE_WHEN_CHARGING:
                    /**
                     * 此需求只是针对消毒豹机器的，其他机器不需要处理，并且其他机器会引起battery状态被异常enable的bug。
                     * 比如建图时断网重连后，此处逻辑会导致机器在充电时也可以使用，导致maptool无法正常设置充电桩点位。
                     * 所以只有消毒豹机器才需要处理。
                     * 目前消毒豹机器不需要再维护，这部分代码可以删除。
                     */
//                    if(ProductInfo.isSaiphXD()){
//                        String usableCharging = mRobotSettingManager.
//                                getRobotSetting(Definition.ROBOT_USABLE_WHEN_CHARGING, true);
//                        Log.d(TAG, "robot_usable_when_charging onChange: " + usableCharging);
//                        mListener.onNewSetting(InternalDef.SETTING_CHARGING_USABLE,
//                                String.valueOf("1".equals(usableCharging)));
//                    }
                    break;
                default:
                    break;
            }
        }
    };
}
