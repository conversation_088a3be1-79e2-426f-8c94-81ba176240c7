package com.ainirobot.coreservice.core.permission;

import android.app.IProcessObserver;
import android.os.Handler;
import android.os.Message;

public class ProcessObserver extends IProcessObserver.Stub {

    private Handler mHandler;

    public ProcessObserver(Handler handler) {
        mHandler = handler;
    }

    @Override
    public void onForegroundActivitiesChanged(int pid, int uid, boolean foregroundActivities) {
        if (mHandler != null) {
            Message message = new Message();
            message.what = PermissionManager.TYPE_FOREGROUND_ACTIVITIES_CHANGED;
            message.obj = new PermissionBean(pid, uid, foregroundActivities);
            mHandler.sendMessage(message);
        }
    }

    @Override
    public void onProcessDied(int pid, int uid) {
        if (mHandler != null) {
            Message message = new Message();
            message.what = PermissionManager.TYPE_PROCESS_DIED;
            message.obj = new PermissionBean(pid, uid);
            mHandler.sendMessage(message);
        }
    }
}
