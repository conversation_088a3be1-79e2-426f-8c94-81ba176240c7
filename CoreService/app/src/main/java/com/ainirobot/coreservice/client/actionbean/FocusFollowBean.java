package com.ainirobot.coreservice.client.actionbean;

/**
 * Created by orion on 2018/4/12.
 */

public class FocusFollowBean extends BaseBean {
    //int reqId, String personName, long lostTimer, float maxDistance, int type
    private int personId;
    private long lostTimer;
    private float maxDistance;
    private boolean isAllowMoveBody = true;

    public FocusFollowBean() {
    }

    public long getLostTimer() {
        return lostTimer;
    }

    public float getMaxDistance() {
        return maxDistance;
    }

    public void setLostTimer(long lostTimer) {
        this.lostTimer = lostTimer;
    }

    public void setMaxDistance(float maxDistance) {
        this.maxDistance = maxDistance;
    }

    public int getPersonId() {
        return personId;
    }

    public void setPersonId(int personId) {
        this.personId = personId;
    }

    public boolean isAllowMoveBody() {
        return this.isAllowMoveBody;
    }

    public void setAllowMoveBody(boolean isAllow) {
        this.isAllowMoveBody = isAllow;
    }
}
