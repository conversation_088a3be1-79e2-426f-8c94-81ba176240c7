package com.ainirobot.coreservice.resmanage;

import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.core.status.LocalSubscriber;

import org.json.JSONException;
import org.json.JSONObject;

public class Radar extends AbstractResProxy {

    private static final String TAG = "Radar";

    Radar(ResManager manager, int resId) {
        super(manager, resId);
        registerRadarUpdateListener();
    }

    @Override
    boolean stopOperate() {
        return true;
    }

    @Override
    boolean releaseRes() {
        return true;
    }

    private void registerRadarUpdateListener() {
        Log.d(TAG, "registerRadarUpdateListener");
        mCoreService.getStatusManager().registerStatusListener(Definition.STATUS_RADAR, new LocalSubscriber() {
            @Override
            public void onStatusUpdate(String type, String data) {
                if (Definition.STATUS_RADAR.equals(type)) {
                    Log.d(TAG, "radar update data=" + data);
                    try {
                        JSONObject jsonObject = new JSONObject(data);
                        mIsAvailable = jsonObject.optBoolean(Definition.JSON_NAVI_OPEN_RADAR);
                        Log.d(TAG, "parse mIsRadarOpen = " + mIsAvailable);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            }
        });
    }
}
