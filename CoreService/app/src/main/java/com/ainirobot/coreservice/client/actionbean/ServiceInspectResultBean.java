package com.ainirobot.coreservice.client.actionbean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * service inspect result bean
 *
 * @version V1.0.0
 * @date 2019/10/14 20:48
 */
public class ServiceInspectResultBean {

    private int result;
    private int step;
    private boolean otaResult = true;
    private List<PassBean> pass = new ArrayList<>();
    private List<FailBean> fail = new ArrayList<>();

    public ServiceInspectResultBean() {
    }

    public ServiceInspectResultBean(int result, int step, List<PassBean> pass, List<FailBean> fail) {
        this.result = result;
        this.step = step;
        this.pass = pass;
        this.fail = fail;
    }

    public ServiceInspectResultBean(int result, int step, boolean otaResult, List<PassBean> pass, List<FailBean> fail) {
        this.result = result;
        this.step = step;
        this.otaResult = otaResult;
        this.pass = pass;
        this.fail = fail;
    }

    public boolean isOtaResult() {
        return otaResult;
    }

    public void setOtaResult(boolean otaResult) {
        this.otaResult = otaResult;
    }

    public int getResult() {
        return result;
    }

    public void setResult(int result) {
        this.result = result;
    }

    public int getStep() {
        return step;
    }

    public void setStep(int step) {
        this.step = step;
    }

    public List<PassBean> getPass() {
        return pass;
    }

    public void setPass(List<PassBean> pass) {
        this.pass = pass;
    }

    public List<FailBean> getFail() {
        return fail;
    }

    public void setFail(List<FailBean> fail) {
        this.fail = fail;
    }

    public static class PassBean {

        public PassBean() {
        }

        public PassBean(String name) {
            this.name = name;
        }

        private String name;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return "PassBean{" +
                    "name='" + name + '\'' +
                    '}';
        }
    }

    public static class FailBean {

        public FailBean() {
        }

        public FailBean(String name, int code, String errorMsg) {
            this.name = name;
            this.code = code;
            this.errorMsg = errorMsg;
        }

        public FailBean(String name, int code, String errorMsg, boolean isIgnore) {
            this.name = name;
            this.code = code;
            this.errorMsg = errorMsg;
            this.isIgnore = isIgnore;
        }

        private String name;
        private int code;
        private String errorMsg;
        private boolean isIgnore;

        /**
         * 错误消息多语言支持
         * <p>
         * 存储对应语言的失败信息
         */
        private Map<String, String> langErrorMsg = new HashMap<>();

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getErrorMsg() {
            return errorMsg;
        }

        public boolean isIgnore() {
            return isIgnore;
        }

        public void setIgnore(boolean ignore) {
            isIgnore = ignore;
        }

        public void setErrorMsg(String errorMsg) {
            this.errorMsg = errorMsg;
        }

        public void setErrorMsg(Map<String, String> errorMsg) {
            if (errorMsg == null) {
                errorMsg = new HashMap<>();
            }
            this.langErrorMsg = errorMsg;
        }

        public Map<String, String> getAllErrorMsg() {
            return this.langErrorMsg;
        }

        /**
         * 添加多语言错误信息
         *
         * @param lang     语言码
         * @param errorMsg 错误信息
         */
        public void addErrorMsg(String lang, String errorMsg) {
            langErrorMsg.put(lang, errorMsg);
        }

        @Override
        public String toString() {
            return "FailBean{" +
                    "name='" + name + '\'' +
                    ", code=" + code +
                    ", errorMsg='" + errorMsg + '\'' +
                    ", isIgnore=" + isIgnore +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "ServiceInspectResultBean{" +
                "result=" + result +
                ", step=" + step +
                ", otaResult=" + otaResult +
                ", pass=" + pass +
                ", fail=" + fail +
                '}';
    }
}
