/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.coreservice.action;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.action.ActionCallbackProxy.Response;
import com.ainirobot.coreservice.action.ActionManager.Status;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.BaseBean;
import com.ainirobot.coreservice.config.ConfigManager;
import com.ainirobot.coreservice.core.HeadTurnManager;
import com.ainirobot.coreservice.core.LocalApi;
import com.ainirobot.coreservice.exception.SendCmdException;
import com.ainirobot.coreservice.resmanage.AssignedRes;
import com.ainirobot.robotlog.RobotLog;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

public abstract class AbstractAction<T extends BaseBean> implements LocalApi.OnCmdResponse, ICmdResponse, Cloneable {
    private static final String TAG = AbstractAction.class.getSimpleName();
    protected static final int INVALID_REQID = -1;

    protected ActionCallbackProxy mCallback;
    protected T mParam;
    protected LocalApi mApi;

    protected static Gson mGson = new Gson();
    private int mId;
    private volatile ActionManager.Status mStatus = ActionManager.Status.IDLE;
    private String[] mResList;
    private HashMap<String, StatusListenerProxy> mListeners = new HashMap<>();
    protected ActionManager mManager;
    private Type mParaType;
    private StopCommandList mStopCommandList;
    protected int mReqId = INVALID_REQID;
    private Response response;

    private int preHeadAngle = -1;
    private boolean isTopAssist = true;
    private boolean isFixHead = false;

    /**
     * 调用者
     */
    private String mCaller;

    protected AbstractAction(ActionManager manager, int id, String[] resList) {
        mManager = manager;
        mId = id;
        mParaType = ((ParameterizedType) getClass().getGenericSuperclass()).
                getActualTypeArguments()[0];
        mResList = (resList != null) ? resList.clone() : null;

        int vAngle = manager.getCoreService().getHeadMoveManager().getVAngle();
        mApi = new LocalApi(this, vAngle);
    }

    public synchronized final boolean start(String parameter, ActionCallbackProxy callback, Response response) {
        this.response = response;
        changeStatus(ActionManager.Status.INIT);
        mCallback = callback;
        mCallback.bind(this);
        boolean result = false;
        if (!TextUtils.isEmpty(parameter)) {
            mParam = parserParameter(parameter);
            if (mParam == null || !verifyParam(mParam)) {
                response.responseCode = Definition.ACTION_RESPONSE_PARAMETER_ERROR;
                response.responseString = parameter;
            } else {
                //request resource
                mReqId = ((BaseBean) mParam).getReqId();

                List<String> unavailableRes = mManager.getResManager()
                                                      .getUnavailableRes(getResList());
                if (unavailableRes == null || unavailableRes.isEmpty()) {
                    AssignedRes res = mManager.getResManager().requestRes(this, getResList());
                    if (res != null) {
                        res.setLanguage(((BaseBean) mParam).getLanguage());
                        prepare(res);
                        result = startAction(mParam, mApi);
                        if (result) {
                            changeStatus(ActionManager.Status.RUN);
                        } else {
                            changeStatus(Status.IDLE);
                            release();
                            response.responseCode = Definition.ACTION_RESPONSE_START_ERROR;
                            response.responseString = null;
                        }
                    } else {
                        response.responseCode = Definition.ACTION_RESPONSE_REQUEST_RES_ERROR;
                        response.responseString = null;
                    }
                } else {
                    response.responseCode = Definition.ACTION_RESPONSE_RES_UNAVAILBALE;
                    response.responseString = new Gson().toJson(unavailableRes);
                }
            }
        } else {
            response.responseCode = Definition.ACTION_RESPONSE_PARAMETER_ERROR;
            response.responseString = parameter;
        }

        if (!result) {
            changeStatus(ActionManager.Status.IDLE);
        } else {
            Log.e(TAG, "Action started ***************** : " + getActionId());
        }
        return result;
    }

    public ActionManager.Status getStatus() {
        return mStatus;
    }

    private void finish() {
        mApi.releaseRes();

        if (mStatus == Status.IDLE) {
            mManager.onFinish(this);
            return;
        }
        changeStatus(ActionManager.Status.IDLE);
        mParam = null;
        if (response.responseCode == Definition.ACTION_RESPONSE_SUCCESS) {
            response.responseCode = Definition.ACTION_RESPONSE_STOP_SUCCESS;
            response.responseString = "";
        }
        mManager.onFinish(this);
        mCallback.response(response);
        //mCallback = null;
        Log.e(TAG, "Action finished ***************** : " + getActionId());
    }

    private StopCommandList genStopList(boolean isCancelStopCommand) throws SendCmdException {
        if (isCancelStopCommand) {
            return null;
        }
        StopCommandList list = null;
        ArrayList<StopCommandList.StopCommand> commands = getStopCommands();
        if (commands != null && commands.size() > 0) {
            boolean sendFlag = true;
            for (StopCommandList.StopCommand item : commands) {
                int cmdId = mApi.sendCommand(mReqId, item.command, item.param);
                if (cmdId > Definition.CMD_SEND_ERROR_UNKNOWN) {
                    item.cmdId = cmdId;
                } else {
                    throw new SendCmdException("Command send failed! errorcode = " + cmdId +
                            "; command = " + item.command + "; param = " + item.param);
                }
            }
            if (sendFlag) {
                list = StopCommandList.Obtain();
                list.addCommands(commands);
            }
        }
        return list;
    }

    private boolean release() {
        boolean result;
        clearAllListener();
        result = mApi.stop();
        Log.d(TAG, "release result : " + result);

        if (isFixHead) {
            mManager.getCoreService().getHeadMoveManager().resetHead(preHeadAngle);
            isFixHead = false;
        }

        if (!isTopAssist) {
            isTopAssist = true;
            mApi.setTopAssistedEstimate(mReqId, true);
        }

        if (result) {
            finish();
        } else {
            response.responseCode = Definition.ACTION_RESPONSE_STOP_RES_ERROR;
            mCallback.response(response);
        }
        return result;
    }

    public synchronized final boolean stop(boolean isResetHW) {
        return stop(isResetHW, false);
    }

    /**
     * @param isResetHW
     * @param isCancelStopCommand 当Action stop 时，是否执行相应Action的stopList 列表中的指令.
     * @return
     */
    public synchronized final boolean stop(boolean isResetHW, boolean isCancelStopCommand) {
        if (mStatus == Status.PAUSE
                || mStatus == Status.IDLE) {
            Log.d(TAG, "Action is already stopped");
            return false;
        }

        boolean result = true;
        try {
            mStopCommandList = genStopList(isCancelStopCommand);
            if (mStopCommandList != null) {
                StopCommandList.IStopCommandChecker listener = getStopCommandChecker();
                if (listener == null) {
                    throw new SendCmdException("Don't set stopCommand listener!");
                } else {
                    mStopCommandList.setChecker(listener);
                }
            }
        } catch (SendCmdException e) {
            e.printStackTrace();
            response.responseCode = Definition.ACTION_RESPONSE_SEND_STOP_CMD_ERROR;
            response.responseString = e.getMessage();
            //onError(Definition.ACTION_RESPONSE_SEND_STOP_CMD_ERROR, e.getMessage());
            result = false;
        }
        Log.d(TAG, "before stop action :" + result + ", mStopCommandList : " + mStopCommandList);
        if (result) {
            Status preStatus = getStatus();
            changeStatus(ActionManager.Status.PAUSE);
            result = stopAction(isResetHW);
            if (result) {
                if (mStopCommandList == null) {
                    result = release();
                }
            } else {
                changeStatus(preStatus);
            }
        }
        return result;
    }

    T parserParameter(String parameter) {
        T result;

        try {
            result = mGson.fromJson(parameter, mParaType);
        } catch (JsonSyntaxException e) {
            result = null;
        }
        return result;
    }

    void reuse(int id, String[] resList) {
        mId = id;
        mResList = resList;
    }

    public void onError(int errorCode, String errorMessage) {
        this.onError(errorCode, errorMessage, "");
    }

    public void onError(int errorCode, String errorMessage, String extraData) {
        if (mStatus == Status.IDLE) {
            return;
        }
        RobotLog.e("AbstractAction error : errorCode=" + errorCode
                + " message=" + errorMessage + " action=" + getActionId() + "  status=" + mStatus);
        response.responseCode = errorCode;
        response.responseString = errorMessage;
        response.extraData = extraData;
        //mCallback.response(response);
        stop(true);
    }

    public void onResult(int result, String message) {
        this.onResult(result, message, "");
    }

    public void onResult(int result, String message, String extraData) {
        RobotLog.e("AbstractAction result : result=" + result
                + " message=" + message + " action=" + getActionId() + "  status=" + mStatus);

        if (mStatus == Status.IDLE) {
            return;
        }
        response.responseCode = result;
        response.responseString = message;
        response.extraData = extraData;
        //mCallback.response(response);
        stop(true);
    }

    public boolean onStatusUpdate(int status, String data) {
        return onStatusUpdate(status, data, false, "");
    }

    public boolean onStatusUpdate(int status, String data, boolean isAllowDiscard) {
        return onStatusUpdate(status, data, isAllowDiscard, "");
    }

    public boolean onStatusUpdate(int status, String data, String extraData) {
        return onStatusUpdate(status, data, false, extraData);
    }

    public boolean onStatusUpdate(int status, String data, boolean isAllowDiscard, String extraData) {
        if (!(this instanceof CommonAction)) {
            RobotLog.e("AbstractAction status : status=" + status
                    + " data=" + data + " action=" + getActionId() + "  status=" + mStatus);
        }

        if (mStatus == Status.IDLE
                || !mCallback.isAlive()) {
            return false;
        }
        Response response = new Response();
        response.statusCode = status;
        response.responseString = data;
        response.extraData = extraData;
        mCallback.response(response);
        return true;
    }

    public final String registerStatusListener(String type, StatusListener listener) {
        String key = null;
        if (!TextUtils.isEmpty(type) && listener != null) {
            key = StatusListenerProxy.genKey(type, listener);
            if (!mListeners.containsKey(key)) {
                StatusListenerProxy proxy = new StatusListenerProxy(type, listener);
                mListeners.put(key, proxy);
                mManager.registerStatusListener(type, proxy);
            }
        }
        return key;
    }

    public void unregisterStatusListener(String key) {
        StatusListenerProxy proxy = mListeners.remove(key);
        if (proxy != null) {
            mManager.unregisterStatusListener(proxy.getId());
        }
    }

    private void clearAllListener() {
        for (StatusListenerProxy proxy : mListeners.values()) {
            mManager.unregisterStatusListener(proxy.getId());
        }
        mListeners.clear();
    }

    protected final void sendStatus(String status, String data) {
        mManager.handleStatus(status, data);
    }

    @Override
    public synchronized final void onCmdResponse(int cmdId, String command, String result, String extraData) {
        if (!Definition.CMD_HEAD_GET_ALL_PERSON_INFOS.equals(command)) {
            Log.d(TAG, "Command response : " + command + "  result : " + result + " status : " + mStatus);
        }
        boolean consumed = false;
        if (mStopCommandList != null) {
            consumed = mStopCommandList.cmdResponse(cmdId, command, result, extraData);
            if (consumed && mStopCommandList.complete()) {
                Iterator<String> iter = mStopCommandList.getFailedIterator();
                if (iter != null) {
                    StringBuilder error = new StringBuilder();
                    error.append('[');
                    while (iter.hasNext()) {
                        error.append(iter.next());
                        error.append(',');
                    }
                    error.append(']');

                    response.responseCode = Definition.ACTION_RESPONSE_SEND_STOP_CMD_ERROR;
                    response.responseString = error.toString();
                    //onError(Definition.ACTION_RESPONSE_SEND_STOP_CMD_ERROR, error.toString());
                }
                release();
                mStopCommandList.recycle();
                mStopCommandList = null;
            }
        }

        if (!consumed
                && mStatus != Status.IDLE
                && mStatus != Status.PAUSE) {
            if (mParam == null) {
                Log.d(TAG, "Action param exception, actionId : " + getActionId() + "  status : " + mStatus);
            }
            cmdResponse(cmdId, command, result, extraData);
        }
    }

    @Override
    public final void onCmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        Log.d(TAG, "Command status : " + command + "  status : " + status + " actionStatus : " + mStatus);
        if (mStatus == Status.IDLE) {
            return;
        }
        cmdStatusUpdate(cmdId, command, status, extraData);
    }

    public boolean cmdStatusUpdate(int cmdId, String command, String status, String extraData) {
        return true;
    }

    public int getActionId() {
        return mId;
    }

    public boolean isRunning() {
        return mStatus == Status.RUN;
    }

    boolean changeStatus(ActionManager.Status status) {
        Log.d(TAG, "Change status : " + status + " actionId:" + getActionId());
        if (mStatus != status) {
            mStatus = status;
            mManager.notifyActionStatusChange(this, status);
        }
        return true;
    }

    protected abstract boolean startAction(T parameter, LocalApi api);

    protected abstract boolean stopAction(boolean isResetHW);

    protected abstract ArrayList<StopCommandList.StopCommand> getStopCommands();

    protected abstract StopCommandList.IStopCommandChecker getStopCommandChecker();

    protected abstract boolean verifyParam(T param);

    @Override
    public String toString() {
        return "AbstractAction{"
                + "mCallback="
                + mCallback
                + ", mParam="
                + mParam
                + ", mApi="
                + mApi
                + ", mId="
                + mId
                + ", mStatus="
                + mStatus
                + ", mListeners="
                + mListeners
                + ", mManager="
                + mManager
                + ", mParaType="
                + mParaType
                + ", mStopCommandList="
                + mStopCommandList
                + ", mReqId="
                + mReqId
                + ", response="
                + response
                + '}';
    }

    public String getCaller() {
        return mCaller;
    }

    public void setCaller(String mCaller) {
        this.mCaller = mCaller;
    }

    private String[] getResList() {
        return mResList != null ? mResList.clone() : null;
    }

    @Override
    protected Object clone() {
        AbstractAction<T> bean = null;
        try {
            bean = (AbstractAction<T>) super.clone();
//            synchronized (this) {
//                // 深拷贝数组
//                bean.mResList = mResList != null ? mResList.clone() : null;
//            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return bean;
    }

    public void prepare(AssignedRes res) {
        mApi.prepare(res);
        onPrepare(res);
    }

    public void onPrepare(AssignedRes res) {
        //需要导航资源且存在TopIP的机型，需要将头部归正到最佳位置
        if (res.hasNavigation()) {
            //1. Action执行过程中，Action明确自己控制头部，则不主动帮助归正,
            //2. 如果接口中明确不需要辅助定位，则不需要归正
            if (!res.hasHead() && mParam.isNeedAssistEstimate()) {
                HeadTurnManager headManager = mManager.getCoreService().getHeadMoveManager();
                preHeadAngle = headManager.getVAngle();
                headManager.fixHead(ConfigManager.getHeadConfig().getDefaultVerticalAngle());
                isFixHead = true;
            } else {
                isTopAssist = false;
                mApi.setTopAssistedEstimate(mReqId, false);
            }
        }
    }
}
