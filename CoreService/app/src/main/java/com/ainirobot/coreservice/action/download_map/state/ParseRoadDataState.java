package com.ainirobot.coreservice.action.download_map.state;

import android.text.TextUtils;

import com.ainirobot.coreservice.action.download_map.DownloadMapStateEnum;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.actionbean.DownloadMapBean;

import org.json.JSONObject;

public class ParseRoadDataState extends BaseDownloadMapPkgState {

    @Override
    protected void onStart(String paramsStr, DownloadMapBean downloadMapBean) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(Definition.JSON_MAP_NAME, downloadMapBean.getMapName());
            jsonObject.put(Definition.JSON_DATA_TYPE, Definition.JSON_DATA_ROAD);
            sendCommand(0, Definition.CMD_NAVI_PARSE_MAP_DATA, jsonObject.toString());
        } catch (Exception e) {
            e.printStackTrace();
            onStateRunFailed(0, "parse road json parse is failed");
        }
    }

    @Override
    public boolean onCmdResponse(int cmdId, String command, String result, String extraData) {
        if (TextUtils.equals(command, Definition.CMD_NAVI_PARSE_MAP_DATA)) {
            if (TextUtils.equals(Definition.SUCCEED, result)) {
                onStateRunSuccess();
            } else {
                onStateRunFailed(0, "parse road is failed");
            }
            return true;
        }
        return false;
    }

    @Override
    protected DownloadMapStateEnum getNextState() {
        return DownloadMapStateEnum.GET_MAP_POS_SEPARATE;
    }
}
