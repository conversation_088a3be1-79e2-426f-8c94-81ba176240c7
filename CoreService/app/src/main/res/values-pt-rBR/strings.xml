<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">CoreService</string>
    <!-- Reposition -->
    <string name="reposition_noset_charge_position">O local de carregamento ainda não foi definido</string>
    <string name="reposition_state_ok">O estado de posicionamento é normal. Não há necessidade de reposicionar novamente.</string>
    <string name="need_estimate">Por favor, reposicione primeiro</string>
    <string name="need_charge_pile">Defina primeiro o pólo de carregamento</string>
    <string name="current_in">%1$s processando, %2$s não está disponível agora</string>
    <string name="first_config">Configurações iniciais</string>
    <string name="situ_service_opened">Desculpe, eu só posso oferecer</string>
    <!-- OTA -->
    <string name="ota_choose_describe">Selecione o pacote de instalação manualmente</string>
    <string name="ota_wait_update">A bateria está fraca, quando o nível da bateria estiver acima de %1$s, o robô será atualizado automaticamente</string>
    <string name="reposition_please_switch_map">Por favor, troque de mapa primeiro</string>
    <!--Inspect-->
    <string name="inspect_no_response">Sem resposta</string>
    <string name="inspect_time_out">Solicitação expirada: Auto-inspeção \"</string>
    <!--注释:系统接管名称,仅用于tts播报，后续新增的接管状态，也需要有对应的tts翻译　-->
    <string name="shutdown_tts">desligar</string>
    <string name="inspection_tts">inspeção</string>
    <string name="system_recovery_tts">recuperação do sistema</string>
    <string name="ota_tts">atualizar</string>
    <string name="standby_tts">espera</string>
    <string name="d430_calibration_tts">calibração D430</string>
    <string name="shutdown_timer_tts">desligamento programado</string>
    <string name="dormancy_tts">dormir</string>
    <string name="remote_bind_tts">vincular</string>
    <string name="lock_tts">bloqueado</string>
    <string name="set_charge_pile_tts">definir pólo de carga</string>
    <string name="emergency_tts">emergência</string>
    <string name="hardware_malfunction_tts">mau funcionamento de hardware</string>
    <string name="start_charge_tts">vá cobrar</string>
    <string name="reposition_tts">Reposição</string>
    <string name="remote_reposition_tts">reposicionamento remoto</string>
    <string name="map_drift_tts">deriva do mapa</string>
    <string name="map_outside_tts">fora do mapa</string>
    <string name="charging_tts">carregando</string>
    <string name="open_radar_tts">lidar aberto</string>
    <string name="time_warning_tts">aviso de tempo</string>
    <string name="remote_task_tts">tarefa remota</string>
    <string name="being_pushed_tts">empurrado</string>
    <string name="ble_status_tts">sinal bluetooth</string>
    <string name="wheel_over_status_tts">Status de proteção contra sobrecorrente</string>
    <string name="multi_robot_error_tts">erro multi_robô</string>
    <string name="system_setting_tts">configuração de sistema</string>
    <string name="remote_stop_charging_tts">parar de carregar</string>
    <string name="navi_sensor_exception_tts">Status do sensor de navegação</string>
    <string name="e70_exception_tts">erro E70</string>
    <string name="close_electric_door_please">Por favor, feche a porta primeiro</string>
    <string name="stop_charge_confirm_tts">Finalize o carregamento e confirme novamente</string>
</resources>
