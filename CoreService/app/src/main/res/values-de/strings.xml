<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">CoreService</string>
    <!-- Reposition -->
    <string name="reposition_noset_charge_position">Ladeplatz wurde noch nicht gesetzt</string>
    <string name="reposition_state_ok">Die Positionierung ist normal. Keine Neupositionierung erforderlich.</string>
    <string name="need_estimate">Bitte zuerst neu positionieren</string>
    <string name="need_charge_pile">Bitte legen Sie zuerst die Ladesäule fest</string>
    <string name="current_in">%1$s wird verarbeitet, %2$s ist jetzt nicht verfügbar</string>
    <string name="first_config">Initiale Einstellungen</string>
    <string name="situ_service_opened">Sorry, ich kann nur anbieten</string>
    <!-- OTA -->
    <string name="ota_choose_describe">Bitte wählen Sie das Installationspaket manuell aus</string>
    <string name="ota_wait_update">Bat<PERSON>ie ist niedrig, wenn der Akkustand über %1$s ist, wird der Roboter automatisch upgraden</string>
    <string name="reposition_please_switch_map">Bitte zuerst die Karte wechseln</string>
    <!--Inspect-->
    <string name="inspect_no_response">Keine Antwort</string>
    <string name="inspect_time_out">Zeitüberschreitung der Anfrage: Selbstüberprüfung\"</string>
    <!--注释:系统接管名称,仅用于tts播报，后续新增的接管状态，也需要有对应的tts翻译　-->
    <string name="shutdown_tts">abschalten</string>
    <string name="inspection_tts">Inspektion</string>
    <string name="system_recovery_tts">Systemwiederherstellung</string>
    <string name="ota_tts">aktualisieren</string>
    <string name="standby_tts">standby</string>
    <string name="d430_calibration_tts">D430-Kalibrierung</string>
    <string name="shutdown_timer_tts">geplante Abschaltung</string>
    <string name="dormancy_tts">schlafen</string>
    <string name="remote_bind_tts">binden</string>
    <string name="lock_tts">gesperrt</string>
    <string name="set_charge_pile_tts">Ladepol setzen</string>
    <string name="emergency_tts">Notfall</string>
    <string name="hardware_malfunction_tts">Hardware-Fehler</string>
    <string name="start_charge_tts">aufladen gehen</string>
    <string name="reposition_tts">umstellen</string>
    <string name="remote_reposition_tts">entfernte Neupositionierung</string>
    <string name="map_drift_tts">Kartendrift</string>
    <string name="map_outside_tts">außerhalb der Karte</string>
    <string name="charging_tts">aufladen</string>
    <string name="open_radar_tts">Lidar öffnen</string>
    <string name="time_warning_tts">Zeitwarnung</string>
    <string name="remote_task_tts">entfernte Aufgabe</string>
    <string name="being_pushed_tts">geschoben</string>
    <string name="ble_status_tts">Bluetooth-Signal</string>
    <string name="wheel_over_status_tts">Überstromschutzstatus</string>
    <string name="multi_robot_error_tts">multi_robot-Fehler</string>
    <string name="system_setting_tts">Systemeinstellung</string>
    <string name="remote_stop_charging_tts">aufhören zu laden</string>
    <string name="navi_sensor_exception_tts">Status des Navigationssensors</string>
    <string name="e70_exception_tts">E70-Fehler</string>
    <string name="close_electric_door_please">Bitte schließen Sie zuerst die Tür</string>
    <string name="stop_charge_confirm_tts">Ladevorgang beenden und nochmals bestätigen</string>
</resources>
