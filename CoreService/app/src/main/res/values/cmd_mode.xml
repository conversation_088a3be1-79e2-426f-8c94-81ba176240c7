<!--
  * Copyright (C) 2017 OrionStar Technology Project
  *
  * Licensed under the Apache License, Version 2.0 (the "License");
  * you may not use this file except in compliance with the License.
  * You may obtain a copy of the License at
  *
  *      http://www.apache.org/licenses/LICENSE-2.0
  *
  * Unless required by applicable law or agreed to in writing, software
  * distributed under the License is distributed on an "AS IS" BASIS,
  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  * See the License for the specific language governing permissions and
  * limitations under the License.
  -->

<resources>
    <string-array name="cmd_type">
        <item>"cmd_get_status"</item>
        <item>"cmd_navi_start_update"</item>;
        <item>"cmd_navi_get_version"</item>;
        <item>"cmd_navi_go_location"</item>
        <item>"cmd_navi_is_in_navigation"</item>
        <item>"cmd_navi_set_location"</item>
        <item>"cmd_navi_remove_location"</item>
        <item>"cmd_navi_get_location"</item>
        <item>"cmd_navi_is_in_location"</item>
        <item>"cmd_navi_move_direction"</item>
        <item>"cmd_navi_move_direction_angle"</item>
        <item>"cmd_navi_move_distance_angle"</item>
        <item>"cmd_navi_stop_move"</item>
        <item>"cmd_navi_stop_navigation"</item>
        <item>"cmd_navi_set_navigation_speed"</item>
        <item>"cmd_navi_change_navigation_speed"</item>
        <item>"cmd_navi_start_velocity_report"</item>
        <item>"cmd_navi_stop_velocity_report"</item>
        <item>"cmd_navi_set_max_acceleration"</item>
        <item>"cmd_navi_report_start_avoid_obstacle"</item>
        <item>"cmd_navi_report_end_avoid_obstacle"</item>
        <item>"cmd_navi_get_navigation_angle_speed"</item>
        <item>"cmd_navi_get_navigation_line_speed"</item>
        <item>"cmd_navi_report_position"</item>
        <item>"cmd_navi_report_navigation_velocity_change"</item>
        <item>"cmd_navi_cruiselayout_start"</item>
        <item>"cmd_navi_cruiselayout_stop"</item>
        <item>"cmd_navi_cruise_start"</item>
        <item>"cmd_navi_cruise_stop"</item>
        <item>"cmd_navi_relocation"</item>

        <item>"cmd_navi_set_pose_location"</item>
        <item>"cmd_navi_set_pose_estimate"</item>
        <item>"cmd_navi_start_creating_map"</item>
        <item>"cmd_navi_stop_creating_map"</item>
        <item>"cmd_navi_switch_map"</item>
        <item>"cmd_navi_remove_map"</item>
        <item>"cmd_navi_get_place_name"</item>
        <item>"cmd_navi_get_place_list"</item>
        <item>"cmd_navi_get_place_list_with_name"</item>
        <item>"cmd_navi_get_placelist_with_namelist"</item>
        <item>"cmd_navi_go_position"</item>
        <item>"cmd_navi_is_estimate"</item>
        <item>"cmd_navi_save_estimate"</item>
        <item>"cmd_navi_go_default_theta"</item>

        <item>"cmd_head_register"</item>
        <item>"cmd_head_unregister"</item>
        <item>"cmd_head_move_head"</item>
        <item>"cmd_head_switch_camera"</item>
        <item>"cmd_head_set_track_target"</item>
        <item>"cmd_head_get_all_person_infos"</item>
        <item>"cmd_head_get_person_info_by_name"</item>
        <item>"cmd_head_search_person_by_name"</item>
        <item>"cmd_head_get_picture_by_id"</item>
        <item>"cmd_report_navigation_status"</item>
        <item>"cmd_head_get_last_position"</item>
        <item>"cmd_head_is_header_connected"</item>
        <item>"cmd_switch_detect_algorithm"</item>

        <item>"cmd_head_video_record"</item>
        <item>"cmd_head_testcase"</item>
        <item>cmd_head_register_by_pic</item>
        <item>cmd_head_start_update</item>
        <item>cmd_head_get_version</item>
        <item>cmd_head_get_update_params</item>
        <item>cmd_head_get_gesture_infos</item>
        <item>cmd_head_stop_gesture_infos</item>
        <item>cmd_head_get_movement</item>
        <item>cmd_head_get_all_face_pos</item>
        <item>cmd_head_stop_all_face_pos</item>
        <item>cmd_head_get_status</item>
        <item>cmd_head_restart</item>
        <item>cmd_head_get_camera_status</item>

        <item>cmd_head_get_depth_status</item>
        <item>cmd_head_get_fov_status</item>
    </string-array>

    <integer-array name="cmd_is_async">
        <item>1</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>

        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>

        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>

        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
    </integer-array>
    <integer-array name="cmd_function">
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>

        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>

        <item>1</item>
        <item>1</item>
        <item>1</item>
        <item>1</item>

        <item>2</item>
        <item>2</item>
        <item>2</item>
        <item>2</item>
        <item>2</item>
        <item>2</item>
        <item>2</item>
        <item>2</item>
        <item>2</item>
        <item>2</item>
        <item>2</item>
        <item>2</item>
        <item>2</item>

        <item>2</item>
        <item>2</item>
        <item>2</item>
        <item>2</item>
        <item>2</item>
        <item>2</item>
        <item>2</item>
        <item>2</item>
        <item>2</item>
        <item>2</item>
        <item>2</item>
        <item>2</item>
        <item>2</item>
        <item>2</item>
    </integer-array>
</resources>
