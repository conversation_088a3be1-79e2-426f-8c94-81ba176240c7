<resources>
    <string name="app_name">CoreService</string>
    <!-- Reposition -->
    <string name="reposition_noset_charge_position">Charging spot has not been set yet</string>
    <string name="reposition_state_ok">Positioning state is normal. No need to reposition again.</string>
    <string name="need_estimate">Please reposition first</string>
    <string name="need_charge_pile">Please set charging pole first</string>
    <string name="current_in">%1$s processing, %2$s is not available now</string>
    <string name="first_config">Initial Settings</string>
    <string name="situ_service_opened">Sorry, I can only offer</string>
    <!-- OTA -->
    <string name="ota_choose_describe">Please select installation package manually</string>
    <string name="ota_wait_update">Battery is low, When battery level is over %1$s, the robot will upgrade automatically</string>
    <string name="reposition_please_switch_map">Please switch map first</string>
    <!--Inspect-->
    <string name="inspect_no_response">No Response</string>
    <string name="inspect_time_out">Request Timed Out: Self Inspection"</string>
    <!--注释:系统接管名称,仅用于tts播报，后续新增的接管状态，也需要有对应的tts翻译　-->
    <string name="shutdown_tts">shutdown</string>
    <string name="inspection_tts">inspection</string>
    <string name="system_recovery_tts">system recovery</string>
    <string name="ota_tts">update</string>
    <string name="standby_tts">standby</string>
    <string name="d430_calibration_tts">D430 calibration</string>
    <string name="shutdown_timer_tts">scheduled shutdown</string>
    <string name="dormancy_tts">sleep</string>
    <string name="remote_bind_tts">bind</string>
    <string name="lock_tts">locked</string>
    <string name="set_charge_pile_tts">set charge pole</string>
    <string name="emergency_tts">emergency</string>
    <string name="hardware_malfunction_tts">hardware malfunction</string>
    <string name="start_charge_tts">go to charge</string>
    <string name="reposition_tts">reposition</string>
    <string name="remote_reposition_tts">remote reposition</string>
    <string name="map_drift_tts">map drift</string>
    <string name="map_outside_tts">out of map</string>
    <string name="charging_tts">charging</string>
    <string name="open_radar_tts">open lidar</string>
    <string name="time_warning_tts">time warning</string>
    <string name="remote_task_tts">remote task</string>
    <string name="being_pushed_tts">pushed</string>
    <string name="ble_status_tts">bluetooth signal</string>
    <string name="wheel_over_status_tts">Overcurrent Protection Status</string>
    <string name="multi_robot_error_tts">multi_robot error</string>
    <string name="system_setting_tts">system setting</string>
    <string name="remote_stop_charging_tts">stop charging</string>
    <string name="navi_sensor_exception_tts">Navigation sensor status</string>
    <string name="e70_exception_tts">E70 error</string>
    <string name="close_electric_door_please">Please close the the door first</string>
    <string name="stop_charge_confirm_tts">End charging and confirm again</string>
    <string name="downgrade_ota_tts">downgrade ota</string>
    <string name="leave_pile_go_point_tts">离桩去起始点</string>
</resources>
