<?xml version="1.0" encoding="utf-8"?><!--
  ~ * Copyright (C) 2017 OrionStar Technology Project
  ~ *
  ~ * Licensed under the Apache License, Version 2.0 (the "License");
  ~ * you may not use this file except in compliance with the License.
  ~ * You may obtain a copy of the License at
  ~ *
  ~ *      http://www.apache.org/licenses/LICENSE-2.0
  ~ *
  ~ * Unless required by applicable law or agreed to in writing, software
  ~ * distributed under the License is distributed on an "AS IS" BASIS,
  ~ * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ * See the License for the specific language governing permissions and
  ~ * limitations under the License.
  -->

<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/white"
            android:paddingBottom="3dp"
            android:paddingLeft="3dp"
            android:paddingRight="3dp"
            android:paddingTop="3dp">

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:orientation="vertical">

        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:onClick="test"
            android:text="初始化"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:onClick="focusFollow"
                android:text="开启焦点跟随"/>

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:onClick="stopFocusFollow"
                android:text="停止焦点跟随"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:onClick="turnLeft"
                android:text="左转60度"/>

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:onClick="turnRight"
                android:text="右转60度"/>

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:onClick="forward"
                android:text="前进1米"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="register"
                android:text="开始注册"/>

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="lead"
                android:text="引领"/>

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="stopLead"
                android:text="停止引领"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

        <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="turnHeadLeft90"
                android:text="左转头90"/>

        <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="turnHeadRight90"
                android:text="右转头90"/>

        <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="stopTurnHead"
                android:text="停止转头"/>

    </LinearLayout>

        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

            <Button
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:onClick="turnHeadUp45"
                    android:text="抬头45"/>

            <Button
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:onClick="turnHeadDown90"
                    android:text="低头90"/>

        </LinearLayout>

        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

            <Button
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:onClick="startInspection"
                    android:text="启动自检"/>

            <Button
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:onClick="stopInspection"
                    android:text="停止自检"/>

            <Button
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:onClick="testNavigation"
                    android:text="导航测试"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="batteryLow"
                android:text="低电量"/>

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="batteryCharging"
                android:text="充电中"/>

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="batteryFull"
                android:text="已充满"/>

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="batteryNormal"
                android:text="停止充电"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="startOta"
                android:text="AutoOta"/>

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="startSettingOta"
                android:text="Ota"/>

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="disableAUtoOta"
                android:text="关闭Ota"/>

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="enableAUtoOta"
                android:text="打开Ota"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="disableEmergency"
                android:text="关闭急停"/>

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="enableEmergency"
                android:text="打开急停"/>

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="disableLow"
                android:text="关闭回充"/>

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="enableLow"
                android:text="打开回充"/>

        </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="inChargingPile"
            android:text="在桩action" />
    </LinearLayout>

        <!--<Button-->
        <!--android:id="@+id/registerModule"-->
        <!--android:layout_width="wrap_content"-->
        <!--android:layout_height="wrap_content"-->
        <!--android:layout_gravity="center_horizontal"-->
        <!--android:onClick="registerModule"-->
        <!--android:text="Register Module" />-->

    </LinearLayout>

</ScrollView>
