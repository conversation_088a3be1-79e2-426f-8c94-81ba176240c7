import java.text.SimpleDateFormat

/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
apply plugin: 'com.android.application'

def projectName = project.hasProperty('projectName') ? projectName : 'mini2_tob'
def configDirectory = '../../../RobotConfig/'
def configFilePath = configDirectory + projectName

def hasConfig = file(configFilePath).exists()

android {

    compileSdkVersion 25
    buildToolsVersion "25.0.3"
    defaultConfig {
        applicationId "com.ainirobot.coreservice"
        minSdkVersion 22
        targetSdkVersion 22
        versionCode 15000
        versionName "1.50.0"
        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {
        signConfig {
            keyAlias 'platform'
            keyPassword 'android'
            storeFile file('debug.keystore')
            storePassword 'android'
        }

        signConfig_845 {
            keyAlias 'AiniBox'
            keyPassword 'AiniRobot@9102'
            storeFile file('platform.keystore')
            storePassword 'AiniRobot@9102'
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.signConfig_845
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            signingConfig signingConfigs.signConfig_845
            debuggable true
        }

        release_821 {
            signingConfig signingConfigs.signConfig
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        debug_821 {
            signingConfig signingConfigs.signConfig
            debuggable true
        }
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }

    aaptOptions {
        if (hasConfig) {
            additionalParameters "--auto-add-overlay"
            additionalParameters "--extra-packages", configFilePath
            noCompress 'foo', 'bar'
            ignoreAssetsPattern '!.svn:!.git:!.ds_store:!*.scc:.*:<dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~'
        }
    }

    android.sourceSets {
        if (hasConfig) {
            main.res.srcDirs = ['src/main/res', configFilePath]
        } else {
            main.res.srcDirs = ['src/main/res', 'src/main/config']
        }
    }

    if (hasConfig) {
        println('has config, file path:' + configFilePath)
    } else {
        println('config not found, use local config')
    }
}

configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}

dependencies {
    compile fileTree(dir: 'libs', include: ['*.jar'])
    compile 'com.android.support:appcompat-v7:25.3.1'
    testCompile 'junit:junit:4.12'
    compile 'com.google.code.gson:gson:2.8.5'
    debugCompile 'com.android.support.test:runner:1.0.2'
    compile('com.cmcm.launcher:OrionBaseSDK:2.0.7.18-i-SNAPSHOT') {
        changing true
    }
}

tasks.lint.onlyIf {
    // Replace this condition with your desired condition
    false
}

task buildJar(dependsOn: ['build'], type: Jar) {
    manifest {
        SimpleDateFormat formatter = new SimpleDateFormat("YYYYMMddhh");
        Date curDate = new Date(System.currentTimeMillis());
        String robotOSVersion = "5.2." + formatter.format(curDate);

        attributes 'RobotOS-Version': robotOSVersion
    }
    archiveName = 'robotservice.jar'

    def higherPath = "build/intermediates/javac/debug/classes"
    def lowerPath = 'build/intermediates/classes/debug'
    from(higherPath)
    from(lowerPath)
    from { configurations.compile.collect { it.isDirectory() ? it : zipTree(it) } }

    destinationDir = file('build/libs')
    include('com/ainirobot/coreservice/config/ServiceConfig.class')
    include('com/ainirobot/coreservice/config/ServiceConfig\$*.class')
    include('com/ainirobot/coreservice/config/SceneEntity.class')
    include('com/ainirobot/coreservice/config/SceneEntity\$*.class')
    include('com/ainirobot/coreservice/bean/*.class')
    include('com/ainirobot/coreservice/client/*.class')
    include('com/ainirobot/coreservice/client/*/*.class')
    include('com/ainirobot/coreservice/client/*/*/*.class')
    include('com/ainirobot/coreservice/listener/*.class')
    include('com/ainirobot/coreservice/data/provider/*.class')
    include('com/ainirobot/coreservice/utils/DelayTimer.class')
    include('com/ainirobot/coreservice/utils/DelayTimer\$*.class')
    include('com/ainirobot/coreservice/utils/DelayTask.class')
    include('com/ainirobot/coreservice/utils/DelayTask\$*.class')
    include('com/ainirobot/coreservice/utils/ResUtls.class')
    include('com/ainirobot/coreservice/utils/ResUtls\$*.class')
    include('com/ainirobot/coreservice/utils/Utils.class')
    include('com/ainirobot/coreservice/utils/Utils\$*.class')
    include('com/ainirobot/coreservice/utils/IOUtils.class')
    include('com/ainirobot/coreservice/utils/IOUtils\$*.class')
    include('com/ainirobot/coreservice/utils/FileUtils.class')
    include('com/ainirobot/coreservice/utils/FileUtils\$*.class')
    include('com/ainirobot/coreservice/utils/SettingDataHelper.class')
    include('com/ainirobot/coreservice/utils/SettingDataHelper\$*.class')
    include('com/ainirobot/coreservice/*.class')
    exclude('com/ainirobot/coreservice/BuildConfig.class')
    exclude('com/ainirobot/coreservice/R.class')
    exclude('com/ainirobot/coreservice/R\$*.class')
    exclude('com/ainirobot/coreservice/ApplicationWrapper.class')
    exclude('com/ainirobot/coreservice/client/SystemApi*.class')
    exclude('com/ainirobot/coreservice/ISystemApi*.class')
    include('com/ainirobot/coreservice/utils/ZipUtils.class')

    include('com/ainirobot/base/*.class')
    include('com/ainirobot/base/*/*.class')
    include('com/ainirobot/base/*/*/*.class')
    include('com/ainirobot/base/*/*/*/*.class')
    include('com/ainirobot/base/*/*/*/*/*.class')
    exclude('com/ainirobot/base/BuildConfig.class')
    exclude('com/ainirobot/base/R.class')
    exclude('com/ainirobot/base/R\$*.class')
    exclude('com/ainirobot/base/test')
}

task buildSystemJar(dependsOn: ['build'], type: Jar) {
    archiveName = 'robotsystem.jar'
    destinationDir = file('build/libs')

    def higherPath = "build/intermediates/javac/debug/classes"
    def lowerPath = 'build/intermediates/classes/debug'
    from(higherPath)
    from(lowerPath)
    from { configurations.compile.collect { it.isDirectory() ? it : zipTree(it) } }
    include('com/ainirobot/coreservice/config/ServiceConfig.class')
    include('com/ainirobot/coreservice/config/ServiceConfig\$*.class')
    include('com/ainirobot/coreservice/config/SceneEntity.class')
    include('com/ainirobot/coreservice/config/SceneEntity\$*.class')
    include('com/ainirobot/coreservice/bean/*.class')
    include('com/ainirobot/coreservice/client/*.class')
    include('com/ainirobot/coreservice/client/*/*.class')
    include('com/ainirobot/coreservice/client/*/*/*.class')
    include('com/ainirobot/coreservice/listener/*.class')
    include('com/ainirobot/coreservice/utils/DelayTimer.class')
    include('com/ainirobot/coreservice/utils/DelayTimer\$*.class')
    include('com/ainirobot/coreservice/utils/DelayTask.class')
    include('com/ainirobot/coreservice/utils/DelayTask\$*.class')
    include('com/ainirobot/coreservice/utils/ResUtils.class')
    include('com/ainirobot/coreservice/utils/ResUtils\$*.class')
    include('com/ainirobot/coreservice/utils/IOUtils.class')
    include('com/ainirobot/coreservice/utils/IOUtils\$*.class')
    include('com/ainirobot/coreservice/utils/FileUtils.class')
    include('com/ainirobot/coreservice/utils/FileUtils\$*.class')
    include('com/ainirobot/coreservice/utils/Utils.class')
    include('com/ainirobot/coreservice/utils/Utils\$*.class')
    include('com/ainirobot/coreservice/*.class')
    exclude('com/ainirobot/coreservice/BuildConfig.class')
    exclude('com/ainirobot/coreservice/R.class')
    exclude('com/ainirobot/coreservice/R\$*.class')
    exclude('com/ainirobot/coreservice/ApplicationWrapper.class')
    exclude('com/ainirobot/coreservice/client/RobotApi*.class')
    include('com/ainirobot/coreservice/data/provider/*.class')
    include('com/ainirobot/coreservice/utils/ZipUtils.class')

    include('com/ainirobot/base/*.class')
    include('com/ainirobot/base/*/*.class')
    include('com/ainirobot/base/*/*/*.class')
    include('com/ainirobot/base/*/*/*/*.class')
    include('com/ainirobot/base/*/*/*/*/*.class')
    exclude('com/ainirobot/base/BuildConfig.class')
    exclude('com/ainirobot/base/R.class')
    exclude('com/ainirobot/base/R\$*.class')
    exclude('com/ainirobot/base/test')

    include('com/ainirobot/coreservice/config/bean/*.class')
}

task copyHooks(type: Copy) {
    from("../../hooks") {
        include "**"
    }
    into "../../.git/hooks"
}

task setCommitTemplate() {
//    def setCommitTemplate = "git config commit.template ./.git/hooks/commitTemplate".execute()
//    setCommitTemplate.waitFor()
}

preBuild.dependsOn setCommitTemplate
preBuild.dependsOn copyHooks
