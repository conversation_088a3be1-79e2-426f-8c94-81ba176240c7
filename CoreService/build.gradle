// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    repositories {
        jcenter()
        google()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:4.2.2'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        jcenter()
        google()
        maven {
            credentials {
                username 'launcher_maven'
                password 'kingsoft'
            }
            url "http://nexus.ainirobot.com:8081/nexus/content/repositories/launchersdksnapshot"
            allowInsecureProtocol true
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
