{
    "项目名称":"meissa",
    "版本":"1.0.0",
    "服务配置": {
        "语音服务": {
            "是否启用": true,
            "service": "com.ainirobot.speechasrservice",
            "配置项": {
                "语言": "普通话",
                "TTS配置": "混合",
                "客户端ID": "orion.ovs.client.1514259512471",
                "正式语音服务地址": {
                    "获取Token": "https://robot-passport.ainirobot.com:8866/",
                    "刷新Token": "https://robot-passport.ainirobot.com:8888/",
                    "ASR": "https://speech-bxm.ainirobot.com:443/",
                    "QueryByText": "https://bxm-asr.ainirobot.com:8004/",
                    "TTS": "https://bxm-asr.ainirobot.com:8004/",
                    "心跳": "https://speech-bxm.ainirobot.com"
                },
                "测试语音服务地址": {
                    "获取Token": "https://passporttest.ainirobot.com:8866/",
                    "刷新Token": "http://passporttest.ainirobot.com:8888/",
                    "ASR": "https://speech-test.ainirobot.com:443/",
                    "QueryByText": "http://test.xiaobao.ainirobot.com:8003/",
                    "TTS": "http://test.xiaobao.ainirobot.com:8003/",
                    "心跳": "https://speech-test.ainirobot.com"
                },
                "灰度语音服务地址": {
                    "获取Token": "https://robot-passport.ainirobot.com:8866/",
                    "刷新Token": "https://robot-passport.ainirobot.com:8888/",
                    "ASR": "https://speech-bxm-pre.ainirobot.com:443/",
                    "QueryByText": "http://asr.ovspre.ainirobot.com:8003/",
                    "TTS": "http://asr.ovspre.ainirobot.com:8003/",
                    "心跳": "https://speech-bxm-pre.ainirobot.com"
                }
            }
        },
        "导航服务":{
            "是否启用":true,
            "service":"com.ainirobot.navigationservice",
            "配置项":{
                "底盘":"client_yuefan"
            },
            "自检项":{
                "激光雷达":true,
                "里程计":true,
                "红外相机":true,
                "后目相机":true,
                "MINIS相机":true,
                "MINI相机":true,
                "超声波":true
            }
        },
        "视觉服务":{
            "是否启用":true,
            "service":"com.ainirobot.headservice",
            "配置项":{
                "算法方案": "vision_sdk",
                "人脸识别": true,
                "人脸注册": true,
                "人脸检测": true,
                "客流统计": false
            },
            "自检项":{
                "前置深度摄像头":false,
                "后置深度摄像头":false,
                "前置广角摄像头":false,
                "后置广角摄像头":false
            }
        },
        "远程服务":{
            "是否启用":true,
            "service":"com.ainirobot.remotecontrolservice",
            "配置项":{
                "服务地址": {
                    "业务": "https://jiedai.ainirobot.com",
                    "维修": "https://jiedai.ainirobot.com",
                    "大数据": "https://api.orionbase.cn",
                    "时间": "http://speech-bxm.ainirobot.com/timestamp"
                },
                "扫码绑定": true,
                "远程控制": true,
                "时钟同步": true,
                "无线网络": false
            },
            "自检项":{
                "远程服务连接":false
            }
        },
        "升级服务":{
            "是否启用":false,
            "service":"com.ainirobot.ota",
            "配置项":{
                "开机升级":true,
                "强制升级":true,
                "夜间升级":true,
                "静默升级": {
                    "是否启用":true,
                    "下载后直接升级":true,
                    "下载后手动升级": false
                },
                "PSB升级":true,
                "AC_CLIENT升级":true,
                "BMS升级":true,
                "MOTOR_HORIZON升级":true,
                "MOTOR_VERTICAL升级":true,
                "MOTOR_LEFT升级":true,
                "MOTOR_RIGHT升级":true,
                "TK1升级":true,
                "TX1升级":true,
                "server_domain":"https://jiedai.ainirobot.com",
                "develop_server_domain":"https://test-jiedai.ainirobot.com",
                "prerelease_server_domain":"https://jiedai.ainirobot.com"
	    }
        },
        "外设管理":{
            "是否启用":false,
            "service":"com.ainirobot.hardwareservice",
            "配置项":{

            },
            "自检项":{
                "云台水平电机":false,
                "云台俯仰电机":false,
                "BMS连接":false,
                "PSB":false,
                "红外模块连接":false,
                "右轮毂":false,
                "左轮毂":false,
                "电机零位检测":false,
                "821摄像头":false,
                "光线传感器":false
            }
        },
        "外设服务":{
            "是否启用":true,
            "service":"com.ainirobot.peripherals",
            "配置项":{

            },
            "自检项":{
                
            }
        }
    }
}
