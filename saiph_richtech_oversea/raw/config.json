{"项目名称": "saiph_oversea", "版本": "1.2.1", "服务配置": {"公共配置": {"语音ID": "orion.ovs.client.*************", "系统语言": {"语言版本号": "17", "语言信息": [{"类型": "zh_CN", "昵称": "简体中文"}, {"类型": "zh_TW", "昵称": "繁體中文"}, {"类型": "zh_GD", "昵称": "繁體中文(粤)"}, {"类型": "ja_<PERSON>", "昵称": "日本語"}, {"类型": "en_US", "昵称": "English"}, {"类型": "ko_KR", "昵称": "한국어"}, {"类型": "th_TH", "昵称": "ไทย"}, {"类型": "id_ID", "昵称": "Bahasa Indonesia"}, {"类型": "ms_MY", "昵称": "Bahasa Melayu"}, {"类型": "cs_CZ", "昵称": "Čeština"}, {"类型": "da_DK", "昵称": "Dansk"}, {"类型": "de_DE", "昵称": "De<PERSON>ch"}, {"类型": "es_ES", "昵称": "Español"}, {"类型": "el_GR", "昵称": "Ελληνικά"}, {"类型": "fil_PH", "昵称": "Filipino"}, {"类型": "fr_FR", "昵称": "Français"}, {"类型": "it_IT", "昵称": "Italiano"}, {"类型": "hu_HU", "昵称": "<PERSON><PERSON><PERSON>"}, {"类型": "nl_NL", "昵称": "Nederlands"}, {"类型": "nb_NO", "昵称": "Norsk"}, {"类型": "pl_PL", "昵称": "Polskie"}, {"类型": "pt_PT", "昵称": "Português"}, {"类型": "pt_BR", "昵称": "<PERSON><PERSON><PERSON><PERSON><PERSON>(Brasil)"}, {"类型": "ru_RU", "昵称": "Pусский"}, {"类型": "ro_RO", "昵称": "Română"}, {"类型": "sk_SK", "昵称": "Slovenský"}, {"类型": "fi_FI", "昵称": "<PERSON><PERSON>"}, {"类型": "sv_SE", "昵称": "Svenska"}, {"类型": "tr_TR", "昵称": "Türk"}, {"类型": "vi_VN", "昵称": "Việt Nam"}, {"类型": "ar_<PERSON>", "昵称": "العربية"}]}, "地图兼容性配置": {"最高兼容版本": 1, "视觉类型": [0], "标识码类型": [0, 1, 2, 3]}}, "语音服务": {"是否启用": true, "service": "com.ainirobot.speechasrservice", "配置项": {"TTS配置": "在线", "ASR配置": "在线", "音频灌入来源": "内部", "唤醒算法": "多通道WaveNet", "vad算法": "vad_2.0", "是否禁用ASR": true, "是否获取声音信息": true, "客户端ID": "orion.ovs.client.*************", "客户端Secret": "B71CB464C4A7730B31FEA15CACD1A7AF", "客户端PID": "100020", "正式语音服务地址": {"获取Token": "https://speech-global.orionstar.com/account/bind", "刷新Token": "https://speech-global.orionstar.com/oauth2/token", "ASR": "https://speech-global.orionstar.com/h2/streaming-asr", "QueryByText": "https://speech-global.orionstar.com/text-asr/v1", "TTS": "https://speech-global.orionstar.com/tts/v1/text2audio", "获取发音人": "https://speech-global.orionstar.com/tts/v1/voices", "获取唤醒应答音频": "https://speech-global.orionstar.com/tts/v1/operate/audio", "建立流式WS连接的请求地址": "wss://speech-global.orionstar.com/speech-bridge/ws/streaming", "建立流式SSE连接的请求地址": "https://speech-global.orionstar.com/speech-bridge/events", "心跳": "https://speech-global.orionstar.com/echo"}, "正式语音服务地址分区": [{"服务器区位": "default", "获取Token": "https://speech-global.orionstar.com/account/bind", "刷新Token": "https://speech-global.orionstar.com/oauth2/token", "ASR": "https://speech-global.orionstar.com/h2/streaming-asr", "QueryByText": "https://speech-global.orionstar.com/text-asr/v1", "TTS": "https://speech-global.orionstar.com/tts/v1/text2audio", "获取发音人": "https://speech-global.orionstar.com/tts/v1/voices", "获取唤醒应答音频": "https://speech-global.orionstar.com/tts/v1/operate/audio", "建立流式WS连接的请求地址": "wss://speech-global.orionstar.com/speech-bridge/ws/streaming", "建立流式SSE连接的请求地址": "https://speech-global.orionstar.com/speech-bridge/events", "心跳": "https://speech-global.orionstar.com/echo"}, {"服务器区位": "us", "获取Token": "https://speech-us.orionstar.com/account/bind", "刷新Token": "https://speech-us.orionstar.com/oauth2/token", "ASR": "https://speech-us.orionstar.com/h2/streaming-asr", "QueryByText": "https://speech-us.orionstar.com/text-asr/v1", "TTS": "https://speech-us.orionstar.com/tts/v1/text2audio", "获取发音人": "https://speech-us.orionstar.com/tts/v1/voices", "获取唤醒应答音频": "https://speech-us.orionstar.com/tts/v1/operate/audio", "建立流式WS连接的请求地址": "wss://speech-us.orionstar.com/speech-bridge/ws/streaming", "建立流式SSE连接的请求地址": "https://speech-us.orionstar.com/speech-bridge/events", "心跳": "https://speech-us.orionstar.com/echo"}, {"服务器区位": "jp", "获取Token": "https://speech-jp.orionstar.com/account/bind", "刷新Token": "https://speech-jp.orionstar.com/oauth2/token", "ASR": "https://speech-jp.orionstar.com/h2/streaming-asr", "QueryByText": "https://speech-jp.orionstar.com/text-asr/v1", "TTS": "https://speech-jp.orionstar.com/tts/v1/text2audio", "获取发音人": "https://speech-jp.orionstar.com/tts/v1/voices", "获取唤醒应答音频": "https://speech-jp.orionstar.com/tts/v1/operate/audio", "建立流式WS连接的请求地址": "wss://speech-jp.orionstar.com/speech-bridge/ws/streaming", "建立流式SSE连接的请求地址": "https://speech-jp.orionstar.com/speech-bridge/events", "心跳": "https://speech-jp.orionstar.com/echo"}], "测试语音服务地址": {"获取Token": "https://dev-voice-lite.ainirobot.com/account/bind", "刷新Token": "https://dev-voice-lite.ainirobot.com/oauth2/token", "ASR": "https://dev-voice-lite.ainirobot.com/h2/streaming-asr", "QueryByText": "https://dev-voice-lite.ainirobot.com/text-asr/v1", "TTS": "https://dev-voice-lite.ainirobot.com/tts/v1/text2audio", "获取发音人": "https://dev-voice-lite.ainirobot.com/tts/v1/voices", "获取唤醒应答音频": "https://dev-voice-lite.ainirobot.com/tts/v1/operate/audio", "建立流式WS连接的请求地址": "wss://speech-test.orionstar.com/speech-bridge/ws/streaming", "建立流式SSE连接的请求地址": "https://speech-test.orionstar.com/speech-bridge/events", "心跳": "https://dev-voice-lite.ainirobot.com/echo"}, "灰度语音服务地址": {"获取Token": "https://speech-global.orionstar.com/account/bind", "刷新Token": "https://speech-global.orionstar.com/oauth2/token", "ASR": "https://speech-global.orionstar.com/h2/streaming-asr", "QueryByText": "https://speech-global.orionstar.com/text-asr/v1", "TTS": "https://speech-global.orionstar.com/tts/v1/text2audio", "获取发音人": "https://speech-global.orionstar.com/tts/v1/voices", "获取唤醒应答音频": "https://speech-global.orionstar.com/tts/v1/operate/audio", "建立流式WS连接的请求地址": "wss://speech-global.orionstar.com/speech-bridge/ws/streaming", "建立流式SSE连接的请求地址": "https://speech-global.orionstar.com/speech-bridge/events", "心跳": "https://speech-global.orionstar.com/echo"}}}, "导航服务": {"是否启用": true, "service": "com.ainirobot.navigationservice", "配置项": {"底盘": "client_waiter", "底盘设备号": "12", "启用子设备号": true, "底盘IP": "127.0.0.1"}, "自检项": {"重定位摄像头": false, "RGBD": true, "激光雷达": true, "红外": false, "里程计": true, "激光数据": false, "校准文件": false, "硬盘空间": false, "can连接": false, "红外摄像头": true, "陀螺仪": true}}, "视觉服务": {"是否启用": true, "service": "com.ainirobot.headservice", "配置项": {"算法方案": "vision_sdk", "人脸识别": true, "客流统计": false, "客流属性": false}, "自检项": {"前置深度摄像头": false, "后置深度摄像头": false, "前置广角摄像头": false, "后置广角摄像头": false}}, "头部控制服务": {"是否启用": false, "service": "com.ainirobot.headservice", "配置项": {}}, "远程服务": {"是否启用": true, "service": "com.ainirobot.remotecontrolservice", "配置项": {"正式服务地址": {"业务": "https://global-jiedai.orionstar.com", "维修": "https://global-jiedai.orionstar.com", "大数据": "https://global-obapi.orionstar.com", "时间": "https://global-jiedai.orionstar.com/timestamp"}, "正式服务地址分区": [{"服务器区位": "default", "业务": "https://global-jiedai.orionstar.com", "维修": "https://global-jiedai.orionstar.com", "大数据": "https://global-obapi.orionstar.com", "时间": "https://global-jiedai.orionstar.com/timestamp"}, {"服务器区位": "us", "业务": "https://us-jiedai.orionstar.com", "维修": "https://us-jiedai.orionstar.com", "大数据": "https://us-obapi.orionstar.com", "时间": "https://us-jiedai.orionstar.com/timestamp"}, {"服务器区位": "jp", "业务": "https://jp-jiedai.orionstar.com", "维修": "https://jp-jiedai.orionstar.com", "大数据": "https://jp-obapi.orionstar.com", "时间": "https://jp-jiedai.orionstar.com/timestamp"}], "测试服务地址": {"业务": "https://dev-jiedai.ainirobot.com", "维修": "https://dev-jiedai.ainirobot.com", "大数据": "https://dev-obapi.orionstar.com", "时间": "https://dev-jiedai.ainirobot.com/timestamp"}, "灰度服务地址": {"业务": "https://global-jiedai.orionstar.com", "维修": "https://global-jiedai.orionstar.com", "大数据": "https://global-obapi.orionstar.com", "时间": "https://global-jiedai.orionstar.com/timestamp"}, "moduleCode下载列表": ["module_skill_home", "module_guide_home", "module_welcome", "module_remote_greet", "module_reception", "module_guide", "module_qa", "module_ride", "module_cate_ads", "module_cafe_menu", "module_bxd_blessing", "module_bxd_deliver", "module_bxd_default_broadcast", "module_pushtask_message", "module_ads", "module_theme", "module_goto_place_talk", "module_collection_music", "module_ads_line_cfg", "module_silence_recom", "module_leading_ads", "module_leading_start_broadcast", "module_leading_arrive_broadcast", "module_collection_broadcast"], "扫码绑定": true, "远程控制": true, "时钟同步": true, "无线网络": true}, "自检项": {"远程服务连接": false}}, "升级服务": {"是否启用": true, "service": "com.ainirobot.ota", "配置项": {"开机升级": true, "强制升级": true, "夜间升级": true, "静默升级": {"是否启用": true, "下载后直接升级": true, "下载后手动升级": false}, "PSB升级": true, "AC_CLIENT升级": false, "BMS升级": false, "MOTOR_HORIZON升级": false, "MOTOR_VERTICAL升级": false, "MOTOR_LEFT升级": false, "MOTOR_RIGHT升级": false, "TK1升级": false, "TX1升级": false, "server_domain": "https://global-jiedai.orionstar.com", "正式服务地址分区": [{"服务器区位": "default", "域名": "https://global-jiedai.orionstar.com"}, {"服务器区位": "us", "域名": "https://us-jiedai.orionstar.com"}, {"服务器区位": "jp", "域名": "https://jp-jiedai.orionstar.com"}], "develop_server_domain": "https://dev-jiedai.ainirobot.com", "prerelease_server_domain": "https://global-jiedai.orionstar.com"}}, "系统服务": {"是否启用": true, "service": "com.ainirobot.hardwareservice", "配置项": {"270度水平云台": false, "轮子恢复": true, "多功能开关": true, "多功能开关类型": 1}, "自检项": {"云台水平电机": false, "云台俯仰电机": false, "BMS连接": false, "PSB": false, "红外模块连接": false, "右轮毂": false, "左轮毂": false, "电机零位检测": false, "821摄像头": false, "光线传感器": false}}, "外设管理": {"service": "com.ainirobot.hardwareservice", "外设配置": {"刷卡器服务": {"是否启用": false}, "扫码器服务": {"是否启用": false, "自检项": {"扫码器": false}}, "客流服务": {"是否启用": false, "配置项": {"客流统计": false}, "自检项": {}}, "D430服务": {"是否启用": true, "配置项": {}, "自检项": {}}}}, "红外测温服务": {"service": "com.ainirobot.thermalservice", "是否启用": false, "配置项": {"产品ID": "1"}}, "视频服务": {"是否启用": false, "service": "com.ainirobot.videocall", "配置项": {"获取视频流方式": 1, "远程视频旋转角度": 90, "本地预览旋转角度": 0, "正式服务地址分区": [{"服务器区位": "default", "呼叫": "http://global-jiedai.orionstar.com", "客服": "http://global-kefu.orionstar.com"}, {"服务器区位": "us", "呼叫": "http://us-jiedai.orionstar.com", "客服": "http://us-kefu.orionstar.com"}, {"服务器区位": "jp", "呼叫": "http://jp-jiedai.orionstar.com", "客服": "http://jp-kefu.orionstar.com"}]}, "自检项": {"视频服务连接": false}}, "核心服务": {"service": "com.ainirobot.coreservice", "配置项": {"待机配置": {"静音": false, "降低屏幕亮度": true, "关闭视觉": true, "关闭语音": true, "关闭灯带": true, "关闭IR灯": true, "关闭雷达": true, "关闭轮子电机": true, "休眠": false, "关机": false, "关闭深度IR摄像头": true}, "定时关机配置": {"是否启用": true, "默认关机时间": "00:30"}, "是否有TopIR": true}, "服务管理": {"账号服务": {"是否启用": false, "配置项": {"本地注册": false}}, "头部服务": {"是否启用": true, "配置项": {"支持270度水平云台": false, "支持水平云台": false, "支持俯仰云台": false}}, "电池服务": {"是否启用": true, "配置项": {"低电值": 5, "OTA低电值": 20}}, "导航服务": {"是否启用": true, "配置项": {"底盘半径": 0.305}}}}}}